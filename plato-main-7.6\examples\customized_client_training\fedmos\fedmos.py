"""
An implementation of the FedMos algorithm.

<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, "FedMoS: Taming Client Drift in Federated Learning with Double Momentum and Adaptive Selection," IEEE INFOCOM 2023

Paper: https://ieeexplore.ieee.org/document/10228957

Source code: https://github.com/Distributed-Learning-Networking-Group/FedMoS
"""

from plato.servers import fedavg
from plato.clients import simple

import fedmos_trainer


def main():
    """A Plato federated learning training session using FedDyn."""
    trainer = fedmos_trainer.Trainer
    client = simple.Client(trainer=trainer)
    server = fedavg.Server()
    server.run(client)


if __name__ == "__main__":
    main()