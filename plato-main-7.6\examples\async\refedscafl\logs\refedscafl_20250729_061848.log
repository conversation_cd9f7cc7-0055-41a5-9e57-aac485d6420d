[INFO][06:18:48]: 日志系统已初始化
[INFO][06:18:48]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_061848.log
[INFO][06:18:48]: 日志级别: INFO
[WARNING][06:18:48]: 无法获取系统信息: No module named 'psutil'
[INFO][06:18:48]: 🚀 ReFedScaFL 训练开始
[INFO][06:18:48]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][06:18:48]: 开始时间: 2025-07-29 06:18:48
[INFO][06:18:48]: [Client None] 基础初始化完成
[INFO][06:18:48]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][06:18:48]: 创建并缓存共享模型
[INFO][06:18:48]: [93m[1m[20076] Logging runtime results to: ./results/refedscafl/comparison_cifar10_alpha01/20076.csv.[0m
[INFO][06:18:48]: [Server #20076] Started training on 100 clients with 20 per round.
[INFO][06:18:48]: 服务器参数配置完成：
[INFO][06:18:48]: - 客户端数量: total=100, per_round=20
[INFO][06:18:48]: - 权重参数: success=0.8, distill=0.2
[INFO][06:18:48]: - SCAFL参数: V=1.0, tau_max=5
[INFO][06:18:48]: 从共享资源模型提取并缓存全局权重
[INFO][06:18:48]: [Server #20076] Configuring the server...
[INFO][06:18:48]: Training: 400 rounds or accuracy above 100.0%

[INFO][06:18:48]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][06:18:48]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][06:18:48]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][06:18:48]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][06:18:48]: Algorithm: fedavg
[INFO][06:18:48]: Data source: CIFAR10
[INFO][06:18:50]: Starting client #1's process.
[INFO][06:18:50]: Starting client #2's process.
[INFO][06:18:50]: Starting client #3's process.
[INFO][06:18:50]: Starting client #4's process.
[INFO][06:18:50]: Starting client #5's process.
[INFO][06:18:50]: Starting client #6's process.
[INFO][06:18:50]: Starting client #7's process.
[INFO][06:18:50]: Starting client #8's process.
[INFO][06:18:50]: Starting client #9's process.
[INFO][06:18:50]: Starting client #10's process.
[INFO][06:18:50]: Setting the random seed for selecting clients: 1
[INFO][06:18:50]: Starting a server at address 127.0.0.1 and port 8092.
[INFO][06:19:05]: [Server #20076] A new client just connected.
[INFO][06:19:05]: [Server #20076] A new client just connected.
[INFO][06:19:05]: [Server #20076] New client with id #2 arrived.
[INFO][06:19:05]: [Server #20076] Client process #37472 registered.
[INFO][06:19:05]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][06:19:05]: [Server #20076] New client with id #1 arrived.
[INFO][06:19:05]: [Server #20076] Client process #38016 registered.
[INFO][06:19:05]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][06:19:05]: [Server #20076] A new client just connected.
[INFO][06:19:05]: [Server #20076] New client with id #3 arrived.
[INFO][06:19:05]: [Server #20076] Client process #22556 registered.
[INFO][06:19:05]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][06:19:05]: [Server #20076] A new client just connected.
[INFO][06:19:05]: [Server #20076] A new client just connected.
[INFO][06:19:05]: [Server #20076] New client with id #4 arrived.
[INFO][06:19:05]: [Server #20076] Client process #28440 registered.
[INFO][06:19:05]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][06:19:05]: [Server #20076] New client with id #6 arrived.
[INFO][06:19:05]: [Server #20076] Client process #36116 registered.
[INFO][06:19:05]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][06:19:05]: [Server #20076] A new client just connected.
[INFO][06:19:05]: [Server #20076] New client with id #9 arrived.
[INFO][06:19:05]: [Server #20076] Client process #18084 registered.
[INFO][06:19:05]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][06:19:05]: [Server #20076] A new client just connected.
[INFO][06:19:05]: [Server #20076] New client with id #5 arrived.
[INFO][06:19:05]: [Server #20076] Client process #34336 registered.
[INFO][06:19:05]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][06:19:05]: [Server #20076] A new client just connected.
[INFO][06:19:05]: [Server #20076] A new client just connected.
[INFO][06:19:05]: [Server #20076] A new client just connected.
[INFO][06:19:05]: [Server #20076] New client with id #10 arrived.
[INFO][06:19:05]: [Server #20076] Client process #20448 registered.
[INFO][06:19:05]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][06:19:05]: [Server #20076] New client with id #8 arrived.
[INFO][06:19:05]: [Server #20076] Client process #37316 registered.
[INFO][06:19:05]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][06:19:05]: [Server #20076] New client with id #7 arrived.
[INFO][06:19:05]: [Server #20076] Client process #5324 registered.
[INFO][06:19:05]: [Server #20076] Starting training.
[INFO][06:19:05]: [93m[1m
[Server #20076] Starting round 1/400.[0m
[INFO][06:19:05]: [Server #20076] Selected clients: [18, 73, 98, 9, 33, 16, 64, 58, 61, 84, 49, 27, 13, 63, 4, 50, 56, 78, 99, 1]
[INFO][06:19:05]: [Server #20076] Selecting client #18 for training.
[INFO][06:19:05]: [Server #20076] Sending the current model to client #18 (simulated).
[INFO][06:19:05]: [Server #20076] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][06:19:05]: [Server #20076] Selecting client #73 for training.
[INFO][06:19:05]: [Server #20076] Sending the current model to client #73 (simulated).
[INFO][06:19:05]: [Server #20076] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][06:19:05]: [Server #20076] Selecting client #98 for training.
[INFO][06:19:05]: [Server #20076] Sending the current model to client #98 (simulated).
[INFO][06:19:05]: [Server #20076] Sending 18.75 MB of payload data to client #98 (simulated).
[INFO][06:19:05]: [Server #20076] Selecting client #9 for training.
[INFO][06:19:05]: [Server #20076] Sending the current model to client #9 (simulated).
[INFO][06:19:05]: [Server #20076] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][06:19:05]: [Server #20076] Selecting client #33 for training.
[INFO][06:19:05]: [Server #20076] Sending the current model to client #33 (simulated).
[INFO][06:19:05]: [Server #20076] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][06:19:05]: [Server #20076] Selecting client #16 for training.
[INFO][06:19:05]: [Server #20076] Sending the current model to client #16 (simulated).
[INFO][06:19:05]: [Server #20076] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][06:19:05]: [Server #20076] Selecting client #64 for training.
[INFO][06:19:05]: [Server #20076] Sending the current model to client #64 (simulated).
[INFO][06:19:05]: [Server #20076] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][06:19:05]: [Server #20076] Selecting client #58 for training.
[INFO][06:19:05]: [Server #20076] Sending the current model to client #58 (simulated).
[INFO][06:19:05]: [Server #20076] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][06:19:05]: [Server #20076] Selecting client #61 for training.
[INFO][06:19:05]: [Server #20076] Sending the current model to client #61 (simulated).
[INFO][06:19:05]: [Server #20076] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][06:19:06]: [Server #20076] Selecting client #84 for training.
[INFO][06:19:06]: [Server #20076] Sending the current model to client #84 (simulated).
[INFO][06:19:06]: [Server #20076] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][06:19:06]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][06:21:45]: [Server #20076] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][06:21:46]: [Server #20076] Received 18.75 MB of payload data from client #98 (simulated).
[INFO][06:21:47]: [Server #20076] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][06:21:47]: [Server #20076] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][06:21:50]: [Server #20076] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][06:21:51]: [Server #20076] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][06:21:51]: [Server #20076] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][06:21:51]: [Server #20076] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][06:21:51]: [Server #20076] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][06:21:51]: [Server #20076] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][06:21:51]: [Server #20076] Selecting client #49 for training.
[INFO][06:21:51]: [Server #20076] Sending the current model to client #49 (simulated).
[INFO][06:21:51]: [Server #20076] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][06:21:51]: [Server #20076] Selecting client #27 for training.
[INFO][06:21:51]: [Server #20076] Sending the current model to client #27 (simulated).
[INFO][06:21:51]: [Server #20076] Sending 18.75 MB of payload data to client #27 (simulated).
[INFO][06:21:51]: [Server #20076] Selecting client #13 for training.
[INFO][06:21:51]: [Server #20076] Sending the current model to client #13 (simulated).
[INFO][06:21:51]: [Server #20076] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][06:21:51]: [Server #20076] Selecting client #63 for training.
[INFO][06:21:51]: [Server #20076] Sending the current model to client #63 (simulated).
[INFO][06:21:52]: [Server #20076] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][06:21:52]: [Server #20076] Selecting client #4 for training.
[INFO][06:21:52]: [Server #20076] Sending the current model to client #4 (simulated).
[INFO][06:21:52]: [Server #20076] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][06:21:52]: [Server #20076] Selecting client #50 for training.
[INFO][06:21:52]: [Server #20076] Sending the current model to client #50 (simulated).
[INFO][06:21:52]: [Server #20076] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][06:21:52]: [Server #20076] Selecting client #56 for training.
[INFO][06:21:52]: [Server #20076] Sending the current model to client #56 (simulated).
[INFO][06:21:52]: [Server #20076] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][06:21:52]: [Server #20076] Selecting client #78 for training.
[INFO][06:21:52]: [Server #20076] Sending the current model to client #78 (simulated).
[INFO][06:21:52]: [Server #20076] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][06:21:52]: [Server #20076] Selecting client #99 for training.
[INFO][06:21:52]: [Server #20076] Sending the current model to client #99 (simulated).
[INFO][06:21:53]: [Server #20076] Sending 18.75 MB of payload data to client #99 (simulated).
[INFO][06:21:53]: [Server #20076] Selecting client #1 for training.
[INFO][06:21:53]: [Server #20076] Sending the current model to client #1 (simulated).
[INFO][06:21:53]: [Server #20076] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][06:24:54]: [Server #20076] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][06:24:58]: [Server #20076] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][06:24:58]: [Server #20076] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][06:24:58]: [Server #20076] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][06:24:58]: [Server #20076] Received 18.75 MB of payload data from client #27 (simulated).
[INFO][06:24:58]: [Server #20076] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][06:24:58]: [Server #20076] Received 18.75 MB of payload data from client #78 (simulated).
[INFO][06:24:58]: [Server #20076] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][06:24:58]: [Server #20076] Received 18.75 MB of payload data from client #99 (simulated).
[INFO][06:24:59]: [Server #20076] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][06:24:59]: [Server #20076] Adding client #18 to the list of clients for aggregation.
[INFO][06:24:59]: [Server #20076] Adding client #98 to the list of clients for aggregation.
[INFO][06:24:59]: [Server #20076] Adding client #73 to the list of clients for aggregation.
[INFO][06:24:59]: [Server #20076] Adding client #9 to the list of clients for aggregation.
[INFO][06:24:59]: [Server #20076] Adding client #16 to the list of clients for aggregation.
[INFO][06:24:59]: [Server #20076] Adding client #33 to the list of clients for aggregation.
[INFO][06:24:59]: [Server #20076] Adding client #49 to the list of clients for aggregation.
[INFO][06:24:59]: [Server #20076] Adding client #78 to the list of clients for aggregation.
[INFO][06:24:59]: [Server #20076] Adding client #50 to the list of clients for aggregation.
[INFO][06:24:59]: [Server #20076] Adding client #13 to the list of clients for aggregation.
[INFO][06:24:59]: [Server #20076] Aggregating 10 clients in total.
[INFO][06:24:59]: [Server #20076] Updated weights have been received.
[INFO][06:24:59]: [Server #20076] Aggregating model weight deltas.
[INFO][06:24:59]: [Server #20076] Finished aggregating updated weights.
[INFO][06:24:59]: [Server #20076] Started model testing.
[INFO][06:25:09]: [Trainer.test] 测试完成 - 准确率: 15.30% (1530/10000)
[INFO][06:25:09]: [93m[1m[Server #20076] Global model accuracy: 15.30%
[0m
[INFO][06:25:09]: [Server #20076] All client reports have been processed.
[INFO][06:25:09]: [Server #20076] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][06:25:09]: [Server #20076] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][06:25:09]: [93m[1m
[Server #20076] Starting round 2/400.[0m
[INFO][06:25:09]: [Server #20076] Selected clients: [100, 66, 38, 33, 85, 16, 44, 6, 5, 93]
[INFO][06:25:09]: [Server #20076] Selecting client #100 for training.
[INFO][06:25:09]: [Server #20076] Sending the current model to client #100 (simulated).
[INFO][06:25:09]: [Server #20076] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][06:25:09]: [Server #20076] Selecting client #66 for training.
[INFO][06:25:09]: [Server #20076] Sending the current model to client #66 (simulated).
[INFO][06:25:09]: [Server #20076] Sending 18.75 MB of payload data to client #66 (simulated).
[INFO][06:25:09]: [Server #20076] Selecting client #38 for training.
[INFO][06:25:09]: [Server #20076] Sending the current model to client #38 (simulated).
[INFO][06:25:09]: [Server #20076] Sending 18.75 MB of payload data to client #38 (simulated).
[INFO][06:25:09]: [Server #20076] Selecting client #33 for training.
[INFO][06:25:09]: [Server #20076] Sending the current model to client #33 (simulated).
[INFO][06:25:09]: [Server #20076] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][06:25:09]: [Server #20076] Selecting client #85 for training.
[INFO][06:25:09]: [Server #20076] Sending the current model to client #85 (simulated).
[INFO][06:25:10]: [Server #20076] Sending 18.75 MB of payload data to client #85 (simulated).
[INFO][06:25:10]: [Server #20076] Selecting client #16 for training.
[INFO][06:25:10]: [Server #20076] Sending the current model to client #16 (simulated).
[INFO][06:25:10]: [Server #20076] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][06:25:10]: [Server #20076] Selecting client #44 for training.
[INFO][06:25:10]: [Server #20076] Sending the current model to client #44 (simulated).
[INFO][06:25:10]: [Server #20076] Sending 18.75 MB of payload data to client #44 (simulated).
[INFO][06:25:10]: [Server #20076] Selecting client #6 for training.
[INFO][06:25:10]: [Server #20076] Sending the current model to client #6 (simulated).
[INFO][06:25:11]: [Server #20076] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][06:25:11]: [Server #20076] Selecting client #5 for training.
[INFO][06:25:11]: [Server #20076] Sending the current model to client #5 (simulated).
[INFO][06:25:11]: [Server #20076] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][06:25:11]: [Server #20076] Selecting client #93 for training.
[INFO][06:25:11]: [Server #20076] Sending the current model to client #93 (simulated).
[INFO][06:25:12]: [Server #20076] Sending 18.75 MB of payload data to client #93 (simulated).
[INFO][06:30:04]: [Server #20076] Received 18.75 MB of payload data from client #66 (simulated).
[INFO][06:30:12]: [Server #20076] Received 18.75 MB of payload data from client #100 (simulated).
[INFO][06:30:13]: [Server #20076] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][06:30:14]: [Server #20076] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][06:30:14]: [Server #20076] Received 18.75 MB of payload data from client #85 (simulated).
[INFO][06:30:14]: [Server #20076] Received 18.75 MB of payload data from client #38 (simulated).
[INFO][06:30:14]: [Server #20076] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][06:30:14]: [Server #20076] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][06:30:14]: [Server #20076] Received 18.75 MB of payload data from client #44 (simulated).
[INFO][06:30:14]: [Server #20076] Received 18.75 MB of payload data from client #93 (simulated).
[INFO][06:30:14]: [Server #20076] Adding client #27 to the list of clients for aggregation.
[INFO][06:30:14]: [Server #20076] Adding client #63 to the list of clients for aggregation.
[INFO][06:30:14]: [Server #20076] Adding client #64 to the list of clients for aggregation.
[INFO][06:30:14]: [Server #20076] Adding client #61 to the list of clients for aggregation.
[INFO][06:30:14]: [Server #20076] Adding client #4 to the list of clients for aggregation.
[INFO][06:30:14]: [Server #20076] Adding client #56 to the list of clients for aggregation.
[INFO][06:30:14]: [Server #20076] Adding client #99 to the list of clients for aggregation.
[INFO][06:30:14]: [Server #20076] Adding client #1 to the list of clients for aggregation.
[INFO][06:30:14]: [Server #20076] Adding client #84 to the list of clients for aggregation.
[INFO][06:30:14]: [Server #20076] Adding client #58 to the list of clients for aggregation.
[INFO][06:30:14]: [Server #20076] Aggregating 10 clients in total.
[INFO][06:30:14]: [Server #20076] Updated weights have been received.
[INFO][06:30:14]: [Server #20076] Aggregating model weight deltas.
[INFO][06:30:14]: [Server #20076] Finished aggregating updated weights.
[INFO][06:30:14]: [Server #20076] Started model testing.
[INFO][06:30:25]: [Trainer.test] 测试完成 - 准确率: 12.19% (1219/10000)
[INFO][06:30:25]: [93m[1m[Server #20076] Global model accuracy: 12.19%
[0m
[INFO][06:30:25]: [Server #20076] All client reports have been processed.
[INFO][06:30:25]: [Server #20076] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][06:30:25]: [Server #20076] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][06:30:25]: [93m[1m
[Server #20076] Starting round 3/400.[0m
[INFO][06:30:25]: [Server #20076] Selected clients: [77, 2, 55, 97, 31, 61, 4, 75, 32, 63]
[INFO][06:30:25]: [Server #20076] Selecting client #77 for training.
[INFO][06:30:25]: [Server #20076] Sending the current model to client #77 (simulated).
[INFO][06:30:25]: [Server #20076] Sending 18.75 MB of payload data to client #77 (simulated).
[INFO][06:30:25]: [Server #20076] Selecting client #2 for training.
[INFO][06:30:25]: [Server #20076] Sending the current model to client #2 (simulated).
[INFO][06:30:25]: [Server #20076] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][06:30:25]: [Server #20076] Selecting client #55 for training.
[INFO][06:30:25]: [Server #20076] Sending the current model to client #55 (simulated).
[INFO][06:30:25]: [Server #20076] Sending 18.75 MB of payload data to client #55 (simulated).
[INFO][06:30:25]: [Server #20076] Selecting client #97 for training.
[INFO][06:30:25]: [Server #20076] Sending the current model to client #97 (simulated).
[INFO][06:30:25]: [Server #20076] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][06:30:25]: [Server #20076] Selecting client #31 for training.
[INFO][06:30:25]: [Server #20076] Sending the current model to client #31 (simulated).
[INFO][06:30:25]: [Server #20076] Sending 18.75 MB of payload data to client #31 (simulated).
[INFO][06:30:25]: [Server #20076] Selecting client #61 for training.
[INFO][06:30:25]: [Server #20076] Sending the current model to client #61 (simulated).
[INFO][06:30:25]: [Server #20076] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][06:30:25]: [Server #20076] Selecting client #4 for training.
[INFO][06:30:25]: [Server #20076] Sending the current model to client #4 (simulated).
[INFO][06:30:26]: [Server #20076] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][06:30:26]: [Server #20076] Selecting client #75 for training.
[INFO][06:30:26]: [Server #20076] Sending the current model to client #75 (simulated).
[INFO][06:30:26]: [Server #20076] Sending 18.75 MB of payload data to client #75 (simulated).
[INFO][06:30:26]: [Server #20076] Selecting client #32 for training.
[INFO][06:30:26]: [Server #20076] Sending the current model to client #32 (simulated).
[INFO][06:30:26]: [Server #20076] Sending 18.75 MB of payload data to client #32 (simulated).
[INFO][06:30:26]: [Server #20076] Selecting client #63 for training.
[INFO][06:30:26]: [Server #20076] Sending the current model to client #63 (simulated).
[INFO][06:30:27]: [Server #20076] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][06:35:38]: [Server #20076] Received 18.75 MB of payload data from client #55 (simulated).
[INFO][06:35:48]: [Server #20076] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][06:35:54]: [Server #20076] Received 18.75 MB of payload data from client #97 (simulated).
[INFO][06:35:55]: [Server #20076] Received 18.75 MB of payload data from client #32 (simulated).
[INFO][06:35:55]: [Server #20076] Received 18.75 MB of payload data from client #75 (simulated).
[INFO][06:35:56]: [Server #20076] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][06:35:56]: [Server #20076] Received 18.75 MB of payload data from client #31 (simulated).
[INFO][06:35:56]: [Server #20076] Received 18.75 MB of payload data from client #77 (simulated).
[INFO][06:35:57]: [Server #20076] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][06:35:57]: [Server #20076] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][06:35:57]: [Server #20076] Adding client #2 to the list of clients for aggregation.
[INFO][06:35:57]: [Server #20076] Adding client #55 to the list of clients for aggregation.
[INFO][06:35:57]: [Server #20076] Adding client #97 to the list of clients for aggregation.
[INFO][06:35:57]: [Server #20076] Adding client #77 to the list of clients for aggregation.
[INFO][06:35:57]: [Server #20076] Adding client #31 to the list of clients for aggregation.
[INFO][06:35:57]: [Server #20076] Adding client #4 to the list of clients for aggregation.
[INFO][06:35:57]: [Server #20076] Adding client #61 to the list of clients for aggregation.
[INFO][06:35:57]: [Server #20076] Adding client #75 to the list of clients for aggregation.
[INFO][06:35:57]: [Server #20076] Adding client #32 to the list of clients for aggregation.
[INFO][06:35:57]: [Server #20076] Adding client #63 to the list of clients for aggregation.
[INFO][06:35:57]: [Server #20076] Aggregating 10 clients in total.
[INFO][06:35:57]: [Server #20076] Updated weights have been received.
[INFO][06:35:57]: [Server #20076] Aggregating model weight deltas.
[INFO][06:35:58]: [Server #20076] Finished aggregating updated weights.
[INFO][06:35:58]: [Server #20076] Started model testing.
[INFO][06:36:35]: [Trainer.test] 测试完成 - 准确率: 19.91% (1991/10000)
[INFO][06:36:35]: [93m[1m[Server #20076] Global model accuracy: 19.91%
[0m
[INFO][06:36:35]: [Server #20076] All client reports have been processed.
[INFO][06:36:35]: [Server #20076] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][06:36:35]: [Server #20076] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][06:36:35]: [93m[1m
[Server #20076] Starting round 4/400.[0m
[INFO][06:36:35]: [Server #20076] Selected clients: [71, 78, 34, 51, 96, 32, 65, 43, 3, 60]
[INFO][06:36:35]: [Server #20076] Selecting client #71 for training.
[INFO][06:36:35]: [Server #20076] Sending the current model to client #71 (simulated).
[INFO][06:36:35]: [Server #20076] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][06:36:35]: [Server #20076] Selecting client #78 for training.
[INFO][06:36:35]: [Server #20076] Sending the current model to client #78 (simulated).
[INFO][06:36:35]: [Server #20076] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][06:36:35]: [Server #20076] Selecting client #34 for training.
[INFO][06:36:35]: [Server #20076] Sending the current model to client #34 (simulated).
[INFO][06:36:36]: [Server #20076] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][06:36:36]: [Server #20076] An existing client just disconnected.
[WARNING][06:36:36]: [Server #20076] Client process #20448 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][06:36:36]: [93m[1m[Server #20076] Closing the server due to a failed client.[0m
[INFO][06:36:36]: [Server #20076] Training concluded.
[INFO][06:36:36]: [Server #20076] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][06:36:36]: [Server #20076] Closing the server.
[INFO][06:36:36]: Closing the connection to client #37472.
[INFO][06:36:36]: [Server #20076] An existing client just disconnected.
[WARNING][06:36:36]: [Server #20076] Client process #18084 disconnected and removed from this server, 8 client processes are remaining.
[WARNING][06:36:36]: [93m[1m[Server #20076] Closing the server due to a failed client.[0m
[INFO][06:36:36]: [Server #20076] Training concluded.
[INFO][06:36:36]: [Server #20076] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][06:36:36]: [Server #20076] Closing the server.
[INFO][06:36:36]: Closing the connection to client #37472.
[INFO][06:36:36]: [Server #20076] An existing client just disconnected.
[WARNING][06:36:36]: [Server #20076] Client process #37316 disconnected and removed from this server, 7 client processes are remaining.
[WARNING][06:36:36]: [93m[1m[Server #20076] Closing the server due to a failed client.[0m
[INFO][06:36:36]: [Server #20076] Training concluded.
[INFO][06:36:36]: [Server #20076] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][06:36:36]: [Server #20076] Closing the server.
[INFO][06:36:36]: Closing the connection to client #37472.
[INFO][06:36:36]: [Server #20076] An existing client just disconnected.
[WARNING][06:36:36]: [Server #20076] Client process #5324 disconnected and removed from this server, 6 client processes are remaining.
[WARNING][06:36:36]: [93m[1m[Server #20076] Closing the server due to a failed client.[0m
[INFO][06:36:36]: [Server #20076] Training concluded.
[INFO][06:36:36]: [Server #20076] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][06:36:36]: [Server #20076] Closing the server.
[INFO][06:36:36]: Closing the connection to client #37472.
[INFO][06:36:36]: [Server #20076] An existing client just disconnected.
[WARNING][06:36:36]: [Server #20076] Client process #36116 disconnected and removed from this server, 5 client processes are remaining.
[WARNING][06:36:36]: [93m[1m[Server #20076] Closing the server due to a failed client.[0m
[INFO][06:36:36]: [Server #20076] Training concluded.
[INFO][06:36:36]: [Server #20076] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][06:36:36]: [Server #20076] Closing the server.
[INFO][06:36:36]: Closing the connection to client #37472.
[INFO][06:36:36]: [Server #20076] Selecting client #51 for training.
[INFO][06:36:36]: [Server #20076] Sending the current model to client #51 (simulated).
[INFO][06:36:36]: [Server #20076] Sending 18.75 MB of payload data to client #51 (simulated).
[INFO][06:36:36]: Closing the connection to client #38016.
[INFO][06:36:36]: Closing the connection to client #38016.
[INFO][06:36:36]: Closing the connection to client #38016.
[INFO][06:36:36]: Closing the connection to client #38016.
[INFO][06:36:36]: Closing the connection to client #38016.
[INFO][06:36:36]: [Server #20076] Selecting client #96 for training.
[INFO][06:36:36]: [Server #20076] Sending the current model to client #96 (simulated).
[INFO][06:36:36]: [Server #20076] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][06:36:36]: Closing the connection to client #22556.
[INFO][06:36:36]: Closing the connection to client #22556.
[INFO][06:36:36]: Closing the connection to client #22556.
[INFO][06:36:36]: Closing the connection to client #22556.
[INFO][06:36:36]: Closing the connection to client #22556.
[INFO][06:36:36]: [Server #20076] Selecting client #32 for training.
[INFO][06:36:36]: [Server #20076] Sending the current model to client #32 (simulated).
[INFO][06:36:36]: [Server #20076] Sending 18.75 MB of payload data to client #32 (simulated).
[INFO][06:36:36]: Closing the connection to client #28440.
[INFO][06:36:36]: Closing the connection to client #28440.
[INFO][06:36:36]: Closing the connection to client #28440.
[INFO][06:36:36]: Closing the connection to client #28440.
[INFO][06:36:36]: Closing the connection to client #28440.
[INFO][06:36:36]: [Server #20076] Selecting client #65 for training.
[INFO][06:36:36]: [Server #20076] Sending the current model to client #65 (simulated).
[INFO][06:36:37]: [Server #20076] Sending 18.75 MB of payload data to client #65 (simulated).
[INFO][06:36:37]: Closing the connection to client #36116.
[INFO][06:36:37]: Closing the connection to client #36116.
[INFO][06:36:37]: Closing the connection to client #36116.
[INFO][06:36:37]: Closing the connection to client #36116.
[INFO][06:36:37]: Closing the connection to client #34336.
[INFO][06:36:37]: [Server #20076] Selecting client #43 for training.
[INFO][06:36:37]: [Server #20076] Sending the current model to client #43 (simulated).
[INFO][06:36:37]: [Server #20076] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][06:36:37]: Closing the connection to client #18084.
[INFO][06:36:37]: Closing the connection to client #34336.
[INFO][06:36:37]: Closing the connection to client #34336.
[INFO][06:36:37]: Closing the connection to client #34336.
