{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from torch import nn\n", "import nest_asyncio\n", "import sys\n", "sys.argv = [sys.argv[0]]\n", "from plato.servers import fedavg\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[INFO][07:32:43]: [Server #16316] Started training on 1 clients with 1 per round.\n", "[INFO][07:32:43]: [Server #16316] Configuring the server...\n", "[INFO][07:32:43]: Training: 5 rounds or 94.0% accuracy\n", "\n", "[INFO][07:32:43]: Trainer: basic\n", "[INFO][07:32:43]: Algorithm: fedavg\n", "[INFO][07:32:43]: Data source: MNIST\n", "[INFO][07:32:43]: No clients are launched (server:disable_clients = true)\n", "[INFO][07:32:43]: Starting a server at address 127.0.0.1 and port 8000.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["======== Running on http://127.0.0.1:8000 ========\n", "(Press CTRL+C to quit)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO][07:32:56]: 127.0.0.1 [13/Dec/2021:23:32:56 +0000] \"GET /socket.io/?transport=polling&EIO=4&t=1639438376.5726383 HTTP/1.1\" 200 292 \"-\" \"Python/3.7 aiohttp/3.8.1\"\n", "[INFO][07:32:56]: [Server #16316] A new client just connected.\n", "[INFO][07:32:56]: [Server #16316] New client with id #1 arrived.\n", "[INFO][07:32:56]: [Server #16316] Starting training.\n", "[INFO][07:32:56]: \n", "[Server #16316] Starting round 1/5.\n", "[INFO][07:32:56]: [Server #16316] Selecting client #1 for training.\n", "[INFO][07:32:56]: [Server #16316] Sending the current model to client #1.\n", "[INFO][07:32:56]: [Server #16316] Sent 0.24 MB of payload data to client #1.\n", "[INFO][07:33:37]: [Server #16316] Received 0.24 MB of payload data from client #1.\n", "[INFO][07:33:37]: [Server #16316] All 1 client reports received. Processing.\n", "[INFO][07:33:40]: [Server #16316] Global model accuracy: 97.75%\n", "\n", "[INFO][07:33:40]: [Server #16316] Target accuracy reached.\n", "[INFO][07:33:40]: [Server #16316] Training concluded.\n", "[INFO][07:33:40]: [Server #16316] Model saved to ./models/pretrained/lenet5.pth.\n", "[INFO][07:33:40]: Closing the connection to client #1.\n"]}], "source": ["server = fedavg.Server()\n", "server.run()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "abb1bb7cc74d8e0a69f854346f68e9c79602092e8f8792cdeea74894b6fb0780"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.5"}}, "nbformat": 4, "nbformat_minor": 4}