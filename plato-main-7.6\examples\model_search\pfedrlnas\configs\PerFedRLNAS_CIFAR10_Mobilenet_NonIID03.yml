clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 2

    # Should the clients compute test accuracy locally?
    do_test: true

    random_seed: 1

    comm_simulation: true 
    compute_comm_time: true

server:
    address: 127.0.0.1
    port: 8003
    do_test: false
    random_seed: 1
    simulate_wall_time: true

data:
    # The training and testing dataset
    datasource:  CIFAR10

    # Number of samples in each partition
    partition_size: 600
    test_partition_size: 600

    sampler: noniid
##    # IID or non-IID?
##
##    # The concentration parameter for the Dirichlet distribution
    concentration: 0.3
##
    testset_sampler: noniid
##
##    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 1500

    # The maximum number of clients running concurrently
    max_concurrency: 5

    # The target accuracy
    target_accuracy: 1.

    # Number of epochs for local training in each communication round
    epochs: 5
    batch_size: 32
    loss_criterion: CrossEntropyLoss
    optimizer: SGD
    lr_scheduler: CosineAnnealingLR
    global_lr_scheduler: true

    model_type: torch_hub
    model_name: mobilenet_v3_large

algorithm:
    # Aggregation algorithm
    type: fedavg
    
results:
    types: round, accuracy, elapsed_time, comm_time, round_time, server_overhead, comm_overhead, clients_accuracy_mean, clients_accuracy_std, clients_accuracy_max, clients_accuracy_min, model_size

parameters:
    model:
        num_classes: 10
        bn_momentum: 0.1
        bn_eps: 1e-5
        dropout: 0
        drop_connect: 0
    optimizer:
        lr: 0.35
        weight_decay: 0.000005
    learning_rate:
        T_max: 7500
    architect:
        learning_rate: 0.5
        weight_decay: 0
        lambda_time: 0.001
        lambda_neg: 0.1
