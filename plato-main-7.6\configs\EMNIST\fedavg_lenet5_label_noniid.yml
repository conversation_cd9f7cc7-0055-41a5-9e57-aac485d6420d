clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8000
    simulate_wall_time: true

data:
    # The training and testing dataset
    datasource: EMNIST

    # Number of samples in each partition
    partition_size: 11280

    # IID or non-IID?
    sampler: label_quantity_noniid

    # per client classes size
    per_client_classes_size: 5

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 100

    # The maximum number of clients running concurrently
    max_concurrency: 10

    # The target accuracy
    target_accuracy: 0.95

    # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    model:
        num_classes: 47

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

results:
    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy
