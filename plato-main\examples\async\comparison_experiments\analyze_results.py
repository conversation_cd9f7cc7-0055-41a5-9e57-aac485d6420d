#!/usr/bin/env python3
"""
ResNet9 CIFAR10 算法对比试验结果分析脚本
分析6个联邦学习算法的性能对比
"""

import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

# 算法列表
ALGORITHMS = ['fedbuff', 'fedac', 'fadas', 'fedasync', 'scafl', 'refedscafl']
ALGORITHM_NAMES = {
    'fedbuff': 'FedBuff',
    'fedac': 'FedAC', 
    'fadas': 'FADAS',
    'fedasync': 'FedAsync',
    'scafl': 'SCAFL',
    'refedscafl': 'ReFedSCAFL'
}

def load_results():
    """加载所有算法的结果数据"""
    results = {}
    base_path = Path("results/comparison")
    
    for algo in ALGORITHMS:
        csv_path = base_path / algo / "results.csv"
        if csv_path.exists():
            try:
                df = pd.read_csv(csv_path)
                results[algo] = df
                print(f"✓ 成功加载 {ALGORITHM_NAMES[algo]} 结果")
            except Exception as e:
                print(f"✗ 加载 {ALGORITHM_NAMES[algo]} 结果失败: {e}")
        else:
            print(f"✗ 未找到 {ALGORITHM_NAMES[algo]} 结果文件: {csv_path}")
    
    return results

def plot_accuracy_comparison(results):
    """绘制准确率对比图"""
    plt.figure(figsize=(12, 8))
    
    for algo, df in results.items():
        if 'global_accuracy' in df.columns and 'round' in df.columns:
            plt.plot(df['round'], df['global_accuracy'], 
                    label=ALGORITHM_NAMES[algo], linewidth=2, marker='o', markersize=4)
    
    plt.xlabel('Training Round')
    plt.ylabel('Global Accuracy')
    plt.title('ResNet9 CIFAR10 - Global Accuracy Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('accuracy_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_convergence_time(results):
    """绘制收敛时间对比图"""
    plt.figure(figsize=(12, 8))
    
    for algo, df in results.items():
        if 'elapsed_time' in df.columns and 'round' in df.columns:
            plt.plot(df['round'], df['elapsed_time'], 
                    label=ALGORITHM_NAMES[algo], linewidth=2, marker='s', markersize=4)
    
    plt.xlabel('Training Round')
    plt.ylabel('Elapsed Time (seconds)')
    plt.title('ResNet9 CIFAR10 - Training Time Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('time_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_summary_table(results):
    """生成性能摘要表"""
    summary_data = []
    
    for algo, df in results.items():
        if df.empty:
            continue
            
        # 计算关键指标
        final_accuracy = df['global_accuracy'].iloc[-1] if 'global_accuracy' in df.columns else 0
        max_accuracy = df['global_accuracy'].max() if 'global_accuracy' in df.columns else 0
        total_time = df['elapsed_time'].iloc[-1] if 'elapsed_time' in df.columns else 0
        total_rounds = len(df)
        
        # 计算收敛轮数（达到80%准确率的轮数）
        convergence_round = None
        if 'global_accuracy' in df.columns:
            convergence_mask = df['global_accuracy'] >= 0.8
            if convergence_mask.any():
                convergence_round = df[convergence_mask]['round'].iloc[0]
        
        summary_data.append({
            'Algorithm': ALGORITHM_NAMES[algo],
            'Final Accuracy': f"{final_accuracy:.4f}",
            'Max Accuracy': f"{max_accuracy:.4f}",
            'Total Time (s)': f"{total_time:.2f}",
            'Total Rounds': total_rounds,
            'Convergence Round (80%)': convergence_round if convergence_round else 'N/A'
        })
    
    summary_df = pd.DataFrame(summary_data)
    print("\n" + "="*80)
    print("算法性能摘要表")
    print("="*80)
    print(summary_df.to_string(index=False))
    print("="*80)
    
    # 保存摘要表
    summary_df.to_csv('performance_summary.csv', index=False)
    print("\n摘要表已保存到: performance_summary.csv")
    
    return summary_df

def plot_accuracy_vs_time(results):
    """绘制准确率vs时间效率图"""
    plt.figure(figsize=(10, 8))
    
    for algo, df in results.items():
        if 'global_accuracy' in df.columns and 'elapsed_time' in df.columns:
            final_accuracy = df['global_accuracy'].iloc[-1]
            total_time = df['elapsed_time'].iloc[-1]
            plt.scatter(total_time, final_accuracy, s=100, 
                       label=ALGORITHM_NAMES[algo], alpha=0.7)
            plt.annotate(ALGORITHM_NAMES[algo], 
                        (total_time, final_accuracy),
                        xytext=(5, 5), textcoords='offset points')
    
    plt.xlabel('Total Training Time (seconds)')
    plt.ylabel('Final Global Accuracy')
    plt.title('ResNet9 CIFAR10 - Accuracy vs Training Time')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('accuracy_vs_time.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("ResNet9 CIFAR10 算法对比试验结果分析")
    print("="*50)
    
    # 加载结果数据
    results = load_results()
    
    if not results:
        print("未找到任何结果文件，请先运行实验！")
        return
    
    print(f"\n成功加载 {len(results)} 个算法的结果数据")
    
    # 生成分析图表
    print("\n正在生成分析图表...")
    
    try:
        # 1. 准确率对比图
        plot_accuracy_comparison(results)
        print("✓ 准确率对比图已生成: accuracy_comparison.png")
        
        # 2. 训练时间对比图
        plot_convergence_time(results)
        print("✓ 训练时间对比图已生成: time_comparison.png")
        
        # 3. 准确率vs时间效率图
        plot_accuracy_vs_time(results)
        print("✓ 准确率vs时间效率图已生成: accuracy_vs_time.png")
        
        # 4. 性能摘要表
        generate_summary_table(results)
        
    except Exception as e:
        print(f"生成图表时出错: {e}")
    
    print("\n分析完成！")
    print("生成的文件:")
    print("- accuracy_comparison.png: 准确率对比图")
    print("- time_comparison.png: 训练时间对比图") 
    print("- accuracy_vs_time.png: 准确率vs时间效率图")
    print("- performance_summary.csv: 性能摘要表")

if __name__ == "__main__":
    main()
