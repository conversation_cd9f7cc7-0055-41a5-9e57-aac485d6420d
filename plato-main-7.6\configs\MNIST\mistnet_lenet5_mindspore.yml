clients:
    # Type
    type: mistnet

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    type: mistnet

    address: 127.0.0.1
    port: 8000

data: !include mnist_iid.yml

trainer:
    # Should <PERSON><PERSON>pore be used instead of PyTorch?
    use_mindspore: true

    # Should <PERSON><PERSON><PERSON> be used with <PERSON> as its target?
    cpuonly: true

    # The type of the trainer
    type: basic

    # The maximum number of training rounds in total
    rounds: 1

    # The target accuracy
    target_accuracy: 0.96

    # The machine learning model
    model_name: lenet5
    pretrained_model: lenet5.ckpt

    # Parameters for local training in each communication round
    epochs: 6
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: mistnet
    epsilon: null

parameters:
    model:
        num_classes: 10
        cut_layer: pool1

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0
