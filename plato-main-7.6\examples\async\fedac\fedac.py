"""
A federated learning server using FedAC.

Reference:
<PERSON>, <PERSON><PERSON>*, <PERSON><PERSON>1, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>1
"Efficient Asynchronous Federated Learning with Prospective Momentum Aggregation and Fine-Grained Correction, "
im Proc. AAAI 2024
"""

import fedac_client
import fedac_server
import fedac_trainer
import fedac_lenet5

from fedac_callback import ScaffoldCallback
from plato.models import registry as models_registry

def main():
    """A Plato federated learning training session using the FedAC algorithm."""
    trainer = fedac_trainer.Trainer
    client = fedac_client.Client(trainer=trainer, callbacks=[ScaffoldCallback])
    server = fedac_server.Server(trainer=trainer, )

    server.run(client)

if __name__ == "__main__":
    main()