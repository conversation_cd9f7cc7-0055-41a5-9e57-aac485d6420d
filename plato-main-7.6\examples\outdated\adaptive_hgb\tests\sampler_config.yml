clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 10

    # The number of clients selected in each round
    per_round: 5

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8000
    synchronous: false
    periodic_interval: 20

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 12000

    # IID or non-IID?
    sampler: iid

    # The random seed for sampling data
    random_seed: 1

    # this is for the quantity label noniid
    per_client_classes_size: 5

    # this for the distribution nonidd
    min_partition_size: 200
    label_concentration: 0.1
    client_quantity_concentration: 0.1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 10

    # The maximum number of clients running concurrently
    max_concurrency: 5

    # The target accuracy
    target_accuracy: 0.97
    
    # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 2
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0