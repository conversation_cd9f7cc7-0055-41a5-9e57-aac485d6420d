clients:
    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

    random_seed: 10

server:
    address: 127.0.0.1
    port: 8081
    simulate_wall_time: true
    do_test: false
    checkpoint_path: checkpoints/resnet18_cifar100
    model_path: models/resnet18_cifar100

    random_seed: 1

data:
    # The training and testing dataset
    datasource: CIFAR100

    # Where the dataset is located
    # data_path: data/tiny-imagenet-200

    # Number of samples in each partition
    partition_size: 128

    # IID or non-IID?
    sampler: iid

    random_seed: 1

trainer:
    # The maximum number of training rounds
    rounds: 1000

    # The maximum number of clients running concurrently
    max_concurrency: 4

    # The target accuracy
    target_accuracy: 1.0

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 128
    optimizer: SGD

    # The machine learning model
    model_name: resnet_18

algorithm:
    # Aggregation algorithm
    type: fedavg

    ## Data reconstruction related configurations
    # Choose from DLG (default), iDLG, csDLG
    attack_method: csDLG
    # For calculating the loss between dummy and target gradients/updates
    # Choose from l2, l1, max, sim, simlocal
    cost_fn: sim
    # Weight for adding total data variation to the loss
    total_variation: 0.01
    # Optimizer used for reconstructing data
    rec_optim: Adam # ["Adam", "SGD", "LBFGS"]
    # Learning rate of the optimizer to reconstruct data
    rec_lr: 0.1
    lr_decay: true
    cost_weights: equal # ["linear", "exp", "equal"]
    boxed: true

    # in a particular communication round (starting from 1)
    attack_round: 1
    # One particular client, e.g., the first selected client
    victim_client: 0
    num_iters: 4000
    log_interval: 1000

    # model mode
    target_eval: true
    dummy_eval: true

    # Dummy data initialization
    init_data: randn # ["randn", "rand", "zeros", "half"]
    # Whether or not use customized initialization for model weights
    init_params: false
    # Whether sharing gradients or model weights
    share_gradients: true
    # Do reconstruction by directly matching weights when it's true,
    # otherwise by matching gradients calculated from updates
    match_weights: false
    # Matching weights (absolute) or weight updates (delta)
    use_updates: true
    # The number of times the attack will be attempted
    trials: 1

    # fishing attack related
    fishing: true
    signed: soft
    class_multiplier: 0.5
    bias_multiplier: 1000
    feat_multiplier: 300
    reset_param_weights: true
    target_cls: 1
    target_cls_idx: 0  # use the target class index when the target class cannot be found
    grad_idx: 0 # which gradient to attack
    feat_threshold: 0.000001 # When to identify two features as identical

    ## Defense related
    # Choose from GradDefense, Soteria, GC (model compression), DP (differential privacy), Outpost (ours)...
    defense: DP
    # for Outpost
    beta: 0.125 # controls iteration decay speed
    phi: 10 # the amount (%) of perturbation
    prune_base: 80 # for pruning
    noise_base: 0.1 # controls scale for gaussian noise
    # for GradDefense
    clip: true
    slices_num: 10
    perturb_slices_num: 5
    scale: 0.01
    Q: 6
    # for Soteria
    threshold: 50
    # for GC
    prune_pct: 80
    # for DP
    epsilon: 0.1

    noise_random_seed: 1
    # The random seed for dummy data and label
    random_seed: 10

parameters:
    model:
        num_classes: 100

    optimizer:
        lr: 0.01

results:
    result_path: results/fishing/resnet18_cifar100/

    # Write the following parameter(s) into a CSV
    types: round, accuracy, elapsed_time, round_time

    # Plot results (x_axis-y_axis)
    plot: round-accuracy, elapsed_time-accuracy

    cols: 8
