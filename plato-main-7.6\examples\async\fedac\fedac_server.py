"""
A federated learning server using FedAC.

Reference:
<PERSON>, <PERSON><PERSON>*, <PERSON><PERSON>1, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
"Efficient Asynchronous Federated Learning with Prospective Momentum Aggregation and Fine-Grained Correction, "
im Proc. AAAI 2024
"""

import asyncio
import copy
import os
import logging
import math
import statistics

import torch
import torch.nn.functional as F
from collections import OrderedDict
from plato.utils import fonts

from plato.config import Config
from plato.servers import fedavg


class Server(fedavg.Server):
    """A federated learning server using the FedAsync algorithm."""
    def __init__(
            self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None
    ):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )
        self.server_control_variate = None
        self.received_client_control_variates = None

        self.hat_m = None
        self.v = None
        
        self.current_round_losses = []
        self.avg_loss = 0.0

        # 添加陈旧度记录
        self.current_round_staleness = []  # 当前轮次的陈旧度列表
        self.avg_staleness = 0.0  # 平均陈旧度

    async def cosine_similarity(self, update, staleness):
        """Compute the cosine similarity of the received updates and the difference
        between the current and a previous model according to client staleness."""
        # Loading the global model from a previous round according to staleness
        filename = f"model_{self.current_round - 2}.pth"
        model_path = Config().params["model_path"]
        model_path = f"{model_path}/{filename}"

        similarity = 1.0

        if staleness > 1 and os.path.exists(model_path):
            previous_model = copy.deepcopy(self.trainer.model)
            previous_model.load_state_dict(torch.load(model_path))

            previous = torch.zeros(0)
            for __, weight in previous_model.cpu().state_dict().items():
                previous = torch.cat((previous, weight.view(-1)))

            current = torch.zeros(0)
            for __, weight in self.trainer.model.cpu().state_dict().items():
                current = torch.cat((current, weight.view(-1)))

            deltas = torch.zeros(0)
            for __, delta in update.items():
                deltas = torch.cat((deltas, delta.view(-1)))

            similarity = abs(F.cosine_similarity(current - previous, deltas, dim=0))

        return similarity

    async def aggregate_deltas(self, updates, deltas_received):
        """Aggregate weight updates from the clients using federated averaging."""
        if self.hat_m is None:
            # params in AMSGrad
            self.hat_m = {name: self.trainer.zeros(delta.shape) for name, delta in deltas_received[0].items()}
            self.v = copy.deepcopy(self.hat_m)

        # Extract the total number of samples
        self.total_samples = sum(update.report.num_samples for update in updates)

        # Constructing the aggregation weights to be used
        aggregation_weights = []

        # Reset the list of losses for this round
        self.current_round_losses = []
        self.current_round_staleness = []  # 重置当前轮次陈旧度列表

        for i, update in enumerate(deltas_received):
            staleness = updates[i].staleness
            similarity = await self.cosine_similarity(update, staleness)
            # 将不同用户的similarity聚合
            aggregation_weights.append(similarity)

            # 记录陈旧度
            self.current_round_staleness.append(staleness)
            

        # Eq(5) Normalize so that the sum of aggregation weights equals 1
        aggregation_weights = [
            i / sum(aggregation_weights) for i in aggregation_weights
        ]

        logging.info(
            "[Server #%s] normalized aggregation weights: %s",
            os.getpid(),
            aggregation_weights,
        )

        # Perform weighted averaging
        global_delta = {
            name: self.trainer.zeros(delta.shape)
            for name, delta in deltas_received[0].items()
        }
        avg_update = {
            name: self.trainer.zeros(delta.shape)
            for name, delta in deltas_received[0].items()
        }

        # 将用户的模型平均
        for i, update in enumerate(deltas_received):
            #read loss from each report
            report = updates[i].report
            if hasattr(report, 'final_loss'):
                    client_id = updates[i].client_id
                    loss = report.final_loss

                    # Store loss for this client
                    self.current_round_losses.append(loss)

                    logging.info(f"[Server] Client {client_id} reported loss: {loss}")
            
            
            for name, delta in update.items():
                global_delta[name] += delta * aggregation_weights[i]

        # 设置参数
        beta1 = Config().server.fedac_beta1
        beta2 = Config().server.fedac_beta2

        # 更新Adam算法的相关参数
        for name, delta in global_delta.items():
            # m_t
            self.hat_m[name].mul_(beta1).add_(delta, alpha=1 - beta1)
            # v_t
            self.v[name].mul_(beta2).addcmul_(delta, delta.conj(), value=1 - beta2)
            # \sqrt{\hat{v_t}}+\epsilon
            denom = (self.v[name].sqrt()).add_(Config().server.fedac_eps)
            # step_size = lr_lambda
            avg_update[name].addcdiv_(self.hat_m[name], denom, value=Config().server.fedac_global_lr)

            # Yield to other tasks in the server
            await asyncio.sleep(0)

        # Calculate average loss for this round if we have any losses
        if self.current_round_losses:
            self.avg_loss = statistics.mean(self.current_round_losses)
            logging.info(f"[Server] Average loss for round {self.current_round}: {self.avg_loss}")

        # Calculate average staleness for this round if we have any staleness values
        if self.current_round_staleness:
            try:
                # 添加数值检查，避免异常值导致崩溃
                valid_staleness = [s for s in self.current_round_staleness if isinstance(s, (int, float)) and not math.isnan(s) and not math.isinf(s)]
                if valid_staleness:
                    self.avg_staleness = statistics.mean(valid_staleness)
                    logging.info(f"[Server] Average staleness for round {self.current_round}: {self.avg_staleness:.2f}")
                    logging.info(f"[Server] Staleness details: {valid_staleness}")
                else:
                    self.avg_staleness = 0
                    logging.warning(f"[Server] No valid staleness values for round {self.current_round}")
            except Exception as e:
                logging.error(f"[Server] Error calculating staleness: {e}")
                self.avg_staleness = 0
        
        return avg_update

    def weights_received(self, weights_received):
        """Compute control variates from clients' updated weights."""
        self.received_client_control_delta_variates = [
            weight[1] for weight in weights_received
        ]

        return [weight[0] for weight in weights_received]

    def weights_aggregated(self, updates):
        """
        Method called at the end of aggregating received weights.
        """
        try:
            # Save the current model for later retrieval when cosine similarity needs to be computed
            filename = f"model_{self.current_round}.pth"
            self.trainer.save_model(filename)

            """Method called after the updated weights have been aggregated."""
            # Update server control variate
            # 公式17
            for client_control_variate_delta in self.received_client_control_delta_variates:
                for name, param in client_control_variate_delta.items():
                    self.server_control_variate[name] += param.cpu() * (1 / len(updates))

            # 清理当前轮次的临时数据，防止内存泄漏
            self.current_round_losses.clear()
            self.current_round_staleness.clear()

            # 强制垃圾回收
            import gc
            gc.collect()

        except Exception as e:
            logging.error(f"[Server] Error in weights_aggregated for round {self.current_round}: {e}")
            import traceback
            logging.error(f"[Server] Traceback: {traceback.format_exc()}")
            raise

    def customize_server_payload(self, payload):
        "Add the server control variate into the server payload."
        if self.server_control_variate is None:
            self.server_control_variate = OrderedDict()
            for name, weight in self.algorithm.extract_weights().items():
                self.server_control_variate[name] = self.trainer.zeros(weight.shape)

        return [payload, self.server_control_variate]

    def get_logged_items(self) -> dict:
            """Get items to be logged by the LogProgressCallback class in a .csv file."""
            logged_items = super().get_logged_items()

            # Add the average loss to the logged items
            logged_items["loss"] = self.avg_loss

            # Add the average staleness to the logged items
            logged_items["avg_staleness"] = self.avg_staleness

            # 添加其他陈旧度相关字段
            if self.current_round_staleness:
                valid_staleness = [s for s in self.current_round_staleness if isinstance(s, (int, float)) and not math.isnan(s) and not math.isinf(s)]
                if valid_staleness:
                    logged_items["max_staleness"] = max(valid_staleness)
                    logged_items["min_staleness"] = min(valid_staleness)
                else:
                    logged_items["max_staleness"] = 0.0
                    logged_items["min_staleness"] = 0.0
            else:
                logged_items["max_staleness"] = 0.0
                logged_items["min_staleness"] = 0.0

            # 添加网络相关字段
            logged_items["network_latency"] = 0.0
            logged_items["network_bandwidth"] = 0.0
            logged_items["network_reliability"] = 1.0
            logged_items["network_success_rate"] = 1.0

            # 添加真实运行时间记录
            import time
            if not hasattr(self, 'real_start_time'):
                self.real_start_time = time.time()
            logged_items["real_elapsed_time"] = time.time() - self.real_start_time

            return logged_items