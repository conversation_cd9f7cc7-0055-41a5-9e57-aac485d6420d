clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 200

    # The number of clients selected in each round
    per_round: 20

    # Should the clients compute test accuracy locally?
    do_test: false

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: zipf # zipf is used.
        s: 1.2

    # The maximum amount of time for clients to sleep after each epoch
    #max_sleep_time: 10

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: true

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 10

    random_seed: 1
server:
    address: 127.0.0.1
    port: 6010
    random_seed: 1
    synchronous: false
    simulate_wall_time: true
    minimum_clients_aggregated: 10
    step_window: 2
    penalty: 2
    exploration_factor: 0.9
    desired_duration: 50
      # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 10

    checkpoint_path: results/FEMNIST/test/checkpoint
    model_path: results/FEMNIST/test/model

data:
    # The training and testing dataset
    datasource: FEMNIST

    reload_data: true

    # Number of samples in each partition
    #partition_size: 1000

    # IID or non-IID?
    sampler: all_inclusive

    #concentration: 1

    #testset_size: 1000

    # The random seed for sampling data
    random_seed: 4

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 150

    # The maximum number of clients running concurrently
    max_concurrency: 6

    # The target accuracy
    target_accuracy: 1.0

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD

    # The machine learning model
    model_name: lenet5

algorithm:
    # Aggregation algorithm
    type: fedavg

results:
    # Write the following parameter(s) into a CSV
    types: round, accuracy, elapsed_time, comm_time, round_time
    result_path: /data/ykang/plato/results/polairs2/FEMNIST/verify

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0
    model:
      num_classes: 62


