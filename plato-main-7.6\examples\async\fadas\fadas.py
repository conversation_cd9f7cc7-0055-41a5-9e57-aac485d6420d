"""
A federated learning training session using FADAS.

Reference:

<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON>, "FADAS: Towards Federated Adaptive Asynchronous Optimization,"
in Proc. Forty-first International Conference on Machine Learning (ICML 2024).

https://openreview.net/forum?id=j56JAd29uH
"""

import fadas_server


def main():
    """A Plato federated learning training session using FADAS."""
    server = fadas_server.Server()
    server.run()


if __name__ == "__main__":
    main()
