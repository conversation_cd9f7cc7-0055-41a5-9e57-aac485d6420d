clients:
    # Type
    type: simple

    # 减少客户端数量以提高稳定性
    total_clients: 10

    # 减少每轮客户端数量
    per_round: 3

    # *Should the clients compute test accuracy locally?(test local model on local dataset)
    do_test: true

    # *Should the clients compute test accuracy with global model?(test global model on local dataset)
    do_global_test: true

    # 关闭客户端异构性模拟以提高稳定性
    speed_simulation: false

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # 减少睡眠时间
    max_sleep_time: 1

    # 使用睡眠模拟而不是真实睡眠
    sleep_simulation: true

    # 如果模拟客户端训练时间，平均训练时间是多少？
    avg_training_time: 2

    # 简化网络模拟配置
    network_simulation: false

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8007
    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server? Useful if max_concurrency is specified
    simulate_wall_time: true

    # (fedbuff)
    # 减少最小聚合客户端数量
    minimum_clients_aggregated: 2

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 5

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: true

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/mnist/01
    model_path: models/mnist/01

    random_seed: 1

    # (FADAS)
    # beta1/2 for adam
    beta1: 0.9
    beta2: 0.99
    # the threshold for adapting the global learning rate (similar with staleness_Bound)
    tauc: 1
    # eps for adam
    eps: 0.00000001
    # learning rate for global model
    global_lr: 0.01

data:
    # The training and testing dataset
    datasource: MNIST

    # 增加每个分区的样本数量
    partition_size: 500

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution(alpha)
    concentration: 0.1

    # The size of the testset on the server
    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

    # *get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # 减少训练轮数进行测试
    rounds: 5

    # 减少最大并发数
    max_concurrency: 2

    # The target accuracy
    target_accuracy: 0.8

    # 减少本地训练轮数
    epochs: 2
    batch_size: 32
    optimizer: SGD
    lr_scheduler: LambdaLR

    # The machine learning model
    model_name: lenet5

algorithm:
    # Aggregation algorithm
    type: fedavg
    lamda: 1.0

parameters:
    model:
        num_classes: 10
        # 移除 in_channels 参数，使用LeNet5的默认值（1通道）
        # in_channels: 1  # 注释掉这行来避免TypeError

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001
    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    result_path: results/mnist/01

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness
