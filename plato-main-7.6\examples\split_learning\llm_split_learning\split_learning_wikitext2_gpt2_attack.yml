clients:
    # Type
    type: split_learning

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

    # Split learning iterations for each client
    iteration: 5

server:
    type: split_learning
    random_seed: 1
    address: 127.0.0.1
    port: 8001

    # Server doesn't have to do test for every round in split learning
    do_test: false

data:
    # The training and testing dataset
    datasource: HuggingFace
    dataset_name: wikitext
    dataset_config: wikitext-2-v1

    # Number of samples in each partition
    partition_size: 12000

    # Fixed random seed
    random_seed: 1

    # IID, biased, or sharded?
    sampler: iid

trainer:
    # The type of the trainer
    type: split_learning

    # The maximum number of training rounds
    rounds: 60

    # The machine learning model
    model_type: huggingface
    model_name: gpt2

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 4
    optimizer: AdamW
    
algorithm:
    # Aggregation algorithm
    type: split_learning

    # Split learning flag
    split_learning: true

parameters:
    model:
        num_classes: 10
        cut_layer: 1
        transformer_module_name: transformer.h
        layers_after_transformer:
            - transformer.ln_f
            - lm_head

    optimizer:
        lr: 0.00005
        eps: 0.00000001
        weight_decay: 0.0

    attack:
        interval: 4
        optimizer:
            lr_guessed_client: 0.1
            lr_reconstructed_data: 0.01
        outer_iterations: 100
        inner_iterations: 100
        report_interval: 10
        embedding_layer: transformer.wte
        calibrate_guessed_client: false

results: 
    types: round, accuracy, elapsed_time, comm_overhead, attack_accuracy,rouge1_fm,rouge1_p,rouge1_r,rouge2_fm,rouge2_p,rouge2_r,rougeL_fm,rougeL_p,rougeL_r,rougeLsum_fm,rougeLsum_p,rougeLsum_r
