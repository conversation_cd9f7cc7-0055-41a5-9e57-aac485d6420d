[INFO][22:26:09]: 日志系统已初始化
[INFO][22:26:09]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250728_222609.log
[INFO][22:26:09]: 日志级别: INFO
[WARNING][22:26:09]: 无法获取系统信息: No module named 'psutil'
[INFO][22:26:09]: 🚀 ReFedScaFL 训练开始
[INFO][22:26:09]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][22:26:09]: 开始时间: 2025-07-28 22:26:09
[INFO][22:26:09]: [Client None] 基础初始化完成
[INFO][22:26:09]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][22:26:09]: 创建并缓存共享模型
[INFO][22:26:09]: [93m[1m[32428] Logging runtime results to: ././results/refedscafl_cifar10_resnet9/32428.csv.[0m
[INFO][22:26:09]: [Server #32428] Started training on 10 clients with 5 per round.
[INFO][22:26:09]: 服务器参数配置完成：
[INFO][22:26:09]: - 客户端数量: total=10, per_round=5
[INFO][22:26:09]: - 权重参数: success=0.8, distill=0.2
[INFO][22:26:09]: - SCAFL参数: V=1.0, tau_max=5
[INFO][22:26:09]: 从共享资源模型提取并缓存全局权重
[INFO][22:26:09]: [Server #32428] Configuring the server...
[INFO][22:26:09]: Training: 500 rounds or accuracy above 80.0%

[INFO][22:26:09]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][22:26:09]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][22:26:09]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][22:26:09]: [Trainer Init] 训练器初始化完成，参数：batch_size=64, learning_rate=0.1, epochs=3
[INFO][22:26:09]: Algorithm: fedavg
[INFO][22:26:09]: Data source: CIFAR10
[INFO][22:26:12]: Starting client #1's process.
[INFO][22:26:12]: Starting client #2's process.
[INFO][22:26:12]: Starting client #3's process.
[INFO][22:26:12]: Starting client #4's process.
[INFO][22:26:12]: Starting client #5's process.
[INFO][22:26:12]: Setting the random seed for selecting clients: 1
[INFO][22:26:12]: Starting a server at address 127.0.0.1 and port 8095.
[INFO][22:26:23]: [Server #32428] A new client just connected.
[INFO][22:26:23]: [Server #32428] A new client just connected.
[INFO][22:26:23]: [Server #32428] New client with id #2 arrived.
[INFO][22:26:23]: [Server #32428] Client process #35292 registered.
[INFO][22:26:23]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][22:26:23]: [Server #32428] New client with id #1 arrived.
[INFO][22:26:23]: [Server #32428] Client process #37240 registered.
[INFO][22:26:23]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][22:26:23]: [Server #32428] A new client just connected.
[INFO][22:26:23]: [Server #32428] New client with id #5 arrived.
[INFO][22:26:23]: [Server #32428] Client process #20096 registered.
[INFO][22:26:23]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][22:26:23]: [Server #32428] A new client just connected.
[INFO][22:26:23]: [Server #32428] A new client just connected.
[INFO][22:26:23]: [Server #32428] New client with id #3 arrived.
[INFO][22:26:23]: [Server #32428] Client process #17848 registered.
[INFO][22:26:23]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][22:26:23]: [Server #32428] New client with id #4 arrived.
[INFO][22:26:23]: [Server #32428] Client process #36780 registered.
[INFO][22:26:23]: [Server #32428] Starting training.
[INFO][22:26:23]: [93m[1m
[Server #32428] Starting round 1/500.[0m
[INFO][22:26:23]: [Server #32428] Selected clients: [3, 2, 5, 1, 4]
[INFO][22:26:23]: [Server #32428] Selecting client #3 for training.
[INFO][22:26:23]: [Server #32428] Sending the current model to client #3 (simulated).
[INFO][22:26:23]: [Server #32428] Sending 18.75 MB of payload data to client #3 (simulated).
[INFO][22:26:23]: [Server #32428] Selecting client #2 for training.
[INFO][22:26:23]: [Server #32428] Sending the current model to client #2 (simulated).
[INFO][22:26:24]: [Server #32428] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][22:26:24]: [Server #32428] Selecting client #5 for training.
[INFO][22:26:24]: [Server #32428] Sending the current model to client #5 (simulated).
[INFO][22:26:24]: [Server #32428] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][22:26:24]: [Server #32428] Selecting client #1 for training.
[INFO][22:26:24]: [Server #32428] Sending the current model to client #1 (simulated).
[INFO][22:26:24]: [Server #32428] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][22:26:24]: [Server #32428] Selecting client #4 for training.
[INFO][22:26:24]: [Server #32428] Sending the current model to client #4 (simulated).
[INFO][22:26:24]: [Server #32428] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][22:26:24]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][22:35:35]: [Server #32428] An existing client just disconnected.
[WARNING][22:35:35]: [Server #32428] Client process #17848 disconnected and removed from this server, 4 client processes are remaining.
[WARNING][22:35:35]: [93m[1m[Server #32428] Closing the server due to a failed client.[0m
[INFO][22:35:35]: [Server #32428] Training concluded.
[INFO][22:35:35]: [Server #32428] Model saved to ./models/refedscafl/cifar10_resnet9/resnet_9.pth.
[INFO][22:35:35]: [Server #32428] Closing the server.
[INFO][22:35:35]: Closing the connection to client #35292.
[INFO][22:35:35]: Closing the connection to client #37240.
[INFO][22:35:35]: [Server #32428] An existing client just disconnected.
[WARNING][22:35:35]: [Server #32428] Client process #36780 disconnected and removed from this server, 3 client processes are remaining.
[WARNING][22:35:35]: [93m[1m[Server #32428] Closing the server due to a failed client.[0m
[INFO][22:35:35]: [Server #32428] Training concluded.
[INFO][22:35:35]: [Server #32428] Model saved to ./models/refedscafl/cifar10_resnet9/resnet_9.pth.
[INFO][22:35:35]: [Server #32428] Closing the server.
[INFO][22:35:35]: Closing the connection to client #35292.
[INFO][22:35:35]: [Server #32428] An existing client just disconnected.
[WARNING][22:35:35]: [Server #32428] Client process #35292 disconnected and removed from this server, 2 client processes are remaining.
[WARNING][22:35:35]: [93m[1m[Server #32428] Closing the server due to a failed client.[0m
[INFO][22:35:35]: [Server #32428] Training concluded.
[INFO][22:35:35]: [Server #32428] Model saved to ./models/refedscafl/cifar10_resnet9/resnet_9.pth.
[INFO][22:35:35]: [Server #32428] Closing the server.
[INFO][22:35:35]: Closing the connection to client #37240.
[INFO][22:35:35]: Closing the connection to client #20096.
[INFO][22:35:35]: [Server #32428] An existing client just disconnected.
[WARNING][22:35:35]: [Server #32428] Client process #37240 disconnected and removed from this server, 1 client processes are remaining.
[WARNING][22:35:35]: [93m[1m[Server #32428] Closing the server due to a failed client.[0m
[INFO][22:35:35]: [Server #32428] Training concluded.
[INFO][22:35:35]: [Server #32428] Model saved to ./models/refedscafl/cifar10_resnet9/resnet_9.pth.
[INFO][22:35:35]: [Server #32428] Closing the server.
[INFO][22:35:35]: Closing the connection to client #20096.
[INFO][22:35:35]: Closing the connection to client #37240.
[INFO][22:35:35]: Closing the connection to client #20096.
[INFO][22:35:35]: Closing the connection to client #36780.
[INFO][22:35:35]: [Server #32428] An existing client just disconnected.
[WARNING][22:35:35]: [Server #32428] Client process #20096 disconnected and removed from this server, 0 client processes are remaining.
[WARNING][22:35:35]: [93m[1m[Server #32428] All clients disconnected, closing the server.[0m
[INFO][22:35:35]: [Server #32428] Training concluded.
[INFO][22:35:35]: [Server #32428] Model saved to ./models/refedscafl/cifar10_resnet9/resnet_9.pth.
[INFO][22:35:35]: [Server #32428] Closing the server.
