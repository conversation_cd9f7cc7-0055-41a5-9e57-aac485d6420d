import pandas as pd
import matplotlib.pyplot as plt

def plot_data():
    """
    Reads the provided CSV files, plots global accuracy against time and rounds,
    and highlights the 'Refedscafl-ours' data.
    """
    # List of files to process
    file_names = [
        'fadas_MNIST_lenet5_alpha0.1_fixed_20250721_2259.csv',
        'fedac_MNIST_network_test_20250721_1302.csv',
        'Fedasync.csv',
        'fedbuff__EMNIST_lenet5_1830070.csv',
        'refedscafl_EMNIST_lenet5_20250724_1237.csv',
        'sc_afl_emnist_lenet5_with_network_20250724_104507.csv'
    ]

    data_frames = {}

    # Load each file, handling potential FileNotFoundError
    for file_name in file_names:
        try:
            df = pd.read_csv(file_name)
            data_frames[file_name] = df
        except FileNotFoundError:
            print(f"File not found: {file_name}")
            continue

    # Prepare the data for plotting, separating the highlighted dataset
    dfs_to_plot = {}
    highlight_df = None
    highlight_label = 'Refedscafl-ours'
    highlight_file = 'refedscafl_EMNIST_lenet5_20250724_1237.csv'

    for file_name, df in data_frames.items():
        if file_name == highlight_file:
            highlight_df = df
        else:
            # Create a clean label from the filename
            label = file_name.split('_')[0]
            dfs_to_plot[label] = df

    # Add the highlight file to the list of dataframes to plot last,
    # ensuring it is drawn on top
    if highlight_df is not None:
        dfs_to_plot[highlight_label] = highlight_df

    def create_plot(dfs, x_col, y_col, title, x_label, y_label, filename):
        """
        Creates and saves a plot with a highlighted line.
        """
        plt.figure(figsize=(10, 6))
        plt.title(title)
        plt.xlabel(x_label)
        plt.ylabel(y_label)

        # Plot all non-highlighted lines first with a lower alpha
        for label, df in list(dfs.items())[:-1]:
            plt.plot(df[x_col], df[y_col], label=label, alpha=0.7)

        # Plot the highlighted line last to make it prominent
        highlight_label = list(dfs.keys())[-1]
        highlight_df = list(dfs.values())[-1]
        plt.plot(highlight_df[x_col], highlight_df[y_col], label=highlight_label, linewidth=3, linestyle='--', color='red')

        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(filename)
        print(f"Plot saved as {filename}")

    # Create the two plots
    # Plot 1: Global accuracy vs. elapsed time
    create_plot(
        dfs_to_plot,
        x_col='elapsed_time',
        y_col='global_accuracy',
        title='Global Accuracy vs. Elapsed Time',
        x_label='Elapsed Time',
        y_label='Global Accuracy',
        filename='global_accuracy_vs_time.png'
    )

    # Plot 2: Global accuracy vs. round
    create_plot(
        dfs_to_plot,
        x_col='round',
        y_col='global_accuracy',
        title='Global Accuracy vs. Round',
        x_label='Round',
        y_label='Global Accuracy',
        filename='global_accuracy_vs_round.png'
    )

if __name__ == '__main__':
    plot_data()