clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 10

    # Should the clients compute test accuracy locally?
    do_test: false

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The simulation distribution
    simulation_distribution:
        distribution: pareto
        alpha: 1
    max_sleep_time: 30
    sleep_simulation: true
    avg_training_time: 10

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8000
    synchronous: false
    simulate_wall_time: true

    # Parameters for FedAsync
    staleness_bound: 1000 # FedAsync doesn't have any staleness bound
    minimum_clients_aggregated: 1
    mixing_hyperparameter: 0.9
    adaptive_mixing: true
    staleness_weighting_function:
        type: Polynomial
        pa: 0.5
        ha: 10
        hb: 2

    checkpoint_path: models/mnist/01
    model_path: models/mnist/01

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 300

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution
    concentration: 0.1

    # The random seed for sampling data
    random_seed: 1

    # The size of the testset on the server
    testset_size: 100

    # *get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 10

    # The maximum number of clients running concurrently
    max_concurrency: 2

    # The target accuracy
    target_accuracy: 1

    # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    model:
        num_classes: 10

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

results:
    result_path: results/mnist/01
    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std
