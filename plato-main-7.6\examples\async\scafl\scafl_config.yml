clients:
    type: simple
    total_clients: 20
    per_round: 5
    do_test: true
    speed_simulation: true
    simulation_distribution:
        distribution: pareto
        alpha: 1
    max_sleep_time: 5
    sleep_simulation: false
    avg_training_time: 5
    random_seed: 1

server:
    address: 127.0.0.1
    port: 8000
    synchronous: false
    simulate_wall_time: true

    # SC-AFL 专属参数
    tau_max: 10
    V: 10

    # 是否保存模型/日志
    checkpoint_path: models/scafl/mnist
    model_path: models/scafl/mnist

data:
    datasource: MNIST
    partition_size: 300
    sampler: noniid
    concentration: 0.1  # 非IID程度强（越小越极端）
    random_seed: 1

trainer:
    type: basic
    rounds: 50
    max_concurrency: 3
    target_accuracy: 0.98  # 目标准确率
    model_name: lenet5
    epochs: 5
    batch_size: 32
    optimizer: SGD

algorithm:
    type: fedavg

parameters:
    model:
        num_classes: 10

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001

results:
    result_path: results/scafl/mnist
    types: round, elapsed_time, accuracy
