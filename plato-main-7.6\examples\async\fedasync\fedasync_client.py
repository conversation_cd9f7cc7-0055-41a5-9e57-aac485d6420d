import copy
import logging
import os
import time

import random
import numpy as np

from types import SimpleNamespace
from plato.clients import simple
from plato.config import Config
from plato.utils import fonts


# pass global model to trainer to act as a regularizer
class Client(simple.Client):
    def __init__(
            self,
            model=None,
            datasource=None,
            algorithm=None,
            trainer=None,
            callbacks=None,
            trainer_callbacks=None,
    ):
        super().__init__(model, datasource, algorithm, trainer, callbacks, trainer_callbacks)
        self.quality = None  # Whether the client is qualified to complete the training and communication 
        # 客户端是否符合完成训练和通信的条件
        self.communication_time = 0
        self.training_time = 0
        self.sojourn_time = 0
        self.para_file = None

    def create_magnified_file(self):
        """当配置了max_concurrency时,会创建另一个线程的trainer，导致无法存储更改，因此创建创建文件存储"""
        # 在当前目录创建文件，文件名为self.client_id_magnified.csv
        filename = f"{self.client_id}_magnified.csv"
        with open(filename, 'w') as f:
            f.write("alpha,beta,process_id\n")
        # 保存文件名
        return filename

    async def _start_training(self, inbound_payload):
        """Complete one round of training on this client. 所以plato/clients中的用户只需要复写_train函数即可完成不同损失函数的模型训练"""
        # 开始训练的第一步就是把全局模型加载到本地用户上
        self._load_payload(inbound_payload)
        # 传入server model
        report, outbound_payload = await self._train(inbound_payload)

        if Config().is_edge_server():
            logging.info(
                "[Server #%d] Model aggregated on edge server (%s).", os.getpid(), self
            )
        else:
            logging.info("[%s] Model trained.", self)

        return report, outbound_payload

    async def _train(self, server_model=None):
        """接收server model"""
        # 提取初始加载的全局模型
        init_global_model = copy.deepcopy(self.algorithm.extract_weights()) 
        logging.info( 
            fonts.colourize( # 打印客户端开始训练的信息
                f"[{self}] Started training in communication round #{self.current_round}."
            )
        )

        # test global model on local dataset # 测试全局模型在本地数据集上的准确率
        if (hasattr(Config().clients, "do_global_test") and Config().clients.do_global_test):# 客户端配置了do_global_test，且为True
            global_accuracy = self.trainer.test(self.testset, self.testset_sampler) # 测试全局模型在本地数据集上的准确率
            logging.info("[%s] Test global accuracy: %.2f%%", self, 100 * global_accuracy) # 打印全局模型在本地数据集上的准确率

            if global_accuracy == -1: # 全局模型在本地数据集上的准确率为-1
                # The testing process failed, disconnect from the server # 测试过程失败，与服务器断开连接
                logging.info(
                    fonts.colourize(
                        f"[{self}] Global accuracy on local data is -1 when testing. Disconnecting from the server."
                    )
                )
                await self.sio.disconnect() # 与服务器断开连接

        else:
            global_accuracy = 0 # 全局模型在本地数据集上的准确率为0
        # Perform model training # 进行模型训练
        try:
            if hasattr(self.trainer, "current_round"): # hasattr() 函数用于判断一个对象是否包含对应的属性
                self.trainer.current_round = self.current_round
            training_time = self.trainer.train(self.trainset, self.sampler, server_model) # 进行模型训练，返回训练时间
            logging.info( # 打印模型训练时间
                fonts.colourize(f"[{self}] Training time: {training_time}")
            )
        except ValueError as exc: # 模型训练过程中出现 ValueError 异常
            logging.error( # 打印模型训练过程中出现的 ValueError 异常
                fonts.colourize(f"[{self}] Error occurred during training: {exc}")
            )
            await self.sio.disconnect()

        # Extract model weights and biases # 提取模型权重和偏置
        weights = self.algorithm.extract_weights()
        deltas = self.algorithm.compute_weight_deltas(init_global_model, [weights])[0]

        # Generate a report for the server, performing model testing if applicable # 生成服务器报告，进行模型测试
        if (hasattr(Config().clients, "do_test") and Config().clients.do_test) and (
            not hasattr(Config().clients, "test_interval") # 客户端配置了do_test，且为True，且没有配置test_interval
            or self.current_round % Config().clients.test_interval == 0
        ):
            accuracy = self.trainer.test(self.testset, self.testset_sampler)

            if accuracy == -1:
                # The testing process failed, disconnect from the server # 测试过程失败，与服务器断开连接
                logging.info(
                    fonts.colourize(
                        f"[{self}] Accuracy is -1 when testing. Disconnecting from the server."
                    )
                )
                await self.sio.disconnect()

            if hasattr(Config().trainer, "target_perplexity"): # 如果配置了目标困惑度
                logging.info("[%s] Test perplexity: %.2f", self, accuracy) # 打印测试困惑度
            else: 
                logging.info("[%s] Test accuracy: %.2f%%", self, 100 * accuracy) 
        else:
            accuracy = 0

        comm_time = time.time() # 记录通信时间 time.time()是真实时间吗？

        if ( 
            hasattr(Config().clients, "sleep_simulation") # 客户端配置了sleep_simulation
            and Config().clients.sleep_simulation # 客户端配置了sleep_simulation为True
        ):
            sleep_seconds = Config().client_sleep_times[self.client_id - 1] # 客户端配置的睡眠时间
            avg_training_time = Config().clients.avg_training_time # 客户端配置的平均训练时间
            training_time = (#训练时间的计算逻辑（scafl此处需要修改）
                avg_training_time + sleep_seconds # 客户端配置的平均训练时间加上客户端配置的睡眠时间
            ) * Config().trainer.epochs 
        if self.sampler is None:
            num_samples = self.datasource.num_train_examples() # 如果采样器为none，使用数据集的样本数量
        else:
            num_samples = self.sampler.num_samples() # 否则，使用采样器的样本数量
        report = SimpleNamespace( # 生成客户端报告
            client_id=self.client_id, # 客户端ID
            num_samples=num_samples, # 客户端配置的样本数量
            accuracy=accuracy, # 客户端测试准确率
            global_accuracy=global_accuracy, # 全局模型在本地数据集上的准确率
            training_time=training_time, # 客户端训练时间
            comm_time=comm_time, # 客户端通信时间
            update_response=False, # 客户端是否更新响应
            deltas=deltas, # 客户端模型参数更新
            #scafl添加返回的客户端的陈旧度
            stale_degree=0,
        )

        self._report = self.customize_report(report) # 自定义报告

        return self._report, weights

    def configure(self) -> None:
        """Prepares this client for training.""" # 准备客户端进行训练
        super().configure() # 调用父类的configure方法
        logging.info("Configuration started")
        # num_train = self.sampler.num_samples()
        # model_size = self.get_model_size()
        # 根据config中的average_duration为基准，根据正态分布随机生成一个sojourn_time
        unqualified_ratio = 0
        average_duration = 0
        stand_devation = 3 

        # 判断是否有max_concurrency参数，如果有则创建文件
        if hasattr(Config().trainer, "max_concurrency"): 
            self.para_file = self.create_magnified_file()

        if hasattr(Config().parameters, "unqualified_ratio"):
            unqualified_ratio = Config().parameters.unqualified_ratio # 未完成训练的客户端比例
        if hasattr(Config().parameters, "average_duration"): 
            average_duration = Config().parameters.average_duration # 客户端配置的平均训练时间
        self.sojourn_time = random.normalvariate(average_duration, stand_devation)
        sojourn_time = self.sojourn_time 

        # 根据config中的unqualified_ratio为0到1间的概率，将self.sojourn_time方法随机生成一个0.5倍到1.5倍的值
        if np.random.uniform(0, 1) < unqualified_ratio:
            sojourn_time = self.sojourn_time * random.uniform(1, 1.2)
            self.quality = False
        else:
            self.quality = True
        self.trainer.set_client_quality(self.quality)
        # 在0,1之间根据高斯分布采样，根据model_size得到一个0到1之间的值，作为communication_time的比例
        communication_rate = random.normalvariate(0.5, 0.15)
        training_rate = random.normalvariate(0.5, 0.1)
        total_rate = communication_rate + training_rate
        communication_rate = communication_rate / total_rate
        training_rate = training_rate / total_rate
        self.communication_time = sojourn_time * communication_rate
        self.training_time = sojourn_time * training_rate
        logging.info("sojourn_time: %f, communication_time: %f, training_time: %f" % (
            self.sojourn_time, self.communication_time, self.training_time))
        logging.info("Configuration ended")

    def customize_report(self, report: SimpleNamespace) -> SimpleNamespace:
        """Wrap up generating the report with any additional information.
        自定义报告，添加客户端的 stale_degree
        """ 
        if hasattr(Config().trainer, "max_concurrency"):
            #读文件，获取alpha,beta,process_id
            with open(self.para_file, 'r') as f: # 打开文件
                lines = f.readlines() # 读取文件所有行
                last_line = lines[-1]
                #判断是否有记录，若无则跳过
                if last_line == "alpha,beta,process_id\n":
                    return report 
                alpha, beta, process_id = last_line.split(',') 
                alpha = float(alpha)
                beta = float(beta)
                process_id = int(process_id)
                self.trainer.set_alpha(alpha) # 设置学习率
                self.trainer.set_epoch_rate(beta) # 设置训练率
        report.quality = self.quality
        report.epoch_rate = self.trainer.get_epoch_rate()
        report.alpha = self.trainer.get_alpha()

        # Add loss information to the report # 添加损失信息到报告
        if hasattr(self.trainer, 'get_final_loss'):
            report.final_loss = self.trainer.get_final_loss()
            logging.info(f"Client #{self.client_id} final loss: {report.final_loss}")

        #scafl添加返回的客户端的陈旧度
        #记录训练数据后将对应参数回归初始化设置
        self.trainer.set_epoch_rate(1)
        self.trainer.set_alpha(1)
        logging.info("quality: %s, epoch_rate: %f, alpha: %f" % (report.quality, report.epoch_rate, report.alpha))
        return report