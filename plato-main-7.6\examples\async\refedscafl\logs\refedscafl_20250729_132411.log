[INFO][13:24:11]: 日志系统已初始化
[INFO][13:24:11]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_132411.log
[INFO][13:24:11]: 日志级别: INFO
[WARNING][13:24:11]: 无法获取系统信息: No module named 'psutil'
[INFO][13:24:11]: 🚀 ReFedScaFL 训练开始
[INFO][13:24:11]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][13:24:11]: 开始时间: 2025-07-29 13:24:11
[INFO][13:24:11]: [Client None] 基础初始化完成
[INFO][13:24:11]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][13:24:11]: 创建并缓存共享模型
[INFO][13:24:11]: [93m[1m[32416] Logging runtime results to: ./results/refedscafl/cifar10_alpha01/32416.csv.[0m
[INFO][13:24:11]: [Server #32416] Started training on 100 clients with 20 per round.
[INFO][13:24:11]: 服务器参数配置完成：
[INFO][13:24:11]: - 客户端数量: total=100, per_round=20
[INFO][13:24:11]: - 权重参数: success=0.8, distill=0.2
[INFO][13:24:11]: - SCAFL参数: V=1.0, tau_max=5
[INFO][13:24:11]: 从共享资源模型提取并缓存全局权重
[INFO][13:24:11]: [Server #32416] Configuring the server...
[INFO][13:24:11]: Training: 400 rounds or accuracy above 100.0%

[INFO][13:24:11]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][13:24:11]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][13:24:11]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][13:24:11]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][13:24:11]: Algorithm: fedavg
[INFO][13:24:11]: Data source: CIFAR10
[INFO][13:24:12]: Starting client #1's process.
[INFO][13:24:12]: Starting client #2's process.
[INFO][13:24:12]: Starting client #3's process.
[INFO][13:24:12]: Starting client #4's process.
[INFO][13:24:12]: Starting client #5's process.
[INFO][13:24:12]: Starting client #6's process.
[INFO][13:24:12]: Starting client #7's process.
[INFO][13:24:12]: Starting client #8's process.
[INFO][13:24:12]: Starting client #9's process.
[INFO][13:24:12]: Starting client #10's process.
[INFO][13:24:12]: Setting the random seed for selecting clients: 1
[INFO][13:24:12]: Starting a server at address 127.0.0.1 and port 8095.
[INFO][13:24:31]: [Server #32416] A new client just connected.
[INFO][13:24:31]: [Server #32416] New client with id #10 arrived.
[INFO][13:24:31]: [Server #32416] Client process #39476 registered.
[INFO][13:24:31]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][13:24:31]: [Server #32416] A new client just connected.
[INFO][13:24:31]: [Server #32416] New client with id #1 arrived.
[INFO][13:24:31]: [Server #32416] Client process #6488 registered.
[INFO][13:24:31]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][13:24:31]: [Server #32416] A new client just connected.
[INFO][13:24:31]: [Server #32416] A new client just connected.
[INFO][13:24:31]: [Server #32416] New client with id #3 arrived.
[INFO][13:24:31]: [Server #32416] Client process #28028 registered.
[INFO][13:24:31]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][13:24:31]: [Server #32416] A new client just connected.
[INFO][13:24:31]: [Server #32416] New client with id #4 arrived.
[INFO][13:24:31]: [Server #32416] Client process #21884 registered.
[INFO][13:24:31]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][13:24:31]: [Server #32416] New client with id #2 arrived.
[INFO][13:24:31]: [Server #32416] Client process #19588 registered.
[INFO][13:24:31]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][13:24:31]: [Server #32416] A new client just connected.
[INFO][13:24:31]: [Server #32416] New client with id #5 arrived.
[INFO][13:24:31]: [Server #32416] Client process #25540 registered.
[INFO][13:24:31]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][13:24:32]: [Server #32416] A new client just connected.
[INFO][13:24:32]: [Server #32416] New client with id #7 arrived.
[INFO][13:24:32]: [Server #32416] Client process #28684 registered.
[INFO][13:24:32]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][13:24:32]: [Server #32416] A new client just connected.
[INFO][13:24:32]: [Server #32416] A new client just connected.
[INFO][13:24:32]: [Server #32416] New client with id #8 arrived.
[INFO][13:24:32]: [Server #32416] Client process #41232 registered.
[INFO][13:24:32]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][13:24:32]: [Server #32416] New client with id #6 arrived.
[INFO][13:24:32]: [Server #32416] Client process #13648 registered.
[INFO][13:24:32]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][13:24:32]: [Server #32416] A new client just connected.
[INFO][13:24:32]: [Server #32416] New client with id #9 arrived.
[INFO][13:24:32]: [Server #32416] Client process #30028 registered.
[INFO][13:24:32]: [Server #32416] Starting training.
[INFO][13:24:32]: [93m[1m
[Server #32416] Starting round 1/400.[0m
[INFO][13:24:32]: [Server #32416] Selected clients: [18, 73, 98, 9, 33, 16, 64, 58, 61, 84, 49, 27, 13, 63, 4, 50, 56, 78, 99, 1]
[INFO][13:24:32]: [Server #32416] Selecting client #18 for training.
[INFO][13:24:32]: [Server #32416] Sending the current model to client #18 (simulated).
[INFO][13:24:32]: [Server #32416] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][13:24:32]: [Server #32416] Selecting client #73 for training.
[INFO][13:24:32]: [Server #32416] Sending the current model to client #73 (simulated).
[INFO][13:24:32]: [Server #32416] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][13:24:32]: [Server #32416] Selecting client #98 for training.
[INFO][13:24:32]: [Server #32416] Sending the current model to client #98 (simulated).
[INFO][13:24:32]: [Server #32416] Sending 18.75 MB of payload data to client #98 (simulated).
[INFO][13:24:32]: [Server #32416] Selecting client #9 for training.
[INFO][13:24:32]: [Server #32416] Sending the current model to client #9 (simulated).
[INFO][13:24:32]: [Server #32416] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][13:24:32]: [Server #32416] Selecting client #33 for training.
[INFO][13:24:32]: [Server #32416] Sending the current model to client #33 (simulated).
[INFO][13:24:32]: [Server #32416] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][13:24:32]: [Server #32416] Selecting client #16 for training.
[INFO][13:24:32]: [Server #32416] Sending the current model to client #16 (simulated).
[INFO][13:24:32]: [Server #32416] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][13:24:32]: [Server #32416] Selecting client #64 for training.
[INFO][13:24:32]: [Server #32416] Sending the current model to client #64 (simulated).
[INFO][13:24:32]: [Server #32416] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][13:24:32]: [Server #32416] Selecting client #58 for training.
[INFO][13:24:32]: [Server #32416] Sending the current model to client #58 (simulated).
[INFO][13:24:32]: [Server #32416] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][13:24:32]: [Server #32416] Selecting client #61 for training.
[INFO][13:24:32]: [Server #32416] Sending the current model to client #61 (simulated).
[INFO][13:24:32]: [Server #32416] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][13:24:32]: [Server #32416] Selecting client #84 for training.
[INFO][13:24:32]: [Server #32416] Sending the current model to client #84 (simulated).
[INFO][13:24:32]: [Server #32416] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][13:24:32]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][13:28:08]: [Server #32416] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][13:28:13]: [Server #32416] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][13:28:15]: [Server #32416] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][13:28:18]: [Server #32416] Received 18.75 MB of payload data from client #98 (simulated).
[INFO][13:28:21]: [Server #32416] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][13:28:22]: [Server #32416] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][13:28:22]: [Server #32416] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][13:28:22]: [Server #32416] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][13:28:22]: [Server #32416] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][13:28:22]: [Server #32416] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][13:28:22]: [Server #32416] Selecting client #49 for training.
[INFO][13:28:22]: [Server #32416] Sending the current model to client #49 (simulated).
[INFO][13:28:22]: [Server #32416] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][13:28:22]: [Server #32416] Selecting client #27 for training.
[INFO][13:28:22]: [Server #32416] Sending the current model to client #27 (simulated).
[INFO][13:28:22]: [Server #32416] Sending 18.75 MB of payload data to client #27 (simulated).
[INFO][13:28:22]: [Server #32416] Selecting client #13 for training.
[INFO][13:28:22]: [Server #32416] Sending the current model to client #13 (simulated).
[INFO][13:28:22]: [Server #32416] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][13:28:22]: [Server #32416] Selecting client #63 for training.
[INFO][13:28:22]: [Server #32416] Sending the current model to client #63 (simulated).
[INFO][13:28:23]: [Server #32416] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][13:28:23]: [Server #32416] Selecting client #4 for training.
[INFO][13:28:23]: [Server #32416] Sending the current model to client #4 (simulated).
[INFO][13:28:23]: [Server #32416] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][13:28:23]: [Server #32416] Selecting client #50 for training.
[INFO][13:28:23]: [Server #32416] Sending the current model to client #50 (simulated).
[INFO][13:28:23]: [Server #32416] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][13:28:23]: [Server #32416] Selecting client #56 for training.
[INFO][13:28:23]: [Server #32416] Sending the current model to client #56 (simulated).
[INFO][13:28:24]: [Server #32416] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][13:28:24]: [Server #32416] Selecting client #78 for training.
[INFO][13:28:24]: [Server #32416] Sending the current model to client #78 (simulated).
[INFO][13:28:24]: [Server #32416] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][13:28:24]: [Server #32416] Selecting client #99 for training.
[INFO][13:28:24]: [Server #32416] Sending the current model to client #99 (simulated).
[INFO][13:28:25]: [Server #32416] Sending 18.75 MB of payload data to client #99 (simulated).
[INFO][13:28:25]: [Server #32416] Selecting client #1 for training.
[INFO][13:28:25]: [Server #32416] Sending the current model to client #1 (simulated).
[INFO][13:28:25]: [Server #32416] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][13:32:21]: [Server #32416] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][13:32:27]: [Server #32416] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][13:32:27]: [Server #32416] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][13:32:27]: [Server #32416] Received 18.75 MB of payload data from client #27 (simulated).
[INFO][13:32:27]: [Server #32416] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][13:32:27]: [Server #32416] Received 18.75 MB of payload data from client #99 (simulated).
[INFO][13:32:27]: [Server #32416] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][13:32:27]: [Server #32416] Received 18.75 MB of payload data from client #78 (simulated).
[INFO][13:32:27]: [Server #32416] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][13:32:27]: [Server #32416] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][13:32:27]: [Server #32416] Adding client #18 to the list of clients for aggregation.
[INFO][13:32:27]: [Server #32416] Adding client #73 to the list of clients for aggregation.
[INFO][13:32:27]: [Server #32416] Adding client #9 to the list of clients for aggregation.
[INFO][13:32:27]: [Server #32416] Adding client #98 to the list of clients for aggregation.
[INFO][13:32:27]: [Server #32416] Adding client #64 to the list of clients for aggregation.
[INFO][13:32:27]: [Server #32416] Adding client #84 to the list of clients for aggregation.
[INFO][13:32:27]: [Server #32416] Adding client #61 to the list of clients for aggregation.
[INFO][13:32:27]: [Server #32416] Adding client #58 to the list of clients for aggregation.
[INFO][13:32:27]: [Server #32416] Adding client #16 to the list of clients for aggregation.
[INFO][13:32:27]: [Server #32416] Adding client #33 to the list of clients for aggregation.
[INFO][13:32:27]: [Server #32416] Aggregating 10 clients in total.
[INFO][13:32:27]: [Server #32416] Updated weights have been received.
[INFO][13:32:28]: [Server #32416] Aggregating model weight deltas.
[INFO][13:32:28]: [Server #32416] Finished aggregating updated weights.
[INFO][13:32:28]: [Server #32416] Started model testing.
[INFO][13:32:41]: [Trainer.test] 测试完成 - 准确率: 11.65% (1165/10000)
[INFO][13:32:41]: [93m[1m[Server #32416] Global model accuracy: 11.65%
[0m
[INFO][13:32:41]: get_logged_items 被调用
[INFO][13:32:41]: 从updates获取参与客户端: [18, 73, 9, 98, 64, 84, 61, 58, 16, 33]
[INFO][13:32:41]: 客户端 18 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][13:32:41]: 客户端 73 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][13:32:41]: 客户端 9 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][13:32:41]: 客户端 98 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][13:32:41]: 客户端 64 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][13:32:41]: 客户端 84 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][13:32:41]: 客户端 61 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][13:32:41]: 客户端 58 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][13:32:41]: 客户端 16 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][13:32:41]: 客户端 33 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][13:32:41]: 陈旧度统计 - 参与客户端: [18, 73, 9, 98, 64, 84, 61, 58, 16, 33], 陈旧度: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
[INFO][13:32:41]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][13:32:41]: 最终logged_items: {'round': 1, 'accuracy': 0.1165, 'accuracy_std': 0, 'elapsed_time': 66.98999881744385, 'processing_time': 0.009952799999155104, 'comm_time': 0, 'round_time': 66.98999809700763, 'comm_overhead': 749.9883651733398, 'global_accuracy': 0.1165, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:32:41]: [Server #32416] All client reports have been processed.
[INFO][13:32:41]: [Server #32416] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][13:32:41]: [Server #32416] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][13:32:41]: [93m[1m
[Server #32416] Starting round 2/400.[0m
[INFO][13:32:41]: [Server #32416] Selected clients: [100, 66, 39, 34, 85, 17, 45, 6, 5, 93]
[INFO][13:32:41]: [Server #32416] Selecting client #100 for training.
[INFO][13:32:41]: [Server #32416] Sending the current model to client #100 (simulated).
[INFO][13:32:41]: [Server #32416] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][13:32:41]: [Server #32416] Selecting client #66 for training.
[INFO][13:32:41]: [Server #32416] Sending the current model to client #66 (simulated).
[INFO][13:32:41]: [Server #32416] Sending 18.75 MB of payload data to client #66 (simulated).
[INFO][13:32:41]: [Server #32416] Selecting client #39 for training.
[INFO][13:32:41]: [Server #32416] Sending the current model to client #39 (simulated).
[INFO][13:32:41]: [Server #32416] Sending 18.75 MB of payload data to client #39 (simulated).
[INFO][13:32:42]: [Server #32416] Selecting client #34 for training.
[INFO][13:32:42]: [Server #32416] Sending the current model to client #34 (simulated).
[INFO][13:32:42]: [Server #32416] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][13:32:42]: [Server #32416] Selecting client #85 for training.
[INFO][13:32:42]: [Server #32416] Sending the current model to client #85 (simulated).
[INFO][13:32:42]: [Server #32416] Sending 18.75 MB of payload data to client #85 (simulated).
[INFO][13:32:42]: [Server #32416] Selecting client #17 for training.
[INFO][13:32:42]: [Server #32416] Sending the current model to client #17 (simulated).
[INFO][13:32:42]: [Server #32416] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][13:32:42]: [Server #32416] Selecting client #45 for training.
[INFO][13:32:42]: [Server #32416] Sending the current model to client #45 (simulated).
[INFO][13:32:43]: [Server #32416] Sending 18.75 MB of payload data to client #45 (simulated).
[INFO][13:32:43]: [Server #32416] Selecting client #6 for training.
[INFO][13:32:43]: [Server #32416] Sending the current model to client #6 (simulated).
[INFO][13:32:43]: [Server #32416] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][13:32:43]: [Server #32416] Selecting client #5 for training.
[INFO][13:32:43]: [Server #32416] Sending the current model to client #5 (simulated).
[INFO][13:32:44]: [Server #32416] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][13:32:44]: [Server #32416] Selecting client #93 for training.
[INFO][13:32:44]: [Server #32416] Sending the current model to client #93 (simulated).
[INFO][13:32:44]: [Server #32416] Sending 18.75 MB of payload data to client #93 (simulated).
[INFO][13:35:52]: [Server #32416] Received 18.75 MB of payload data from client #100 (simulated).
[INFO][13:35:53]: [Server #32416] Received 18.75 MB of payload data from client #66 (simulated).
[INFO][13:35:54]: [Server #32416] Received 18.75 MB of payload data from client #39 (simulated).
[INFO][13:35:55]: [Server #32416] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][13:35:55]: [Server #32416] Received 18.75 MB of payload data from client #85 (simulated).
[INFO][13:35:56]: [Server #32416] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][13:35:56]: [Server #32416] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][13:35:56]: [Server #32416] Received 18.75 MB of payload data from client #45 (simulated).
[INFO][13:35:56]: [Server #32416] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][13:35:56]: [Server #32416] Received 18.75 MB of payload data from client #93 (simulated).
[INFO][13:35:56]: [Server #32416] Adding client #49 to the list of clients for aggregation.
[INFO][13:35:56]: [Server #32416] Adding client #13 to the list of clients for aggregation.
[INFO][13:35:56]: [Server #32416] Adding client #27 to the list of clients for aggregation.
[INFO][13:35:56]: [Server #32416] Adding client #63 to the list of clients for aggregation.
[INFO][13:35:56]: [Server #32416] Adding client #99 to the list of clients for aggregation.
[INFO][13:35:56]: [Server #32416] Adding client #4 to the list of clients for aggregation.
[INFO][13:35:56]: [Server #32416] Adding client #78 to the list of clients for aggregation.
[INFO][13:35:56]: [Server #32416] Adding client #1 to the list of clients for aggregation.
[INFO][13:35:56]: [Server #32416] Adding client #56 to the list of clients for aggregation.
[INFO][13:35:56]: [Server #32416] Adding client #50 to the list of clients for aggregation.
[INFO][13:35:56]: [Server #32416] Aggregating 10 clients in total.
[INFO][13:35:56]: [Server #32416] Updated weights have been received.
[INFO][13:35:56]: [Server #32416] Aggregating model weight deltas.
[INFO][13:35:56]: [Server #32416] Finished aggregating updated weights.
[INFO][13:35:56]: [Server #32416] Started model testing.
[INFO][13:36:09]: [Trainer.test] 测试完成 - 准确率: 15.51% (1551/10000)
[INFO][13:36:09]: [93m[1m[Server #32416] Global model accuracy: 15.51%
[0m
[INFO][13:36:09]: get_logged_items 被调用
[INFO][13:36:09]: 从updates获取参与客户端: [49, 13, 27, 63, 99, 4, 78, 1, 56, 50]
[INFO][13:36:09]: 客户端 49 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][13:36:09]: 客户端 13 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][13:36:09]: 客户端 27 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][13:36:09]: 客户端 63 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][13:36:09]: 客户端 99 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][13:36:09]: 客户端 4 陈旧度: 2 (当前轮次:2, 上次参与:0)
[INFO][13:36:09]: 客户端 78 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][13:36:09]: 客户端 1 陈旧度: 2 (当前轮次:2, 上次参与:0)
[INFO][13:36:09]: 客户端 56 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][13:36:09]: 客户端 50 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][13:36:09]: 陈旧度统计 - 参与客户端: [49, 13, 27, 63, 99, 4, 78, 1, 56, 50], 陈旧度: [1, 1, 1, 1, 1, 2, 1, 2, 1, 1]
[INFO][13:36:09]: 平均陈旧度: 1.2, 最大: 2, 最小: 1
[INFO][13:36:09]: 最终logged_items: {'round': 2, 'accuracy': 0.1551, 'accuracy_std': 0, 'elapsed_time': 80.94139003753662, 'processing_time': 0.006041000015102327, 'comm_time': 0, 'round_time': 80.94138937545358, 'comm_overhead': 1124.9825477600098, 'global_accuracy': 0.1551, 'avg_staleness': 1.2, 'max_staleness': 2, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:36:09]: [Server #32416] All client reports have been processed.
[INFO][13:36:09]: [Server #32416] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][13:36:09]: [Server #32416] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][13:36:09]: [93m[1m
[Server #32416] Starting round 3/400.[0m
[INFO][13:36:09]: [Server #32416] Selected clients: [77, 2, 55, 97, 31, 61, 4, 75, 32, 63]
[INFO][13:36:09]: [Server #32416] Selecting client #77 for training.
[INFO][13:36:09]: [Server #32416] Sending the current model to client #77 (simulated).
[INFO][13:36:09]: [Server #32416] Sending 18.75 MB of payload data to client #77 (simulated).
[INFO][13:36:09]: [Server #32416] Selecting client #2 for training.
[INFO][13:36:09]: [Server #32416] Sending the current model to client #2 (simulated).
[INFO][13:36:09]: [Server #32416] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][13:36:09]: [Server #32416] Selecting client #55 for training.
[INFO][13:36:09]: [Server #32416] Sending the current model to client #55 (simulated).
[INFO][13:36:09]: [Server #32416] Sending 18.75 MB of payload data to client #55 (simulated).
[INFO][13:36:09]: [Server #32416] Selecting client #97 for training.
[INFO][13:36:09]: [Server #32416] Sending the current model to client #97 (simulated).
[INFO][13:36:09]: [Server #32416] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][13:36:09]: [Server #32416] Selecting client #31 for training.
[INFO][13:36:09]: [Server #32416] Sending the current model to client #31 (simulated).
[INFO][13:36:09]: [Server #32416] Sending 18.75 MB of payload data to client #31 (simulated).
[INFO][13:36:09]: [Server #32416] Selecting client #61 for training.
[INFO][13:36:09]: [Server #32416] Sending the current model to client #61 (simulated).
[INFO][13:36:10]: [Server #32416] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][13:36:10]: [Server #32416] Selecting client #4 for training.
[INFO][13:36:10]: [Server #32416] Sending the current model to client #4 (simulated).
[INFO][13:36:10]: [Server #32416] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][13:36:10]: [Server #32416] Selecting client #75 for training.
[INFO][13:36:10]: [Server #32416] Sending the current model to client #75 (simulated).
[INFO][13:36:10]: [Server #32416] Sending 18.75 MB of payload data to client #75 (simulated).
[INFO][13:36:10]: [Server #32416] Selecting client #32 for training.
[INFO][13:36:10]: [Server #32416] Sending the current model to client #32 (simulated).
[INFO][13:36:11]: [Server #32416] Sending 18.75 MB of payload data to client #32 (simulated).
[INFO][13:36:11]: [Server #32416] Selecting client #63 for training.
[INFO][13:36:11]: [Server #32416] Sending the current model to client #63 (simulated).
[INFO][13:36:11]: [Server #32416] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][13:41:27]: [Server #32416] Received 18.75 MB of payload data from client #77 (simulated).
[INFO][13:41:27]: [Server #32416] Received 18.75 MB of payload data from client #55 (simulated).
[INFO][13:41:29]: [Server #32416] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][13:41:29]: [Server #32416] Received 18.75 MB of payload data from client #97 (simulated).
[INFO][13:41:38]: [Server #32416] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][13:41:38]: [Server #32416] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][13:41:38]: [Server #32416] Received 18.75 MB of payload data from client #31 (simulated).
[INFO][13:41:39]: [Server #32416] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][13:41:39]: [Server #32416] Received 18.75 MB of payload data from client #32 (simulated).
[INFO][13:41:39]: [Server #32416] Received 18.75 MB of payload data from client #75 (simulated).
[INFO][13:41:39]: [Server #32416] Adding client #100 to the list of clients for aggregation.
[INFO][13:41:39]: [Server #32416] Adding client #66 to the list of clients for aggregation.
[INFO][13:41:39]: [Server #32416] Adding client #85 to the list of clients for aggregation.
[INFO][13:41:39]: [Server #32416] Adding client #39 to the list of clients for aggregation.
[INFO][13:41:39]: [Server #32416] Adding client #34 to the list of clients for aggregation.
[INFO][13:41:39]: [Server #32416] Adding client #17 to the list of clients for aggregation.
[INFO][13:41:39]: [Server #32416] Adding client #45 to the list of clients for aggregation.
[INFO][13:41:39]: [Server #32416] Adding client #6 to the list of clients for aggregation.
[INFO][13:41:39]: [Server #32416] Adding client #93 to the list of clients for aggregation.
[INFO][13:41:39]: [Server #32416] Adding client #5 to the list of clients for aggregation.
[INFO][13:41:39]: [Server #32416] Aggregating 10 clients in total.
[INFO][13:41:39]: [Server #32416] Updated weights have been received.
[INFO][13:41:40]: [Server #32416] Aggregating model weight deltas.
[INFO][13:41:40]: [Server #32416] Finished aggregating updated weights.
[INFO][13:41:40]: [Server #32416] Started model testing.
[INFO][13:42:25]: [Trainer.test] 测试完成 - 准确率: 17.69% (1769/10000)
[INFO][13:42:25]: [93m[1m[Server #32416] Global model accuracy: 17.69%
[0m
[INFO][13:42:25]: get_logged_items 被调用
[INFO][13:42:25]: 从updates获取参与客户端: [100, 66, 85, 39, 34, 17, 45, 6, 93, 5]
[INFO][13:42:25]: 客户端 100 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][13:42:25]: 客户端 66 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][13:42:25]: 客户端 85 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][13:42:25]: 客户端 39 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][13:42:25]: 客户端 34 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][13:42:25]: 客户端 17 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][13:42:25]: 客户端 45 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][13:42:25]: 客户端 6 陈旧度: 3 (当前轮次:3, 上次参与:0)
[INFO][13:42:25]: 客户端 93 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][13:42:25]: 客户端 5 陈旧度: 3 (当前轮次:3, 上次参与:0)
[INFO][13:42:25]: 陈旧度统计 - 参与客户端: [100, 66, 85, 39, 34, 17, 45, 6, 93, 5], 陈旧度: [1, 1, 1, 1, 1, 1, 1, 3, 1, 3]
[INFO][13:42:25]: 平均陈旧度: 1.4, 最大: 3, 最小: 1
[INFO][13:42:25]: 最终logged_items: {'round': 3, 'accuracy': 0.1769, 'accuracy_std': 0, 'elapsed_time': 130.9679081439972, 'processing_time': 0.010664000030374154, 'comm_time': 0, 'round_time': 63.977909403445665, 'comm_overhead': 1499.9767303466797, 'global_accuracy': 0.1769, 'avg_staleness': 1.4, 'max_staleness': 3, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:42:25]: [Server #32416] All client reports have been processed.
[INFO][13:42:25]: [Server #32416] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][13:42:25]: [Server #32416] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][13:42:25]: [93m[1m
[Server #32416] Starting round 4/400.[0m
[INFO][13:42:25]: [Server #32416] Selected clients: [71, 80, 34, 49, 96, 33, 66, 42, 5, 59]
[INFO][13:42:25]: [Server #32416] Selecting client #71 for training.
[INFO][13:42:25]: [Server #32416] Sending the current model to client #71 (simulated).
[INFO][13:42:25]: [Server #32416] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][13:42:25]: [Server #32416] Selecting client #80 for training.
[INFO][13:42:25]: [Server #32416] Sending the current model to client #80 (simulated).
[INFO][13:42:25]: [Server #32416] Sending 18.75 MB of payload data to client #80 (simulated).
[INFO][13:42:25]: [Server #32416] Selecting client #34 for training.
[INFO][13:42:25]: [Server #32416] Sending the current model to client #34 (simulated).
[INFO][13:42:25]: [Server #32416] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][13:42:25]: [Server #32416] Selecting client #49 for training.
[INFO][13:42:25]: [Server #32416] Sending the current model to client #49 (simulated).
[INFO][13:42:25]: [Server #32416] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][13:42:25]: [Server #32416] Selecting client #96 for training.
[INFO][13:42:25]: [Server #32416] Sending the current model to client #96 (simulated).
[INFO][13:42:25]: [Server #32416] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][13:42:25]: [Server #32416] Selecting client #33 for training.
[INFO][13:42:25]: [Server #32416] Sending the current model to client #33 (simulated).
[INFO][13:42:26]: [Server #32416] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][13:42:26]: [Server #32416] Selecting client #66 for training.
[INFO][13:42:26]: [Server #32416] Sending the current model to client #66 (simulated).
[INFO][13:42:26]: [Server #32416] Sending 18.75 MB of payload data to client #66 (simulated).
[INFO][13:42:26]: [Server #32416] Selecting client #42 for training.
[INFO][13:42:26]: [Server #32416] Sending the current model to client #42 (simulated).
[INFO][13:42:26]: [Server #32416] Sending 18.75 MB of payload data to client #42 (simulated).
[INFO][13:42:26]: [Server #32416] Selecting client #5 for training.
[INFO][13:42:26]: [Server #32416] Sending the current model to client #5 (simulated).
[INFO][13:42:26]: [Server #32416] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][13:42:26]: [Server #32416] Selecting client #59 for training.
[INFO][13:42:26]: [Server #32416] Sending the current model to client #59 (simulated).
[INFO][13:42:27]: [Server #32416] Sending 18.75 MB of payload data to client #59 (simulated).
[INFO][13:46:52]: [Server #32416] Received 18.75 MB of payload data from client #42 (simulated).
[INFO][13:46:52]: [Server #32416] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][13:46:53]: [Server #32416] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][13:46:53]: [Server #32416] Received 18.75 MB of payload data from client #59 (simulated).
[INFO][13:46:53]: [Server #32416] Received 18.75 MB of payload data from client #80 (simulated).
[INFO][13:46:54]: [Server #32416] Received 18.75 MB of payload data from client #71 (simulated).
[INFO][13:46:54]: [Server #32416] Received 18.75 MB of payload data from client #66 (simulated).
[INFO][13:46:54]: [Server #32416] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][13:46:54]: [Server #32416] Received 18.75 MB of payload data from client #96 (simulated).
[INFO][13:46:54]: [Server #32416] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][13:46:54]: [Server #32416] Adding client #77 to the list of clients for aggregation.
[INFO][13:46:54]: [Server #32416] Adding client #2 to the list of clients for aggregation.
[INFO][13:46:54]: [Server #32416] Adding client #55 to the list of clients for aggregation.
[INFO][13:46:54]: [Server #32416] Adding client #31 to the list of clients for aggregation.
[INFO][13:46:54]: [Server #32416] Adding client #97 to the list of clients for aggregation.
[INFO][13:46:54]: [Server #32416] Adding client #61 to the list of clients for aggregation.
[INFO][13:46:54]: [Server #32416] Adding client #4 to the list of clients for aggregation.
[INFO][13:46:54]: [Server #32416] Adding client #32 to the list of clients for aggregation.
[INFO][13:46:54]: [Server #32416] Adding client #75 to the list of clients for aggregation.
[INFO][13:46:54]: [Server #32416] Adding client #63 to the list of clients for aggregation.
[INFO][13:46:54]: [Server #32416] Aggregating 10 clients in total.
[INFO][13:46:54]: [Server #32416] Updated weights have been received.
[INFO][13:46:54]: [Server #32416] Aggregating model weight deltas.
[INFO][13:46:54]: [Server #32416] Finished aggregating updated weights.
[INFO][13:46:54]: [Server #32416] Started model testing.
[INFO][13:47:07]: [Trainer.test] 测试完成 - 准确率: 19.59% (1959/10000)
[INFO][13:47:07]: [93m[1m[Server #32416] Global model accuracy: 19.59%
[0m
[INFO][13:47:07]: get_logged_items 被调用
[INFO][13:47:07]: 从updates获取参与客户端: [77, 2, 55, 31, 97, 61, 4, 32, 75, 63]
[INFO][13:47:07]: 客户端 77 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][13:47:07]: 客户端 2 陈旧度: 4 (当前轮次:4, 上次参与:0)
[INFO][13:47:07]: 客户端 55 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][13:47:07]: 客户端 31 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][13:47:07]: 客户端 97 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][13:47:07]: 客户端 61 陈旧度: 3 (当前轮次:4, 上次参与:1)
[INFO][13:47:07]: 客户端 4 陈旧度: 2 (当前轮次:4, 上次参与:2)
[INFO][13:47:07]: 客户端 32 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][13:47:07]: 客户端 75 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][13:47:07]: 客户端 63 陈旧度: 2 (当前轮次:4, 上次参与:2)
[INFO][13:47:07]: 陈旧度统计 - 参与客户端: [77, 2, 55, 31, 97, 61, 4, 32, 75, 63], 陈旧度: [1, 4, 1, 1, 1, 3, 2, 1, 1, 2]
[INFO][13:47:07]: 平均陈旧度: 1.7, 最大: 4, 最小: 1
[INFO][13:47:07]: 最终logged_items: {'round': 4, 'accuracy': 0.1959, 'accuracy_std': 0, 'elapsed_time': 152.7280979156494, 'processing_time': 0.0012445999891497195, 'comm_time': 0, 'round_time': 71.78670776772196, 'comm_overhead': 1874.9709129333496, 'global_accuracy': 0.1959, 'avg_staleness': 1.7, 'max_staleness': 4, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:47:07]: [Server #32416] All client reports have been processed.
[INFO][13:47:07]: [Server #32416] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][13:47:07]: [Server #32416] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][13:47:07]: [93m[1m
[Server #32416] Starting round 5/400.[0m
[INFO][13:47:07]: [Server #32416] Selected clients: [81, 92, 14, 25, 90, 41, 17, 47, 73, 61]
[INFO][13:47:07]: [Server #32416] Selecting client #81 for training.
[INFO][13:47:07]: [Server #32416] Sending the current model to client #81 (simulated).
[INFO][13:47:07]: [Server #32416] Sending 18.75 MB of payload data to client #81 (simulated).
[INFO][13:47:07]: [Server #32416] Selecting client #92 for training.
[INFO][13:47:07]: [Server #32416] Sending the current model to client #92 (simulated).
[INFO][13:47:07]: [Server #32416] Sending 18.75 MB of payload data to client #92 (simulated).
[INFO][13:47:07]: [Server #32416] Selecting client #14 for training.
[INFO][13:47:07]: [Server #32416] Sending the current model to client #14 (simulated).
[INFO][13:47:07]: [Server #32416] Sending 18.75 MB of payload data to client #14 (simulated).
[INFO][13:47:07]: [Server #32416] Selecting client #25 for training.
[INFO][13:47:07]: [Server #32416] Sending the current model to client #25 (simulated).
[INFO][13:47:07]: [Server #32416] Sending 18.75 MB of payload data to client #25 (simulated).
[INFO][13:47:07]: [Server #32416] Selecting client #90 for training.
[INFO][13:47:07]: [Server #32416] Sending the current model to client #90 (simulated).
[INFO][13:47:07]: [Server #32416] Sending 18.75 MB of payload data to client #90 (simulated).
[INFO][13:47:07]: [Server #32416] Selecting client #41 for training.
[INFO][13:47:07]: [Server #32416] Sending the current model to client #41 (simulated).
[INFO][13:47:07]: [Server #32416] Sending 18.75 MB of payload data to client #41 (simulated).
[INFO][13:47:07]: [Server #32416] Selecting client #17 for training.
[INFO][13:47:07]: [Server #32416] Sending the current model to client #17 (simulated).
[INFO][13:47:08]: [Server #32416] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][13:47:08]: [Server #32416] Selecting client #47 for training.
[INFO][13:47:08]: [Server #32416] Sending the current model to client #47 (simulated).
[INFO][13:47:08]: [Server #32416] Sending 18.75 MB of payload data to client #47 (simulated).
[INFO][13:47:08]: [Server #32416] Selecting client #73 for training.
[INFO][13:47:08]: [Server #32416] Sending the current model to client #73 (simulated).
[INFO][13:47:08]: [Server #32416] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][13:47:08]: [Server #32416] Selecting client #61 for training.
[INFO][13:47:08]: [Server #32416] Sending the current model to client #61 (simulated).
[INFO][13:47:09]: [Server #32416] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][13:50:04]: [Server #32416] Received 18.75 MB of payload data from client #81 (simulated).
[INFO][13:50:06]: [Server #32416] Received 18.75 MB of payload data from client #92 (simulated).
[INFO][13:50:07]: [Server #32416] Received 18.75 MB of payload data from client #41 (simulated).
[INFO][13:50:08]: [Server #32416] Received 18.75 MB of payload data from client #14 (simulated).
[INFO][13:50:08]: [Server #32416] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][13:50:08]: [Server #32416] Received 18.75 MB of payload data from client #90 (simulated).
[INFO][13:50:08]: [Server #32416] Received 18.75 MB of payload data from client #25 (simulated).
[INFO][13:50:08]: [Server #32416] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][13:50:08]: [Server #32416] Received 18.75 MB of payload data from client #47 (simulated).
[INFO][13:50:09]: [Server #32416] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][13:50:09]: [Server #32416] Adding client #81 to the list of clients for aggregation.
[INFO][13:50:09]: [Server #32416] Adding client #92 to the list of clients for aggregation.
[INFO][13:50:09]: [Server #32416] Adding client #90 to the list of clients for aggregation.
[INFO][13:50:09]: [Server #32416] Adding client #41 to the list of clients for aggregation.
[INFO][13:50:09]: [Server #32416] Adding client #14 to the list of clients for aggregation.
[INFO][13:50:09]: [Server #32416] Adding client #25 to the list of clients for aggregation.
[INFO][13:50:09]: [Server #32416] Adding client #61 to the list of clients for aggregation.
[INFO][13:50:09]: [Server #32416] Adding client #17 to the list of clients for aggregation.
[INFO][13:50:09]: [Server #32416] Adding client #47 to the list of clients for aggregation.
[INFO][13:50:09]: [Server #32416] Adding client #73 to the list of clients for aggregation.
[INFO][13:50:09]: [Server #32416] Aggregating 10 clients in total.
[INFO][13:50:09]: [Server #32416] Updated weights have been received.
[INFO][13:50:09]: [Server #32416] Aggregating model weight deltas.
[INFO][13:50:09]: [Server #32416] Finished aggregating updated weights.
[INFO][13:50:09]: [Server #32416] Started model testing.
[INFO][13:50:22]: [Trainer.test] 测试完成 - 准确率: 22.00% (2200/10000)
[INFO][13:50:22]: [93m[1m[Server #32416] Global model accuracy: 22.00%
[0m
[INFO][13:50:22]: get_logged_items 被调用
[INFO][13:50:22]: 从updates获取参与客户端: [81, 92, 90, 41, 14, 25, 61, 17, 47, 73]
[INFO][13:50:22]: 客户端 81 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][13:50:22]: 客户端 92 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][13:50:22]: 客户端 90 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][13:50:22]: 客户端 41 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][13:50:22]: 客户端 14 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][13:50:22]: 客户端 25 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][13:50:22]: 客户端 61 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][13:50:22]: 客户端 17 陈旧度: 2 (当前轮次:5, 上次参与:3)
[INFO][13:50:22]: 客户端 47 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][13:50:22]: 客户端 73 陈旧度: 4 (当前轮次:5, 上次参与:1)
[INFO][13:50:22]: 陈旧度统计 - 参与客户端: [81, 92, 90, 41, 14, 25, 61, 17, 47, 73], 陈旧度: [1, 1, 1, 1, 1, 1, 1, 2, 1, 4]
[INFO][13:50:22]: 平均陈旧度: 1.4, 最大: 4, 最小: 1
[INFO][13:50:22]: 最终logged_items: {'round': 5, 'accuracy': 0.22, 'accuracy_std': 0, 'elapsed_time': 209.99596095085144, 'processing_time': 0.00578870001481846, 'comm_time': 0, 'round_time': 57.26786299352534, 'comm_overhead': 2249.9650955200195, 'global_accuracy': 0.22, 'avg_staleness': 1.4, 'max_staleness': 4, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:50:22]: [Server #32416] All client reports have been processed.
[INFO][13:50:22]: [Server #32416] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][13:50:22]: [Server #32416] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][13:50:22]: [93m[1m
[Server #32416] Starting round 6/400.[0m
[INFO][13:50:22]: [Server #32416] Selected clients: [73, 95, 26, 43, 40, 85, 72, 56, 6, 69]
[INFO][13:50:22]: [Server #32416] Selecting client #73 for training.
[INFO][13:50:22]: [Server #32416] Sending the current model to client #73 (simulated).
[INFO][13:50:22]: [Server #32416] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][13:50:22]: [Server #32416] Selecting client #95 for training.
[INFO][13:50:22]: [Server #32416] Sending the current model to client #95 (simulated).
[INFO][13:50:22]: [Server #32416] Sending 18.75 MB of payload data to client #95 (simulated).
[INFO][13:50:22]: [Server #32416] Selecting client #26 for training.
[INFO][13:50:22]: [Server #32416] Sending the current model to client #26 (simulated).
[INFO][13:50:22]: [Server #32416] Sending 18.75 MB of payload data to client #26 (simulated).
[INFO][13:50:22]: [Server #32416] Selecting client #43 for training.
[INFO][13:50:22]: [Server #32416] Sending the current model to client #43 (simulated).
[INFO][13:50:22]: [Server #32416] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][13:50:22]: [Server #32416] Selecting client #40 for training.
[INFO][13:50:22]: [Server #32416] Sending the current model to client #40 (simulated).
[INFO][13:50:22]: [Server #32416] Sending 18.75 MB of payload data to client #40 (simulated).
[INFO][13:50:22]: [Server #32416] Selecting client #85 for training.
[INFO][13:50:22]: [Server #32416] Sending the current model to client #85 (simulated).
[INFO][13:50:22]: [Server #32416] Sending 18.75 MB of payload data to client #85 (simulated).
[INFO][13:50:22]: [Server #32416] Selecting client #72 for training.
[INFO][13:50:22]: [Server #32416] Sending the current model to client #72 (simulated).
[INFO][13:50:23]: [Server #32416] Sending 18.75 MB of payload data to client #72 (simulated).
[INFO][13:50:23]: [Server #32416] Selecting client #56 for training.
[INFO][13:50:23]: [Server #32416] Sending the current model to client #56 (simulated).
[INFO][13:50:23]: [Server #32416] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][13:50:23]: [Server #32416] Selecting client #6 for training.
[INFO][13:50:23]: [Server #32416] Sending the current model to client #6 (simulated).
[INFO][13:50:24]: [Server #32416] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][13:50:24]: [Server #32416] Selecting client #69 for training.
[INFO][13:50:24]: [Server #32416] Sending the current model to client #69 (simulated).
[INFO][13:50:24]: [Server #32416] Sending 18.75 MB of payload data to client #69 (simulated).
[INFO][13:53:21]: [Server #32416] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][13:53:23]: [Server #32416] Received 18.75 MB of payload data from client #95 (simulated).
[INFO][13:53:24]: [Server #32416] Received 18.75 MB of payload data from client #26 (simulated).
[INFO][13:53:28]: [Server #32416] Received 18.75 MB of payload data from client #40 (simulated).
[INFO][13:53:29]: [Server #32416] Received 18.75 MB of payload data from client #72 (simulated).
[INFO][13:53:29]: [Server #32416] Received 18.75 MB of payload data from client #43 (simulated).
[INFO][13:53:30]: [Server #32416] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][13:53:30]: [Server #32416] Received 18.75 MB of payload data from client #69 (simulated).
[INFO][13:53:30]: [Server #32416] Received 18.75 MB of payload data from client #85 (simulated).
[INFO][13:53:30]: [Server #32416] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][13:53:30]: [Server #32416] Adding client #33 to the list of clients for aggregation.
[INFO][13:53:30]: [Server #32416] Adding client #49 to the list of clients for aggregation.
[INFO][13:53:30]: [Server #32416] Adding client #59 to the list of clients for aggregation.
[INFO][13:53:30]: [Server #32416] Adding client #80 to the list of clients for aggregation.
[INFO][13:53:30]: [Server #32416] Adding client #42 to the list of clients for aggregation.
[INFO][13:53:30]: [Server #32416] Adding client #95 to the list of clients for aggregation.
[INFO][13:53:30]: [Server #32416] Adding client #73 to the list of clients for aggregation.
[INFO][13:53:30]: [Server #32416] Adding client #96 to the list of clients for aggregation.
[INFO][13:53:30]: [Server #32416] Adding client #26 to the list of clients for aggregation.
[INFO][13:53:30]: [Server #32416] Adding client #72 to the list of clients for aggregation.
[INFO][13:53:30]: [Server #32416] Aggregating 10 clients in total.
[INFO][13:53:30]: [Server #32416] Updated weights have been received.
[INFO][13:53:30]: [Server #32416] Aggregating model weight deltas.
[INFO][13:53:30]: [Server #32416] Finished aggregating updated weights.
[INFO][13:53:30]: [Server #32416] Started model testing.
[INFO][13:53:43]: [Trainer.test] 测试完成 - 准确率: 21.66% (2166/10000)
[INFO][13:53:43]: [93m[1m[Server #32416] Global model accuracy: 21.66%
[0m
[INFO][13:53:43]: get_logged_items 被调用
[INFO][13:53:43]: 从updates获取参与客户端: [33, 49, 59, 80, 42, 95, 73, 96, 26, 72]
[INFO][13:53:43]: 客户端 33 陈旧度: 5 (当前轮次:6, 上次参与:1)
[INFO][13:53:43]: 客户端 49 陈旧度: 4 (当前轮次:6, 上次参与:2)
[INFO][13:53:43]: 客户端 59 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][13:53:43]: 客户端 80 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][13:53:43]: 客户端 42 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][13:53:43]: 客户端 95 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][13:53:43]: 客户端 73 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][13:53:43]: 客户端 96 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][13:53:43]: 客户端 26 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][13:53:43]: 客户端 72 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][13:53:43]: 陈旧度统计 - 参与客户端: [33, 49, 59, 80, 42, 95, 73, 96, 26, 72], 陈旧度: [5, 4, 1, 1, 1, 1, 1, 1, 1, 1]
[INFO][13:53:43]: 平均陈旧度: 1.7, 最大: 5, 最小: 1
[INFO][13:53:43]: 最终logged_items: {'round': 6, 'accuracy': 0.2166, 'accuracy_std': 0, 'elapsed_time': 268.4668848514557, 'processing_time': 0.00492790000862442, 'comm_time': 0, 'round_time': 137.12438138120342, 'comm_overhead': 2624.9592781066895, 'global_accuracy': 0.2166, 'avg_staleness': 1.7, 'max_staleness': 5, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:53:43]: [Server #32416] All client reports have been processed.
[INFO][13:53:43]: [Server #32416] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_6.pth.
[INFO][13:53:43]: [Server #32416] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_6.pth.
[INFO][13:53:43]: [93m[1m
[Server #32416] Starting round 7/400.[0m
[INFO][13:53:43]: [Server #32416] Selected clients: [35, 58, 60, 96, 25, 52, 80, 100, 97, 53]
[INFO][13:53:43]: [Server #32416] Selecting client #35 for training.
[INFO][13:53:43]: [Server #32416] Sending the current model to client #35 (simulated).
[INFO][13:53:43]: [Server #32416] Sending 18.75 MB of payload data to client #35 (simulated).
[INFO][13:53:43]: [Server #32416] Selecting client #58 for training.
[INFO][13:53:43]: [Server #32416] Sending the current model to client #58 (simulated).
[INFO][13:53:43]: [Server #32416] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][13:53:43]: [Server #32416] Selecting client #60 for training.
[INFO][13:53:43]: [Server #32416] Sending the current model to client #60 (simulated).
[INFO][13:53:43]: [Server #32416] Sending 18.75 MB of payload data to client #60 (simulated).
[INFO][13:53:43]: [Server #32416] Selecting client #96 for training.
[INFO][13:53:43]: [Server #32416] Sending the current model to client #96 (simulated).
[INFO][13:53:43]: [Server #32416] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][13:53:43]: [Server #32416] Selecting client #25 for training.
[INFO][13:53:43]: [Server #32416] Sending the current model to client #25 (simulated).
[INFO][13:53:43]: [Server #32416] Sending 18.75 MB of payload data to client #25 (simulated).
[INFO][13:53:43]: [Server #32416] Selecting client #52 for training.
[INFO][13:53:43]: [Server #32416] Sending the current model to client #52 (simulated).
[INFO][13:53:44]: [Server #32416] Sending 18.75 MB of payload data to client #52 (simulated).
[INFO][13:53:44]: [Server #32416] Selecting client #80 for training.
[INFO][13:53:44]: [Server #32416] Sending the current model to client #80 (simulated).
[INFO][13:53:44]: [Server #32416] Sending 18.75 MB of payload data to client #80 (simulated).
[INFO][13:53:44]: [Server #32416] Selecting client #100 for training.
[INFO][13:53:44]: [Server #32416] Sending the current model to client #100 (simulated).
[INFO][13:53:44]: [Server #32416] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][13:53:44]: [Server #32416] Selecting client #97 for training.
[INFO][13:53:44]: [Server #32416] Sending the current model to client #97 (simulated).
[INFO][13:53:45]: [Server #32416] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][13:53:45]: [Server #32416] Selecting client #53 for training.
[INFO][13:53:45]: [Server #32416] Sending the current model to client #53 (simulated).
[INFO][13:53:45]: [Server #32416] Sending 18.75 MB of payload data to client #53 (simulated).
[INFO][13:54:56]: [Server #32416] An existing client just disconnected.
[WARNING][13:54:56]: [Server #32416] Client process #39476 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][13:54:56]: [93m[1m[Server #32416] Closing the server due to a failed client.[0m
[INFO][13:54:56]: [Server #32416] Training concluded.
[INFO][13:54:57]: [Server #32416] Model saved to ./models/refedscafl/cifar10_alpha01/resnet_9.pth.
[INFO][13:54:57]: [Server #32416] Closing the server.
[INFO][13:54:57]: Closing the connection to client #6488.
[INFO][13:54:57]: Closing the connection to client #28028.
[INFO][13:54:57]: Closing the connection to client #21884.
[INFO][13:54:57]: Closing the connection to client #19588.
[INFO][13:54:57]: Closing the connection to client #25540.
[INFO][13:54:57]: Closing the connection to client #28684.
[INFO][13:54:57]: Closing the connection to client #41232.
[INFO][13:54:57]: Closing the connection to client #13648.
[INFO][13:54:57]: Closing the connection to client #30028.
