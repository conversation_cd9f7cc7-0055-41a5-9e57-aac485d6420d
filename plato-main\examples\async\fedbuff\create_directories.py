#!/usr/bin/env python3
"""
创建FedBuff实验所需的目录结构
"""

import os

def create_directories():
    """创建所需的目录结构"""
    directories = [
        "results/mnist_original_fedbuff/01",
        "results/mnist_standard_fedbuff/01", 
        "results/mnist_network_test_fedbuff/01",
        "models/mnist_original_fedbuff/01",
        "models/mnist_standard_fedbuff/01",
        "models/mnist_network_test_fedbuff/01"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        except Exception as e:
            print(f"❌ 创建目录失败: {directory}, 错误: {e}")
    
    print(f"\n📁 目录结构创建完成！")

if __name__ == "__main__":
    create_directories()
