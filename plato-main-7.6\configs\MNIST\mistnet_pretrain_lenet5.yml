clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8000

data: !include mnist_iid.yml

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 100

    # The target accuracy
    target_accuracy: 0.90

    # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    model:
        num_classes: 10

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0
