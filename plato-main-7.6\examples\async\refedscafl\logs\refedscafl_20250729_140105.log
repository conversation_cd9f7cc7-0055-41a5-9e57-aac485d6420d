[INFO][14:01:05]: 日志系统已初始化
[INFO][14:01:05]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_140105.log
[INFO][14:01:05]: 日志级别: INFO
[WARNING][14:01:05]: 无法获取系统信息: No module named 'psutil'
[INFO][14:01:05]: 🚀 ReFedScaFL 训练开始
[INFO][14:01:05]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][14:01:05]: 开始时间: 2025-07-29 14:01:05
[INFO][14:01:05]: [Client None] 基础初始化完成
[INFO][14:01:05]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][14:01:05]: 创建并缓存共享模型
[INFO][14:01:05]: [93m[1m[29312] Logging runtime results to: ./results/refedscafl/cifar10_alpha01/29312.csv.[0m
[INFO][14:01:05]: [Server #29312] Started training on 100 clients with 20 per round.
[INFO][14:01:05]: 服务器参数配置完成：
[INFO][14:01:05]: - 客户端数量: total=100, per_round=20
[INFO][14:01:05]: - 权重参数: success=0.8, distill=0.2
[INFO][14:01:05]: - SCAFL参数: V=1.0, tau_max=5
[INFO][14:01:05]: 从共享资源模型提取并缓存全局权重
[INFO][14:01:05]: [Server #29312] Configuring the server...
[INFO][14:01:05]: Training: 400 rounds or accuracy above 100.0%

[INFO][14:01:05]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][14:01:05]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][14:01:05]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][14:01:05]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][14:01:05]: Algorithm: fedavg
[INFO][14:01:05]: Data source: CIFAR10
[INFO][14:01:06]: Starting client #1's process.
[INFO][14:01:06]: Starting client #2's process.
[INFO][14:01:06]: Starting client #3's process.
[INFO][14:01:06]: Starting client #4's process.
[INFO][14:01:06]: Starting client #5's process.
[INFO][14:01:06]: Starting client #6's process.
[INFO][14:01:06]: Starting client #7's process.
[INFO][14:01:06]: Starting client #8's process.
[INFO][14:01:06]: Starting client #9's process.
[INFO][14:01:06]: Starting client #10's process.
[INFO][14:01:06]: Setting the random seed for selecting clients: 1
[INFO][14:01:06]: Starting a server at address 127.0.0.1 and port 8095.
[INFO][14:01:24]: [Server #29312] A new client just connected.
[INFO][14:01:24]: [Server #29312] New client with id #6 arrived.
[INFO][14:01:24]: [Server #29312] Client process #26668 registered.
[INFO][14:01:24]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][14:01:25]: [Server #29312] A new client just connected.
[INFO][14:01:25]: [Server #29312] New client with id #3 arrived.
[INFO][14:01:25]: [Server #29312] Client process #35524 registered.
[INFO][14:01:25]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][14:01:25]: [Server #29312] A new client just connected.
[INFO][14:01:25]: [Server #29312] New client with id #1 arrived.
[INFO][14:01:25]: [Server #29312] Client process #41112 registered.
[INFO][14:01:25]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][14:01:25]: [Server #29312] A new client just connected.
[INFO][14:01:25]: [Server #29312] A new client just connected.
[INFO][14:01:25]: [Server #29312] New client with id #7 arrived.
[INFO][14:01:25]: [Server #29312] Client process #29548 registered.
[INFO][14:01:25]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][14:01:25]: [Server #29312] New client with id #8 arrived.
[INFO][14:01:25]: [Server #29312] Client process #8424 registered.
[INFO][14:01:25]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][14:01:25]: [Server #29312] A new client just connected.
[INFO][14:01:25]: [Server #29312] New client with id #4 arrived.
[INFO][14:01:25]: [Server #29312] Client process #27856 registered.
[INFO][14:01:25]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][14:01:25]: [Server #29312] A new client just connected.
[INFO][14:01:25]: [Server #29312] A new client just connected.
[INFO][14:01:25]: [Server #29312] A new client just connected.
[INFO][14:01:25]: [Server #29312] New client with id #10 arrived.
[INFO][14:01:25]: [Server #29312] Client process #24312 registered.
[INFO][14:01:25]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][14:01:25]: [Server #29312] New client with id #2 arrived.
[INFO][14:01:25]: [Server #29312] Client process #20200 registered.
[INFO][14:01:25]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][14:01:25]: [Server #29312] New client with id #9 arrived.
[INFO][14:01:25]: [Server #29312] Client process #14484 registered.
[INFO][14:01:25]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][14:01:25]: [Server #29312] A new client just connected.
[INFO][14:01:25]: [Server #29312] New client with id #5 arrived.
[INFO][14:01:25]: [Server #29312] Client process #19908 registered.
[INFO][14:01:25]: [Server #29312] Starting training.
[INFO][14:01:25]: [93m[1m
[Server #29312] Starting round 1/400.[0m
[INFO][14:01:26]: [Server #29312] Selected clients: [18, 73, 98, 9, 33, 16, 64, 58, 61, 84, 49, 27, 13, 63, 4, 50, 56, 78, 99, 1]
[INFO][14:01:26]: [Server #29312] Selecting client #18 for training.
[INFO][14:01:26]: [Server #29312] Sending the current model to client #18 (simulated).
[INFO][14:01:26]: [Server #29312] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][14:01:26]: [Server #29312] Selecting client #73 for training.
[INFO][14:01:26]: [Server #29312] Sending the current model to client #73 (simulated).
[INFO][14:01:26]: [Server #29312] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][14:01:26]: [Server #29312] Selecting client #98 for training.
[INFO][14:01:26]: [Server #29312] Sending the current model to client #98 (simulated).
[INFO][14:01:26]: [Server #29312] Sending 18.75 MB of payload data to client #98 (simulated).
[INFO][14:01:26]: [Server #29312] Selecting client #9 for training.
[INFO][14:01:26]: [Server #29312] Sending the current model to client #9 (simulated).
[INFO][14:01:26]: [Server #29312] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][14:01:26]: [Server #29312] Selecting client #33 for training.
[INFO][14:01:26]: [Server #29312] Sending the current model to client #33 (simulated).
[INFO][14:01:26]: [Server #29312] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][14:01:26]: [Server #29312] Selecting client #16 for training.
[INFO][14:01:26]: [Server #29312] Sending the current model to client #16 (simulated).
[INFO][14:01:26]: [Server #29312] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][14:01:26]: [Server #29312] Selecting client #64 for training.
[INFO][14:01:26]: [Server #29312] Sending the current model to client #64 (simulated).
[INFO][14:01:26]: [Server #29312] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][14:01:26]: [Server #29312] Selecting client #58 for training.
[INFO][14:01:26]: [Server #29312] Sending the current model to client #58 (simulated).
[INFO][14:01:26]: [Server #29312] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][14:01:26]: [Server #29312] Selecting client #61 for training.
[INFO][14:01:26]: [Server #29312] Sending the current model to client #61 (simulated).
[INFO][14:01:26]: [Server #29312] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][14:01:26]: [Server #29312] Selecting client #84 for training.
[INFO][14:01:26]: [Server #29312] Sending the current model to client #84 (simulated).
[INFO][14:01:26]: [Server #29312] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][14:01:26]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][14:06:25]: [Server #29312] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][14:06:26]: [Server #29312] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][14:06:28]: [Server #29312] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][14:06:30]: [Server #29312] Received 18.75 MB of payload data from client #98 (simulated).
[INFO][14:06:30]: [Server #29312] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][14:06:30]: [Server #29312] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][14:06:30]: [Server #29312] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][14:06:32]: [Server #29312] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][14:06:32]: [Server #29312] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][14:06:32]: [Server #29312] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][14:06:32]: [Server #29312] Selecting client #49 for training.
[INFO][14:06:32]: [Server #29312] Sending the current model to client #49 (simulated).
[INFO][14:06:32]: [Server #29312] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][14:06:32]: [Server #29312] Selecting client #27 for training.
[INFO][14:06:32]: [Server #29312] Sending the current model to client #27 (simulated).
[INFO][14:06:32]: [Server #29312] Sending 18.75 MB of payload data to client #27 (simulated).
[INFO][14:06:32]: [Server #29312] Selecting client #13 for training.
[INFO][14:06:32]: [Server #29312] Sending the current model to client #13 (simulated).
[INFO][14:06:32]: [Server #29312] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][14:06:32]: [Server #29312] Selecting client #63 for training.
[INFO][14:06:32]: [Server #29312] Sending the current model to client #63 (simulated).
[INFO][14:06:33]: [Server #29312] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][14:06:33]: [Server #29312] Selecting client #4 for training.
[INFO][14:06:33]: [Server #29312] Sending the current model to client #4 (simulated).
[INFO][14:06:33]: [Server #29312] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][14:06:33]: [Server #29312] Selecting client #50 for training.
[INFO][14:06:33]: [Server #29312] Sending the current model to client #50 (simulated).
[INFO][14:06:33]: [Server #29312] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][14:06:33]: [Server #29312] Selecting client #56 for training.
[INFO][14:06:33]: [Server #29312] Sending the current model to client #56 (simulated).
[INFO][14:06:33]: [Server #29312] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][14:06:33]: [Server #29312] Selecting client #78 for training.
[INFO][14:06:33]: [Server #29312] Sending the current model to client #78 (simulated).
[INFO][14:06:33]: [Server #29312] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][14:06:33]: [Server #29312] Selecting client #99 for training.
[INFO][14:06:33]: [Server #29312] Sending the current model to client #99 (simulated).
[INFO][14:06:34]: [Server #29312] Sending 18.75 MB of payload data to client #99 (simulated).
[INFO][14:06:34]: [Server #29312] Selecting client #1 for training.
[INFO][14:06:34]: [Server #29312] Sending the current model to client #1 (simulated).
[INFO][14:06:34]: [Server #29312] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][14:12:20]: [Server #29312] Received 18.75 MB of payload data from client #27 (simulated).
[INFO][14:12:25]: [Server #29312] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][14:12:27]: [Server #29312] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][14:12:28]: [Server #29312] Received 18.75 MB of payload data from client #78 (simulated).
[INFO][14:12:28]: [Server #29312] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][14:12:29]: [Server #29312] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][14:12:31]: [Server #29312] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][14:12:32]: [Server #29312] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][14:12:32]: [Server #29312] Received 18.75 MB of payload data from client #99 (simulated).
[INFO][14:12:33]: [Server #29312] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][14:12:33]: [Server #29312] Adding client #18 to the list of clients for aggregation.
[INFO][14:12:33]: [Server #29312] Adding client #73 to the list of clients for aggregation.
[INFO][14:12:33]: [Server #29312] Adding client #98 to the list of clients for aggregation.
[INFO][14:12:33]: [Server #29312] Adding client #16 to the list of clients for aggregation.
[INFO][14:12:33]: [Server #29312] Adding client #33 to the list of clients for aggregation.
[INFO][14:12:33]: [Server #29312] Adding client #9 to the list of clients for aggregation.
[INFO][14:12:33]: [Server #29312] Adding client #64 to the list of clients for aggregation.
[INFO][14:12:33]: [Server #29312] Adding client #58 to the list of clients for aggregation.
[INFO][14:12:33]: [Server #29312] Adding client #61 to the list of clients for aggregation.
[INFO][14:12:33]: [Server #29312] Adding client #84 to the list of clients for aggregation.
[INFO][14:12:33]: [Server #29312] Aggregating 10 clients in total.
[INFO][14:12:33]: 🎯 ReFedScaFL _process_reports 被调用，开始自定义聚合逻辑
[INFO][14:12:33]: 🎯 开始ReFedScaFL聚合逻辑
[INFO][14:12:33]: 🎯 客户端 18 通信时间(H_comm)：1.26 <= 2.00，可以顺利上传
[INFO][14:12:33]: 🎯 客户端 73 通信时间(H_comm)：0.53 <= 2.00，可以顺利上传
[INFO][14:12:33]: 🎯 客户端 98 通信时间(H_comm)：6.74 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:12:33]: 🎯 客户端 16 通信时间(H_comm)：3.17 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:12:33]: 🎯 客户端 33 通信时间(H_comm)：1.10 <= 2.00，可以顺利上传
[INFO][14:12:33]: 🎯 客户端 9 通信时间(H_comm)：0.93 <= 2.00，可以顺利上传
[INFO][14:12:33]: 🎯 客户端 64 通信时间(H_comm)：0.75 <= 2.00，可以顺利上传
[INFO][14:12:33]: 🎯 客户端 58 通信时间(H_comm)：3.79 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:12:33]: 🎯 客户端 61 通信时间(H_comm)：2.83 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:12:33]: 🎯 客户端 84 通信时间(H_comm)：3.30 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:12:33]: 🎯 客户端处理结果 - 顺利上传: 5个, 无法顺利上传: 5个
[INFO][14:12:33]: 🎯 对 5 个顺利上传的客户端进行贪心选择
[INFO][14:12:33]: 🎯 开始贪心选择聚合子集，候选更新数量: 5
[INFO][14:12:33]: 🎯 候选客户端按延迟估计值排序: [(64, np.float64(1.7414876502280898)), (33, np.float64(2.375458338556362)), (18, np.float64(2.5078118278970245)), (73, np.float64(3.6795288643940784)), (9, np.float64(4.923172578190234))]
[INFO][14:12:33]: 🎯 算法参数: V=1.0, τ_max=5, 最大聚合客户端数=5
[INFO][14:12:33]: 🎯 聚合1个客户端: [64]
[INFO][14:12:33]: 🎯   - D_t=1.7415, 队列惩罚=0.0000, 目标值=1.7415
[INFO][14:12:33]: 🎯   - 成功上传:1个, 蒸馏补偿:0个
[INFO][14:12:33]: 🎯 找到更优聚合集合: [64]，目标值: 1.7415
[INFO][14:12:33]: 🎯 聚合2个客户端: [64, 33]
[INFO][14:12:33]: 🎯   - D_t=2.3755, 队列惩罚=0.0000, 目标值=2.3755
[INFO][14:12:33]: 🎯   - 成功上传:2个, 蒸馏补偿:0个
[INFO][14:12:33]: 🎯 聚合3个客户端: [64, 33, 18]
[INFO][14:12:33]: 🎯   - D_t=2.5078, 队列惩罚=0.0000, 目标值=2.5078
[INFO][14:12:33]: 🎯   - 成功上传:3个, 蒸馏补偿:0个
[INFO][14:12:33]: 🎯 聚合4个客户端: [64, 33, 18, 73]
[INFO][14:12:33]: 🎯   - D_t=3.6795, 队列惩罚=0.0000, 目标值=3.6795
[INFO][14:12:33]: 🎯   - 成功上传:4个, 蒸馏补偿:0个
[INFO][14:12:33]: 🎯 聚合5个客户端: [64, 33, 18, 73, 9]
[INFO][14:12:33]: 🎯   - D_t=4.9232, 队列惩罚=0.0000, 目标值=4.9232
[INFO][14:12:33]: 🎯   - 成功上传:5个, 蒸馏补偿:0个
[INFO][14:12:33]: 🎯 贪心聚合子集选择完成:
[INFO][14:12:33]: 🎯 - 选中客户端: [64]
[INFO][14:12:33]: 🎯 - 顺利上传: 1个, 蒸馏补偿: 0个
[INFO][14:12:33]: 🎯 - 最终目标函数值: 1.7415
[INFO][14:12:33]: 🎯 - 最大延迟D_t: 1.7415
[INFO][14:12:33]: 🎯 贪心选择结果：选中 1 个顺利上传的客户端
[INFO][14:12:33]: 🎯 选中的顺利上传客户端ID: [64]
[INFO][14:12:33]: 🎯 检测到 5 个无法顺利上传的客户端，暂时不做处理
[INFO][14:12:33]: 🎯 无法顺利上传的客户端ID: [98, 16, 58, 61, 84]
[INFO][14:12:33]: 🎯 客户端处理完成:
[INFO][14:12:33]: 🎯 - 选中顺利上传: 1个客户端
[INFO][14:12:33]: 🎯 - 暂不处理蒸馏补偿: 5个客户端
[INFO][14:12:33]: 🎯 聚合开始 - 选中的顺利上传客户端: 1个
[INFO][14:12:33]: 🎯 本轮参与聚合的客户端ID列表: [64]
[INFO][14:12:33]: [Server #29312] Updated weights have been received.
[INFO][14:12:33]: [Server #29312] Aggregating model weight deltas.
[INFO][14:12:33]: [Server #29312] Finished aggregating updated weights.
[INFO][14:12:33]: [Server #29312] Started model testing.
[INFO][14:13:17]: [Trainer.test] 测试完成 - 准确率: 12.00% (1200/10000)
[INFO][14:13:17]: [93m[1m[Server #29312] Global model accuracy: 12.00%
[0m
[INFO][14:13:17]: get_logged_items 被调用
[INFO][14:13:17]: 从updates获取参与客户端: [64]
[INFO][14:13:17]: 客户端 64 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][14:13:17]: 陈旧度统计 - 参与客户端: [64], 陈旧度: [1]
[INFO][14:13:17]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][14:13:17]: 最终logged_items: {'round': 1, 'accuracy': 0.12, 'accuracy_std': 0, 'elapsed_time': 75.19447612762451, 'processing_time': 0.09752129999105819, 'comm_time': 0, 'round_time': 74.26096319642966, 'comm_overhead': 749.9883651733398, 'global_accuracy': 0.12, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][14:13:17]: [Server #29312] All client reports have been processed.
[INFO][14:13:17]: 🎯 本轮检测到但暂不处理的蒸馏补偿客户端: [98, 16, 58, 61, 84]
[INFO][14:13:17]: [Server #29312] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][14:13:17]: [Server #29312] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][14:13:17]: [93m[1m
[Server #29312] Starting round 2/400.[0m
[INFO][14:13:17]: [Server #29312] Selected clients: [100, 66, 39, 34, 85, 17, 45, 6, 5, 93]
[INFO][14:13:17]: [Server #29312] Selecting client #100 for training.
[INFO][14:13:17]: [Server #29312] Sending the current model to client #100 (simulated).
[INFO][14:13:17]: [Server #29312] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][14:13:17]: [Server #29312] Selecting client #66 for training.
[INFO][14:13:17]: [Server #29312] Sending the current model to client #66 (simulated).
[INFO][14:13:17]: [Server #29312] Sending 18.75 MB of payload data to client #66 (simulated).
[INFO][14:13:17]: [Server #29312] Selecting client #39 for training.
[INFO][14:13:17]: [Server #29312] Sending the current model to client #39 (simulated).
[INFO][14:13:17]: [Server #29312] Sending 18.75 MB of payload data to client #39 (simulated).
[INFO][14:13:17]: [Server #29312] Selecting client #34 for training.
[INFO][14:13:17]: [Server #29312] Sending the current model to client #34 (simulated).
[INFO][14:13:17]: [Server #29312] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][14:13:17]: [Server #29312] Selecting client #85 for training.
[INFO][14:13:17]: [Server #29312] Sending the current model to client #85 (simulated).
[INFO][14:13:17]: [Server #29312] Sending 18.75 MB of payload data to client #85 (simulated).
[INFO][14:13:17]: [Server #29312] Selecting client #17 for training.
[INFO][14:13:17]: [Server #29312] Sending the current model to client #17 (simulated).
[INFO][14:13:18]: [Server #29312] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][14:13:18]: [Server #29312] Selecting client #45 for training.
[INFO][14:13:18]: [Server #29312] Sending the current model to client #45 (simulated).
[INFO][14:13:18]: [Server #29312] Sending 18.75 MB of payload data to client #45 (simulated).
[INFO][14:13:18]: [Server #29312] Selecting client #6 for training.
[INFO][14:13:18]: [Server #29312] Sending the current model to client #6 (simulated).
[INFO][14:13:18]: [Server #29312] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][14:13:18]: [Server #29312] Selecting client #5 for training.
[INFO][14:13:18]: [Server #29312] Sending the current model to client #5 (simulated).
[INFO][14:13:18]: [Server #29312] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][14:13:18]: [Server #29312] Selecting client #93 for training.
[INFO][14:13:19]: [Server #29312] Sending the current model to client #93 (simulated).
[INFO][14:13:19]: [Server #29312] Sending 18.75 MB of payload data to client #93 (simulated).
[INFO][14:19:27]: [Server #29312] Received 18.75 MB of payload data from client #39 (simulated).
[INFO][14:19:29]: [Server #29312] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][14:19:33]: [Server #29312] Received 18.75 MB of payload data from client #100 (simulated).
[INFO][14:19:33]: [Server #29312] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][14:19:34]: [Server #29312] Received 18.75 MB of payload data from client #85 (simulated).
[INFO][14:19:34]: [Server #29312] Received 18.75 MB of payload data from client #45 (simulated).
[INFO][14:19:35]: [Server #29312] Received 18.75 MB of payload data from client #66 (simulated).
[INFO][14:19:36]: [Server #29312] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][14:19:36]: [Server #29312] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][14:19:36]: [Server #29312] Received 18.75 MB of payload data from client #93 (simulated).
[INFO][14:19:36]: [Server #29312] Adding client #49 to the list of clients for aggregation.
[INFO][14:19:36]: [Server #29312] Adding client #27 to the list of clients for aggregation.
[INFO][14:19:36]: [Server #29312] Adding client #4 to the list of clients for aggregation.
[INFO][14:19:36]: [Server #29312] Adding client #78 to the list of clients for aggregation.
[INFO][14:19:36]: [Server #29312] Adding client #63 to the list of clients for aggregation.
[INFO][14:19:36]: [Server #29312] Adding client #13 to the list of clients for aggregation.
[INFO][14:19:36]: [Server #29312] Adding client #56 to the list of clients for aggregation.
[INFO][14:19:36]: [Server #29312] Adding client #50 to the list of clients for aggregation.
[INFO][14:19:36]: [Server #29312] Adding client #99 to the list of clients for aggregation.
[INFO][14:19:36]: [Server #29312] Adding client #1 to the list of clients for aggregation.
[INFO][14:19:36]: [Server #29312] Aggregating 10 clients in total.
[INFO][14:19:36]: 🎯 ReFedScaFL _process_reports 被调用，开始自定义聚合逻辑
[INFO][14:19:36]: 🎯 开始ReFedScaFL聚合逻辑
[INFO][14:19:36]: 🎯 客户端 49 通信时间(H_comm)：0.52 <= 2.00，可以顺利上传
[INFO][14:19:36]: 🎯 客户端 27 通信时间(H_comm)：1.49 <= 2.00，可以顺利上传
[INFO][14:19:36]: 🎯 客户端 4 通信时间(H_comm)：1.14 <= 2.00，可以顺利上传
[INFO][14:19:36]: 🎯 客户端 78 通信时间(H_comm)：0.61 <= 2.00，可以顺利上传
[INFO][14:19:36]: 🎯 客户端 63 通信时间(H_comm)：3.28 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:19:36]: 🎯 客户端 13 通信时间(H_comm)：1.80 <= 2.00，可以顺利上传
[INFO][14:19:36]: 🎯 客户端 56 通信时间(H_comm)：1.30 <= 2.00，可以顺利上传
[INFO][14:19:36]: 🎯 客户端 50 通信时间(H_comm)：2.03 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:19:36]: 🎯 客户端 99 通信时间(H_comm)：1.15 <= 2.00，可以顺利上传
[INFO][14:19:36]: 🎯 客户端 1 通信时间(H_comm)：2.26 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:19:36]: 🎯 客户端处理结果 - 顺利上传: 7个, 无法顺利上传: 3个
[INFO][14:19:36]: 🎯 对 7 个顺利上传的客户端进行贪心选择
[INFO][14:19:36]: 🎯 开始贪心选择聚合子集，候选更新数量: 7
[INFO][14:19:36]: 🎯 候选客户端按延迟估计值排序: [(78, np.float64(1.5701614108097095)), (49, np.float64(1.7042747632311772)), (56, np.float64(2.064400319843843)), (4, np.float64(2.1217597388885308)), (13, np.float64(2.3651850148428237)), (27, np.float64(2.6563365798010086)), (99, np.float64(4.155742000230745))]
[INFO][14:19:36]: 🎯 算法参数: V=1.0, τ_max=5, 最大聚合客户端数=7
[INFO][14:19:36]: 🎯 聚合1个客户端: [78]
[INFO][14:19:36]: 🎯   - D_t=1.5702, 队列惩罚=0.0000, 目标值=1.5702
[INFO][14:19:36]: 🎯   - 成功上传:1个, 蒸馏补偿:0个
[INFO][14:19:36]: 🎯 找到更优聚合集合: [78]，目标值: 1.5702
[INFO][14:19:36]: 🎯 聚合2个客户端: [78, 49]
[INFO][14:19:36]: 🎯   - D_t=1.7043, 队列惩罚=0.0000, 目标值=1.7043
[INFO][14:19:36]: 🎯   - 成功上传:2个, 蒸馏补偿:0个
[INFO][14:19:36]: 🎯 聚合3个客户端: [78, 49, 56]
[INFO][14:19:36]: 🎯   - D_t=2.0644, 队列惩罚=0.0000, 目标值=2.0644
[INFO][14:19:36]: 🎯   - 成功上传:3个, 蒸馏补偿:0个
[INFO][14:19:36]: 🎯 聚合4个客户端: [78, 49, 56, 4]
[INFO][14:19:36]: 🎯   - D_t=2.1218, 队列惩罚=0.0000, 目标值=2.1218
[INFO][14:19:36]: 🎯   - 成功上传:4个, 蒸馏补偿:0个
[INFO][14:19:36]: 🎯 聚合5个客户端: [78, 49, 56, 4, 13]
[INFO][14:19:36]: 🎯   - D_t=2.3652, 队列惩罚=0.0000, 目标值=2.3652
[INFO][14:19:36]: 🎯   - 成功上传:5个, 蒸馏补偿:0个
[INFO][14:19:36]: 🎯 聚合6个客户端: [78, 49, 56, 4, 13, 27]
[INFO][14:19:36]: 🎯   - D_t=2.6563, 队列惩罚=0.0000, 目标值=2.6563
[INFO][14:19:36]: 🎯   - 成功上传:6个, 蒸馏补偿:0个
[INFO][14:19:36]: 🎯 聚合7个客户端: [78, 49, 56, 4, 13, 27, 99]
[INFO][14:19:36]: 🎯   - D_t=4.1557, 队列惩罚=0.0000, 目标值=4.1557
[INFO][14:19:36]: 🎯   - 成功上传:7个, 蒸馏补偿:0个
[INFO][14:19:36]: 🎯 贪心聚合子集选择完成:
[INFO][14:19:36]: 🎯 - 选中客户端: [78]
[INFO][14:19:36]: 🎯 - 顺利上传: 1个, 蒸馏补偿: 0个
[INFO][14:19:36]: 🎯 - 最终目标函数值: 1.5702
[INFO][14:19:36]: 🎯 - 最大延迟D_t: 1.5702
[INFO][14:19:36]: 🎯 贪心选择结果：选中 1 个顺利上传的客户端
[INFO][14:19:36]: 🎯 选中的顺利上传客户端ID: [78]
[INFO][14:19:36]: 🎯 检测到 3 个无法顺利上传的客户端，暂时不做处理
[INFO][14:19:36]: 🎯 无法顺利上传的客户端ID: [63, 50, 1]
[INFO][14:19:36]: 🎯 客户端处理完成:
[INFO][14:19:36]: 🎯 - 选中顺利上传: 1个客户端
[INFO][14:19:36]: 🎯 - 暂不处理蒸馏补偿: 3个客户端
[INFO][14:19:36]: 🎯 聚合开始 - 选中的顺利上传客户端: 1个
[INFO][14:19:36]: 🎯 本轮参与聚合的客户端ID列表: [78]
[INFO][14:19:36]: [Server #29312] Updated weights have been received.
[INFO][14:19:36]: [Server #29312] Aggregating model weight deltas.
[INFO][14:19:37]: [Server #29312] Finished aggregating updated weights.
[INFO][14:19:37]: [Server #29312] Started model testing.
[INFO][14:20:23]: [Trainer.test] 测试完成 - 准确率: 10.01% (1001/10000)
[INFO][14:20:23]: [93m[1m[Server #29312] Global model accuracy: 10.01%
[0m
[INFO][14:20:23]: get_logged_items 被调用
[INFO][14:20:23]: 从updates获取参与客户端: [78]
[INFO][14:20:23]: 客户端 78 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][14:20:23]: 陈旧度统计 - 参与客户端: [78], 陈旧度: [1]
[INFO][14:20:23]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][14:20:23]: 最终logged_items: {'round': 2, 'accuracy': 0.1001, 'accuracy_std': 0, 'elapsed_time': 120.73788452148438, 'processing_time': 0.00039270002162083983, 'comm_time': 0, 'round_time': 116.20082357007777, 'comm_overhead': 1124.9825477600098, 'global_accuracy': 0.1001, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][14:20:23]: [Server #29312] All client reports have been processed.
[INFO][14:20:23]: 🎯 本轮检测到但暂不处理的蒸馏补偿客户端: [63, 50, 1]
[INFO][14:20:23]: [Server #29312] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][14:20:23]: [Server #29312] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][14:20:23]: [93m[1m
[Server #29312] Starting round 3/400.[0m
[INFO][14:20:23]: [Server #29312] Selected clients: [77, 2, 55, 97, 31, 61, 4, 75, 32, 63]
[INFO][14:20:23]: [Server #29312] Selecting client #77 for training.
[INFO][14:20:23]: [Server #29312] Sending the current model to client #77 (simulated).
[INFO][14:20:23]: [Server #29312] Sending 18.75 MB of payload data to client #77 (simulated).
[INFO][14:20:23]: [Server #29312] Selecting client #2 for training.
[INFO][14:20:23]: [Server #29312] Sending the current model to client #2 (simulated).
[INFO][14:20:23]: [Server #29312] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][14:20:23]: [Server #29312] Selecting client #55 for training.
[INFO][14:20:23]: [Server #29312] Sending the current model to client #55 (simulated).
[INFO][14:20:23]: [Server #29312] Sending 18.75 MB of payload data to client #55 (simulated).
[INFO][14:20:23]: [Server #29312] Selecting client #97 for training.
[INFO][14:20:23]: [Server #29312] Sending the current model to client #97 (simulated).
[INFO][14:20:23]: [Server #29312] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][14:20:23]: [Server #29312] Selecting client #31 for training.
[INFO][14:20:23]: [Server #29312] Sending the current model to client #31 (simulated).
[INFO][14:20:23]: [Server #29312] Sending 18.75 MB of payload data to client #31 (simulated).
[INFO][14:20:23]: [Server #29312] Selecting client #61 for training.
[INFO][14:20:24]: [Server #29312] Sending the current model to client #61 (simulated).
[INFO][14:20:24]: [Server #29312] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][14:20:24]: [Server #29312] Selecting client #4 for training.
[INFO][14:20:24]: [Server #29312] Sending the current model to client #4 (simulated).
[INFO][14:20:24]: [Server #29312] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][14:20:24]: [Server #29312] Selecting client #75 for training.
[INFO][14:20:24]: [Server #29312] Sending the current model to client #75 (simulated).
[INFO][14:20:24]: [Server #29312] Sending 18.75 MB of payload data to client #75 (simulated).
[INFO][14:20:24]: [Server #29312] Selecting client #32 for training.
[INFO][14:20:24]: [Server #29312] Sending the current model to client #32 (simulated).
[INFO][14:20:24]: [Server #29312] Sending 18.75 MB of payload data to client #32 (simulated).
[INFO][14:20:24]: [Server #29312] Selecting client #63 for training.
[INFO][14:20:24]: [Server #29312] Sending the current model to client #63 (simulated).
[INFO][14:20:25]: [Server #29312] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][14:26:34]: [Server #29312] Received 18.75 MB of payload data from client #55 (simulated).
[INFO][14:26:34]: [Server #29312] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][14:26:36]: [Server #29312] Received 18.75 MB of payload data from client #77 (simulated).
[INFO][14:26:37]: [Server #29312] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][14:26:38]: [Server #29312] Received 18.75 MB of payload data from client #31 (simulated).
[INFO][14:26:40]: [Server #29312] Received 18.75 MB of payload data from client #97 (simulated).
[INFO][14:26:42]: [Server #29312] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][14:26:42]: [Server #29312] Received 18.75 MB of payload data from client #32 (simulated).
[INFO][14:26:42]: [Server #29312] Received 18.75 MB of payload data from client #75 (simulated).
[INFO][14:26:43]: [Server #29312] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][14:26:43]: [Server #29312] Adding client #6 to the list of clients for aggregation.
[INFO][14:26:43]: [Server #29312] Adding client #17 to the list of clients for aggregation.
[INFO][14:26:43]: [Server #29312] Adding client #85 to the list of clients for aggregation.
[INFO][14:26:43]: [Server #29312] Adding client #66 to the list of clients for aggregation.
[INFO][14:26:43]: [Server #29312] Adding client #100 to the list of clients for aggregation.
[INFO][14:26:43]: [Server #29312] Adding client #5 to the list of clients for aggregation.
[INFO][14:26:43]: [Server #29312] Adding client #39 to the list of clients for aggregation.
[INFO][14:26:43]: [Server #29312] Adding client #45 to the list of clients for aggregation.
[INFO][14:26:43]: [Server #29312] Adding client #93 to the list of clients for aggregation.
[INFO][14:26:43]: [Server #29312] Adding client #34 to the list of clients for aggregation.
[INFO][14:26:43]: [Server #29312] Aggregating 10 clients in total.
[INFO][14:26:43]: 🎯 ReFedScaFL _process_reports 被调用，开始自定义聚合逻辑
[INFO][14:26:43]: 🎯 开始ReFedScaFL聚合逻辑
[INFO][14:26:43]: 🎯 客户端 6 通信时间(H_comm)：3.78 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:26:43]: 🎯 客户端 17 通信时间(H_comm)：1.17 <= 2.00，可以顺利上传
[INFO][14:26:43]: 🎯 客户端 85 通信时间(H_comm)：1.56 <= 2.00，可以顺利上传
[INFO][14:26:43]: 🎯 客户端 66 通信时间(H_comm)：1.32 <= 2.00，可以顺利上传
[INFO][14:26:43]: 🎯 客户端 100 通信时间(H_comm)：6.50 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:26:43]: 🎯 客户端 5 通信时间(H_comm)：1.89 <= 2.00，可以顺利上传
[INFO][14:26:43]: 🎯 客户端 39 通信时间(H_comm)：2.99 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:26:43]: 🎯 客户端 45 通信时间(H_comm)：1.10 <= 2.00，可以顺利上传
[INFO][14:26:43]: 🎯 客户端 93 通信时间(H_comm)：1.47 <= 2.00，可以顺利上传
[INFO][14:26:43]: 🎯 客户端 34 通信时间(H_comm)：3.54 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:26:43]: 🎯 客户端处理结果 - 顺利上传: 6个, 无法顺利上传: 4个
[INFO][14:26:43]: 🎯 对 6 个顺利上传的客户端进行贪心选择
[INFO][14:26:43]: 🎯 开始贪心选择聚合子集，候选更新数量: 6
[INFO][14:26:43]: 🎯 候选客户端按延迟估计值排序: [(85, np.float64(2.398524029763508)), (93, np.float64(2.4947935051293446)), (45, np.float64(2.7995199495497363)), (66, np.float64(3.4661502277790563)), (17, np.float64(3.7776945448270034)), (5, np.float64(6.149022867266371))]
[INFO][14:26:43]: 🎯 算法参数: V=1.0, τ_max=5, 最大聚合客户端数=6
[INFO][14:26:43]: 🎯 聚合1个客户端: [85]
[INFO][14:26:43]: 🎯   - D_t=2.3985, 队列惩罚=0.0000, 目标值=2.3985
[INFO][14:26:43]: 🎯   - 成功上传:1个, 蒸馏补偿:0个
[INFO][14:26:43]: 🎯 找到更优聚合集合: [85]，目标值: 2.3985
[INFO][14:26:43]: 🎯 聚合2个客户端: [85, 93]
[INFO][14:26:43]: 🎯   - D_t=2.4948, 队列惩罚=0.0000, 目标值=2.4948
[INFO][14:26:43]: 🎯   - 成功上传:2个, 蒸馏补偿:0个
[INFO][14:26:43]: 🎯 聚合3个客户端: [85, 93, 45]
[INFO][14:26:43]: 🎯   - D_t=2.7995, 队列惩罚=0.0000, 目标值=2.7995
[INFO][14:26:43]: 🎯   - 成功上传:3个, 蒸馏补偿:0个
[INFO][14:26:43]: 🎯 聚合4个客户端: [85, 93, 45, 66]
[INFO][14:26:43]: 🎯   - D_t=3.4662, 队列惩罚=0.0000, 目标值=3.4662
[INFO][14:26:43]: 🎯   - 成功上传:4个, 蒸馏补偿:0个
[INFO][14:26:43]: 🎯 聚合5个客户端: [85, 93, 45, 66, 17]
[INFO][14:26:43]: 🎯   - D_t=3.7777, 队列惩罚=0.0000, 目标值=3.7777
[INFO][14:26:43]: 🎯   - 成功上传:5个, 蒸馏补偿:0个
[INFO][14:26:43]: 🎯 聚合6个客户端: [85, 93, 45, 66, 17, 5]
[INFO][14:26:43]: 🎯   - D_t=6.1490, 队列惩罚=0.0000, 目标值=6.1490
[INFO][14:26:43]: 🎯   - 成功上传:6个, 蒸馏补偿:0个
[INFO][14:26:43]: 🎯 贪心聚合子集选择完成:
[INFO][14:26:43]: 🎯 - 选中客户端: [85]
[INFO][14:26:43]: 🎯 - 顺利上传: 1个, 蒸馏补偿: 0个
[INFO][14:26:43]: 🎯 - 最终目标函数值: 2.3985
[INFO][14:26:43]: 🎯 - 最大延迟D_t: 2.3985
[INFO][14:26:43]: 🎯 贪心选择结果：选中 1 个顺利上传的客户端
[INFO][14:26:43]: 🎯 选中的顺利上传客户端ID: [85]
[INFO][14:26:43]: 🎯 检测到 4 个无法顺利上传的客户端，暂时不做处理
[INFO][14:26:43]: 🎯 无法顺利上传的客户端ID: [6, 100, 39, 34]
[INFO][14:26:43]: 🎯 客户端处理完成:
[INFO][14:26:43]: 🎯 - 选中顺利上传: 1个客户端
[INFO][14:26:43]: 🎯 - 暂不处理蒸馏补偿: 4个客户端
[INFO][14:26:43]: 🎯 聚合开始 - 选中的顺利上传客户端: 1个
[INFO][14:26:43]: 🎯 本轮参与聚合的客户端ID列表: [85]
[INFO][14:26:43]: [Server #29312] Updated weights have been received.
[INFO][14:26:43]: [Server #29312] Aggregating model weight deltas.
[INFO][14:26:43]: [Server #29312] Finished aggregating updated weights.
[INFO][14:26:43]: [Server #29312] Started model testing.
[INFO][14:27:34]: [Trainer.test] 测试完成 - 准确率: 15.21% (1521/10000)
[INFO][14:27:34]: [93m[1m[Server #29312] Global model accuracy: 15.21%
[0m
[INFO][14:27:34]: get_logged_items 被调用
[INFO][14:27:34]: 从updates获取参与客户端: [85]
[INFO][14:27:34]: 客户端 85 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][14:27:34]: 陈旧度统计 - 参与客户端: [85], 陈旧度: [1]
[INFO][14:27:34]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][14:27:34]: 最终logged_items: {'round': 3, 'accuracy': 0.1521, 'accuracy_std': 0, 'elapsed_time': 214.97367191314697, 'processing_time': 0.000238799984799698, 'comm_time': 0, 'round_time': 135.982085609372, 'comm_overhead': 1499.9767303466797, 'global_accuracy': 0.1521, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][14:27:34]: [Server #29312] All client reports have been processed.
[INFO][14:27:34]: 🎯 本轮检测到但暂不处理的蒸馏补偿客户端: [6, 100, 39, 34]
[INFO][14:27:34]: [Server #29312] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][14:27:34]: [Server #29312] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][14:27:34]: [93m[1m
[Server #29312] Starting round 4/400.[0m
[INFO][14:27:34]: [Server #29312] Selected clients: [71, 80, 34, 49, 96, 33, 66, 42, 5, 59]
[INFO][14:27:34]: [Server #29312] Selecting client #71 for training.
[INFO][14:27:34]: [Server #29312] Sending the current model to client #71 (simulated).
[INFO][14:27:34]: [Server #29312] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][14:27:34]: [Server #29312] Selecting client #80 for training.
[INFO][14:27:34]: [Server #29312] Sending the current model to client #80 (simulated).
[INFO][14:27:34]: [Server #29312] Sending 18.75 MB of payload data to client #80 (simulated).
[INFO][14:27:34]: [Server #29312] Selecting client #34 for training.
[INFO][14:27:34]: [Server #29312] Sending the current model to client #34 (simulated).
[INFO][14:27:34]: [Server #29312] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][14:27:34]: [Server #29312] Selecting client #49 for training.
[INFO][14:27:34]: [Server #29312] Sending the current model to client #49 (simulated).
[INFO][14:27:34]: [Server #29312] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][14:27:34]: [Server #29312] Selecting client #96 for training.
[INFO][14:27:34]: [Server #29312] Sending the current model to client #96 (simulated).
[INFO][14:27:34]: [Server #29312] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][14:27:34]: [Server #29312] Selecting client #33 for training.
[INFO][14:27:34]: [Server #29312] Sending the current model to client #33 (simulated).
[INFO][14:27:34]: [Server #29312] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][14:27:35]: [Server #29312] Selecting client #66 for training.
[INFO][14:27:35]: [Server #29312] Sending the current model to client #66 (simulated).
[INFO][14:27:35]: [Server #29312] Sending 18.75 MB of payload data to client #66 (simulated).
[INFO][14:27:35]: [Server #29312] Selecting client #42 for training.
[INFO][14:27:35]: [Server #29312] Sending the current model to client #42 (simulated).
[INFO][14:27:35]: [Server #29312] Sending 18.75 MB of payload data to client #42 (simulated).
[INFO][14:27:35]: [Server #29312] Selecting client #5 for training.
[INFO][14:27:35]: [Server #29312] Sending the current model to client #5 (simulated).
[INFO][14:27:35]: [Server #29312] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][14:27:35]: [Server #29312] Selecting client #59 for training.
[INFO][14:27:35]: [Server #29312] Sending the current model to client #59 (simulated).
[INFO][14:27:36]: [Server #29312] Sending 18.75 MB of payload data to client #59 (simulated).
[INFO][14:33:47]: [Server #29312] Received 18.75 MB of payload data from client #80 (simulated).
[INFO][14:33:48]: [Server #29312] Received 18.75 MB of payload data from client #42 (simulated).
[INFO][14:33:51]: [Server #29312] Received 18.75 MB of payload data from client #59 (simulated).
[INFO][14:33:52]: [Server #29312] Received 18.75 MB of payload data from client #66 (simulated).
[INFO][14:33:52]: [Server #29312] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][14:33:52]: [Server #29312] Received 18.75 MB of payload data from client #71 (simulated).
[INFO][14:33:55]: [Server #29312] Received 18.75 MB of payload data from client #96 (simulated).
[INFO][14:33:55]: [Server #29312] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][14:33:56]: [Server #29312] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][14:33:57]: [Server #29312] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][14:33:57]: [Server #29312] Adding client #55 to the list of clients for aggregation.
[INFO][14:33:57]: [Server #29312] Adding client #2 to the list of clients for aggregation.
[INFO][14:33:57]: [Server #29312] Adding client #97 to the list of clients for aggregation.
[INFO][14:33:57]: [Server #29312] Adding client #4 to the list of clients for aggregation.
[INFO][14:33:57]: [Server #29312] Adding client #77 to the list of clients for aggregation.
[INFO][14:33:57]: [Server #29312] Adding client #31 to the list of clients for aggregation.
[INFO][14:33:57]: [Server #29312] Adding client #32 to the list of clients for aggregation.
[INFO][14:33:57]: [Server #29312] Adding client #63 to the list of clients for aggregation.
[INFO][14:33:57]: [Server #29312] Adding client #75 to the list of clients for aggregation.
[INFO][14:33:57]: [Server #29312] Adding client #61 to the list of clients for aggregation.
[INFO][14:33:57]: [Server #29312] Aggregating 10 clients in total.
[INFO][14:33:57]: 🎯 ReFedScaFL _process_reports 被调用，开始自定义聚合逻辑
[INFO][14:33:57]: 🎯 开始ReFedScaFL聚合逻辑
[INFO][14:33:57]: 🎯 客户端 55 通信时间(H_comm)：1.64 <= 2.00，可以顺利上传
[INFO][14:33:57]: 🎯 客户端 2 通信时间(H_comm)：1.77 <= 2.00，可以顺利上传
[INFO][14:33:57]: 🎯 客户端 97 通信时间(H_comm)：0.89 <= 2.00，可以顺利上传
[INFO][14:33:57]: 🎯 客户端 4 通信时间(H_comm)：1.47 <= 2.00，可以顺利上传
[INFO][14:33:57]: 🎯 客户端 77 通信时间(H_comm)：1.23 <= 2.00，可以顺利上传
[INFO][14:33:57]: 🎯 客户端 31 通信时间(H_comm)：2.03 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:33:57]: 🎯 客户端 32 通信时间(H_comm)：2.69 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:33:57]: 🎯 客户端 63 通信时间(H_comm)：1.27 <= 2.00，可以顺利上传
[INFO][14:33:57]: 🎯 客户端 75 通信时间(H_comm)：2.93 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:33:57]: 🎯 客户端 61 通信时间(H_comm)：2.37 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:33:57]: 🎯 客户端处理结果 - 顺利上传: 6个, 无法顺利上传: 4个
[INFO][14:33:57]: 🎯 对 6 个顺利上传的客户端进行贪心选择
[INFO][14:33:57]: 🎯 开始贪心选择聚合子集，候选更新数量: 6
[INFO][14:33:57]: 🎯 候选客户端按延迟估计值排序: [(63, np.float64(1.8752763049608776)), (97, np.float64(2.021751937818239)), (4, np.float64(2.1748522790598717)), (77, np.float64(2.3567282747509264)), (2, np.float64(3.8952640153356377)), (55, np.float64(4.480223857451946))]
[INFO][14:33:57]: 🎯 算法参数: V=1.0, τ_max=5, 最大聚合客户端数=6
[INFO][14:33:57]: 🎯 聚合1个客户端: [63]
[INFO][14:33:57]: 🎯   - D_t=1.8753, 队列惩罚=0.0000, 目标值=1.8753
[INFO][14:33:57]: 🎯   - 成功上传:1个, 蒸馏补偿:0个
[INFO][14:33:57]: 🎯 找到更优聚合集合: [63]，目标值: 1.8753
[INFO][14:33:57]: 🎯 聚合2个客户端: [63, 97]
[INFO][14:33:57]: 🎯   - D_t=2.0218, 队列惩罚=0.0000, 目标值=2.0218
[INFO][14:33:57]: 🎯   - 成功上传:2个, 蒸馏补偿:0个
[INFO][14:33:57]: 🎯 聚合3个客户端: [63, 97, 4]
[INFO][14:33:57]: 🎯   - D_t=2.1749, 队列惩罚=0.0000, 目标值=2.1749
[INFO][14:33:57]: 🎯   - 成功上传:3个, 蒸馏补偿:0个
[INFO][14:33:57]: 🎯 聚合4个客户端: [63, 97, 4, 77]
[INFO][14:33:57]: 🎯   - D_t=2.3567, 队列惩罚=0.0000, 目标值=2.3567
[INFO][14:33:57]: 🎯   - 成功上传:4个, 蒸馏补偿:0个
[INFO][14:33:57]: 🎯 聚合5个客户端: [63, 97, 4, 77, 2]
[INFO][14:33:57]: 🎯   - D_t=3.8953, 队列惩罚=0.0000, 目标值=3.8953
[INFO][14:33:57]: 🎯   - 成功上传:5个, 蒸馏补偿:0个
[INFO][14:33:57]: 🎯 聚合6个客户端: [63, 97, 4, 77, 2, 55]
[INFO][14:33:57]: 🎯   - D_t=4.4802, 队列惩罚=0.0000, 目标值=4.4802
[INFO][14:33:57]: 🎯   - 成功上传:6个, 蒸馏补偿:0个
[INFO][14:33:57]: 🎯 贪心聚合子集选择完成:
[INFO][14:33:57]: 🎯 - 选中客户端: [63]
[INFO][14:33:57]: 🎯 - 顺利上传: 1个, 蒸馏补偿: 0个
[INFO][14:33:57]: 🎯 - 最终目标函数值: 1.8753
[INFO][14:33:57]: 🎯 - 最大延迟D_t: 1.8753
[INFO][14:33:57]: 🎯 贪心选择结果：选中 1 个顺利上传的客户端
[INFO][14:33:57]: 🎯 选中的顺利上传客户端ID: [63]
[INFO][14:33:57]: 🎯 检测到 4 个无法顺利上传的客户端，暂时不做处理
[INFO][14:33:57]: 🎯 无法顺利上传的客户端ID: [31, 32, 75, 61]
[INFO][14:33:57]: 🎯 客户端处理完成:
[INFO][14:33:57]: 🎯 - 选中顺利上传: 1个客户端
[INFO][14:33:57]: 🎯 - 暂不处理蒸馏补偿: 4个客户端
[INFO][14:33:57]: 🎯 聚合开始 - 选中的顺利上传客户端: 1个
[INFO][14:33:57]: 🎯 本轮参与聚合的客户端ID列表: [63]
[INFO][14:33:57]: [Server #29312] Updated weights have been received.
[INFO][14:33:57]: [Server #29312] Aggregating model weight deltas.
[INFO][14:33:57]: [Server #29312] Finished aggregating updated weights.
[INFO][14:33:57]: [Server #29312] Started model testing.
[INFO][14:34:46]: [Trainer.test] 测试完成 - 准确率: 18.57% (1857/10000)
[INFO][14:34:46]: [93m[1m[Server #29312] Global model accuracy: 18.57%
[0m
[INFO][14:34:46]: get_logged_items 被调用
[INFO][14:34:46]: 从updates获取参与客户端: [63]
[INFO][14:34:46]: 客户端 63 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][14:34:46]: 陈旧度统计 - 参与客户端: [63], 陈旧度: [1]
[INFO][14:34:46]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][14:34:46]: 最终logged_items: {'round': 4, 'accuracy': 0.1857, 'accuracy_std': 0, 'elapsed_time': 262.3685290813446, 'processing_time': 0.0001635999942664057, 'comm_time': 0, 'round_time': 141.3636200876499, 'comm_overhead': 1874.9709129333496, 'global_accuracy': 0.1857, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][14:34:46]: [Server #29312] All client reports have been processed.
[INFO][14:34:46]: 🎯 本轮检测到但暂不处理的蒸馏补偿客户端: [31, 32, 75, 61]
[INFO][14:34:46]: [Server #29312] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][14:34:46]: [Server #29312] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][14:34:46]: [93m[1m
[Server #29312] Starting round 5/400.[0m
[INFO][14:34:46]: [Server #29312] Selected clients: [81, 92, 14, 25, 90, 41, 17, 47, 73, 61]
[INFO][14:34:46]: [Server #29312] Selecting client #81 for training.
[INFO][14:34:46]: [Server #29312] Sending the current model to client #81 (simulated).
[INFO][14:34:46]: [Server #29312] Sending 18.75 MB of payload data to client #81 (simulated).
[INFO][14:34:46]: [Server #29312] Selecting client #92 for training.
[INFO][14:34:46]: [Server #29312] Sending the current model to client #92 (simulated).
[INFO][14:34:46]: [Server #29312] Sending 18.75 MB of payload data to client #92 (simulated).
[INFO][14:34:46]: [Server #29312] Selecting client #14 for training.
[INFO][14:34:46]: [Server #29312] Sending the current model to client #14 (simulated).
[INFO][14:34:46]: [Server #29312] Sending 18.75 MB of payload data to client #14 (simulated).
[INFO][14:34:46]: [Server #29312] Selecting client #25 for training.
[INFO][14:34:46]: [Server #29312] Sending the current model to client #25 (simulated).
[INFO][14:34:46]: [Server #29312] Sending 18.75 MB of payload data to client #25 (simulated).
[INFO][14:34:46]: [Server #29312] Selecting client #90 for training.
[INFO][14:34:46]: [Server #29312] Sending the current model to client #90 (simulated).
[INFO][14:34:46]: [Server #29312] Sending 18.75 MB of payload data to client #90 (simulated).
[INFO][14:34:46]: [Server #29312] Selecting client #41 for training.
[INFO][14:34:46]: [Server #29312] Sending the current model to client #41 (simulated).
[INFO][14:34:47]: [Server #29312] Sending 18.75 MB of payload data to client #41 (simulated).
[INFO][14:34:47]: [Server #29312] Selecting client #17 for training.
[INFO][14:34:47]: [Server #29312] Sending the current model to client #17 (simulated).
[INFO][14:34:47]: [Server #29312] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][14:34:47]: [Server #29312] Selecting client #47 for training.
[INFO][14:34:47]: [Server #29312] Sending the current model to client #47 (simulated).
[INFO][14:34:47]: [Server #29312] Sending 18.75 MB of payload data to client #47 (simulated).
[INFO][14:34:47]: [Server #29312] Selecting client #73 for training.
[INFO][14:34:47]: [Server #29312] Sending the current model to client #73 (simulated).
[INFO][14:34:47]: [Server #29312] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][14:34:47]: [Server #29312] Selecting client #61 for training.
[INFO][14:34:47]: [Server #29312] Sending the current model to client #61 (simulated).
[INFO][14:34:48]: [Server #29312] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][14:40:54]: [Server #29312] Received 18.75 MB of payload data from client #81 (simulated).
[INFO][14:40:59]: [Server #29312] Received 18.75 MB of payload data from client #41 (simulated).
[INFO][14:41:00]: [Server #29312] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][14:41:00]: [Server #29312] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][14:41:01]: [Server #29312] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][14:41:03]: [Server #29312] Received 18.75 MB of payload data from client #92 (simulated).
[INFO][14:41:03]: [Server #29312] Received 18.75 MB of payload data from client #90 (simulated).
[INFO][14:41:03]: [Server #29312] Received 18.75 MB of payload data from client #25 (simulated).
[INFO][14:41:04]: [Server #29312] Received 18.75 MB of payload data from client #47 (simulated).
[INFO][14:41:06]: [Server #29312] Received 18.75 MB of payload data from client #14 (simulated).
[INFO][14:41:06]: [Server #29312] Adding client #59 to the list of clients for aggregation.
[INFO][14:41:06]: [Server #29312] Adding client #34 to the list of clients for aggregation.
[INFO][14:41:06]: [Server #29312] Adding client #80 to the list of clients for aggregation.
[INFO][14:41:06]: [Server #29312] Adding client #42 to the list of clients for aggregation.
[INFO][14:41:06]: [Server #29312] Adding client #33 to the list of clients for aggregation.
[INFO][14:41:06]: [Server #29312] Adding client #5 to the list of clients for aggregation.
[INFO][14:41:06]: [Server #29312] Adding client #66 to the list of clients for aggregation.
[INFO][14:41:06]: [Server #29312] Adding client #49 to the list of clients for aggregation.
[INFO][14:41:06]: [Server #29312] Adding client #96 to the list of clients for aggregation.
[INFO][14:41:06]: [Server #29312] Adding client #71 to the list of clients for aggregation.
[INFO][14:41:06]: [Server #29312] Aggregating 10 clients in total.
[INFO][14:41:06]: 🎯 ReFedScaFL _process_reports 被调用，开始自定义聚合逻辑
[INFO][14:41:06]: 🎯 开始ReFedScaFL聚合逻辑
[INFO][14:41:06]: 🎯 客户端 59 通信时间(H_comm)：1.24 <= 2.00，可以顺利上传
[INFO][14:41:06]: 🎯 客户端 34 通信时间(H_comm)：1.37 <= 2.00，可以顺利上传
[INFO][14:41:06]: 🎯 客户端 80 通信时间(H_comm)：1.17 <= 2.00，可以顺利上传
[INFO][14:41:06]: 🎯 客户端 42 通信时间(H_comm)：2.20 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:41:06]: 🎯 客户端 33 通信时间(H_comm)：0.70 <= 2.00，可以顺利上传
[INFO][14:41:06]: 🎯 客户端 5 通信时间(H_comm)：2.11 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:41:06]: 🎯 客户端 66 通信时间(H_comm)：1.31 <= 2.00，可以顺利上传
[INFO][14:41:06]: 🎯 客户端 49 通信时间(H_comm)：2.37 > 2.00，无法顺利上传，需要蒸馏补偿
[INFO][14:41:06]: 🎯 客户端 96 通信时间(H_comm)：0.95 <= 2.00，可以顺利上传
[INFO][14:41:06]: 🎯 客户端 71 通信时间(H_comm)：1.88 <= 2.00，可以顺利上传
[INFO][14:41:06]: 🎯 客户端处理结果 - 顺利上传: 7个, 无法顺利上传: 3个
[INFO][14:41:06]: 🎯 对 7 个顺利上传的客户端进行贪心选择
[INFO][14:41:06]: 🎯 开始贪心选择聚合子集，候选更新数量: 7
[INFO][14:41:06]: 🎯 候选客户端按延迟估计值排序: [(80, np.float64(1.9702784511713531)), (96, np.float64(2.16499803994542)), (33, np.float64(2.2078286847219983)), (34, np.float64(2.81100474219644)), (66, np.float64(2.990480716289504)), (71, np.float64(4.241586641731545)), (59, np.float64(4.736281895134076))]
[INFO][14:41:06]: 🎯 算法参数: V=1.0, τ_max=5, 最大聚合客户端数=7
[INFO][14:41:06]: 🎯 聚合1个客户端: [80]
[INFO][14:41:06]: 🎯   - D_t=1.9703, 队列惩罚=0.0000, 目标值=1.9703
[INFO][14:41:06]: 🎯   - 成功上传:1个, 蒸馏补偿:0个
[INFO][14:41:06]: 🎯 找到更优聚合集合: [80]，目标值: 1.9703
[INFO][14:41:06]: 🎯 聚合2个客户端: [80, 96]
[INFO][14:41:06]: 🎯   - D_t=2.1650, 队列惩罚=0.0000, 目标值=2.1650
[INFO][14:41:06]: 🎯   - 成功上传:2个, 蒸馏补偿:0个
[INFO][14:41:06]: 🎯 聚合3个客户端: [80, 96, 33]
[INFO][14:41:06]: 🎯   - D_t=2.2078, 队列惩罚=0.0000, 目标值=2.2078
[INFO][14:41:06]: 🎯   - 成功上传:3个, 蒸馏补偿:0个
[INFO][14:41:06]: 🎯 聚合4个客户端: [80, 96, 33, 34]
[INFO][14:41:06]: 🎯   - D_t=2.8110, 队列惩罚=0.0000, 目标值=2.8110
[INFO][14:41:06]: 🎯   - 成功上传:4个, 蒸馏补偿:0个
[INFO][14:41:06]: 🎯 聚合5个客户端: [80, 96, 33, 34, 66]
[INFO][14:41:06]: 🎯   - D_t=2.9905, 队列惩罚=0.0000, 目标值=2.9905
[INFO][14:41:06]: 🎯   - 成功上传:5个, 蒸馏补偿:0个
[INFO][14:41:06]: 🎯 聚合6个客户端: [80, 96, 33, 34, 66, 71]
[INFO][14:41:06]: 🎯   - D_t=4.2416, 队列惩罚=0.0000, 目标值=4.2416
[INFO][14:41:06]: 🎯   - 成功上传:6个, 蒸馏补偿:0个
[INFO][14:41:06]: 🎯 聚合7个客户端: [80, 96, 33, 34, 66, 71, 59]
[INFO][14:41:06]: 🎯   - D_t=4.7363, 队列惩罚=0.0000, 目标值=4.7363
[INFO][14:41:06]: 🎯   - 成功上传:7个, 蒸馏补偿:0个
[INFO][14:41:06]: 🎯 贪心聚合子集选择完成:
[INFO][14:41:06]: 🎯 - 选中客户端: [80]
[INFO][14:41:06]: 🎯 - 顺利上传: 1个, 蒸馏补偿: 0个
[INFO][14:41:06]: 🎯 - 最终目标函数值: 1.9703
[INFO][14:41:06]: 🎯 - 最大延迟D_t: 1.9703
[INFO][14:41:06]: 🎯 贪心选择结果：选中 1 个顺利上传的客户端
[INFO][14:41:06]: 🎯 选中的顺利上传客户端ID: [80]
[INFO][14:41:06]: 🎯 检测到 3 个无法顺利上传的客户端，暂时不做处理
[INFO][14:41:06]: 🎯 无法顺利上传的客户端ID: [42, 5, 49]
[INFO][14:41:06]: 🎯 客户端处理完成:
[INFO][14:41:06]: 🎯 - 选中顺利上传: 1个客户端
[INFO][14:41:06]: 🎯 - 暂不处理蒸馏补偿: 3个客户端
[INFO][14:41:06]: 🎯 聚合开始 - 选中的顺利上传客户端: 1个
[INFO][14:41:06]: 🎯 本轮参与聚合的客户端ID列表: [80]
[INFO][14:41:06]: [Server #29312] Updated weights have been received.
[INFO][14:41:06]: [Server #29312] Aggregating model weight deltas.
[INFO][14:41:06]: [Server #29312] Finished aggregating updated weights.
[INFO][14:41:06]: [Server #29312] Started model testing.
[INFO][14:41:57]: [Trainer.test] 测试完成 - 准确率: 13.74% (1374/10000)
[INFO][14:41:57]: [93m[1m[Server #29312] Global model accuracy: 13.74%
[0m
[INFO][14:41:57]: get_logged_items 被调用
[INFO][14:41:57]: 从updates获取参与客户端: [80]
[INFO][14:41:57]: 客户端 80 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][14:41:57]: 陈旧度统计 - 参与客户端: [80], 陈旧度: [1]
[INFO][14:41:57]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][14:41:57]: 最终logged_items: {'round': 5, 'accuracy': 0.1374, 'accuracy_std': 0, 'elapsed_time': 356.32332968711853, 'processing_time': 0.0006118000019341707, 'comm_time': 0, 'round_time': 136.10872270469554, 'comm_overhead': 2249.9650955200195, 'global_accuracy': 0.1374, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][14:41:57]: [Server #29312] All client reports have been processed.
[INFO][14:41:57]: 🎯 本轮检测到但暂不处理的蒸馏补偿客户端: [42, 5, 49]
[INFO][14:41:57]: [Server #29312] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][14:41:57]: [Server #29312] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][14:41:57]: [93m[1m
[Server #29312] Starting round 6/400.[0m
[INFO][14:41:57]: [Server #29312] Selected clients: [71, 96, 28, 43, 40, 84, 70, 56, 5, 68]
[INFO][14:41:57]: [Server #29312] Selecting client #71 for training.
[INFO][14:41:57]: [Server #29312] Sending the current model to client #71 (simulated).
[INFO][14:41:57]: [Server #29312] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][14:41:57]: [Server #29312] Selecting client #96 for training.
[INFO][14:41:57]: [Server #29312] Sending the current model to client #96 (simulated).
[INFO][14:41:57]: [Server #29312] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][14:41:57]: [Server #29312] Selecting client #28 for training.
[INFO][14:41:57]: [Server #29312] Sending the current model to client #28 (simulated).
[INFO][14:41:57]: [Server #29312] Sending 18.75 MB of payload data to client #28 (simulated).
[INFO][14:41:57]: [Server #29312] Selecting client #43 for training.
[INFO][14:41:57]: [Server #29312] Sending the current model to client #43 (simulated).
[INFO][14:41:57]: [Server #29312] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][14:41:57]: [Server #29312] Selecting client #40 for training.
[INFO][14:41:57]: [Server #29312] Sending the current model to client #40 (simulated).
[INFO][14:41:57]: [Server #29312] Sending 18.75 MB of payload data to client #40 (simulated).
[INFO][14:41:57]: [Server #29312] Selecting client #84 for training.
[INFO][14:41:57]: [Server #29312] Sending the current model to client #84 (simulated).
[INFO][14:41:57]: [Server #29312] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][14:41:58]: [Server #29312] Selecting client #70 for training.
[INFO][14:41:58]: [Server #29312] Sending the current model to client #70 (simulated).
[INFO][14:41:58]: [Server #29312] Sending 18.75 MB of payload data to client #70 (simulated).
[INFO][14:41:58]: [Server #29312] Selecting client #56 for training.
[INFO][14:41:58]: [Server #29312] Sending the current model to client #56 (simulated).
[INFO][14:41:58]: [Server #29312] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][14:41:58]: [Server #29312] Selecting client #5 for training.
[INFO][14:41:58]: [Server #29312] Sending the current model to client #5 (simulated).
[INFO][14:41:58]: [Server #29312] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][14:41:58]: [Server #29312] Selecting client #68 for training.
[INFO][14:41:58]: [Server #29312] Sending the current model to client #68 (simulated).
[INFO][14:41:58]: [Server #29312] Sending 18.75 MB of payload data to client #68 (simulated).
[INFO][14:43:00]: [Server #29312] An existing client just disconnected.
[WARNING][14:43:00]: [Server #29312] Client process #24312 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][14:43:00]: [93m[1m[Server #29312] Closing the server due to a failed client.[0m
[INFO][14:43:00]: [Server #29312] Training concluded.
[INFO][14:43:00]: [Server #29312] Model saved to ./models/refedscafl/cifar10_alpha01/resnet_9.pth.
[INFO][14:43:00]: [Server #29312] Closing the server.
[INFO][14:43:00]: Closing the connection to client #26668.
[INFO][14:43:00]: Closing the connection to client #35524.
[INFO][14:43:00]: Closing the connection to client #41112.
[INFO][14:43:00]: Closing the connection to client #29548.
[INFO][14:43:00]: Closing the connection to client #8424.
[INFO][14:43:00]: Closing the connection to client #27856.
[INFO][14:43:00]: Closing the connection to client #20200.
[INFO][14:43:00]: Closing the connection to client #14484.
[INFO][14:43:00]: Closing the connection to client #19908.
