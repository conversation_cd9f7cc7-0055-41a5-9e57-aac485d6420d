#!/bin/bash

# 检查输出目录
mkdir -p distribution_comparisons

# 比较不同数据集在非IID (α=0.1) 下的分布
echo "比较不同数据集的非IID分布 (α=0.1)..."
python visualize_dataset_distribution.py --dataset MNIST --sampler noniid --alpha 0.1 --num_clients 10
python visualize_dataset_distribution.py --dataset CIFAR10 --sampler noniid --alpha 0.1 --num_clients 10
python visualize_dataset_distribution.py --dataset CIFAR100 --sampler noniid --alpha 0.1 --num_clients 10

# 比较MNIST数据集在不同α值下的分布
echo "比较MNIST数据集在不同α值下的非IID分布..."
python visualize_dataset_distribution.py --dataset MNIST --sampler noniid --alpha 0.1 --num_clients 10
python visualize_dataset_distribution.py --dataset MNIST --sampler noniid --alpha 0.5 --num_clients 10
python visualize_dataset_distribution.py --dataset MNIST --sampler noniid --alpha 1.0 --num_clients 10

# 比较IID和非IID分布
echo "比较IID和非IID分布..."
python visualize_dataset_distribution.py --dataset MNIST --sampler iid --num_clients 10
python visualize_dataset_distribution.py --dataset MNIST --sampler noniid --alpha 0.1 --num_clients 10

# 可视化数据集样本
echo "生成数据集样本可视化..."
python visualize_dataset_distribution.py --dataset MNIST --show_samples
python visualize_dataset_distribution.py --dataset CIFAR10 --show_samples

# 分析结果整合
echo "整合分析结果..."
mkdir -p distribution_comparisons/alpha_comparison
mkdir -p distribution_comparisons/dataset_comparison
mkdir -p distribution_comparisons/iid_vs_noniid

# 复制相关图表和报告到相应目录
cp partition_distribution/MNIST_noniid_alpha0.1_*.png distribution_comparisons/alpha_comparison/
cp partition_distribution/MNIST_noniid_alpha0.5_*.png distribution_comparisons/alpha_comparison/
cp partition_distribution/MNIST_noniid_alpha1.0_*.png distribution_comparisons/alpha_comparison/

cp partition_distribution/MNIST_noniid_alpha0.1_*.png distribution_comparisons/dataset_comparison/
cp partition_distribution/CIFAR10_noniid_alpha0.1_*.png distribution_comparisons/dataset_comparison/
cp partition_distribution/CIFAR100_noniid_alpha0.1_*.png distribution_comparisons/dataset_comparison/

cp partition_distribution/MNIST_iid_*.png distribution_comparisons/iid_vs_noniid/
cp partition_distribution/MNIST_noniid_alpha0.1_*.png distribution_comparisons/iid_vs_noniid/

cp dataset_samples/* distribution_comparisons/

echo "分析完成，结果保存在 distribution_comparisons 目录" 