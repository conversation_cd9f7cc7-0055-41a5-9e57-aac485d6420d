"""
A federated learning client using SCAFFOLD.

Reference:

<PERSON><PERSON><PERSON><PERSON> et al., "SCAFFOLD: Stochastic Controlled Averaging for Federated Learning,"
in Proceedings of the 37th International Conference on Machine Learning (ICML), 2020.

https://arxiv.org/pdf/1910.06378.pdf
"""
from collections import OrderedDict

import copy
import logging
import pickle
import torch
import math
import os
import time
import numpy as np

from plato.config import Config
from plato.trainers import basic


class Trainer(basic.Trainer):
    """The federated learning trainer for the SCAFFOLD client."""
    def __init__(self, model=None, callbacks=None):
        """Initializing the trainer with the provided model."""
        super().__init__(model=model, callbacks=callbacks)

        self.server_control_variate = None
        self.client_control_variate = None
        self.client_control_variate_delta_path = None

        # Save the global model weights for computing new control variate
        # using the Option 2 in the paper
        self.global_model_weights = None

        # Path to the client control variate
        self.client_control_variate_path = None

        self.additional_data = None
        self.param_groups = None
        
        self.client_quality = None
        self.epoch_rate = 1
        self.alpha = 1
        self.epoch_losses = []
        self.final_loss = 0.0

    def get_optimizer(self, model):
        """Gets the parameter groups from the optimizer"""
        optimizer = super().get_optimizer(model)
        # 获得优化的参数
        self.param_groups = optimizer.param_groups
        return optimizer

    def train_run_start(self, config):
        """Initializes the client control variate to 0 if the client
        is participating for the first time.
        """
        self.server_control_variate = self.additional_data
        # client_control_variate 从fedac_client 的config函数中加载
        if self.client_control_variate is None:
            self.client_control_variate = {}
            for variate in self.server_control_variate:
                self.client_control_variate[variate] = torch.zeros(
                    self.server_control_variate[variate].shape
                )
        self.global_model_weights = copy.deepcopy(self.model.state_dict())

    def train_step_end(self, config, batch=None, loss=None):
        # Modifies the weights based on the server and client control variates.
        # 根据论文中的公式12，该本地更新过程相当于先利用SGD方法计算g_i，然后再利用以下代码计算h_i（分开更新）
        for group in self.param_groups:
            learning_rate = -group["lr"]
            # each layer
            counter = 0

            for name in self.server_control_variate:
                if "weight" in name or "bias" in name:
                    server_control_variate = self.server_control_variate[name].to(
                        self.device
                    )
                    param = group["params"][counter]
                    if self.client_control_variate is not None:
                        param.data.add_(
                            torch.sub(
                                server_control_variate,
                                self.client_control_variate[name].to(self.device),
                            ),
                            alpha=learning_rate,
                        )
                    else:
                        param.data.add_(server_control_variate, alpha=learning_rate)
                    counter += 1

    def train_run_end(self, config):
        """Compute deltas of this client's control variate and deltas of the model"""
        # Compute deltas of control variate to be used for the next time that
        # the client is selected
        new_client_control_variate = OrderedDict()
        control_variate_deltas = OrderedDict()
        alpha = Config().trainer.batch_size / (Config().trainer.epochs * Config().data.partition_size)
        if self.client_control_variate is not None:
            for name, previous_weight in self.global_model_weights.items():
                # 公式14
                new_client_control_variate[name] = torch.sub(
                    self.client_control_variate[name].to(device=self.device),
                    self.server_control_variate[name].to(device=self.device),
                ).to(device=self.device)
                new_client_control_variate[name].add_(
                    torch.sub(
                        previous_weight.to(device=self.device),
                        self.model.state_dict()[name],
                    ),
                    alpha=alpha,
                )
                # 公式15
                control_variate_deltas[name] = torch.sub(
                    new_client_control_variate[name],
                    self.client_control_variate[name].to(self.device),
                )
        else:
            for name, previous_weight in self.global_model_weights.items():
                new_client_control_variate[name] = -self.server_control_variate[name]
                new_client_control_variate[name].add_(
                    torch.sub(previous_weight, self.model.state_dict()[name]),
                    alpha=alpha,
                )

                control_variate_deltas[name] = new_client_control_variate[name]

        # Update client control variate
        self.client_control_variate = new_client_control_variate

        # Save client control variate
        logging.info(
            "[Client #%d] Saving the control variate to %s.",
            self.client_id,
            self.client_control_variate_path,
        )
        with open(self.client_control_variate_path, "wb") as path:
            pickle.dump(self.client_control_variate, path)

        logging.info(
            "[Client #%d] Control variate saved to %s.",
            self.client_id,
            self.client_control_variate_path,
        )

        # Save client control variate delta
        logging.info(
            "[Client #%d] Saving the control variate delta to %s.",
            self.client_id,
            self.client_control_variate_delta_path,
        )
        with open(self.client_control_variate_delta_path, "wb") as path:
            pickle.dump(control_variate_deltas, path)

        logging.info(
            "[Client #%d] Control variate delta saved to %s.",
            self.client_id,
            self.client_control_variate_delta_path,
        )

    def set_client_quality(self, quality):
        self.client_quality = quality

    def set_epoch_rate(self, epoch_rate):
        self.epoch_rate = epoch_rate

    def get_epoch_rate(self):
        return self.epoch_rate

    def set_alpha(self, alpha):
        self.alpha = alpha

    def get_alpha(self):
        return self.alpha

    def train_model(self, config, trainset, sampler, **kwargs):
        """The default training loop when a custom training loop is not supplied."""
        batch_size = config["batch_size"]
        self.sampler = sampler
        tic = time.perf_counter()

        self.run_history.reset()
        self.epoch_losses = []  # Reset epoch losses for this training run
        self.final_loss = 0.0

        self.train_run_start(config)
        self.callback_handler.call_event("on_train_run_start", self, config)

        self.train_loader = self.get_train_loader(batch_size, trainset, sampler)

        # Initializing the loss criterion
        self._loss_criterion = self.get_loss_criterion()

        # Initializing the optimizer
        self.optimizer = self.get_optimizer(self.model)
        self.lr_scheduler = self.get_lr_scheduler(config, self.optimizer)
        self.optimizer = self._adjust_lr(config, self.lr_scheduler, self.optimizer)

        self.model.to(self.device)
        self.model.train()

        total_epochs = config["epochs"]

        # 修复：不再随机缩减训练轮数，保持完整的训练
        if not self.client_quality:
            # 保持原始的训练轮数，不进行缩减
            total_epochs_rate = 1.0  # 不缩减
            # total_epochs保持不变
            self.set_epoch_rate(1.0)  # 设置为1.0，表示没有缩减
            self.set_alpha(1.0)  # 设置为1.0，表示正常训练

            if "max_concurrency" in config:
                # 若存在文件"self.client_id_magnified.csv",则存储alpha,beta,process_id
                filename = f"{self.client_id}_magnified.csv"
                self.save_magnified_parameters(filename)

        for self.current_epoch in range(1, total_epochs + 1):
            self._loss_tracker.reset()
            self.train_epoch_start(config)
            self.callback_handler.call_event("on_train_epoch_start", self, config)

            for batch_id, (examples, labels) in enumerate(self.train_loader):
                self.train_step_start(config, batch=batch_id)
                self.callback_handler.call_event(
                    "on_train_step_start", self, config, batch=batch_id
                )

                examples, labels = examples.to(self.device), labels.to(self.device)

                loss = self.perform_forward_and_backward_passes(
                    config, examples, labels
                )

                self.train_step_end(config, batch=batch_id, loss=loss)
                self.callback_handler.call_event(
                    "on_train_step_end", self, config, batch=batch_id, loss=loss
                )

            self.lr_scheduler_step()

            if hasattr(self.optimizer, "params_state_update"):
                self.optimizer.params_state_update()

            # Simulate client's speed
            if (
                    self.client_id != 0
                    and hasattr(Config().clients, "speed_simulation")
                    and Config().clients.speed_simulation
            ):
                self.simulate_sleep_time()

            # Saving the model at the end of this epoch to a file so that
            # it can later be retrieved to respond to server requests
            # in asynchronous mode when the wall clock time is simulated
            if (
                    hasattr(Config().server, "request_update")
                    and Config().server.request_update
            ):
                self.model.cpu()
                training_time = time.perf_counter() - tic
                filename = f"{self.client_id}_{self.current_epoch}_{training_time}.pth"
                self.save_model(filename)
                self.model.to(self.device)

            # Get and save the average loss for this epoch
            epoch_loss = self._loss_tracker.average
            self.run_history.update_metric("train_loss", epoch_loss)

            # Store loss in memory for reporting
            self.epoch_losses.append(epoch_loss)
            # Update final loss with the latest epoch loss
            self.final_loss = epoch_loss

            self.train_epoch_end(config)
            self.callback_handler.call_event("on_train_epoch_end", self, config)

        self.train_run_end(config)
        self.callback_handler.call_event("on_train_run_end", self, config)

    def save_magnified_parameters(self, filename):
        with open(filename, 'a') as f:
            f.write(f"{self.get_alpha()},{self.get_epoch_rate()},{os.getpid()}\n")

    def get_epoch_losses(self):
        """Return the list of loss values for all epochs."""
        return self.epoch_losses

    def get_final_loss(self):
        """Return the final loss value from training."""
        return self.final_loss