{"cells": [{"cell_type": "markdown", "metadata": {"id": "G1RsvvL4z2Op"}, "source": ["# Running Plato in a Terminal with Google Colab and Visual Studio Code\n", "\n", "It is strongly recommended and more convenient to run <PERSON> in a terminal, preferably in Visual Studio Code. This notebook contains step-by-step instructions on how this can be done when Google Colab is used. \n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "AcAKbwcEp3y6"}, "source": ["## 1. Changing your Runtime Type\n", "\n", "If you have subscribed to Google Colab Pro or Pro+, you may select *Runtime > Change runtime type* and then select *High-RAM* for *Runtime shape* and *Background execution* (only available to Colab Pro+)."]}, {"cell_type": "markdown", "metadata": {"id": "kUcTpfnUqktI"}, "source": ["Now you may use the following code to check the GPU type allocated to you in the current session:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "23TOba33L4qf"}, "outputs": [], "source": ["gpu_info = !nvidia-smi\n", "gpu_info = '\\n'.join(gpu_info)\n", "if gpu_info.find('failed') >= 0:\n", "  print('Not connected to a GPU')\n", "else:\n", "  print(gpu_info)"]}, {"cell_type": "markdown", "metadata": {"id": "Jm2ozpI0q3Gr"}, "source": ["You can also use the following code to verify the amount of physical memory available in your active session:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dh4hqs0Dq9Nr"}, "outputs": [], "source": ["from psutil import virtual_memory\n", "ram_gb = virtual_memory().total / 1e9\n", "print('Your runtime has {:.1f} gigabytes of available RAM\\n'.format(ram_gb))\n", "\n", "if ram_gb < 20:\n", "  print('Not using a high-RAM runtime')\n", "else:\n", "  print('You are using a high-RAM runtime!')"]}, {"cell_type": "markdown", "metadata": {"id": "SkkBTKHtuTVy"}, "source": ["## 2. Mounting your Google Drive\n", "\n", "Since Google Colab removes all the files that you may have downloaded or created when you terminal a session, the best option is to use GitHub to store your code, and Google Drive to store your downloaded datasets and anything else needed by your Plato training sessions that you would normally store on your local filesystem.\n", "\n", "You can use the code below to mount your Google Drive, which may contain your downloaded datasets. When you run the code below, you will need to click a link and follow a process that takes a few seconds. When the process is complete, all of your Google Drive files will be available at `/content/drive/MyDrive` on your Colab instance."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "z5gW8LQivFHs"}, "outputs": [], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "markdown", "metadata": {"id": "exlQPjof-lZ4"}, "source": ["## 3. Connecting to the Remote Server\n", "\n", "You are now ready to remotely connect to the Google Colab host in Visual Studio Code. If this is the first time you do it, please download and install [Cloudflared](https://developers.cloudflare.com/argo-tunnel/getting-started/installation). In macOS, for example, the command to install it will be `brew install cloudflare/cloudflare/cloudflared`.\n", "\n", "After installing `cloudflared`, you should add the following entry into `~/.ssh/config`:\n", "\n", "```\n", "Host *.trycloudflare.com\n", "\tHostName %h\n", "\tUser root\n", "\tPort 22\n", "\tProxyCommand PUT_THE_ABSOLUTE_CLOUDFLARED_PATH_HERE access ssh --hostname %h\n", "```\n", "\n", "Where the absolute path to `cloudflared` on macOS, for example, is `/opt/homebrew/bin/cloudflared`. You do not need to perform these steps again later on —— you only need to do them once."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "vEzzDEmu1lk1"}, "outputs": [], "source": ["!pip install colab_ssh --upgrade\n", "\n", "from getpass import getpass\n", "password = getpass('Please enter the login password for accessing the remote server: ')\n", "\n", "from colab_ssh import launch_ssh_cloudflared, init_git_cloudflared\n", "launch_ssh_cloudflared(password=password)\n", "\n", "repository_url=\"https://github.com/TL-System/plato\"\n", "init_git_cloudflared(repository_url)"]}, {"cell_type": "markdown", "metadata": {"id": "MMBgF6Nr16r6"}, "source": ["Now click the \"Open plato\" button, and enter the login password you have just provided. You will now be connected to the remote Colab server in Visual Studio Code. You can open a terminal in Visual Studio Code to access the remote server at this time."]}, {"cell_type": "markdown", "metadata": {"id": "TXmC92xX_BeG"}, "source": ["## 4. Setting Up your Python Environment\n", "\n", "Before running Plato, use the following commands to install Plato and all its dependencies in Visual Studio Code's terminal:\n", "\n", "```shell\n", "$ pip install .\n", "```\n"]}], "metadata": {"accelerator": "GPU", "colab": {"background_execution": "on", "collapsed_sections": ["AcAKbwcEp3y6", "SkkBTKHtuTVy", "exlQPjof-lZ4", "TXmC92xX_BeG"], "machine_shape": "hm", "name": "colab_use_terminal.ipynb", "private_outputs": true, "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}