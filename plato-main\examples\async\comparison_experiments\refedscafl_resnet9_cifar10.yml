# ReFedSCAFL算法 - ResNet9 CIFAR10 对比试验配置
# 基于声誉的联邦脚手架学习与知识蒸馏补偿算法

clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 20

    # Should the clients compute test accuracy locally?
    do_test: true

    # Should the clients compute test accuracy with global model?
    do_global_test: true

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 5

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 5

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8006
    ping_timeout: 36000
    ping_interval: 36000

    # Should we simulate the wall-clock time on the server?
    simulate_wall_time: true

    # Should we operate in sychronous mode?
    synchronous: false

    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 10

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 5

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: true

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/comparison/refedscafl
    model_path: models/comparison/refedscafl

    random_seed: 1

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition
    partition_size: 300

    # IID or non-IID?
    sampler: noniid
    concentration: 0.1

    # The size of the testset on the server
    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

    # Get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 400

    # The maximum number of clients running concurrently
    max_concurrency: 10

    # The target accuracy
    target_accuracy: 1

    # The machine learning model
    model_name: resnet_9

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 50
    optimizer: SGD
    lr_scheduler: LambdaLR

algorithm:
    # Aggregation algorithm
    type: fedavg
    lamda: 1.0

    # ReFedSCAFL parameters
    buffer_pool_size: 20
    greedy_selection_size: 5
    tau_max: 5
    
    # Basic weights
    success_weight: 0.8
    distill_weight: 0.2
    rho: 0.9
    communication_threshold: 2.0

    # Enhanced adaptive weight adjustment parameters
    enable_adaptive_weights: true
    weight_adaptation_window: 5
    weight_adaptation_threshold: 0.02
    weight_adaptation_step: 0.05
    min_weight_ratio: 0.1

    # Model consistency evaluation parameters
    enable_consistency_factor: true
    consistency_gradient_weight: 0.4
    consistency_magnitude_weight: 0.3
    consistency_distribution_weight: 0.3
    consistency_variance_penalty: 0.2

    # Knowledge distillation parameters
    enable_knowledge_distillation: true
    distillation_temperature: 3.0
    distillation_alpha: 0.7

    # SCAFL parameters
    use_pure_scafl_selection: true
    V: 1.0

parameters:
    model:
        num_classes: 10
        in_channels: 3
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001
    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    result_path: results/comparison/refedscafl

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std
