clients:

    # The total number of clients
    total_clients: 10

    # The number of clients selected in each round
    per_round: 2

    # Should the clients compute test accuracy locally?
    do_test: true

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8009
    do_test: false
    random_seed: 1

    # Should we simulate the wall-clock time on the server? Useful if max_concurrency is specified
    simulate_wall_time: true

data:
    # cifar10 non-iid distribution
    !include cifar10_ssl_noniid.yml

trainer:
    type: self_supervised_learning
   
    # The maximum number of training rounds
    rounds: 2

    # The maximum number of clients running concurrently
    max_concurrency: 2

    # The target accuracy
    target_accuracy: 0.94

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 128
    optimizer: SGD
    lr_scheduler: MultiStepLR
    loss_criterion: NTXentLoss

    # The machine learning model
    model_name: MoCoV2
    encoder_name: resnet_18

    projection_hidden_dim: 1024
    projection_out_dim: 256


algorithm:
    # Aggregation algorithm
    type: fedavg_personalized
    #  - STL10 (Image size=96)
    #  - CIFAR10 (Image size=32)
    #  - MNIST (Image size=28)
    data_transforms:
        train_transform:
            name: MoCoV2
            parameters:
                input_size: 32

    personalization:

        !include ssl_personalization.yml


parameters:
    # ResNet-18 ssl training params
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001

    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

    loss_criterion:
        temperature: 0.2
        memory_bank_size: 4096

    model:
        num_classes: 10

    personalization:
        !include resnet18_pers_params.yml

results:
    # Write the following parameter(s) into a CSV
    types: round, accuracy, accuracy_std, elapsed_time, round_time, comm_overhead
    result_path: results/pfl-mocov2

    

