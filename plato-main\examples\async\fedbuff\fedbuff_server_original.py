"""
A federated learning server using FedBuff (Original Version - No Network Simulation).

Reference:

<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al., "Federated Learning with Buffered Asynchronous Aggregation,
" in Proc. International Conference on Artificial Intelligence and Statistics (AISTATS 2022).

https://proceedings.mlr.press/v151/nguyen22b/nguyen22b.pdf
"""
import asyncio
import os
import logging
from datetime import datetime

from plato.config import Config
from plato.servers import fedavg
from plato.utils import csv_processor


class Server(fedavg.Server):
    """A federated learning server using the FedBuff algorithm (Original Version)."""

    def __init__(
        self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None
    ):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )

        # 设置简单的结果文件命名
        self._setup_result_file()

    def _setup_result_file(self):
        """设置结果文件"""
        try:
            # 生成简单的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")
            custom_filename = f"fedbuff_original_MNIST_{timestamp}.csv"

            # 获取结果路径
            result_path = Config().params["result_path"]

            # 确保目录存在
            os.makedirs(result_path, exist_ok=True)

            custom_result_file = os.path.join(result_path, custom_filename)

            # 保存自定义文件路径
            self.custom_result_file = custom_result_file

            # 初始化CSV文件
            recorded_items = Config().params["result_types"]
            recorded_items_list = [x.strip() for x in recorded_items.split(",")]

            csv_processor.initialize_csv(
                custom_result_file, recorded_items_list, result_path
            )

            logging.info(f"📊 原始版本结果文件: {custom_filename}")
            logging.info(f"📁 完整路径: {custom_result_file}")

        except Exception as e:
            logging.warning(f"设置结果文件失败，使用默认命名: {e}")
            self.custom_result_file = None

    async def aggregate_deltas(self, updates, deltas_received):
        """Aggregate weight updates from the clients using federated averaging."""
        # Extract the total number of samples
        total_updates = len(updates)

        # Perform weighted averaging
        avg_update = {
            name: self.trainer.zeros(delta.shape)
            for name, delta in deltas_received[0].items()
        }

        for update in deltas_received:
            for name, delta in update.items():
                # Use weighted average by the number of samples
                avg_update[name] += delta * (1 / total_updates)

            # Yield to other tasks in the server
            await asyncio.sleep(0)

        return avg_update

    def clients_processed(self):
        """重写clients_processed方法以记录结果"""
        # 调用父类方法
        super().clients_processed()

        # 如果有自定义文件，写入结果
        if hasattr(self, 'custom_result_file') and self.custom_result_file:
            try:
                # 获取要记录的项目
                logged_items = self.get_logged_items()

                # 按照配置文件中定义的顺序准备数据行
                recorded_items = Config().params["result_types"]
                recorded_items_list = [x.strip() for x in recorded_items.split(",")]

                new_row = []
                for item in recorded_items_list:
                    if item in logged_items:
                        new_row.append(logged_items[item])
                    else:
                        new_row.append(0.0)  # 默认值

                # 写入自定义CSV文件
                csv_processor.write_csv(self.custom_result_file, new_row)

            except Exception as e:
                logging.error(f"写入结果文件失败: {e}")

    def get_logged_items(self) -> dict:
        """获取要记录的项目"""
        logged_items = {
            "round": self.current_round,
            "accuracy": getattr(self, 'accuracy', 0.0),
            "elapsed_time": self.wall_time - self.initial_wall_time,
        }

        # 只有在存在时才添加这些字段
        if hasattr(self, 'global_accuracy'):
            logged_items["global_accuracy"] = self.global_accuracy
        if hasattr(self, 'global_accuracy_std'):
            logged_items["global_accuracy_std"] = self.global_accuracy_std

        return logged_items
