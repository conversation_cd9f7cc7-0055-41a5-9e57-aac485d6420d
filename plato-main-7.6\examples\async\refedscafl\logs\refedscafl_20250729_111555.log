[INFO][11:15:55]: 日志系统已初始化
[INFO][11:15:55]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_111555.log
[INFO][11:15:55]: 日志级别: INFO
[WARNING][11:15:55]: 无法获取系统信息: No module named 'psutil'
[INFO][11:15:55]: 🚀 ReFedScaFL 训练开始
[INFO][11:15:55]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][11:15:55]: 开始时间: 2025-07-29 11:15:55
[INFO][11:15:55]: [Client None] 基础初始化完成
[INFO][11:15:55]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][11:15:55]: 创建并缓存共享模型
[INFO][11:15:55]: [93m[1m[4824] Logging runtime results to: ./results/refedscafl/cifar10_alpha01/4824.csv.[0m
[INFO][11:15:55]: [Server #4824] Started training on 100 clients with 20 per round.
[INFO][11:15:55]: 服务器参数配置完成：
[INFO][11:15:55]: - 客户端数量: total=100, per_round=20
[INFO][11:15:55]: - 权重参数: success=0.8, distill=0.2
[INFO][11:15:55]: - SCAFL参数: V=1.0, tau_max=5
[INFO][11:15:55]: 从共享资源模型提取并缓存全局权重
[INFO][11:15:55]: [Server #4824] Configuring the server...
[INFO][11:15:55]: Training: 400 rounds or accuracy above 100.0%

[INFO][11:15:55]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][11:15:55]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][11:15:55]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][11:15:55]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][11:15:55]: Algorithm: fedavg
[INFO][11:15:55]: Data source: CIFAR10
[INFO][11:15:56]: Starting client #1's process.
[INFO][11:15:56]: Starting client #2's process.
[INFO][11:15:56]: Starting client #3's process.
[INFO][11:15:56]: Starting client #4's process.
[INFO][11:15:56]: Starting client #5's process.
[INFO][11:15:56]: Starting client #6's process.
[INFO][11:15:56]: Starting client #7's process.
[INFO][11:15:56]: Starting client #8's process.
[INFO][11:15:56]: Starting client #9's process.
[INFO][11:15:56]: Starting client #10's process.
[INFO][11:15:56]: Setting the random seed for selecting clients: 1
[INFO][11:15:56]: Starting a server at address 127.0.0.1 and port 8095.
[INFO][11:16:13]: [Server #4824] A new client just connected.
[INFO][11:16:13]: [Server #4824] New client with id #3 arrived.
[INFO][11:16:13]: [Server #4824] Client process #25200 registered.
[INFO][11:16:13]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][11:16:13]: [Server #4824] A new client just connected.
[INFO][11:16:13]: [Server #4824] New client with id #7 arrived.
[INFO][11:16:13]: [Server #4824] Client process #36604 registered.
[INFO][11:16:13]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][11:16:14]: [Server #4824] A new client just connected.
[INFO][11:16:14]: [Server #4824] New client with id #4 arrived.
[INFO][11:16:14]: [Server #4824] Client process #10044 registered.
[INFO][11:16:14]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][11:16:14]: [Server #4824] A new client just connected.
[INFO][11:16:14]: [Server #4824] New client with id #9 arrived.
[INFO][11:16:14]: [Server #4824] Client process #11184 registered.
[INFO][11:16:14]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][11:16:15]: [Server #4824] A new client just connected.
[INFO][11:16:15]: [Server #4824] New client with id #5 arrived.
[INFO][11:16:15]: [Server #4824] Client process #5820 registered.
[INFO][11:16:15]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][11:16:15]: [Server #4824] A new client just connected.
[INFO][11:16:15]: [Server #4824] A new client just connected.
[INFO][11:16:15]: [Server #4824] New client with id #8 arrived.
[INFO][11:16:15]: [Server #4824] Client process #24176 registered.
[INFO][11:16:15]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][11:16:15]: [Server #4824] New client with id #2 arrived.
[INFO][11:16:15]: [Server #4824] Client process #26244 registered.
[INFO][11:16:15]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][11:16:15]: [Server #4824] A new client just connected.
[INFO][11:16:15]: [Server #4824] A new client just connected.
[INFO][11:16:15]: [Server #4824] A new client just connected.
[INFO][11:16:15]: [Server #4824] New client with id #1 arrived.
[INFO][11:16:15]: [Server #4824] Client process #26032 registered.
[INFO][11:16:15]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][11:16:15]: [Server #4824] New client with id #6 arrived.
[INFO][11:16:15]: [Server #4824] Client process #31740 registered.
[INFO][11:16:15]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][11:16:15]: [Server #4824] New client with id #10 arrived.
[INFO][11:16:15]: [Server #4824] Client process #31200 registered.
[INFO][11:16:15]: [Server #4824] Starting training.
[INFO][11:16:15]: [93m[1m
[Server #4824] Starting round 1/400.[0m
[INFO][11:16:15]: [Server #4824] Selected clients: [18, 73, 98, 9, 33, 16, 64, 58, 61, 84, 49, 27, 13, 63, 4, 50, 56, 78, 99, 1]
[INFO][11:16:15]: [Server #4824] Selecting client #18 for training.
[INFO][11:16:15]: [Server #4824] Sending the current model to client #18 (simulated).
[INFO][11:16:15]: [Server #4824] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][11:16:15]: [Server #4824] Selecting client #73 for training.
[INFO][11:16:15]: [Server #4824] Sending the current model to client #73 (simulated).
[INFO][11:16:15]: [Server #4824] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][11:16:15]: [Server #4824] Selecting client #98 for training.
[INFO][11:16:15]: [Server #4824] Sending the current model to client #98 (simulated).
[INFO][11:16:15]: [Server #4824] Sending 18.75 MB of payload data to client #98 (simulated).
[INFO][11:16:15]: [Server #4824] Selecting client #9 for training.
[INFO][11:16:15]: [Server #4824] Sending the current model to client #9 (simulated).
[INFO][11:16:15]: [Server #4824] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][11:16:15]: [Server #4824] Selecting client #33 for training.
[INFO][11:16:15]: [Server #4824] Sending the current model to client #33 (simulated).
[INFO][11:16:15]: [Server #4824] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][11:16:15]: [Server #4824] Selecting client #16 for training.
[INFO][11:16:15]: [Server #4824] Sending the current model to client #16 (simulated).
[INFO][11:16:15]: [Server #4824] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][11:16:15]: [Server #4824] Selecting client #64 for training.
[INFO][11:16:15]: [Server #4824] Sending the current model to client #64 (simulated).
[INFO][11:16:16]: [Server #4824] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][11:16:16]: [Server #4824] Selecting client #58 for training.
[INFO][11:16:16]: [Server #4824] Sending the current model to client #58 (simulated).
[INFO][11:16:16]: [Server #4824] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][11:16:16]: [Server #4824] Selecting client #61 for training.
[INFO][11:16:16]: [Server #4824] Sending the current model to client #61 (simulated).
[INFO][11:16:16]: [Server #4824] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][11:16:16]: [Server #4824] Selecting client #84 for training.
[INFO][11:16:16]: [Server #4824] Sending the current model to client #84 (simulated).
[INFO][11:16:16]: [Server #4824] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][11:16:16]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][11:20:12]: [Server #4824] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][11:20:12]: [Server #4824] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][11:20:13]: [Server #4824] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][11:20:13]: [Server #4824] Received 18.75 MB of payload data from client #98 (simulated).
[INFO][11:20:13]: [Server #4824] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][11:20:15]: [Server #4824] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][11:20:15]: [Server #4824] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][11:20:15]: [Server #4824] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][11:20:16]: [Server #4824] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][11:20:16]: [Server #4824] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][11:20:16]: [Server #4824] Selecting client #49 for training.
[INFO][11:20:16]: [Server #4824] Sending the current model to client #49 (simulated).
[INFO][11:20:16]: [Server #4824] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][11:20:16]: [Server #4824] Selecting client #27 for training.
[INFO][11:20:16]: [Server #4824] Sending the current model to client #27 (simulated).
[INFO][11:20:16]: [Server #4824] Sending 18.75 MB of payload data to client #27 (simulated).
[INFO][11:20:16]: [Server #4824] Selecting client #13 for training.
[INFO][11:20:16]: [Server #4824] Sending the current model to client #13 (simulated).
[INFO][11:20:19]: [Server #4824] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][11:20:19]: [Server #4824] Selecting client #63 for training.
[INFO][11:20:19]: [Server #4824] Sending the current model to client #63 (simulated).
[INFO][11:20:19]: [Server #4824] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][11:20:19]: [Server #4824] Selecting client #4 for training.
[INFO][11:20:19]: [Server #4824] Sending the current model to client #4 (simulated).
[INFO][11:20:20]: [Server #4824] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][11:20:20]: [Server #4824] Selecting client #50 for training.
[INFO][11:20:20]: [Server #4824] Sending the current model to client #50 (simulated).
[INFO][11:20:20]: [Server #4824] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][11:20:20]: [Server #4824] Selecting client #56 for training.
[INFO][11:20:20]: [Server #4824] Sending the current model to client #56 (simulated).
[INFO][11:20:20]: [Server #4824] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][11:20:20]: [Server #4824] Selecting client #78 for training.
[INFO][11:20:20]: [Server #4824] Sending the current model to client #78 (simulated).
[INFO][11:20:20]: [Server #4824] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][11:20:20]: [Server #4824] Selecting client #99 for training.
[INFO][11:20:20]: [Server #4824] Sending the current model to client #99 (simulated).
[INFO][11:20:20]: [Server #4824] Sending 18.75 MB of payload data to client #99 (simulated).
[INFO][11:20:20]: [Server #4824] Selecting client #1 for training.
[INFO][11:20:20]: [Server #4824] Sending the current model to client #1 (simulated).
[INFO][11:20:20]: [Server #4824] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][11:26:15]: [Server #4824] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][11:26:21]: [Server #4824] Received 18.75 MB of payload data from client #99 (simulated).
[INFO][11:26:23]: [Server #4824] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][11:26:24]: [Server #4824] Received 18.75 MB of payload data from client #78 (simulated).
[INFO][11:26:24]: [Server #4824] Received 18.75 MB of payload data from client #27 (simulated).
[INFO][11:26:24]: [Server #4824] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][11:26:24]: [Server #4824] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][11:26:25]: [Server #4824] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][11:26:25]: [Server #4824] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][11:26:25]: [Server #4824] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][11:26:25]: [Server #4824] Adding client #33 to the list of clients for aggregation.
[INFO][11:26:25]: [Server #4824] Adding client #84 to the list of clients for aggregation.
[INFO][11:26:25]: [Server #4824] Adding client #58 to the list of clients for aggregation.
[INFO][11:26:25]: [Server #4824] Adding client #18 to the list of clients for aggregation.
[INFO][11:26:25]: [Server #4824] Adding client #9 to the list of clients for aggregation.
[INFO][11:26:25]: [Server #4824] Adding client #73 to the list of clients for aggregation.
[INFO][11:26:25]: [Server #4824] Adding client #64 to the list of clients for aggregation.
[INFO][11:26:25]: [Server #4824] Adding client #98 to the list of clients for aggregation.
[INFO][11:26:25]: [Server #4824] Adding client #61 to the list of clients for aggregation.
[INFO][11:26:25]: [Server #4824] Adding client #16 to the list of clients for aggregation.
[INFO][11:26:25]: [Server #4824] Aggregating 10 clients in total.
[INFO][11:26:25]: [Server #4824] Updated weights have been received.
[INFO][11:26:25]: [Server #4824] Aggregating model weight deltas.
[INFO][11:26:25]: [Server #4824] Finished aggregating updated weights.
[INFO][11:26:25]: [Server #4824] Started model testing.
[INFO][11:26:39]: [Trainer.test] 测试完成 - 准确率: 11.06% (1106/10000)
[INFO][11:26:39]: [93m[1m[Server #4824] Global model accuracy: 11.06%
[0m
[INFO][11:26:39]: get_logged_items 被调用
[INFO][11:26:39]: 从updates获取参与客户端: [33, 84, 58, 18, 9, 73, 64, 98, 61, 16]
[INFO][11:26:39]: 客户端 33 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][11:26:39]: 客户端 84 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][11:26:39]: 客户端 58 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][11:26:39]: 客户端 18 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][11:26:39]: 客户端 9 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][11:26:39]: 客户端 73 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][11:26:39]: 客户端 64 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][11:26:39]: 客户端 98 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][11:26:39]: 客户端 61 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][11:26:39]: 客户端 16 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][11:26:39]: 陈旧度统计 - 参与客户端: [33, 84, 58, 18, 9, 73, 64, 98, 61, 16], 陈旧度: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
[INFO][11:26:39]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][11:26:39]: 最终logged_items: {'round': 1, 'accuracy': 0.1106, 'accuracy_std': 0, 'elapsed_time': 60.75521802902222, 'processing_time': 0.17581089999999833, 'comm_time': 0, 'round_time': 60.75521811326297, 'comm_overhead': 749.9883651733398, 'global_accuracy': 0.1106, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][11:26:39]: [Server #4824] All client reports have been processed.
[INFO][11:26:39]: [Server #4824] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][11:26:39]: [Server #4824] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][11:26:39]: [93m[1m
[Server #4824] Starting round 2/400.[0m
[INFO][11:26:39]: [Server #4824] Selected clients: [100, 66, 39, 34, 85, 17, 45, 6, 5, 93]
[INFO][11:26:39]: [Server #4824] Selecting client #100 for training.
[INFO][11:26:39]: [Server #4824] Sending the current model to client #100 (simulated).
[INFO][11:26:39]: [Server #4824] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][11:26:39]: [Server #4824] Selecting client #66 for training.
[INFO][11:26:39]: [Server #4824] Sending the current model to client #66 (simulated).
[INFO][11:26:39]: [Server #4824] Sending 18.75 MB of payload data to client #66 (simulated).
[INFO][11:26:39]: [Server #4824] Selecting client #39 for training.
[INFO][11:26:39]: [Server #4824] Sending the current model to client #39 (simulated).
[INFO][11:26:39]: [Server #4824] Sending 18.75 MB of payload data to client #39 (simulated).
[INFO][11:26:39]: [Server #4824] Selecting client #34 for training.
[INFO][11:26:39]: [Server #4824] Sending the current model to client #34 (simulated).
[INFO][11:26:39]: [Server #4824] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][11:26:39]: [Server #4824] Selecting client #85 for training.
[INFO][11:26:39]: [Server #4824] Sending the current model to client #85 (simulated).
[INFO][11:26:39]: [Server #4824] Sending 18.75 MB of payload data to client #85 (simulated).
[INFO][11:26:39]: [Server #4824] Selecting client #17 for training.
[INFO][11:26:39]: [Server #4824] Sending the current model to client #17 (simulated).
[INFO][11:26:39]: [Server #4824] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][11:26:39]: [Server #4824] Selecting client #45 for training.
[INFO][11:26:39]: [Server #4824] Sending the current model to client #45 (simulated).
[INFO][11:26:39]: [Server #4824] Sending 18.75 MB of payload data to client #45 (simulated).
[INFO][11:26:39]: [Server #4824] Selecting client #6 for training.
[INFO][11:26:39]: [Server #4824] Sending the current model to client #6 (simulated).
[INFO][11:26:40]: [Server #4824] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][11:26:40]: [Server #4824] Selecting client #5 for training.
[INFO][11:26:40]: [Server #4824] Sending the current model to client #5 (simulated).
[INFO][11:26:40]: [Server #4824] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][11:26:40]: [Server #4824] Selecting client #93 for training.
[INFO][11:26:40]: [Server #4824] Sending the current model to client #93 (simulated).
[INFO][11:26:41]: [Server #4824] Sending 18.75 MB of payload data to client #93 (simulated).
[INFO][11:30:41]: [Server #4824] Received 18.75 MB of payload data from client #100 (simulated).
[INFO][11:30:45]: [Server #4824] Received 18.75 MB of payload data from client #66 (simulated).
[INFO][11:30:47]: [Server #4824] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][11:30:48]: [Server #4824] Received 18.75 MB of payload data from client #85 (simulated).
[INFO][11:30:48]: [Server #4824] Received 18.75 MB of payload data from client #45 (simulated).
[INFO][11:30:48]: [Server #4824] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][11:30:49]: [Server #4824] Received 18.75 MB of payload data from client #39 (simulated).
[INFO][11:30:49]: [Server #4824] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][11:30:49]: [Server #4824] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][11:30:49]: [Server #4824] Received 18.75 MB of payload data from client #93 (simulated).
[INFO][11:30:49]: [Server #4824] Adding client #49 to the list of clients for aggregation.
[INFO][11:30:49]: [Server #4824] Adding client #4 to the list of clients for aggregation.
[INFO][11:30:49]: [Server #4824] Adding client #99 to the list of clients for aggregation.
[INFO][11:30:49]: [Server #4824] Adding client #1 to the list of clients for aggregation.
[INFO][11:30:49]: [Server #4824] Adding client #50 to the list of clients for aggregation.
[INFO][11:30:49]: [Server #4824] Adding client #78 to the list of clients for aggregation.
[INFO][11:30:49]: [Server #4824] Adding client #63 to the list of clients for aggregation.
[INFO][11:30:49]: [Server #4824] Adding client #13 to the list of clients for aggregation.
[INFO][11:30:49]: [Server #4824] Adding client #27 to the list of clients for aggregation.
[INFO][11:30:49]: [Server #4824] Adding client #56 to the list of clients for aggregation.
[INFO][11:30:49]: [Server #4824] Aggregating 10 clients in total.
[INFO][11:30:49]: [Server #4824] Updated weights have been received.
[INFO][11:30:49]: [Server #4824] Aggregating model weight deltas.
[INFO][11:30:49]: [Server #4824] Finished aggregating updated weights.
[INFO][11:30:49]: [Server #4824] Started model testing.
[INFO][11:31:02]: [Trainer.test] 测试完成 - 准确率: 17.14% (1714/10000)
[INFO][11:31:02]: [93m[1m[Server #4824] Global model accuracy: 17.14%
[0m
[INFO][11:31:02]: get_logged_items 被调用
[INFO][11:31:02]: 从updates获取参与客户端: [49, 4, 99, 1, 50, 78, 63, 13, 27, 56]
[INFO][11:31:02]: 客户端 49 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][11:31:02]: 客户端 4 陈旧度: 2 (当前轮次:2, 上次参与:0)
[INFO][11:31:02]: 客户端 99 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][11:31:02]: 客户端 1 陈旧度: 2 (当前轮次:2, 上次参与:0)
[INFO][11:31:02]: 客户端 50 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][11:31:02]: 客户端 78 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][11:31:02]: 客户端 63 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][11:31:02]: 客户端 13 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][11:31:02]: 客户端 27 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][11:31:02]: 客户端 56 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][11:31:02]: 陈旧度统计 - 参与客户端: [49, 4, 99, 1, 50, 78, 63, 13, 27, 56], 陈旧度: [1, 2, 1, 2, 1, 1, 1, 1, 1, 1]
[INFO][11:31:02]: 平均陈旧度: 1.2, 最大: 2, 最小: 1
[INFO][11:31:02]: 最终logged_items: {'round': 2, 'accuracy': 0.1714, 'accuracy_std': 0, 'elapsed_time': 147.74115538597107, 'processing_time': 0.000567799999998897, 'comm_time': 0, 'round_time': 147.74115533141173, 'comm_overhead': 1124.9825477600098, 'global_accuracy': 0.1714, 'avg_staleness': 1.2, 'max_staleness': 2, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][11:31:02]: [Server #4824] All client reports have been processed.
[INFO][11:31:02]: [Server #4824] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][11:31:02]: [Server #4824] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][11:31:02]: [93m[1m
[Server #4824] Starting round 3/400.[0m
[INFO][11:31:02]: [Server #4824] Selected clients: [77, 2, 55, 97, 31, 61, 4, 75, 32, 63]
[INFO][11:31:02]: [Server #4824] Selecting client #77 for training.
[INFO][11:31:02]: [Server #4824] Sending the current model to client #77 (simulated).
[INFO][11:31:02]: [Server #4824] Sending 18.75 MB of payload data to client #77 (simulated).
[INFO][11:31:02]: [Server #4824] Selecting client #2 for training.
[INFO][11:31:02]: [Server #4824] Sending the current model to client #2 (simulated).
[INFO][11:31:02]: [Server #4824] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][11:31:02]: [Server #4824] Selecting client #55 for training.
[INFO][11:31:02]: [Server #4824] Sending the current model to client #55 (simulated).
[INFO][11:31:02]: [Server #4824] Sending 18.75 MB of payload data to client #55 (simulated).
[INFO][11:31:02]: [Server #4824] Selecting client #97 for training.
[INFO][11:31:02]: [Server #4824] Sending the current model to client #97 (simulated).
[INFO][11:31:02]: [Server #4824] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][11:31:02]: [Server #4824] Selecting client #31 for training.
[INFO][11:31:02]: [Server #4824] Sending the current model to client #31 (simulated).
[INFO][11:31:02]: [Server #4824] Sending 18.75 MB of payload data to client #31 (simulated).
[INFO][11:31:02]: [Server #4824] Selecting client #61 for training.
[INFO][11:31:02]: [Server #4824] Sending the current model to client #61 (simulated).
[INFO][11:31:02]: [Server #4824] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][11:31:02]: [Server #4824] Selecting client #4 for training.
[INFO][11:31:02]: [Server #4824] Sending the current model to client #4 (simulated).
[INFO][11:31:03]: [Server #4824] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][11:31:03]: [Server #4824] Selecting client #75 for training.
[INFO][11:31:03]: [Server #4824] Sending the current model to client #75 (simulated).
[INFO][11:31:03]: [Server #4824] Sending 18.75 MB of payload data to client #75 (simulated).
[INFO][11:31:03]: [Server #4824] Selecting client #32 for training.
[INFO][11:31:03]: [Server #4824] Sending the current model to client #32 (simulated).
[INFO][11:31:04]: [Server #4824] Sending 18.75 MB of payload data to client #32 (simulated).
[INFO][11:31:04]: [Server #4824] Selecting client #63 for training.
[INFO][11:31:04]: [Server #4824] Sending the current model to client #63 (simulated).
[INFO][11:31:05]: [Server #4824] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][11:34:22]: [Server #4824] Received 18.75 MB of payload data from client #77 (simulated).
[INFO][11:34:23]: [Server #4824] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][11:34:24]: [Server #4824] Received 18.75 MB of payload data from client #97 (simulated).
[INFO][11:34:25]: [Server #4824] Received 18.75 MB of payload data from client #31 (simulated).
[INFO][11:34:25]: [Server #4824] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][11:34:26]: [Server #4824] Received 18.75 MB of payload data from client #55 (simulated).
[INFO][11:34:26]: [Server #4824] Received 18.75 MB of payload data from client #75 (simulated).
[INFO][11:34:26]: [Server #4824] Received 18.75 MB of payload data from client #32 (simulated).
[INFO][11:34:26]: [Server #4824] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][11:34:26]: [Server #4824] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][11:34:26]: [Server #4824] Adding client #100 to the list of clients for aggregation.
[INFO][11:34:26]: [Server #4824] Adding client #66 to the list of clients for aggregation.
[INFO][11:34:26]: [Server #4824] Adding client #17 to the list of clients for aggregation.
[INFO][11:34:26]: [Server #4824] Adding client #5 to the list of clients for aggregation.
[INFO][11:34:26]: [Server #4824] Adding client #45 to the list of clients for aggregation.
[INFO][11:34:26]: [Server #4824] Adding client #39 to the list of clients for aggregation.
[INFO][11:34:26]: [Server #4824] Adding client #85 to the list of clients for aggregation.
[INFO][11:34:26]: [Server #4824] Adding client #6 to the list of clients for aggregation.
[INFO][11:34:26]: [Server #4824] Adding client #93 to the list of clients for aggregation.
[INFO][11:34:26]: [Server #4824] Adding client #34 to the list of clients for aggregation.
[INFO][11:34:26]: [Server #4824] Aggregating 10 clients in total.
[INFO][11:34:26]: [Server #4824] Updated weights have been received.
[INFO][11:34:26]: [Server #4824] Aggregating model weight deltas.
[INFO][11:34:26]: [Server #4824] Finished aggregating updated weights.
[INFO][11:34:26]: [Server #4824] Started model testing.
[INFO][11:34:39]: [Trainer.test] 测试完成 - 准确率: 19.40% (1940/10000)
[INFO][11:34:39]: [93m[1m[Server #4824] Global model accuracy: 19.40%
[0m
[INFO][11:34:39]: get_logged_items 被调用
[INFO][11:34:39]: 从updates获取参与客户端: [100, 66, 17, 5, 45, 39, 85, 6, 93, 34]
[INFO][11:34:39]: 客户端 100 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][11:34:39]: 客户端 66 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][11:34:39]: 客户端 17 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][11:34:39]: 客户端 5 陈旧度: 3 (当前轮次:3, 上次参与:0)
[INFO][11:34:39]: 客户端 45 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][11:34:39]: 客户端 39 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][11:34:39]: 客户端 85 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][11:34:39]: 客户端 6 陈旧度: 3 (当前轮次:3, 上次参与:0)
[INFO][11:34:39]: 客户端 93 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][11:34:39]: 客户端 34 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][11:34:39]: 陈旧度统计 - 参与客户端: [100, 66, 17, 5, 45, 39, 85, 6, 93, 34], 陈旧度: [1, 1, 1, 3, 1, 1, 1, 3, 1, 1]
[INFO][11:34:39]: 平均陈旧度: 1.4, 最大: 3, 最小: 1
[INFO][11:34:39]: 最终logged_items: {'round': 3, 'accuracy': 0.194, 'accuracy_std': 0, 'elapsed_time': 185.0244162082672, 'processing_time': 0.0012940000000298824, 'comm_time': 0, 'round_time': 124.26919821554566, 'comm_overhead': 1499.9767303466797, 'global_accuracy': 0.194, 'avg_staleness': 1.4, 'max_staleness': 3, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][11:34:39]: [Server #4824] All client reports have been processed.
[INFO][11:34:39]: [Server #4824] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][11:34:39]: [Server #4824] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][11:34:39]: [93m[1m
[Server #4824] Starting round 4/400.[0m
[INFO][11:34:39]: [Server #4824] Selected clients: [71, 80, 34, 49, 96, 33, 66, 42, 5, 59]
[INFO][11:34:39]: [Server #4824] Selecting client #71 for training.
[INFO][11:34:39]: [Server #4824] Sending the current model to client #71 (simulated).
[INFO][11:34:39]: [Server #4824] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][11:34:39]: [Server #4824] Selecting client #80 for training.
[INFO][11:34:39]: [Server #4824] Sending the current model to client #80 (simulated).
[INFO][11:34:39]: [Server #4824] Sending 18.75 MB of payload data to client #80 (simulated).
[INFO][11:34:39]: [Server #4824] Selecting client #34 for training.
[INFO][11:34:39]: [Server #4824] Sending the current model to client #34 (simulated).
[INFO][11:34:40]: [Server #4824] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][11:34:40]: [Server #4824] Selecting client #49 for training.
[INFO][11:34:40]: [Server #4824] Sending the current model to client #49 (simulated).
[INFO][11:34:40]: [Server #4824] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][11:34:40]: [Server #4824] Selecting client #96 for training.
[INFO][11:34:40]: [Server #4824] Sending the current model to client #96 (simulated).
[INFO][11:34:40]: [Server #4824] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][11:34:40]: [Server #4824] Selecting client #33 for training.
[INFO][11:34:40]: [Server #4824] Sending the current model to client #33 (simulated).
[INFO][11:34:40]: [Server #4824] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][11:34:40]: [Server #4824] Selecting client #66 for training.
[INFO][11:34:40]: [Server #4824] Sending the current model to client #66 (simulated).
[INFO][11:34:40]: [Server #4824] Sending 18.75 MB of payload data to client #66 (simulated).
[INFO][11:34:40]: [Server #4824] Selecting client #42 for training.
[INFO][11:34:40]: [Server #4824] Sending the current model to client #42 (simulated).
[INFO][11:34:41]: [Server #4824] Sending 18.75 MB of payload data to client #42 (simulated).
[INFO][11:34:41]: [Server #4824] Selecting client #5 for training.
[INFO][11:34:41]: [Server #4824] Sending the current model to client #5 (simulated).
[INFO][11:34:41]: [Server #4824] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][11:34:41]: [Server #4824] Selecting client #59 for training.
[INFO][11:34:42]: [Server #4824] Sending the current model to client #59 (simulated).
[INFO][11:34:42]: [Server #4824] Sending 18.75 MB of payload data to client #59 (simulated).
[INFO][11:38:28]: [Server #4824] Received 18.75 MB of payload data from client #80 (simulated).
[INFO][11:38:29]: [Server #4824] Received 18.75 MB of payload data from client #71 (simulated).
[INFO][11:38:31]: [Server #4824] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][11:38:32]: [Server #4824] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][11:38:33]: [Server #4824] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][11:38:33]: [Server #4824] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][11:38:35]: [Server #4824] Received 18.75 MB of payload data from client #96 (simulated).
[INFO][11:38:35]: [Server #4824] Received 18.75 MB of payload data from client #42 (simulated).
[INFO][11:38:35]: [Server #4824] Received 18.75 MB of payload data from client #66 (simulated).
[INFO][11:38:36]: [Server #4824] Received 18.75 MB of payload data from client #59 (simulated).
[INFO][11:38:36]: [Server #4824] Adding client #77 to the list of clients for aggregation.
[INFO][11:38:36]: [Server #4824] Adding client #2 to the list of clients for aggregation.
[INFO][11:38:36]: [Server #4824] Adding client #55 to the list of clients for aggregation.
[INFO][11:38:36]: [Server #4824] Adding client #31 to the list of clients for aggregation.
[INFO][11:38:36]: [Server #4824] Adding client #97 to the list of clients for aggregation.
[INFO][11:38:36]: [Server #4824] Adding client #61 to the list of clients for aggregation.
[INFO][11:38:36]: [Server #4824] Adding client #75 to the list of clients for aggregation.
[INFO][11:38:36]: [Server #4824] Adding client #4 to the list of clients for aggregation.
[INFO][11:38:36]: [Server #4824] Adding client #32 to the list of clients for aggregation.
[INFO][11:38:36]: [Server #4824] Adding client #63 to the list of clients for aggregation.
[INFO][11:38:36]: [Server #4824] Aggregating 10 clients in total.
[INFO][11:38:36]: [Server #4824] Updated weights have been received.
[INFO][11:38:36]: [Server #4824] Aggregating model weight deltas.
[INFO][11:38:36]: [Server #4824] Finished aggregating updated weights.
[INFO][11:38:36]: [Server #4824] Started model testing.
[INFO][11:39:20]: [Trainer.test] 测试完成 - 准确率: 20.10% (2010/10000)
[INFO][11:39:20]: [93m[1m[Server #4824] Global model accuracy: 20.10%
[0m
[INFO][11:39:20]: get_logged_items 被调用
[INFO][11:39:20]: 从updates获取参与客户端: [77, 2, 55, 31, 97, 61, 75, 4, 32, 63]
[INFO][11:39:20]: 客户端 77 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][11:39:20]: 客户端 2 陈旧度: 4 (当前轮次:4, 上次参与:0)
[INFO][11:39:20]: 客户端 55 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][11:39:20]: 客户端 31 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][11:39:20]: 客户端 97 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][11:39:20]: 客户端 61 陈旧度: 3 (当前轮次:4, 上次参与:1)
[INFO][11:39:20]: 客户端 75 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][11:39:20]: 客户端 4 陈旧度: 2 (当前轮次:4, 上次参与:2)
[INFO][11:39:20]: 客户端 32 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][11:39:20]: 客户端 63 陈旧度: 2 (当前轮次:4, 上次参与:2)
[INFO][11:39:20]: 陈旧度统计 - 参与客户端: [77, 2, 55, 31, 97, 61, 75, 4, 32, 63], 陈旧度: [1, 4, 1, 1, 1, 3, 1, 2, 1, 2]
[INFO][11:39:20]: 平均陈旧度: 1.7, 最大: 4, 最小: 1
[INFO][11:39:20]: 最终logged_items: {'round': 4, 'accuracy': 0.201, 'accuracy_std': 0, 'elapsed_time': 212.15708661079407, 'processing_time': 0.009889399999792658, 'comm_time': 0, 'round_time': 64.41593116867682, 'comm_overhead': 1874.9709129333496, 'global_accuracy': 0.201, 'avg_staleness': 1.7, 'max_staleness': 4, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][11:39:20]: [Server #4824] All client reports have been processed.
[INFO][11:39:20]: [Server #4824] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][11:39:20]: [Server #4824] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][11:39:20]: [93m[1m
[Server #4824] Starting round 5/400.[0m
[INFO][11:39:20]: [Server #4824] Selected clients: [81, 92, 14, 25, 90, 41, 17, 47, 73, 61]
[INFO][11:39:20]: [Server #4824] Selecting client #81 for training.
[INFO][11:39:20]: [Server #4824] Sending the current model to client #81 (simulated).
[INFO][11:39:20]: [Server #4824] Sending 18.75 MB of payload data to client #81 (simulated).
[INFO][11:39:20]: [Server #4824] Selecting client #92 for training.
[INFO][11:39:20]: [Server #4824] Sending the current model to client #92 (simulated).
[INFO][11:39:20]: [Server #4824] Sending 18.75 MB of payload data to client #92 (simulated).
[INFO][11:39:20]: [Server #4824] Selecting client #14 for training.
[INFO][11:39:20]: [Server #4824] Sending the current model to client #14 (simulated).
[INFO][11:39:20]: [Server #4824] Sending 18.75 MB of payload data to client #14 (simulated).
[INFO][11:39:20]: [Server #4824] Selecting client #25 for training.
[INFO][11:39:20]: [Server #4824] Sending the current model to client #25 (simulated).
[INFO][11:39:20]: [Server #4824] Sending 18.75 MB of payload data to client #25 (simulated).
[INFO][11:39:20]: [Server #4824] Selecting client #90 for training.
[INFO][11:39:20]: [Server #4824] Sending the current model to client #90 (simulated).
[INFO][11:39:20]: [Server #4824] Sending 18.75 MB of payload data to client #90 (simulated).
[INFO][11:39:20]: [Server #4824] Selecting client #41 for training.
[INFO][11:39:20]: [Server #4824] Sending the current model to client #41 (simulated).
[INFO][11:39:21]: [Server #4824] Sending 18.75 MB of payload data to client #41 (simulated).
[INFO][11:39:21]: [Server #4824] Selecting client #17 for training.
[INFO][11:39:21]: [Server #4824] Sending the current model to client #17 (simulated).
[INFO][11:39:21]: [Server #4824] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][11:39:21]: [Server #4824] Selecting client #47 for training.
[INFO][11:39:21]: [Server #4824] Sending the current model to client #47 (simulated).
[INFO][11:39:21]: [Server #4824] Sending 18.75 MB of payload data to client #47 (simulated).
[INFO][11:39:21]: [Server #4824] Selecting client #73 for training.
[INFO][11:39:21]: [Server #4824] Sending the current model to client #73 (simulated).
[INFO][11:39:21]: [Server #4824] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][11:39:21]: [Server #4824] Selecting client #61 for training.
[INFO][11:39:22]: [Server #4824] Sending the current model to client #61 (simulated).
[INFO][11:39:22]: [Server #4824] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][11:42:59]: [Server #4824] Received 18.75 MB of payload data from client #14 (simulated).
[INFO][11:43:01]: [Server #4824] Received 18.75 MB of payload data from client #92 (simulated).
[INFO][11:43:02]: [Server #4824] Received 18.75 MB of payload data from client #81 (simulated).
[INFO][11:43:02]: [Server #4824] Received 18.75 MB of payload data from client #41 (simulated).
[INFO][11:43:03]: [Server #4824] Received 18.75 MB of payload data from client #90 (simulated).
[INFO][11:43:03]: [Server #4824] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][11:43:03]: [Server #4824] Received 18.75 MB of payload data from client #47 (simulated).
[INFO][11:43:03]: [Server #4824] Received 18.75 MB of payload data from client #25 (simulated).
[INFO][11:43:03]: [Server #4824] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][11:43:03]: [Server #4824] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][11:43:03]: [Server #4824] Adding client #71 to the list of clients for aggregation.
[INFO][11:43:03]: [Server #4824] Adding client #80 to the list of clients for aggregation.
[INFO][11:43:03]: [Server #4824] Adding client #34 to the list of clients for aggregation.
[INFO][11:43:03]: [Server #4824] Adding client #5 to the list of clients for aggregation.
[INFO][11:43:03]: [Server #4824] Adding client #96 to the list of clients for aggregation.
[INFO][11:43:03]: [Server #4824] Adding client #42 to the list of clients for aggregation.
[INFO][11:43:03]: [Server #4824] Adding client #49 to the list of clients for aggregation.
[INFO][11:43:03]: [Server #4824] Adding client #33 to the list of clients for aggregation.
[INFO][11:43:03]: [Server #4824] Adding client #59 to the list of clients for aggregation.
[INFO][11:43:03]: [Server #4824] Adding client #66 to the list of clients for aggregation.
[INFO][11:43:03]: [Server #4824] Aggregating 10 clients in total.
[INFO][11:43:03]: [Server #4824] Updated weights have been received.
[INFO][11:43:03]: [Server #4824] Aggregating model weight deltas.
[INFO][11:43:03]: [Server #4824] Finished aggregating updated weights.
[INFO][11:43:04]: [Server #4824] Started model testing.
[INFO][11:43:16]: [Trainer.test] 测试完成 - 准确率: 17.53% (1753/10000)
[INFO][11:43:16]: [93m[1m[Server #4824] Global model accuracy: 17.53%
[0m
[INFO][11:43:16]: get_logged_items 被调用
[INFO][11:43:16]: 从updates获取参与客户端: [71, 80, 34, 5, 96, 42, 49, 33, 59, 66]
[INFO][11:43:16]: 客户端 71 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][11:43:16]: 客户端 80 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][11:43:16]: 客户端 34 陈旧度: 2 (当前轮次:5, 上次参与:3)
[INFO][11:43:16]: 客户端 5 陈旧度: 2 (当前轮次:5, 上次参与:3)
[INFO][11:43:16]: 客户端 96 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][11:43:16]: 客户端 42 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][11:43:16]: 客户端 49 陈旧度: 3 (当前轮次:5, 上次参与:2)
[INFO][11:43:16]: 客户端 33 陈旧度: 4 (当前轮次:5, 上次参与:1)
[INFO][11:43:16]: 客户端 59 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][11:43:16]: 客户端 66 陈旧度: 2 (当前轮次:5, 上次参与:3)
[INFO][11:43:16]: 陈旧度统计 - 参与客户端: [71, 80, 34, 5, 96, 42, 49, 33, 59, 66], 陈旧度: [1, 1, 2, 2, 1, 1, 3, 4, 1, 2]
[INFO][11:43:16]: 平均陈旧度: 1.8, 最大: 4, 最小: 1
[INFO][11:43:16]: 最终logged_items: {'round': 5, 'accuracy': 0.1753, 'accuracy_std': 0, 'elapsed_time': 252.67568635940552, 'processing_time': 0.11975809999989906, 'comm_time': 0, 'round_time': 67.65127011489267, 'comm_overhead': 2249.9650955200195, 'global_accuracy': 0.1753, 'avg_staleness': 1.8, 'max_staleness': 4, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][11:43:16]: [Server #4824] All client reports have been processed.
[INFO][11:43:16]: [Server #4824] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][11:43:16]: [Server #4824] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][11:43:16]: [93m[1m
[Server #4824] Starting round 6/400.[0m
[INFO][11:43:16]: [Server #4824] Selected clients: [71, 96, 28, 43, 40, 84, 70, 56, 5, 68]
[INFO][11:43:16]: [Server #4824] Selecting client #71 for training.
[INFO][11:43:16]: [Server #4824] Sending the current model to client #71 (simulated).
[INFO][11:43:16]: [Server #4824] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][11:43:16]: [Server #4824] Selecting client #96 for training.
[INFO][11:43:16]: [Server #4824] Sending the current model to client #96 (simulated).
[INFO][11:43:16]: [Server #4824] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][11:43:16]: [Server #4824] Selecting client #28 for training.
[INFO][11:43:16]: [Server #4824] Sending the current model to client #28 (simulated).
[INFO][11:43:16]: [Server #4824] Sending 18.75 MB of payload data to client #28 (simulated).
[INFO][11:43:16]: [Server #4824] Selecting client #43 for training.
[INFO][11:43:16]: [Server #4824] Sending the current model to client #43 (simulated).
[INFO][11:43:16]: [Server #4824] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][11:43:16]: [Server #4824] Selecting client #40 for training.
[INFO][11:43:16]: [Server #4824] Sending the current model to client #40 (simulated).
[INFO][11:43:16]: [Server #4824] Sending 18.75 MB of payload data to client #40 (simulated).
[INFO][11:43:16]: [Server #4824] Selecting client #84 for training.
[INFO][11:43:16]: [Server #4824] Sending the current model to client #84 (simulated).
[INFO][11:43:16]: [Server #4824] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][11:43:16]: [Server #4824] Selecting client #70 for training.
[INFO][11:43:16]: [Server #4824] Sending the current model to client #70 (simulated).
[INFO][11:43:17]: [Server #4824] Sending 18.75 MB of payload data to client #70 (simulated).
[INFO][11:43:17]: [Server #4824] Selecting client #56 for training.
[INFO][11:43:17]: [Server #4824] Sending the current model to client #56 (simulated).
[INFO][11:43:17]: [Server #4824] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][11:43:17]: [Server #4824] Selecting client #5 for training.
[INFO][11:43:17]: [Server #4824] Sending the current model to client #5 (simulated).
[INFO][11:43:18]: [Server #4824] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][11:43:18]: [Server #4824] Selecting client #68 for training.
[INFO][11:43:18]: [Server #4824] Sending the current model to client #68 (simulated).
[INFO][11:43:19]: [Server #4824] Sending 18.75 MB of payload data to client #68 (simulated).
[INFO][11:46:31]: [Server #4824] Received 18.75 MB of payload data from client #28 (simulated).
[INFO][11:46:31]: [Server #4824] Received 18.75 MB of payload data from client #71 (simulated).
[INFO][11:46:32]: [Server #4824] Received 18.75 MB of payload data from client #96 (simulated).
[INFO][11:46:34]: [Server #4824] Received 18.75 MB of payload data from client #40 (simulated).
[INFO][11:46:35]: [Server #4824] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][11:46:35]: [Server #4824] Received 18.75 MB of payload data from client #70 (simulated).
[INFO][11:46:35]: [Server #4824] Received 18.75 MB of payload data from client #43 (simulated).
[INFO][11:46:35]: [Server #4824] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][11:46:35]: [Server #4824] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][11:46:35]: [Server #4824] Received 18.75 MB of payload data from client #68 (simulated).
[INFO][11:46:35]: [Server #4824] Adding client #61 to the list of clients for aggregation.
[INFO][11:46:35]: [Server #4824] Adding client #47 to the list of clients for aggregation.
[INFO][11:46:35]: [Server #4824] Adding client #14 to the list of clients for aggregation.
[INFO][11:46:35]: [Server #4824] Adding client #17 to the list of clients for aggregation.
[INFO][11:46:35]: [Server #4824] Adding client #90 to the list of clients for aggregation.
[INFO][11:46:35]: [Server #4824] Adding client #41 to the list of clients for aggregation.
[INFO][11:46:35]: [Server #4824] Adding client #81 to the list of clients for aggregation.
[INFO][11:46:35]: [Server #4824] Adding client #92 to the list of clients for aggregation.
[INFO][11:46:35]: [Server #4824] Adding client #25 to the list of clients for aggregation.
[INFO][11:46:35]: [Server #4824] Adding client #73 to the list of clients for aggregation.
[INFO][11:46:35]: [Server #4824] Aggregating 10 clients in total.
[INFO][11:46:35]: [Server #4824] Updated weights have been received.
[INFO][11:46:35]: [Server #4824] Aggregating model weight deltas.
[INFO][11:46:35]: [Server #4824] Finished aggregating updated weights.
[INFO][11:46:35]: [Server #4824] Started model testing.
[INFO][11:46:47]: [Trainer.test] 测试完成 - 准确率: 22.51% (2251/10000)
[INFO][11:46:47]: [93m[1m[Server #4824] Global model accuracy: 22.51%
[0m
[INFO][11:46:47]: get_logged_items 被调用
[INFO][11:46:47]: 从updates获取参与客户端: [61, 47, 14, 17, 90, 41, 81, 92, 25, 73]
[INFO][11:46:47]: 客户端 61 陈旧度: 2 (当前轮次:6, 上次参与:4)
[INFO][11:46:47]: 客户端 47 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][11:46:47]: 客户端 14 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][11:46:47]: 客户端 17 陈旧度: 3 (当前轮次:6, 上次参与:3)
[INFO][11:46:47]: 客户端 90 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][11:46:47]: 客户端 41 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][11:46:47]: 客户端 81 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][11:46:47]: 客户端 92 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][11:46:47]: 客户端 25 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][11:46:47]: 客户端 73 陈旧度: 5 (当前轮次:6, 上次参与:1)
[INFO][11:46:47]: 陈旧度统计 - 参与客户端: [61, 47, 14, 17, 90, 41, 81, 92, 25, 73], 陈旧度: [2, 1, 1, 3, 1, 1, 1, 1, 1, 5]
[INFO][11:46:47]: 平均陈旧度: 1.7, 最大: 5, 最小: 1
[INFO][11:46:47]: 最终logged_items: {'round': 6, 'accuracy': 0.2251, 'accuracy_std': 0, 'elapsed_time': 282.26224064826965, 'processing_time': 0.014643199999909484, 'comm_time': 0, 'round_time': 70.10515399611518, 'comm_overhead': 2624.9592781066895, 'global_accuracy': 0.2251, 'avg_staleness': 1.7, 'max_staleness': 5, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][11:46:47]: [Server #4824] All client reports have been processed.
[INFO][11:46:47]: [Server #4824] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_6.pth.
[INFO][11:46:47]: [Server #4824] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_6.pth.
[INFO][11:46:47]: [93m[1m
[Server #4824] Starting round 7/400.[0m
[INFO][11:46:47]: [Server #4824] Selected clients: [34, 57, 59, 95, 24, 51, 79, 100, 97, 52]
[INFO][11:46:47]: [Server #4824] Selecting client #34 for training.
[INFO][11:46:47]: [Server #4824] Sending the current model to client #34 (simulated).
[INFO][11:46:47]: [Server #4824] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][11:46:47]: [Server #4824] Selecting client #57 for training.
[INFO][11:46:47]: [Server #4824] Sending the current model to client #57 (simulated).
[INFO][11:46:47]: [Server #4824] Sending 18.75 MB of payload data to client #57 (simulated).
[INFO][11:46:47]: [Server #4824] Selecting client #59 for training.
[INFO][11:46:47]: [Server #4824] Sending the current model to client #59 (simulated).
[INFO][11:46:47]: [Server #4824] Sending 18.75 MB of payload data to client #59 (simulated).
[INFO][11:46:47]: [Server #4824] Selecting client #95 for training.
[INFO][11:46:47]: [Server #4824] Sending the current model to client #95 (simulated).
[INFO][11:46:47]: [Server #4824] Sending 18.75 MB of payload data to client #95 (simulated).
[INFO][11:46:47]: [Server #4824] Selecting client #24 for training.
[INFO][11:46:47]: [Server #4824] Sending the current model to client #24 (simulated).
[INFO][11:46:47]: [Server #4824] Sending 18.75 MB of payload data to client #24 (simulated).
[INFO][11:46:47]: [Server #4824] Selecting client #51 for training.
[INFO][11:46:47]: [Server #4824] Sending the current model to client #51 (simulated).
[INFO][11:46:47]: [Server #4824] Sending 18.75 MB of payload data to client #51 (simulated).
[INFO][11:46:47]: [Server #4824] Selecting client #79 for training.
[INFO][11:46:47]: [Server #4824] Sending the current model to client #79 (simulated).
[INFO][11:46:47]: [Server #4824] Sending 18.75 MB of payload data to client #79 (simulated).
[INFO][11:46:47]: [Server #4824] Selecting client #100 for training.
[INFO][11:46:47]: [Server #4824] Sending the current model to client #100 (simulated).
[INFO][11:46:48]: [Server #4824] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][11:46:48]: [Server #4824] Selecting client #97 for training.
[INFO][11:46:48]: [Server #4824] Sending the current model to client #97 (simulated).
[INFO][11:46:48]: [Server #4824] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][11:46:48]: [Server #4824] Selecting client #52 for training.
[INFO][11:46:48]: [Server #4824] Sending the current model to client #52 (simulated).
[INFO][11:46:49]: [Server #4824] Sending 18.75 MB of payload data to client #52 (simulated).
[INFO][11:49:26]: [Server #4824] Received 18.75 MB of payload data from client #57 (simulated).
[INFO][11:49:28]: [Server #4824] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][11:49:28]: [Server #4824] Received 18.75 MB of payload data from client #51 (simulated).
[INFO][11:49:29]: [Server #4824] Received 18.75 MB of payload data from client #24 (simulated).
[INFO][11:49:29]: [Server #4824] Received 18.75 MB of payload data from client #59 (simulated).
[INFO][11:49:29]: [Server #4824] Received 18.75 MB of payload data from client #95 (simulated).
[INFO][11:49:29]: [Server #4824] Received 18.75 MB of payload data from client #79 (simulated).
[INFO][11:49:29]: [Server #4824] Received 18.75 MB of payload data from client #52 (simulated).
[INFO][11:49:29]: [Server #4824] Received 18.75 MB of payload data from client #100 (simulated).
[INFO][11:49:29]: [Server #4824] Received 18.75 MB of payload data from client #97 (simulated).
[INFO][11:49:29]: [Server #4824] Adding client #96 to the list of clients for aggregation.
[INFO][11:49:29]: [Server #4824] Adding client #28 to the list of clients for aggregation.
[INFO][11:49:29]: [Server #4824] Adding client #71 to the list of clients for aggregation.
[INFO][11:49:29]: [Server #4824] Adding client #40 to the list of clients for aggregation.
[INFO][11:49:29]: [Server #4824] Adding client #84 to the list of clients for aggregation.
[INFO][11:49:29]: [Server #4824] Adding client #68 to the list of clients for aggregation.
[INFO][11:49:29]: [Server #4824] Adding client #5 to the list of clients for aggregation.
[INFO][11:49:29]: [Server #4824] Adding client #70 to the list of clients for aggregation.
[INFO][11:49:29]: [Server #4824] Adding client #43 to the list of clients for aggregation.
[INFO][11:49:29]: [Server #4824] Adding client #56 to the list of clients for aggregation.
[INFO][11:49:29]: [Server #4824] Aggregating 10 clients in total.
[INFO][11:49:29]: [Server #4824] Updated weights have been received.
[INFO][11:49:29]: [Server #4824] Aggregating model weight deltas.
[INFO][11:49:29]: [Server #4824] Finished aggregating updated weights.
[INFO][11:49:29]: [Server #4824] Started model testing.
[INFO][11:49:41]: [Trainer.test] 测试完成 - 准确率: 21.13% (2113/10000)
[INFO][11:49:41]: [93m[1m[Server #4824] Global model accuracy: 21.13%
[0m
[INFO][11:49:41]: get_logged_items 被调用
[INFO][11:49:41]: 从updates获取参与客户端: [96, 28, 71, 40, 84, 68, 5, 70, 43, 56]
[INFO][11:49:41]: 客户端 96 陈旧度: 2 (当前轮次:7, 上次参与:5)
[INFO][11:49:41]: 客户端 28 陈旧度: 1 (当前轮次:7, 上次参与:6)
[INFO][11:49:41]: 客户端 71 陈旧度: 2 (当前轮次:7, 上次参与:5)
[INFO][11:49:41]: 客户端 40 陈旧度: 1 (当前轮次:7, 上次参与:6)
[INFO][11:49:41]: 客户端 84 陈旧度: 6 (当前轮次:7, 上次参与:1)
[INFO][11:49:41]: 客户端 68 陈旧度: 1 (当前轮次:7, 上次参与:6)
[INFO][11:49:41]: 客户端 5 陈旧度: 2 (当前轮次:7, 上次参与:5)
[INFO][11:49:41]: 客户端 70 陈旧度: 1 (当前轮次:7, 上次参与:6)
[INFO][11:49:41]: 客户端 43 陈旧度: 1 (当前轮次:7, 上次参与:6)
[INFO][11:49:41]: 客户端 56 陈旧度: 5 (当前轮次:7, 上次参与:2)
[INFO][11:49:41]: 陈旧度统计 - 参与客户端: [96, 28, 71, 40, 84, 68, 5, 70, 43, 56], 陈旧度: [2, 1, 2, 1, 6, 1, 2, 1, 1, 5]
[INFO][11:49:41]: 平均陈旧度: 2.2, 最大: 6, 最小: 1
[INFO][11:49:41]: 最终logged_items: {'round': 7, 'accuracy': 0.2113, 'accuracy_std': 0, 'elapsed_time': 318.34458446502686, 'processing_time': 0.08481010000014066, 'comm_time': 0, 'round_time': 65.66889807096254, 'comm_overhead': 2999.9534606933594, 'global_accuracy': 0.2113, 'avg_staleness': 2.2, 'max_staleness': 6, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][11:49:41]: [Server #4824] All client reports have been processed.
[INFO][11:49:41]: [Server #4824] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_7.pth.
[INFO][11:49:41]: [Server #4824] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_7.pth.
[INFO][11:49:41]: [93m[1m
[Server #4824] Starting round 8/400.[0m
[INFO][11:49:41]: [Server #4824] Selected clients: [12, 63, 92, 72, 14, 21, 73, 55, 50, 69]
[INFO][11:49:41]: [Server #4824] Selecting client #12 for training.
[INFO][11:49:41]: [Server #4824] Sending the current model to client #12 (simulated).
[INFO][11:49:41]: [Server #4824] Sending 18.75 MB of payload data to client #12 (simulated).
[INFO][11:49:41]: [Server #4824] Selecting client #63 for training.
[INFO][11:49:41]: [Server #4824] Sending the current model to client #63 (simulated).
[INFO][11:49:41]: [Server #4824] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][11:49:41]: [Server #4824] Selecting client #92 for training.
[INFO][11:49:41]: [Server #4824] Sending the current model to client #92 (simulated).
[INFO][11:49:41]: [Server #4824] Sending 18.75 MB of payload data to client #92 (simulated).
[INFO][11:49:41]: [Server #4824] Selecting client #72 for training.
[INFO][11:49:41]: [Server #4824] Sending the current model to client #72 (simulated).
[INFO][11:49:42]: [Server #4824] Sending 18.75 MB of payload data to client #72 (simulated).
[INFO][11:49:42]: [Server #4824] Selecting client #14 for training.
[INFO][11:49:42]: [Server #4824] Sending the current model to client #14 (simulated).
[INFO][11:49:42]: [Server #4824] Sending 18.75 MB of payload data to client #14 (simulated).
[INFO][11:49:42]: [Server #4824] Selecting client #21 for training.
[INFO][11:49:42]: [Server #4824] Sending the current model to client #21 (simulated).
[INFO][11:49:42]: [Server #4824] Sending 18.75 MB of payload data to client #21 (simulated).
[INFO][11:49:42]: [Server #4824] Selecting client #73 for training.
[INFO][11:49:42]: [Server #4824] Sending the current model to client #73 (simulated).
[INFO][11:49:42]: [Server #4824] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][11:49:42]: [Server #4824] Selecting client #55 for training.
[INFO][11:49:42]: [Server #4824] Sending the current model to client #55 (simulated).
[INFO][11:49:43]: [Server #4824] Sending 18.75 MB of payload data to client #55 (simulated).
[INFO][11:49:43]: [Server #4824] Selecting client #50 for training.
[INFO][11:49:43]: [Server #4824] Sending the current model to client #50 (simulated).
[INFO][11:49:43]: [Server #4824] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][11:49:43]: [Server #4824] Selecting client #69 for training.
[INFO][11:49:43]: [Server #4824] Sending the current model to client #69 (simulated).
[INFO][11:49:44]: [Server #4824] Sending 18.75 MB of payload data to client #69 (simulated).
[INFO][11:50:52]: [Server #4824] An existing client just disconnected.
[WARNING][11:50:52]: [Server #4824] Client process #24176 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][11:50:52]: [93m[1m[Server #4824] Closing the server due to a failed client.[0m
[INFO][11:50:52]: [Server #4824] Training concluded.
[INFO][11:50:52]: [Server #4824] Model saved to ./models/refedscafl/cifar10_alpha01/resnet_9.pth.
[INFO][11:50:52]: [Server #4824] Closing the server.
[INFO][11:50:52]: Closing the connection to client #25200.
[INFO][11:50:52]: Closing the connection to client #36604.
[INFO][11:50:52]: Closing the connection to client #10044.
[INFO][11:50:52]: Closing the connection to client #11184.
[INFO][11:50:52]: Closing the connection to client #5820.
[INFO][11:50:52]: Closing the connection to client #26244.
[INFO][11:50:52]: Closing the connection to client #26032.
[INFO][11:50:52]: Closing the connection to client #31740.
[INFO][11:50:52]: Closing the connection to client #31200.
