clients:
    # The total number of clients
    total_clients: 3

    # The number of clients selected in each round
    per_round: 3

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8000

data:
    # The training and testing dataset
    datasource: MNIST

    # Where the dataset is located
    data_path: data

    # Number of samples in each partition
    partition_size: 20000

    # IID or non-IID?
    sampler: iid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 100

    # The maximum number of clients running concurrently
    max_concurrency: 3

    # The target accuracy
    target_accuracy: 0.95

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 32
    optimizer: FedProx

    # The machine learning model
    model_name: lenet5

algorithm:
    # Aggregation algorithm
    type: fedavg

    # The maximum number of local epochs
    max_local_epochs: 10

    # Pattern of local epochs, either `constant` or `uniform_random`
    pattern: uniform_random

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0
        mu: 0.1
