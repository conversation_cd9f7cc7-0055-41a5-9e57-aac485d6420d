#!/usr/bin/env python3
"""
联邦学习算法对比试验脚本
比较 ReFedScaFL、FedAC 和 SC_AFL 的性能

使用方法:
python run_comparison_experiments.py --algorithm refedscafl
python run_comparison_experiments.py --algorithm fedac
python run_comparison_experiments.py --algorithm scafl
python run_comparison_experiments.py --all  # 运行所有算法
"""

import os
import sys
import subprocess
import argparse
import time
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'comparison_experiments_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

# 实验配置
EXPERIMENTS = {
    'refedscafl': {
        'name': 'ReFedScaFL',
        'script': 'refedscafl.py',
        'config': 'refedscafl_comparison_config.yml',
        'directory': 'refedscafl',
        'port': 8092
    },
    'fedac': {
        'name': 'FedAC',
        'script': 'fedac.py',
        'config': 'fedac_CIFAR10_resnet9_alpha0.1.yml',
        'directory': 'fedac',
        'port': 8005
    },
    'scafl': {
        'name': 'SC_AFL',
        'script': 'sc_afl.py',
        'config': 'sc_afl_cifar10_resnet9.yml',
        'directory': 'SC_AFL',
        'port': 8000
    }
}

def check_port_available(port):
    """检查端口是否可用"""
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('127.0.0.1', port))
            return True
        except OSError:
            return False

def kill_python_processes():
    """杀死所有Python进程以清理环境"""
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                         capture_output=True, check=False)
        else:  # Linux/Mac
            subprocess.run(['pkill', '-f', 'python'], 
                         capture_output=True, check=False)
        time.sleep(2)
        logger.info("已清理Python进程")
    except Exception as e:
        logger.warning(f"清理进程时出错: {e}")

def run_experiment(algorithm_name):
    """运行单个算法的实验"""
    if algorithm_name not in EXPERIMENTS:
        logger.error(f"未知算法: {algorithm_name}")
        return False
    
    exp_config = EXPERIMENTS[algorithm_name]
    logger.info(f"开始运行 {exp_config['name']} 实验...")
    
    # 检查端口
    if not check_port_available(exp_config['port']):
        logger.warning(f"端口 {exp_config['port']} 被占用，尝试清理...")
        kill_python_processes()
        time.sleep(3)
        if not check_port_available(exp_config['port']):
            logger.error(f"端口 {exp_config['port']} 仍被占用，跳过实验")
            return False
    
    # 切换到对应目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    exp_dir = os.path.join(os.path.dirname(base_dir), exp_config['directory'])
    
    if not os.path.exists(exp_dir):
        logger.error(f"实验目录不存在: {exp_dir}")
        return False
    
    # 检查脚本和配置文件
    script_path = os.path.join(exp_dir, exp_config['script'])
    config_path = os.path.join(exp_dir, exp_config['config'])
    
    if not os.path.exists(script_path):
        logger.error(f"脚本文件不存在: {script_path}")
        return False
    
    if not os.path.exists(config_path):
        logger.error(f"配置文件不存在: {config_path}")
        return False
    
    # 运行实验
    try:
        logger.info(f"在目录 {exp_dir} 中运行: python {exp_config['script']} -c {exp_config['config']}")
        
        start_time = time.time()
        process = subprocess.Popen(
            ['python', exp_config['script'], '-c', exp_config['config']],
            cwd=exp_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时输出日志
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                logger.info(f"[{exp_config['name']}] {output.strip()}")
        
        # 等待进程完成
        return_code = process.wait()
        end_time = time.time()
        
        if return_code == 0:
            logger.info(f"{exp_config['name']} 实验完成，耗时: {end_time - start_time:.2f}秒")
            return True
        else:
            stderr_output = process.stderr.read()
            logger.error(f"{exp_config['name']} 实验失败，返回码: {return_code}")
            logger.error(f"错误输出: {stderr_output}")
            return False
            
    except Exception as e:
        logger.error(f"运行 {exp_config['name']} 实验时出错: {e}")
        return False
    finally:
        # 清理进程
        try:
            if 'process' in locals():
                process.terminate()
        except:
            pass

def main():
    parser = argparse.ArgumentParser(description='联邦学习算法对比实验')
    parser.add_argument('--algorithm', choices=['refedscafl', 'fedac', 'scafl'], 
                       help='要运行的算法')
    parser.add_argument('--all', action='store_true', help='运行所有算法')
    
    args = parser.parse_args()
    
    if not args.algorithm and not args.all:
        parser.print_help()
        return
    
    logger.info("=" * 60)
    logger.info("联邦学习算法对比实验开始")
    logger.info("=" * 60)
    
    success_count = 0
    total_count = 0
    
    if args.all:
        # 运行所有算法
        for alg_name in EXPERIMENTS.keys():
            total_count += 1
            logger.info(f"\n{'='*40}")
            logger.info(f"实验 {total_count}: {EXPERIMENTS[alg_name]['name']}")
            logger.info(f"{'='*40}")
            
            if run_experiment(alg_name):
                success_count += 1
            
            # 实验间隔
            if alg_name != list(EXPERIMENTS.keys())[-1]:
                logger.info("等待5秒后开始下一个实验...")
                time.sleep(5)
    else:
        # 运行指定算法
        total_count = 1
        if run_experiment(args.algorithm):
            success_count = 1
    
    logger.info("\n" + "=" * 60)
    logger.info("实验总结")
    logger.info("=" * 60)
    logger.info(f"总实验数: {total_count}")
    logger.info(f"成功实验数: {success_count}")
    logger.info(f"失败实验数: {total_count - success_count}")
    
    if success_count == total_count:
        logger.info("🎉 所有实验都成功完成！")
    else:
        logger.warning("⚠️  部分实验失败，请检查日志")

if __name__ == '__main__':
    main()
