#!/usr/bin/env python3
"""
测试客户端创建修复
验证是否解决了重复创建客户端的问题
"""

import os
import sys
import time
import logging
import subprocess
from datetime import datetime

def setup_logging():
    """设置日志"""
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    logging.basicConfig(level=logging.INFO, format=log_format)
    return logging.getLogger(__name__)

def run_scafl_test():
    """运行SC-AFL测试"""
    logger = logging.getLogger(__name__)
    
    logger.info("🔧 测试客户端创建修复")
    logger.info("=" * 50)
    
    # 显示修复内容
    fixes = [
        "🔧 移除服务器start()方法中的重复客户端创建",
        "✅ 客户端只在sc_afl.py中创建一次",
        "🎯 避免客户端ID冲突和训练器重复初始化",
        "📊 配置: 6个客户端, 每轮3个, 最大并发1个"
    ]
    
    for fix in fixes:
        logger.info(fix)
    
    logger.info("=" * 50)
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 运行实验
        cmd = [sys.executable, "sc_afl.py", "-c", "sc_afl_cifar10_resnet9_with_network"]
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 监控关键信息
        output_lines = []
        client_creation_count = 0
        training_start_count = 0
        aggregation_count = 0
        error_count = 0
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                print(line)
                output_lines.append(line)
                
                # 统计关键事件
                if "开始创建客户端" in line:
                    client_creation_count += 1
                elif "开始训练" in line and "epoch" in line:
                    training_start_count += 1
                elif "聚合完成" in line:
                    aggregation_count += 1
                elif "ERROR" in line or "Exception" in line:
                    error_count += 1
                    logger.error(f"❌ 检测到错误: {line}")
        
        # 等待进程结束
        return_code = process.poll()
        
        # 记录结束时间
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info("=" * 50)
        logger.info("📊 测试结果统计:")
        logger.info(f"⏱️ 运行时间: {duration:.2f}秒")
        logger.info(f"🔚 进程返回码: {return_code}")
        logger.info(f"👥 客户端创建次数: {client_creation_count}")
        logger.info(f"🏃 训练开始次数: {training_start_count}")
        logger.info(f"🔄 聚合完成次数: {aggregation_count}")
        logger.info(f"❌ 错误次数: {error_count}")
        
        # 分析结果
        if client_creation_count == 6:
            logger.info("✅ 客户端创建次数正确 (6个)")
        elif client_creation_count > 6:
            logger.warning(f"⚠️ 客户端创建次数过多 ({client_creation_count}个)，可能仍有重复创建")
        else:
            logger.warning(f"⚠️ 客户端创建次数不足 ({client_creation_count}个)")
        
        # 检查日志文件
        logs_dir = "logs"
        if os.path.exists(logs_dir):
            log_files = [f for f in os.listdir(logs_dir) if f.startswith("sc_afl_server_")]
            if log_files:
                latest_log = max(log_files, key=lambda x: os.path.getctime(os.path.join(logs_dir, x)))
                log_path = os.path.join(logs_dir, latest_log)
                log_size = os.path.getsize(log_path)
                
                logger.info(f"📋 最新日志文件: {latest_log}")
                logger.info(f"📊 日志文件大小: {log_size} 字节")
                
                # 简单分析日志内容
                if log_size > 50000:  # 如果日志文件大于50KB
                    logger.info("✅ 日志文件较大，说明系统运行了一段时间")
                else:
                    logger.warning("⚠️ 日志文件较小，可能系统很快就停止了")
        
        # 判断测试是否成功
        success = (
            return_code == 0 and 
            client_creation_count == 6 and 
            error_count == 0 and
            duration > 10  # 至少运行10秒
        )
        
        if success:
            logger.info("🎉 客户端创建修复测试成功！")
        else:
            logger.error("❌ 客户端创建修复测试失败")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 运行测试时发生异常: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🚀 开始测试客户端创建修复")
    
    success = run_scafl_test()
    
    if success:
        logger.info("🎉 所有测试通过！客户端创建问题已修复")
    else:
        logger.error("❌ 测试失败，需要进一步调试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
