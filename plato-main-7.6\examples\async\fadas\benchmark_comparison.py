#!/usr/bin/env python3
"""
联邦学习算法对比基准测试
在统一的通信失败环境下对比不同算法的性能

对比算法：
1. ReFedScaFL (有蒸馏补偿)
2. FedAS (无补偿机制)
3. FedAC (无补偿机制)
4. FedAvg (基准算法)

测试环境：
- 统一的通信失败模拟
- 相同的网络参数
- 相同的数据分布
- 相同的模型架构
"""

import os
import sys
import json
import time
import logging
import numpy as np
import pandas as pd
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from communication_failure_simulator import CommunicationFailureSimulator


class BenchmarkConfig:
    """统一的基准测试配置"""
    
    def __init__(self):
        # 网络环境配置
        self.network_configs = {
            'stable': {
                'base_communication_time': 0.3,
                'communication_noise_std': 0.2,
                'initial_threshold': 0.8,
                'description': '稳定网络环境'
            },
            'normal': {
                'base_communication_time': 0.5,
                'communication_noise_std': 0.5,
                'initial_threshold': 1.0,
                'description': '普通网络环境'
            },
            'unstable': {
                'base_communication_time': 0.8,
                'communication_noise_std': 0.8,
                'initial_threshold': 1.5,
                'description': '不稳定网络环境'
            },
            'very_unstable': {
                'base_communication_time': 1.2,
                'communication_noise_std': 1.0,
                'initial_threshold': 2.0,
                'description': '极不稳定网络环境'
            }
        }
        
        # 训练配置
        self.training_config = {
            'total_clients': 100,
            'clients_per_round': 20,
            'rounds': 50,  # 减少轮数以便快速对比
            'local_epochs': 3,
            'learning_rate': 0.01,
            'batch_size': 32
        }
        
        # 算法列表
        self.algorithms = ['ReFedScaFL', 'FedAS', 'FedAC', 'FedAvg']


class AlgorithmSimulator:
    """算法模拟器基类"""
    
    def __init__(self, algorithm_name, network_config, training_config):
        self.algorithm_name = algorithm_name
        self.network_config = network_config
        self.training_config = training_config
        
        # 初始化失败模拟器
        self.failure_simulator = CommunicationFailureSimulator({
            **network_config,
            'enable_dynamic_threshold': True,
            'enable_logging': False  # 减少日志输出
        })
        
        # 训练状态
        self.current_round = 0
        self.global_accuracy = 0.0
        self.training_results = []
        
        # 算法特定参数
        self.init_algorithm_specific()
        
        # 日志
        self.logger = logging.getLogger(f"{algorithm_name}")
    
    def init_algorithm_specific(self):
        """初始化算法特定参数"""
        if self.algorithm_name == 'ReFedScaFL':
            self.has_distillation = True
            self.distillation_success_rate = 0.8  # 蒸馏补偿成功率
        else:
            self.has_distillation = False
    
    def simulate_client_training(self, client_id, round_num):
        """模拟客户端训练"""
        # 模拟训练时间（快速版本）
        training_time = np.random.uniform(0.5, 1.5)  # 快速模拟
        
        # 模拟训练结果
        base_accuracy = 0.1 + (round_num * 0.015)  # 逐步提升
        noise = np.random.uniform(-0.02, 0.02)
        local_accuracy = min(0.95, max(0.05, base_accuracy + noise))
        
        # 计算陈旧度
        staleness = max(0, round_num - np.random.randint(1, 4))
        
        return {
            'client_id': client_id,
            'accuracy': local_accuracy,
            'training_time': training_time,
            'staleness': staleness,
            'num_samples': np.random.randint(100, 500)
        }
    
    def handle_communication_failure(self, failed_update):
        """处理通信失败"""
        if self.has_distillation:
            # ReFedScaFL: 使用蒸馏补偿
            if np.random.random() < self.distillation_success_rate:
                # 蒸馏成功，生成补偿更新
                compensated_update = {
                    'client_id': failed_update['client_id'],
                    'accuracy': failed_update['accuracy'] * 0.9,  # 略微降低
                    'staleness': failed_update['staleness'] + 1,  # 增加陈旧度
                    'num_samples': failed_update['num_samples'],
                    'is_distilled': True
                }
                return compensated_update
        
        # 其他算法：直接丢弃
        return None
    
    def aggregate_updates(self, successful_updates, compensated_updates=None):
        """聚合更新"""
        all_updates = successful_updates.copy()
        if compensated_updates:
            all_updates.extend(compensated_updates)
        
        if not all_updates:
            return self.global_accuracy, 0.0
        
        # 计算陈旧度
        staleness_values = [update['staleness'] for update in all_updates]
        avg_staleness = np.mean(staleness_values)
        
        # 计算加权准确率
        total_samples = sum(update['num_samples'] for update in all_updates)
        weighted_accuracy = sum(
            update['accuracy'] * update['num_samples'] 
            for update in all_updates
        ) / total_samples
        
        return weighted_accuracy, avg_staleness
    
    def run_round(self, round_num):
        """运行一轮训练"""
        self.current_round = round_num
        
        # 选择客户端
        selected_clients = np.random.choice(
            range(1, self.training_config['total_clients'] + 1),
            size=self.training_config['clients_per_round'],
            replace=False
        )
        
        successful_updates = []
        failed_updates = []
        compensated_updates = []
        
        # 模拟客户端训练和上传
        for client_id in selected_clients:
            update = self.simulate_client_training(client_id, round_num)
            
            # 判断上传是否成功
            success, comm_time, _ = self.failure_simulator.should_upload_succeed(client_id)
            
            if success:
                successful_updates.append(update)
            else:
                failed_updates.append(update)
                
                # 尝试补偿机制
                compensated = self.handle_communication_failure(update)
                if compensated:
                    compensated_updates.append(compensated)
        
        # 聚合更新
        self.global_accuracy, avg_staleness = self.aggregate_updates(
            successful_updates, compensated_updates
        )
        
        # 记录结果
        failure_stats = self.failure_simulator.get_statistics()
        result = {
            'round': round_num,
            'algorithm': self.algorithm_name,
            'global_accuracy': self.global_accuracy,
            'avg_staleness': avg_staleness,
            'successful_updates': len(successful_updates),
            'failed_updates': len(failed_updates),
            'compensated_updates': len(compensated_updates),
            'total_updates': len(successful_updates) + len(compensated_updates),
            'success_rate': len(successful_updates) / len(selected_clients),
            'effective_rate': (len(successful_updates) + len(compensated_updates)) / len(selected_clients),
            'failure_rate': failure_stats['overall']['failure_rate'],
            'comm_threshold': failure_stats['overall']['current_threshold']
        }
        
        self.training_results.append(result)
        return result


class BenchmarkRunner:
    """基准测试运行器"""
    
    def __init__(self):
        self.config = BenchmarkConfig()
        self.results = []
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(message)s'
        )
        self.logger = logging.getLogger("BenchmarkRunner")
    
    def run_single_experiment(self, algorithm, network_env):
        """运行单个实验"""
        self.logger.info(f"🔄 运行实验: {algorithm} @ {network_env}")
        
        network_config = self.config.network_configs[network_env]
        simulator = AlgorithmSimulator(
            algorithm, network_config, self.config.training_config
        )
        
        # 运行训练轮次
        for round_num in range(1, self.config.training_config['rounds'] + 1):
            result = simulator.run_round(round_num)
            result['network_env'] = network_env
            result['network_desc'] = network_config['description']
            self.results.append(result)
            
            # 每10轮打印一次进度
            if round_num % 10 == 0:
                self.logger.info(
                    f"  轮次 {round_num}: 准确率={result['global_accuracy']:.4f}, "
                    f"有效率={result['effective_rate']:.2%}, "
                    f"失败率={result['failure_rate']:.2%}"
                )
        
        final_accuracy = simulator.training_results[-1]['global_accuracy']
        avg_effective_rate = np.mean([r['effective_rate'] for r in simulator.training_results])
        
        self.logger.info(
            f"✅ {algorithm} @ {network_env} 完成: "
            f"最终准确率={final_accuracy:.4f}, "
            f"平均有效率={avg_effective_rate:.2%}"
        )
        
        return simulator.training_results
    
    def run_full_benchmark(self):
        """运行完整基准测试"""
        self.logger.info("🚀 开始联邦学习算法基准测试")
        self.logger.info(f"算法: {self.config.algorithms}")
        self.logger.info(f"网络环境: {list(self.config.network_configs.keys())}")
        
        start_time = time.time()
        
        # 运行所有组合
        for network_env in self.config.network_configs.keys():
            self.logger.info(f"\n📡 测试网络环境: {self.config.network_configs[network_env]['description']}")
            
            for algorithm in self.config.algorithms:
                self.run_single_experiment(algorithm, network_env)
        
        total_time = time.time() - start_time
        self.logger.info(f"\n🎉 基准测试完成! 总耗时: {total_time:.1f}秒")
        
        # 保存和分析结果
        self.save_results()
        self.analyze_results()
    
    def save_results(self):
        """保存结果"""
        # 创建结果目录
        results_dir = os.path.join(current_dir, 'benchmark_results')
        os.makedirs(results_dir, exist_ok=True)
        
        # 保存详细结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # CSV格式
        df = pd.DataFrame(self.results)
        csv_file = os.path.join(results_dir, f'benchmark_detailed_{timestamp}.csv')
        df.to_csv(csv_file, index=False)
        
        # JSON格式
        json_file = os.path.join(results_dir, f'benchmark_detailed_{timestamp}.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        self.logger.info(f"📁 详细结果已保存:")
        self.logger.info(f"  CSV: {csv_file}")
        self.logger.info(f"  JSON: {json_file}")
        
        return csv_file, json_file
    
    def analyze_results(self):
        """分析结果"""
        self.logger.info("\n📊 结果分析:")
        self.logger.info("=" * 60)
        
        df = pd.DataFrame(self.results)
        
        # 按算法和网络环境分组分析
        for network_env in self.config.network_configs.keys():
            self.logger.info(f"\n🌐 {self.config.network_configs[network_env]['description']}:")
            
            env_data = df[df['network_env'] == network_env]
            final_round = env_data['round'].max()
            final_data = env_data[env_data['round'] == final_round]
            
            for _, row in final_data.iterrows():
                algorithm = row['algorithm']
                accuracy = row['global_accuracy']
                effective_rate = row['effective_rate']
                failure_rate = row['failure_rate']
                
                self.logger.info(
                    f"  {algorithm:12s}: 准确率={accuracy:.4f}, "
                    f"有效率={effective_rate:.2%}, 失败率={failure_rate:.2%}"
                )
        
        # 找出最佳算法
        self.logger.info(f"\n🏆 最佳算法分析:")
        
        for network_env in self.config.network_configs.keys():
            env_data = df[df['network_env'] == network_env]
            final_round = env_data['round'].max()
            final_data = env_data[env_data['round'] == final_round]
            
            best_accuracy = final_data.loc[final_data['global_accuracy'].idxmax()]
            best_effective = final_data.loc[final_data['effective_rate'].idxmax()]
            
            self.logger.info(f"  {network_env:12s}: 最高准确率={best_accuracy['algorithm']} ({best_accuracy['global_accuracy']:.4f})")
            self.logger.info(f"  {network_env:12s}: 最高有效率={best_effective['algorithm']} ({best_effective['effective_rate']:.2%})")


def main():
    """主函数"""
    print("🧪 联邦学习算法基准测试")
    print("=" * 50)
    print("测试目标: 验证ReFedScaFL在通信失败环境下的优势")
    print()
    
    try:
        runner = BenchmarkRunner()
        runner.run_full_benchmark()
        
        print("\n💡 分析建议:")
        print("1. 查看ReFedScaFL是否在所有网络环境下都表现最佳")
        print("2. 对比有效率(effective_rate)，ReFedScaFL应该更高")
        print("3. 在不稳定网络下，ReFedScaFL的优势应该更明显")
        print("4. 查看补偿更新(compensated_updates)的效果")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
