clients:
    # The total number of clients
    total_clients: 10

    # The number of clients selected in each round
    per_round: 3

    # Should the clients compute test accuracy locally?
    do_test: true

server:
    address: 127.0.0.1
    port: 8000

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Where the dataset is located
    data_path: ./data

    # Number of samples in each partition
    partition_size: 20000

    # IID or non-IID?
    sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 20

    # The maximum number of clients running concurrently
    max_concurrency: 3

    # The target accuracy
    target_accuracy: 0.95

    # The machine learning model
    model_name: resnet_18

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 128
    optimizer: SGD
    lr_scheduler: StepLR

algorithm:
    # The initial synchronization frequency
    global_learning_rate: 1
    lr_decay: 0.998

parameters:
    optimizer:
        lr: 0.1
        momentum: 0.9
        weight_decay: 0.0

    learning_rate:
        step_size: 1
        gamma: 1
