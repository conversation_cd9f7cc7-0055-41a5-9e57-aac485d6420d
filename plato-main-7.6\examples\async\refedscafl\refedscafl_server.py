import logging
import os
import random
import sys
import time
import numpy as np
import torch
from datetime import datetime
import asyncio
from logging.handlers import RotatingFileHandler
import csv
import copy

from collections import OrderedDict

from plato.config import Config
from plato.servers import fedavg
from plato.models import registry as models
from refedscafl_client import Client
from refedscafl_algorithm import Algorithm
from refedscafl_trainer import Trainer
from refedscafl_result import ResultManager  # 导入结果管理器
from math import log2
from types import SimpleNamespace
# 动态导入模型和数据源，不再硬编码

# 配置日志
def setup_logging():
    """配置日志系统"""
    # 获取根日志记录器
    root_logger = logging.getLogger()
    
    # 检查是否已经配置过日志处理器，避免重复添加
    if root_logger.handlers:
        # 如果已经有处理器，直接返回现有的logger
        return root_logger
    
    # 清除任何现有的处理器（以防万一）
    for handler in root_logger.handlers[:]:
        root_logger.removeH<PERSON><PERSON>(handler)
    
    # 创建日志目录
    log_dir = os.path.join(Config().result_path if hasattr(Config(), 'result_path') else './logs', 'server_logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 设置日志文件名
    log_file = os.path.join(log_dir, f'server_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    # 配置根日志记录器
    root_logger.setLevel(logging.DEBUG)
    
    # 创建文件处理器（带轮转）
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '[%(asctime)s][%(levelname)s][%(filename)s:%(lineno)d] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return root_logger

# 初始化日志
logger = setup_logging()

class Server(fedavg.Server):
    """
    改进的RefedSCAFL联邦学习服务器类，继承自FedAvg服务器。
    实现了基于SCAFL客户端选择 + 知识蒸馏补偿的混合算法。
    主要特点：
    1. 使用纯SCAFL的贪心客户端选择算法（优化通信成功率）
    2. 对成功上传的客户端进行正常聚合
    3. 对上传失败的客户端进行知识蒸馏补偿
    4. 动态权重聚合策略，根据成功/失败比例调整权重
    5. 保留容错能力的同时提高训练效率
    """
    # 添加类级别的共享资源
    _shared_resources = {
        'model': None,
        'trainer': None,
        'weights': None,
        'initialized': False
    }

    def create_model(self, config):
        """
        根据配置动态创建模型
        使用软编码方式，支持更灵活的模型选择
        """
        try:
            model_name = getattr(config.trainer, 'model_name', 'lenet5')
            model_config = getattr(config.parameters, 'model', {})
            
            # 获取模型参数
            num_classes = getattr(model_config, 'num_classes', 10)
            in_channels = getattr(model_config, 'in_channels', 1)
            
            # 根据模型名称动态创建模型
            if model_name.startswith('resnet'):
                from plato.models import resnet
                # 修正：使用 Model.get() 方法创建 ResNet 模型
                model = resnet.Model.get(
                    model_name=model_name,
                    num_classes=num_classes
                )
            elif model_name == 'lenet5':
                from plato.models import lenet5
                model = lenet5.Model(
                    num_classes=num_classes,
                    in_channels=in_channels
                )
            elif model_name == 'vgg16':
                from plato.models import vgg
                model = vgg.VGG16(num_classes=num_classes)
            else:
                raise ValueError(f"不支持的模型类型: {model_name}")
            
            logger.info(f"创建模型: {model_name}, 参数: num_classes={num_classes}, in_channels={in_channels}")
            
            return model
            
        except Exception as e:
            logger.error(f"创建模型失败: {str(e)}")
            raise

    def __init__(self, model=None, algorithm=None, trainer=None):
        """初始化服务器，优化共享资源管理"""
        try:
            # 获取配置
            config = Config()
            
            # 如果没有共享模型，创建并缓存
            if model is None and not Server._shared_resources['initialized']:
                try:
                    # 使用软编码方式创建模型
                    model = self.create_model(config)
                    if model is not None:
                        Server._shared_resources['model'] = model
                        logger.info("创建并缓存共享模型")
                    else:
                        logger.error("模型创建失败，返回了 None")
                except Exception as e:
                    logger.error(f"创建模型时出错: {str(e)}")
                    model = None
            elif Server._shared_resources['model'] is not None:
                model = Server._shared_resources['model']
                logger.info("使用已缓存的共享模型")

            # 确保模型不为 None
            if model is None:
                logger.warning("模型为 None，尝试创建默认模型")
                try:
                    # 尝试创建一个简单的默认模型作为后备
                    import torch.nn as nn
                    class SimpleModel(nn.Module):
                        def __init__(self):
                            super().__init__()
                            self.fc = nn.Linear(10, 10)
                        def forward(self, x):
                            return self.fc(x)
                    model = SimpleModel()
                    Server._shared_resources['model'] = model
                    logger.info("创建了简单的默认模型")
                except Exception as e:
                    logger.error(f"创建默认模型失败: {str(e)}")

            # 如果没有共享训练器，创建并缓存
            if trainer is None and not Server._shared_resources['initialized']:
                try:
                    trainer = Trainer(model=model, client_id=None)
                    Server._shared_resources['trainer'] = trainer
                    logger.info("创建并缓存共享训练器")
                except Exception as e:
                    logger.error(f"创建训练器失败: {str(e)}")
                    trainer = None
            elif Server._shared_resources['trainer'] is not None:
                trainer = Server._shared_resources['trainer']
                logger.info("使用已缓存的共享训练器")

            # 调用父类初始化
            super().__init__(model=model, trainer=trainer)

            # 标记资源已初始化
            Server._shared_resources['initialized'] = True
            
            # 初始化服务器状态
            self.initialize_server_state()
            
            # 初始化配置参数
            self.initialize_config_parameters(config)
            
            # 初始化全局模型权重
            self.initialize_global_weights()
            
        except Exception as e:
            logger.error(f"服务器初始化失败: {str(e)}")
            # 确保基本属性被初始化，即使出错
            self.initialize_server_state()
            self.global_weights = {}
            raise

    def initialize_config_parameters(self, config):
        """初始化配置参数"""
        # 基本参数
        self.total_clients = getattr(config.clients, 'total_clients', 10)
        self.clients_per_round = getattr(config.clients, 'per_round', 2)
        self.greedy_selection_size = getattr(config.algorithm, 'greedy_selection_size', 3)
        
        # 权重参数
        self.initial_success_weight = getattr(config.algorithm, 'success_weight', 0.7)
        self.initial_distill_weight = getattr(config.algorithm, 'distill_weight', 0.3)
        self.success_weight = self.initial_success_weight
        self.distill_weight = self.initial_distill_weight
        
        # 自适应参数
        self.enable_adaptive_weights = getattr(config.algorithm, 'enable_adaptive_weights', True)
        self.weight_adaptation_window = getattr(config.algorithm, 'weight_adaptation_window', 5)
        self.weight_adaptation_threshold = getattr(config.algorithm, 'weight_adaptation_threshold', 0.02)
        self.weight_adaptation_step = getattr(config.algorithm, 'weight_adaptation_step', 0.05)
        self.min_weight_ratio = getattr(config.algorithm, 'min_weight_ratio', 0.1)
        
        # 缓冲池参数
        self.buffer_pool_size = getattr(config.algorithm, 'buffer_pool_size', 20)
        
        # 通信参数
        self.upload_threshold = getattr(config.algorithm, 'communication_threshold', 1.0)
        self.comm_time_window = []
        
        # SCAFL参数
        self.V = getattr(config.algorithm, 'V', 1.0)
        self.tau_max = getattr(config.algorithm, 'tau_max', 6)
        
        # 其他参数
        self.global_round = 0
        self.total_rounds = getattr(config.trainer, 'rounds', 100)
        self.assessment_window = getattr(config.algorithm, 'assessment_window', 10)
        
        logger.info("服务器参数配置完成：")
        logger.info(f"- 客户端数量: total={self.total_clients}, per_round={self.clients_per_round}")
        logger.info(f"- 权重参数: success={self.success_weight}, distill={self.distill_weight}")
        logger.info(f"- SCAFL参数: V={self.V}, tau_max={self.tau_max}")

    def initialize_server_state(self):
        """初始化服务器状态"""
        # 初始化基本属性
        self.current_round = 0
        self.selected_clients = []
        self.client_upload_success = {}
        self.distilled_clients = set()
        self.client_updates = {}
        
        # 初始化缓冲池
        self.success_buffer_pool = []
        self.distill_buffer_pool = []
        self.success_buffered_clients = []
        self.distill_buffered_clients = []
        
        # 初始化客户端状态
        self.client_states = {}
        self.client_staleness = {}
        self.staleness_queue = {}
        self.client_beta = {}  # 客户端通信成功率
        
        # 初始化客户端时间估计相关属性
        self.client_Hk_comp = {}  # 客户端计算时间
        self.client_Hk_comm = {}  # 客户端通信时间
        self.client_Hk = {}  # 客户端总时间
        self.client_estimated_duration = {}  # 客户端估计持续时间
        self.historical_Ds = []  # 历史聚合时间
        self.comm_time_window = []  # 通信时间窗口
        
        # 初始化客户端选择相关属性
        self.trigger_clients = []  # 触发客户端
        self.non_trigger_clients = []  # 非触发客户端
        
        # 初始化性能指标
        self.accuracy_history = []
        self.staleness_history = []
        self.training_start_time = time.time()
        
        # 初始化其他必要属性
        self.global_weights = None  # 这将在 initialize_global_weights 中被设置
        self.global_model_cache = None  # 用于客户端蒸馏的全局模型缓存
        self.client_start_time = {}  # 记录客户端开始训练的时间
        self.rounds_since_assessment = 0  # 用于环境评估
        self.assessment_results = {}  # 存储评估结果
        
        # 初始化环境监控相关属性
        self.environment_monitoring = {'enable': False}  # 默认关闭环境监控
        self.environment_metrics = {
            'communication_failure_rate': 0.0,
            'network_instability': 0.0,
            'device_heterogeneity': 0.0,
            'data_quality_variance': 0.0
        }
        self.environment_thresholds = {
            'communication_failure_rate': 0.2,
            'network_instability': 0.3,
            'data_quality_variance': 0.3
        }
        self.active_enhancements = {
            'knowledge_distillation': False,
            'quality_aware_weighting': False,
            'adaptive_aggregation': False
        }
        self.enhancement_parameters = {
            'max_distillation_weight': 0.15
        }
        self.accuracy_log = []  # 用于记录准确率历史

    def initialize_global_weights(self):
        """初始化全局模型权重，优化权重共享"""
        try:
            # 检查是否有缓存的权重
            if Server._shared_resources['weights'] is not None:
                self.global_weights = Server._shared_resources['weights']
                # 同时更新全局模型缓存
                self.global_model_cache = copy.deepcopy(self.global_weights)
                logger.info("使用缓存的全局权重")
                return

            # 如果没有缓存的权重，从模型中提取
            if self.model is not None:
                self.global_weights = self.model.state_dict()
                # 同时更新全局模型缓存
                self.global_model_cache = copy.deepcopy(self.global_weights)
                # 缓存权重
                Server._shared_resources['weights'] = self.global_weights
                logger.info("从模型提取并缓存全局权重")
            elif Server._shared_resources['model'] is not None:
                # 如果实例的 model 为 None，但共享资源中有模型，则使用共享资源中的模型
                self.model = Server._shared_resources['model']
                self.global_weights = self.model.state_dict()
                # 同时更新全局模型缓存
                self.global_model_cache = copy.deepcopy(self.global_weights)
                Server._shared_resources['weights'] = self.global_weights
                logger.info("从共享资源模型提取并缓存全局权重")
            else:
                # 如果所有模型都为 None，则创建一个空字典作为全局权重
                # 这是一个临时解决方案，实际应用中应该确保模型正确初始化
                logger.warning("模型未初始化，创建空的全局权重字典")
                self.global_weights = {}
                self.global_model_cache = {}
                Server._shared_resources['weights'] = self.global_weights

        except Exception as e:
            logger.error(f"初始化全局权重失败: {str(e)}")
            # 创建一个空字典作为全局权重，避免后续代码出错
            self.global_weights = {}
            self.global_model_cache = {}
            Server._shared_resources['weights'] = self.global_weights
            logger.warning("由于错误，创建了空的全局权重字典")
            # 不再抛出异常，允许程序继续运行

    async def send_model_to_client(self, client_id):
        """向客户端发送模型，优化权重传输"""
        try:
            # 确保全局权重已初始化
            if self.global_weights is None:
                self.initialize_global_weights()

            # 创建响应对象
            response = SimpleNamespace()
            response.weights = self.global_weights
            
            # 记录权重发送日志
            if 'conv1.weight' in self.global_weights:
                conv1_shape = self.global_weights['conv1.weight'].shape
                logger.debug(f"发送权重到客户端{client_id}, conv1形状: {conv1_shape}")

            return response

        except Exception as e:
            logger.error(f"发送模型到客户端{client_id}失败: {str(e)}")
            return None

    async def register_client(self, sid, pid, client_id):
        """注册客户端，使用标准Plato接口"""
        # 调用父类的register_client方法
        await super().register_client(sid, pid, client_id)

        # 初始化ReFedScaFL特有的客户端状态
        if client_id is not None:
            self.client_states[client_id] = {
                'last_round': 0,
                'is_upload_failed': False,
                'tau_k': 0,
                'beta_k': 0,
                'D_k': 0,
                'Q_k': 1.0
            }

            logger.info(f"客户端{client_id}注册完成，已初始化ReFedScaFL状态")
        else:
            logger.error("尝试注册的客户端ID为None")

    def cleanup_shared_resources(self):
        """清理共享资源"""
        try:
            Server._shared_resources['model'] = None
            Server._shared_resources['trainer'] = None
            Server._shared_resources['weights'] = None
            Server._shared_resources['initialized'] = False
            logger.info("共享资源已清理")
        except Exception as e:
            logger.error(f"清理共享资源失败: {str(e)}")

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        try:
            self.cleanup_shared_resources()
            if hasattr(self, 'result_manager'):
                self.result_manager.close()
            logger.info("服务器资源已释放")
        except Exception as e:
            logger.error(f"服务器清理失败: {str(e)}")

    def log_weight_shapes(self, context=""):
        """记录当前权重形状，用于调试"""
        if self.global_weights and 'conv1.weight' in self.global_weights:
            conv1_shape = self.global_weights['conv1.weight'].shape
            logger.debug(f"[权重监控]{context} 全局权重 conv1.weight 形状: {conv1_shape}")

        if self.global_model_cache and 'conv1.weight' in self.global_model_cache:
            cache_shape = self.global_model_cache['conv1.weight'].shape
            logger.debug(f"[权重监控]{context} 全局缓存 conv1.weight 形状: {cache_shape}")

        if self.model and hasattr(self.model, 'state_dict'):
            model_shape = self.model.state_dict()['conv1.weight'].shape
            logger.debug(f"[权重监控]{context} 服务器模型 conv1.weight 形状: {model_shape}")

    def get_cid(self, cid_obj):
        """从客户端ID对象中提取客户端ID"""
        try:
            if isinstance(cid_obj, SimpleNamespace):
                for key in ['id', 'client_id', 'sid', 'name']:
                    if hasattr(cid_obj, key):
                        cid = getattr(cid_obj, key)
                        if cid is not None:
                            return cid
                # 如果无法从SimpleNamespace获取有效ID，记录详细信息
                logger.error("无法从SimpleNamespace获取有效ID: %s", cid_obj)
                logger.error("SimpleNamespace属性: %s", dir(cid_obj))
                raise ValueError(f"无法从SimpleNamespace获取合法ID: {cid_obj}")
            elif cid_obj is None:
                logger.error("客户端ID对象为None")
                raise ValueError("客户端ID对象为None")
            else:
                return cid_obj
        except Exception as e:
            logger.error("get_cid方法出错，输入对象: %s, 类型: %s, 错误: %s", 
                        cid_obj, type(cid_obj), str(e))
            raise

    def estimate_client_times(self, cid):
        """
        估计客户端的计算和通信时间（更贴近现实无线边缘环境）
        """
        cid = self.get_cid(cid)
        a = 0.5  # 计算时间基线
        mu = 1.0
        param_count = 100000  # 模型参数量
        # 距离分布更广，模拟异构性
        distance = np.random.uniform(0.5, 3.0)  # 单位: km

        # 计算时间估计
        H_comp = a + np.random.exponential(1/mu)
        self.client_Hk_comp[cid] = H_comp

        # 通信参数
        B = np.random.uniform(50e3, 500e3)  # 带宽: 50~500kHz
        P = 10 ** ((10 - 30) / 10)  # 发射功率 (W)
        N0 = np.random.uniform(1e-15, 1e-13)  # 噪声功率谱密度 (W/Hz)
        L = 10 ** (3.5 * np.log10(distance))  # 路径损耗
        SNR = P / (N0 * B * L)  # 信噪比

        # 信道容量
        C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps

        # 模型大小（100K参数，32位）
        model_size = param_count * 32  # bits

        # 基础延迟（如协议、排队、接入等）
        latency = np.random.uniform(0.05, 0.3)  # 50~300ms

        # 通信时间 = 传输时延 + 基础延迟
        H_comm = model_size / C + latency
        # 🎯 调整通信时间范围，使部分客户端无法顺利上传
        # 扩大范围并增加一些随机性，模拟网络不稳定
        network_instability = np.random.uniform(0.8, 2.5)  # 网络不稳定因子
        H_comm = H_comm * network_instability
        H_comm = np.clip(H_comm, 0.5, 8.0)  # 扩大范围，增加失败概率
        self.client_Hk_comm[cid] = H_comm

        # 总时间估计
        H_k = H_comp + H_comm
        self.client_Hk[cid] = H_k
        self.client_estimated_duration[cid] = H_k
        logger.debug("估计客户端%s训练+通信时间: %f秒 (计算: %f秒, 通信: %f秒, 距离: %.2fkm, 带宽: %.0fHz, SNR: %.2e)",
                     cid, H_k, H_comp, H_comm, distance, B, SNR)

    def get_dkt(self, cid):
        """
        基于真实客户端状态的延迟估计函数
        
        计算公式：d_k^t = max{t_k_start + D_k - ∑D(i), 0}
        
        参数:
            cid: 客户端ID
            
        返回:
            float: 客户端当前的延迟估计值
        """
        cid = self.get_cid(cid)
        t = len(self.historical_Ds)

        # 获取客户端开始时间、估计持续时间和陈旧度
        t_k_start = self.client_start_time.get(cid, 0)
        D_k = self.client_estimated_duration.get(cid, 0)
        tau_k = self.client_staleness.get(cid, 0)

        # 计算历史聚合时间总和
        start_idx = max(0, t - tau_k)
        D_sum = sum(self.historical_Ds[start_idx: t]) if self.historical_Ds else 0

        # 计算延迟估计值 ♥
        d_k_t = max(t_k_start + D_k - D_sum, 0)
        
        logger.debug("客户端%s延迟估计: start=%f, duration=%f, tau=%f, D_sum=%f, d_k_t=%f", cid, t_k_start, D_k, tau_k, D_sum, d_k_t)
        
        return d_k_t
    
    def update_comm_threshold(self, H_comm):
        """
        更新通信时间阈值
        参数:
            H_comm: 客户端本次通信时间，如果为None则只更新阈值不添加新样本
        """
        window_size = 20
        # 只有H_comm不为None时才添加到窗口
        if H_comm is not None:
            self.comm_time_window.append(H_comm)
            if len(self.comm_time_window) > window_size:
                self.comm_time_window.pop(0)
                
        # 🎯 调整阈值计算，目标：60%成功率（40%失败率）- 增加失败概率以测试逻辑
        if self.comm_time_window:
            # 排序通信时间
            sorted_times = sorted(self.comm_time_window)

            # 使用60%分位数作为阈值，增加失败概率
            percentile_60 = np.percentile(sorted_times, 60)

            # 为了稳定性，结合均值进行调整
            mean_time = np.mean(sorted_times)

            # 使用较低的分位数，不添加额外余量
            self.upload_threshold = percentile_60

            # 设置合理的阈值范围
            min_threshold = 1.0  # 提高最小阈值
            max_threshold = 6.0  # 设置最大阈值
            self.upload_threshold = max(min_threshold, min(self.upload_threshold, max_threshold))

            logger.debug("🎯 更新通信阈值: %.3f (均值: %.3f, 60%%分位数: %.3f)",
                       self.upload_threshold, mean_time, percentile_60)
        else:
            # 如果没有历史数据，使用一个较大的默认值
            self.upload_threshold = 2.0
            logger.debug("使用默认通信阈值: %.3f", self.upload_threshold)

    def can_upload_successfully(self, client_id):
        """
        🎯 新增：判断客户端是否能顺利上传
        基于服务器估算的通信时间和动态阈值判断

        参数:
            client_id: 客户端ID

        返回:
            bool: True表示能顺利上传，False表示无法顺利上传
        """
        # 获取客户端的估算通信时间
        comm_time = self.client_Hk_comm.get(client_id, 0.1)

        # 基于通信时间阈值判断
        can_upload = comm_time <= self.upload_threshold

        logger.debug(f"客户端 {client_id} 通信时间: {comm_time:.3f}s, 阈值: {self.upload_threshold:.3f}s, "
                    f"能否顺利上传: {can_upload}")

        return can_upload

    async def clients_processed(self):
        """处理客户端更新"""
        try:
            super().clients_processed()

            start_time = getattr(self, "aggregation_start_time", time.time())
            D_j = time.time() - start_time
            self.historical_Ds.append(D_j)

            if not hasattr(self, 'buffer_pool'):
                self.buffer_pool = []
            if not hasattr(self, 'failed_clients'):
                self.failed_clients = []

            self.client_upload_success.clear()

            # 只保留聚合、状态更新等核心流程
            for update in self.updates:
                try:
                    cid = self.get_cid(update.client_id)
                    if cid not in self.client_start_time:
                        logger.warning("客户端%s的开始时间未记录，使用当前时间", cid)
                        self.client_start_time[cid] = time.time()
                    d_k_t = self.get_dkt(cid)
                    if not hasattr(self, 'd_k_t_dict'):
                        self.d_k_t_dict = {}
                    self.d_k_t_dict[cid] = d_k_t
                    prev_q = self.staleness_queue.get(cid, 0)
                    tau_k = self.client_staleness.get(cid, 0)
                    beta_k = 1
                    new_q = max(prev_q + (tau_k + 1) * (1 - beta_k) - self.tau_max, 0)
                    self.staleness_queue[cid] = new_q
                    logger.debug("客户端%s状态更新 - 延迟估计: %.4f, 陈旧度: %s, 队列长度: %.2f",
                               cid, d_k_t, tau_k, new_q)
                except Exception as e:
                    logger.error("更新客户端状态时出错: %s", str(e))
                    import traceback
                    logger.error("详细错误信息: %s", traceback.format_exc())
                    continue

            current_staleness = sum(self.d_k_t_dict.values()) / len(self.d_k_t_dict) if self.d_k_t_dict else 0
            self.staleness_history.append(current_staleness)

            if self.should_trigger_aggregation():
                logger.info("满足聚合条件，开始聚合...")
                await self.aggregate_models()
            else:
                logger.debug("当前轮次不满足聚合条件")

            self.client_updates.clear()

        except Exception as e:
            logger.error("处理客户端更新时出错: %s", str(e))
            raise

    def get_logged_items(self) -> dict:
        """获取要记录到CSV文件中的项目"""
        logger.info("get_logged_items 被调用")
        logged_items = super().get_logged_items()

        # 添加全局模型准确率
        if hasattr(self, 'accuracy') and self.accuracy is not None:
            logged_items["global_accuracy"] = self.accuracy
        else:
            logged_items["global_accuracy"] = 0.0

        # 添加陈旧度统计 - 使用当前轮次参与聚合的客户端
        participating_clients = []
        participating_staleness = []

        if hasattr(self, 'updates') and self.updates:
            # 从当前轮次的更新中获取参与的客户端ID
            participating_clients = [update.client_id for update in self.updates]
            logger.info(f"从updates获取参与客户端: {participating_clients}")

            # 为每个参与的客户端计算陈旧度
            for client_id in participating_clients:
                # 确保客户端状态存在
                if client_id not in self.client_states:
                    self.client_states[client_id] = {'last_round': max(0, self.current_round - 1)}

                # 计算陈旧度：当前轮次 - 客户端最后参与的轮次
                last_round = self.client_states[client_id].get('last_round', max(0, self.current_round - 1))
                staleness = max(0, self.current_round - last_round)
                participating_staleness.append(staleness)

                # 更新客户端状态和陈旧度字典
                self.client_staleness[client_id] = staleness
                logger.info(f"客户端 {client_id} 陈旧度: {staleness} (当前轮次:{self.current_round}, 上次参与:{last_round})")

                # 更新客户端最后参与的轮次
                self.client_states[client_id]['last_round'] = self.current_round

        if participating_staleness:
            logged_items["avg_staleness"] = sum(participating_staleness) / len(participating_staleness)
            logged_items["max_staleness"] = max(participating_staleness)
            logged_items["min_staleness"] = min(participating_staleness)
            logger.info(f"陈旧度统计 - 参与客户端: {participating_clients}, 陈旧度: {participating_staleness}")
            logger.info(f"平均陈旧度: {logged_items['avg_staleness']}, 最大: {logged_items['max_staleness']}, 最小: {logged_items['min_staleness']}")
        else:
            logged_items["avg_staleness"] = 0.0
            logged_items["max_staleness"] = 0.0
            logged_items["min_staleness"] = 0.0
            logger.warning("没有参与聚合的客户端")

        # 添加全局模型准确率标准差（如果有多个客户端测试结果）
        if hasattr(self, 'accuracy_history') and len(self.accuracy_history) > 1:
            import numpy as np
            logged_items["global_accuracy_std"] = np.std(self.accuracy_history[-5:])  # 最近5轮的标准差
        else:
            logged_items["global_accuracy_std"] = 0.0

        logger.info(f"最终logged_items: {logged_items}")
        return logged_items

    def pure_scafl_greedy_select_clients(self, available_clients, staleness_dict, max_clients=5):
        """
        纯SCAFL的贪心客户端选择算法（不包含RefedSCAFL的质量加权）
        专注于最小化系统延迟和队列长度，提高通信成功率

        参数:
            available_clients: 可用客户端列表
            staleness_dict: 客户端陈旧度字典
            max_clients: 最大选择数量

        返回:
            选中的客户端列表
        """
        if not available_clients:
            return []

        selected_clients = []
        remaining_clients = available_clients.copy()
        current_D_t = 0.0  # 当前最大延迟
        current_Q_sum = 0.0  # 当前队列总和

        logger.info(f"🎯 开始纯SCAFL贪心选择，候选客户端: {len(available_clients)}个")

        for round_num in range(min(max_clients, len(available_clients))):
            best_client = None
            best_delta_score = float('inf')

            for client_id in remaining_clients:
                # 获取客户端参数
                staleness = staleness_dict.get(client_id, 0)
                beta_k = self.client_beta.get(client_id, 0.8)
                D_k = self.client_estimated_duration.get(client_id, 1.0)
                Q_k = self.staleness_queue.get(client_id, 1.0)

                # 计算增量成本（纯SCAFL公式）
                temp_D_t = max(current_D_t, D_k)
                temp_Q_term = Q_k * ((staleness + 1) * (1 - beta_k) - self.tau_max)
                temp_total_cost = self.V * temp_D_t + (current_Q_sum + temp_Q_term)

                # 当前成本
                current_cost = self.V * current_D_t + current_Q_sum

                # 增量成本（纯SCAFL，不加质量加权）
                delta_cost = temp_total_cost - current_cost

                logger.debug(f"评估客户端{client_id}: staleness={staleness}, "
                           f"beta_k={beta_k:.3f}, D_k={D_k:.3f}, delta_cost={delta_cost:.4f}")

                if delta_cost < best_delta_score:
                    best_delta_score = delta_cost
                    best_client = client_id

            # 添加最优客户端
            if best_client is not None:
                selected_clients.append(best_client)
                remaining_clients.remove(best_client)

                # 更新状态
                staleness = staleness_dict.get(best_client, 0)
                beta_k = self.client_beta.get(best_client, 0.8)
                D_k = self.client_estimated_duration.get(best_client, 1.0)
                Q_k = self.staleness_queue.get(best_client, 1.0)

                current_D_t = max(current_D_t, D_k)
                current_Q_sum += Q_k * ((staleness + 1) * (1 - beta_k) - self.tau_max)

                logger.info(f"🎯 第{round_num+1}轮选择: 客户端{best_client}, "
                          f"当前D_t={current_D_t:.4f}, Q_sum={current_Q_sum:.4f}")
            else:
                logger.warning(f"第{round_num+1}轮未找到合适的客户端")
                break

        logger.info(f"🎯 纯SCAFL贪心选择完成，选中{len(selected_clients)}个客户端: {selected_clients}")
        return selected_clients


    def _calculate_client_quality_weight(self, client_id):
        """
        计算客户端质量权重 α_k,t
        基于客户端的历史表现和数据质量

        参数:
            client_id: 客户端ID

        返回:
            float: 质量权重 (0.0-1.0)
        """
        try:
            # 基础质量权重
            base_weight = 0.8

            # 根据客户端ID模拟不同的数据质量
            quality_factor = 1.0 - (client_id % 10) * 0.05  # 0.5到1.0

            # 根据陈旧度调整权重（陈旧度越高，权重越低）
            tau_k = self.client_staleness.get(client_id, 0)
            staleness_penalty = min(0.3, tau_k * 0.05)

            # 计算最终权重
            alpha_k_t = base_weight * quality_factor - staleness_penalty
            alpha_k_t = max(0.1, min(1.0, alpha_k_t))  # 限制在0.1-1.0之间

            return alpha_k_t

        except Exception as e:
            logger.error(f"计算客户端{client_id}质量权重失败: {e}")
            return 0.8  # 默认权重

    def greedy_select_aggregation_subset(self, successful_updates):
        """
        修正的ReFedScaFL贪心聚合子集选择算法
        参考SC_AFL的实现，使用标准的贪心选择策略：
        目标函数: S(A_t) = V * D_t + Σ(Q_k * (-τ_max))  (基础SC_AFL版本)

        修正要点：
        1. 按延迟估计值d_k_t排序客户端（从小到大）
        2. 贪心选择前k个客户端，使目标函数最小
        3. 正确计算虚拟队列惩罚项
        4. 与SC_AFL保持一致的选择逻辑
        5. 🎯 新增：只对顺利上传的客户端进行贪心选择

        参数:
            successful_updates: 顺利上传的客户端更新列表（不包括蒸馏补偿）

        返回:
            selected_updates: 选中的聚合子集
        """
        if not successful_updates:
            logger.warning("🎯 没有顺利上传的客户端更新，无法进行贪心选择")
            return []

        logger.info(f"🎯 开始贪心选择聚合子集，候选更新数量: {len(successful_updates)}")

        # 1. 准备客户端指标（参考SC_AFL的实现）
        client_metrics = {}
        for update in successful_updates:
            # 🎯 处理Plato的update对象
            client_id = update.client_id

            # 计算客户端的关键指标
            d_k_t = self.get_dkt(client_id)  # 延迟估计值
            Q_k = self.staleness_queue.get(client_id, 0.0)  # 虚拟队列长度
            tau_k = self.client_staleness.get(client_id, 0)  # 陈旧度

            client_metrics[client_id] = {
                'update': update,
                'd_k_t': d_k_t,
                'Q_k': Q_k,
                'tau_k': tau_k,
                'is_distilled': False  # 🎯 顺利上传的客户端都不是蒸馏的
            }

            logger.debug(f"🎯 客户端{client_id}指标: d_k_t={d_k_t:.4f}, Q_k={Q_k:.4f}, "
                        f"τ_k={tau_k}, 类型=顺利上传")

        # 2. 按延迟估计值d_k_t排序客户端（从小到大）- 参考SC_AFL
        sorted_clients = sorted(
            client_metrics.keys(),
            key=lambda cid: client_metrics[cid]['d_k_t']
        )

        client_delays = [(cid, client_metrics[cid]['d_k_t']) for cid in sorted_clients]
        logger.info(f"🎯 候选客户端按延迟估计值排序: {client_delays}")

        # 3. 贪心选择最优客户端子集（参考SC_AFL的逻辑）
        best_objective_value = float('inf')
        best_aggregation_set = []
        best_D_t = 0

        # 获取算法参数
        V = getattr(self, 'V', 1.0)  # 延迟权重
        tau_max = getattr(self, 'tau_max', 5)  # 最大陈旧度阈值
        max_aggregation_clients = min(self.clients_per_round, len(sorted_clients))

        logger.info(f"🎯 算法参数: V={V}, τ_max={tau_max}, 最大聚合客户端数={max_aggregation_clients}")

        # 4. 遍历所有可能的客户端子集大小（参考SC_AFL）
        for k in range(1, max_aggregation_clients + 1):
            # 取排序后的前k个客户端
            current_agg_candidates = sorted_clients[:k]

            # 计算D_t：最大的延迟估计值
            current_D_t = max([client_metrics[cid]['d_k_t'] for cid in current_agg_candidates])

            # 计算虚拟队列惩罚项（使用SC_AFL的标准公式）
            sum_queue_term = 0
            for cid in current_agg_candidates:
                Q_k = client_metrics[cid]['Q_k']
                # 根据SC-AFL算法，每个选中的客户端都贡献 Q_k * (-tau_max)
                sum_queue_term += Q_k * (-tau_max)

            # 计算目标函数值: V * D_t + ∑Q_k * (-tau_max)
            current_objective_value = V * current_D_t + sum_queue_term

            # 统计成功上传和蒸馏补偿的数量
            success_count = sum(1 for cid in current_agg_candidates if not client_metrics[cid]['is_distilled'])
            distill_count = sum(1 for cid in current_agg_candidates if client_metrics[cid]['is_distilled'])

            logger.info(f"🎯 聚合{k}个客户端: {current_agg_candidates}")
            logger.info(f"🎯   - D_t={current_D_t:.4f}, 队列惩罚={sum_queue_term:.4f}, 目标值={current_objective_value:.4f}")
            logger.info(f"🎯   - 成功上传:{success_count}个, 蒸馏补偿:{distill_count}个")

            # 更新最优解
            if current_objective_value < best_objective_value:
                best_objective_value = current_objective_value
                best_aggregation_set = current_agg_candidates
                best_D_t = current_D_t
                logger.info(f"🎯 找到更优聚合集合: {best_aggregation_set}，目标值: {best_objective_value:.4f}")

        # 5. 返回选中的更新
        if best_aggregation_set:
            selected_updates = [client_metrics[cid]['update'] for cid in best_aggregation_set]

            # 🎯 所有选中的都是顺利上传的客户端
            success_count = len(selected_updates)
            distill_count = 0

            logger.info(f"🎯 贪心聚合子集选择完成:")
            logger.info(f"🎯 - 选中客户端: {best_aggregation_set}")
            logger.info(f"🎯 - 顺利上传: {success_count}个, 蒸馏补偿: {distill_count}个")
            logger.info(f"🎯 - 最终目标函数值: {best_objective_value:.4f}")
            logger.info(f"🎯 - 最大延迟D_t: {best_D_t:.4f}")

            return selected_updates
        else:
            logger.warning("🎯 无法找到合适的聚合集合")
            return []



    def _calculate_history_performance_factor(self):
        """
        计算历史性能因子，基于最近几轮的准确率趋势
        返回值范围: 0.0-1.0，越高表示性能越好
        """
        try:
            if not hasattr(self, 'accuracy_history'):
                self.accuracy_history = []

            # 如果历史记录不足，返回中性值
            if len(self.accuracy_history) < 3:
                return 0.5

            # 计算最近5轮的准确率趋势
            recent_history = self.accuracy_history[-5:]

            # 计算趋势斜率（简单线性回归）
            n = len(recent_history)
            x_mean = (n - 1) / 2
            y_mean = sum(recent_history) / n

            numerator = sum((i - x_mean) * (acc - y_mean) for i, acc in enumerate(recent_history))
            denominator = sum((i - x_mean) ** 2 for i in range(n))

            if denominator == 0:
                trend_slope = 0
            else:
                trend_slope = numerator / denominator

            # 将趋势转换为因子 (正趋势 -> 高因子)
            trend_factor = max(0.0, min(1.0, 0.5 + trend_slope * 10))

            # 结合当前准确率水平
            current_level = recent_history[-1] if recent_history else 0.5
            level_factor = min(1.0, current_level)

            # 综合因子
            history_factor = 0.7 * level_factor + 0.3 * trend_factor

            return history_factor

        except Exception as e:
            logger.warning(f"计算历史性能因子失败: {e}")
            return 0.5

    def _calculate_staleness_factor(self):
        """
        计算陈旧度因子，基于客户端陈旧度分布
        返回值范围: 0.0-1.0，越高表示陈旧度分布越好
        """
        try:
            if not self.selected_clients:
                return 0.5

            # 获取选中客户端的陈旧度
            staleness_values = [self.client_staleness.get(client_id, 0) for client_id in self.selected_clients]

            if not staleness_values:
                return 0.5

            avg_staleness = sum(staleness_values) / len(staleness_values)
            max_staleness = max(staleness_values)

            # 陈旧度越低越好，转换为因子
            avg_factor = max(0.0, 1.0 - avg_staleness / self.tau_max)
            max_factor = max(0.0, 1.0 - max_staleness / self.tau_max)

            # 综合因子 (平均陈旧度权重更高)
            staleness_factor = 0.7 * avg_factor + 0.3 * max_factor

            return staleness_factor

        except Exception as e:
            logger.warning(f"计算陈旧度因子失败: {e}")
            return 0.5

    def _calculate_quality_factor(self, success_updates):
        """
        计算数据质量因子，基于成功客户端的数据质量
        返回值范围: 0.0-1.0，越高表示数据质量越好
        """
        try:
            if not success_updates:
                return 0.0

            total_quality = 0.0
            count = 0

            for update_record in success_updates:
                # 从更新记录中获取客户端ID
                client_id = update_record['client_id']

                # 基于客户端的数据分布质量
                data_quality = self._calculate_data_quality_score(client_id)

                # 基于客户端的通信稳定性
                comm_stability = self.client_beta.get(client_id, 0.8)

                # 基于客户端的参与频率
                participation_score = self._calculate_participation_diversity(client_id)

                # 综合质量分数
                client_quality = 0.5 * data_quality + 0.3 * comm_stability + 0.2 * participation_score
                total_quality += client_quality
                count += 1

                logger.debug(f"客户端{client_id}质量评估: 数据质量={data_quality:.3f}, "
                           f"通信稳定性={comm_stability:.3f}, 参与多样性={participation_score:.3f}, "
                           f"综合质量={client_quality:.3f}")

            if count == 0:
                return 0.5

            avg_quality = total_quality / count
            return min(1.0, avg_quality)

        except Exception as e:
            logger.warning(f"计算质量因子失败: {e}")
            return 0.5

    async def select_clients(self):
        """
        改进的客户端选择方法：使用纯SCAFL贪心算法
        专注于选择通信成功率高的客户端，减少蒸馏补偿需求
        """
        try:
            # 更新所有客户端的陈旧度（在选择前预先计算）
            for client_id in range(self.total_clients):
                if client_id not in self.client_states:
                    # 初始化时，假设客户端在第1轮开始参与，避免初始陈旧度过高
                    self.client_states[client_id] = {'last_round': max(0, self.current_round - 1)}

                current_round = self.current_round
                last_round = self.client_states[client_id].get('last_round', max(0, current_round - 1))

                # 计算陈旧度：当前轮次 - 客户端最后参与的轮次
                tau_k = max(0, current_round - last_round)
                self.client_staleness[client_id] = tau_k

                # 同时更新队列长度
                prev_q = self.staleness_queue.get(client_id, 0)
                beta_k = self.client_beta.get(client_id, 1)
                new_q = max(prev_q + (tau_k + 1) * (1 - beta_k) - self.tau_max, 0)
                self.staleness_queue[client_id] = new_q

                # 估计客户端的计算和通信时间
                self.estimate_client_times(client_id)

                logger.debug(f"客户端{client_id}陈旧度更新: 当前轮次={current_round}, 上次参与={last_round}, 陈旧度={tau_k}")

            # 🎯 使用纯SCAFL的贪心算法选择客户端
            available_clients = list(range(self.total_clients))

            # 过滤掉陈旧度过高的客户端
            filtered_clients = []
            for client_id in available_clients:
                tau_k = self.client_staleness.get(client_id, 0)
                if tau_k <= self.tau_max:
                    filtered_clients.append(client_id)
                else:
                    logger.debug(f"客户端{client_id}陈旧度({tau_k})超过阈值({self.tau_max})，已过滤")

            if not filtered_clients:
                logger.warning("所有客户端陈旧度都超过阈值，使用全部客户端")
                filtered_clients = available_clients

            # 使用纯SCAFL贪心选择算法
            self.selected_clients = self.pure_scafl_greedy_select_clients(
                available_clients=filtered_clients,
                staleness_dict=self.client_staleness,
                max_clients=self.clients_per_round
            )

            # 所有选中的客户端都是触发客户端（简化逻辑）
            self.trigger_clients = self.selected_clients.copy()
            self.non_trigger_clients = []

            logger.info("🎯 改进方案：已选择%s个客户端参与训练", len(self.selected_clients))
            logger.info("🎯 选中的客户端: %s", self.selected_clients)
            logger.info("🎯 所有客户端都是触发客户端，将根据上传成功/失败分别处理")

            return self.selected_clients

        except Exception as e:
            logger.error("选择客户端时出错: %s", str(e))
            self.selected_clients = []
            self.trigger_clients = []
            self.non_trigger_clients = []
            return []
            
    async def process_client_update(self, client_id, report, weights):
        """处理客户端更新"""
        try:
            # 获取或生成通信时间
            comm_time = self.client_Hk_comm.get(client_id, None)
            if comm_time is None:
                # 降低基础通信时间和噪声，提高成功率
                base_time = 0.1  # 进一步降低基础通信时间
                noise = np.random.normal(0, base_time * 0.2)  # 降低噪声
                comm_time = max(base_time + noise, 0.05)
            
            # 不管是否成功上传，都更新通信时间阈值
            self.update_comm_threshold(comm_time)
            
            # 🎯 改进：基于服务器估算的通信时间判断客户端能否顺利上传
            should_upload = self.can_upload_successfully(client_id)
            comm_time = self.client_Hk_comm.get(client_id, 0.1)
            
            # 记录通信情况
            if should_upload:
                logger.info("🎯 客户端 %s 通信时间(H_comm)：%.2f <= %.2f，可以顺利上传", client_id, comm_time, self.upload_threshold)
            else:
                logger.info("🎯 客户端 %s 通信时间(H_comm)：%.2f > %.2f，无法顺利上传，需要蒸馏补偿", client_id, comm_time, self.upload_threshold)
                
            # 检查weights是否有效
            if weights is None or not isinstance(weights, dict) or len(weights) == 0:
                logger.error(f"客户端{client_id}上传的weights无效，已丢弃")
                should_upload = False
            
            if should_upload:
                self.client_upload_success[client_id] = True
                update_record = {
                    'client_id': client_id,
                    'weights': weights,
                    'report': report,
                    'is_distilled': False
                }
                self.success_buffer_pool.append(update_record)
                if client_id not in self.success_buffered_clients:
                    self.success_buffered_clients.append(client_id)
                logger.info("客户端 %s 的更新已加入成功缓冲池", client_id)
                
                # 检查是否为触发客户端，并更新进度
                if client_id in self.trigger_clients:
                    trigger_success_count = len([cid for cid in self.trigger_clients if cid in self.success_buffered_clients])
                    logger.info("触发客户端 %s 已成功上传 (%s/%s)",
                               client_id, trigger_success_count, len(self.trigger_clients))
                    if trigger_success_count == len(self.trigger_clients):
                        logger.info("所有触发客户端均已成功上传，可以触发聚合")
            else:
                self.client_upload_success[client_id] = False
                logger.warning("🎯 客户端 %s 无法顺利上传 | 通信时间(H_comm): %f秒 > 阈值: %f秒", client_id, comm_time, self.upload_threshold)
                # 健壮性增强：检查 global_model_cache 和 client
                client = self.clients.get(client_id)
                if client is None:
                    logger.error("客户端 %s 蒸馏补偿失败：client为None", client_id)
                elif self.global_model_cache is None:
                    logger.error("客户端 %s 蒸馏补偿失败：global_model_cache为None", client_id)
                else:
                    try:
                        pseudo_gradient = client.distill_pseudo_gradient(self.global_model_cache)
                        if pseudo_gradient is not None:
                            # 计算伪梯度的正确统计信息
                            if isinstance(pseudo_gradient, dict):
                                total_params = sum(torch.numel(v) for v in pseudo_gradient.values())
                                all_grads = torch.cat([v.flatten() for v in pseudo_gradient.values()])
                                grad_mean = all_grads.mean().item()
                                grad_norm = torch.norm(all_grads).item()
                                logger.info("客户端 %s 生成伪梯度成功，参数数: %s, 均值: %.6f, 范数: %.6f",
                                          client_id, total_params, grad_mean, grad_norm)
                            else:
                                logger.info("客户端 %s 生成伪梯度成功，类型: %s", client_id, type(pseudo_gradient).__name__)
                            distill_record = {
                                'client_id': client_id,
                                'weights': pseudo_gradient,
                                'report': report,
                                'is_distilled': True
                            }
                            self.distill_buffer_pool.append(distill_record)
                            if client_id not in self.distill_buffered_clients:
                                self.distill_buffered_clients.append(client_id)
                            logger.info("客户端 %s 蒸馏伪梯度已加入蒸馏缓冲池", client_id)
                        else:
                            logger.warning("客户端 %s 生成伪梯度失败，返回值为None", client_id)
                    except Exception as e:
                        logger.error("客户端 %s 蒸馏补偿失败: %s (global_model_cache类型: %s, client类型: %s)", client_id, str(e), type(self.global_model_cache).__name__, type(client).__name__)
            
            logger.info("[异步统计] 当前已成功上传: %s个, 失败: %s个, 成功ID: %s",
                      len(self.success_buffered_clients),
                      list(self.client_upload_success.values()).count(False),
                      self.success_buffered_clients)
                      
            # 检查是否可以触发聚合
            if self.should_trigger_aggregation():
                logger.info("满足聚合条件，开始聚合...")
                await self.aggregate_models()
                
            return True
        except Exception as e:
            logger.error("处理客户端 %s 更新时出错: %s", client_id, str(e))
            import traceback
            logger.error("详细错误堆栈: %s", traceback.format_exc())
            return False





    def update_accuracy_history(self, accuracy):
        """更新准确率历史记录"""
        self.accuracy_history.append({
            'round': self.current_round,
            'accuracy': accuracy,
            'success_weight': self.success_weight,
            'distill_weight': self.distill_weight
        })

        # 保持历史记录在合理范围内
        if len(self.accuracy_history) > 20:
            self.accuracy_history = self.accuracy_history[-20:]

    def calculate_performance_trend(self):
        """计算最近几轮的性能趋势"""
        if len(self.accuracy_history) < self.weight_adaptation_window:
            return 0.0  # 数据不足，无法计算趋势

        # 获取最近的性能数据
        recent_data = self.accuracy_history[-self.weight_adaptation_window:]

        # 计算线性趋势（简单的斜率计算）
        x = list(range(len(recent_data)))
        y = [data['accuracy'] for data in recent_data]

        # 计算斜率 (简单线性回归)
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] * x[i] for i in range(n))

        if n * sum_x2 - sum_x * sum_x == 0:
            return 0.0

        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        return slope


    def should_trigger_aggregation(self):
        """
        检查是否应该触发聚合
        修复：更宽松的聚合触发条件，避免过度等待导致训练停滞
        """
        success_count = len(self.success_buffered_clients)
        distill_count = len(self.distill_buffered_clients)
        total_count = success_count + distill_count

        # 条件1：有足够的成功上传客户端（优先条件）
        # 降低阈值，只要有一定比例的客户端成功上传即可
        min_success_clients = max(1, self.clients_per_round // 2)  # 至少1个，或者一半的客户端
        if success_count >= min_success_clients:
            logger.info("条件1满足：成功上传客户端数量充足 (%s >= %s)", success_count, min_success_clients)
            return True

        # 条件2：成功上传 + 蒸馏补偿达到阈值
        if total_count >= self.clients_per_round:
            logger.info("条件2满足：总更新数量充足 (%s >= %s)", total_count, self.clients_per_round)
            return True

        # 条件3：有一定数量的更新且等待时间过长（避免无限等待）
        # 降低更新数量要求，只要有任何更新且等待足够长即可
        if total_count >= 1:
            # 记录第一个更新的时间
            if not hasattr(self, 'first_update_time'):
                self.first_update_time = time.time()

            wait_time = time.time() - self.first_update_time
            if wait_time > 15:  # 减少等待时间，从30秒降低到15秒
                logger.info("条件3满足：等待时间过长 (%.1f秒)，强制触发聚合", wait_time)
                return True

        # 条件4：检查原始的触发客户端逻辑（作为备用）
        if self.trigger_clients:
            trigger_success_count = sum(1 for cid in self.trigger_clients if cid in self.success_buffered_clients)
            trigger_distill_count = sum(1 for cid in self.trigger_clients if cid in self.distill_buffered_clients)
            trigger_completed = trigger_success_count + trigger_distill_count

            if trigger_completed > 0 and trigger_completed >= len(self.trigger_clients) // 2:
                logger.info("条件4满足：足够的触发客户端已完成 (%s/%s)", trigger_completed, len(self.trigger_clients))
                return True

        # 条件5：超时保障 - 如果很长时间没有聚合，强制聚合
        if not hasattr(self, 'last_aggregation_time'):
            self.last_aggregation_time = time.time()
        
        time_since_last_agg = time.time() - self.last_aggregation_time
        if time_since_last_agg > 60:  # 60秒没有聚合，强制聚合
            logger.info("条件5满足：距离上次聚合时间过长 (%.1f秒)，强制触发聚合", time_since_last_agg)
            return True

        # 记录当前状态，便于调试
        logger.debug("聚合条件未满足 - 成功:%s, 蒸馏:%s, 总计:%s, 需要:%s",
                    success_count, distill_count, total_count, self.clients_per_round)
        return False

    def assess_environment(self):
        """🌐 评估当前环境状况"""
        try:
            # 计算通信失败率
            total_attempts = len(self.recent_performance_history)
            if total_attempts > 0:
                failed_attempts = sum(1 for perf in self.recent_performance_history if perf.get('communication_failed', False))
                self.environment_metrics['communication_failure_rate'] = failed_attempts / total_attempts

            # 计算网络不稳定性 (基于延迟变化)
            if len(self.staleness_history) > 5:
                recent_staleness = self.staleness_history[-10:]
                staleness_std = np.std(recent_staleness) if len(recent_staleness) > 1 else 0
                self.environment_metrics['network_instability'] = min(staleness_std / 5.0, 1.0)  # 归一化到[0,1]

            # 计算设备异构性 (基于客户端性能差异)
            if len(self.client_beta) > 1:
                beta_values = list(self.client_beta.values())
                beta_variance = np.var(beta_values) if len(beta_values) > 1 else 0
                self.environment_metrics['device_heterogeneity'] = min(beta_variance * 4, 1.0)  # 归一化

            # 计算数据质量差异 (简化版本，基于准确度方差)
            if len(self.accuracy_log) > 5:
                recent_acc = [acc for acc, _ in self.accuracy_log[-10:]]
                acc_variance = np.var(recent_acc) if len(recent_acc) > 1 else 0
                self.environment_metrics['data_quality_variance'] = min(acc_variance * 10, 1.0)  # 归一化

            logger.info("🌐 环境评估结果: 失败率=%.3f, 不稳定性=%.3f, 异构性=%.3f, 质量差异=%.3f",
                       self.environment_metrics['communication_failure_rate'],
                       self.environment_metrics['network_instability'],
                       self.environment_metrics['device_heterogeneity'],
                       self.environment_metrics['data_quality_variance'])

        except Exception as e:
            logger.error("环境评估失败: %s", str(e))

    def update_enhancement_status(self):
        """🔧 根据环境评估结果更新增强功能状态"""
        try:
            # 获取阈值
            failure_threshold = self.environment_thresholds.get('communication_failure_rate', 0.2)
            quality_threshold = self.environment_thresholds.get('data_quality_variance', 0.3)
            instability_threshold = self.environment_thresholds.get('network_instability', 0.3)

            # 更新知识蒸馏状态
            should_enable_distillation = self.environment_metrics['communication_failure_rate'] > failure_threshold
            if should_enable_distillation != self.active_enhancements['knowledge_distillation']:
                self.active_enhancements['knowledge_distillation'] = should_enable_distillation
                if should_enable_distillation:
                    self.distill_weight = self.enhancement_parameters.get('max_distillation_weight', 0.15)
                    logger.info("🔧 启用知识蒸馏补偿 (权重=%.3f)", self.distill_weight)
                else:
                    self.distill_weight = 0.0
                    logger.info("🔧 关闭知识蒸馏补偿")

            # 更新质量感知权重状态
            should_enable_quality = self.environment_metrics['data_quality_variance'] > quality_threshold
            if should_enable_quality != self.active_enhancements['quality_aware_weighting']:
                self.active_enhancements['quality_aware_weighting'] = should_enable_quality
                logger.info("🔧 %s质量感知权重", "启用" if should_enable_quality else "关闭")

            # 更新自适应聚合状态
            should_enable_adaptive = self.environment_metrics['network_instability'] > instability_threshold
            if should_enable_adaptive != self.active_enhancements['adaptive_aggregation']:
                self.active_enhancements['adaptive_aggregation'] = should_enable_adaptive
                logger.info("🔧 %s自适应聚合", "启用" if should_enable_adaptive else "关闭")

            # 记录当前增强状态
            active_count = sum(self.active_enhancements.values())
            if active_count == 0:
                logger.info("🎯 当前模式: 纯SCAFL (环境良好)")
            else:
                active_features = [k for k, v in self.active_enhancements.items() if v]
                logger.info("🧠 当前模式: SCAFL + %s", ", ".join(active_features))

        except Exception as e:
            logger.error("更新增强功能状态失败: %s", str(e))

    def should_perform_assessment(self):
        """判断是否应该进行环境评估"""
        try:
            # 确保 environment_monitoring 属性存在
            if not hasattr(self, 'environment_monitoring'):
                self.environment_monitoring = {'enable': False}
                
            # 确保 assessment_window 属性存在
            if not hasattr(self, 'assessment_window'):
                self.assessment_window = 10
                
            # 确保 rounds_since_assessment 属性存在
            if not hasattr(self, 'rounds_since_assessment'):
                self.rounds_since_assessment = 0
                
            return (self.environment_monitoring.get('enable', False) and
                    self.rounds_since_assessment >= self.assessment_window)
        except Exception as e:
            logger.error(f"检查是否应该进行环境评估时出错: {str(e)}")
            return False  # 出错时默认不进行评估

    async def _process_reports(self):
        """
        重写Plato框架的_process_reports方法，调用我们的自定义聚合逻辑
        """
        logger.info("🎯 ReFedScaFL _process_reports 被调用，开始自定义聚合逻辑")

        try:
            # 调用我们的自定义聚合方法
            await self.aggregate_models()

            # 调用父类的客户端处理完成方法
            self.clients_processed()

        except Exception as e:
            logger.error(f"🎯 自定义聚合过程中发生错误: {str(e)}", exc_info=True)
            # 出错时回退到父类方法
            await super()._process_reports()

    async def aggregate_models(self):
        """
        聚合模型更新
        功能:
            1. 使用贪心选择算法选择最佳聚合子集A_t
            2. 对成功上传的客户端进行正常聚合
            3. 对上传失败的客户端进行知识蒸馏补偿
            4. 更新全局模型并清空缓冲池
        """
        try:
            logger.info("🎯 开始ReFedScaFL聚合逻辑")

            # 🎯 处理Plato框架的updates数据结构
            if not hasattr(self, 'updates') or not self.updates:
                logger.warning("🎯 没有收到客户端更新，跳过聚合")
                return

            # 🎯 根据通信时间判断客户端能否顺利上传
            success_updates = []  # 顺利上传的客户端
            distill_updates = []  # 无法顺利上传的客户端

            for update in self.updates:
                client_id = update.client_id

                # 估算客户端通信时间
                self.estimate_client_times(client_id)

                # 🎯 判断客户端能否顺利上传
                can_upload = self.can_upload_successfully(client_id)
                comm_time = self.client_Hk_comm.get(client_id, 0.1)

                if can_upload:
                    success_updates.append(update)
                    logger.info("🎯 客户端 %s 通信时间(H_comm)：%.2f <= %.2f，可以顺利上传",
                              client_id, comm_time, self.upload_threshold)
                else:
                    distill_updates.append(update)
                    logger.info("🎯 客户端 %s 通信时间(H_comm)：%.2f > %.2f，无法顺利上传，需要蒸馏补偿",
                              client_id, comm_time, self.upload_threshold)

            logger.info("🎯 客户端处理结果 - 顺利上传: %d个, 无法顺利上传: %d个",
                       len(success_updates), len(distill_updates))
            # 🎯 对顺利上传的客户端使用贪心算法选择聚合子集
            if success_updates:
                logger.info(f"🎯 对 {len(success_updates)} 个顺利上传的客户端进行贪心选择")
                selected_success_updates = self.greedy_select_aggregation_subset(success_updates)
                logger.info(f"🎯 贪心选择结果：选中 {len(selected_success_updates)} 个顺利上传的客户端")
                success_client_ids = [update.client_id for update in selected_success_updates]
                logger.info(f"🎯 选中的顺利上传客户端ID: {success_client_ids}")
            else:
                selected_success_updates = []
                logger.info("🎯 没有顺利上传的客户端")

            # 🎯 对无法顺利上传的客户端，暂时不做处理（按您的要求）
            if distill_updates:
                logger.info(f"🎯 检测到 {len(distill_updates)} 个无法顺利上传的客户端，暂时不做处理")
                distill_client_ids = [update.client_id for update in distill_updates]
                logger.info(f"🎯 无法顺利上传的客户端ID: {distill_client_ids}")
                # 暂时不处理蒸馏补偿客户端
                selected_distill_updates = []
            else:
                selected_distill_updates = []
                logger.info("🎯 没有无法顺利上传的客户端")

            # 🎯 记录聚合信息
            logger.info("🎯 客户端处理完成:")
            logger.info(f"🎯 - 选中顺利上传: {len(selected_success_updates)}个客户端")
            logger.info(f"🎯 - 暂不处理蒸馏补偿: {len(distill_updates)}个客户端")

            # 🎯 使用选中的子集进行后续聚合
            if selected_success_updates:
                # 更新self.updates为选中的客户端，用于标准Plato聚合
                self.updates = selected_success_updates
                logger.info("🎯 聚合开始 - 选中的顺利上传客户端: %s个", len(selected_success_updates))

                # 记录参与聚合的客户端ID列表
                success_client_ids = [update.client_id for update in selected_success_updates]
                logger.info(f"🎯 本轮参与聚合的客户端ID列表: {success_client_ids}")

                # 🎯 调用标准Plato聚合逻辑
                await super()._process_reports()

            else:
                logger.warning("🎯 没有选中任何客户端进行聚合")

            # 🎯 记录蒸馏补偿客户端信息（暂不处理）
            if distill_updates:
                distill_client_ids = [update.client_id for update in distill_updates]
                logger.info(f"🎯 本轮检测到但暂不处理的蒸馏补偿客户端: {distill_client_ids}")


        except Exception as e:
            logger.error(f"🎯 聚合过程中发生错误: {str(e)}", exc_info=True)

        except Exception as e:
            logger.error("🎯 聚合模型时出错: %s", str(e))
            import traceback
            logger.error("🎯 详细错误堆栈: %s", traceback.format_exc())

    def _calculate_data_quality_score(self, client_id):
        """
        计算客户端数据质量得分
        基于数据量、类别分布等因素
        """
        try:
            # 获取客户端数据信息（如果可用）
            if hasattr(self, 'client_data_info') and client_id in self.client_data_info:
                data_info = self.client_data_info[client_id]
                data_size = data_info.get('size', 600)  # 默认数据量
                class_diversity = data_info.get('class_diversity', 0.5)  # 类别多样性

                # 数据量得分（归一化到[0,1]）
                size_score = min(1.0, data_size / 1000.0)

                # 类别多样性得分
                diversity_score = class_diversity

                # 综合数据质量得分
                quality_score = 0.6 * size_score + 0.4 * diversity_score
            else:
                # 默认得分
                quality_score = 0.5

            return quality_score

        except Exception as e:
            logger.warning(f"计算客户端{client_id}数据质量得分失败: {e}")
            return 0.5

    def _calculate_communication_efficiency(self, client_id):
        """
        计算客户端通信效率得分
        基于历史通信时间和成功率
        """
        try:
            # 获取历史通信数据
            if hasattr(self, 'client_comm_history') and client_id in self.client_comm_history:
                comm_history = self.client_comm_history[client_id]
                avg_comm_time = sum(comm_history) / len(comm_history) if comm_history else 1.0

                # 通信效率得分（通信时间越短得分越高）
                efficiency_score = max(0.1, min(1.0, 2.0 / (1.0 + avg_comm_time)))
            else:
                # 使用当前通信成功率作为代理
                beta_k = self.client_beta.get(client_id, 0.8)
                efficiency_score = beta_k

            return efficiency_score

        except Exception as e:
            logger.warning(f"计算客户端{client_id}通信效率得分失败: {e}")
            return 0.5

    def _calculate_participation_diversity(self, client_id):
        """
        计算客户端参与多样性得分
        避免总是选择相同的客户端
        """
        try:
            # 获取最近参与历史
            if hasattr(self, 'client_participation_history'):
                if client_id not in self.client_participation_history:
                    self.client_participation_history[client_id] = []
            else:
                self.client_participation_history = {client_id: []}

            recent_participation = self.client_participation_history[client_id]

            # 计算最近参与频率（最近10轮）
            recent_rounds = recent_participation[-10:] if len(recent_participation) >= 10 else recent_participation
            participation_rate = len(recent_rounds) / 10.0 if recent_rounds else 0.0

            # 多样性得分（参与率越低得分越高，鼓励多样性）
            diversity_score = max(0.1, 1.0 - participation_rate)

            return diversity_score

        except Exception as e:
            logger.warning(f"计算客户端{client_id}参与多样性得分失败: {e}")
            return 0.5

    def _calculate_model_consistency_factor(self, success_updates, global_weights):
        """
        计算模型一致性因子 - 评估客户端更新与全局模型的兼容性

        参数:
            success_updates: 成功上传的客户端更新列表
            global_weights: 当前全局模型权重

        返回:
            consistency_factor: 一致性因子 (0.0-1.0)
        """
        try:
            if not success_updates or not global_weights:
                return 0.5

            import torch
            import numpy as np

            consistency_scores = []

            for update_record in success_updates:
                client_id = update_record['client_id']
                client_weights = update_record['weights']

                if not client_weights:
                    continue

                # 1. 计算梯度方向一致性
                gradient_consistency = self._calculate_gradient_consistency(
                    client_weights, global_weights
                )

                # 2. 计算参数变化幅度合理性
                magnitude_reasonableness = self._calculate_magnitude_reasonableness(
                    client_weights, global_weights
                )

                # 3. 计算权重分布相似性
                distribution_similarity = self._calculate_distribution_similarity(
                    client_weights, global_weights
                )

                # 综合一致性得分（使用配置参数）
                gradient_weight = getattr(self.algorithm, 'consistency_gradient_weight', 0.4)
                magnitude_weight = getattr(self.algorithm, 'consistency_magnitude_weight', 0.3)
                distribution_weight = getattr(self.algorithm, 'consistency_distribution_weight', 0.3)

                client_consistency = (
                    gradient_weight * gradient_consistency +      # 梯度方向
                    magnitude_weight * magnitude_reasonableness + # 变化幅度
                    distribution_weight * distribution_similarity # 分布相似性
                )

                consistency_scores.append(client_consistency)

                logger.debug(f"客户端{client_id}一致性评估: "
                           f"梯度一致性={gradient_consistency:.3f}, "
                           f"幅度合理性={magnitude_reasonableness:.3f}, "
                           f"分布相似性={distribution_similarity:.3f}, "
                           f"综合一致性={client_consistency:.3f}")

            if not consistency_scores:
                return 0.5

            # 计算平均一致性因子
            avg_consistency = np.mean(consistency_scores)

            # 添加一致性方差惩罚（鼓励客户端间一致性）
            consistency_variance = np.var(consistency_scores) if len(consistency_scores) > 1 else 0
            penalty_coeff = getattr(self.algorithm, 'consistency_variance_penalty', 0.2)
            variance_penalty = min(penalty_coeff, consistency_variance * 2)  # 使用配置的惩罚系数

            final_consistency = max(0.0, min(1.0, avg_consistency - variance_penalty))

            logger.info(f"🔗 模型一致性评估: 平均一致性={avg_consistency:.3f}, "
                       f"方差惩罚={variance_penalty:.3f}, 最终一致性={final_consistency:.3f}")

            return final_consistency

        except Exception as e:
            logger.warning(f"计算模型一致性因子失败: {e}")
            return 0.5

    def _calculate_gradient_consistency(self, client_weights, global_weights):
        """计算梯度方向一致性"""
        try:
            import torch

            # 计算客户端更新方向（梯度）
            client_gradients = []
            global_norms = []

            for key in client_weights.keys():
                if key in global_weights:
                    # 计算更新方向（客户端权重 - 全局权重）
                    client_grad = client_weights[key] - global_weights[key]
                    client_gradients.append(client_grad.flatten())
                    global_norms.append(torch.norm(global_weights[key]).item())

            if not client_gradients:
                return 0.5

            # 合并所有梯度
            all_client_grads = torch.cat(client_gradients)

            # 计算梯度范数
            grad_norm = torch.norm(all_client_grads).item()

            # 基于梯度范数的一致性评分（适中的梯度范数最好）
            # 使用高斯函数，最优范数在0.1-1.0之间
            optimal_norm = 0.5
            import math
            consistency = math.exp(-((grad_norm - optimal_norm) ** 2) / (2 * 0.3 ** 2))

            return max(0.0, min(1.0, consistency))

        except Exception as e:
            logger.debug(f"计算梯度一致性失败: {e}")
            return 0.5

    def _calculate_magnitude_reasonableness(self, client_weights, global_weights):
        """计算参数变化幅度合理性"""
        try:
            import torch

            relative_changes = []

            for key in client_weights.keys():
                if key in global_weights:
                    global_param = global_weights[key]
                    client_param = client_weights[key]

                    # 计算相对变化
                    param_diff = torch.abs(client_param - global_param)
                    global_magnitude = torch.abs(global_param) + 1e-8  # 避免除零
                    relative_change = (param_diff / global_magnitude).mean().item()

                    relative_changes.append(relative_change)

            if not relative_changes:
                return 0.5

            # 计算平均相对变化
            avg_relative_change = sum(relative_changes) / len(relative_changes)

            # 合理性评分：适中的变化最好（0.01-0.1之间）
            if 0.01 <= avg_relative_change <= 0.1:
                reasonableness = 1.0
            elif avg_relative_change < 0.01:
                reasonableness = avg_relative_change / 0.01  # 变化太小
            else:  # avg_relative_change > 0.1
                reasonableness = max(0.0, 1.0 - (avg_relative_change - 0.1) / 0.5)  # 变化太大

            return max(0.0, min(1.0, reasonableness))

        except Exception as e:
            logger.debug(f"计算幅度合理性失败: {e}")
            return 0.5

    def _calculate_distribution_similarity(self, client_weights, global_weights):
        """计算权重分布相似性"""
        try:
            import torch

            similarities = []

            for key in client_weights.keys():
                if key in global_weights:
                    global_param = global_weights[key].flatten()
                    client_param = client_weights[key].flatten()

                    # 计算余弦相似度
                    dot_product = torch.dot(global_param, client_param).item()
                    global_norm = torch.norm(global_param).item()
                    client_norm = torch.norm(client_param).item()

                    if global_norm > 0 and client_norm > 0:
                        cosine_sim = dot_product / (global_norm * client_norm)
                        # 将余弦相似度从[-1,1]映射到[0,1]
                        similarity = (cosine_sim + 1.0) / 2.0
                        similarities.append(similarity)

            if not similarities:
                return 0.5

            # 计算平均相似度
            avg_similarity = sum(similarities) / len(similarities)

            return max(0.0, min(1.0, avg_similarity))

        except Exception as e:
            logger.debug(f"计算分布相似性失败: {e}")
            return 0.5

    def update_client_states(self, client_id, report):
        """
        更新客户端状态
        参数:
            client_id: 客户端ID
            report: 训练报告
        """
        try:
            # 确保客户端状态字典已初始化
            if client_id not in self.client_states:
                self.client_states[client_id] = {
                    'last_round': 0,
                    'timestamp': time.time(),
                    'is_upload_failed': False
                }
            
            # 注意：不重复计算陈旧度，因为已在选择客户端时预先计算
            # 仅更新客户端最后一次成功参与的轮次记录
            
            # 更新通信成功率
            if hasattr(report, 'comm_success'):
                self.client_beta[client_id] = report.comm_success
            
            # 更新客户端状态
            self.client_states[client_id] = {
                'last_round': self.current_round,  # 更新最后一次成功的轮次
                'timestamp': time.time(),
                'is_upload_failed': False
            }
            
            logger.debug("客户端%s状态已更新：最后成功轮次=%s, 通信成功率=%.2f",
                        client_id, self.current_round,
                        self.client_beta.get(client_id, 0))
            
        except Exception as e:
            logger.error("更新客户端%s状态时出错: %s", client_id, str(e))