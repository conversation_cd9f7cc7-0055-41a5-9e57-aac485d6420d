# FedBuff输出路径配置更改

## 🎯 配置更改目标

将FedBuff的结果文件和模型文件输出到FedBuff目录下，而不是plato根目录下。

## 📁 路径配置更改

### **原始配置 (之前)**
```yaml
# 输出到plato根目录
results:
    result_path: results/mnist_original_fedbuff/01

server:
    checkpoint_path: models/mnist_original_fedbuff/01
    model_path: models/mnist_original_fedbuff/01
```

### **新配置 (现在)**
```yaml
# 输出到FedBuff目录
results:
    result_path: examples/async/fedbuff/results/mnist_original_fedbuff/01

server:
    checkpoint_path: examples/async/fedbuff/models/mnist_original_fedbuff/01
    model_path: examples/async/fedbuff/models/mnist_original_fedbuff/01
```

## 🔧 已修改的配置文件

### **1. fedbuff_MNIST_original.yml**
```diff
- result_path: results/mnist_original_fedbuff/01
+ result_path: examples/async/fedbuff/results/mnist_original_fedbuff/01

- checkpoint_path: models/mnist_original_fedbuff/01
- model_path: models/mnist_original_fedbuff/01
+ checkpoint_path: examples/async/fedbuff/models/mnist_original_fedbuff/01
+ model_path: examples/async/fedbuff/models/mnist_original_fedbuff/01
```

### **2. fedbuff_MNIST_standard.yml**
```diff
- result_path: results/mnist_standard_fedbuff/01
+ result_path: examples/async/fedbuff/results/mnist_standard_fedbuff/01

- checkpoint_path: models/mnist_standard_fedbuff/01
- model_path: models/mnist_standard_fedbuff/01
+ checkpoint_path: examples/async/fedbuff/models/mnist_standard_fedbuff/01
+ model_path: examples/async/fedbuff/models/mnist_standard_fedbuff/01
```

### **3. fedbuff_MNIST_network_test.yml**
```diff
- result_path: results/mnist_network_test_fedbuff/01
+ result_path: examples/async/fedbuff/results/mnist_network_test_fedbuff/01

- checkpoint_path: models/mnist_network_test_fedbuff/01
- model_path: models/mnist_network_test_fedbuff/01
+ checkpoint_path: examples/async/fedbuff/models/mnist_network_test_fedbuff/01
+ model_path: examples/async/fedbuff/models/mnist_network_test_fedbuff/01
```

## 📂 创建的目录结构

```
plato-main/examples/async/fedbuff/
├── results/
│   ├── mnist_original_fedbuff/01/
│   ├── mnist_standard_fedbuff/01/
│   └── mnist_network_test_fedbuff/01/
└── models/
    ├── mnist_original_fedbuff/01/
    ├── mnist_standard_fedbuff/01/
    └── mnist_network_test_fedbuff/01/
```

## 🎯 预期输出位置

### **原始版本**
```
输出位置: examples/async/fedbuff/results/mnist_original_fedbuff/01/
文件名: [服务器ID].csv (如: 2764.csv)
```

### **标准增强版**
```
输出位置: examples/async/fedbuff/results/mnist_standard_fedbuff/01/
文件名: fedbuff_MNIST_standard_YYYYMMDD_HHMM.csv
```

### **网络测试版**
```
输出位置: examples/async/fedbuff/results/mnist_network_test_fedbuff/01/
文件名: fedbuff_MNIST_network_test_YYYYMMDD_HHMM.csv
```

## 🚀 运行命令

### **在FedBuff目录下运行**
```bash
cd examples/async/fedbuff

# 原始版本
python fedbuff_original.py -c fedbuff_MNIST_original.yml

# 标准增强版
python fedbuff.py -c fedbuff_MNIST_standard.yml

# 网络测试版
python fedbuff.py -c fedbuff_MNIST_network_test.yml
```

### **在plato根目录下运行**
```bash
# 原始版本
python examples/async/fedbuff/fedbuff_original.py -c examples/async/fedbuff/fedbuff_MNIST_original.yml

# 标准增强版
python examples/async/fedbuff/fedbuff.py -c examples/async/fedbuff/fedbuff_MNIST_standard.yml

# 网络测试版
python examples/async/fedbuff/fedbuff.py -c examples/async/fedbuff/fedbuff_MNIST_network_test.yml
```

## 📊 文件组织优势

### **集中管理**
✅ **所有FedBuff相关文件在一个目录下**  
✅ **便于实验管理和文件查找**  
✅ **避免与其他算法文件混淆**  
✅ **便于打包和分享实验结果**  

### **清晰结构**
```
fedbuff/
├── 源代码文件 (.py)
├── 配置文件 (.yml)
├── results/ (结果文件)
├── models/ (模型文件)
└── 文档和脚本
```

### **版本对比**
```
fedbuff/results/
├── mnist_original_fedbuff/01/     # 原始版本结果
├── mnist_standard_fedbuff/01/     # 标准增强版结果
└── mnist_network_test_fedbuff/01/ # 网络测试版结果
```

## 🔍 验证方法

### **检查目录结构**
```bash
ls -la examples/async/fedbuff/results/
ls -la examples/async/fedbuff/models/
```

### **运行测试**
```bash
cd examples/async/fedbuff
python fedbuff_original.py -c fedbuff_MNIST_original.yml
```

### **检查输出**
```bash
ls -la results/mnist_original_fedbuff/01/
```

## 🎉 配置完成

✅ **路径配置已更新**: 所有配置文件已修改  
✅ **目录结构已创建**: 必要的目录已创建  
✅ **准备就绪**: 可以开始运行实验  

现在FedBuff的所有输出文件都会保存在FedBuff目录下，便于管理和对比分析！

---

*配置更改完成时间: 2025-01-21*  
*目录结构: ✅ 已创建*  
*配置文件: ✅ 已更新*  
*准备状态: ✅ 就绪*
