"""
The registry that contains all available federated learning clients.

Having a registry of all available classes is convenient for retrieving an instance based
on a configuration at run-time.
"""
import logging

from plato.config import Config
from plato.clients import (
    self_supervised_learning,
    simple,
    mistnet,
    fedavg_personalized,
    split_learning,
)

registered_clients = {
    "simple": simple.Client,
    "mistnet": mistnet.Client,
    "fedavg_personalized": fedavg_personalized.Client,
    "self_supervised_learning": self_supervised_learning.Client,
    "split_learning": split_learning.Client,
}


def get(model=None, datasource=None, algorithm=None, trainer=None):
    """Get an instance of the server.""" # 获取客户端的实例
    if hasattr(Config().clients, "type"):  # 如果配置文件中包含客户端的类型
        client_type = Config().clients.type  # 获取客户端的类型
    else:
        client_type = Config().algorithm.type  # 如果配置文件中不包含客户端的类型，使用算法的类型

    if client_type in registered_clients:  # 如果客户端的类型在注册的客户端中
        logging.info("Client: %s", client_type)  # 打印客户端的类型
        registered_client = registered_clients[client_type](  # 获取注册的客户端的实例
            model=model, datasource=datasource, algorithm=algorithm, trainer=trainer
        )
    else:  # 如果客户端的类型不在注册的客户端中
        raise ValueError(f"No such client: {client_type}")  # 抛出异常

    return registered_client
