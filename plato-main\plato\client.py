"""
Starting point for a Plato federated learning client.
"""

import asyncio
import logging
import os

from plato.clients import registry as client_registry
from plato.config import Config

# 启动用户进程，主要调用plato\clients\base.py中的start_client函数
def run(client_id, port, client=None, edge_server=None, edge_client=None, trainer=None):     
    """Starting a client to connect to the server."""   # 启动一个客户端，连接到服务器
    Config().args.id = client_id   # 客户端的id
    if port is not None:
        Config().args.port = port   # 客户端的端口号

    # If a server needs to be running concurrently
    if Config().is_edge_server():  # 如果是边缘服务器
        Config().trainer = Config().trainer._replace(  # 替换训练器的参数
            rounds=Config().algorithm.local_rounds
        )

        if edge_server is None:  # 如果边缘服务器为空
            from plato.clients import edge  # 导入边缘客户端
            from plato.servers import fedavg_cs     # 导入异步联邦平均服务器

            server = fedavg_cs.Server()  # 启动一个异步联邦平均服务器
            client = edge.Client(server)  # 启动一个边缘客户端
        else:
            # A customized edge server 
            if trainer is not None: # 如果训练器不为空
                server = edge_server(trainer=trainer())  # 启动一个自定义的边缘服务器
            else:
                server = edge_server() 
            client = edge_client(server)  # 启动一个自定义的边缘客户端

        server.configure()  # 配置服务器
        client.configure()  # 配置客户端

        logging.info("Starting an edge server as client #%d", Config().args.id) 
        asyncio.ensure_future(client.start_client())  # 启动客户端

        logging.info( 
            "Starting an edge server as server #%d on port %d",
            os.getpid(),  # 客户端的进程id
            Config().args.port,
        )
        server.start(port=Config().args.port)  # 启动服务器

    else:
        if client is None:
            client = client_registry.get()  # 获取客户端的实例
            logging.info("Starting a %s client #%d.", Config().clients.type, client_id)
        else:
            # 启动用户
            client.client_id = client_id
            logging.info("Starting a custom client #%d", client_id)

        client.configure() 

        loop = asyncio.get_event_loop() 
        # 调用plato\clients\base.py中的start_client函数
        loop.run_until_complete(client.start_client())


if __name__ == "__main__":
    run(Config().args.id, Config().args.port)
