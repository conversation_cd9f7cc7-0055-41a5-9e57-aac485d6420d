"""
FedSCAFL函数测试工具 - 用于单独测试关键函数
"""
import logging
import asyncio
import time
import random
import numpy as np
import os
import sys

# 添加Plato库路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from types import SimpleNamespace

# 设置日志记录
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fedscafl_functions_test.log')
    ]
)

logger = logging.getLogger("FedSCAFL.FunctionTest")

try:
    import fedscafl_server
    import fedscafl_algorithm
    logger.info("✓ 模块导入成功")
except ImportError as e:
    logger.error("✗ 模块导入失败: %s", str(e))
    import traceback
    traceback.print_exc()
    exit(1)

class MockReport:
    """模拟客户端上传的报告"""
    def __init__(self, client_id, round_number, payload=None):
        self.client_id = client_id
        self.round = round_number
        self.payload = payload or {}

class MockTrainer:
    """模拟训练器"""
    def __init__(self):
        self.model = "mock_model"
    
    def __str__(self):
        return "MockTrainer"

class FunctionsTest:
    """测试FedSCAFL服务器中的关键函数"""
    
    def __init__(self):
        """初始化测试环境"""
        logger.info("初始化测试环境")
        self.algorithm_class = fedscafl_algorithm.Algorithm
        self.trainer = MockTrainer()
        self.algorithm = fedscafl_algorithm.Algorithm(self.trainer)
        self.server = fedscafl_server.Server(algorithm=self.algorithm_class)
    
    async def test_get_dkt(self):
        """测试get_dkt函数"""
        logger.info("测试get_dkt函数")
        
        # 设置必要的前提条件
        cid = 1
        self.server.client_Hk[cid] = 5.0  # 设置客户端计算+通信时间
        self.server.client_staleness[cid] = 2  # 设置客户端过时程度
        self.server.historical_Ds = [0.5, 0.6, 0.7]  # 设置历史聚合时间
        
        # 调用函数
        d_k_t = self.server.get_dkt(cid)
        logger.info(f"客户端{cid}的过时度: {d_k_t}")
        
        return d_k_t
    
    async def test_estimate_client_times(self):
        """测试estimate_client_times函数"""
        logger.info("测试estimate_client_times函数")
        
        cid = 2
        
        # 调用函数
        self.server.estimate_client_times(cid)
        
        # 检查结果
        H_comp = self.server.client_Hk_comp.get(cid, 0)
        H_comm = self.server.client_Hk_comm.get(cid, 0)
        H_k = self.server.client_Hk.get(cid, 0)
        
        logger.info(f"客户端{cid}时间估计: 计算时间={H_comp}秒, 通信时间={H_comm}秒, 总时间={H_k}秒")
        
        return H_k
    
    async def test_select_clients_by_scafl(self):
        """测试select_clients_by_scafl函数"""
        logger.info("测试select_clients_by_scafl函数")
        
        # 准备测试数据
        N_t = [1, 2, 3, 4, 5]
        M_max = 3
        
        # 为这些客户端设置必要的属性
        for cid in N_t:
            self.server.client_Hk_comm[cid] = random.uniform(0.2, 1.0)
            self.server.client_staleness[cid] = random.randint(0, 5)
            self.server.client_completion_time[cid] = random.uniform(1.0, 5.0)
        
        # 调用函数
        selected = self.server.select_clients_by_scafl(N_t, M_max)
        logger.info(f"SCAFL选择的客户端: {selected}")
        
        return selected
    
    async def test_process_reports(self):
        """测试process_reports函数"""
        logger.info("测试process_reports函数")
        
        # 准备测试数据
        self.server.current_round = 5
        self.server.received_reports = [
            MockReport("client_1", 3, {"param1": np.array([1.0, 2.0])}),
            MockReport("client_2", 4, {"param1": np.array([3.0, 4.0])}),
            MockReport("client_3", 5, {"param1": np.array([5.0, 6.0])})
        ]
        
        # 确保算法有client_staleness属性
        if not hasattr(self.server.algorithm, 'client_staleness'):
            self.server.algorithm.client_staleness = {}
        
        # 尝试调用函数
        try:
            self.server.process_reports()
            logger.info("process_reports函数执行成功")
            
            # 检查结果
            for cid in ["1", "2", "3"]:
                staleness = self.server.client_staleness.get(cid)
                logger.info(f"客户端{cid}的过时度: {staleness}")
            
            return True
        except Exception as e:
            logger.error(f"process_reports函数执行失败: {e}", exc_info=True)
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("====== 开始FedSCAFL函数测试 ======")
        
        tests = [
            ("get_dkt测试", self.test_get_dkt),
            ("estimate_client_times测试", self.test_estimate_client_times),
            ("select_clients_by_scafl测试", self.test_select_clients_by_scafl),
            ("process_reports测试", self.test_process_reports)
        ]
        
        results = []
        for name, test_func in tests:
            logger.info(f"\n------ 测试: {name} ------")
            start_time = time.time()
            try:
                result = await test_func()
                success = True
            except Exception as e:
                logger.error(f"测试失败: {e}", exc_info=True)
                result = None
                success = False
            elapsed = time.time() - start_time
            results.append((name, success, elapsed, result))
        
        # 输出结果摘要
        logger.info("\n====== 测试结果摘要 ======")
        all_passed = True
        for name, success, elapsed, result in results:
            status = "通过" if success else "失败"
            logger.info(f"{name}: {status} (耗时{elapsed:.2f}秒), 结果: {result}")
            all_passed = all_passed and success
        
        if all_passed:
            logger.info("\n✅ 全部测试通过！关键函数可以正常工作。")
        else:
            logger.error("\n❌ 测试失败！请检查日志了解详细信息。")
        
        return all_passed

if __name__ == "__main__":
    tester = FunctionsTest()
    asyncio.run(tester.run_all_tests()) 