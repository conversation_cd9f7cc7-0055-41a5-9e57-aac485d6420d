[INFO][08:20:14]: 日志系统已初始化
[INFO][08:20:14]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_082014.log
[INFO][08:20:14]: 日志级别: INFO
[WARNING][08:20:14]: 无法获取系统信息: No module named 'psutil'
[INFO][08:20:14]: 🚀 ReFedScaFL 训练开始
[INFO][08:20:14]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][08:20:14]: 开始时间: 2025-07-29 08:20:14
[INFO][08:20:14]: [Client None] 基础初始化完成
[INFO][08:20:14]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][08:20:14]: 创建并缓存共享模型
[INFO][08:20:14]: [93m[1m[11292] Logging runtime results to: ./results/refedscafl/comparison_cifar10_alpha01/11292.csv.[0m
[INFO][08:20:14]: [Server #11292] Started training on 100 clients with 20 per round.
[INFO][08:20:14]: 服务器参数配置完成：
[INFO][08:20:14]: - 客户端数量: total=100, per_round=20
[INFO][08:20:14]: - 权重参数: success=0.8, distill=0.2
[INFO][08:20:14]: - SCAFL参数: V=1.0, tau_max=5
[INFO][08:20:14]: 从共享资源模型提取并缓存全局权重
[INFO][08:20:14]: [Server #11292] Configuring the server...
[INFO][08:20:14]: Training: 400 rounds or accuracy above 100.0%

[INFO][08:20:14]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][08:20:14]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][08:20:14]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][08:20:14]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][08:20:14]: Algorithm: fedavg
[INFO][08:20:14]: Data source: CIFAR10
[INFO][08:20:15]: Starting client #1's process.
[INFO][08:20:15]: Starting client #2's process.
[INFO][08:20:15]: Starting client #3's process.
[INFO][08:20:15]: Starting client #4's process.
[INFO][08:20:15]: Starting client #5's process.
[INFO][08:20:15]: Starting client #6's process.
[INFO][08:20:15]: Starting client #7's process.
[INFO][08:20:15]: Starting client #8's process.
[INFO][08:20:15]: Starting client #9's process.
[INFO][08:20:15]: Starting client #10's process.
[INFO][08:20:15]: Setting the random seed for selecting clients: 1
[INFO][08:20:15]: Starting a server at address 127.0.0.1 and port 8092.
[INFO][08:20:29]: [Server #11292] A new client just connected.
[INFO][08:20:29]: [Server #11292] New client with id #4 arrived.
[INFO][08:20:29]: [Server #11292] Client process #15236 registered.
[INFO][08:20:29]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][08:20:29]: [Server #11292] A new client just connected.
[INFO][08:20:29]: [Server #11292] New client with id #9 arrived.
[INFO][08:20:29]: [Server #11292] Client process #5620 registered.
[INFO][08:20:29]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][08:20:29]: [Server #11292] A new client just connected.
[INFO][08:20:29]: [Server #11292] New client with id #7 arrived.
[INFO][08:20:29]: [Server #11292] Client process #37912 registered.
[INFO][08:20:29]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][08:20:29]: [Server #11292] A new client just connected.
[INFO][08:20:29]: [Server #11292] New client with id #5 arrived.
[INFO][08:20:29]: [Server #11292] Client process #24372 registered.
[INFO][08:20:29]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][08:20:30]: [Server #11292] A new client just connected.
[INFO][08:20:30]: [Server #11292] New client with id #2 arrived.
[INFO][08:20:30]: [Server #11292] Client process #36372 registered.
[INFO][08:20:30]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][08:20:30]: [Server #11292] A new client just connected.
[INFO][08:20:30]: [Server #11292] A new client just connected.
[INFO][08:20:30]: [Server #11292] New client with id #6 arrived.
[INFO][08:20:30]: [Server #11292] Client process #29016 registered.
[INFO][08:20:30]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][08:20:30]: [Server #11292] New client with id #10 arrived.
[INFO][08:20:30]: [Server #11292] Client process #14536 registered.
[INFO][08:20:30]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][08:20:30]: [Server #11292] A new client just connected.
[INFO][08:20:30]: [Server #11292] A new client just connected.
[INFO][08:20:30]: [Server #11292] A new client just connected.
[INFO][08:20:30]: [Server #11292] New client with id #1 arrived.
[INFO][08:20:30]: [Server #11292] Client process #22420 registered.
[INFO][08:20:30]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][08:20:30]: [Server #11292] New client with id #3 arrived.
[INFO][08:20:30]: [Server #11292] Client process #31208 registered.
[INFO][08:20:30]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][08:20:30]: [Server #11292] New client with id #8 arrived.
[INFO][08:20:30]: [Server #11292] Client process #36980 registered.
[INFO][08:20:30]: [Server #11292] Starting training.
[INFO][08:20:30]: [93m[1m
[Server #11292] Starting round 1/400.[0m
[INFO][08:20:30]: [Server #11292] Selected clients: [18, 73, 98, 9, 33, 16, 64, 58, 61, 84, 49, 27, 13, 63, 4, 50, 56, 78, 99, 1]
[INFO][08:20:30]: [Server #11292] Selecting client #18 for training.
[INFO][08:20:30]: [Server #11292] Sending the current model to client #18 (simulated).
[INFO][08:20:30]: [Server #11292] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][08:20:30]: [Server #11292] Selecting client #73 for training.
[INFO][08:20:30]: [Server #11292] Sending the current model to client #73 (simulated).
[INFO][08:20:30]: [Server #11292] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][08:20:30]: [Server #11292] Selecting client #98 for training.
[INFO][08:20:30]: [Server #11292] Sending the current model to client #98 (simulated).
[INFO][08:20:30]: [Server #11292] Sending 18.75 MB of payload data to client #98 (simulated).
[INFO][08:20:30]: [Server #11292] Selecting client #9 for training.
[INFO][08:20:30]: [Server #11292] Sending the current model to client #9 (simulated).
[INFO][08:20:30]: [Server #11292] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][08:20:30]: [Server #11292] Selecting client #33 for training.
[INFO][08:20:30]: [Server #11292] Sending the current model to client #33 (simulated).
[INFO][08:20:30]: [Server #11292] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][08:20:30]: [Server #11292] Selecting client #16 for training.
[INFO][08:20:30]: [Server #11292] Sending the current model to client #16 (simulated).
[INFO][08:20:30]: [Server #11292] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][08:20:30]: [Server #11292] Selecting client #64 for training.
[INFO][08:20:30]: [Server #11292] Sending the current model to client #64 (simulated).
[INFO][08:20:30]: [Server #11292] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][08:20:30]: [Server #11292] Selecting client #58 for training.
[INFO][08:20:30]: [Server #11292] Sending the current model to client #58 (simulated).
[INFO][08:20:31]: [Server #11292] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][08:20:31]: [Server #11292] Selecting client #61 for training.
[INFO][08:20:31]: [Server #11292] Sending the current model to client #61 (simulated).
[INFO][08:20:31]: [Server #11292] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][08:20:31]: [Server #11292] Selecting client #84 for training.
[INFO][08:20:31]: [Server #11292] Sending the current model to client #84 (simulated).
[INFO][08:20:31]: [Server #11292] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][08:20:31]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][08:26:45]: [Server #11292] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][08:26:47]: [Server #11292] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][08:26:48]: [Server #11292] Received 18.75 MB of payload data from client #98 (simulated).
[INFO][08:26:50]: [Server #11292] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][08:26:50]: [Server #11292] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][08:26:51]: [Server #11292] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][08:26:51]: [Server #11292] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][08:26:52]: [Server #11292] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][08:26:53]: [Server #11292] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][08:26:53]: [Server #11292] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][08:26:53]: [Server #11292] Selecting client #49 for training.
[INFO][08:26:53]: [Server #11292] Sending the current model to client #49 (simulated).
[INFO][08:26:53]: [Server #11292] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][08:26:53]: [Server #11292] Selecting client #27 for training.
[INFO][08:26:53]: [Server #11292] Sending the current model to client #27 (simulated).
[INFO][08:26:53]: [Server #11292] Sending 18.75 MB of payload data to client #27 (simulated).
[INFO][08:26:53]: [Server #11292] Selecting client #13 for training.
[INFO][08:26:53]: [Server #11292] Sending the current model to client #13 (simulated).
[INFO][08:27:14]: [Server #11292] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][08:27:14]: [Server #11292] Selecting client #63 for training.
[INFO][08:27:14]: [Server #11292] Sending the current model to client #63 (simulated).
[INFO][08:27:14]: [Server #11292] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][08:27:14]: [Server #11292] Selecting client #4 for training.
[INFO][08:27:14]: [Server #11292] Sending the current model to client #4 (simulated).
[INFO][08:27:14]: [Server #11292] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][08:27:14]: [Server #11292] Selecting client #50 for training.
[INFO][08:27:14]: [Server #11292] Sending the current model to client #50 (simulated).
[INFO][08:27:14]: [Server #11292] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][08:27:14]: [Server #11292] Selecting client #56 for training.
[INFO][08:27:14]: [Server #11292] Sending the current model to client #56 (simulated).
[INFO][08:27:14]: [Server #11292] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][08:27:14]: [Server #11292] Selecting client #78 for training.
[INFO][08:27:14]: [Server #11292] Sending the current model to client #78 (simulated).
[INFO][08:27:15]: [Server #11292] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][08:27:15]: [Server #11292] Selecting client #99 for training.
[INFO][08:27:15]: [Server #11292] Sending the current model to client #99 (simulated).
[INFO][08:27:15]: [Server #11292] Sending 18.75 MB of payload data to client #99 (simulated).
[INFO][08:27:15]: [Server #11292] Selecting client #1 for training.
[INFO][08:27:15]: [Server #11292] Sending the current model to client #1 (simulated).
[INFO][08:27:15]: [Server #11292] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][08:31:38]: [Server #11292] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][08:33:14]: [Server #11292] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][08:33:18]: [Server #11292] Received 18.75 MB of payload data from client #99 (simulated).
[INFO][08:33:19]: [Server #11292] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][08:33:19]: [Server #11292] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][08:33:20]: [Server #11292] Received 18.75 MB of payload data from client #27 (simulated).
[INFO][08:33:20]: [Server #11292] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][08:33:22]: [Server #11292] Received 18.75 MB of payload data from client #78 (simulated).
[INFO][08:33:22]: [Server #11292] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][08:33:22]: [Server #11292] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][08:33:22]: [Server #11292] Adding client #49 to the list of clients for aggregation.
[INFO][08:33:22]: [Server #11292] Adding client #50 to the list of clients for aggregation.
[INFO][08:33:22]: [Server #11292] Adding client #99 to the list of clients for aggregation.
[INFO][08:33:22]: [Server #11292] Adding client #56 to the list of clients for aggregation.
[INFO][08:33:22]: [Server #11292] Adding client #33 to the list of clients for aggregation.
[INFO][08:33:22]: [Server #11292] Adding client #4 to the list of clients for aggregation.
[INFO][08:33:22]: [Server #11292] Adding client #16 to the list of clients for aggregation.
[INFO][08:33:22]: [Server #11292] Adding client #27 to the list of clients for aggregation.
[INFO][08:33:22]: [Server #11292] Adding client #98 to the list of clients for aggregation.
[INFO][08:33:22]: [Server #11292] Adding client #13 to the list of clients for aggregation.
[INFO][08:33:22]: [Server #11292] Aggregating 10 clients in total.
[INFO][08:33:22]: [Server #11292] Updated weights have been received.
[INFO][08:33:22]: [Server #11292] Aggregating model weight deltas.
[INFO][08:33:22]: [Server #11292] Finished aggregating updated weights.
[INFO][08:33:22]: [Server #11292] Started model testing.
[INFO][08:34:09]: [Trainer.test] 测试完成 - 准确率: 12.43% (1243/10000)
[INFO][08:34:09]: [93m[1m[Server #11292] Global model accuracy: 12.43%
[0m
[INFO][08:34:09]: get_logged_items 被调用
[INFO][08:34:09]: 从updates获取参与客户端: [49, 50, 99, 56, 33, 4, 16, 27, 98, 13]
[INFO][08:34:09]: 客户端 49 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][08:34:09]: 客户端 50 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][08:34:09]: 客户端 99 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][08:34:09]: 客户端 56 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][08:34:09]: 客户端 33 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][08:34:09]: 客户端 4 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][08:34:09]: 客户端 16 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][08:34:09]: 客户端 27 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][08:34:09]: 客户端 98 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][08:34:09]: 客户端 13 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][08:34:09]: 陈旧度统计 - 参与客户端: [49, 50, 99, 56, 33, 4, 16, 27, 98, 13], 陈旧度: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
[INFO][08:34:09]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][08:34:09]: 最终logged_items: {'round': 1, 'accuracy': 0.1243, 'accuracy_std': 0, 'elapsed_time': 136.8479015827179, 'processing_time': 0.001355300000003723, 'comm_time': 0, 'round_time': 136.8479016571717, 'comm_overhead': 749.9883651733398, 'global_accuracy': 0.1243, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][08:34:09]: [Server #11292] All client reports have been processed.
[INFO][08:34:09]: [Server #11292] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][08:34:09]: [Server #11292] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][08:34:09]: [93m[1m
[Server #11292] Starting round 2/400.[0m
[INFO][08:34:09]: [Server #11292] Selected clients: [100, 65, 38, 33, 86, 16, 44, 5, 4, 94]
[INFO][08:34:09]: [Server #11292] Selecting client #100 for training.
[INFO][08:34:09]: [Server #11292] Sending the current model to client #100 (simulated).
[INFO][08:34:09]: [Server #11292] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][08:34:09]: [Server #11292] Selecting client #65 for training.
[INFO][08:34:09]: [Server #11292] Sending the current model to client #65 (simulated).
[INFO][08:34:10]: [Server #11292] Sending 18.75 MB of payload data to client #65 (simulated).
[INFO][08:34:10]: [Server #11292] Selecting client #38 for training.
[INFO][08:34:10]: [Server #11292] Sending the current model to client #38 (simulated).
[INFO][08:34:10]: [Server #11292] Sending 18.75 MB of payload data to client #38 (simulated).
[INFO][08:34:10]: [Server #11292] Selecting client #33 for training.
[INFO][08:34:10]: [Server #11292] Sending the current model to client #33 (simulated).
[INFO][08:34:10]: [Server #11292] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][08:34:10]: [Server #11292] Selecting client #86 for training.
[INFO][08:34:10]: [Server #11292] Sending the current model to client #86 (simulated).
[INFO][08:34:10]: [Server #11292] Sending 18.75 MB of payload data to client #86 (simulated).
[INFO][08:34:10]: [Server #11292] Selecting client #16 for training.
[INFO][08:34:10]: [Server #11292] Sending the current model to client #16 (simulated).
[INFO][08:34:10]: [Server #11292] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][08:34:10]: [Server #11292] Selecting client #44 for training.
[INFO][08:34:10]: [Server #11292] Sending the current model to client #44 (simulated).
[INFO][08:34:10]: [Server #11292] Sending 18.75 MB of payload data to client #44 (simulated).
[INFO][08:34:10]: [Server #11292] Selecting client #5 for training.
[INFO][08:34:10]: [Server #11292] Sending the current model to client #5 (simulated).
[INFO][08:34:11]: [Server #11292] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][08:34:11]: [Server #11292] Selecting client #4 for training.
[INFO][08:34:11]: [Server #11292] Sending the current model to client #4 (simulated).
[INFO][08:34:11]: [Server #11292] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][08:34:11]: [Server #11292] Selecting client #94 for training.
[INFO][08:34:11]: [Server #11292] Sending the current model to client #94 (simulated).
[INFO][08:34:11]: [Server #11292] Sending 18.75 MB of payload data to client #94 (simulated).
[INFO][08:39:26]: [Server #11292] Received 18.75 MB of payload data from client #100 (simulated).
[INFO][08:39:27]: [Server #11292] Received 18.75 MB of payload data from client #38 (simulated).
[INFO][08:39:28]: [Server #11292] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][08:39:29]: [Server #11292] Received 18.75 MB of payload data from client #44 (simulated).
[INFO][08:39:29]: [Server #11292] Received 18.75 MB of payload data from client #86 (simulated).
[INFO][08:39:29]: [Server #11292] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][08:39:29]: [Server #11292] Received 18.75 MB of payload data from client #65 (simulated).
[INFO][08:39:29]: [Server #11292] Received 18.75 MB of payload data from client #94 (simulated).
[INFO][08:39:29]: [Server #11292] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][08:39:29]: [Server #11292] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][08:39:29]: [Server #11292] Adding client #84 to the list of clients for aggregation.
[INFO][08:39:29]: [Server #11292] Adding client #58 to the list of clients for aggregation.
[INFO][08:39:29]: [Server #11292] Adding client #78 to the list of clients for aggregation.
[INFO][08:39:29]: [Server #11292] Adding client #1 to the list of clients for aggregation.
[INFO][08:39:29]: [Server #11292] Adding client #63 to the list of clients for aggregation.
[INFO][08:39:29]: [Server #11292] Adding client #9 to the list of clients for aggregation.
[INFO][08:39:29]: [Server #11292] Adding client #73 to the list of clients for aggregation.
[INFO][08:39:29]: [Server #11292] Adding client #61 to the list of clients for aggregation.
[INFO][08:39:29]: [Server #11292] Adding client #18 to the list of clients for aggregation.
[INFO][08:39:29]: [Server #11292] Adding client #64 to the list of clients for aggregation.
[INFO][08:39:29]: [Server #11292] Aggregating 10 clients in total.
[INFO][08:39:29]: [Server #11292] Updated weights have been received.
[INFO][08:39:29]: [Server #11292] Aggregating model weight deltas.
[INFO][08:39:29]: [Server #11292] Finished aggregating updated weights.
[INFO][08:39:29]: [Server #11292] Started model testing.
[INFO][08:39:42]: [Trainer.test] 测试完成 - 准确率: 11.80% (1180/10000)
[INFO][08:39:42]: [93m[1m[Server #11292] Global model accuracy: 11.80%
[0m
[INFO][08:39:42]: get_logged_items 被调用
[INFO][08:39:42]: 从updates获取参与客户端: [84, 58, 78, 1, 63, 9, 73, 61, 18, 64]
[INFO][08:39:42]: 客户端 84 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][08:39:42]: 客户端 58 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][08:39:42]: 客户端 78 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][08:39:42]: 客户端 1 陈旧度: 2 (当前轮次:2, 上次参与:0)
[INFO][08:39:42]: 客户端 63 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][08:39:42]: 客户端 9 陈旧度: 2 (当前轮次:2, 上次参与:0)
[INFO][08:39:42]: 客户端 73 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][08:39:42]: 客户端 61 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][08:39:42]: 客户端 18 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][08:39:42]: 客户端 64 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][08:39:42]: 陈旧度统计 - 参与客户端: [84, 58, 78, 1, 63, 9, 73, 61, 18, 64], 陈旧度: [1, 1, 1, 2, 1, 2, 1, 1, 1, 1]
[INFO][08:39:42]: 平均陈旧度: 1.2, 最大: 2, 最小: 1
[INFO][08:39:42]: 最终logged_items: {'round': 2, 'accuracy': 0.118, 'accuracy_std': 0, 'elapsed_time': 147.1824426651001, 'processing_time': 0.00047080000001642475, 'comm_time': 0, 'round_time': 147.18244258114623, 'comm_overhead': 1124.9825477600098, 'global_accuracy': 0.118, 'avg_staleness': 1.2, 'max_staleness': 2, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][08:39:42]: [Server #11292] All client reports have been processed.
[INFO][08:39:42]: [Server #11292] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][08:39:42]: [Server #11292] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][08:39:42]: [93m[1m
[Server #11292] Starting round 3/400.[0m
[INFO][08:39:42]: [Server #11292] Selected clients: [77, 2, 55, 97, 31, 61, 6, 75, 32, 63]
[INFO][08:39:42]: [Server #11292] Selecting client #77 for training.
[INFO][08:39:42]: [Server #11292] Sending the current model to client #77 (simulated).
[INFO][08:39:42]: [Server #11292] Sending 18.75 MB of payload data to client #77 (simulated).
[INFO][08:39:42]: [Server #11292] Selecting client #2 for training.
[INFO][08:39:42]: [Server #11292] Sending the current model to client #2 (simulated).
[INFO][08:39:42]: [Server #11292] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][08:39:42]: [Server #11292] Selecting client #55 for training.
[INFO][08:39:42]: [Server #11292] Sending the current model to client #55 (simulated).
[INFO][08:39:42]: [Server #11292] Sending 18.75 MB of payload data to client #55 (simulated).
[INFO][08:39:42]: [Server #11292] Selecting client #97 for training.
[INFO][08:39:42]: [Server #11292] Sending the current model to client #97 (simulated).
[INFO][08:39:42]: [Server #11292] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][08:39:42]: [Server #11292] Selecting client #31 for training.
[INFO][08:39:42]: [Server #11292] Sending the current model to client #31 (simulated).
[INFO][08:39:42]: [Server #11292] Sending 18.75 MB of payload data to client #31 (simulated).
[INFO][08:39:42]: [Server #11292] Selecting client #61 for training.
[INFO][08:39:42]: [Server #11292] Sending the current model to client #61 (simulated).
[INFO][08:39:42]: [Server #11292] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][08:39:42]: [Server #11292] Selecting client #6 for training.
[INFO][08:39:42]: [Server #11292] Sending the current model to client #6 (simulated).
[INFO][08:39:43]: [Server #11292] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][08:39:43]: [Server #11292] Selecting client #75 for training.
[INFO][08:39:43]: [Server #11292] Sending the current model to client #75 (simulated).
[INFO][08:39:43]: [Server #11292] Sending 18.75 MB of payload data to client #75 (simulated).
[INFO][08:39:43]: [Server #11292] Selecting client #32 for training.
[INFO][08:39:43]: [Server #11292] Sending the current model to client #32 (simulated).
[INFO][08:39:43]: [Server #11292] Sending 18.75 MB of payload data to client #32 (simulated).
[INFO][08:39:43]: [Server #11292] Selecting client #63 for training.
[INFO][08:39:43]: [Server #11292] Sending the current model to client #63 (simulated).
[INFO][08:39:44]: [Server #11292] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][08:43:39]: [Server #11292] An existing client just disconnected.
[WARNING][08:43:39]: [Server #11292] Client process #15236 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][08:43:39]: [93m[1m[Server #11292] Closing the server due to a failed client.[0m
[INFO][08:43:39]: [Server #11292] Training concluded.
[INFO][08:43:39]: [Server #11292] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][08:43:39]: [Server #11292] Closing the server.
[INFO][08:43:39]: Closing the connection to client #5620.
[INFO][08:43:39]: Closing the connection to client #37912.
[INFO][08:43:39]: Closing the connection to client #24372.
[INFO][08:43:39]: Closing the connection to client #36372.
[INFO][08:43:39]: Closing the connection to client #29016.
[INFO][08:43:39]: Closing the connection to client #14536.
[INFO][08:43:39]: Closing the connection to client #22420.
[INFO][08:43:39]: Closing the connection to client #31208.
[INFO][08:43:39]: Closing the connection to client #36980.
