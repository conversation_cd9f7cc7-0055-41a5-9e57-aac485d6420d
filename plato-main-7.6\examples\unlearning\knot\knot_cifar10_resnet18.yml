general:
    # The prefix of directories of dataset, models, checkpoints, and results
    base_path: .

clients:
    # Type
    type: simple

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 30

    # Should clients really go to sleep, or should we just simulate the sleep times? The sleep_simulation is true only when speed_simulation is true
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 20

    random_seed: 1
    #------------------- Need modification -------------------

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 30

    # The round to remove the local data by clients
    # If do_optimized_clustering is true, it needs to add 2.
    # For example, if you want to do the data deletion after round 2
    # You should set it as round 4.
    data_deletion_round: 5

    # The clients which need to delete their local data samples
    clients_requesting_deletion: [1, 10, 20, 30, 40, 50, 60, 70, 80, 90]

    # The percentage to delete the local data by clients
    deleted_data_ratio: 0.1

server:
    address: 127.0.0.1
    port: 8088
    random_seed: 1

    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in sychronous mode?
    synchronous: false

    do_test: true

    # Should we do the cluster accuracy test during learning?
    do_clustered_test: true

    # Should be false. Specified in knot
    do_global_test: false

    staleness_bound: 1000

    #------------------- Need modification -------------------
    # The window size that accuracy of each cluster should reach
    window_size: 3

    # The minimum arrival clients that all clusters can aggregrate
    minimum_clients_aggregated: 15

    #optimization clustering method
    do_optimized_clustering: false

    # The total number of clusters
    clusters: 2

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition
    partition_size: 1000

    # IID or non-IID?
    sampler: noniid

    test_sampler: noniid

    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The machine learning model
    model_name: resnet_18

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 10
    optimizer: SGD
    lr_scheduler: LambdaLR

    #------------------- Need modification -------------------
    # The maximum number of training rounds
    rounds: 70

    # The maximum number of clients running concurrently
    # Always cacluate by 20G(GPU space on the sim)/size_per_client
    # (client size should always lower than 4G)
    max_concurrency: 7

    # The target accuracy
    target_accuracy: 0.65
    target_accuracy_std: 0.005

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    # ResNet-18 training params
    !include resnet18_params.yml

results:
    results_path: results/random_cifar10
    types: round, accuracy, clusters_accuracy, elapsed_time
