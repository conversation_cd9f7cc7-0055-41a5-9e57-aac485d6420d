"""
Samples data from a dataset, biased across labels according to the Dirichlet distribution.
"""
import logging

import numpy as np
import torch
from torch.utils.data import WeightedRandomSampler, SubsetRandomSampler
from plato.config import Config

from plato.samplers import base


class Sampler(base.Sampler):
    """Create a data sampler for each client to use a divided partition of the
    dataset, biased across labels according to the Dirichlet distribution."""

    def __init__(self, datasource, client_id, testing):
        super().__init__()

        # Different clients should have a different bias across the labels & partition size
        np.random.seed(self.random_seed * int(client_id))

        # Concentration(浓度) parameter to be used in the Dirichlet distribution (alpha， 默认为1)
        concentration = (
            Config().data.concentration
            if hasattr(Config().data, "concentration")
            else 1.0
        )

        # 数据集中所有的label
        if testing:
            target_list = datasource.get_test_set().targets
        else:
            # The list of labels (targets) for all the examples
            target_list = datasource.targets()

        # 数据集中类别数量
        class_list = datasource.classes()

        # 采样一个 len(class_list) 维的向量表示每个数据类别的，每个值服从狄利克雷分布
        target_proportions = np.random.dirichlet(
            np.repeat(concentration, len(class_list))
        )

        # 如果以上划分为空，就随机生成
        if np.isnan(np.sum(target_proportions)):
            target_proportions = np.repeat(0, len(class_list))
            target_proportions[np.random.randint(0, len(class_list))] = 1

        self.sample_weights = target_proportions[target_list]

    def num_samples(self) -> int:
        """Returns the length of the dataset after sampling."""
        # 用户的数据量都是一样的
        sampled_size = Config().data.partition_size

        # Variable partition size across clients
        if hasattr(Config().data, "partition_distribution"):
            dist = Config().data.partition_distribution

            if dist.distribution.lower() == "uniform":
                sampled_size *= np.random.uniform(dist.low, dist.high)

            if dist.distribution.lower() == "normal":
                sampled_size *= np.random.normal(dist.mean, dist.high)

            sampled_size = int(sampled_size)

        return sampled_size

    def get(self):
        """Obtains an instance of the sampler."""
        gen = torch.Generator()
        gen.manual_seed(self.random_seed)

        # Samples without replacement using the sample weights
        subset_indices = list(
            WeightedRandomSampler(
                weights=self.sample_weights, # 每类数据采样的概率
                num_samples=self.num_samples(), # 采样数据的总数据量
                replacement=False,
                generator=gen,
            )
        )

        return SubsetRandomSampler(subset_indices, generator=gen)
