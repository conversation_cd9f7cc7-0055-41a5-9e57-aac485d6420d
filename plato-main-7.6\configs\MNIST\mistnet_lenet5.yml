clients:
    # Type
    type: mistnet

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

    # Processors for outbound data payloads
    outbound_processors:
        - feature_randomized_response
        # - feature_quantize
        - feature_unbatch
        - outbound_feature_ndarrays
        - compress

server:
    type: mistnet

    address: 127.0.0.1
    port: 8000

    # Processors for inbound data payloads
    inbound_processors:
        - decompress
        - inbound_feature_tensors
        # - feature_dequantize

data: !include mnist_iid.yml

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 1

    # The target accuracy
    target_accuracy: 0.95

    # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 10
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: mistnet

    epsilon: null

parameters:
    model:
        num_classes: 10
        cut_layer: relu3

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0
