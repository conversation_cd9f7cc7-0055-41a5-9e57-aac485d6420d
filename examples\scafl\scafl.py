"""
SCAFL (Staleness-Controlled Asynchronous Federated Learning) 主程序

这是SCAFL的入口程序，负责：
1. 加载配置
2. 创建服务器和客户端
3. 启动训练过程
"""

import os
import sys
import logging

# 添加plato路径，确保能导入plato模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))


def main():
    """SCAFL主函数"""

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    logging.info("🚀 启动SCAFL训练...")


if __name__ == "__main__":
    main()
