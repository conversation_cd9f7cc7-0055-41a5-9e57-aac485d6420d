"""
SCAFL (Staleness-Controlled Asynchronous Federated Learning) 主程序

这是SCAFL的入口程序，负责：
1. 加载配置
2. 创建服务器和客户端
3. 启动训练过程
"""

import os
import sys
import logging

# 添加plato路径，确保能导入plato模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 先导入Config，其他模块稍后导入
from plato.config import Config


def main():
    """SCAFL主函数"""

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    logging.info("🚀 启动SCAFL训练...")

    try:
        # 加载配置文件 - 必须在导入其他plato模块之前
        config_file = os.path.join(os.path.dirname(__file__), 'scafl_config.yml')
        if os.path.exists(config_file):
            # 设置配置文件路径到环境变量
            os.environ['config_file'] = config_file
            # 创建Config实例（会自动读取环境变量中的配置文件）
            config = Config()
            logging.info(f"✅ 加载配置文件: {config_file}")
        else:
            logging.error(f"❌ 配置文件不存在: {config_file}")
            return

        # 现在可以安全导入其他plato模块
        from plato.clients import simple
        from scafl_server import Server
        from scafl_algorithm import Algorithm

        # 创建SCAFL服务器
        # 注意：我们不直接传入algorithm，让服务器自己创建SCAFL算法实例
        server = Server()

        # 创建客户端（使用简单客户端）
        client = simple.Client()
        
        logging.info("✅ SCAFL组件创建完成")
        logging.info(f"📊 配置信息:")
        logging.info(f"   - 总客户端数: {Config().clients.total_clients}")
        logging.info(f"   - 每轮客户端数: {Config().clients.per_round}")
        logging.info(f"   - 训练轮数: {Config().trainer.rounds}")
        logging.info(f"   - 数据集: {Config().data.datasource}")
        logging.info(f"   - 模型: {Config().trainer.model_name}")
        logging.info(f"   - 最大陈旧度: {Config().server.tau_max}")
        logging.info(f"   - Lyapunov参数V: {Config().server.V}")
        
        # 启动训练
        logging.info("🎯 开始SCAFL训练...")
        server.run(client)
        
        logging.info("🎉 SCAFL训练完成！")
        
    except Exception as e:
        logging.error(f"❌ SCAFL训练过程中出错: {str(e)}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        raise


if __name__ == "__main__":
    main()
