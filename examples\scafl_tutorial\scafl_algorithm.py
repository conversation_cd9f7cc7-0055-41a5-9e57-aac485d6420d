"""
SCAFL算法核心实现
师妹学习版本 - 详细注释

这个文件实现了SCAFL论文中的核心数学逻辑：
1. 陈旧度跟踪 (Staleness Tracking)
2. 虚拟队列管理 (Virtual Queue Management) 
3. 陈旧度感知权重聚合 (Staleness-aware Weight Aggregation)
"""

import logging
import torch
from collections import OrderedDict
from plato.algorithms import fedavg
from plato.config import Config


class Algorithm(fedavg.Algorithm):
    """SCAFL算法类
    
    继承自FedAvg，这样我们可以复用大部分功能，
    只需要重写关键的聚合方法
    """
    
    def __init__(self, trainer):
        """初始化SCAFL算法
        
        Args:
            trainer: 训练器实例
        """
        # 先调用父类初始化，获得基础功能
        super().__init__(trainer)
        
        # 从配置文件读取SCAFL参数
        config = Config()
        self.tau_max = getattr(config.server, 'tau_max', 5)  # 最大陈旧度阈值
        self.V = getattr(config.server, 'V', 1.0)            # Lyapunov参数
        
        # SCAFL状态变量 - 这些是SCAFL特有的
        self.client_staleness = {}     # 记录每个客户端的陈旧度 τk(t)
        self.virtual_queues = {}       # 虚拟队列 Qk(t) - 用于Lyapunov优化
        
        logging.info(f"[SCAFL Algorithm] 初始化完成")
        logging.info(f"[SCAFL Algorithm] 参数: tau_max={self.tau_max}, V={self.V}")
    
    def update_client_staleness(self, client_id, participated):
        """更新客户端的陈旧度
        
        实现论文公式(5): τk(t+1) = τk(t) + 1*(1-βt_k)
        
        Args:
            client_id: 客户端ID
            participated: 是否参与了本轮聚合 (βt_k)
        """
        # 如果是新客户端，初始化陈旧度为0
        if client_id not in self.client_staleness:
            self.client_staleness[client_id] = 0
            
        if participated:
            # 参与聚合 (βt_k = 1)，陈旧度重置为0
            self.client_staleness[client_id] = 0
            logging.debug(f"[SCAFL] 客户端{client_id}参与聚合，陈旧度重置为0")
        else:
            # 未参与聚合 (βt_k = 0)，陈旧度+1
            self.client_staleness[client_id] += 1
            logging.debug(f"[SCAFL] 客户端{client_id}未参与聚合，陈旧度+1 = {self.client_staleness[client_id]}")
    
    def update_virtual_queue(self, client_id, participated):
        """更新虚拟队列 - Lyapunov优化的核心
        
        实现论文公式(8): Qk(t+1) = max{Qk(t) + (τk(t)+1)(1-βt_k) - τmax, 0}
        
        这个虚拟队列的作用：
        - 当客户端长时间不参与聚合时，队列长度增加
        - 队列长度反映了客户端的"饥饿程度"
        - Lyapunov优化会优先选择队列长度大的客户端
        
        Args:
            client_id: 客户端ID
            participated: 是否参与了本轮聚合
        """
        # 初始化虚拟队列
        if client_id not in self.virtual_queues:
            self.virtual_queues[client_id] = 0.0
            
        current_queue = self.virtual_queues[client_id]
        staleness = self.client_staleness.get(client_id, 0)
        
        if participated:
            # 参与聚合: βt_k = 1, (1-βt_k) = 0
            # 队列减少 τmax
            queue_increment = 0 - self.tau_max
        else:
            # 未参与聚合: βt_k = 0, (1-βt_k) = 1  
            # 队列增加 (τk(t)+1) - τmax
            queue_increment = (staleness + 1) - self.tau_max
            
        # 更新队列长度，确保非负
        new_queue = max(current_queue + queue_increment, 0.0)
        self.virtual_queues[client_id] = new_queue
        
        logging.debug(f"[SCAFL] 客户端{client_id}虚拟队列: {current_queue:.2f} → {new_queue:.2f}")
    
    def compute_staleness_weight(self, staleness):
        """计算基于陈旧度的权重
        
        陈旧度越高，权重越小，这样可以减少过时梯度的影响
        
        Args:
            staleness: 客户端的陈旧度
            
        Returns:
            float: 权重值 (0.1 到 1.0 之间)
        """
        # 使用简单的线性衰减公式
        # 你可以根据实验效果调整这个公式
        if staleness == 0:
            return 1.0  # 最新的梯度，权重最大
        else:
            # 陈旧度越高，权重越小，但保持最小权重0.1
            weight = max(0.1, 1.0 - staleness / (2 * self.tau_max))
            return weight
    
    async def aggregate_weights(self, baseline_weights, weights_received, **kwargs):
        """SCAFL的陈旧度感知权重聚合
        
        这是SCAFL的核心：根据每个客户端的陈旧度来分配聚合权重
        实现论文公式(4)的思想
        
        Args:
            baseline_weights: 当前全局模型权重
            weights_received: 接收到的客户端权重列表
            
        Returns:
            聚合后的权重
        """
        if not weights_received:
            logging.warning("[SCAFL] 没有接收到客户端权重")
            return baseline_weights
            
        logging.info(f"[SCAFL] 开始陈旧度感知聚合，收到{len(weights_received)}个客户端权重")
        
        # 第一步：计算每个客户端的陈旧度权重
        staleness_weights = []
        total_samples = 0
        
        for i, weight_data in enumerate(weights_received):
            # 获取客户端ID（如果权重数据中包含的话）
            client_id = weight_data.get('client_id', i) if isinstance(weight_data, dict) else i
            staleness = self.client_staleness.get(client_id, 0)
            
            # 计算陈旧度权重
            staleness_weight = self.compute_staleness_weight(staleness)
            
            # 获取样本数量（用于加权）
            num_samples = weight_data.get('num_samples', 1) if isinstance(weight_data, dict) else 1
            
            # 最终权重 = 陈旧度权重 × 样本数量权重
            final_weight = staleness_weight * num_samples
            staleness_weights.append(final_weight)
            total_samples += num_samples
            
            logging.info(f"[SCAFL] 客户端{client_id}: 陈旧度={staleness}, "
                        f"陈旧度权重={staleness_weight:.4f}, 样本数={num_samples}, "
                        f"最终权重={final_weight:.4f}")
        
        # 第二步：归一化权重
        total_weight = sum(staleness_weights)
        if total_weight > 0:
            normalized_weights = [w / total_weight for w in staleness_weights]
        else:
            # 如果总权重为0，使用均匀权重
            normalized_weights = [1.0 / len(weights_received)] * len(weights_received)
            logging.warning("[SCAFL] 总权重为0，使用均匀权重")
        
        # 第三步：执行加权聚合
        aggregated_weights = OrderedDict()
        
        # 获取权重的键（参数名）
        if isinstance(weights_received[0], dict) and 'weights' in weights_received[0]:
            first_weights = weights_received[0]['weights']
        else:
            first_weights = weights_received[0]
            
        # 对每个模型参数进行加权聚合
        for param_name in first_weights.keys():
            aggregated_weights[param_name] = torch.zeros_like(first_weights[param_name])
            
            for i, weight_data in enumerate(weights_received):
                # 提取实际的权重
                if isinstance(weight_data, dict) and 'weights' in weight_data:
                    client_weights = weight_data['weights']
                else:
                    client_weights = weight_data
                    
                # 加权累加：聚合权重 += 归一化权重 × 客户端权重
                aggregated_weights[param_name] += normalized_weights[i] * client_weights[param_name]
        
        logging.info("[SCAFL] 陈旧度感知权重聚合完成")
        return aggregated_weights
    
    def get_client_staleness(self, client_id):
        """获取客户端陈旧度（供服务器使用）"""
        return self.client_staleness.get(client_id, 0)
    
    def get_virtual_queue_length(self, client_id):
        """获取虚拟队列长度（供服务器使用）"""
        return self.virtual_queues.get(client_id, 0.0)
