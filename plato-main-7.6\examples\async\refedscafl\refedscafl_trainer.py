"""
ReFedScaFL训练器类，扩展自基础训练器
提供获取最后一个批次的输入数据和模型输出的功能
"""

import logging
import time
import torch
import asyncio
from plato.trainers import basic
from plato.config import Config

class Trainer(basic.Trainer):
    """
    ReFedScaFL训练器类，用于支持知识蒸馏的伪梯度生成
    """
    def __init__(self, model=None, client_id=None, callbacks=None):
        """初始化训练器

        Args:
            model: 要训练的模型，可以是已实例化的nn.Module对象或可调用的模型类
            client_id: 客户端ID，用于日志记录
            callbacks: 回调函数列表
        """
        # 调用父类初始化，让父类自动从registry获取模型
        super().__init__(model=None, callbacks=callbacks)

        # 设置客户端ID（服务器为0，客户端为具体ID）
        if client_id is None:
            self.client_id = 0  # 服务器默认为0
        else:
            self.client_id = client_id
        logging.info(f"[Trainer Init] 初始化 Trainer，client_id = {self.client_id}")

        # 验证模型状态（父类已经处理了模型设置）
        if self.model is not None:
            logging.info(f"[Trainer Init] 模型已设置: {type(self.model)}")
            try:
                state_dict = self.model.state_dict()
                logging.info(f"[Trainer Init] 模型状态字典已获取，包含 {len(state_dict)} 个参数")
            except Exception as e:
                logging.error(f"[Trainer Init] 获取模型状态字典失败: {str(e)}")
                raise
        else:
            logging.warning("[Trainer Init] 模型为 None")
            
        # 初始化基本属性
        self.training_start_time = asyncio.get_event_loop().time()
        self.model_state_dict = None
        self.current_round = 0
        
        # 初始化训练参数
        config = Config()
        self.batch_size = getattr(config.trainer, 'batch_size', 32)
        self.learning_rate = getattr(config.parameters.optimizer, 'lr', 0.01)
        self.momentum = getattr(config.trainer, 'momentum', 0.9)
        self.weight_decay = getattr(config.trainer, 'weight_decay', 0.0001)
        self.epochs = getattr(config.trainer, 'epochs', 5)
        
        # 初始化优化器和损失函数
        self.optimizer = None
        self.loss_criterion = None
        
        # 用于知识蒸馏的变量
        self.x_batch = None
        self.local_logits = None
        
        # 初始化训练统计信息
        self.train_loss = 0.0
        self.train_correct = 0
        self.train_total = 0
        self.num_batches = 0
        self.current_epoch = 0
        
        logging.info(f"[Trainer Init] 训练器初始化完成，参数：batch_size={self.batch_size}, "
                    f"learning_rate={self.learning_rate}, epochs={self.epochs}")
        
    def train(self, trainset=None, sampler=None, **kwargs):
        """训练模型

        Args:
            trainset: 训练数据集
            sampler: 数据采样器
            **kwargs: 其他参数

        Returns:
            float: 训练时间（秒）
        """
        logging.info(f"[Trainer.train] 开始训练，客户端 ID: {self.client_id}")

        # 记录训练开始时间
        start_time = time.time()

        try:
            if trainset is None:
                raise ValueError("训练数据集不能为空")
                
            # 让Plato框架自动处理模型创建
            if self.model is None:
                logging.info(f"[Trainer.train] 模型将由Plato框架自动创建")
                
            # 初始化优化器
            self.optimizer = torch.optim.SGD(
                self.model.parameters(),
                lr=self.learning_rate,
                momentum=self.momentum,
                weight_decay=self.weight_decay
            )
            
            # 初始化损失函数
            self.loss_criterion = torch.nn.CrossEntropyLoss()
            
            # 创建数据加载器
            if sampler is None:
                train_loader = torch.utils.data.DataLoader(
                    dataset=trainset,
                    batch_size=self.batch_size,
                    shuffle=True,
                    num_workers=0,  # 避免Windows多进程问题
                    pin_memory=False  # 减少内存使用
                )
            else:
                # 使用Plato的sampler.get()方法获取PyTorch兼容的sampler
                pytorch_sampler = sampler.get()
                train_loader = torch.utils.data.DataLoader(
                    dataset=trainset,
                    batch_size=self.batch_size,
                    shuffle=False,
                    sampler=pytorch_sampler,
                    num_workers=0,  # 避免Windows多进程问题
                    pin_memory=False  # 减少内存使用
                )
            
            # 检查训练数据是否为空（安全检查）
            try:
                if len(train_loader) == 0:
                    raise ValueError("训练数据加载器为空")
            except TypeError:
                # 某些sampler可能没有__len__方法，跳过检查
                pass
            
            # 优化的训练过程
            self.model.train()

            # 保守稳定的学习率调度器
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, T_max=self.epochs, eta_min=self.learning_rate * 0.1
            )

            for epoch in range(self.epochs):
                self.current_epoch = epoch
                epoch_loss = 0.0
                epoch_correct = 0
                epoch_total = 0

                # 精度优化的早停机制
                best_epoch_loss = float('inf')
                patience_counter = 0
                patience = 3  # 增加耐心值，允许更多学习

                for batch_idx, (x, y) in enumerate(train_loader):

                    self.optimizer.zero_grad()
                    output = self.model(x)
                    loss = self.loss_criterion(output, y)

                    # 保守稳定的梯度裁剪
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)  # 严格裁剪阈值
                    self.optimizer.step()

                    # 保存最后一个批次的数据和logits
                    if batch_idx == len(train_loader) - 1:
                        self.x_batch = x.detach()  # 使用detach减少内存占用
                        self.local_logits = output.detach()

                    # 更新训练统计信息
                    self.train_loss += loss.item()
                    epoch_loss += loss.item()
                    _, predicted = output.max(1)
                    self.train_total += y.size(0)
                    epoch_total += y.size(0)
                    self.train_correct += predicted.eq(y).sum().item()
                    epoch_correct += predicted.eq(y).sum().item()
                    self.num_batches += 1

                # 更新学习率
                scheduler.step()

                # 计算epoch平均损失
                avg_epoch_loss = epoch_loss / max(1, len(train_loader))

                # 早停检查
                if avg_epoch_loss < best_epoch_loss:
                    best_epoch_loss = avg_epoch_loss
                    patience_counter = 0
                else:
                    patience_counter += 1

                # 记录每个epoch的训练结果
                if epoch_total > 0:
                    epoch_accuracy = 100. * epoch_correct / epoch_total
                    current_lr = scheduler.get_last_lr()[0]
                    logging.info(f"[Trainer.train] Epoch {epoch+1}/{self.epochs} - "
                               f"Loss: {avg_epoch_loss:.4f}, "
                               f"Accuracy: {epoch_accuracy:.2f}%, "
                               f"LR: {current_lr:.6f}")
                else:
                    logging.warning(f"[Trainer.train] Epoch {epoch+1}/{self.epochs} - 没有训练数据")

                # 早停条件
                if patience_counter >= patience and epoch >= 2:  # 至少训练3个epoch
                    logging.info(f"[Trainer.train] 早停触发，在epoch {epoch+1}停止训练")
                    break
            
            # 计算训练时间
            training_time = time.time() - start_time

            logging.info(f"[Trainer.train] 客户端 {self.client_id} 完成训练，耗时: {training_time:.2f}秒")

            # 保存训练报告到run_history（用于客户端报告）
            self._save_training_report()

            # 返回训练时间（Plato标准格式）
            return training_time
            
        except Exception as e:
            logging.error(f"[Trainer.train] 训练过程出错: {str(e)}")
            raise
            
    def get_last_batch_logits(self):
        """获取最后一个批次的输入数据和logits"""
        try:
            if self.x_batch is None or self.local_logits is None:
                logging.warning(f"[Trainer.get_last_batch_logits] 客户端 {self.client_id} 未找到最后一个批次的logits")
                return None, None
            return self.x_batch, self.local_logits
        except Exception as e:
            logging.error(f"[Trainer.get_last_batch_logits] 获取最后一个批次logits时出错: {str(e)}")
            return None, None

    def _save_training_report(self):
        """保存训练报告到run_history，供客户端使用"""
        try:
            # 计算平均损失和准确率
            avg_loss = self.train_loss / self.num_batches if self.num_batches > 0 else 0.0
            accuracy = self.train_correct / self.train_total if self.train_total > 0 else 0.0

            # 保存到run_history
            if hasattr(self, 'run_history'):
                self.run_history.update_metric("train_loss", avg_loss)
                self.run_history.update_metric("train_accuracy", accuracy)

            logging.info(f"[Trainer._save_training_report] 客户端 {self.client_id} 训练报告 - "
                        f"Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2%}")

        except Exception as e:
            logging.error(f"[Trainer._save_training_report] 保存训练报告时出错: {str(e)}")

    def get_report(self):
        """获取训练报告"""
        try:
            # 计算平均损失和准确率
            avg_loss = self.train_loss / self.num_batches if self.num_batches > 0 else 0.0
            accuracy = self.train_correct / self.train_total if self.train_total > 0 else 0.0
            
            report = {
                'train_loss': avg_loss,
                'train_accuracy': accuracy,
                'train_total': self.train_total,
                'num_batches': self.num_batches,
                'current_epoch': self.current_epoch,
                'total_epochs': self.epochs,
                'client_id': self.client_id
            }
            
            logging.info(f"[Trainer.get_report] 客户端 {self.client_id} 训练报告 - "
                        f"Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2%}")
            
            return report
            
        except Exception as e:
            logging.error(f"[Trainer.get_report] 生成训练报告时出错: {str(e)}")
            return {
                'train_loss': 0.0,
                'train_accuracy': 0.0,
                'train_total': 0,
                'num_batches': 0,
                'current_epoch': 0,
                'total_epochs': self.epochs,
                'client_id': self.client_id
            }

    def test(self, testset, sampler=None):
        """测试模型准确率

        Args:
            testset: 测试数据集
            sampler: 数据采样器（可选）

        Returns:
            float: 测试准确率
        """
        if testset is None:
            logging.warning(f"[Trainer.test] 测试数据集为空")
            return 0.0

        try:
            self.model.eval()
            correct = 0
            total = 0

            # 创建测试数据加载器
            test_loader = torch.utils.data.DataLoader(
                dataset=testset,
                batch_size=self.batch_size,
                shuffle=False,
                num_workers=0,
                pin_memory=False
            )

            with torch.no_grad():
                for data, target in test_loader:
                    data, target = data.to(self.device), target.to(self.device)

                    # 前向传播
                    output = self.model(data)

                    # 计算预测结果
                    _, predicted = torch.max(output.data, 1)
                    total += target.size(0)
                    correct += (predicted == target).sum().item()

            accuracy = correct / total if total > 0 else 0.0
            logging.info(f"[Trainer.test] 测试完成 - 准确率: {accuracy:.2%} ({correct}/{total})")

            return accuracy

        except Exception as e:
            logging.error(f"[Trainer.test] 测试过程出错: {str(e)}")
            return 0.0