#!/usr/bin/env python3
"""
简化的FedBuff测试版本，确保结果输出正常工作
"""

import os
import sys
import asyncio
import logging
import csv
from datetime import datetime

# 添加plato路径
sys.path.append('../../../')

from plato.config import Config
from plato.servers import fedavg


class SimpleFedBuffServer(fedavg.Server):
    """简化的FedBuff服务器，专注于结果输出测试"""

    def __init__(self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )
        
        # 设置简单的结果文件
        self.setup_simple_result_file()
        
        # 初始化计数器
        self.round_count = 0

    def setup_simple_result_file(self):
        """设置简单的结果文件"""
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"fedbuff_simple_test_{timestamp}.csv"
            
            # 获取结果路径
            result_path = Config().params.get("result_path", "./results/test")
            
            # 确保目录存在
            os.makedirs(result_path, exist_ok=True)
            
            # 完整文件路径
            self.result_file = os.path.join(result_path, filename)
            
            # 创建CSV文件并写入表头
            headers = ["round", "elapsed_time", "accuracy", "global_accuracy", "global_accuracy_std"]
            
            with open(self.result_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(headers)
            
            logging.info(f"✅ 简化结果文件创建成功: {self.result_file}")
            print(f"✅ 简化结果文件创建成功: {self.result_file}")
            
        except Exception as e:
            logging.error(f"❌ 创建结果文件失败: {e}")
            print(f"❌ 创建结果文件失败: {e}")
            self.result_file = None

    async def aggregate_deltas(self, updates, deltas_received):
        """简单的聚合方法"""
        # 简单的平均聚合
        total_updates = len(updates)
        
        avg_update = {
            name: self.trainer.zeros(delta.shape)
            for name, delta in deltas_received[0].items()
        }

        for update in deltas_received:
            for name, delta in update.items():
                avg_update[name] += delta * (1 / total_updates)
            await asyncio.sleep(0)

        return avg_update

    def clients_processed(self):
        """重写clients_processed方法以确保结果输出"""
        # 调用父类方法
        super().clients_processed()
        
        # 增加轮次计数
        self.round_count += 1
        
        # 写入结果
        self.write_simple_result()

    def write_simple_result(self):
        """写入简单的结果"""
        if not hasattr(self, 'result_file') or not self.result_file:
            print("❌ 没有结果文件")
            return
        
        try:
            # 准备数据
            elapsed_time = getattr(self, 'wall_time', 0) - getattr(self, 'initial_wall_time', 0)
            accuracy = getattr(self, 'accuracy', 0.0)
            
            # 模拟一些数据（如果实际数据不可用）
            if accuracy == 0.0:
                accuracy = 0.1 + self.round_count * 0.05  # 模拟递增的准确率
            
            global_accuracy = accuracy * 0.9  # 模拟全局准确率
            global_accuracy_std = 0.02 + self.round_count * 0.001  # 模拟标准差
            
            # 准备行数据
            row_data = [
                self.round_count,
                elapsed_time,
                accuracy,
                global_accuracy,
                global_accuracy_std
            ]
            
            # 写入CSV
            with open(self.result_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(row_data)
            
            logging.info(f"✅ 第{self.round_count}轮结果写入成功: {row_data}")
            print(f"✅ 第{self.round_count}轮结果写入成功: {row_data}")
            
        except Exception as e:
            logging.error(f"❌ 写入结果失败: {e}")
            print(f"❌ 写入结果失败: {e}")


def main():
    """主函数"""
    print("🚀 启动简化FedBuff测试")
    print("=" * 50)
    
    try:
        # 加载配置
        config_file = "fedbuff_MNIST_standard.yml"
        Config.load_config(config_file)
        print(f"✅ 配置加载成功: {config_file}")
        
        # 创建服务器
        server = SimpleFedBuffServer()
        print(f"✅ 服务器创建成功")
        
        # 模拟几轮训练结果
        print(f"\n🔄 模拟训练过程...")
        for i in range(3):
            print(f"--- 第{i+1}轮 ---")
            server.clients_processed()
        
        print(f"\n🎉 测试完成！")
        
        # 检查结果文件
        if hasattr(server, 'result_file') and server.result_file:
            if os.path.exists(server.result_file):
                print(f"✅ 结果文件存在: {server.result_file}")
                
                with open(server.result_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"\n📄 文件内容:")
                    print(content)
            else:
                print(f"❌ 结果文件不存在: {server.result_file}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
