clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 5

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8000
    simulate_wall_time: true

data:
    # The training and testing dataset
    datasource: EMNIST

    # Where the dataset is located
    data_path: data

    # Number of samples in each partition
    partition_size: 1128

    # IID or non-IID?
    sampler: iid

    # The random seed for sampling data
    random_seed: 1

trainer:
    # Using the differential privacy trainer
    type: diff_privacy
    dp_epsilon: 10
    dp_delta: 0.0001
    dp_max_grad_norm: 1

    # The maximum number of training rounds
    rounds: 100

    # Whether the training should use multiple GPUs if available
    parallelized: false

    # The maximum number of clients running concurrently
    max_concurrency: 10

    # The target accuracy
    target_accuracy:
        0.95

        # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    model:
        num_classes: 47

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

results:
    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy
