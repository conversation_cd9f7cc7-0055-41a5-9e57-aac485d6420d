clients:


server:

data:


trainer:



algorithm:

    concept_name: IClematis

    generation:
        n_prompt_images: 6
        n_steps: 30

        experiments_dir: StableDiffusionFed_iid_50
        model_path: models/pretrained/
        generation_output_dir: image_generation
        model_name: generation_prompt.pth

        base_context: 
            #- a {} inside ramen-bowl
            # - an oil painting of {}
            # - a {} themed lunchbox
            - a photo of {}
parameters:
