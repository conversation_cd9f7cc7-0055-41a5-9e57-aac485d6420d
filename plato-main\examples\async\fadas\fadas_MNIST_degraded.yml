clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 20

    # The number of clients selected in each round
    per_round: 2  # 减少每轮客户端数量

    # Should the clients compute test accuracy locally?
    do_test: true

    # Should the clients compute test accuracy with global model?
    do_global_test: true

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds - 极端不均匀分布
    simulation_distribution:
        distribution: pareto
        alpha: 0.5  # 更极端的分布

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 15  # 大幅增加睡眠时间

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 10  # 增加平均训练时间

    # 极端恶劣网络环境配置
    network_simulation: true
    
    # 极高延迟配置
    network_delay:
        min_delay: 1000     # 最小延迟1秒
        max_delay: 15000    # 最大延迟15秒
        distribution: exponential
        
    # 极高丢包率配置
    packet_loss:
        loss_rate: 0.5      # 50%丢包率
        burst_loss: true
        
    # 极低带宽配置
    bandwidth_limit:
        upload_speed: 128   # 128KB/s上传
        download_speed: 256 # 256KB/s下载

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8008
    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server?
    simulate_wall_time: true

    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 1  # 降低最小聚合客户端数

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 10  # 增加陈旧度阈值

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: true

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/mnist/degraded
    model_path: models/mnist/degraded

    random_seed: 1

    # (FADAS) - 降低性能的参数
    # beta1/2 for adam
    beta1: 0.5  # 降低beta1
    beta2: 0.9  # 降低beta2
    # the threshold for adapting the global learning rate
    tauc: 5     # 增加阈值
    # eps for adam
    eps: 0.001  # 增加eps
    # learning rate for global model
    global_lr: 0.001  # 大幅降低学习率

algorithm:
    # Aggregation algorithm
    type: fedavg

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 100  # 减少每个客户端的数据量

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution(alpha)
    concentration: 0.01  # 极端非IID分布

    # The size of the testset on the server
    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

    # get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 10

    # The maximum number of clients running concurrently
    max_concurrency: 1  # 降低并发数

    # The target accuracy
    target_accuracy: 1

    # Number of epoches for local training in each communication round
    epochs: 1  # 减少本地训练轮数
    batch_size: 8  # 减少批次大小
    optimizer: SGD
    lr_scheduler: LambdaLR

    # The machine learning model
    model_name: lenet5

parameters:
    model:
        num_classes: 10
        in_channels: 1
    
    optimizer:
        lr: 0.001  # 极低学习率
        momentum: 0.1  # 低动量
        weight_decay: 0.01  # 高权重衰减

    learning_rate:
        gamma: 0.5  # 更激进的学习率衰减
        milestone_steps: 20ep,40ep

results:
    result_path: results/mnist/degraded

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std
