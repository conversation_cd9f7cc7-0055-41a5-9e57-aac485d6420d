[INFO][23:06:46]: 日志系统已初始化
[INFO][23:06:46]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250728_230646.log
[INFO][23:06:46]: 日志级别: INFO
[WARNING][23:06:46]: 无法获取系统信息: No module named 'psutil'
[INFO][23:06:46]: 🚀 ReFedScaFL 训练开始
[INFO][23:06:46]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][23:06:46]: 开始时间: 2025-07-28 23:06:46
[INFO][23:06:46]: [Client None] 基础初始化完成
[INFO][23:06:46]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][23:06:46]: 创建并缓存共享模型
[INFO][23:06:46]: [93m[1m[36416] Logging runtime results to: ././results/refedscafl_cifar10_resnet9/36416.csv.[0m
[INFO][23:06:46]: [Server #36416] Started training on 10 clients with 5 per round.
[INFO][23:06:46]: 服务器参数配置完成：
[INFO][23:06:46]: - 客户端数量: total=10, per_round=5
[INFO][23:06:46]: - 权重参数: success=0.8, distill=0.2
[INFO][23:06:46]: - SCAFL参数: V=1.0, tau_max=5
[INFO][23:06:46]: 从共享资源模型提取并缓存全局权重
[INFO][23:06:46]: [Server #36416] Configuring the server...
[INFO][23:06:46]: Training: 500 rounds or accuracy above 80.0%

[INFO][23:06:46]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][23:06:46]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][23:06:46]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][23:06:46]: [Trainer Init] 训练器初始化完成，参数：batch_size=64, learning_rate=0.1, epochs=3
[INFO][23:06:46]: Algorithm: fedavg
[INFO][23:06:46]: Data source: CIFAR10
[INFO][23:06:48]: Starting client #1's process.
[INFO][23:06:48]: Starting client #2's process.
[INFO][23:06:48]: Starting client #3's process.
[INFO][23:06:48]: Starting client #4's process.
[INFO][23:06:48]: Starting client #5's process.
[INFO][23:06:48]: Setting the random seed for selecting clients: 1
[INFO][23:06:48]: Starting a server at address 127.0.0.1 and port 8091.
[INFO][23:07:00]: [Server #36416] A new client just connected.
[INFO][23:07:00]: [Server #36416] New client with id #2 arrived.
[INFO][23:07:00]: [Server #36416] Client process #38896 registered.
[INFO][23:07:00]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][23:07:00]: [Server #36416] A new client just connected.
[INFO][23:07:00]: [Server #36416] New client with id #5 arrived.
[INFO][23:07:00]: [Server #36416] Client process #1888 registered.
[INFO][23:07:00]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][23:07:00]: [Server #36416] A new client just connected.
[INFO][23:07:00]: [Server #36416] New client with id #4 arrived.
[INFO][23:07:00]: [Server #36416] Client process #26016 registered.
[INFO][23:07:00]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][23:07:00]: [Server #36416] A new client just connected.
[INFO][23:07:00]: [Server #36416] New client with id #1 arrived.
[INFO][23:07:00]: [Server #36416] Client process #27684 registered.
[INFO][23:07:00]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][23:07:00]: [Server #36416] A new client just connected.
[INFO][23:07:00]: [Server #36416] New client with id #3 arrived.
[INFO][23:07:00]: [Server #36416] Client process #37544 registered.
[INFO][23:07:00]: [Server #36416] Starting training.
[INFO][23:07:00]: [93m[1m
[Server #36416] Starting round 1/500.[0m
[INFO][23:07:00]: [Server #36416] Selected clients: [3, 2, 5, 1, 4]
[INFO][23:07:00]: [Server #36416] Selecting client #3 for training.
[INFO][23:07:00]: [Server #36416] Sending the current model to client #3 (simulated).
[INFO][23:07:00]: [Server #36416] Sending 18.75 MB of payload data to client #3 (simulated).
[INFO][23:07:00]: [Server #36416] Selecting client #2 for training.
[INFO][23:07:00]: [Server #36416] Sending the current model to client #2 (simulated).
[INFO][23:07:00]: [Server #36416] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][23:07:00]: [Server #36416] Selecting client #5 for training.
[INFO][23:07:00]: [Server #36416] Sending the current model to client #5 (simulated).
[INFO][23:07:01]: [Server #36416] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][23:07:01]: [Server #36416] Selecting client #1 for training.
[INFO][23:07:01]: [Server #36416] Sending the current model to client #1 (simulated).
[INFO][23:07:01]: [Server #36416] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][23:07:01]: [Server #36416] Selecting client #4 for training.
[INFO][23:07:01]: [Server #36416] Sending the current model to client #4 (simulated).
[INFO][23:07:01]: [Server #36416] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][23:07:01]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][23:14:57]: [Server #36416] An existing client just disconnected.
[WARNING][23:14:57]: [Server #36416] Client process #1888 disconnected and removed from this server, 4 client processes are remaining.
[WARNING][23:14:57]: [93m[1m[Server #36416] Closing the server due to a failed client.[0m
[INFO][23:14:57]: [Server #36416] Training concluded.
[INFO][23:14:57]: [Server #36416] Model saved to ./models/refedscafl/cifar10_resnet9/resnet_9.pth.
[INFO][23:14:57]: [Server #36416] Closing the server.
[INFO][23:14:57]: Closing the connection to client #38896.
[INFO][23:14:57]: Closing the connection to client #26016.
[INFO][23:14:57]: Closing the connection to client #27684.
[INFO][23:14:57]: Closing the connection to client #37544.
