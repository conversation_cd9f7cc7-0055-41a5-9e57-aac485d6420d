import logging
from collections import OrderedDict

from plato.algorithms import fedavg


class Algorithm(fedavg.Algorithm): # 定义FedAsync算法类
    """The federated learning algorithm for FedAsync, used by the server."""

    async def aggregate_weights( # 聚合客户端的权重
        self, baseline_weights, weights_received, mixing=0.9, **kwargs
    ):
        """Aggregates the weights received into baseline weights."""
        # Actually update the global model's weights (PyTorch-only implementation) 
        # 实际更新全局模型的权重（仅PyTorch实现）
        # logging.info("new aggregation method for FedAsync") # 日志记录新的聚合方法
        updated_weights = OrderedDict()
        for name, weight in baseline_weights.items():
            updated_weights[name] = (
                weight * (1 - mixing) + weights_received[name] * mixing 
            )

        return updated_weights
