Metadata-Version: 2.4
Name: plato-learn
Version: 1.0
Summary: Packaged version of the Plato framework for federated learning research
Home-page: https://github.com/TL-System/plato
Author: 
License: Apache-2.0
Keywords: machine-learning,deep-learning,edge-learning,federated-learning
Classifier: Development Status :: 3 - Alpha
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Education
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: numpy
Requires-Dist: python-socketio
Requires-Dist: aiohttp
Requires-Dist: requests
Requires-Dist: boto3
Requires-Dist: pyyaml
Requires-Dist: datasets
Requires-Dist: transformers
Requires-Dist: opacus
Requires-Dist: gym
Requires-Dist: zstd
Requires-Dist: torch-optimizer
Requires-Dist: timm
Requires-Dist: lightly
Requires-Dist: evaluate
Requires-Dist: matplotlib
Requires-Dist: lpips
Provides-Extra: tests
Requires-Dist: pytest; extra == "tests"
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

Plato: a new scalable research framework for federated learning
