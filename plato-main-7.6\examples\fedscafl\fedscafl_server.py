import logging
import os
import random

from collections import OrderedDict

from plato.config import Config
from plato.servers import fedavg

import time
import numpy as np
from math import log2
from types import SimpleNamespace
import pandas as pd
from plato.utils import csv_processor

from plato.servers.fedavg import Server as fedavg_Server
# 由于 "fedscafl_algorithm" 导入项未使用，故移除该导入
from fedscafl_algorithm import Algorithm

# 确保日志配置正确
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("FedSCAFL")

class Server(fedavg_Server):
    """
    自定义的SCAFL联邦学习服务器类，继承自FedAvg服务器。
    """

    def __init__(self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None):
        self.client_Hk_comp = {}
        self.client_Hk_comm = {}
        self.client_Hk = {}
        self.aggregation_start_time = time.time()
        # 初始化客户端估计持续时间字典
        self.client_estimated_duration = {}
        # 初始化客户端完成时间字典
        self.client_completion_time = {}
        
        # 从配置中获取客户端相关设置
        self.total_clients = Config().clients.total_clients if hasattr(Config(), 'clients') and hasattr(Config().clients, 'total_clients') else 100
        self.clients_per_round = Config().clients.per_round if hasattr(Config(), 'clients') and hasattr(Config().clients, 'per_round') else 20
        logger.info(f"初始化客户端设置：总客户端数={self.total_clients}, 每轮选择={self.clients_per_round}")
        
        # 保存传入的算法类，以便在需要时重新创建算法实例
        self.algorithm_class = algorithm

        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks
        )

        self.client_staleness = {}
        self.staleness_queue = {}
        self.tau_max = Config().tau_max if hasattr(Config(), 'tau_max') else 10
        self.V = Config().V if hasattr(Config(), 'V') else 10
        self.historical_Ds = []
        self.staleness_history = []
        self.accuracy_log = []
        self.first_reach_time = {0.7: None, 0.8: None, 0.9: None}
        self.start_time = time.time()
        self.run_id = "scafl_metrics"
        # 获取结果保存路径（使用配置中的路径或默认路径）
        result_path = getattr(Config(), 'result_path', 'E:\\Experiments\\orgin-plato-main\\plato-main\\examples\\async\\scafl\\results\\scafl_metrics')
        # 初始化CSV表头（包含avg_staleness）
        # 确保初始化时写入完整表头（包含avg_staleness）
        csv_processor.initialize_csv(f"{self.run_id}.csv", ['round', 'elapsed_time', 'accuracy', 'avg_staleness'], result_path)
        logger.info("FedSCAFL服务器初始化完成，tau_max=%d, V=%d", self.tau_max, self.V)
        
        # 确保算法实例被正确初始化，并且有client_staleness属性
        self.ensure_algorithm_initialized()

    def ensure_algorithm_initialized(self):
        """确保算法实例被正确初始化"""
        if not hasattr(self, 'algorithm') or self.algorithm is None:
            logger.warning("算法实例为空，尝试创建新实例")
            if hasattr(self, 'algorithm_class') and self.algorithm_class is not None:
                if hasattr(self, 'trainer') and self.trainer is not None:
                    logger.info("使用trainer创建算法实例")
                    self.algorithm = self.algorithm_class(trainer=self.trainer)
                else:
                    logger.warning("没有可用的trainer，创建空算法实例")
                    self.algorithm = self.algorithm_class()
            else:
                logger.error("无法创建算法实例，algorithm_class未定义")
                return
        
        # 确保算法有client_staleness属性
        if not hasattr(self.algorithm, 'client_staleness'):
            logger.info("为算法添加client_staleness属性")
            self.algorithm.client_staleness = self.client_staleness
    
    def initialize_global_model(self):
        """扩展父类方法，确保模型和算法正确初始化"""
        try:
            result = super().initialize_global_model()
            # 在初始化全局模型后，确保算法有正确设置
            self.ensure_algorithm_initialized()
            
            # 检查模型是否成功初始化
            if hasattr(self.algorithm, 'model') and self.algorithm.model is None:
                logger.warning("全局模型初始化后仍为None，尝试创建新模型")
                try:
                    import torch
                    import torch.nn as nn
                    from plato.config import Config
                    
                    # 检查配置中的模型名称
                    model_name = Config().trainer.model_name if hasattr(Config().trainer, 'model_name') else 'lenet5'
                    num_classes = Config().parameters.model.num_classes if hasattr(Config(), 'parameters') and hasattr(Config().parameters, 'model') and hasattr(Config().parameters.model, 'num_classes') else 10
                    
                    logger.info(f"创建模型: {model_name}, num_classes={num_classes}")
                    
                    # 创建LeNet5模型
                    class LeNet5(nn.Module):
                        """A simple LeNet-5 model."""
                        def __init__(self, num_classes=10):
                            super().__init__()
                            self.conv1 = nn.Conv2d(1, 6, 5)
                            self.conv2 = nn.Conv2d(6, 16, 5)
                            self.conv3 = nn.Conv2d(16, 120, 5)
                            self.fc4 = nn.Linear(120, 84)
                            self.fc5 = nn.Linear(84, num_classes)

                        def forward(self, x):
                            out = nn.functional.relu(self.conv1(x))
                            out = nn.functional.max_pool2d(out, 2)
                            out = nn.functional.relu(self.conv2(out))
                            out = nn.functional.max_pool2d(out, 2)
                            out = nn.functional.relu(self.conv3(out))
                            out = out.view(out.size(0), -1)
                            out = nn.functional.relu(self.fc4(out))
                            out = self.fc5(out)
                            return out
                    
                    # 创建模型实例
                    model = LeNet5(num_classes=num_classes)
                    
                    # 设置给算法
                    self.algorithm.model = model
                    logger.info("成功创建并设置了LeNet5模型")
                except Exception as e:
                    logger.error("创建模型失败: %s", str(e), exc_info=True)
            
            return result
        except Exception as e:
            logger.error("初始化全局模型时出错: %s", str(e), exc_info=True)
            # 如果初始化失败，尝试创建最小模型
            self.initialize_minimal_model()
            return None
    
    def initialize_minimal_model(self):
        """在主流程初始化失败时创建一个最小模型"""
        try:
            import torch
            import torch.nn as nn
            
            # 创建一个简单的模型
            class SimpleModel(nn.Module):
                def __init__(self):
                    super().__init__()
                    self.fc1 = nn.Linear(784, 10)
                
                def forward(self, x):
                    return self.fc1(x.view(x.size(0), -1))
            
            model = SimpleModel()
            
            # 如果算法实例存在，设置模型
            if hasattr(self, 'algorithm') and self.algorithm is not None:
                self.algorithm.model = model
                logger.info("已创建并设置最小模型")
            else:
                logger.warning("算法实例不存在，无法设置模型")
        except Exception as e:
            logger.error("创建最小模型失败: %s", str(e), exc_info=True)

    def customize_server_response(self, server_response=None, client_id=None):
        """自定义服务器响应，在发送响应前确保算法正确初始化"""
        logger.debug("自定义服务器响应，client_id=%s", client_id)
        # 确保算法被正确初始化
        self.ensure_algorithm_initialized()
        # 调用父类方法处理响应
        return super().customize_server_response(server_response, client_id)

    def get_cid(self, cid_obj):
        if isinstance(cid_obj, SimpleNamespace):
            for key in ['id', 'client_id', 'sid', 'name']:
                if hasattr(cid_obj, key):
                    return getattr(cid_obj, key)
            raise ValueError(f"[错误] 无法从 SimpleNamespace 获取合法 ID: {cid_obj}")
        return cid_obj

    def estimate_client_times(self, cid):
        """
        估计客户端的计算和通信时间（更贴近现实无线边缘环境）
        """
        cid = self.get_cid(cid)
        logger.debug("开始估计客户端 %s 的时间", cid)
        
        a = 0.5  # 计算时间基线
        mu = 1.0
        param_count = 100000  # 模型参数量
        # 距离分布更广，模拟异构性
        distance = np.random.uniform(0.5, 3.0)  # 单位: km

        # 计算时间估计
        H_comp = a + np.random.exponential(1/mu)
        self.client_Hk_comp[cid] = H_comp

        # 通信参数
        B = np.random.uniform(50e3, 500e3)  # 带宽: 50~500kHz
        P = 10 ** ((10 - 30) / 10)  # 发射功率 (W)
        N0 = np.random.uniform(1e-15, 1e-13)  # 噪声功率谱密度 (W/Hz)
        L = 10 ** (3.5 * np.log10(distance))  # 路径损耗
        SNR = P / (N0 * B * L)  # 信噪比

        # 信道容量
        C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps

        # 模型大小（100K参数，32位）
        model_size = param_count * 32  # bits

        # 基础延迟（如协议、排队、接入等）
        latency = np.random.uniform(0.05, 0.3)  # 50~300ms

        # 通信时间 = 传输时延 + 基础延迟
        H_comm = model_size / C + latency
        # 控制在0.2~2s区间
        H_comm = np.clip(H_comm, 0.2, 2.0)
        self.client_Hk_comm[cid] = H_comm

        # 总时间估计
        H_k = H_comp + H_comm
        self.client_Hk[cid] = H_k
        self.client_estimated_duration[cid] = H_k
        logger.debug("估计客户端%s训练+通信时间: %f秒 (计算: %f秒, 通信: %f秒, 距离: %.2fkm, 带宽: %.0fHz, SNR: %.2e)",
                     cid, H_k, H_comp, H_comm, distance, B, SNR)


    def get_dkt(self, cid):
        cid = self.get_cid(cid)
        H_k = self.client_Hk.get(cid, 0)
        tau_k_t = self.client_staleness.get(cid, 0)
        t = len(self.historical_Ds)
        start_index = max(0, t - tau_k_t)
        end_index = t - 1
        sum_Dj = sum(self.historical_Ds[start_index:end_index + 1]) if self.historical_Ds else 0
        d_k_t = max(H_k - sum_Dj, 0)
        
        logger.debug("计算客户端%s的过时度: H_k=%.4f, tau_k_t=%d, sum_Dj=%.4f, d_k_t=%.4f", 
                    cid, H_k, tau_k_t, sum_Dj, d_k_t)
        return d_k_t

    async def select_clients(self, clients_pool=None):
        """覆盖父类方法，确保使用SCAFL的客户端选择算法"""
        logger.info("FedSCAFL 客户端选择过程开始")
        
        # 确保算法已正确初始化
        self.ensure_algorithm_initialized()
        
        if not clients_pool:
            clients_pool = list(range(1, self.total_clients + 1))
        
        # 从可用客户端中随机选择样本，然后应用SCAFL算法
        clients_count = min(self.clients_per_round, len(clients_pool))
        random_clients = random.sample(clients_pool, clients_count)
        
        try:
            # 调用SCAFL特有的客户端选择算法
            selected_clients = self.select_clients_by_scafl(random_clients, clients_count)
            logger.info(f"使用SCAFL算法选择了 {len(selected_clients)} 个客户端: {selected_clients}")
        except Exception as e:
            logger.error("SCAFL客户端选择算法失败: %s", str(e), exc_info=True)
            logger.warning("回退到随机选择客户端")
            selected_clients = random_clients[:clients_count]
            logger.info(f"随机选择了 {len(selected_clients)} 个客户端: {selected_clients}")
        
        # 为所有选中的客户端估计时间
        for cid in selected_clients:
            try:
                self.estimate_client_times(cid)
            except Exception as e:
                logger.error(f"为客户端{cid}估计时间失败: {str(e)}")
        
        return selected_clients
        
    def process_reports(self):
        """处理客户端上传的报告，计算陈旧度"""
        logger.info(f"处理客户端报告，当前轮次: {self.current_round}")
        
        # 确保算法已正确初始化
        self.ensure_algorithm_initialized()
        
        # 在处理报告前检查报告是否存在
        if not hasattr(self, 'received_reports') or not self.received_reports:
            logger.warning("没有收到任何客户端报告")
            return super().process_reports()
        
        logger.info(f"收到 {len(self.received_reports)} 个客户端报告")
        for report in self.received_reports:
            # 提取客户端ID
            cid = self.get_cid(report.client_id)
            
            # 输出报告基本信息
            logger.info(f"客户端 {cid} 的报告: 来自轮次 {report.round}, 当前轮次 {self.current_round}")
            
            # 更新客户端陈旧度
            self.client_staleness[cid] = self.current_round - report.round
            
            # 确保算法可以访问陈旧度信息
            if hasattr(self.algorithm, 'client_staleness'):
                self.algorithm.client_staleness[cid] = self.client_staleness[cid]
            
            logger.info(f"客户端 {cid} 陈旧度: {self.client_staleness[cid]}")
        
        # 调用父类方法处理剩余逻辑
        return super().process_reports()

    def clients_processed(self):
        """覆盖以确保在每轮结束时进行SCAFL特定处理"""
        logger.info("开始处理客户端更新，本轮有 %d 个更新", len(self.updates) if hasattr(self, 'updates') else 0)
        
        # 确保算法已正确初始化
        self.ensure_algorithm_initialized()
        
        super().clients_processed()

        start_time = getattr(self, "aggregation_start_time", time.time())
        D_j = time.time() - start_time
        self.historical_Ds.append(D_j)
        logger.debug("本轮聚合耗时 %.4f 秒", D_j)

        for cid in self.updates:
            cid = self.get_cid(cid)
            d_k_t = self.get_dkt(cid)
            if not hasattr(self, 'd_k_t_dict'):
                self.d_k_t_dict = {}
            self.d_k_t_dict[cid] = d_k_t

            prev_q = self.staleness_queue.get(cid, 0)
            tau_k = self.client_staleness.get(cid, 0)
            beta_k = 1
            new_q = max(prev_q + (tau_k + 1) * (1 - beta_k) - self.tau_max, 0)
            self.staleness_queue[cid] = new_q
            logger.debug("客户端%s: 过时度=%.4f, tau_k=%d, prev_q=%.4f, new_q=%.4f", 
                        cid, d_k_t, tau_k, prev_q, new_q)
            
            # 确保算法的client_staleness也被更新
            if hasattr(self.algorithm, 'client_staleness'):
                self.algorithm.client_staleness[cid] = tau_k
                logger.debug("已同步客户端%s的过时度到算法: %d", cid, tau_k)

        current_staleness = sum(self.d_k_t_dict.values()) / len(self.d_k_t_dict) if self.d_k_t_dict else 0
        self.staleness_history.append(current_staleness)
        logger.info("本轮平均过时度: %.4f", current_staleness)

    def select_clients_by_scafl(self, N_t, M_max):
        """使用SCAFL算法选择最优客户端集合"""
        logger.info("使用SCAFL算法选择客户端，候选数量：%d，最大选择数量：%d", len(N_t), M_max)
        
        # 打印当前所有客户端的过时度信息
        logger.info("当前轮次: %d, 客户端过时度状态:", self.current_round)
        for cid, staleness in self.client_staleness.items():
            logger.info("  客户端 %s: 过时度=%d", cid, staleness)
        
        # 确保输入参数有效
        if not N_t:
            logger.warning("候选客户端列表为空，无法应用SCAFL算法")
            return []
        
        if M_max <= 0:
            logger.warning("最大选择数量必须大于0，当前值：%d", M_max)
            return []
        
        try:
            # 按客户端完成时间排序
            N_t_sorted = sorted(N_t, key=lambda cid: self.client_completion_time.get(self.get_cid(cid), 0))
            logger.debug("客户端按完成时间排序: %s", N_t_sorted)
            
            # 输出候选客户端的信息
            logger.info("候选客户端详细信息:")
            for cid in N_t_sorted:
                cid_str = self.get_cid(cid)
                completion_time = self.client_completion_time.get(cid_str, 0)
                staleness = self.client_staleness.get(cid_str, 0)
                comm_time = self.client_Hk_comm.get(cid_str, 0)
                logger.info("  客户端 %s: 完成时间=%.2f, 过时度=%d, 通信时间=%.2f", 
                          cid, completion_time, staleness, comm_time)
            
            # 初始化变量
            M_t_star = []
            selected_ids = set()
            D_t = 0
            Q_k_sum = 0
            
            # 针对每个候选客户端，选择最优的子集
            for i in range(min(M_max, len(N_t))):
                best_delta_S = float('inf')
                best_client = None

                for cid in N_t_sorted:
                    cid_str = self.get_cid(cid)
                    if cid_str in selected_ids:
                        continue

                    tau_k = self.client_staleness.get(cid_str, 0)
                    alpha_k_t = self.client_weights.get(cid_str, 1.0) if hasattr(self, 'client_weights') else 1.0
                    Q_k = 1
                    Q_term = Q_k * ((tau_k + 1) * (1 - alpha_k_t) - self.tau_max)

                    comm_time = self.client_Hk_comm.get(cid_str, 0)
                    temp_D = max(D_t, comm_time)
                    temp_S = self.V * temp_D + (Q_k_sum + Q_term)
                    delta_S = temp_S - (self.V * D_t + Q_k_sum)
                    
                    logger.debug("评估客户端%s: tau_k=%d, Q_term=%.2f, temp_D=%.2f, delta_S=%.2f", 
                               cid_str, tau_k, Q_term, temp_D, delta_S)

                    if delta_S < best_delta_S:
                        best_delta_S = delta_S
                        best_client = cid

                if best_client is not None:
                    M_t_star.append(best_client)
                    selected_ids.add(self.get_cid(best_client))
                    D_t = max(D_t, self.client_Hk_comm.get(self.get_cid(best_client), 0))
                    tau_k = self.client_staleness.get(self.get_cid(best_client), 0)
                    alpha_k_t = self.client_weights.get(self.get_cid(best_client), 1.0) if hasattr(self, 'client_weights') else 1.0
                    Q_k = 1
                    Q_k_sum += Q_k * ((tau_k + 1) * (1 - alpha_k_t) - self.tau_max)
                    min_S_M_t = self.V * D_t + Q_k_sum
                    logger.debug("第%d轮选择: 选中客户端%s，当前D_t=%.4f，Q_k_sum=%.4f", 
                               i+1, best_client, D_t, Q_k_sum)
            
            # 如果没有选择到任何客户端，则使用原始列表中的前M_max个作为降级选择
            if not M_t_star and N_t:
                logger.warning("SCAFL算法没有选择到任何客户端，使用前%d个候选客户端作为降级选择", min(M_max, len(N_t)))
                M_t_star = N_t[:min(M_max, len(N_t))]
            
            logger.info("SCAFL算法选择了%d个客户端: %s", len(M_t_star), M_t_star)
            return M_t_star
        except Exception as e:
            logger.error("SCAFL客户端选择算法出现错误: %s", str(e), exc_info=True)
            # 降级到使用原始客户端列表的前M_max个
            result = N_t[:min(M_max, len(N_t))]
            logger.warning("因错误回退到使用前%d个候选客户端", len(result))
            return result

    async def wrap_up(self) -> None:
        logger.info("完成第%d轮训练，准备保存检查点和更新指标", self.current_round)
        try:
            self.save_to_checkpoint()

            if hasattr(self, 'accuracy'):
                self.accuracy_log.append(self.accuracy)
                for threshold in self.first_reach_time:
                    if self.first_reach_time[threshold] is None and self.accuracy >= threshold:
                        self.first_reach_time[threshold] = time.time() - self.start_time
                        logger.info("首次达到%.1f%%准确率，用时%.2f秒", threshold*100, self.first_reach_time[threshold])

            current_round = self.current_round
            avg_staleness = self.staleness_history[-1] if self.staleness_history else 0.0
            logging.info(f"\n【第{current_round}轮结束】\n平均过时程度: {avg_staleness:.4f}\n当前准确率: {self.accuracy:.4f}")

            if self.accuracy_log:
                avg_last_10 = sum(self.accuracy_log[-10:]) / min(10, len(self.accuracy_log))
                best_acc = max(self.accuracy_log)
                logging.info(f"过去10轮平均准确率: {avg_last_10:.4f}, 最佳准确率: {best_acc:.4f}")

            # 确定CSV文件路径
            import time
            from datetime import datetime
            # 只在第一次运行时生成时间戳文件名，后续轮次使用同一个文件
            if not hasattr(self, 'csv_filename'):
                self.csv_filename = f"scafl_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}.csv"
                self.csv_path = os.path.join(Config().result_path if hasattr(Config(), 'result_path') else './results/scafl_metrics', self.csv_filename)
                os.makedirs(os.path.dirname(self.csv_path), exist_ok=True)
                logging.info(f"当前CSV文件路径：{self.csv_path}")  # 添加路径确认日志
            
            # 确保后续轮次也能访问csv_path
            csv_path = self.csv_path

            # 检查文件是否存在，不存在则初始化表头
            file_exists = os.path.exists(csv_path)
            logging.info(f"CSV文件是否存在：{file_exists}")  # 添加文件存在性检查日志
            if not file_exists:
                csv_processor.initialize_csv(
                    csv_path,
                    ['round', 'elapsed_time', 'accuracy', 'avg_staleness'],
                    result_path=Config().result_path if hasattr(Config(), 'result_path') else './results/scafl_metrics'
                )
                logging.info("已初始化CSV表头")  # 添加表头初始化成功日志

            # 写入当前轮次数据
            csv_processor.write_csv(
                csv_path,
                [current_round, time.time() - self.start_time, self.accuracy, avg_staleness]
            )
            logging.info(f"已写入第{current_round}轮数据：[round={current_round}, elapsed_time={time.time() - self.start_time:.2f}, accuracy={self.accuracy:.4f}, avg_staleness={avg_staleness:.4f}]")  # 添加数据写入成功日志

            target_accuracy = getattr(Config().trainer, "target_accuracy", None)
            if target_accuracy and self.accuracy >= target_accuracy:
                logging.info(f"🎉 已达到目标准确率 {target_accuracy*100:.2f}%，总耗时 {time.time() - self.start_time:.2f}s，结束训练！")
                await self._close()
                return

            if self.current_round >= Config().trainer.rounds:
                logging.info(f"⏳ 已完成所有 {Config().trainer.rounds} 轮训练，结束训练！")
                await self._close()
                return
        except Exception as e:
            logger.error("wrap_up过程中发生错误: %s", str(e), exc_info=True)
            # 即使出错，我们也尝试继续后续轮次的训练
            
    async def aggregate_weights(self, updates, baseline_weights, weights_received):
        """调用外部定义的聚合函数"""
        logger.info("开始聚合权重，共有%d个客户端更新", len(updates) if updates else 0)
        
        # 确保算法已正确初始化
        self.ensure_algorithm_initialized()
        
        try:
            # 确保算法的client_staleness是最新的
            for cid in self.client_staleness:
                if hasattr(self.algorithm, 'client_staleness'):
                    self.algorithm.client_staleness[cid] = self.client_staleness[cid]
            
            result = await self.algorithm.aggregate_weights(
                updates, baseline_weights, weights_received
            )
            logger.info("权重聚合完成，结果类型: %s", type(result).__name__)
            return result
        except Exception as e:
            logger.error("权重聚合失败: %s", str(e), exc_info=True)
            # 如果算法聚合失败，使用FedAvg方法作为回退
            logger.warning("使用FedAvg作为回退方法聚合权重")
            return await super().aggregate_weights(updates, baseline_weights, weights_received)
        
    def save_staleness_data(self):
        logger.info("保存过时度和准确率数据到CSV文件")
        df = pd.DataFrame({
            'round': range(1, len(self.staleness_history)+1),
            'staleness': self.staleness_history,
            'accuracy': self.accuracy_log[:len(self.staleness_history)]
        })
        df.to_csv('training_metrics.csv', index=False)
        logger.info("数据已保存到training_metrics.csv")

    def training_complete(self):
        logger.info("训练完成，执行最终清理工作")
        super().training_complete()
        self.save_staleness_data()

        if self.staleness_history:
            logging.info("已通过training_metrics.csv保存合并指标（过时程度+准确率）")
        else:
            df_acc = pd.DataFrame({
                'round': range(1, len(self.accuracy_log)+1),
                'accuracy': self.accuracy_log
            })
            df_acc.to_csv('accuracy_records.csv', index=False)
            logger.info("已将准确率记录保存到accuracy_records.csv")

        logging.info("达到各测试精度阈值的时间:")
        for thres, ts in self.first_reach_time.items():
            logging.info(f"  {int(thres*100)}%: {'未达到' if ts is None else f'{ts:.2f}秒'}")

    async def _select_clients(self):
        """重写内部的_select_clients方法，确保我们的SCAFL选择逻辑被调用"""
        logger.info("内部_select_clients方法被调用，确保使用SCAFL算法")
        
        # 确保算法初始化
        self.ensure_algorithm_initialized()
        
        # 获取可用客户端列表
        available_clients = {}
        for client_id in self.clients:
            available_clients[client_id] = self.clients[client_id]
        
        if not available_clients:
            logger.warning("没有可用的客户端")
            return
        
        # 使用SCAFL选择客户端
        clients_pool = list(available_clients.keys())
        logger.info(f"客户端池中有 {len(clients_pool)} 个客户端可供选择")
        
        try:
            # 直接使用我们的SCAFL客户端选择逻辑
            client_ids = await self.select_clients(clients_pool)
            logger.info(f"通过SCAFL选择了 {len(client_ids)} 个客户端: {client_ids}")
            
            # 提取模型权重
            payload = self.algorithm.extract_weights()
            
            # 选择的客户端进行训练
            for client_id in client_ids:
                self.train_clients.append(client_id)
                logger.info(f"[服务器] 选择客户端 #{client_id} 进行训练")
                await self._send_weights_to_client(client_id, payload)
        except Exception as e:
            logger.error(f"选择客户端过程中发生错误: {str(e)}", exc_info=True)
            # 出错时尝试使用父类方法
            await super()._select_clients()

    async def _process_reports(self):
        """
        重写内部的_process_reports方法，确保我们的过时度计算逻辑被调用
        """
        logger.info("内部_process_reports方法被调用，确保更新客户端过时度信息")
        
        # 确保算法初始化
        self.ensure_algorithm_initialized()
        
        if not hasattr(self, 'received_reports') or not self.received_reports:
            logger.warning("没有收到任何报告，调用父类方法")
            return await super()._process_reports()
        
        try:
            # 调用我们的处理报告逻辑
            self.process_reports()
            
            # 清理
            self.received_reports = []
            
            # 根据当前轮次状态决定是否开始新的训练轮次
            if len(self.train_clients) == 0 and not self.no_more_clients():
                await self._select_clients()
        except Exception as e:
            logger.error(f"处理报告过程中发生错误: {str(e)}", exc_info=True)
            # 出错时使用父类方法
            await super()._process_reports()

    async def round_start(self, round_number: int) -> None:
        """在每轮训练开始时调用"""
        logger.info(f"====== 开始第 {round_number} 轮训练 ======")
        self.current_round = round_number
        
        # 清空本轮状态
        self.updates = []
        
        # 更新聚合开始时间
        self.aggregation_start_time = time.time()
        
        # 确保client_staleness字典已初始化
        if not hasattr(self, 'client_staleness'):
            self.client_staleness = {}
        
        # 初始化模型（如果尚未初始化）
        if hasattr(self, 'algorithm') and self.algorithm is not None:
            if not hasattr(self.algorithm, 'model') or self.algorithm.model is None:
                # 初始化模型
                self.initialize_global_model()
        
        logger.info(f"第 {round_number} 轮初始化完成，准备选择客户端")
        
        # 调用父类方法
        await super().round_start(round_number)

    async def on_client_training_finished(self, client_id, report=None):
        """客户端训练完成时的回调函数"""
        logger.info(f"客户端 {client_id} 完成训练，更新过时度信息")
        
        # 记录当前时间
        current_time = time.time()
        
        # 存储客户端完成时间，用于后续客户端选择
        cid = self.get_cid(client_id)
        self.client_completion_time[cid] = current_time
        
        if report is not None:
            # 记录客户端的过时度
            self.client_staleness[cid] = self.current_round - report.round
            logger.info(f"更新客户端 {cid} 的过时度为 {self.client_staleness[cid]}")
            
            # 确保算法实例也有这个信息
            if hasattr(self.algorithm, 'client_staleness'):
                self.algorithm.client_staleness[cid] = self.client_staleness[cid]
        
        # 调用父类方法
        return await super().on_client_training_finished(client_id, report)