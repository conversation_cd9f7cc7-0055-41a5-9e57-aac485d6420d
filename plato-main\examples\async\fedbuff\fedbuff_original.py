"""
A federated learning training session using FedBuff (Original Version).

Reference:

<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al., "Federated Learning with Buffered Asynchronous Aggregation,
" in Proc. International Conference on Artificial Intelligence and Statistics (AISTATS 2022).

https://proceedings.mlr.press/v151/nguyen22b/nguyen22b.pdf
"""

import fedbuff_server_original


def main():
    """A Plato federated learning training session using FedBuff (Original Version)."""
    server = fedbuff_server_original.Server()
    server.run()


if __name__ == "__main__":
    main()
