"""
A federated learning training session using FedAsync.

Reference:

<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. "Asynchronous federated optimization,"
in Proc. 12th Annual Workshop on Optimization for Machine Learning (OPT 2020).

https://opt-ml.org/papers/2020/paper_28.pdf
"""

import fedasync_client
import fedasync_algorithm
import fedasync_server
import fedasync_trainer


def main():
    """A Plato federated learning training session using FedAsync."""
    algorithm = fedasync_algorithm.Algorithm
    trainer = fedasync_trainer.Trainer
    client = fedasync_client.Client(algorithm=algorithm, trainer=trainer)
    server = fedasync_server.Server(algorithm=algorithm, trainer=trainer)
    server.run(client)

if __name__ == "__main__":
    main()
