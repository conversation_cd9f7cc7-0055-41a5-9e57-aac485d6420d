#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import random
import time
import numpy as np
from math import log2
import asyncio

# 配置日志
logging.basicConfig(level=logging.DEBUG, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("FedSCAFL-Test")

# 将父目录添加到 PYTHONPATH
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 创建一个模拟 Config 类
class MockConfig:
    def __init__(self):
        self.clients = type('obj', (object,), {
            'total_clients': 20,
            'per_round': 5
        })
        self.tau_max = 5
        self.V = 10
        self.trainer = type('obj', (object,), {
            'rounds': 10
        })

# 创建一个模拟 Server 类
class MockServer:
    def __init__(self):
        self.current_round = 1
        self.client_staleness = {}
        self.client_Hk = {}
        self.client_Hk_comp = {}
        self.client_Hk_comm = {}
        self.client_completion_time = {}
        self.tau_max = 5
        self.V = 10
        self.historical_Ds = [0.1, 0.2, 0.3]
        self.d_k_t_dict = {}
        self.staleness_queue = {}
        self.client_weights = {}
        
        # 初始化一些模拟客户端数据
        for i in range(1, 21):  # 20个客户端
            self.client_staleness[str(i)] = random.randint(0, 4)
            self.client_Hk_comp[str(i)] = random.uniform(0.5, 2.0)
            self.client_Hk_comm[str(i)] = random.uniform(0.2, 1.0)
            self.client_Hk[str(i)] = self.client_Hk_comp[str(i)] + self.client_Hk_comm[str(i)]
            self.client_completion_time[str(i)] = time.time() - random.uniform(0, 60)
            self.client_weights[str(i)] = 1.0
        
    def get_cid(self, cid_obj):
        """获取客户端ID的辅助方法"""
        return str(cid_obj)
        
    def get_dkt(self, cid):
        """计算客户端过时度的辅助方法"""
        cid = self.get_cid(cid)
        H_k = self.client_Hk.get(cid, 0)
        tau_k_t = self.client_staleness.get(cid, 0)
        t = len(self.historical_Ds)
        start_index = max(0, t - tau_k_t)
        end_index = t - 1
        sum_Dj = sum(self.historical_Ds[start_index:end_index + 1])
        d_k_t = max(H_k - sum_Dj, 0)
        
        logger.debug("计算客户端%s的过时度: H_k=%.4f, tau_k_t=%d, sum_Dj=%.4f, d_k_t=%.4f", 
                    cid, H_k, tau_k_t, sum_Dj, d_k_t)
        return d_k_t
        
    def select_clients_by_scafl(self, N_t, M_max):
        """使用SCAFL算法选择最优客户端集合"""
        logger.info("使用SCAFL算法选择客户端，候选数量：%d，最大选择数量：%d", len(N_t), M_max)
        
        # 打印当前所有客户端的过时度信息
        logger.info("当前轮次: %d, 客户端过时度状态:", self.current_round)
        for cid, staleness in self.client_staleness.items():
            logger.info("  客户端 %s: 过时度=%d", cid, staleness)
        
        # 确保输入参数有效
        if not N_t:
            logger.warning("候选客户端列表为空，无法应用SCAFL算法")
            return []
        
        if M_max <= 0:
            logger.warning("最大选择数量必须大于0，当前值：%d", M_max)
            return []
        
        try:
            # 按客户端完成时间排序
            N_t_sorted = sorted(N_t, key=lambda cid: self.client_completion_time.get(self.get_cid(cid), 0))
            logger.debug("客户端按完成时间排序: %s", N_t_sorted)
            
            # 输出候选客户端的信息
            logger.info("候选客户端详细信息:")
            for cid in N_t_sorted:
                cid_str = self.get_cid(cid)
                completion_time = self.client_completion_time.get(cid_str, 0)
                staleness = self.client_staleness.get(cid_str, 0)
                comm_time = self.client_Hk_comm.get(cid_str, 0)
                logger.info("  客户端 %s: 完成时间=%.2f, 过时度=%d, 通信时间=%.2f", 
                          cid, completion_time, staleness, comm_time)
            
            # 初始化变量
            M_t_star = []
            selected_ids = set()
            D_t = 0
            Q_k_sum = 0
            
            # 针对每个候选客户端，选择最优的子集
            for i in range(min(M_max, len(N_t))):
                best_delta_S = float('inf')
                best_client = None

                for cid in N_t_sorted:
                    cid_str = self.get_cid(cid)
                    if cid_str in selected_ids:
                        continue

                    tau_k = self.client_staleness.get(cid_str, 0)
                    alpha_k_t = self.client_weights.get(cid_str, 1.0)
                    Q_k = 1
                    Q_term = Q_k * ((tau_k + 1) * (1 - alpha_k_t) - self.tau_max)

                    comm_time = self.client_Hk_comm.get(cid_str, 0)
                    temp_D = max(D_t, comm_time)
                    temp_S = self.V * temp_D + (Q_k_sum + Q_term)
                    delta_S = temp_S - (self.V * D_t + Q_k_sum)
                    
                    logger.debug("评估客户端%s: tau_k=%d, Q_term=%.2f, temp_D=%.2f, delta_S=%.2f", 
                               cid_str, tau_k, Q_term, temp_D, delta_S)

                    if delta_S < best_delta_S:
                        best_delta_S = delta_S
                        best_client = cid

                if best_client is not None:
                    M_t_star.append(best_client)
                    selected_ids.add(self.get_cid(best_client))
                    D_t = max(D_t, self.client_Hk_comm.get(self.get_cid(best_client), 0))
                    tau_k = self.client_staleness.get(self.get_cid(best_client), 0)
                    alpha_k_t = self.client_weights.get(self.get_cid(best_client), 1.0)
                    Q_k = 1
                    Q_k_sum += Q_k * ((tau_k + 1) * (1 - alpha_k_t) - self.tau_max)
                    min_S_M_t = self.V * D_t + Q_k_sum
                    logger.debug("第%d轮选择: 选中客户端%s，当前D_t=%.4f，Q_k_sum=%.4f", 
                               i+1, best_client, D_t, Q_k_sum)
            
            # 如果没有选择到任何客户端，则使用原始列表中的前M_max个作为降级选择
            if not M_t_star and N_t:
                logger.warning("SCAFL算法没有选择到任何客户端，使用前%d个候选客户端作为降级选择", min(M_max, len(N_t)))
                M_t_star = N_t[:min(M_max, len(N_t))]
            
            logger.info("SCAFL算法最终选择了%d个客户端: %s", len(M_t_star), M_t_star)
            return M_t_star
        except Exception as e:
            logger.error("SCAFL客户端选择算法出现错误: %s", str(e), exc_info=True)
            # 降级到使用原始客户端列表的前M_max个
            result = N_t[:min(M_max, len(N_t))]
            logger.warning("因错误回退到使用前%d个候选客户端", len(result))
            return result

async def test_client_selection():
    """测试客户端选择算法"""
    logger.info("开始测试FedSCAFL客户端选择算法")
    
    # 创建模拟服务器
    server = MockServer()
    
    # 生成一些候选客户端
    candidate_clients = [str(i) for i in range(1, 11)]  # 选择客户端1-10
    
    # 调用客户端选择算法
    selected_clients = server.select_clients_by_scafl(candidate_clients, 5)
    
    # 打印结果
    print("\n\n")
    print("=" * 50)
    print("最终选择的客户端:", selected_clients)
    print("=" * 50)
    print("选中客户端详细信息:")
    for cid in selected_clients:
        staleness = server.client_staleness.get(cid, 0)
        comm_time = server.client_Hk_comm.get(cid, 0)
        comp_time = server.client_Hk_comp.get(cid, 0)
        print(f"  客户端 {cid}: 过时度={staleness}, 通信时间={comm_time:.2f}, 计算时间={comp_time:.2f}")
    print("=" * 50)
    print("\n\n")
    
    logger.info("测试完成")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_client_selection()) 