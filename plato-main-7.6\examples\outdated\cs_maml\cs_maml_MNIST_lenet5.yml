clients:
    # The total number of clients
    total_clients: 2

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: true

server:
    type: fedavg_cross_silo
    address: 127.0.0.1
    port: 8000
    do_test: true

    checkpoint_path: results/test/checkpoint
    model_path: results/test/model

data:
    # The training and testing dataset
    datasource: MNIST

    # Where the dataset is located
    data_path: ../../data

    # Number of samples in each partition
    partition_size: 200

    # IID or non-IID?
    sampler: iid

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The maximum number of training rounds
    rounds: 2

    # The maximum number of clients running concurrently
    max_concurrency: 1

    # The target accuracy
    target_accuracy: 0.98

    # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 10
    optimizer: SGD

    meta_learning_rate: 0.01

algorithm:
    # Aggregation algorithm
    type: fedavg

    # Cross-silo training
    cross_silo: true

    # The total number of silos (edge servers)
    total_silos: 1

    # The number of local aggregation rounds on edge servers before sending
    # aggregated weights to the central server
    local_rounds: 1

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

results:
    # Write the following parameter(s) into a CSV
    types: round, accuracy, personalization_accuracy, elapsed_time, comm_time, round_time
    result_path: results/test
