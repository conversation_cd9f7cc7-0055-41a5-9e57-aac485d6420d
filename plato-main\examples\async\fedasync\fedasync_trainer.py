import copy
import logging
import torch

from plato.models import registry as models_registry
from plato.config import Config
from plato.trainers import basic

class Trainer(basic.Trainer):
    def __init__(self, model=None, callbacks=None):
        super().__init__(model, callbacks)
        self.global_model = copy.deepcopy(self.model)

    def train(self, trainset, sampler, servermodel=None, **kwargs) -> float:
        # 加载全局模型
        self.global_model.load_state_dict(servermodel, strict=True)
        return super().train(trainset, sampler)

    def perform_forward_and_backward_passes(self, config, examples, labels):
        init_global_weights = _flatten_weights_from_model(self.global_model, self.device)
        current_weights = _flatten_weights_from_model(self.model, self.device)
        parameter_mu = (
            Config().clients.proximal_term_penalty_constant
            if hasattr(Config().clients, "proximal_term_penalty_constant")
            else 1
        )
        proximal_term = (
                parameter_mu
                / 2
                * torch.linalg.norm(current_weights - init_global_weights) # ord=2
        )
        self.optimizer.zero_grad()
        outputs = self.model(examples)

        loss = self._loss_criterion(outputs, labels) + proximal_term
        self._loss_tracker.update(loss, labels.size(0))

        if "create_graph" in config:
            loss.backward(create_graph=config["create_graph"])
        else:
            loss.backward()

        self.optimizer.step()

        return loss

def _flatten_weights_from_model(model, device):
    """Return the weights of the given model as a 1-D tensor"""
    weights = torch.tensor([], requires_grad=False).to(device)
    model.to(device)
    for param in model.parameters():
        weights = torch.cat((weights, torch.flatten(param)))
    return weights


