clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 50  # 大量客户端

    # The number of clients selected in each round
    per_round: 1  # 每轮只有1个客户端

    # Should the clients compute test accuracy locally?
    do_test: true

    # Should the clients compute test accuracy with global model?
    do_global_test: true

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 0.1  # 最极端的分布

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 30  # 极长睡眠时间

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: true  # 真实睡眠

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 20

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8010
    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server?
    simulate_wall_time: true

    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 1

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 20  # 极高陈旧度

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: false

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/mnist/resource_limited
    model_path: models/mnist/resource_limited

    random_seed: 1

    # (FADAS) - 最差的参数组合
    beta1: 0.01
    beta2: 0.1
    tauc: 20
    eps: 0.1
    global_lr: 0.00001  # 极极低的学习率

algorithm:
    # Aggregation algorithm
    type: fedavg

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 20  # 极少数据

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution(alpha)
    concentration: 0.0001  # 极端非IID

    # The size of the testset on the server
    testset_size: 50

    # The random seed for sampling data
    random_seed: 1

    # get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 20

    # The maximum number of clients running concurrently
    max_concurrency: 1

    # The target accuracy
    target_accuracy: 1

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 1  # 单样本批次
    optimizer: SGD
    lr_scheduler: LambdaLR

    # The machine learning model
    model_name: lenet5

parameters:
    model:
        num_classes: 10
        in_channels: 1
    
    optimizer:
        lr: 0.00001  # 极极低学习率
        momentum: 0.001
        weight_decay: 0.5  # 极高权重衰减

    learning_rate:
        gamma: 0.01  # 极激进衰减
        milestone_steps: 2ep,5ep

results:
    result_path: results/mnist/resource_limited

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std
