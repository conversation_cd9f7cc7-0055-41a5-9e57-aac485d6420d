#!/usr/bin/env python3
"""
测试异步改进和资源优化
验证 asyncio 全家桶解决方案
"""

import os
import sys
import time
import logging
import asyncio
import subprocess
from datetime import datetime

def setup_logging():
    """设置日志"""
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    logging.basicConfig(level=logging.INFO, format=log_format)
    return logging.getLogger(__name__)

async def monitor_system_resources():
    """监控系统资源使用情况"""
    logger = logging.getLogger(__name__)
    
    try:
        import psutil
        
        while True:
            # 获取系统资源信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            logger.info(f"📊 系统资源: CPU={cpu_percent:.1f}%, 内存={memory.percent:.1f}%")
            
            # 如果资源使用过高，发出警告
            if cpu_percent > 80:
                logger.warning(f"⚠️ CPU使用率过高: {cpu_percent:.1f}%")
            if memory.percent > 80:
                logger.warning(f"⚠️ 内存使用率过高: {memory.percent:.1f}%")
            
            await asyncio.sleep(10)  # 每10秒监控一次
            
    except ImportError:
        logger.warning("⚠️ psutil未安装，无法监控系统资源")
    except Exception as e:
        logger.error(f"❌ 监控系统资源时出错: {e}")

async def monitor_log_files():
    """监控日志文件生成"""
    logger = logging.getLogger(__name__)
    
    logs_dir = "logs"
    initial_files = set()
    
    if os.path.exists(logs_dir):
        initial_files = set(os.listdir(logs_dir))
    
    logger.info(f"📋 初始日志文件数量: {len(initial_files)}")
    
    while True:
        try:
            if os.path.exists(logs_dir):
                current_files = set(os.listdir(logs_dir))
                new_files = current_files - initial_files
                
                if new_files:
                    for new_file in new_files:
                        file_path = os.path.join(logs_dir, new_file)
                        file_size = os.path.getsize(file_path)
                        logger.info(f"📄 新日志文件: {new_file} (大小: {file_size} 字节)")
                    
                    initial_files = current_files
                
                # 检查最新日志文件的大小变化
                log_files = [f for f in current_files if f.startswith("sc_afl_server_")]
                if log_files:
                    latest_log = max(log_files, key=lambda x: os.path.getctime(os.path.join(logs_dir, x)))
                    log_path = os.path.join(logs_dir, latest_log)
                    log_size = os.path.getsize(log_path)
                    
                    if log_size > 0:
                        logger.info(f"📊 最新日志 {latest_log}: {log_size} 字节")
            
            await asyncio.sleep(5)  # 每5秒检查一次
            
        except Exception as e:
            logger.error(f"❌ 监控日志文件时出错: {e}")
            await asyncio.sleep(5)

async def run_scafl_experiment():
    """运行SC-AFL实验"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("🚀 启动SC-AFL实验...")
        
        # 启动实验进程
        cmd = [sys.executable, "sc_afl.py", "-c", "sc_afl_cifar10_resnet9_with_network"]
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.STDOUT
        )
        
        logger.info(f"📋 实验进程已启动，PID: {process.pid}")
        
        # 监控进程输出
        output_lines = []
        client_creation_count = 0
        training_start_count = 0
        aggregation_count = 0
        error_count = 0
        
        start_time = time.time()
        timeout = 120  # 2分钟超时
        
        try:
            while True:
                # 检查超时
                if time.time() - start_time > timeout:
                    logger.warning(f"⏰ 实验运行超时 ({timeout}秒)，终止进程")
                    process.terminate()
                    break
                
                # 读取输出
                try:
                    line = await asyncio.wait_for(process.stdout.readline(), timeout=1.0)
                    if not line:
                        break
                    
                    line_str = line.decode().strip()
                    if line_str:
                        print(line_str)
                        output_lines.append(line_str)
                        
                        # 统计关键事件
                        if "开始创建客户端" in line_str:
                            client_creation_count += 1
                        elif "异步训练协程已创建" in line_str:
                            training_start_count += 1
                        elif "聚合完成" in line_str:
                            aggregation_count += 1
                        elif "ERROR" in line_str or "Exception" in line_str:
                            error_count += 1
                            logger.error(f"❌ 检测到错误: {line_str}")
                            
                except asyncio.TimeoutError:
                    # 检查进程是否还在运行
                    if process.returncode is not None:
                        break
                    continue
                    
        except Exception as monitor_error:
            logger.error(f"❌ 监控进程输出时出错: {monitor_error}")
        
        # 等待进程结束
        try:
            await asyncio.wait_for(process.wait(), timeout=10.0)
        except asyncio.TimeoutError:
            logger.warning("⏰ 等待进程结束超时，强制终止")
            process.kill()
            await process.wait()
        
        return_code = process.returncode
        end_time = time.time()
        duration = end_time - start_time
        
        # 分析结果
        logger.info("=" * 50)
        logger.info("📊 实验结果统计:")
        logger.info(f"⏱️ 运行时间: {duration:.2f}秒")
        logger.info(f"🔚 进程返回码: {return_code}")
        logger.info(f"👥 客户端创建次数: {client_creation_count}")
        logger.info(f"🏃 训练启动次数: {training_start_count}")
        logger.info(f"🔄 聚合完成次数: {aggregation_count}")
        logger.info(f"❌ 错误次数: {error_count}")
        
        # 判断实验是否成功
        success = (
            client_creation_count > 0 and
            training_start_count > 0 and
            error_count == 0 and
            duration > 10  # 至少运行10秒
        )
        
        if success:
            logger.info("🎉 SC-AFL异步改进实验成功！")
        else:
            logger.error("❌ SC-AFL异步改进实验失败")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 运行实验时发生异常: {e}")
        return False

async def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🔧 开始测试SC-AFL异步改进")
    logger.info("=" * 50)
    
    improvements = [
        "🔄 使用 asyncio 协程替代线程",
        "🔒 添加异步锁防止竞态条件",
        "💾 自动释放CUDA显存",
        "🛡️ 增强异常处理和恢复机制",
        "📊 实时监控系统资源和日志"
    ]
    
    for improvement in improvements:
        logger.info(improvement)
    
    logger.info("=" * 50)
    
    try:
        # 创建并发任务
        tasks = [
            asyncio.create_task(run_scafl_experiment(), name="scafl_experiment"),
            asyncio.create_task(monitor_system_resources(), name="resource_monitor"),
            asyncio.create_task(monitor_log_files(), name="log_monitor")
        ]
        
        # 等待实验完成
        experiment_task = tasks[0]
        monitor_tasks = tasks[1:]
        
        # 运行实验，同时监控资源
        done, pending = await asyncio.wait(
            [experiment_task], 
            timeout=180,  # 3分钟总超时
            return_when=asyncio.FIRST_COMPLETED
        )
        
        # 取消监控任务
        for task in monitor_tasks:
            task.cancel()
        
        # 等待所有任务清理完成
        await asyncio.gather(*monitor_tasks, return_exceptions=True)
        
        # 获取实验结果
        if experiment_task in done:
            success = await experiment_task
        else:
            logger.error("❌ 实验超时未完成")
            experiment_task.cancel()
            success = False
        
        if success:
            logger.info("🎉 所有测试通过！异步改进成功")
        else:
            logger.error("❌ 测试失败，需要进一步优化")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 主函数执行失败: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        sys.exit(1)
