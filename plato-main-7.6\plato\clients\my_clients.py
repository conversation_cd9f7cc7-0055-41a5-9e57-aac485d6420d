# re_safl_client.py
from plato.clients import client as plato_client
import random

class Client(plato_client.Client):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.upload_success_prob = 0.7  # 模拟上传成功的概率（可调）

    async def _send(self, payload):
        # 加入上传成功的模拟判定
        if random.random() > self.upload_success_prob:
            print(f"[Client {self.client_id}] 模拟上传失败，本轮不上传。")
            return  # 模拟上传失败：直接 return，不执行上传逻辑

        print(f"[Client {self.client_id}] 模拟上传成功，开始上传。")
        await super()._send(payload)  # 调用父类的上传逻辑
