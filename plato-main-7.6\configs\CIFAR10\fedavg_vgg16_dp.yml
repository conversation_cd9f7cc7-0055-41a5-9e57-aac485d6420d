clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: true

server:
    address: 127.0.0.1
    port: 8030

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition
    partition_size: 50000

    # IID or non-IID?
    sampler: iid

trainer:
    # Using the differential privacy trainer
    type: diff_privacy
    dp_epsilon: 1
    dp_delta: 0.00001
    dp_max_grad_norm: 1

    # The maximum number of training rounds
    rounds: 10000

    # The maximum number of clients running concurrently
    max_concurrency: 1

    # The target accuracy
    target_accuracy: 0.88

    # The machine learning model
    model_name: vgg_16

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 128
    optimizer: Adam

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    model:
        num_classes: 10

    optimizer:
        lr: 0.01
        weight_decay: 0.0
