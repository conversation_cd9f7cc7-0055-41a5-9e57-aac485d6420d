"""
Replicated from cnn/genotypes.py in https://github.com/quark0/darts.

Added more different geneotypes generated by different papers, all based on Darts space.

Including:
FedRLNAS: https://ieeexplore.ieee.org/stamp/stamp.jsp?tp=&arnumber=9546522
FedNAS: https://arxiv.org/abs/2004.08546#
"""

from collections import namedtuple

Genotype = namedtuple("Genotype", "normal normal_concat reduce reduce_concat")

PRIMITIVES = [
    "none",
    "max_pool_3x3",
    "avg_pool_3x3",
    "skip_connect",
    "sep_conv_3x3",
    "sep_conv_5x5",
    "dil_conv_3x3",
    "dil_conv_5x5",
]

# Provided by Darts.
NASNet = Genotype(
    normal=[
        ("sep_conv_5x5", 1),
        ("sep_conv_3x3", 0),
        ("sep_conv_5x5", 0),
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 1),
        ("skip_connect", 0),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("sep_conv_3x3", 1),
        ("skip_connect", 1),
    ],
    normal_concat=[2, 3, 4, 5, 6],
    reduce=[
        ("sep_conv_5x5", 1),
        ("sep_conv_7x7", 0),
        ("max_pool_3x3", 1),
        ("sep_conv_7x7", 0),
        ("avg_pool_3x3", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 3),
        ("avg_pool_3x3", 2),
        ("sep_conv_3x3", 2),
        ("max_pool_3x3", 1),
    ],
    reduce_concat=[4, 5, 6],
)

# Provided by Darts.
AmoebaNet = Genotype(
    normal=[
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("sep_conv_3x3", 0),
        ("sep_conv_5x5", 2),
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 3),
        ("sep_conv_3x3", 1),
        ("skip_connect", 1),
        ("skip_connect", 0),
        ("avg_pool_3x3", 1),
    ],
    normal_concat=[4, 5, 6],
    reduce=[
        ("avg_pool_3x3", 0),
        ("sep_conv_3x3", 1),
        ("max_pool_3x3", 0),
        ("sep_conv_7x7", 2),
        ("sep_conv_7x7", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("conv_7x1_1x7", 0),
        ("sep_conv_3x3", 5),
    ],
    reduce_concat=[3, 4, 6],
)

# Searched by Darts.
DARTS_V1 = Genotype(
    normal=[
        ("sep_conv_3x3", 1),
        ("sep_conv_3x3", 0),
        ("skip_connect", 0),
        ("sep_conv_3x3", 1),
        ("skip_connect", 0),
        ("sep_conv_3x3", 1),
        ("sep_conv_3x3", 0),
        ("skip_connect", 2),
    ],
    normal_concat=[2, 3, 4, 5],
    reduce=[
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("skip_connect", 2),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 0),
        ("skip_connect", 2),
        ("skip_connect", 2),
        ("avg_pool_3x3", 0),
    ],
    reduce_concat=[2, 3, 4, 5],
)

# Searched by Darts.
DARTS_V2 = Genotype(
    normal=[
        ("sep_conv_3x3", 0),
        ("sep_conv_3x3", 1),
        ("sep_conv_3x3", 0),
        ("sep_conv_3x3", 1),
        ("sep_conv_3x3", 1),
        ("skip_connect", 0),
        ("skip_connect", 0),
        ("dil_conv_3x3", 2),
    ],
    normal_concat=[2, 3, 4, 5],
    reduce=[
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("skip_connect", 2),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("skip_connect", 2),
        ("skip_connect", 2),
        ("max_pool_3x3", 1),
    ],
    reduce_concat=[2, 3, 4, 5],
)

DARTS = DARTS_V2

# Searched by FedRLNAS, random search.
FL_1 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("skip_connect", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 1),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 2),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 3),
        ("dil_conv_5x5", 0),
        ("skip_connect", 4),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: glace 3000, total 6000, client 10.
RL1 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 0),
        ("skip_connect", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 1),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: warmup 10k, search 6k, lr=3e-3, client 10, 97.02,
RL2 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("skip_connect", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 4),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: warmup 10k, search 6k, lr=3e-3, client 10, v2, 96.0,
RL3 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("sep_conv_3x3", 0),
        ("max_pool_3x3", 2),
        ("avg_pool_3x3", 2),
        ("sep_conv_3x3", 4),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: warmup 10k, search 6k, lr=3e-3, client 10, v3, 96.16.
RL4 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("avg_pool_3x3", 1),
        ("skip_connect", 2),
        ("avg_pool_3x3", 2),
        ("dil_conv_5x5", 4),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 0),
        ("dil_conv_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: warmup 10k, search 6k, lr=3e-3, client 10, v4, 96.96.
RL5 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 2),
        ("skip_connect", 0),
        ("sep_conv_3x3", 4),
        ("avg_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 0),
        ("skip_connect", 2),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: warmup 10k, search 6k, lr=3e-3, client 10, v5, 96.92.
RL6 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 2),
        ("dil_conv_3x3", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: warmup 10k, search 6k, lr=3e-3, client 10, seed 2 96.25.
RL7 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("skip_connect", 2),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 3),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 0),
        ("dil_conv_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: acc 96.07.
RL8 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 2),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 2),
        ("dil_conv_5x5", 3),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("sep_conv_3x3", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: acc 96.11.
RL9 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("sep_conv_3x3", 0),
        ("max_pool_3x3", 2),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 3),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS 96.81.
RL10 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 2),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("dil_conv_3x3", 1),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by Darts: seed 1345 96.56.
RL11 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 2),
        ("skip_connect", 2),
        ("skip_connect", 1),
        ("avg_pool_3x3", 3),
        ("skip_connect", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 3),
        ("max_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: acc 96.06.
RL12 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 2),
        ("dil_conv_5x5", 3),
        ("skip_connect", 2),
        ("sep_conv_5x5", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("skip_connect", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS.
RL13 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 2),
        ("sep_conv_3x3", 3),
        ("max_pool_3x3", 2),
        ("sep_conv_5x5", 3),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("skip_connect", 0),
        ("dil_conv_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("dil_conv_5x5", 3),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: warmup 10k, search 6k, lr=3e-3, client 10, seed 5, acc 97.38 *BEST.
RL14 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("max_pool_3x3", 2),
        ("sep_conv_3x3", 3),
        ("skip_connect", 0),
        ("dil_conv_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 1),
        ("skip_connect", 0),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("skip_connect", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 1),
        ("sep_conv_5x5", 4),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: client 20.
C20 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 2),
        ("sep_conv_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: client 50.
C50 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 2),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 2),
        ("sep_conv_5x5", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_3x3", 1),
        ("max_pool_3x3", 1),
        ("sep_conv_3x3", 4),
    ],
    reduce_concat=range(2, 6),
)


# Searched by FedRLNAS acc 96.77.
SP = Genotype(
    normal=[
        ("skip_connect", 0),
        ("sep_conv_5x5", 1),
        ("dil_conv_5x5", 2),
        ("sep_conv_3x3", 0),
        ("skip_connect", 1),
        ("skip_connect", 0),
        ("dil_conv_5x5", 2),
        ("dil_conv_5x5", 4),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("skip_connect", 1),
        ("skip_connect", 2),
        ("avg_pool_3x3", 0),
        ("dil_conv_3x3", 2),
        ("max_pool_3x3", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS.
DEEPRL = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("max_pool_3x3", 1),
        ("sep_conv_3x3", 0),
        ("max_pool_3x3", 0),
        ("sep_conv_5x5", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: Same Setting as NONIID results reported in paper.
NONIID = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 3),
        ("sep_conv_5x5", 4),
        ("sep_conv_5x5", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("dil_conv_5x5", 1),
        ("sep_conv_5x5", 3),
        ("sep_conv_5x5", 1),
        ("dil_conv_3x3", 1),
        ("sep_conv_3x3", 3),
    ],
    reduce_concat=range(2, 6),
)


# soft
# Searched by FedRLNAS: Same Setting as 'fresh' reported in paper.
zero = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("skip_connect", 0),
        ("sep_conv_3x3", 2),
        ("sep_conv_3x3", 4),
        ("avg_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: Same Setting as 'stale' reported in paper.
stale = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 1),
        ("sep_conv_5x5", 3),
        ("sep_conv_3x3", 4),
        ("dil_conv_5x5", 3),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 3),
        ("avg_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: Same Setting as 'throw' reported in paper.
throw = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("sep_conv_5x5", 1),
        ("max_pool_3x3", 0),
        ("skip_connect", 1),
        ("avg_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 0),
        ("dil_conv_3x3", 4),
    ],
    reduce_concat=range(2, 6),
)

# Searched byFedRLNAS: Same Setting as 'use' reported in paper.
use = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 2),
        ("skip_connect", 0),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("avg_pool_3x3", 0),
        ("dil_conv_3x3", 2),
        ("sep_conv_5x5", 2),
        ("max_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: limitation of stale epoch 0.
stale0 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 2),
        ("skip_connect", 2),
        ("max_pool_3x3", 1),
        ("skip_connect", 2),
        ("avg_pool_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 4),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: limitation of stale epoch 2.
stale2 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 2),
        ("max_pool_3x3", 2),
        ("dil_conv_5x5", 3),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("skip_connect", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 1),
        ("sep_conv_3x3", 4),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: limitation of stale epoch 3.
stale3 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 2),
        ("sep_conv_3x3", 3),
        ("max_pool_3x3", 2),
        ("max_pool_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_3x3", 0),
        ("dil_conv_3x3", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_3x3", 1),
        ("max_pool_3x3", 0),
        ("sep_conv_5x5", 3),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: limitation of stale epoch 4.
stale4 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("skip_connect", 2),
        ("sep_conv_3x3", 3),
        ("max_pool_3x3", 2),
        ("dil_conv_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched byFedRLNAS: limitation of stale epoch 5.
stale5 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 2),
        ("max_pool_3x3", 0),
        ("dil_conv_3x3", 4),
        ("skip_connect", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("skip_connect", 0),
        ("sep_conv_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 2),
        ("sep_conv_5x5", 3),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: limitation of stale epoch 6.
stale6 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 2),
        ("dil_conv_5x5", 3),
        ("sep_conv_5x5", 4),
        ("avg_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("skip_connect", 2),
        ("sep_conv_3x3", 1),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 2),
        ("avg_pool_3x3", 1),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: limitation of stale epoch 7.
stale7 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 2),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("skip_connect", 2),
        ("skip_connect", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("sep_conv_5x5", 1),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 3),
        ("sep_conv_3x3", 1),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: limitation of stale epoch 2.
stale_2 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("skip_connect", 0),
        ("max_pool_3x3", 2),
        ("avg_pool_3x3", 2),
        ("dil_conv_5x5", 4),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 4),
        ("sep_conv_3x3", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: limitation of stale epoch 0, seed=2.
zero_2 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 2),
        ("max_pool_3x3", 0),
        ("sep_conv_3x3", 4),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("dil_conv_3x3", 1),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: same setting as 'throw' in paper, waiting 40% of clients.
throw60 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 1),
        ("sep_conv_5x5", 4),
        ("avg_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("sep_conv_3x3", 1),
        ("avg_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: same setting as 'throw' in paper, waiting first 40% of clients.
use60 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("skip_connect", 2),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("dil_conv_5x5", 1),
        ("max_pool_3x3", 0),
        ("sep_conv_3x3", 2),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_3x3", 1),
        ("avg_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: same setting as 'stale' in paper, waiting 40% of clients.
stale60 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("skip_connect", 2),
        ("skip_connect", 0),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("dil_conv_3x3", 1),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: same setting as 'throw' in paper, waiting 70% of clients.
throw30 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 2),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("skip_connect", 1),
        ("avg_pool_3x3", 4),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("skip_connect", 0),
        ("skip_connect", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_3x3", 1),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("dil_conv_3x3", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: same setting as 'use' in paper, waiting 70% of clients.
use30 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 2),
        ("avg_pool_3x3", 2),
        ("skip_connect", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("sep_conv_5x5", 1),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("skip_connect", 0),
        ("sep_conv_3x3", 1),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: same setting as 'stale' in paper, seed 3421.
stale3421 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 2),
        ("dil_conv_5x5", 4),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: same setting as 'throw' in paper, seed 3421.
throw3421 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("skip_connect", 2),
        ("sep_conv_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_5x5", 1),
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 0),
        ("dil_conv_5x5", 1),
        ("dil_conv_3x3", 0),
        ("sep_conv_5x5", 3),
        ("avg_pool_3x3", 4),
        ("max_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: same setting as 'stale' in paper, seed 3241.
throw3241 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_3x3", 0),
        ("dil_conv_5x5", 1),
        ("sep_conv_3x3", 0),
        ("sep_conv_3x3", 2),
        ("avg_pool_3x3", 1),
        ("sep_conv_5x5", 3),
        ("max_pool_3x3", 4),
        ("dil_conv_5x5", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: same setting as 'use' in paper, seed 3241.
use3241 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 1),
        ("max_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_3x3", 2),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 3),
        ("dil_conv_5x5", 1),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS.
dishard = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS.
dissoft = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 2),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: client 20 seed 5.
c2s5 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 2),
        ("sep_conv_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 3),
        ("avg_pool_3x3", 1),
        ("max_pool_3x3", 2),
    ],
    reduce_concat=range(2, 6),
)
# Searched by FedRLNAS: client 20 seed 2.
c2s2 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 2),
        ("sep_conv_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: client 20 seed 7.
c2s7 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
        ("sep_conv_3x3", 1),
        ("sep_conv_5x5", 4),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 2),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: client 50 seed 5.
c5s5 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 1),
        ("dil_conv_3x3", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 4),
        ("sep_conv_5x5", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 1),
        ("sep_conv_5x5", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("dil_conv_5x5", 2),
        ("sep_conv_3x3", 4),
        ("sep_conv_5x5", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: client 50 seed 2.
c5s2 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 1),
        ("dil_conv_3x3", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 4),
        ("sep_conv_5x5", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_3x3", 0),
        ("max_pool_3x3", 1),
        ("sep_conv_5x5", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("dil_conv_5x5", 2),
        ("sep_conv_3x3", 4),
        ("sep_conv_5x5", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: client 50 seed 7.
c5s7 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("dil_conv_3x3", 1),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 2),
        ("sep_conv_3x3", 3),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_3x3", 0),
        ("max_pool_3x3", 1),
        ("sep_conv_5x5", 1),
        ("avg_pool_3x3", 0),
        ("dil_conv_5x5", 2),
        ("avg_pool_3x3", 0),
        ("sep_conv_3x3", 4),
        ("sep_conv_5x5", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: NONIID, only has 5 classes.
non02359 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("sep_conv_3x3", 0),
        ("sep_conv_5x5", 1),
        ("max_pool_3x3", 0),
        ("avg_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 1),
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 0),
        ("skip_connect", 2),
        ("dil_conv_3x3", 2),
        ("max_pool_3x3", 1),
        ("sep_conv_5x5", 3),
        ("avg_pool_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: NONIID.
noniid = Genotype(
    normal=[
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 0),
        ("sep_conv_3x3", 1),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 2),
        ("dil_conv_5x5", 4),
        ("skip_connect", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 1),
        ("sep_conv_5x5", 1),
        ("skip_connect", 0),
        ("sep_conv_3x3", 0),
        ("sep_conv_3x3", 3),
        ("sep_conv_3x3", 4),
        ("sep_conv_5x5", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: On Cifar100 dataset.
cifar100 = Genotype(
    normal=[
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("skip_connect", 2),
        ("sep_conv_5x5", 0),
        ("avg_pool_3x3", 2),
        ("avg_pool_3x3", 0),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_3x3", 0),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("sep_conv_3x3", 2),
        ("avg_pool_3x3", 1),
        ("avg_pool_3x3", 0),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 2),
    ],
    reduce_concat=range(2, 6),
)

# svhn
# Searched by FedRLNAS: On SVHN dataset, the same setting as reported in the paper.
svhn = Genotype(
    normal=[
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 2),
        ("avg_pool_3x3", 2),
        ("dil_conv_3x3", 1),
        ("skip_connect", 0),
        ("max_pool_3x3", 3),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("avg_pool_3x3", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_3x3", 2),
        ("avg_pool_3x3", 1),
        ("dil_conv_5x5", 3),
        ("dil_conv_3x3", 2),
        ("max_pool_3x3", 1),
        ("dil_conv_3x3", 0),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: On SVHN dataset, seed 3.
svhn3 = Genotype(
    normal=[
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("dil_conv_3x3", 3),
        ("sep_conv_5x5", 0),
        ("dil_conv_3x3", 4),
        ("skip_connect", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("skip_connect", 0),
        ("sep_conv_3x3", 1),
        ("dil_conv_3x3", 0),
        ("dil_conv_5x5", 1),
        ("sep_conv_3x3", 1),
        ("dil_conv_3x3", 2),
        ("dil_conv_5x5", 3),
        ("skip_connect", 1),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: On SVHN dataset, seed 4.
svhn4 = Genotype(
    normal=[
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 1),
        ("dil_conv_3x3", 4),
        ("dil_conv_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("skip_connect", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_3x3", 2),
        ("dil_conv_3x3", 0),
        ("sep_conv_3x3", 1),
        ("dil_conv_3x3", 2),
        ("sep_conv_5x5", 1),
        ("skip_connect", 2),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: On SVHN dataset, seed 5.
svhn5 = Genotype(
    normal=[
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 0),
        ("max_pool_3x3", 1),
        ("skip_connect", 0),
        ("avg_pool_3x3", 2),
        ("dil_conv_3x3", 2),
        ("avg_pool_3x3", 1),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("sep_conv_5x5", 1),
        ("dil_conv_3x3", 0),
        ("sep_conv_5x5", 1),
        ("sep_conv_3x3", 1),
        ("skip_connect", 3),
        ("dil_conv_3x3", 0),
        ("max_pool_3x3", 1),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedRLNAS: On SVHN dataset.
svhns4 = Genotype(
    normal=[
        ("sep_conv_5x5", 1),
        ("sep_conv_5x5", 0),
        ("sep_conv_5x5", 0),
        ("skip_connect", 1),
        ("dil_conv_5x5", 3),
        ("skip_connect", 2),
        ("dil_conv_5x5", 0),
        ("max_pool_3x3", 2),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("sep_conv_5x5", 1),
        ("max_pool_3x3", 0),
        ("dil_conv_5x5", 1),
        ("max_pool_3x3", 0),
        ("skip_connect", 3),
        ("avg_pool_3x3", 0),
        ("max_pool_3x3", 1),
        ("dil_conv_3x3", 3),
    ],
    reduce_concat=range(2, 6),
)

# Searched by FedNAS.
cvprfed = Genotype(
    normal=[
        ("sep_conv_3x3", 1),
        ("sep_conv_3x3", 0),
        ("sep_conv_3x3", 2),
        ("sep_conv_5x5", 0),
        ("sep_conv_3x3", 1),
        ("sep_conv_5x5", 3),
        ("dil_conv_5x5", 3),
        ("sep_conv_3x3", 4),
    ],
    normal_concat=range(2, 6),
    reduce=[
        ("max_pool_3x3", 0),
        ("skip_connect", 1),
        ("max_pool_3x3", 0),
        ("max_pool_3x3", 2),
        ("max_pool_3x3", 0),
        ("dil_conv_5x5", 1),
        ("max_pool_3x3", 0),
        ("dil_conv_5x5", 2),
    ],
    reduce_concat=range(2, 6),
)
