#!/usr/bin/env python3
"""
FedBuff网络增强功能对比实验

对比原始FedBuff与添加网络模拟功能后的FedBuff的差异。
"""

import os
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import subprocess
import time

def run_experiment(script_name, config_file, experiment_name):
    """运行单个实验"""
    print(f"\n🚀 开始运行实验: {experiment_name}")
    print(f"   脚本: {script_name}")
    print(f"   配置: {config_file}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # 运行实验
        result = subprocess.run(
            ["python", script_name, "-c", config_file],
            capture_output=True,
            text=True,
            timeout=600  # 10分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ {experiment_name} 完成 (耗时: {duration:.1f}秒)")
            return True, duration, result.stdout
        else:
            print(f"❌ {experiment_name} 失败")
            print(f"错误输出: {result.stderr}")
            return False, duration, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {experiment_name} 超时")
        return False, 600, "实验超时"
    except Exception as e:
        print(f"💥 {experiment_name} 异常: {e}")
        return False, 0, str(e)

def find_result_files():
    """查找结果文件"""
    result_files = {}
    
    # 查找原始版本结果
    original_path = "results/mnist_original_fedbuff/01"
    if os.path.exists(original_path):
        for file in os.listdir(original_path):
            if file.endswith('.csv') and 'fedbuff' in file:
                result_files['original'] = os.path.join(original_path, file)
                break
    
    # 查找标准增强版本结果
    standard_path = "results/mnist_standard_fedbuff/01"
    if os.path.exists(standard_path):
        for file in os.listdir(standard_path):
            if file.endswith('.csv') and 'fedbuff' in file:
                result_files['standard'] = os.path.join(standard_path, file)
                break
    
    # 查找网络测试版本结果
    network_path = "results/mnist_network_test_fedbuff/01"
    if os.path.exists(network_path):
        for file in os.listdir(network_path):
            if file.endswith('.csv') and 'fedbuff' in file:
                result_files['network'] = os.path.join(network_path, file)
                break
    
    return result_files

def analyze_results(result_files):
    """分析实验结果"""
    print(f"\n📊 分析实验结果")
    print("=" * 50)
    
    results = {}
    
    for version, file_path in result_files.items():
        if os.path.exists(file_path):
            try:
                df = pd.read_csv(file_path)
                results[version] = df
                print(f"✅ 加载 {version} 结果: {len(df)} 轮数据")
            except Exception as e:
                print(f"❌ 加载 {version} 结果失败: {e}")
        else:
            print(f"❌ {version} 结果文件不存在: {file_path}")
    
    return results

def compare_features(results):
    """对比功能特性"""
    print(f"\n🔍 功能特性对比")
    print("-" * 50)
    
    if 'original' in results:
        original_columns = list(results['original'].columns)
        print(f"📋 原始版本字段: {original_columns}")
    
    if 'standard' in results:
        standard_columns = list(results['standard'].columns)
        print(f"📋 标准增强版字段: {standard_columns}")
    
    if 'network' in results:
        network_columns = list(results['network'].columns)
        print(f"📋 网络测试版字段: {network_columns}")
    
    # 分析新增功能
    if 'original' in results and 'standard' in results:
        original_set = set(results['original'].columns)
        standard_set = set(results['standard'].columns)
        new_features = standard_set - original_set
        
        print(f"\n🆕 标准增强版新增功能:")
        for feature in new_features:
            print(f"   • {feature}")
    
    if 'standard' in results and 'network' in results:
        standard_set = set(results['standard'].columns)
        network_set = set(results['network'].columns)
        network_features = network_set - standard_set
        
        print(f"\n🌐 网络测试版额外功能:")
        for feature in network_features:
            print(f"   • {feature}")

def compare_performance(results):
    """对比性能表现"""
    print(f"\n📈 性能表现对比")
    print("-" * 50)
    
    performance_summary = {}
    
    for version, df in results.items():
        if len(df) > 0:
            final_accuracy = df['accuracy'].iloc[-1] if 'accuracy' in df.columns else 0
            max_accuracy = df['accuracy'].max() if 'accuracy' in df.columns else 0
            total_time = df['elapsed_time'].iloc[-1] if 'elapsed_time' in df.columns else 0
            
            performance_summary[version] = {
                'final_accuracy': final_accuracy,
                'max_accuracy': max_accuracy,
                'total_time': total_time,
                'rounds': len(df)
            }
            
            print(f"📊 {version.upper()} 版本:")
            print(f"   最终准确率: {final_accuracy:.4f}")
            print(f"   最高准确率: {max_accuracy:.4f}")
            print(f"   总训练时间: {total_time:.1f}秒")
            print(f"   训练轮数: {len(df)}")
            
            # 显示新增统计信息（如果有）
            if 'avg_staleness' in df.columns:
                avg_staleness = df['avg_staleness'].mean()
                print(f"   平均陈旧度: {avg_staleness:.2f}")
            
            if 'network_success_rate' in df.columns:
                avg_success_rate = df['network_success_rate'].mean()
                print(f"   网络成功率: {avg_success_rate:.2%}")
            
            print()
    
    return performance_summary

def generate_comparison_plots(results):
    """生成对比图表"""
    print(f"\n📊 生成对比图表")
    print("-" * 50)
    
    if len(results) < 2:
        print("❌ 需要至少两个版本的结果才能生成对比图")
        return
    
    plt.figure(figsize=(15, 10))
    
    # 1. 准确率对比
    plt.subplot(2, 3, 1)
    for version, df in results.items():
        if 'accuracy' in df.columns:
            plt.plot(df['round'], df['accuracy'], label=f'{version.title()} FedBuff', marker='o')
    plt.xlabel('Round')
    plt.ylabel('Accuracy')
    plt.title('Accuracy Comparison')
    plt.legend()
    plt.grid(True)
    
    # 2. 训练时间对比
    plt.subplot(2, 3, 2)
    for version, df in results.items():
        if 'elapsed_time' in df.columns:
            plt.plot(df['round'], df['elapsed_time'], label=f'{version.title()} FedBuff', marker='s')
    plt.xlabel('Round')
    plt.ylabel('Elapsed Time (s)')
    plt.title('Training Time Comparison')
    plt.legend()
    plt.grid(True)
    
    # 3. 陈旧度对比（如果有）
    plt.subplot(2, 3, 3)
    staleness_versions = []
    for version, df in results.items():
        if 'avg_staleness' in df.columns:
            plt.plot(df['round'], df['avg_staleness'], label=f'{version.title()} Staleness', marker='^')
            staleness_versions.append(version)
    if staleness_versions:
        plt.xlabel('Round')
        plt.ylabel('Average Staleness')
        plt.title('Staleness Comparison')
        plt.legend()
        plt.grid(True)
    else:
        plt.text(0.5, 0.5, 'No Staleness Data', ha='center', va='center', transform=plt.gca().transAxes)
        plt.title('Staleness Comparison (No Data)')
    
    # 4. 网络成功率对比（如果有）
    plt.subplot(2, 3, 4)
    network_versions = []
    for version, df in results.items():
        if 'network_success_rate' in df.columns:
            plt.plot(df['round'], df['network_success_rate'], label=f'{version.title()} Success Rate', marker='d')
            network_versions.append(version)
    if network_versions:
        plt.xlabel('Round')
        plt.ylabel('Network Success Rate')
        plt.title('Network Success Rate Comparison')
        plt.legend()
        plt.grid(True)
    else:
        plt.text(0.5, 0.5, 'No Network Data', ha='center', va='center', transform=plt.gca().transAxes)
        plt.title('Network Success Rate (No Data)')
    
    # 5. 全局准确率对比
    plt.subplot(2, 3, 5)
    for version, df in results.items():
        if 'global_accuracy' in df.columns:
            plt.plot(df['round'], df['global_accuracy'], label=f'{version.title()} Global Acc', marker='x')
    plt.xlabel('Round')
    plt.ylabel('Global Accuracy')
    plt.title('Global Accuracy Comparison')
    plt.legend()
    plt.grid(True)
    
    # 6. 通信时间对比（如果有）
    plt.subplot(2, 3, 6)
    comm_versions = []
    for version, df in results.items():
        if 'avg_communication_time' in df.columns:
            plt.plot(df['round'], df['avg_communication_time'], label=f'{version.title()} Comm Time', marker='*')
            comm_versions.append(version)
    if comm_versions:
        plt.xlabel('Round')
        plt.ylabel('Avg Communication Time (s)')
        plt.title('Communication Time Comparison')
        plt.legend()
        plt.grid(True)
    else:
        plt.text(0.5, 0.5, 'No Communication Data', ha='center', va='center', transform=plt.gca().transAxes)
        plt.title('Communication Time (No Data)')
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    plot_filename = f"fedbuff_network_enhancement_comparison_{timestamp}.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"📊 对比图表已保存: {plot_filename}")
    
    plt.show()

def main():
    """主对比实验函数"""
    print("🚀 FedBuff网络增强功能对比实验")
    print("=" * 60)
    
    print("本实验将对比以下版本:")
    print("1. 原始FedBuff (无网络模拟)")
    print("2. 标准增强FedBuff (有陈旧度统计)")
    print("3. 网络测试FedBuff (有网络模拟)")
    print()
    
    # 实验配置
    experiments = [
        ("fedbuff_original.py", "fedbuff_MNIST_original.yml", "原始FedBuff"),
        ("fedbuff.py", "fedbuff_MNIST_standard.yml", "标准增强FedBuff"),
        ("fedbuff.py", "fedbuff_MNIST_network_test.yml", "网络测试FedBuff")
    ]
    
    # 运行实验
    experiment_results = {}
    for script, config, name in experiments:
        success, duration, output = run_experiment(script, config, name)
        experiment_results[name] = {
            'success': success,
            'duration': duration,
            'output': output
        }
    
    # 等待一下确保文件写入完成
    print("\n⏳ 等待结果文件写入完成...")
    time.sleep(5)
    
    # 查找结果文件
    result_files = find_result_files()
    print(f"\n📁 找到结果文件:")
    for version, file_path in result_files.items():
        print(f"   {version}: {file_path}")
    
    if not result_files:
        print("❌ 未找到任何结果文件，请检查实验是否成功运行")
        return
    
    # 分析结果
    results = analyze_results(result_files)
    
    if results:
        compare_features(results)
        performance_summary = compare_performance(results)
        generate_comparison_plots(results)
        
        # 总结
        print(f"\n🎯 实验总结")
        print("=" * 60)
        print("✅ 网络增强功能成功添加到FedBuff")
        print("✅ 新增功能包括:")
        print("   • 陈旧度统计 (avg_staleness, max_staleness, min_staleness)")
        print("   • 网络模拟 (network_success_rate, avg_communication_time)")
        print("   • 自定义文件命名")
        print("   • 详细的网络监控日志")
        
        print(f"\n📊 性能影响分析:")
        if len(performance_summary) >= 2:
            versions = list(performance_summary.keys())
            for i in range(len(versions)-1):
                v1, v2 = versions[i], versions[i+1]
                acc_diff = performance_summary[v2]['final_accuracy'] - performance_summary[v1]['final_accuracy']
                time_diff = performance_summary[v2]['total_time'] - performance_summary[v1]['total_time']
                print(f"   {v1} -> {v2}:")
                print(f"     准确率变化: {acc_diff:+.4f}")
                print(f"     时间变化: {time_diff:+.1f}秒")
    
    else:
        print("❌ 无法分析结果，请检查实验输出")

if __name__ == "__main__":
    main()
