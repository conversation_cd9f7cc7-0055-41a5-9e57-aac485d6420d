#!/usr/bin/env python3
"""
验证所有EMNIST配置文件是否包含统一的结果记录指标
"""

import os
import yaml
from pathlib import Path

def check_config_metrics():
    """检查所有配置文件的结果记录指标"""
    
    # 要求的统一指标 (包含网络波动指标)
    required_metrics = [
        'round', 'elapsed_time', 'accuracy',
        'global_accuracy', 'global_accuracy_std',
        'avg_staleness', 'max_staleness', 'min_staleness',
        'network_latency', 'network_bandwidth', 'network_reliability', 'network_success_rate'
    ]
    
    # 算法配置文件映射
    algorithms = {
        'RefedSCAFL': 'refedscafl/refedscafl_EMNIST_lenet5_optimized.yml',
        'SCAFL': 'SC_AFL/sc_afl_emnist_lenet5_with_network.yml',
        'FADAS': 'fadas/fadas_EMNIST_lenet5_alpha0.1.yml',
        'FedAC': 'fedac/fedac_EMNIST_lenet5_alpha0.1.yml',
        'FedBuff': 'fedbuff/fedbuff_EMNIST_lenet5_alpha0.1.yml',
        'FedAsync': 'fedasync/fedasync_EMNIST_lenet5_alpha0.1.yml'
    }
    
    print("🔍 验证所有EMNIST配置文件的结果记录指标")
    print("=" * 60)
    print(f"要求的统一指标: {', '.join(required_metrics)}")
    print("=" * 60)
    
    all_valid = True
    
    for alg_name, config_path in algorithms.items():
        full_path = Path(__file__).parent / config_path
        
        print(f"\n📋 检查 {alg_name}:")
        print(f"   配置文件: {config_path}")
        
        if not full_path.exists():
            print(f"   ❌ 配置文件不存在: {full_path}")
            all_valid = False
            continue
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查results.types字段
            if 'results' not in config:
                print(f"   ❌ 缺少 'results' 配置段")
                all_valid = False
                continue
            
            if 'types' not in config['results']:
                print(f"   ❌ 缺少 'results.types' 字段")
                all_valid = False
                continue
            
            # 解析types字段
            types_str = config['results']['types']
            if isinstance(types_str, str):
                # 处理字符串格式的types
                actual_metrics = [metric.strip() for metric in types_str.split(',')]
            elif isinstance(types_str, list):
                # 处理列表格式的types
                actual_metrics = types_str
            else:
                print(f"   ❌ 'results.types' 格式不正确: {type(types_str)}")
                all_valid = False
                continue
            
            print(f"   📊 实际指标: {', '.join(actual_metrics)}")
            
            # 检查是否包含所有必需指标
            missing_metrics = []
            for metric in required_metrics:
                if metric not in actual_metrics:
                    missing_metrics.append(metric)
            
            if missing_metrics:
                print(f"   ❌ 缺少指标: {', '.join(missing_metrics)}")
                all_valid = False
            else:
                print(f"   ✅ 包含所有必需指标")
                
                # 显示额外指标
                extra_metrics = [m for m in actual_metrics if m not in required_metrics]
                if extra_metrics:
                    print(f"   ➕ 额外指标: {', '.join(extra_metrics)}")
        
        except Exception as e:
            print(f"   ❌ 读取配置文件失败: {e}")
            all_valid = False
    
    print("\n" + "=" * 60)
    if all_valid:
        print("🎉 所有配置文件都包含统一的结果记录指标！")
        print("✅ 可以进行公平的算法对比实验")
    else:
        print("❌ 发现配置文件问题，请修复后再进行实验")
    
    return all_valid

def show_metrics_summary():
    """显示指标说明"""
    print("\n📖 指标说明:")
    print("-" * 40)
    
    metrics_description = {
        'round': '训练轮次',
        'elapsed_time': '累计运行时间',
        'accuracy': '本地测试准确率',
        'global_accuracy': '全局模型准确率',
        'global_accuracy_std': '全局准确率标准差',
        'avg_staleness': '平均陈旧度',
        'max_staleness': '最大陈旧度',
        'min_staleness': '最小陈旧度',
        'network_latency': '网络延迟 (ms)',
        'network_bandwidth': '网络带宽 (MB/s)',
        'network_reliability': '网络可靠性',
        'network_success_rate': '网络成功率'
    }
    
    for metric, description in metrics_description.items():
        print(f"  {metric:20} - {description}")

if __name__ == "__main__":
    print("🔧 EMNIST配置文件指标验证工具\n")
    
    # 验证配置文件
    is_valid = check_config_metrics()
    
    # 显示指标说明
    show_metrics_summary()
    
    if is_valid:
        print(f"\n💡 现在可以运行对比实验:")
        print(f"   python quick_test_emnist.py      # 快速测试")
        print(f"   python run_emnist_comparison.py  # 完整实验")
    else:
        print(f"\n⚠️  请先修复配置文件问题")
        exit(1)
