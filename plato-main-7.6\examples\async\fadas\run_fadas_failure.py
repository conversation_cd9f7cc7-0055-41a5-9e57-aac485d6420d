#!/usr/bin/env python3
"""
FedAS + 通信失败模拟 运行程序

这是一个简化的运行程序，集成了通信失败模拟功能。
使用方法：python run_fadas_failure.py

功能：
1. 模拟客户端上传失败
2. 记录失败统计信息
3. 生成包含失败率的结果文件
"""

import os
import sys
import logging
import time
import random
import numpy as np

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入失败模拟器
from communication_failure_simulator import CommunicationFailureSimulator


class FedASWithFailureSimulation:
    """集成通信失败模拟的FedAS训练器"""
    
    def __init__(self, config=None):
        """初始化"""
        # 默认配置
        default_config = {
            'total_clients': 100,
            'clients_per_round': 20,
            'rounds': 500,
            'local_epochs': 3,
            'learning_rate': 0.01,
            'batch_size': 32
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 初始化失败模拟器
        failure_config = {
            'base_communication_time': 0.5,
            'communication_noise_std': 0.5,
            'initial_threshold': 1.0,
            'enable_dynamic_threshold': True,
            'enable_logging': True
        }
        
        self.failure_simulator = CommunicationFailureSimulator(failure_config)
        
        # 训练状态
        self.current_round = 0
        self.global_accuracy = 0.0
        self.training_results = []
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("FedAS + 通信失败模拟 初始化完成")
    
    def simulate_client_training(self, client_id, round_num):
        """模拟客户端训练过程"""
        # 模拟真实的本地训练时间（8-15秒）
        base_training_time = 8.0 + random.uniform(0.0, 7.0)  # 8-15秒
        training_time = base_training_time

        # 模拟实际训练延迟（真实等待时间）
        self.logger.info(f"客户端{client_id}开始训练，预计耗时{training_time:.1f}秒...")
        time.sleep(training_time)  # 真实等待训练时间

        # 模拟训练结果
        base_accuracy = 0.1 + (round_num * 0.001)  # 更慢的收敛
        noise = random.uniform(-0.02, 0.02)
        local_accuracy = min(0.95, max(0.05, base_accuracy + noise))

        # 模拟更大的权重更新（更真实的模型大小）
        weights = {
            'conv1': np.random.randn(32, 1, 5, 5).tolist(),
            'conv2': np.random.randn(64, 32, 5, 5).tolist(),
            'fc1': np.random.randn(1024, 512).tolist(),
            'fc2': np.random.randn(512, 10).tolist()
        }

        # 计算陈旧度（客户端可能基于旧的全局模型）
        staleness = max(0, round_num - random.randint(1, 3))

        return {
            'client_id': client_id,
            'accuracy': local_accuracy,
            'training_time': training_time,
            'weights': weights,
            'num_samples': random.randint(100, 500),
            'staleness': staleness,
            'round_completed': round_num
        }
    
    def aggregate_weights(self, successful_updates):
        """聚合成功的权重更新（包含陈旧度处理）"""
        if not successful_updates:
            return None, 0.0

        # 模拟真实的聚合时间
        aggregation_start = time.time()

        # 计算陈旧度统计
        staleness_values = [update['staleness'] for update in successful_updates]
        avg_staleness = sum(staleness_values) / len(staleness_values)

        # 简化的权重聚合（实际FedAS会更复杂）
        total_samples = sum(update['num_samples'] for update in successful_updates)

        # 计算加权平均准确率
        weighted_accuracy = sum(
            update['accuracy'] * update['num_samples']
            for update in successful_updates
        ) / total_samples

        # 模拟聚合计算时间（基于客户端数量和模型大小）
        base_aggregation_time = 0.5 + (len(successful_updates) * 0.2)
        time.sleep(min(1.0, base_aggregation_time))  # 限制最大延迟

        aggregation_time = time.time() - aggregation_start

        self.logger.info(f"聚合了{len(successful_updates)}个客户端更新")
        self.logger.info(f"平均陈旧度: {avg_staleness:.2f}")
        self.logger.info(f"加权平均准确率: {weighted_accuracy:.4f}")
        self.logger.info(f"聚合时间: {aggregation_time:.2f}秒")

        return weighted_accuracy, avg_staleness
    
    def run_training(self):
        """运行FedAS训练"""
        self.logger.info("🚀 开始FedAS训练（含通信失败模拟）")
        self.logger.info(f"配置: {self.config['total_clients']}个客户端, "
                        f"每轮{self.config['clients_per_round']}个, "
                        f"共{self.config['rounds']}轮")
        
        start_time = time.time()
        
        for round_num in range(1, self.config['rounds'] + 1):
            self.current_round = round_num
            self.logger.info(f"\n{'='*50}")
            self.logger.info(f"🔄 第{round_num}轮训练")
            self.logger.info(f"{'='*50}")
            
            # 选择客户端
            selected_clients = self.select_clients(round_num)
            self.logger.info(f"选择客户端: {selected_clients}")
            
            # 客户端训练
            successful_updates = []
            failed_updates = []
            
            for client_id in selected_clients:
                # 模拟客户端训练
                update = self.simulate_client_training(client_id, round_num)
                
                # 判断上传是否成功
                success, comm_time, info = self.failure_simulator.should_upload_succeed(client_id)
                
                if success:
                    successful_updates.append(update)
                    self.logger.info(f"✅ 客户端{client_id}: 上传成功 ({comm_time:.3f}s)")
                else:
                    failed_updates.append({
                        'client_id': client_id,
                        'comm_time': comm_time,
                        'update': update
                    })
                    self.logger.warning(f"❌ 客户端{client_id}: 上传失败 ({comm_time:.3f}s)")
            
            # 聚合权重
            if successful_updates:
                self.global_accuracy, avg_staleness = self.aggregate_weights(successful_updates)
                self.current_avg_staleness = avg_staleness
            else:
                self.logger.warning("⚠️ 本轮没有成功更新，跳过聚合")
                self.global_accuracy = self.global_accuracy  # 保持上一轮的准确率
                self.current_avg_staleness = getattr(self, 'current_avg_staleness', 0.0)
            
            # 记录本轮结果
            round_result = self.record_round_result(
                round_num, successful_updates, failed_updates, time.time() - start_time
            )
            self.training_results.append(round_result)
            
            # 打印本轮统计
            self.print_round_summary(round_result)
        
        # 训练完成
        self.training_completed(time.time() - start_time)
    
    def select_clients(self, round_num):
        """选择客户端（简单轮询）"""
        start_idx = ((round_num - 1) * self.config['clients_per_round']) % self.config['total_clients']
        selected = []
        for i in range(self.config['clients_per_round']):
            client_id = (start_idx + i) % self.config['total_clients'] + 1
            selected.append(client_id)
        return selected
    
    def record_round_result(self, round_num, successful_updates, failed_updates, elapsed_time):
        """记录轮次结果"""
        # 获取失败统计
        failure_stats = self.failure_simulator.get_statistics()

        result = {
            'round': round_num,
            'elapsed_time': elapsed_time,
            'global_accuracy': self.global_accuracy,
            'avg_staleness': getattr(self, 'current_avg_staleness', 0.0),
            'selected_clients': self.config['clients_per_round'],
            'successful_updates': len(successful_updates),
            'failed_updates': len(failed_updates),
            'success_rate': len(successful_updates) / self.config['clients_per_round'],
            'failure_rate': failure_stats['overall']['failure_rate'],
            'comm_threshold': failure_stats['overall']['current_threshold'],
            'avg_comm_time': failure_stats['overall']['avg_comm_time']
        }

        return result
    
    def print_round_summary(self, result):
        """打印轮次摘要"""
        self.logger.info(f"📊 第{result['round']}轮结果:")
        self.logger.info(f"   全局准确率: {result['global_accuracy']:.4f}")
        self.logger.info(f"   平均陈旧度: {result['avg_staleness']:.2f}")
        self.logger.info(f"   成功更新: {result['successful_updates']}/{result['selected_clients']}")
        self.logger.info(f"   本轮成功率: {result['success_rate']:.2%}")
        self.logger.info(f"   累计失败率: {result['failure_rate']:.2%}")
        self.logger.info(f"   通信阈值: {result['comm_threshold']:.3f}s")
    
    def training_completed(self, total_time):
        """训练完成处理"""
        self.logger.info(f"\n{'='*60}")
        self.logger.info(f"🎉 FedAS训练完成！")
        self.logger.info(f"{'='*60}")
        
        # 最终统计
        final_stats = self.failure_simulator.get_statistics()
        overall_stats = final_stats['overall']
        
        self.logger.info(f"总训练时间: {total_time:.2f}秒")
        self.logger.info(f"总训练轮数: {self.current_round}")
        self.logger.info(f"最终准确率: {self.global_accuracy:.4f}")
        self.logger.info(f"总上传尝试: {overall_stats['total_attempts']}")
        self.logger.info(f"总上传失败: {overall_stats['total_failures']}")
        self.logger.info(f"总体失败率: {overall_stats['failure_rate']:.2%}")
        self.logger.info(f"最终通信阈值: {overall_stats['current_threshold']:.3f}s")
        
        # 保存结果
        self.save_results()
        
        # 效果分析
        self.analyze_results()
    
    def save_results(self):
        """保存训练结果到CSV文件"""
        import csv
        
        # 创建results目录
        results_dir = os.path.join(current_dir, 'results')
        os.makedirs(results_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = int(time.time())
        csv_file = os.path.join(results_dir, f'fadas_failure_{timestamp}.csv')
        
        # 写入CSV
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'round', 'elapsed_time', 'global_accuracy', 'avg_staleness',
                'selected_clients', 'successful_updates', 'failed_updates',
                'success_rate', 'failure_rate', 'comm_threshold', 'avg_comm_time'
            ])
            writer.writeheader()
            writer.writerows(self.training_results)
        
        self.logger.info(f"📁 结果已保存到: {csv_file}")
        
        # 保存失败日志
        failure_log = os.path.join(results_dir, f'failure_log_{timestamp}.json')
        import json
        with open(failure_log, 'w', encoding='utf-8') as f:
            json.dump(self.failure_simulator.get_statistics(), f, indent=2, default=str)
        
        self.logger.info(f"📁 失败日志已保存到: {failure_log}")
    
    def analyze_results(self):
        """分析训练结果"""
        self.logger.info(f"\n📈 结果分析:")
        self.logger.info(f"{'='*40}")
        
        if self.training_results:
            # 计算平均成功率
            avg_success_rate = sum(r['success_rate'] for r in self.training_results) / len(self.training_results)
            
            # 计算准确率提升
            initial_accuracy = self.training_results[0]['global_accuracy']
            final_accuracy = self.training_results[-1]['global_accuracy']
            accuracy_improvement = final_accuracy - initial_accuracy
            
            self.logger.info(f"平均成功率: {avg_success_rate:.2%}")
            self.logger.info(f"准确率提升: {accuracy_improvement:.4f}")
            
            if avg_success_rate < 0.8:
                self.logger.info("⚠️ 成功率较低，网络环境不稳定")
            elif avg_success_rate < 0.9:
                self.logger.info("ℹ️ 成功率适中，模拟真实网络环境")
            else:
                self.logger.info("✅ 成功率较高，网络环境相对稳定")
        
        self.logger.info(f"\n💡 使用建议:")
        self.logger.info(f"1. 将相同配置应用到其他算法进行对比")
        self.logger.info(f"2. 调整失败率参数测试不同网络环境")
        self.logger.info(f"3. 分析ReFedScaFL在相同条件下的表现")


def main():
    """主函数"""
    print("🚀 FedAS + 通信失败模拟")
    print("=" * 50)
    
    # 配置参数
    config = {
        'total_clients': 6,
        'clients_per_round': 3,
        'rounds': 8,  # 减少轮数以便快速测试
        'local_epochs': 3,
        'learning_rate': 0.01,
        'batch_size': 32
    }
    
    try:
        # 创建训练器
        trainer = FedASWithFailureSimulation(config)
        
        # 运行训练
        trainer.run_training()
        
    except KeyboardInterrupt:
        print("\n⏹️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
