"""
The federated averaging algorithm for PyTorch.
"""

from collections import OrderedDict

from plato.algorithms import base


class Algorithm(base.Algorithm):
    """PyTorch-based federated averaging algorithm, used by both the client and the server."""

    def compute_weight_deltas(self, baseline_weights, weights_received):
        """Compute the deltas between baseline weights and weights received."""
        # Calculate updates from the received weights #计算更新
        deltas = []
        for weight in weights_received:
            # 添加类型检查，确保weight是字典类型
            if not isinstance(weight, dict):
                import logging
                logging.error(f"接收到的权重类型错误: {type(weight)}，期望dict类型")
                logging.error(f"权重内容: {weight}")
                continue  # 跳过这个错误的权重

            delta = OrderedDict()
            for name, current_weight in weight.items():
                baseline = baseline_weights[name]

                # Calculate update
                _delta = current_weight - baseline
                delta[name] = _delta
            deltas.append(delta)

        return deltas

    def update_weights(self, deltas):
        """Updates the existing model weights from the provided deltas."""
        baseline_weights = self.extract_weights()

        updated_weights = OrderedDict()
        for name, weight in baseline_weights.items():
            updated_weights[name] = weight + deltas[name]

        return updated_weights

    def extract_weights(self, model=None):
        """Extracts weights from the model.""" #提取权重
        import torch
        if model is None:
            weights = self.model.cpu().state_dict()
        else:
            weights = model.cpu().state_dict()
            
        # 确保所有张量都在CPU上
        for key, value in weights.items():
            if isinstance(value, torch.Tensor):
                weights[key] = value.cpu()
                
        return weights

    def load_weights(self, weights):
        """Loads the model weights passed in as a parameter.""" #加载权重
        self.model.load_state_dict(weights, strict=True)
