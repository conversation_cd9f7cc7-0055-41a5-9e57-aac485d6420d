clients:
    # Type
    type: simple

    # 减少初始客户端数量以加快第一轮
    total_clients: 5
    per_round: 5

    # 客户端配置
    do_test: true
    do_global_test: true
    speed_simulation: true
    simulation_distribution:
        distribution: pareto
        alpha: 1
    max_sleep_time: 5
    sleep_simulation: false
    avg_training_time: 8

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8095
    ping_timeout: 36000
    ping_interval: 36000
    synchronous: false
    simulate_wall_time: true
    minimum_clients_aggregated: 3
    staleness_bound: 3
    request_update: true
    checkpoint_path: models/refedscafl/cifar10_resnet9
    model_path: models/refedscafl/cifar10_resnet9
    random_seed: 1

data:
    # 数据集配置
    datasource: CIFAR10
    partition_size: 500
    sampler: noniid
    concentration: 0.1
    testset_size: 1000
    random_seed: 1
    testset_sampler: noniid

    # 优化：启用数据预加载和缓存
    enable_data_caching: true
    cache_size_limit_mb: 1024
    prefetch_factor: 2
    num_workers: 4

trainer:
    type: basic
    rounds: 500
    max_concurrency: 5  # 减少并发数
    target_accuracy: 0.80
    epochs: 1  # 第一轮只跑1个epoch
    batch_size: 64  # 增大批量大小
    optimizer: SGD
    lr_scheduler: StepLR
    model_name: resnet_9

algorithm:
    type: refedscafl  # 修正为refedscafl
    lamda: 1.0

    # 第一轮简化配置
    buffer_pool_size: 5
    greedy_selection_size: 3
    tau_max: 5
    V: 1.0  # 延迟权重参数

    # 第一轮关闭复杂功能
    success_weight: 0.8
    distill_weight: 0.2
    rho: 0.9
    communication_threshold: 2.0
    enable_adaptive_weights: false
    enable_consistency_factor: false

    # 聚合算法选择（修正的核心配置）
    use_simple_average_aggregation: false
    use_sc_afl_style_aggregation: true  # 启用SC_AFL风格聚合
    enable_knowledge_distillation: false
    use_simple_average_aggregation: true

    # 优化：启用模型缓存
    enable_model_caching: true
    model_cache_size_mb: 512
    use_shared_memory: true

parameters:
    model:
        num_classes: 10
        in_channels: 3

    optimizer:
        lr: 0.1
        momentum: 0.9
        weight_decay: 0.0005

    learning_rate:
        step_size: 40
        gamma: 0.1

results:
    result_path: ./results/refedscafl_cifar10_resnet9_optimized

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness
