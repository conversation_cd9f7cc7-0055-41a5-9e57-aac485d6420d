clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 5

    # Should the clients compute test accuracy locally?
    do_test: true

    random_seed: 1

    comm_simulation: true
    compute_comm_time: true
    
server:
    address: 127.0.0.1
    port: 8011
    do_test: false
    random_seed: 1
    
    simulate_wall_time: true

data:
    # The training and testing dataset
    datasource:  CIFAR10
    # Number of samples in each partition
    partition_size: 600
    test_partition_size: 600

    sampler: noniid
##    # IID or non-IID?
##
##    # The concentration parameter for the Dirichlet distribution
    concentration: 0.3
##
    testset_sampler: noniid
##
##    # The random seed for sampling data
    random_seed: 1
    
trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 1500

    # The maximum number of clients running concurrently
    max_concurrency: 1

    # The target accuracy
    target_accuracy: 0.98

    # Number of epochs for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD
    loss_criterion: CrossEntropyLoss
    lr_scheduler: CosineAnnealingLR
    global_lr_scheduler: true

    model_name: darts

algorithm:
    # Aggregation algorithm
    type: fedavg
    
results:
    types: round, accuracy, elapsed_time, comm_time, round_time, comm_overhead
    
parameters:
    learning_rate:
        base_lr: 0.025
    lr_scheduler:
        eta_min: 0.001
        T_max: 7500
    optimizer:
        lr: 0.025
        momentum: 0.9 # learning rate is fixed as in Appendix C.2
        weight_decay: 0.0003
    model:
        C: 3
        num_classes: 10
        layers: 8
    architect:
        learning_rate: 0.5
        weight_decay: 0
        lambda_time: 0.001
        lambda_neg: 0.1
