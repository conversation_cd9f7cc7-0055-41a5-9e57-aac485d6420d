clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 3

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8000

data:
    # The training and testing dataset
    datasource: CelebA

    # Only add face identity as labels for training
    celeba_targets:
        # For ResNet, do not set <attr> to True since it does not match the expected output of ResNet
        attr: false
        identity: true
    
    # Resize all images to 32x32; default is 64x64
    celeba_img_size: 32

    # Number of identity in CelebA
    num_classes: 10178

    # Number of samples in each partition
    partition_size: 20000

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution
    concentration: 0.5

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 5

    # The maximum number of clients running concurrently
    max_concurrency: 3

    # The target accuracy
    target_accuracy: 0.94

    # The machine learning model
    model_name: resnet_18

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0
