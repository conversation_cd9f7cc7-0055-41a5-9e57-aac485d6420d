[INFO][23:05:12]: 日志系统已初始化
[INFO][23:05:12]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250728_230512.log
[INFO][23:05:12]: 日志级别: INFO
[WARNING][23:05:12]: 无法获取系统信息: No module named 'psutil'
[INFO][23:05:12]: 🚀 ReFedScaFL 训练开始
[INFO][23:05:12]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][23:05:12]: 开始时间: 2025-07-28 23:05:12
[INFO][23:05:12]: [Client None] 基础初始化完成
[INFO][23:05:12]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][23:05:12]: 创建并缓存共享模型
[INFO][23:05:12]: [93m[1m[30444] Logging runtime results to: ././results/refedscafl_cifar10_resnet9/30444.csv.[0m
[INFO][23:05:12]: [Server #30444] Started training on 10 clients with 5 per round.
[INFO][23:05:12]: 服务器参数配置完成：
[INFO][23:05:12]: - 客户端数量: total=10, per_round=5
[INFO][23:05:12]: - 权重参数: success=0.8, distill=0.2
[INFO][23:05:12]: - SCAFL参数: V=1.0, tau_max=5
[INFO][23:05:12]: 从共享资源模型提取并缓存全局权重
[INFO][23:05:12]: [Server #30444] Configuring the server...
[INFO][23:05:12]: Training: 500 rounds or accuracy above 80.0%

[INFO][23:05:12]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][23:05:12]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][23:05:12]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][23:05:12]: [Trainer Init] 训练器初始化完成，参数：batch_size=64, learning_rate=0.1, epochs=3
[INFO][23:05:12]: Algorithm: fedavg
[INFO][23:05:12]: Data source: CIFAR10
[INFO][23:05:13]: Starting client #1's process.
[INFO][23:05:13]: Starting client #2's process.
[INFO][23:05:13]: Starting client #3's process.
[INFO][23:05:13]: Starting client #4's process.
[INFO][23:05:13]: Starting client #5's process.
[INFO][23:05:13]: Setting the random seed for selecting clients: 1
[INFO][23:05:13]: Starting a server at address 127.0.0.1 and port 8090.
[ERROR][23:05:13]: 训练过程中发生错误: [Errno 10048] error while attempting to bind on address ('127.0.0.1', 8090): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
[ERROR][23:05:13]: 错误堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\refedscafl.py", line 45, in main
    server.run(client)
    ~~~~~~~~~~^^^^^^^^
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\servers\base.py", line 410, in run
    self.start()
    ~~~~~~~~~~^^
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\servers\base.py", line 432, in start
    web.run_app(
    ~~~~~~~~~~~^
        app, host=Config().server.address, port=port, loop=asyncio.get_event_loop()
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Develop\Miniconda\Lib\site-packages\aiohttp\web.py", line 530, in run_app
    loop.run_until_complete(main_task)
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "D:\Develop\Miniconda\Lib\asyncio\base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
OSError: [Errno 10048] error while attempting to bind on address ('127.0.0.1', 8090): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。

[INFO][23:05:13]: ✅ ReFedScaFL 训练结束
[INFO][23:05:13]: 结束时间: 2025-07-28 23:05:13
