"""
A federated learning training session using ReFedScaFL.

ReFedScaFL: Reputation-based Federated Scaffold Learning with Knowledge Distillation Compensation
"""

import os
import sys
import logging

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../../"))
sys.path.insert(0, project_root)

# 导入日志配置
from logging_config import setup_logging, log_system_info, log_training_start, log_training_end

from refedscafl_server import Server
from refedscafl_client import Client
from refedscafl_trainer import Trainer


def main():
    """A Plato federated learning training session using ReFedScaFL."""

    # 设置日志系统
    log_dir = os.path.join(current_dir, "logs")
    log_filepath = setup_logging(log_dir=log_dir, log_level=logging.INFO)

    # 记录系统信息
    log_system_info()

    # 记录训练开始
    config_file = "refedscafl_cifar10_resnet9.yml"
    log_training_start(config_file)

    try:
        # 采用类似FedAC的架构，创建客户端和服务器实例
        trainer = Trainer
        client = Client(trainer=trainer)
        server = Server(trainer=trainer)

        # 使用标准Plato运行方式
        server.run(client)

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"训练过程中发生错误: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise
    finally:
        # 记录训练结束
        log_training_end()
        print(f"\n📁 日志文件已保存到: {log_filepath}")


if __name__ == "__main__":
    main()