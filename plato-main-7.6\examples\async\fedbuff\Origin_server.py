"""
Origin server implementation for FedBuff.
"""

import logging
import os
import time
import asyncio
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../../"))
sys.path.insert(0, project_root)

from plato.config import Config
from plato.servers import fedavg

class Origin_server(fedavg.Server):
    """Origin server implementation for FedBuff."""

    def __init__(self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )
        self.current_round_losses = []
        self.avg_loss = 0.0

    async def process_reports(self):
        """Process the client reports by aggregating their weights."""
        await super().process_reports()
        
        # 记录处理报告的时间
        self.report_time = time.time()

    async def aggregate_weights(self, updates, baseline_weights, weights_received):
        """Aggregate the reported weight updates from the selected clients."""
        return await super().aggregate_weights(updates, baseline_weights, weights_received)

    def get_logged_items(self) -> dict:
        """Get items to be logged by the LogProgressCallback class in a .csv file."""
        logged_items = super().get_logged_items()
        
        # 添加额外的日志项
        if hasattr(self, 'avg_loss'):
            logged_items["loss"] = self.avg_loss
            
        return logged_items 