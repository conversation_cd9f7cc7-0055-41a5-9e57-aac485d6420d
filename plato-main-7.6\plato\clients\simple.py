"""
A basic federated learning client who sends weight updates to the server.
"""

import logging
import time
from types import SimpleNamespace

from plato.algorithms import registry as algorithms_registry
from plato.clients import base
from plato.config import Config
from plato.datasources import registry as datasources_registry
from plato.processors import registry as processor_registry
from plato.samplers import registry as samplers_registry
from plato.trainers import registry as trainers_registry
from plato.utils import fonts

# 没有客户端模型上传的逻辑：只有客户端配置，数据准备，训练，模型权重以及更新打包
class Client(base.Client):
    """A basic federated learning client who sends simple weight updates."""
    #  中文注释：
    #  这是一个基本的联邦学习客户端，它发送简单的权重更新。
    #  这个类继承自 base.Client，它是 Plato 框架中所有客户端的基类。
    #  这个类的主要功能是：
    #  1. 初始化客户端的各种组件，如模型、数据源、算法、训练器、回调函数等。
    #  2. 配置客户端，包括设置客户端的 ID、模型、数据源、算法、训练器等。
    #  3. 加载数据，包括生成数据和加载数据到客户端。
    #  4. 训练模型，包括加载模型、训练模型、测试模型等。
    #  5. 上传模型，包括上传模型的权重、配置、性能等。
    #  6. 处理服务器响应，包括处理服务器的响应，如处理服务器的响应、处理服务器的响应等。
    
    def __init__(
        self,
        model=None,
        datasource=None,
        algorithm=None,
        trainer=None,
        callbacks=None,
        trainer_callbacks=None,
    ):
        super().__init__(callbacks=callbacks)
        # Save the callbacks that will be passed to trainer later
        self.trainer_callbacks = trainer_callbacks

        self.custom_model = model
        self.model = None

        self.custom_datasource = datasource
        self.datasource = None

        self.custom_algorithm = algorithm
        self.algorithm = None

        self.custom_trainer = trainer
        self.trainer = None

        self.trainset = None  # Training dataset
        self.testset = None  # Testing dataset
        self.sampler = None
        self.testset_sampler = None  # Sampler for the test set

        self._report = None

    def configure(self) -> None:
        """Prepares this client for training."""#  中文注释：准备客户端进行训练
        super().configure()

        if self.model is None and self.custom_model is not None:
            self.model = self.custom_model

        # 导入basic训练器
        from plato.trainers import basic

        # 初始化训练器，若未配置则使用basic.Trainer
        if self.trainer is None:
            if self.custom_trainer is not None:
                self.trainer = self.custom_trainer(
                    model=self.model, callbacks=self.trainer_callbacks
                )
            else:
                # 从注册器获取或使用默认basic训练器
                self.trainer = trainers_registry.get(
                    model=self.model, callbacks=self.trainer_callbacks
                ) or basic.Trainer(model=self.model, callbacks=self.trainer_callbacks)

        # 训练器初始化失败检查
        if self.trainer is None:
            logging.error(f"[Client #{self.client_id}] 训练器初始化失败，未找到有效训练器配置")
            raise RuntimeError("训练器初始化失败")

        self.trainer.set_client_id(self.client_id)

        if self.algorithm is None and self.custom_algorithm is None:
            self.algorithm = algorithms_registry.get(trainer=self.trainer)
        elif self.algorithm is None and self.custom_algorithm is not None:
            self.algorithm = self.custom_algorithm(trainer=self.trainer)

        self.algorithm.set_client_id(self.client_id)

        # Pass inbound and outbound data payloads through processors for
        # additional data processing@ # 中文注释：
        #  这是一个基本的联邦学习客户端，它发送简单的权重更新。
        #  这个类继承自 base.Client，它是 Plato 框架中所有客户端的基类。
        #  这个类的主要功能是： 1. 配置客户端， 2. 加载数据， 3. 训练， 4. 发送权重更新， 5. 接收权重更新。
        self.outbound_processor, self.inbound_processor = processor_registry.get(
            "Client", client_id=self.client_id, trainer=self.trainer
        )

        # Setting up the data sampler#  中文注释：设置数据采样器
        if self.datasource:
            self.sampler = samplers_registry.get(self.datasource, self.client_id)

            if (
                hasattr(Config().clients, "do_test")
                and Config().clients.do_test
                and hasattr(Config().data, "testset_sampler")
            ):
                # Set the sampler for test set
                self.testset_sampler = samplers_registry.get(
                    self.datasource, self.client_id, testing=True
                )

    def _load_data(self) -> None:
        """Generates data and loads them onto this client."""#  中文注释：加载数据到客户端
        # The only case where Config().data.reload_data is set to true is
        # when clients with different client IDs need to load from different datasets,
        # such as in the pre-partitioned Federated EMNIST dataset. We do not support
        # reloading data from a custom datasource at this time.
        # 只有当 Config().data.reload_data 为 True 时，客户端的 client_id 不同才会加载不同的数据集。
        # 例如，在预分区的 Federated EMNIST 数据集中，不同客户端的 client_id 对应不同的数据集。
        # 目前不支持从自定义数据源重新加载数据。        
        if (
            self.datasource is None
            or hasattr(Config().data, "reload_data")
            and Config().data.reload_data
        ):
            logging.info("[%s] Loading its data source...", self)

            if self.custom_datasource is None:
                self.datasource = datasources_registry.get(client_id=self.client_id)
            elif self.custom_datasource is not None:
                self.datasource = self.custom_datasource()

            logging.info(
                "[%s] Dataset size: %s", self, self.datasource.num_train_examples()
            )

    def _allocate_data(self) -> None:
        """Allocate training or testing dataset of this client."""#  中文注释：分配训练或测试数据集给客户端
        if hasattr(Config().trainer, "use_mindspore"):
            # MindSpore requires samplers to be used while constructing
            # the dataset
            #  中文注释：        MindSpore 需要在构造数据集时使用 sampler
            self.trainset = self.datasource.get_train_set(self.sampler)
        else:
            # PyTorch uses samplers when loading data with a data loader
            self.trainset = self.datasource.get_train_set()

        if hasattr(Config().clients, "do_test") and Config().clients.do_test:
            # Set the testset if local testing is needed
            self.testset = self.datasource.get_test_set()

    def _load_payload(self, server_payload) -> None:
        """Loads the server model onto this client."""# 将服务器模型加载到客户端
        self.algorithm.load_weights(server_payload)

    async def _train(self):
        # 访问trainer前进行非空检查
        if self.trainer is None:
            logging.error(f"[Client #{self.client_id}] 训练器未初始化，无法执行训练")
            return

        # 原训练逻辑
        """The machine learning training workload on a client.""" # 客户端的机器学习训练工作
        logging.info(
            fonts.colourize(
                f"[{self}] Started training in communication round #{self.current_round}."
            )
        )

        # Perform model training #训练模型
        try:
            if hasattr(self.trainer, "current_round"):
                self.trainer.current_round = self.current_round
            training_time = self.trainer.train(self.trainset, self.sampler)

        except ValueError as exc:
            logging.info(
                fonts.colourize(f"[{self}] Error occurred during training: {exc}")
            )
            await self.sio.disconnect()

        # Extract model weights and biases #提取模型权重和偏置
        weights = self.algorithm.extract_weights()

        # Generate a report for the server, performing model testing if applicable
        # 生成一个报告给服务器，进行模型测试（如果适用）
        # 测试模型的准确性
        if (hasattr(Config().clients, "do_test") and Config().clients.do_test) and (
            not hasattr(Config().clients, "test_interval")
            or self.current_round % Config().clients.test_interval == 0
        ):
            accuracy = self.trainer.test(self.testset, self.testset_sampler)

            if accuracy == -1:
                # The testing process failed, disconnect from the server
                # 测试过程失败，断开与服务器的连接
                logging.info(
                    fonts.colourize(
                        f"[{self}] Accuracy is -1 when testing. Disconnecting from the server."
                    )
                )
                await self.sio.disconnect()

            if hasattr(Config().trainer, "target_perplexity"):
                logging.info("[%s] Test perplexity: %.2f", self, accuracy)
            else:
                logging.info("[%s] Test accuracy: %.2f%%", self, 100 * accuracy)
        else:
            accuracy = 0

        comm_time = time.time()

        if (
            hasattr(Config().clients, "sleep_simulation")
            and Config().clients.sleep_simulation
        ):
            sleep_seconds = Config().client_sleep_times[self.client_id - 1]
            avg_training_time = Config().clients.avg_training_time

            training_time = (
                avg_training_time + sleep_seconds
            ) * Config().trainer.epochs

        report = SimpleNamespace(
            client_id=self.client_id,
            num_samples=self.sampler.num_samples(),
            accuracy=accuracy,
            training_time=training_time,
            comm_time=comm_time,
            update_response=False,
        )

        self._report = self.customize_report(report)

        return self._report, weights

    async def _obtain_model_update(self, client_id, requested_time):
        """Retrieves a model update corresponding to a particular wall clock time."""
        #  获取对应于特定时间的模型更新
        model = self.trainer.obtain_model_update(client_id, requested_time)
        weights = self.algorithm.extract_weights(model)
        self._report.comm_time = time.time()
        self._report.client_id = client_id
        self._report.update_response = True

        return self._report, weights

    def customize_report(self, report: SimpleNamespace) -> SimpleNamespace:
        """Customizes the report with any additional information."""#  自定义报告，添加任何额外的信息
        return report
