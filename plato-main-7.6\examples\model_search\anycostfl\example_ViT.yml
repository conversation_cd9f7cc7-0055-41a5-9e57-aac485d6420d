clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 10

    # Should the clients compute test accuracy locally?
    do_test: false

    random_seed: 1

    comm_simulation: true 
    compute_comm_time: true

server:
    address: 127.0.0.1
    port: 8015
    do_test: true
    random_seed: 1

    simulate_wall_time: true

data:
    # The training and testing dataset
    datasource:  CIFAR10
    # data_path: /data/dixi

    # Number of samples in each partition
    partition_size: 500
    test_partition_size: 10000

    sampler: iid
    testset_sampler: iid
##
##    # The random seed for sampling data
    random_seed: 1234

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 1000

    # The maximum number of clients running concurrently
    max_concurrency: 20

    # The target accuracy
    target_accuracy: 1.

    # Number of epochs for local training in each communication round
    epochs: 1
    batch_size: 64
    loss_criterion: CrossEntropyLoss
    optimizer: Adam
    lr_scheduler: CosineAnnealingLR
    global_lr_scheduler: true

    model_type: torch_hub
    model_name: vit


algorithm:
    # A aggregation algorithm
    type: fedavg
    
results:
    types: round, accuracy, elapsed_time, comm_time, round_time, comm_overhead

parameters:
    client_model:
        num_classes: 10
        channels: 3
    model:
        num_classes: 10
        channels: 3
    optimizer:
        lr: 0.0001
    learning_rate:
        T_max: 5000
    limitation:
        activate: false
        min_size: 43
        max_size: 223
        min_flops: 531
        max_flops: 3563
        epsilon: 0.8
        max_loop: 5
    distillation:
        activate: false
        iterations: 100
        optimizer:
            lr: 0.001

