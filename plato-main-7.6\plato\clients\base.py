"""
The base class for all federated learning clients on edge devices or edge servers.
"""

import asyncio
import logging
import os
import pickle
import re
import sys
import time
import uuid
from abc import abstractmethod

import numpy as np
import socketio

from plato.callbacks.client import LogProgressCallback
from plato.callbacks.handler import CallbackHandler
from plato.config import Config
from plato.utils import s3


# pylint: disable=unused-argument, protected-access
class ClientEvents(socketio.AsyncClientNamespace):
    """A custom namespace for socketio.AsyncServer."""
#  中文注释：初始化方法，接受命名空间和Plato客户端作为参数。
    def __init__(self, namespace, plato_client):
        super().__init__(namespace)
        self.plato_client = plato_client
        self.client_id = plato_client.client_id

    async def on_connect(self):
        """Upon a new connection to the server."""# 中文注释：当客户端连接到服务器时执行。
        logging.info("[Client #%d] Connected to the server.", self.client_id)

    async def on_disconnect(self):
        """Upon a disconnection event."""# 中文注释：当客户端断开连接时执行。
        logging.info(
            "[Client #%d] The server disconnected the connection.", self.client_id
        )
        self.plato_client._clear_checkpoint_files()
        os._exit(0)

    async def on_connect_error(self, data):
        """Upon a failed connection attempt to the server."""# 中文注释：当客户端连接到服务器失败时执行。
        logging.info(
            "[Client #%d] A connection attempt to the server failed.", self.client_id
        )

    async def on_payload_to_arrive(self, data):
        """New payload is about to arrive from the server."""# 中文注释：新的负载即将从服务器到达。
        await self.plato_client._payload_to_arrive(data["response"])

    async def on_request_update(self, data):
        """The server is requesting an urgent model update."""#  中文注释：服务器正在请求紧急模型更新。
        await self.plato_client._request_update(data)

    async def on_chunk(self, data):
        """A chunk of data from the server arrived."""# 中文注释：服务器发送的分片数据到达了。
        await self.plato_client._chunk_arrived(data["data"])

    async def on_payload(self, data):
        """A portion of the new payload from the server arrived."""# 服务器发送的新负载的一部分到达了。
        await self.plato_client._payload_arrived(data["id"])

    async def on_payload_done(self, data):
        """All of the new payload sent from the server arrived."""# 服务器发送的所有新负载到达了。
        if "s3_key" in data:
            await self.plato_client._payload_done(data["id"], s3_key=data["s3_key"])
        else:
            await self.plato_client._payload_done(data["id"])


class Client:
    """A basic federated learning client."""#  中文注释：基本的联邦学习客户端。

    def __init__(self, callbacks=None) -> None:
        self.client_id = Config().args.id
        self.current_round = 0
        self.sio = None
        self.chunks = []
        self.server_payload = None
        self.s3_client = None
        self.outbound_processor = None
        self.inbound_processor = None
        self.payload = None
        self.report = None

        self.processing_time = 0

        self.comm_simulation = (
            Config().clients.comm_simulation
            if hasattr(Config().clients, "comm_simulation")
            else True
        )

        if hasattr(Config().algorithm, "cross_silo") and not Config().is_edge_server():
            self.edge_server_id = None

            assert hasattr(Config().algorithm, "total_silos")

        # Starting from the default client callback class, add all supplied server callbacks
        # 从默认的客户端回调类开始，添加所有提供的服务器回调
        self.callbacks = [LogProgressCallback]
        if callbacks is not None:
            self.callbacks.extend(callbacks)
        self.callback_handler = CallbackHandler(self.callbacks)

    def __repr__(self):
        return f"Client #{self.client_id}"

    async def start_client(self) -> None:
        """Startup function for a client."""# 客户端的启动函数。
        if hasattr(Config().algorithm, "cross_silo") and not Config().is_edge_server():
            # Contact one of the edge servers
            self.edge_server_id = self.get_edge_server_id()

            logging.info(
                "[Client #%d] Contacting Edge Server #%d.",
                self.client_id,
                self.edge_server_id,
            )
        else:
            await asyncio.sleep(5)
            logging.info("[Client #%d] Contacting the server.", self.client_id)

        self.sio = socketio.AsyncClient(reconnection=True)
        self.sio.register_namespace(ClientEvents(namespace="/", plato_client=self))

        if hasattr(Config().server, "s3_endpoint_url"):
            self.s3_client = s3.S3()

        if hasattr(Config().server, "use_https"):
            uri = f"https://{Config().server.address}"
        else:
            uri = f"http://{Config().server.address}"

        if hasattr(Config().server, "port"):
            # If we are not using a production server deployed in the cloud
            # 注释：如果我们没有在云端部署的生产服务器
            if (
                hasattr(Config().algorithm, "cross_silo")
                and not Config().is_edge_server()
            ):
                uri = f"{uri}:{int(Config().server.port) + int(self.edge_server_id)}"
            else:
                uri = f"{uri}:{Config().server.port}"

        logging.info("[%s] Connecting to the server at %s.", self, uri)
        await self.sio.connect(uri, wait_timeout=600)
        await self.sio.emit("client_alive", {"pid": os.getpid(), "id": self.client_id})

        logging.info("[Client #%d] Waiting to be selected.", self.client_id)
        await self.sio.wait()

    def get_edge_server_id(self):
        """Returns the edge server id of the client in cross-silo FL."""
        # 返回跨边服务器的客户端ID。
        # 这个方法用于返回跨边服务器的客户端ID。
        # 具体来说，它会根据客户端ID和配置文件中的参数计算出边服务器ID。
        # 首先，它会检查是否设置了Config().trainer.max_concurrency参数，如果设置了，它会计算出每个边服务器需要启动的客户端数量。
        # 然后，它会将所有的客户端ID分成Config().algorithm.total_silos个部分，每个部分包含的客户端数量为Config().clients.per_round。
        # 最后，它会遍历每个部分，找到包含当前客户端ID的部分，并返回该部分的索引。
        # 这个方法的目的是根据客户端ID和配置文件中的参数计算出边服务器ID。
        # 具体来说，它会根据客户端ID和配置文件中的参数计算出边服务器ID。  
        launched_client_num = (
            min(
                Config().trainer.max_concurrency
                * max(1, Config().gpu_count())
                * Config().algorithm.total_silos,
                Config().clients.per_round,
            )
            if hasattr(Config().trainer, "max_concurrency")
            else Config().clients.per_round
        )

        edges_launched_clients = [
            len(i)
            for i in np.array_split(
                np.arange(launched_client_num), Config().algorithm.total_silos
            )
        ]

        total = 0
        for i, count in enumerate(edges_launched_clients):
            total += count
            if self.client_id <= total:
                return i + 1 + Config().clients.total_clients

    async def _payload_to_arrive(self, response) -> None:
        """Upon receiving a response from the server."""# 收到服务器的响应时执行。
        self.current_round = response["current_round"]

        # Update (virtual) client id for client, trainer and algorithm
        self.client_id = response["id"]

        logging.info("[Client #%d] Selected by the server.", self.client_id)

        self.process_server_response(response)

        self._load_data()
        self.configure()
        self._allocate_data()

        self.server_payload = None

        if self.comm_simulation:
            payload_filename = response["payload_filename"]
            with open(payload_filename, "rb") as payload_file:
                self.server_payload = pickle.load(payload_file)

            payload_size = sys.getsizeof(pickle.dumps(self.server_payload))

            logging.info(
                "[%s] Received %.2f MB of payload data from the server (simulated).",
                self,
                payload_size / 1024**2,
            )

            await self._handle_payload(self.server_payload)

    async def _handle_payload(self, inbound_payload):
        """Handles the inbound payload upon receiving it from the server."""
        # 处理收到的来自服务器的入站负载。
        self.inbound_received(self.inbound_processor)
        self.callback_handler.call_event(
            "on_inbound_received", self, self.inbound_processor
        )

        tic = time.perf_counter()
        processed_inbound_payload = self.inbound_processor.process(inbound_payload)
        self.processing_time = time.perf_counter() - tic

        # Inbound data is processed, computing outbound response
        report, outbound_payload = await self.inbound_processed(
            processed_inbound_payload
        )
        self.callback_handler.call_event(
            "on_inbound_processed", self, processed_inbound_payload
        )

        # Outbound data is ready to be processed
        tic = time.perf_counter()
        self.outbound_ready(report, self.outbound_processor)
        self.callback_handler.call_event(
            "on_outbound_ready", self, report, self.outbound_processor
        )
        processed_outbound_payload = self.outbound_processor.process(outbound_payload)
        self.processing_time += time.perf_counter() - tic
        report.processing_time = self.processing_time

        # Sending the client report as metadata to the server (payload to follow)
        await self.sio.emit(
            "client_report", {"id": self.client_id, "report": pickle.dumps(report)}
        )

        # Sending the client training payload to the server
        await self._send(processed_outbound_payload)

    def inbound_received(self, inbound_processor):
        """
        Override this method to complete additional tasks before the inbound processors start to
        process the data received from the server.
        """
        # 这个方法用于在入站处理器开始处理来自服务器的数据之前完成额外的任务。
        # 具体来说，它会调用CallbackHandler类的call_event方法，
        # 并将self、inbound_processor作为参数传递给该方法。
      

    async def inbound_processed(self, processed_inbound_payload):
        """
        Override this method to conduct customized operations to generate a client's response to
        the server when inbound payload from the server has been processed.
        """# 该方法用于处理客户端发来的消息，并进行一些自定义操作。
        report, outbound_payload = await self._start_training(processed_inbound_payload)
        return report, outbound_payload

    def outbound_ready(self, report, outbound_processor):
        """
        Override this method to complete additional tasks before the outbound processors start
        to process the data to be sent to the server.
        """

    async def _chunk_arrived(self, data) -> None:
        """Upon receiving a chunk of data from the server."""
        self.chunks.append(data)

    async def _request_update(self, data) -> None:
        """Upon receiving a request for an urgent model update."""
        logging.info(
            "[Client #%s] Urgent request received for model update at time %s.",
            data["client_id"],
            data["time"],
        )

        report, payload = await self._obtain_model_update(
            client_id=data["client_id"],
            requested_time=data["time"],
        )

        # Process outbound data when necessary
        self.callback_handler.call_event(
            "on_outbound_ready", self, report, self.outbound_processor
        )
        self.outbound_ready(report, self.outbound_processor)
        payload = self.outbound_processor.process(payload)

        # Sending the client report as metadata to the server (payload to follow)
        await self.sio.emit(
            "client_report", {"id": self.client_id, "report": pickle.dumps(report)}
        )

        # Sending the client training payload to the server
        await self._send(payload)

    async def _payload_arrived(self, client_id) -> None:
        """Upon receiving a portion of the new payload from the server."""
        # 收到服务器发送的新负载的一部分时执行。
        assert client_id == self.client_id

        payload = b"".join(self.chunks)
        _data = pickle.loads(payload)
        self.chunks = []

        if self.server_payload is None:
            self.server_payload = _data
        elif isinstance(self.server_payload, list):
            self.server_payload.append(_data)
        else:
            self.server_payload = [self.server_payload]
            self.server_payload.append(_data)

    async def _payload_done(self, client_id, s3_key=None) -> None:
        """Upon receiving all the new payload from the server."""# 收到服务器发送的所有新负载时执行。
        payload_size = 0

        if s3_key is None:
            if isinstance(self.server_payload, list):
                for _data in self.server_payload:
                    payload_size += sys.getsizeof(pickle.dumps(_data))
            elif isinstance(self.server_payload, dict):
                for key, value in self.server_payload.items():
                    payload_size += sys.getsizeof(pickle.dumps({key: value}))
            else:
                payload_size = sys.getsizeof(pickle.dumps(self.server_payload))
        else:
            self.server_payload = self.s3_client.receive_from_s3(s3_key)
            payload_size = sys.getsizeof(pickle.dumps(self.server_payload))

        assert client_id == self.client_id

        logging.info(
            "[Client #%d] Received %.2f MB of payload data from the server.",
            client_id,
            payload_size / 1024**2,
        )

        await self._handle_payload(self.server_payload)

    async def _start_training(self, inbound_payload):
        """Complete one round of training on this client."""
        # 完成一次训练。 
        self._load_payload(inbound_payload)

        report, outbound_payload = await self._train()

        if Config().is_edge_server():
            logging.info(
                "[Server #%d] Model aggregated on edge server (%s).", os.getpid(), self
            )
        else:
            logging.info("[%s] Model trained.", self)

        return report, outbound_payload

    async def _send_in_chunks(self, data) -> None:
        """Sending a bytes object in fixed-sized chunks to the client."""
        # 发送一个字节对象到客户端。
        step = 1024**2
        chunks = [data[i : i + step] for i in range(0, len(data), step)]

        for chunk in chunks:
            await self.sio.emit("chunk", {"data": chunk})

        await self.sio.emit("client_payload", {"id": self.client_id})

#我要在这个部分进行修改 发送客户端负载到服务器。 
    async def _send(self, payload) -> None:
        """Sending the client payload to the server using simulation, S3 or socket.io."""
        # 发送客户端负载到服务器。 
        # 如果启用了通信模拟，则使用模拟进行通信。
        # 如果启用了S3，则使用S3进行通信。
        # 如果都未启用，则使用socket.io进行通信。
        if self.comm_simulation:
            # 使用文件系统进行通信模拟
            # If we are using the filesystem to simulate communication over a network
            model_name = (
                Config().trainer.model_name
                if hasattr(Config().trainer, "model_name")
                else "custom"
            )# 获取模型名称。
            if "/" in model_name:
                model_name = model_name.replace("/", "_")
            checkpoint_path = Config().params["checkpoint_path"]
            payload_filename = (
                f"{checkpoint_path}/{model_name}_client_{self.client_id}.pth"
            )#  获取模型保存的文件名。

            # 使用临时文件确保原子性写入
            temp_filename = payload_filename + ".tmp"
            try:
                with open(temp_filename, "wb") as payload_file:
                    pickle.dump(payload, payload_file)
                    payload_file.flush()  # 确保数据写入磁盘
                    os.fsync(payload_file.fileno())  # 强制同步到磁盘

                # 原子性重命名
                import shutil
                shutil.move(temp_filename, payload_filename)
            except Exception as e:
                # 清理临时文件
                if os.path.exists(temp_filename):
                    os.remove(temp_filename)
                raise e

            data_size = sys.getsizeof(pickle.dumps(payload))
#  发送模型数据到服务器。
            logging.info(
                "[%s] Sent %.2f MB of payload data to the server (simulated).",
                self,
                data_size / 1024**2,
            )#  打印发送的模型数据大小。

        else:#  使用socket.io进行通信。
            metadata = {"id": self.client_id}
#  使用S3进行通信。
            if self.s3_client is not None:
                unique_key = uuid.uuid4().hex[:6].upper()
                s3_key = f"client_payload_{self.client_id}_{unique_key}"
                self.s3_client.send_to_s3(s3_key, payload)
                data_size = sys.getsizeof(pickle.dumps(payload))
                metadata["s3_key"] = s3_key
            else:
                if isinstance(payload, list):
                    data_size: int = 0

                    for data in payload:
                        _data = pickle.dumps(data)
                        await self._send_in_chunks(_data)
                        data_size += sys.getsizeof(_data)
                else:
                    _data = pickle.dumps(payload)
                    await self._send_in_chunks(_data)
                    data_size = sys.getsizeof(_data)
# 发送模型数据到服务器。
            await self.sio.emit("client_payload_done", metadata)
#  打印发送的模型数据大小。
            logging.info(
                "[%s] Sent %.2f MB of payload data to the server.",
                self,
                data_size / 1024**2,
            )

    def _clear_checkpoint_files(self):
        """Delete all the temporary checkpoint files created by the client."""
        model_path = Config().params["model_path"]
        for filename in os.listdir(model_path):
            split = re.match(
                r"(?P<client_id>\d+)_(?P<epoch>\d+)_(?P<training_time>\d+.\d+).pth",
                filename,
            )
            if split is not None:
                file_path = f"{model_path}/{filename}"
                os.remove(file_path)

    def add_callbacks(self, callbacks):
        """Adds a list of callbacks to the client callback handler."""
        self.callback_handler.add_callbacks(callbacks)

    @abstractmethod
    async def _train(self):
        """The machine learning training workload on a client."""

    @abstractmethod
    def configure(self) -> None:
        """Prepare this client for training."""

    @abstractmethod
    def _load_data(self) -> None:
        """Generating data and loading them onto this client."""

    @abstractmethod
    def _allocate_data(self) -> None:
        """Allocate training or testing dataset of this client."""

    @abstractmethod
    def _load_payload(self, server_payload) -> None:
        """Loading the payload onto this client."""

    def process_server_response(self, server_response) -> None:
        """Additional client-specific processing on the server response."""
        # 对服务器响应进行额外的处理。   

    @abstractmethod
    async def _obtain_model_update(self, client_id, requested_time):
        """Retrieving a model update corrsponding to a particular wall clock time."""
        # 注释：这个方法在基类中定义，子类必须实现该方法。
        # 具体来说，这个方法用于从服务器获取一个模型更新。
        # 它接受两个参数：client_id和requested_time。
        # client_id是请求模型更新的客户端ID。
        # requested_time是请求模型更新的时间。
        # 这个方法的实现应该根据具体的需求来编写。
        # 具体来说，这个方法应该执行以下操作：
        # 1. 从服务器获取一个模型更新。
        # 2. 返回模型更新。

