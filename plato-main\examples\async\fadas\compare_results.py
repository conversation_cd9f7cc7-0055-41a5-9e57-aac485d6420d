#!/usr/bin/env python3
"""
实验结果对比分析脚本
对比FedADS在标准环境和网络波动环境下的性能差异
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

def load_fadas_results():
    """加载FedADS实验结果"""
    # 标准环境结果
    standard_file = "results/mnist/01/14020.csv"
    # 网络波动环境结果  
    network_file = "results/mnist/network_test/4140.csv"
    
    try:
        # 读取标准环境数据
        df_standard = pd.read_csv(standard_file)
        df_standard = df_standard.dropna()  # 移除空行
        
        # 读取网络波动环境数据
        df_network = pd.read_csv(network_file)
        df_network = df_network.dropna()  # 移除空行
        
        return df_standard, df_network
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None

def create_comparison_plots(df_standard, df_network):
    """创建对比图表"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 准确率对比
    ax1.plot(df_standard['round'], df_standard['accuracy'], 'b-o', label='标准环境', linewidth=2, markersize=6)
    ax1.plot(df_network['round'], df_network['accuracy'], 'r-s', label='网络波动环境', linewidth=2, markersize=6)
    ax1.set_xlabel('训练轮次')
    ax1.set_ylabel('准确率')
    ax1.set_title('FedADS准确率对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 训练时间对比
    ax2.plot(df_standard['round'], df_standard['elapsed_time'], 'b-o', label='标准环境', linewidth=2, markersize=6)
    ax2.plot(df_network['round'], df_network['elapsed_time'], 'r-s', label='网络波动环境', linewidth=2, markersize=6)
    ax2.set_xlabel('训练轮次')
    ax2.set_ylabel('累计时间 (秒)')
    ax2.set_title('FedADS训练时间对比')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 每轮训练时间
    standard_round_time = np.diff(np.concatenate([[0], df_standard['elapsed_time'].values]))
    network_round_time = np.diff(np.concatenate([[0], df_network['elapsed_time'].values]))
    
    ax3.bar(df_standard['round'] - 0.2, standard_round_time, 0.4, label='标准环境', alpha=0.7)
    ax3.bar(df_network['round'] + 0.2, network_round_time, 0.4, label='网络波动环境', alpha=0.7)
    ax3.set_xlabel('训练轮次')
    ax3.set_ylabel('每轮时间 (秒)')
    ax3.set_title('FedADS每轮训练时间对比')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 准确率差异
    accuracy_diff = df_network['accuracy'].values - df_standard['accuracy'].values
    colors = ['green' if x >= 0 else 'red' for x in accuracy_diff]
    ax4.bar(df_standard['round'], accuracy_diff, color=colors, alpha=0.7)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax4.set_xlabel('训练轮次')
    ax4.set_ylabel('准确率差异 (%)')
    ax4.set_title('网络波动环境相对标准环境的准确率提升')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('fadas_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_summary_table(df_standard, df_network):
    """生成汇总表格"""
    
    print("\n" + "="*80)
    print("📊 FedADS 实验结果汇总对比")
    print("="*80)
    
    # 基本统计
    print(f"\n📈 基本统计:")
    print(f"{'指标':<20} {'标准环境':<15} {'网络波动环境':<15} {'差异':<10}")
    print("-" * 65)
    
    final_acc_std = df_standard['accuracy'].iloc[-1]
    final_acc_net = df_network['accuracy'].iloc[-1]
    final_time_std = df_standard['elapsed_time'].iloc[-1]
    final_time_net = df_network['elapsed_time'].iloc[-1]
    
    print(f"{'最终准确率':<20} {final_acc_std:<15.1%} {final_acc_net:<15.1%} {final_acc_net-final_acc_std:<+10.1%}")
    print(f"{'总训练时间':<20} {final_time_std:<15.1f}秒 {final_time_net:<15.1f}秒 {final_time_net-final_time_std:<+10.1f}秒")
    print(f"{'平均每轮时间':<20} {final_time_std/len(df_standard):<15.1f}秒 {final_time_net/len(df_network):<15.1f}秒 {(final_time_net/len(df_network))-(final_time_std/len(df_standard)):<+10.1f}秒")
    
    # 详细轮次对比
    print(f"\n📋 详细轮次对比:")
    print(f"{'轮次':<6} {'标准环境准确率':<12} {'网络波动准确率':<12} {'准确率差异':<10} {'时间差异':<10}")
    print("-" * 65)
    
    for i in range(min(len(df_standard), len(df_network))):
        round_num = df_standard.iloc[i]['round']
        acc_std = df_standard.iloc[i]['accuracy']
        acc_net = df_network.iloc[i]['accuracy']
        time_std = df_standard.iloc[i]['elapsed_time']
        time_net = df_network.iloc[i]['elapsed_time']
        
        print(f"{round_num:<6} {acc_std:<12.1%} {acc_net:<12.1%} {acc_net-acc_std:<+10.1%} {time_net-time_std:<+10.1f}秒")

def main():
    """主函数"""
    print("🔍 加载FedADS实验结果...")
    
    df_standard, df_network = load_fadas_results()
    
    if df_standard is None or df_network is None:
        print("❌ 无法加载实验结果文件")
        return
    
    print("✅ 数据加载成功")
    print(f"标准环境: {len(df_standard)} 轮数据")
    print(f"网络波动环境: {len(df_network)} 轮数据")
    
    # 生成汇总表格
    generate_summary_table(df_standard, df_network)
    
    # 创建对比图表
    print("\n📊 生成对比图表...")
    create_comparison_plots(df_standard, df_network)
    print("✅ 图表已保存为 fadas_comparison.png")

if __name__ == "__main__":
    main()
