"""
A federated learning server using Port.

Reference:

"How Asynchronous can Federated Learning Be?"

"""
import os

os.environ["WANDB_DISABLED"] = "true"
os.environ["TOKENIZERS_PARALLELISM"] = "false"

import port_server
from Origin_client import Origin_client
from Origin_trainer import Origin_trainer


def main():
    """ A Plato federated learning training session using FedAsync. """
    trainer = Origin_trainer
    client = Origin_client(trainer=trainer)
    server = port_server.Server(trainer=trainer)
    server.run(client)


if __name__ == "__main__":
    main()
