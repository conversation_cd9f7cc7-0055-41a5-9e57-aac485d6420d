"""
A federated learning training session using <PERSON><PERSON><PERSON>

<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, 
“Hermes: An Efficient Federated Learning Framework for Heterogeneous Mobile
Clients,” in Proc. 27th Annual International Conference on Mobile Computing and
Networking (MobiCom), 2021.
"""

from hermes_callback import HermesCallback
import hermes_trainer
import hermes_server


from plato.clients import fedavg_personalized as personalized_client


def main():
    """A Plato federated learning training session using the <PERSON>mes algorithm."""
    trainer = hermes_trainer.Trainer

    client = personalized_client.Client(trainer=trainer, callbacks=[HermesCallback])
    server = hermes_server.Server(trainer=trainer)

    server.run(client)


if __name__ == "__main__":
    main()
