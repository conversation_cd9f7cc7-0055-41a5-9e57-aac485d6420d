"""
The Processor class is designed for pre-processing data payloads before or after they
are transmitted over the network between the clients and the servers.
"""
from abc import abstractmethod
from collections.abc import Iterable
from typing import Any


class Processor:
    """
    The base Processor class does nothing on the data payload.
    """
    #中文注释：
    #Processor 类是为了在客户端和服务器之间传输数据载荷之前或之后对数据载荷进行预处理而设计的。
    #Processor 类的作用是对数据载荷进行预处理，例如压缩、加密、解密等。


    def __init__(self, name=None, trainer=None, **kwargs) -> None:
        """Constructor for Processor."""
        self.name = name
        self.trainer = trainer

#中文注释：
    @abstractmethod
    def process(self, data: Any) -> Any:
        """
        Processing a data payload.
        """
        return data

    def process_iterable(self, data: Iterable) -> Iterable:
        """
        Processing an Iterable of data payload.#
        处理数据载荷的可迭代对象。
        这个方法接受一个可迭代对象 data，对其中的每个元素调用 process 方法进行处理，
        然后返回一个新的可迭代对象，其中包含处理后的元素。
        """
        return map(self.process, data)

    def __repr__(self) -> str:
        return self.name
