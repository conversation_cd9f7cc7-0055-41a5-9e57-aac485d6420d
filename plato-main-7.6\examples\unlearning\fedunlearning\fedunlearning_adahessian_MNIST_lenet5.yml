clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 10

    # The number of clients selected in each round
    per_round: 2

    # Should the clients compute test accuracy locally?
    do_test: false

    # The round to remove the local data by clients
    data_deletion_round: 2

    # The clients which need to delete their local data samples
    clients_requesting_deletion: [2, 3]

    # The percentage to delete the local data by clients
    deleted_data_ratio: 0.3

    # Whether the same set of clients would be selected during retraining
    exact_retrain: true

server:
    address: 127.0.0.1
    port: 8000
    random_seed: 1

    mia_eval: true
    mia_eval_round: 2

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 20000

    # IID or non-IID?
    sampler: iid

    # Number of classes
    num_classes: 10

    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 5

    # The maximum number of clients running concurrently
    max_concurrency: 2

    # The target accuracy
    target_accuracy: 0.97

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 128
    optimizer: <PERSON><PERSON><PERSON>ian
    create_graph: true

    # The machine learning model
    model_name: lenet5

parameters:
    optimizer:
        lr: 0.001
        eps: 0.00000001
        weight_decay: 0.0
        hessian_power: 1.0
        betas: [0.9, 0.999] #the two momentums for betas

algorithm:
    # Aggregation algorithm
    type: fedavg

results:
    result_path: results/fedunlearning

    # Write the following parameter(s) into a CSV
    types: round, accuracy, elapsed_time, comm_time, round_time
