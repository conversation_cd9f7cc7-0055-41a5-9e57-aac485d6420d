clients:
    # Type
    type: mistnet

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    type: mistnet

    address: 127.0.0.1
    port: 8000

data:
    # The training and testing dataset
    datasource: YOLO
    data_params: configs/yolov5/data/coco128.yaml

    # Where the dataset is located
    data_path: data/COCO
    train_path: ./data/COCO/coco128/images/train2017/
    test_path: ./data/COCO/coco128/images/train2017/

    # download command/URL (optional)
    download_urls:
        [
            "https://github.com/ultralytics/yolov5/releases/download/v1.0/coco128.zip",
        ]

    # number of training examples
    num_train_examples: 128

    # number of testing examples
    num_test_examples: 128

    # number of classes
    num_classes: 80

    # image size
    image_size: 640

    # class names
    classes:
        [
            "person",
            "bicycle",
            "car",
            "motorcycle",
            "airplane",
            "bus",
            "train",
            "truck",
            "boat",
            "traffic light",
            "fire hydrant",
            "stop sign",
            "parking meter",
            "bench",
            "bird",
            "cat",
            "dog",
            "horse",
            "sheep",
            "cow",
            "elephant",
            "bear",
            "zebra",
            "giraffe",
            "backpack",
            "umbrella",
            "handbag",
            "tie",
            "suitcase",
            "frisbee",
            "skis",
            "snowboard",
            "sports ball",
            "kite",
            "baseball bat",
            "baseball glove",
            "skateboard",
            "surfboard",
            "tennis racket",
            "bottle",
            "wine glass",
            "cup",
            "fork",
            "knife",
            "spoon",
            "bowl",
            "banana",
            "apple",
            "sandwich",
            "orange",
            "broccoli",
            "carrot",
            "hot dog",
            "pizza",
            "donut",
            "cake",
            "chair",
            "couch",
            "potted plant",
            "bed",
            "dining table",
            "toilet",
            "tv",
            "laptop",
            "mouse",
            "remote",
            "keyboard",
            "cell phone",
            "microwave",
            "oven",
            "toaster",
            "sink",
            "refrigerator",
            "book",
            "clock",
            "vase",
            "scissors",
            "teddy bear",
            "hair drier",
            "toothbrush",
        ]

    # Number of samples in each partition
    partition_size: 128

    # IID or non-IID?
    sampler: all_inclusive

    # Input height
    input_height: 640

    # Input width
    input_width: 640

trainer:
    # The type of the trainer
    type: yolov5

    # The maximum number of training rounds
    rounds: 1

    # The maximum number of clients running concurrently
    max_concurrency: 3

    # The target accuracy
    target_accuracy: 0.99

    # Number of epoches for local training in each communication round
    epochs: 500
    batch_size: 16
    optimizer: SGD
    linear_lr: false

    # The machine learning model
    model_name: yolov5

    # OM file path
    om_path: ./models/yolov5_cutlayer4.om

    # Device ID
    deviceID: 0

    # framework
    use_nnrt: true

algorithm:
    # Aggregation algorithm
    type: mistnet

    cut_layer: 4
    epsilon: 100

parameters:
    grid_size: 32

    model:
        model_config: configs/yolov5/models/yolov5s.yaml
        num_classes: 80

    trainer:
        train_params: configs/yolov5/data/hyps/hyp.scratch.yaml
