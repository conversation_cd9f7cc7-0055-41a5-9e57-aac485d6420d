clients:
    # Type
    type: simple_he

    # The total number of clients
    total_clients: 10

    # encrypt ratio
    encrypt_ratio: 0.05
    random_mask: False

    # The number of clients selected in each round
    per_round: 2

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8000
    random_seed: 1
    simulate_wall_time: true

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 1000

    # IID or non-IID?
    sampler: iid

trainer:

    # The maximum number of training rounds
    rounds: 50

    # The maximum number of clients running concurrently
    max_concurrency: 1

    # The target accuracy
    target_accuracy: 0.99

    # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 2
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

results:
    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, comm_overhead

parameters:
    model:
        num_classes: 10
        
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0
