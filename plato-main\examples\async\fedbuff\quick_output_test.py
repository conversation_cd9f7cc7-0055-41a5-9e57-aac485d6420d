#!/usr/bin/env python3
"""
快速测试FedBuff结果输出功能
"""

import os
import csv
import sys
from datetime import datetime

# 添加plato路径
sys.path.append('../../../')

def test_csv_output():
    """测试CSV输出功能"""
    print("🧪 快速CSV输出测试")
    print("=" * 40)
    
    try:
        # 创建测试目录
        test_dir = "results/output_test"
        os.makedirs(test_dir, exist_ok=True)
        print(f"✅ 创建测试目录: {test_dir}")
        
        # 生成测试文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file = os.path.join(test_dir, f"fedbuff_output_test_{timestamp}.csv")
        
        # 定义字段
        headers = [
            "round", "elapsed_time", "accuracy", "global_accuracy", "global_accuracy_std",
            "avg_staleness", "max_staleness", "min_staleness", 
            "network_success_rate", "avg_communication_time"
        ]
        
        # 创建CSV文件
        with open(test_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(headers)
            print(f"✅ 创建CSV文件: {test_file}")
            
            # 写入测试数据
            for round_num in range(1, 6):
                row = [
                    round_num,                           # round
                    round_num * 12.5,                   # elapsed_time
                    0.15 + round_num * 0.08,            # accuracy
                    0.13 + round_num * 0.07,            # global_accuracy
                    0.03 + round_num * 0.002,           # global_accuracy_std
                    1.8 + round_num * 0.3,              # avg_staleness
                    3 + round_num,                      # max_staleness
                    1,                                  # min_staleness
                    0.75 + round_num * 0.01,            # network_success_rate
                    2.2 + round_num * 0.2               # avg_communication_time
                ]
                
                writer.writerow(row)
                print(f"✅ 写入第{round_num}轮数据")
        
        # 验证文件
        if os.path.exists(test_file):
            print(f"\n📄 文件验证成功: {test_file}")
            
            # 读取并显示内容
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"\n📊 文件内容:")
                print(content)
            
            return test_file
        else:
            print(f"❌ 文件创建失败")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_plato_csv_processor():
    """测试plato的CSV处理器"""
    print(f"\n🔧 测试Plato CSV处理器")
    print("-" * 40)
    
    try:
        from plato.utils import csv_processor
        from plato.config import Config
        
        # 加载配置
        Config.load_config("fedbuff_MNIST_standard.yml")
        print("✅ 配置加载成功")
        
        # 获取配置信息
        result_path = Config().params.get("result_path")
        result_types = Config().params.get("result_types")
        
        print(f"📁 结果路径: {result_path}")
        print(f"📊 结果类型: {result_types}")
        
        # 确保目录存在
        os.makedirs(result_path, exist_ok=True)
        
        # 创建测试文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file = os.path.join(result_path, f"plato_csv_test_{timestamp}.csv")
        
        # 解析字段
        recorded_items_list = [x.strip() for x in result_types.split(",")]
        
        # 使用plato的CSV处理器
        csv_processor.initialize_csv(test_file, recorded_items_list, result_path)
        print(f"✅ Plato CSV初始化成功: {test_file}")
        
        # 写入测试数据
        for round_num in range(1, 4):
            test_row = [
                round_num,                    # round
                round_num * 15.0,            # elapsed_time
                0.2 + round_num * 0.1,       # accuracy
                0.18 + round_num * 0.09,     # global_accuracy
                0.03,                        # global_accuracy_std
                2.0 + round_num * 0.2,       # avg_staleness
                4 + round_num,               # max_staleness
                1                            # min_staleness
            ]
            
            csv_processor.write_csv(test_file, test_row)
            print(f"✅ Plato CSV写入第{round_num}轮成功")
        
        # 验证文件
        if os.path.exists(test_file):
            print(f"\n📄 Plato CSV文件验证成功")
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"\n📊 Plato CSV内容:")
                print(content)
            
            return test_file
        else:
            print(f"❌ Plato CSV文件创建失败")
            return None
            
    except Exception as e:
        print(f"❌ Plato CSV测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_current_fedbuff_run():
    """检查当前FedBuff运行状态"""
    print(f"\n🔍 检查当前FedBuff运行状态")
    print("-" * 40)
    
    # 检查所有结果目录
    result_dirs = [
        "results/mnist_standard_fedbuff/01",
        "results/mnist_network_test_fedbuff/01",
        "results/mnist_original_fedbuff/01",
        "results/output_test"
    ]
    
    for result_dir in result_dirs:
        print(f"\n📂 检查: {result_dir}")
        if os.path.exists(result_dir):
            files = os.listdir(result_dir)
            csv_files = [f for f in files if f.endswith('.csv')]
            
            if csv_files:
                print(f"✅ 找到CSV文件: {len(csv_files)}个")
                for csv_file in csv_files:
                    filepath = os.path.join(result_dir, csv_file)
                    try:
                        stat = os.stat(filepath)
                        size = stat.st_size
                        mtime = datetime.fromtimestamp(stat.st_mtime)
                        print(f"   📄 {csv_file}: {size}字节, 修改时间: {mtime}")
                    except Exception as e:
                        print(f"   ❌ 无法读取文件信息: {e}")
            else:
                print(f"❌ 没有CSV文件")
        else:
            print(f"❌ 目录不存在")

def main():
    """主函数"""
    print("🚀 FedBuff结果输出测试")
    print("=" * 60)
    
    # 1. 基础CSV输出测试
    test_file1 = test_csv_output()
    
    # 2. Plato CSV处理器测试
    test_file2 = test_plato_csv_processor()
    
    # 3. 检查当前状态
    check_current_fedbuff_run()
    
    # 总结
    print(f"\n🎯 测试总结")
    print("=" * 60)
    
    if test_file1:
        print(f"✅ 基础CSV输出: 正常")
    else:
        print(f"❌ 基础CSV输出: 失败")
    
    if test_file2:
        print(f"✅ Plato CSV处理器: 正常")
    else:
        print(f"❌ Plato CSV处理器: 失败")
    
    if test_file1 and test_file2:
        print(f"\n🎉 结果输出功能正常！")
        print(f"💡 FedBuff应该能够正常输出结果")
    else:
        print(f"\n⚠️ 部分功能异常，需要进一步调试")

if __name__ == "__main__":
    main()
