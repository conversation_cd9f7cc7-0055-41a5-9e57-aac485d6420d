clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: true

server:
    address: 127.0.0.1
    port: 8000

data:
    # The training and testing dataset
    dataname: ReferItGame
    datasource: COCO2017

    split_config: refcoco
    split_name: google # google, unc, umd
    download_splits_base_url: https://bvisionweb1.cs.unc.edu/licheng/referit/data/
    num_workers: 4

trainer:
    # The type of the trainer
    type: basic
    batch_size: 24

algorithm:
    # Aggregation algorithm
    type: fedavg
