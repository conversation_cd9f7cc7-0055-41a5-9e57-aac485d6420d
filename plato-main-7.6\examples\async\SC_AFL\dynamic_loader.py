"""
动态数据源和模型加载器
支持根据配置文件软编码加载任何数据集和模型
"""

import logging
import torch
from typing import Optional, Tuple, Any
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms

logger = logging.getLogger(__name__)

class DynamicDataLoader:
    """动态数据加载器"""
    
    # 数据集配置映射
    DATASET_CONFIGS = {
        'MNIST': {
            'module': 'torchvision.datasets',
            'class': 'MNIST',
            'channels': 1,
            'size': (28, 28),
            'num_classes': 10,
            'mean': (0.1307,),
            'std': (0.3081,)
        },
        'CIFAR10': {
            'module': 'torchvision.datasets',
            'class': 'CIFAR10',
            'channels': 3,
            'size': (32, 32),
            'num_classes': 10,
            'mean': (0.4914, 0.4822, 0.4465),
            'std': (0.2023, 0.1994, 0.2010)
        },
        'CIFAR100': {
            'module': 'torchvision.datasets',
            'class': 'CIFAR100',
            'channels': 3,
            'size': (32, 32),
            'num_classes': 100,
            'mean': (0.5071, 0.4867, 0.4408),
            'std': (0.2675, 0.2565, 0.2761)
        },
        'FMNIST': {
            'module': 'torchvision.datasets',
            'class': 'FashionMNIST',
            'channels': 1,
            'size': (28, 28),
            'num_classes': 10,
            'mean': (0.2860,),
            'std': (0.3530,)
        }
    }
    
    @classmethod
    def get_dataset_config(cls, dataset_name: str) -> dict:
        """获取数据集配置"""
        dataset_name = dataset_name.upper()
        if dataset_name not in cls.DATASET_CONFIGS:
            logger.warning(f"未知数据集 {dataset_name}，使用MNIST配置")
            return cls.DATASET_CONFIGS['MNIST']
        return cls.DATASET_CONFIGS[dataset_name]
    
    @classmethod
    def create_transform(cls, dataset_name: str, is_train: bool = False) -> transforms.Compose:
        """创建数据变换"""
        config = cls.get_dataset_config(dataset_name)
        
        transform_list = []
        
        # 基础变换
        if is_train:
            # 训练时的数据增强
            if config['channels'] == 3:  # 彩色图像
                transform_list.extend([
                    transforms.RandomHorizontalFlip(0.5),
                    transforms.RandomCrop(config['size'], padding=4)
                ])
        
        # 通用变换
        transform_list.extend([
            transforms.ToTensor(),
            transforms.Normalize(config['mean'], config['std'])
        ])
        
        return transforms.Compose(transform_list)
    
    @classmethod
    def load_dataset(cls, dataset_name: str, data_path: str = './data', 
                    is_train: bool = False, download: bool = True) -> Dataset:
        """动态加载数据集"""
        try:
            config = cls.get_dataset_config(dataset_name)
            
            # 动态导入数据集类
            module_name = config['module']
            class_name = config['class']
            
            import importlib
            module = importlib.import_module(module_name)
            dataset_class = getattr(module, class_name)
            
            # 创建变换
            transform = cls.create_transform(dataset_name, is_train)
            
            # 创建数据集
            dataset = dataset_class(
                root=data_path,
                train=is_train,
                download=download,
                transform=transform
            )
            
            logger.info(f"✅ 成功加载数据集 {dataset_name}: {len(dataset)} 样本")
            return dataset
            
        except Exception as e:
            logger.error(f"❌ 加载数据集 {dataset_name} 失败: {e}")
            raise

class DynamicModelLoader:
    """动态模型加载器"""
    
    # 模型配置映射
    MODEL_CONFIGS = {
        'lenet5': {
            'module': 'plato.models.lenet5',
            'class': 'Model',
            'supports_channels': True,
            'supports_classes': True
        },
        'resnet_9': {
            'module': 'plato.models.resnet',
            'class': 'Model',
            'method': 'get',
            'supports_channels': False,  # ResNet通过get方法创建
            'supports_classes': True
        },
        'resnet_18': {
            'module': 'plato.models.resnet',
            'class': 'Model',
            'method': 'get',
            'supports_channels': False,
            'supports_classes': True
        },
        'cnn': {
            'module': 'plato.models.cnn',
            'class': 'Model',
            'supports_channels': True,
            'supports_classes': True
        }
    }
    
    @classmethod
    def get_model_config(cls, model_name: str) -> dict:
        """获取模型配置"""
        model_name = model_name.lower()
        if model_name not in cls.MODEL_CONFIGS:
            logger.warning(f"未知模型 {model_name}，使用lenet5配置")
            return cls.MODEL_CONFIGS['lenet5']
        return cls.MODEL_CONFIGS[model_name]
    
    @classmethod
    def create_model(cls, model_name: str, num_classes: int = 10, 
                    in_channels: int = 1) -> torch.nn.Module:
        """动态创建模型"""
        try:
            config = cls.get_model_config(model_name)
            
            # 动态导入模型类
            module_name = config['module']
            class_name = config['class']
            
            import importlib
            module = importlib.import_module(module_name)
            model_class = getattr(module, class_name)
            
            # 创建模型
            if 'method' in config:
                # 使用特定方法创建（如ResNet的get方法）
                method = getattr(model_class, config['method'])
                model = method(model_name=model_name, num_classes=num_classes)
            else:
                # 直接实例化
                kwargs = {}
                if config['supports_classes']:
                    kwargs['num_classes'] = num_classes
                if config['supports_channels']:
                    kwargs['in_channels'] = in_channels
                    
                model = model_class(**kwargs)
            
            logger.info(f"✅ 成功创建模型 {model_name}: {type(model).__name__}")
            return model
            
        except Exception as e:
            logger.error(f"❌ 创建模型 {model_name} 失败: {e}")
            raise

class ConfigurationManager:
    """配置管理器"""
    
    @staticmethod
    def get_dataset_info_from_config(config) -> Tuple[str, int, int]:
        """从配置中获取数据集信息"""
        # 获取数据集名称
        dataset_name = getattr(config.data, 'datasource', 'MNIST')
        
        # 获取数据集配置
        dataset_config = DynamicDataLoader.get_dataset_config(dataset_name)
        
        # 从配置或数据集配置中获取参数
        num_classes = getattr(config.parameters.model, 'num_classes', dataset_config['num_classes'])
        in_channels = getattr(config.parameters.model, 'in_channels', dataset_config['channels'])
        
        return dataset_name, num_classes, in_channels
    
    @staticmethod
    def get_model_info_from_config(config) -> str:
        """从配置中获取模型信息"""
        # 尝试多个可能的配置位置
        model_name = None

        # 1. 尝试从trainer.model_name读取
        if hasattr(config, 'trainer') and hasattr(config.trainer, 'model_name'):
            model_name = config.trainer.model_name
            logger.info(f"从config.trainer.model_name读取模型: {model_name}")

        # 2. 尝试从model.name读取
        elif hasattr(config, 'model') and hasattr(config.model, 'name'):
            model_name = config.model.name
            logger.info(f"从config.model.name读取模型: {model_name}")

        # 3. 尝试从parameters.model.name读取
        elif hasattr(config, 'parameters') and hasattr(config.parameters, 'model') and hasattr(config.parameters.model, 'name'):
            model_name = config.parameters.model.name
            logger.info(f"从config.parameters.model.name读取模型: {model_name}")

        # 4. 默认值
        if not model_name:
            model_name = 'lenet5'
            logger.warning(f"未找到模型配置，使用默认模型: {model_name}")

        return model_name
    
    @staticmethod
    def auto_configure_from_dataset(config, dataset_name: str):
        """根据数据集自动配置参数"""
        dataset_config = DynamicDataLoader.get_dataset_config(dataset_name)
        
        # 自动设置模型参数
        if not hasattr(config.parameters, 'model'):
            config.parameters.model = type('obj', (object,), {})()
            
        if not hasattr(config.parameters.model, 'num_classes'):
            config.parameters.model.num_classes = dataset_config['num_classes']
            logger.info(f"自动设置 num_classes = {dataset_config['num_classes']}")
            
        if not hasattr(config.parameters.model, 'in_channels'):
            config.parameters.model.in_channels = dataset_config['channels']
            logger.info(f"自动设置 in_channels = {dataset_config['channels']}")

def create_dynamic_components(config, data_path: str = './data'):
    """
    根据配置动态创建所有组件
    
    Returns:
        tuple: (model, train_dataset, test_dataset, dataset_info)
    """
    try:
        # 获取配置信息
        dataset_name, num_classes, in_channels = ConfigurationManager.get_dataset_info_from_config(config)
        model_name = ConfigurationManager.get_model_info_from_config(config)
        
        # 自动配置
        ConfigurationManager.auto_configure_from_dataset(config, dataset_name)
        
        # 创建数据集
        train_dataset = DynamicDataLoader.load_dataset(
            dataset_name, data_path, is_train=True, download=True
        )
        test_dataset = DynamicDataLoader.load_dataset(
            dataset_name, data_path, is_train=False, download=True
        )
        
        # 创建模型
        model = DynamicModelLoader.create_model(
            model_name, num_classes=num_classes, in_channels=in_channels
        )
        
        # 数据集信息
        dataset_info = {
            'name': dataset_name,
            'num_classes': num_classes,
            'in_channels': in_channels,
            'train_size': len(train_dataset),
            'test_size': len(test_dataset)
        }
        
        logger.info(f"🎉 动态组件创建完成:")
        logger.info(f"   数据集: {dataset_name} ({dataset_info['train_size']} + {dataset_info['test_size']})")
        logger.info(f"   模型: {model_name} ({num_classes} 类, {in_channels} 通道)")
        
        return model, train_dataset, test_dataset, dataset_info
        
    except Exception as e:
        logger.error(f"❌ 动态组件创建失败: {e}")
        raise
