"""
SCAFL客户端实现
师妹学习版本 - 详细注释

这个文件实现了SCAFL的客户端逻辑：
1. 支持异步训练
2. 在上传时包含必要的信息（样本数量等）
3. 处理陈旧度相关的逻辑

注意：在SCAFL中，客户端的修改相对较少，
主要的创新在算法和服务器端
"""

import logging
import time
from plato.clients import simple
from plato.config import Config


class Client(simple.Client):
    """SCAFL客户端类
    
    继承自简单客户端，添加SCAFL特有的功能
    """
    
    def __init__(self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None, trainer_callbacks=None):
        """初始化SCAFL客户端"""
        super().__init__(model, datasource, algorithm, trainer, callbacks, trainer_callbacks)
        
        # SCAFL客户端状态
        self.training_start_time = None    # 训练开始时间
        self.training_end_time = None      # 训练结束时间
        self.local_staleness = 0           # 本地陈旧度估计
        
        logging.info(f"[SCAFL Client] 客户端{self.client_id}初始化完成")
    
    def configure(self):
        """配置客户端"""
        super().configure()
        logging.info(f"[SCAFL Client] 客户端{self.client_id}配置完成")
    
    async def train(self):
        """训练过程
        
        在SCAFL中，客户端的训练过程基本不变，
        但我们需要记录一些额外信息用于后续分析
        """
        logging.info(f"[SCAFL Client] 客户端{self.client_id}开始训练")
        
        # 记录训练开始时间
        self.training_start_time = time.time()
        
        # 调用父类的训练方法
        report = await super().train()
        
        # 记录训练结束时间
        self.training_end_time = time.time()
        
        # 计算实际训练时间
        if self.training_start_time:
            actual_training_time = self.training_end_time - self.training_start_time
            logging.info(f"[SCAFL Client] 客户端{self.client_id}训练完成，"
                        f"实际训练时间: {actual_training_time:.2f}秒")
        
        return report
    
    def customize_report(self, report):
        """自定义报告内容
        
        在SCAFL中，我们可能需要在报告中包含额外信息：
        1. 实际训练时间
        2. 本地数据统计
        3. 其他SCAFL相关信息
        
        Args:
            report: 原始训练报告
            
        Returns:
            修改后的报告
        """
        # 添加训练时间信息
        if hasattr(self, 'training_start_time') and hasattr(self, 'training_end_time'):
            if self.training_start_time and self.training_end_time:
                report.actual_training_time = self.training_end_time - self.training_start_time
        
        # 添加客户端ID（确保服务器能识别）
        report.client_id = self.client_id
        
        # 添加样本数量（用于加权聚合）
        if hasattr(self, 'sampler') and self.sampler:
            report.num_samples = self.sampler.num_samples()
        else:
            report.num_samples = len(self.trainset) if hasattr(self, 'trainset') and self.trainset else 1
        
        logging.debug(f"[SCAFL Client] 客户端{self.client_id}报告: "
                     f"样本数={report.num_samples}, "
                     f"准确率={getattr(report, 'accuracy', 'N/A')}")
        
        return report
    
    async def send_report(self, report):
        """发送报告给服务器
        
        在发送前进行最后的自定义
        """
        # 自定义报告内容
        customized_report = self.customize_report(report)
        
        # 调用父类方法发送报告
        await super().send_report(customized_report)
        
        logging.info(f"[SCAFL Client] 客户端{self.client_id}已发送训练报告")
    
    def process_server_response(self, server_response):
        """处理服务器响应
        
        在SCAFL中，服务器可能会发送一些额外信息：
        1. 当前轮次信息
        2. 陈旧度信息
        3. 其他协调信息
        
        Args:
            server_response: 服务器响应
        """
        # 调用父类方法处理基本响应
        super().process_server_response(server_response)
        
        # 处理SCAFL特有的响应信息
        if hasattr(server_response, 'current_round'):
            logging.info(f"[SCAFL Client] 客户端{self.client_id}收到轮次信息: "
                        f"第{server_response.current_round}轮")
        
        if hasattr(server_response, 'staleness_info'):
            self.local_staleness = server_response.staleness_info
            logging.info(f"[SCAFL Client] 客户端{self.client_id}当前陈旧度: {self.local_staleness}")
    
    def get_client_info(self):
        """获取客户端信息
        
        返回客户端的状态信息，用于调试和监控
        
        Returns:
            dict: 客户端信息字典
        """
        info = {
            'client_id': self.client_id,
            'local_staleness': self.local_staleness,
            'training_start_time': self.training_start_time,
            'training_end_time': self.training_end_time,
        }
        
        # 添加数据集信息
        if hasattr(self, 'sampler') and self.sampler:
            info['num_samples'] = self.sampler.num_samples()
        
        # 添加训练时间信息
        if self.training_start_time and self.training_end_time:
            info['actual_training_time'] = self.training_end_time - self.training_start_time
        
        return info
