[INFO][06:48:10]: 日志系统已初始化
[INFO][06:48:10]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_064810.log
[INFO][06:48:10]: 日志级别: INFO
[WARNING][06:48:10]: 无法获取系统信息: No module named 'psutil'
[INFO][06:48:10]: 🚀 ReFedScaFL 训练开始
[INFO][06:48:10]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][06:48:10]: 开始时间: 2025-07-29 06:48:10
[INFO][06:48:10]: [Client None] 基础初始化完成
[INFO][06:48:10]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][06:48:10]: 创建并缓存共享模型
[INFO][06:48:10]: [93m[1m[32532] Logging runtime results to: ./results/refedscafl/comparison_cifar10_alpha01/32532.csv.[0m
[INFO][06:48:10]: [Server #32532] Started training on 20 clients with 20 per round.
[INFO][06:48:10]: 服务器参数配置完成：
[INFO][06:48:10]: - 客户端数量: total=20, per_round=20
[INFO][06:48:10]: - 权重参数: success=0.8, distill=0.2
[INFO][06:48:10]: - SCAFL参数: V=1.0, tau_max=5
[INFO][06:48:10]: 从共享资源模型提取并缓存全局权重
[INFO][06:48:10]: [Server #32532] Configuring the server...
[INFO][06:48:10]: Training: 400 rounds or accuracy above 100.0%

[INFO][06:48:10]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][06:48:10]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][06:48:10]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][06:48:10]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][06:48:10]: Algorithm: fedavg
[INFO][06:48:10]: Data source: CIFAR10
[INFO][06:48:14]: Starting client #1's process.
[INFO][06:48:14]: Starting client #2's process.
[INFO][06:48:14]: Starting client #3's process.
[INFO][06:48:14]: Starting client #4's process.
[INFO][06:48:14]: Starting client #5's process.
[INFO][06:48:14]: Starting client #6's process.
[INFO][06:48:14]: Starting client #7's process.
[INFO][06:48:14]: Starting client #8's process.
[INFO][06:48:14]: Starting client #9's process.
[INFO][06:48:14]: Starting client #10's process.
[INFO][06:48:14]: Setting the random seed for selecting clients: 1
[INFO][06:48:14]: Starting a server at address 127.0.0.1 and port 8092.
[INFO][06:48:38]: [Server #32532] A new client just connected.
[INFO][06:48:38]: [Server #32532] New client with id #3 arrived.
[INFO][06:48:38]: [Server #32532] Client process #30764 registered.
[INFO][06:48:38]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][06:48:38]: [Server #32532] A new client just connected.
[INFO][06:48:38]: [Server #32532] New client with id #2 arrived.
[INFO][06:48:38]: [Server #32532] Client process #35140 registered.
[INFO][06:48:38]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][06:48:38]: [Server #32532] A new client just connected.
[INFO][06:48:38]: [Server #32532] New client with id #10 arrived.
[INFO][06:48:38]: [Server #32532] Client process #31256 registered.
[INFO][06:48:38]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][06:48:39]: [Server #32532] A new client just connected.
[INFO][06:48:39]: [Server #32532] New client with id #6 arrived.
[INFO][06:48:39]: [Server #32532] Client process #7336 registered.
[INFO][06:48:39]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][06:48:39]: [Server #32532] A new client just connected.
[INFO][06:48:39]: [Server #32532] New client with id #5 arrived.
[INFO][06:48:39]: [Server #32532] Client process #21720 registered.
[INFO][06:48:39]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][06:48:39]: [Server #32532] A new client just connected.
[INFO][06:48:39]: [Server #32532] New client with id #9 arrived.
[INFO][06:48:39]: [Server #32532] Client process #35260 registered.
[INFO][06:48:39]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][06:48:39]: [Server #32532] A new client just connected.
[INFO][06:48:39]: [Server #32532] A new client just connected.
[INFO][06:48:39]: [Server #32532] New client with id #1 arrived.
[INFO][06:48:39]: [Server #32532] Client process #38864 registered.
[INFO][06:48:39]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][06:48:39]: [Server #32532] New client with id #7 arrived.
[INFO][06:48:39]: [Server #32532] Client process #13212 registered.
[INFO][06:48:39]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][06:48:39]: [Server #32532] A new client just connected.
[INFO][06:48:39]: [Server #32532] New client with id #4 arrived.
[INFO][06:48:39]: [Server #32532] Client process #7316 registered.
[INFO][06:48:39]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][06:48:39]: [Server #32532] A new client just connected.
[INFO][06:48:39]: [Server #32532] New client with id #8 arrived.
[INFO][06:48:39]: [Server #32532] Client process #4208 registered.
[INFO][06:48:39]: [Server #32532] Starting training.
[INFO][06:48:39]: [93m[1m
[Server #32532] Starting round 1/400.[0m
[INFO][06:48:39]: [Server #32532] Selected clients: [5, 19, 3, 9, 4, 8, 13, 15, 14, 11, 7, 16, 2, 17, 1, 10, 20, 18, 6, 12]
[INFO][06:48:39]: [Server #32532] Selecting client #5 for training.
[INFO][06:48:39]: [Server #32532] Sending the current model to client #5 (simulated).
[INFO][06:48:39]: [Server #32532] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][06:48:39]: [Server #32532] Selecting client #19 for training.
[INFO][06:48:39]: [Server #32532] Sending the current model to client #19 (simulated).
[INFO][06:48:39]: [Server #32532] Sending 18.75 MB of payload data to client #19 (simulated).
[INFO][06:48:39]: [Server #32532] Selecting client #3 for training.
[INFO][06:48:39]: [Server #32532] Sending the current model to client #3 (simulated).
[INFO][06:48:39]: [Server #32532] Sending 18.75 MB of payload data to client #3 (simulated).
[INFO][06:48:39]: [Server #32532] Selecting client #9 for training.
[INFO][06:48:39]: [Server #32532] Sending the current model to client #9 (simulated).
[INFO][06:48:39]: [Server #32532] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][06:48:39]: [Server #32532] Selecting client #4 for training.
[INFO][06:48:39]: [Server #32532] Sending the current model to client #4 (simulated).
[INFO][06:48:39]: [Server #32532] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][06:48:39]: [Server #32532] Selecting client #8 for training.
[INFO][06:48:39]: [Server #32532] Sending the current model to client #8 (simulated).
[INFO][06:48:39]: [Server #32532] Sending 18.75 MB of payload data to client #8 (simulated).
[INFO][06:48:39]: [Server #32532] Selecting client #13 for training.
[INFO][06:48:39]: [Server #32532] Sending the current model to client #13 (simulated).
[INFO][06:48:40]: [Server #32532] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][06:48:40]: [Server #32532] Selecting client #15 for training.
[INFO][06:48:40]: [Server #32532] Sending the current model to client #15 (simulated).
[INFO][06:48:40]: [Server #32532] Sending 18.75 MB of payload data to client #15 (simulated).
[INFO][06:48:40]: [Server #32532] Selecting client #14 for training.
[INFO][06:48:40]: [Server #32532] Sending the current model to client #14 (simulated).
[INFO][06:48:40]: [Server #32532] Sending 18.75 MB of payload data to client #14 (simulated).
[INFO][06:48:40]: [Server #32532] Selecting client #11 for training.
[INFO][06:48:40]: [Server #32532] Sending the current model to client #11 (simulated).
[INFO][06:48:40]: [Server #32532] Sending 18.75 MB of payload data to client #11 (simulated).
[INFO][06:48:40]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][06:53:38]: [Server #32532] Received 18.75 MB of payload data from client #19 (simulated).
[INFO][06:53:45]: [Server #32532] Received 18.75 MB of payload data from client #8 (simulated).
[INFO][06:53:45]: [Server #32532] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][06:53:46]: [Server #32532] Received 18.75 MB of payload data from client #3 (simulated).
[INFO][06:53:46]: [Server #32532] Received 18.75 MB of payload data from client #15 (simulated).
[INFO][06:53:48]: [Server #32532] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][06:53:48]: [Server #32532] Received 18.75 MB of payload data from client #14 (simulated).
[INFO][06:53:49]: [Server #32532] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][06:53:49]: [Server #32532] Received 18.75 MB of payload data from client #11 (simulated).
[INFO][06:53:49]: [Server #32532] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][06:53:49]: [Server #32532] Selecting client #7 for training.
[INFO][06:53:49]: [Server #32532] Sending the current model to client #7 (simulated).
[INFO][06:53:50]: [Server #32532] Sending 18.75 MB of payload data to client #7 (simulated).
[INFO][06:53:50]: [Server #32532] Selecting client #16 for training.
[INFO][06:53:50]: [Server #32532] Sending the current model to client #16 (simulated).
[INFO][06:53:50]: [Server #32532] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][06:53:50]: [Server #32532] Selecting client #2 for training.
[INFO][06:53:50]: [Server #32532] Sending the current model to client #2 (simulated).
[INFO][06:53:50]: [Server #32532] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][06:53:50]: [Server #32532] Selecting client #17 for training.
[INFO][06:53:50]: [Server #32532] Sending the current model to client #17 (simulated).
[INFO][06:53:50]: [Server #32532] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][06:53:50]: [Server #32532] Selecting client #1 for training.
[INFO][06:53:50]: [Server #32532] Sending the current model to client #1 (simulated).
[INFO][06:53:50]: [Server #32532] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][06:53:50]: [Server #32532] Selecting client #10 for training.
[INFO][06:53:50]: [Server #32532] Sending the current model to client #10 (simulated).
[INFO][06:53:50]: [Server #32532] Sending 18.75 MB of payload data to client #10 (simulated).
[INFO][06:53:50]: [Server #32532] Selecting client #20 for training.
[INFO][06:53:50]: [Server #32532] Sending the current model to client #20 (simulated).
[INFO][06:53:50]: [Server #32532] Sending 18.75 MB of payload data to client #20 (simulated).
[INFO][06:53:50]: [Server #32532] Selecting client #18 for training.
[INFO][06:53:50]: [Server #32532] Sending the current model to client #18 (simulated).
[INFO][06:53:51]: [Server #32532] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][06:53:51]: [Server #32532] Selecting client #6 for training.
[INFO][06:53:51]: [Server #32532] Sending the current model to client #6 (simulated).
[INFO][06:53:51]: [Server #32532] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][06:53:51]: [Server #32532] Selecting client #12 for training.
[INFO][06:53:51]: [Server #32532] Sending the current model to client #12 (simulated).
[INFO][06:53:51]: [Server #32532] Sending 18.75 MB of payload data to client #12 (simulated).
[INFO][06:54:39]: [Server #32532] An existing client just disconnected.
[WARNING][06:54:39]: [Server #32532] Client process #31256 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][06:54:39]: [93m[1m[Server #32532] Closing the server due to a failed client.[0m
[INFO][06:54:39]: [Server #32532] Training concluded.
[INFO][06:54:39]: [Server #32532] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][06:54:39]: [Server #32532] Closing the server.
[INFO][06:54:39]: Closing the connection to client #30764.
[INFO][06:54:39]: Closing the connection to client #35140.
[INFO][06:54:39]: Closing the connection to client #7336.
[INFO][06:54:39]: Closing the connection to client #21720.
[INFO][06:54:39]: Closing the connection to client #35260.
[INFO][06:54:39]: Closing the connection to client #38864.
[INFO][06:54:39]: Closing the connection to client #13212.
[INFO][06:54:39]: Closing the connection to client #7316.
[INFO][06:54:39]: Closing the connection to client #4208.
