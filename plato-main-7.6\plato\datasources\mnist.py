"""
The MNIST dataset from the torchvision package.
"""
import logging
import numpy as np
from torchvision import datasets, transforms
from torch.utils.data import Subset

from plato.config import Config
from plato.datasources import base


class DataSource(base.DataSource):
    """The MNIST dataset."""

    def __init__(self, **kwargs):
        super().__init__()
        _path = Config().params["data_path"]
        
        # 获取分区参数
        self.partition_size = kwargs.get("partition_size", 600)
        self.sampler = kwargs.get("sampler", "noniid")
        self.concentration = kwargs.get("concentration", 0.1)
        self.random_seed = kwargs.get("random_seed", 1)
        self.client_id = kwargs.get("client_id", 0)
        
        # 设置随机种子
        np.random.seed(self.random_seed)

        train_transform = (
            kwargs["train_transform"]
            if "train_transform" in kwargs
            else transforms.Compose(
                [transforms.ToTensor(), transforms.Normalize((0.1307,), (0.3081,))]
            )
        )

        test_transform = (
            kwargs["test_transform"]
            if "test_transform" in kwargs
            else transforms.Compose(
                [transforms.ToTensor(), transforms.Normalize((0.1307,), (0.3081,))]
            )
        )
        
        # 加载完整数据集
        self.full_trainset = datasets.MNIST(
            root=_path, train=True, download=True, transform=train_transform
        )
        self.testset = datasets.MNIST(
            root=_path, train=False, download=True, transform=test_transform
        )
        
        # 对训练集进行分区
        self.trainset = self._partition_dataset()

    def _partition_dataset(self):
        """对数据集进行分区"""
        if self.sampler == "iid":
            return self._iid_partition()
        elif self.sampler == "noniid":
            return self._noniid_partition()
        else:
            raise ValueError(f"不支持的采样器类型: {self.sampler}")

    def _iid_partition(self):
        """IID分区：随机打乱后均匀分配"""
        indices = list(range(len(self.full_trainset)))
        np.random.shuffle(indices)
        
        # 计算当前客户端的数据范围
        start_idx = self.client_id * self.partition_size
        end_idx = start_idx + self.partition_size
        
        # 确保不超出数据集大小
        if start_idx >= len(indices):
            raise ValueError(f"客户端ID {self.client_id} 超出范围")
        end_idx = min(end_idx, len(indices))
        
        # 创建子集
        client_indices = indices[start_idx:end_idx]
        return Subset(self.full_trainset, client_indices)

    def _noniid_partition(self):
        """Non-IID分区：按标签分布进行分区"""
        # 获取所有样本的标签
        labels = np.array(self.full_trainset.targets)
        num_classes = len(np.unique(labels))
        
        # 计算每个类别的样本数
        class_counts = np.bincount(labels)
        
        # 使用Dirichlet分布生成每个客户端的标签分布
        alpha = self.concentration
        label_distribution = np.random.dirichlet([alpha] * num_classes)
        
        # 计算每个客户端应该获得的每个类别的样本数
        samples_per_class = (label_distribution * self.partition_size).astype(int)
        
        # 为每个类别选择样本
        client_indices = []
        for class_idx in range(num_classes):
            # 获取当前类别的所有样本索引
            class_indices = np.where(labels == class_idx)[0]
            np.random.shuffle(class_indices)
            
            # 选择当前客户端应该获得的样本
            num_samples = samples_per_class[class_idx]
            if num_samples > 0:
                client_indices.extend(class_indices[:num_samples])
        
        # 如果样本数不足，从其他类别补充
        if len(client_indices) < self.partition_size:
            remaining_indices = list(set(range(len(labels))) - set(client_indices))
            np.random.shuffle(remaining_indices)
            client_indices.extend(remaining_indices[:self.partition_size - len(client_indices)])
        
        # 如果样本数过多，随机删除一些
        if len(client_indices) > self.partition_size:
            np.random.shuffle(client_indices)
            client_indices = client_indices[:self.partition_size]
        
        return Subset(self.full_trainset, client_indices)

    def num_train_examples(self):
        return len(self.trainset)

    def num_test_examples(self):
        return len(self.testset)
