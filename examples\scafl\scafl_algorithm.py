"""
SCAFL (Staleness-Controlled Asynchronous Federated Learning) 算法实现

这个文件实现了SCAFL论文中的核心算法逻辑：
1. 陈旧度感知的权重聚合
2. 虚拟队列管理
3. <PERSON><PERSON>punov优化决策
"""

import logging
import torch
from collections import OrderedDict
from plato.algorithms import fedavg
from plato.config import Config


class Algorithm(fedavg.Algorithm):
    """SCAFL算法类，继承自FedAvg"""
    
    def __init__(self, trainer):
        """初始化SCAFL算法
        
        Args:
            trainer: 训练器实例
        """
        super().__init__(trainer)
        
        # 从配置文件读取SCAFL参数
        config = Config()
        self.tau_max = getattr(config.server, 'tau_max', 10)  # 最大陈旧度阈值
        self.V = getattr(config.server, 'V', 10)  # Lyapunov权衡参数
        
        # SCAFL状态变量
        self.client_staleness = {}  # 记录每个客户端的陈旧度
        self.virtual_queues = {}    # 虚拟队列，用于Lyapunov优化
        
        logging.info(f"[SCAFL Algorithm] 初始化完成，tau_max={self.tau_max}, V={self.V}")
    
    def update_client_staleness(self, client_id, participated):
        """更新客户端的陈旧度
        
        Args:
            client_id: 客户端ID
            participated: 是否参与了本轮聚合
        """
        if client_id not in self.client_staleness:
            self.client_staleness[client_id] = 0
            
        if participated:
            # 参与聚合，陈旧度重置为0
            self.client_staleness[client_id] = 0
        else:
            # 未参与聚合，陈旧度+1
            self.client_staleness[client_id] += 1
            
        logging.debug(f"[SCAFL] 客户端{client_id}陈旧度更新为: {self.client_staleness[client_id]}")
    
    def update_virtual_queue(self, client_id, participated):
        """更新虚拟队列 - 这是Lyapunov优化的核心
        
        根据论文公式(8): Qk(t+1) = max{Qk(t) + (τk(t)+1)(1-βt_k) - τmax, 0}
        
        Args:
            client_id: 客户端ID  
            participated: 是否参与了本轮聚合
        """
        if client_id not in self.virtual_queues:
            self.virtual_queues[client_id] = 0.0
            
        current_queue = self.virtual_queues[client_id]
        staleness = self.client_staleness.get(client_id, 0)
        
        if participated:
            # βt_k = 1, (1-βt_k) = 0
            queue_increment = 0 - self.tau_max
        else:
            # βt_k = 0, (1-βt_k) = 1
            queue_increment = (staleness + 1) - self.tau_max
            
        # 更新队列长度，确保非负
        self.virtual_queues[client_id] = max(current_queue + queue_increment, 0.0)
        
        logging.debug(f"[SCAFL] 客户端{client_id}虚拟队列更新为: {self.virtual_queues[client_id]}")
    
    def compute_staleness_weight(self, staleness):
        """计算基于陈旧度的权重
        
        陈旧度越高，权重越小，这样可以减少过时梯度的影响
        
        Args:
            staleness: 客户端的陈旧度
            
        Returns:
            float: 权重值 (0.1 到 1.0 之间)
        """
        # 简单的反比例关系，你可以根据实验效果调整
        weight = max(0.1, 1.0 - staleness / (2 * self.tau_max))
        return weight
    
    async def aggregate_weights(self, baseline_weights, weights_received, **kwargs):
        """SCAFL的陈旧度感知权重聚合
        
        这是SCAFL的核心：根据每个客户端的陈旧度来分配聚合权重
        
        Args:
            baseline_weights: 当前全局模型权重
            weights_received: 接收到的客户端权重列表
            
        Returns:
            聚合后的权重
        """
        if not weights_received:
            logging.warning("[SCAFL] 没有接收到客户端权重")
            return baseline_weights
            
        logging.info(f"[SCAFL] 开始聚合{len(weights_received)}个客户端的权重")
        
        # 计算每个客户端的陈旧度权重
        staleness_weights = []
        for i, weight_data in enumerate(weights_received):
            # 获取客户端ID（如果权重数据中包含的话）
            client_id = weight_data.get('client_id', i) if isinstance(weight_data, dict) else i
            staleness = self.client_staleness.get(client_id, 0)
            
            # 计算陈旧度权重
            staleness_weight = self.compute_staleness_weight(staleness)
            staleness_weights.append(staleness_weight)
            
            logging.debug(f"[SCAFL] 客户端{client_id}: 陈旧度={staleness}, 权重={staleness_weight:.4f}")
        
        # 归一化权重
        total_weight = sum(staleness_weights)
        if total_weight > 0:
            normalized_weights = [w / total_weight for w in staleness_weights]
        else:
            # 如果总权重为0，使用均匀权重
            normalized_weights = [1.0 / len(weights_received)] * len(weights_received)
        
        # 执行加权聚合
        aggregated_weights = OrderedDict()
        
        # 获取权重的键（参数名）
        if isinstance(weights_received[0], dict) and 'weights' in weights_received[0]:
            # 如果权重数据是字典格式
            first_weights = weights_received[0]['weights']
        else:
            # 如果权重数据直接是权重
            first_weights = weights_received[0]
            
        for name in first_weights.keys():
            aggregated_weights[name] = torch.zeros_like(first_weights[name])
            
            for i, weight_data in enumerate(weights_received):
                # 提取实际的权重
                if isinstance(weight_data, dict) and 'weights' in weight_data:
                    client_weights = weight_data['weights']
                else:
                    client_weights = weight_data
                    
                # 加权累加
                aggregated_weights[name] += normalized_weights[i] * client_weights[name]
        
        logging.info("[SCAFL] 权重聚合完成")
        return aggregated_weights
