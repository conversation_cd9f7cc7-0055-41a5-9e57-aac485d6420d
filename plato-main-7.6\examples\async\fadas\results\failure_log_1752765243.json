{"overall": {"total_attempts": 24, "total_failures": 7, "failure_rate": 0.2916666666666667, "current_threshold": 0.5193098593162997, "window_size": 20, "avg_comm_time": 0.47132917327153623}, "clients": {"1": {"attempts": 4, "failures": 0, "failure_rate": 0.0, "last_comm_time": 0.3393020083538766, "last_success": "True"}, "2": {"attempts": 4, "failures": 1, "failure_rate": 0.25, "last_comm_time": 0.4297693863694814, "last_success": "True"}, "3": {"attempts": 4, "failures": 1, "failure_rate": 0.25, "last_comm_time": 0.4644916044497448, "last_success": "True"}, "4": {"attempts": 4, "failures": 1, "failure_rate": 0.25, "last_comm_time": 0.1, "last_success": "True"}, "5": {"attempts": 4, "failures": 2, "failure_rate": 0.5, "last_comm_time": 0.6023572695565378, "last_success": "False"}, "6": {"attempts": 4, "failures": 2, "failure_rate": 0.5, "last_comm_time": 0.1, "last_success": "True"}}, "config": {"base_communication_time": 0.5, "communication_noise_std": 0.5, "initial_threshold": 1.0, "threshold_range": [0.2, 3.0], "smoothing_alpha": 0.3, "window_size": 20, "enable_dynamic_threshold": true, "enable_logging": true}}