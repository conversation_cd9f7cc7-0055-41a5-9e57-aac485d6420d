[INFO][23:03:07]: 日志系统已初始化
[INFO][23:03:07]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250728_230307.log
[INFO][23:03:07]: 日志级别: INFO
[WARNING][23:03:07]: 无法获取系统信息: No module named 'psutil'
[INFO][23:03:07]: 🚀 ReFedScaFL 训练开始
[INFO][23:03:07]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][23:03:07]: 开始时间: 2025-07-28 23:03:07
[INFO][23:03:07]: [Client None] 基础初始化完成
[INFO][23:03:07]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][23:03:07]: 创建并缓存共享模型
[INFO][23:03:07]: [93m[1m[2684] Logging runtime results to: ././results/refedscafl_cifar10_resnet9/2684.csv.[0m
[INFO][23:03:07]: [Server #2684] Started training on 10 clients with 5 per round.
[INFO][23:03:07]: 服务器参数配置完成：
[INFO][23:03:07]: - 客户端数量: total=10, per_round=5
[INFO][23:03:07]: - 权重参数: success=0.8, distill=0.2
[INFO][23:03:07]: - SCAFL参数: V=1.0, tau_max=5
[INFO][23:03:07]: 从共享资源模型提取并缓存全局权重
[INFO][23:03:07]: [Server #2684] Configuring the server...
[INFO][23:03:07]: Training: 500 rounds or accuracy above 80.0%

[INFO][23:03:07]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][23:03:07]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][23:03:07]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][23:03:07]: [Trainer Init] 训练器初始化完成，参数：batch_size=64, learning_rate=0.1, epochs=3
[INFO][23:03:07]: Algorithm: fedavg
[INFO][23:03:07]: Data source: CIFAR10
[INFO][23:03:11]: Starting client #1's process.
[INFO][23:03:11]: Starting client #2's process.
[INFO][23:03:11]: Starting client #3's process.
[INFO][23:03:11]: Starting client #4's process.
[INFO][23:03:11]: Starting client #5's process.
[INFO][23:03:11]: Setting the random seed for selecting clients: 1
[INFO][23:03:11]: Starting a server at address 127.0.0.1 and port 8090.
[INFO][23:03:24]: [Server #2684] A new client just connected.
[INFO][23:03:24]: [Server #2684] New client with id #1 arrived.
[INFO][23:03:24]: [Server #2684] Client process #23276 registered.
[INFO][23:03:24]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][23:03:24]: [Server #2684] A new client just connected.
[INFO][23:03:24]: [Server #2684] New client with id #3 arrived.
[INFO][23:03:24]: [Server #2684] Client process #36944 registered.
[INFO][23:03:24]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][23:03:24]: [Server #2684] A new client just connected.
[INFO][23:03:24]: [Server #2684] A new client just connected.
[INFO][23:03:24]: [Server #2684] A new client just connected.
[INFO][23:03:24]: [Server #2684] New client with id #5 arrived.
[INFO][23:03:24]: [Server #2684] Client process #4804 registered.
[INFO][23:03:24]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][23:03:24]: [Server #2684] New client with id #2 arrived.
[INFO][23:03:24]: [Server #2684] Client process #6156 registered.
[INFO][23:03:24]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][23:03:24]: [Server #2684] New client with id #4 arrived.
[INFO][23:03:24]: [Server #2684] Client process #19204 registered.
[INFO][23:03:24]: [Server #2684] Starting training.
[INFO][23:03:24]: [93m[1m
[Server #2684] Starting round 1/500.[0m
[INFO][23:03:24]: [Server #2684] Selected clients: [3, 2, 5, 1, 4]
[INFO][23:03:24]: [Server #2684] Selecting client #3 for training.
[INFO][23:03:24]: [Server #2684] Sending the current model to client #3 (simulated).
[INFO][23:03:24]: [Server #2684] Sending 18.75 MB of payload data to client #3 (simulated).
[INFO][23:03:24]: [Server #2684] Selecting client #2 for training.
[INFO][23:03:24]: [Server #2684] Sending the current model to client #2 (simulated).
[INFO][23:03:24]: [Server #2684] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][23:03:24]: [Server #2684] Selecting client #5 for training.
[INFO][23:03:24]: [Server #2684] Sending the current model to client #5 (simulated).
[INFO][23:03:24]: [Server #2684] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][23:03:24]: [Server #2684] Selecting client #1 for training.
[INFO][23:03:24]: [Server #2684] Sending the current model to client #1 (simulated).
[INFO][23:03:24]: [Server #2684] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][23:03:24]: [Server #2684] Selecting client #4 for training.
[INFO][23:03:24]: [Server #2684] Sending the current model to client #4 (simulated).
[INFO][23:03:24]: [Server #2684] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][23:03:24]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][23:05:38]: [Server #2684] A new client just connected.
[INFO][23:05:38]: [Server #2684] A new client just connected.
[INFO][23:05:38]: [Server #2684] A new client just connected.
[INFO][23:05:38]: [Server #2684] A new client just connected.
[INFO][23:05:38]: [Server #2684] A new client just connected.
[INFO][23:05:38]: [Server #2684] New client with id #3 arrived.
[INFO][23:05:38]: [Server #2684] Client process #38864 registered.
[INFO][23:05:38]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][23:05:38]: [Server #2684] New client with id #5 arrived.
[INFO][23:05:38]: [Server #2684] Client process #13296 registered.
[INFO][23:05:38]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][23:05:38]: [Server #2684] New client with id #2 arrived.
[INFO][23:05:38]: [Server #2684] Client process #34872 registered.
[INFO][23:05:38]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][23:05:38]: [Server #2684] New client with id #1 arrived.
[INFO][23:05:38]: [Server #2684] Client process #35228 registered.
[INFO][23:05:38]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][23:05:38]: [Server #2684] New client with id #4 arrived.
[INFO][23:05:38]: [Server #2684] Client process #32212 registered.
[INFO][23:05:38]: 客户端4注册完成，已初始化ReFedScaFL状态
