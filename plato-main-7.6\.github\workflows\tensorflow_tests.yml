name: TensorFlow CI Tests

on:
    push:
        branches: [main]
    pull_request:
        branches: [main]
    # Allows you to run this workflow manually from the Actions tab
    workflow_dispatch:
        branches: [main]

jobs:
    TensorFlow-Tests:
        runs-on: ubuntu-20.04

        steps:
            - uses: actions/checkout@v3
              with:
                submodules: recursive

            # Runs a set of commands using the runners shell
            - name: Install dependencies
              shell: bash -l {0}
              run: |
                  sudo apt-get update
                  sudo apt install -y python3-pip
                  pip3 install -r requirements.txt --upgrade
                  pip3 install tensorflow tensorflow-datasets
                  pip3 uninstall protobuf
                  pip3 install protobuf==3.20.3

            - name: Training workloads
              shell: bash -l {0}
              run: |
                  ./run --config=configs/MNIST/fedavg_lenet5_tensorflow.yml
