from plato.processors import base

class MetaProcessor(base.Processor):
    """为数据添加训练元信息的处理器"""
    def process(self, data):
        data.meta.update({
            'client_id': self.client_id,
            'timestamp': time.time()
        })
        return data

class SCAFLMetaProcessor(base.Processor):
    """为客户端报告添加训练时间和轮次信息"""
    def process(self, data):
        data.report.training_time = time.time() - self.trainer.start_time
        data.report.round = self.server.current_round
        return data