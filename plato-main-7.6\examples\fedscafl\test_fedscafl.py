import logging
import os
import sys
import time

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fedscafl_test.log')
    ]
)

logger = logging.getLogger("FedSCAFL.Test")

def test_imports():
    """测试是否可以正确导入所需模块"""
    logger.info("测试模块导入...")
    try:
        import fedscafl_server
        import fedscafl_algorithm
        logger.info("✓ 模块导入成功")
        return True
    except ImportError as e:
        logger.error("✗ 模块导入失败: %s", str(e))
        return False

def test_algorithm():
    """测试算法类是否可以正确初始化"""
    logger.info("测试算法初始化...")
    try:
        import fedscafl_algorithm
        
        # 创建一个简单的Mock训练器
        class MockTrainer:
            def __init__(self):
                self.model = "mock_model"
                
            def __str__(self):
                return "MockTrainer"
        
        trainer = MockTrainer()
        algorithm = fedscafl_algorithm.Algorithm(trainer)
        logger.info("✓ 算法初始化成功")
        
        # 测试客户端ID提取
        cid = algorithm.get_cid("client_123")
        if cid == "123":
            logger.info("✓ 客户端ID提取正确: client_123 -> 123")
        else:
            logger.error("✗ 客户端ID提取错误: client_123 -> %s", cid)
        
        return True
    except Exception as e:
        logger.error("✗ 算法初始化失败: %s", str(e))
        return False

def test_server_init():
    """测试服务器是否可以正确初始化"""
    logger.info("测试服务器初始化...")
    try:
        import fedscafl_server
        import fedscafl_algorithm
        
        algorithm = fedscafl_algorithm.Algorithm
        server = fedscafl_server.Server(algorithm=algorithm)
        logger.info("✓ 服务器初始化成功")
        
        # 测试客户端ID提取
        cid = server.get_cid("client_456")
        if cid == "456":
            logger.info("✓ 服务器客户端ID提取正确: client_456 -> 456")
        else:
            logger.error("✗ 服务器客户端ID提取错误: client_456 -> %s", cid)
            
        return True
    except Exception as e:
        logger.error("✗ 服务器初始化失败: %s", str(e), exc_info=True)
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("====== 开始FedSCAFL系统测试 ======")
    
    tests = [
        ("导入测试", test_imports),
        ("算法测试", test_algorithm),
        ("服务器测试", test_server_init)
    ]
    
    results = []
    for name, test_func in tests:
        logger.info("\n------ 测试: %s ------", name)
        start_time = time.time()
        success = test_func()
        elapsed = time.time() - start_time
        results.append((name, success, elapsed))
        
    # 输出结果摘要
    logger.info("\n====== 测试结果摘要 ======")
    all_passed = True
    for name, success, elapsed in results:
        status = "通过" if success else "失败"
        logger.info("%s: %s (%.2f秒)", name, status, elapsed)
        all_passed = all_passed and success
        
    if all_passed:
        logger.info("\n全部测试通过！系统可以正常运行。")
        return 0
    else:
        logger.error("\n测试失败！请检查日志了解详细信息。")
        return 1

if __name__ == "__main__":
    sys.exit(run_all_tests()) 