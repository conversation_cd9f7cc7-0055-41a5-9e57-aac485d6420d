#!/usr/bin/env python3
"""
测试FedBuff配置文件是否正确
"""

import sys
import os
sys.path.append('../../../')

from plato.config import Config

def test_config():
    """测试配置文件"""
    print("🧪 测试FedBuff配置文件")
    print("=" * 50)
    
    config_file = "fedbuff_MNIST_original.yml"
    
    try:
        # 加载配置
        Config.load_config(config_file)
        
        # 检查关键配置
        print(f"✅ 配置文件加载成功: {config_file}")
        
        # 检查结果路径
        result_path = Config().params.get("result_path", "未设置")
        print(f"📁 结果路径: {result_path}")
        
        # 检查模型路径
        model_path = Config().server.get("model_path", "未设置")
        print(f"🏗️ 模型路径: {model_path}")
        
        # 检查端口
        port = Config().server.get("port", "未设置")
        print(f"🔌 服务器端口: {port}")
        
        # 检查客户端数量
        total_clients = Config().clients.get("total_clients", "未设置")
        print(f"👥 客户端总数: {total_clients}")
        
        # 检查训练轮数
        rounds = Config().trainer.get("rounds", "未设置")
        print(f"🔄 训练轮数: {rounds}")
        
        # 检查输出字段
        result_types = Config().params.get("result_types", "未设置")
        print(f"📊 输出字段: {result_types}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False

if __name__ == "__main__":
    success = test_config()
    if success:
        print(f"\n✅ 配置测试通过")
    else:
        print(f"\n❌ 配置测试失败")
