clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 10

    # The number of clients selected in each round
    per_round: 3

    # Should the clients compute test accuracy locally?
    do_test: true

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 5

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 3

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8007
    ping_timeout: 36000
    ping_interval: 36000

    # Should we simulate the wall-clock time on the server?
    simulate_wall_time: true

    # Should we operate in sychronous mode?
    synchronous: false

    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 2

    # staleness bound for async aggregation
    staleness_bound: 10

    # The staleness function
    staleness_weighting_function: polynomial

    # The staleness weighting exponent
    staleness_weighting_exponent: 0.5

    # The model path
    model_path: models/mnist/simple

    random_seed: 1

    # FedADS parameters
    beta1: 0.9
    beta2: 0.99
    tauc: 3
    eps: 0.00000001
    global_lr: 0.01

data:
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 600

    # IID or non-IID?
    sampler: noniid
    concentration: 0.1

    # The size of the testset on the server
    testset_size: 1000

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 15

    # The maximum number of clients running concurrently
    max_concurrency: 2

    # The target accuracy
    target_accuracy: 0.95

    # Number of epoches for local training in each communication round
    epochs: 2
    batch_size: 32
    optimizer: SGD

    # The machine learning model
    model_name: resnet_9

algorithm:
    # Aggregation algorithm
    type: fedavg
    lamda: 1.0

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001

results:
    result_path: results/mnist/simple

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy
