{"cells": [{"cell_type": "markdown", "metadata": {"id": "olul0mnbnLoL"}, "source": ["# Running Plato in Google's Colab Notebooks"]}, {"cell_type": "markdown", "metadata": {"id": "06Ua9IDYgnt6"}, "source": ["## 1. Preparation\n", "\n", "### Use Brave, Chrome or Microsoft Edge as your browser\n", "\n", "Since Colab is a product from Google, try to use a modern web browser based on the Chromium engine, such as Brave, Chrome or Microsoft Edge (released after February 2020)."]}, {"cell_type": "markdown", "metadata": {"id": "825aAsBKg0Sx"}, "source": ["### Activating GPU support\n", "\n", "If you need GPU support in your project, you may activate it in Google Colab by clicking on *Runtime > Change runtime type* in the dropdown menu and choosing *GPU* as the hardware accelerator. To check whether the GPU is available for computation, we import the deep learning framework [PyTorch](https://pytorch.org/):"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2RB_ZJrZmugO"}, "outputs": [], "source": ["import torch\n", "torch.cuda.is_available()"]}, {"cell_type": "markdown", "metadata": {"id": "SkkBTKHtuTVy"}, "source": ["If successful, the output of the cell above should print `True`.\n", "\n", "If you have subscribed to Colab Pro or Colab Pro+, choose *High-RAM* under *Runtime shape* after selecting *Runtime > Change runtime type* in the dropdown menu, before connecting to a hosted runtime. This will double the amount of RAM from around 12 GB to around 25 GB (for Colab Pro) or 54.8 GB (for Colab Pro+).\n", "\n", "### Mounting your Google Drive\n", "\n", "Since Google Colab removes all the files that you may have downloaded or created when you terminal a session, the best option is to use GitHub to store your code, and Google Drive to store your downloaded datasets and anything else needed by your Plato training sessions that you would normally store on your local filesystem.\n", "\n", "You can use the code below to mount your Google Drive, which may contain your downloaded datasets. When you run the code below, you will need to click a link and follow a process that takes a few seconds. When the process is complete, all of your Google Drive files will be available at `/content/drive/MyDrive` on your Colab instance."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "z5gW8LQivFHs"}, "outputs": [], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "markdown", "metadata": {"id": "2iKIiH7AbuYm"}, "source": ["### 2. <PERSON><PERSON><PERSON> <PERSON>\n", "\n", "<PERSON><PERSON>'s public git repository on GitHub onto your Colab instance:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "P175hLMavyOg"}, "outputs": [], "source": ["%cd /content\n", "!git clone https://github.com/TL-System/plato"]}, {"cell_type": "markdown", "metadata": {"id": "cTfvXkYC1j45"}, "source": ["Get into the `plato` directory:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "oW6q5XWi1v4i"}, "outputs": [], "source": ["!chmod -R ugo+rx /content/plato/run\n", "%cd /content/plato/"]}, {"cell_type": "markdown", "metadata": {"id": "kleim<PERSON>f"}, "source": ["Install Plato as a pip package:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "urh4nYGbbL_o"}, "outputs": [], "source": ["!pip install ."]}, {"cell_type": "markdown", "metadata": {"id": "DdjnhRWPdcRI"}, "source": ["In the future, if you find the remote git repository is updated, you can update your local git repository by running"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "R0OuwEG7Z3Xt"}, "outputs": [], "source": ["!git pull"]}, {"cell_type": "markdown", "metadata": {"id": "OtK_o5gGd0-S"}, "source": ["Then install <PERSON> as a pip package again:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "outputs": [], "source": ["!pip install ."]}, {"cell_type": "markdown", "metadata": {"id": "WiuCCtu7hFsR"}, "source": ["## 3. <PERSON>"]}, {"cell_type": "markdown", "metadata": {"id": "mlmSKKaSk6P3"}, "source": ["### Running <PERSON> in the Colab notebook\n", "\n", "To start a federated learning training workload, run `run` from <PERSON>'s home directory. For example:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ivm8dSigaV7Y"}, "outputs": [], "source": ["!./run -s 127.0.0.1:8000 -c ./configs/MNIST/fedavg_lenet5.yml"]}, {"cell_type": "markdown", "metadata": {"id": "Jv1jzv0Ynd17"}, "source": ["Here, `fedavg_lenet5.yml` is a sample configuration file that uses Federated Averaging as the federated learning algorithm, and LeNet5 as the model. Other configuration files under `plato/configs/` could also be used here.\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "G1RsvvL4z2Op"}, "source": ["### Running Plato in Visual Studio Code's Terminal\n", "\n", "It is strongly recommended and more convenient to run <PERSON> in a terminal, preferably in Visual Studio Code. To do this, refer to the tutorial in a companion notebook `colab_use_terminal.ipynb` in `examples/colab` in Plato."]}], "metadata": {"accelerator": "GPU", "colab": {"collapsed_sections": [], "name": "plato_colab.ipynb", "provenance": [{"file_id": "1boDurcQF5X9jq25-DsKDTus3h50NBn8h", "timestamp": 1641260422970}]}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}