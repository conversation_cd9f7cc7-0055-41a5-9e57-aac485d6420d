import logging
import os
import copy
import random
import sys
import time
import numpy as np
import torch
from datetime import datetime
import asyncio
from logging.handlers import RotatingFileHandler
from collections import OrderedDict
from plato.config import Config
from plato.algorithms import fedavg
from plato.models import registry as models
from plato.utils import csv_processor
from math import log2
from types import SimpleNamespace
import pandas as pd
from plato.models import lenet5
from functools import partial

# setup_logging函数已移除，避免重复定义
# 使用统一的日志配置，由主程序负责初始化
logger = logging.getLogger(__name__)

class Algorithm(fedavg.Algorithm):
    """基于过时度的加权聚合算法"""
    
    def __init__(self, trainer=None, server=None, *args, **kwargs):
        if trainer is None:
            raise ValueError("trainer cannot be None for Algorithm initialization.")
        
        self.trainer = trainer
        self.server = server
        
        self.client_id = None
        if hasattr(trainer, 'client_id'):
            self.client_id = trainer.client_id
            logging.info(f"[Algorithm] 从训练器获取客户端ID: {self.client_id}")
            
        super().__init__(trainer=trainer)
        
        if hasattr(trainer, 'client_id') and self.client_id != trainer.client_id:
            self.client_id = trainer.client_id
            logging.info(f"[Algorithm] 初始化后修正client_id: {self.client_id}")
            
        if hasattr(trainer, 'client_id') and trainer.client_id is None and self.client_id is not None:
            trainer.client_id = self.client_id
            logging.info(f"[Algorithm] 设置trainer的client_id: {self.client_id}")
            
        self.client_staleness = {}
        
        config = Config()
        self.tau_max = getattr(config.algorithm, 'tau_max', 6)
        self.lambda_param = getattr(config.algorithm, 'lambda', 0.5)
        
        logging.info("[Algorithm] 初始化完成")
        logging.info(f"[Algorithm] 参数配置：tau_max={self.tau_max}, lambda={self.lambda_param}")
        
    def set_client_id(self, client_id):
        """设置客户端ID"""
        self.client_id = client_id
        logging.info(f"[Algorithm] 设置客户端ID: {client_id}")
        
        if hasattr(self, 'trainer') and self.trainer is not None:
            self.trainer.client_id = client_id
            logging.info(f"[Algorithm] 同步更新trainer的client_id: {client_id}")
            
        return self
        
    def set_server_reference(self, server):
        """设置对服务器的引用"""
        self.server = server
        logging.info(f"[Algorithm] 已设置服务器引用 (客户端ID: {self.client_id})")
        return self

    def get_client_staleness(self, client_id):
        """从服务器获取客户端陈旧度"""
        if self.server and hasattr(self.server, 'client_staleness'):
            return self.server.client_staleness.get(client_id, 0)
        return self.client_staleness.get(client_id, 0)

    async def aggregate_weights(self, updates):
        """使用SC-AFL权重聚合模型"""
        if not updates:
            logging.warning("[Algorithm] 没有客户端更新可聚合")
            return None
            
        try:
            client_weights = {}
            total_samples = 0
            
            for update in updates:
                client_id = update.client_id

                # 修复陈旧度获取逻辑：支持多种数据结构
                staleness = 0
                if hasattr(update, 'staleness'):
                    staleness = update.staleness
                elif hasattr(update, 'payload') and isinstance(update.payload, dict):
                    staleness = update.payload.get('staleness', 0)
                elif hasattr(update, '__dict__') and 'staleness' in update.__dict__:
                    staleness = update.__dict__['staleness']
                else:
                    # 如果无法获取陈旧度，设为0（最新）
                    staleness = 0
                    logging.warning(f"无法获取客户端 {client_id} 的陈旧度信息，设为0")

                num_samples = getattr(update.report, 'num_samples', 0)
                total_samples += num_samples
                
                # 修复陈旧度因子计算：使用更合理的公式，避免权重过小
                # 原公式：max(0.1, 1.0 - staleness / (2 * tau_max)) 会导致高陈旧度时权重过小
                # 新公式：使用指数衰减，保证权重不会过小
                if staleness <= self.tau_max:
                    staleness_factor = max(0.3, 1.0 - staleness / (3 * self.tau_max))  # 提高最小权重到0.3
                else:
                    # 对于超过阈值的陈旧度，使用指数衰减但保持最小权重
                    staleness_factor = max(0.2, 0.5 * (0.8 ** (staleness - self.tau_max)))

                client_weights[client_id] = num_samples * staleness_factor

                logging.info(f"[Algorithm] 客户端 {client_id} - 样本数: {num_samples}, 陈旧度: {staleness}, "
                            f"陈旧度因子: {staleness_factor:.4f}, 原始权重: {client_weights[client_id]:.4f}")
            
            weight_sum = sum(client_weights.values())
            if weight_sum > 0:
                for client_id in client_weights:
                    client_weights[client_id] /= weight_sum
            else: # 如果权重和为0，使用均匀权重
                for client_id in client_weights:
                    client_weights[client_id] = 1.0 / len(updates)
                    
            for client_id, weight in client_weights.items():
                logging.info(f"[Algorithm] 客户端 {client_id} 最终权重: {weight:.4f}")
                
            aggregated_weights = {}
            # 确保updates[0].weights.keys()是有效的
            if not updates[0].weights:
                logging.error("[Algorithm] 第一个更新的权重字典为空，无法聚合")
                return None

            for key in updates[0].weights.keys():
                weighted_sum = None
                
                for update in updates:
                    client_id = update.client_id
                    weight = client_weights[client_id]
                    
                    if weighted_sum is None:
                        # 确保张量在同一设备上
                        weighted_sum = update.weights[key].to(self.trainer.device) * weight
                    else:
                        weighted_sum += update.weights[key].to(self.trainer.device) * weight
                
                aggregated_weights[key] = weighted_sum
                
            return aggregated_weights
            
        except Exception as e:
            logging.error(f"[Algorithm] 聚合权重时出错: {str(e)}")
            import traceback
            logging.error(f"[Algorithm] 异常堆栈: {traceback.format_exc()}")
            
            logging.info("[Algorithm] 回退到父类的聚合方法")
            return await super().aggregate_weights(updates)

    def extract_weights(self):
        """从模型中提取权重"""
        if not hasattr(self, 'trainer') or self.trainer is None:
            logging.error(f"[Algorithm] 算法的trainer为None，无法提取权重")
            return None
            
        if not hasattr(self.trainer, 'model') or self.trainer.model is None:
            logging.error(f"[Algorithm] 训练器的model为None，无法提取权重")
            return None
            
        try:
            weights = self.trainer.model.state_dict()
            return weights
        except Exception as e:
            logging.error(f"[Algorithm] 提取权重时出错: {str(e)}")
            import traceback
            logging.error(f"[Algorithm] 异常堆栈: {traceback.format_exc()}")
            return None
        
    def load_weights(self, weights):
        """加载权重到模型
        
        Args:
            weights: 模型权重
            
        Returns:
            bool: 加载是否成功
        """
        try:
            if not hasattr(self, 'trainer') or self.trainer is None:
                logging.error(f"[Algorithm] 算法的trainer为None，无法加载权重")
                try:
                    # 尝试创建一个训练器
                    from sc_afl_trainer import Trainer
                    self.trainer = Trainer()
                    logging.info(f"[Algorithm] 自动创建了训练器")
                except Exception as e:
                    logging.error(f"[Algorithm] 创建训练器失败: {str(e)}")
                    return False
            
            model = self.trainer.model
            if model is None:
                logging.error(f"[Algorithm] 训练器的model为None，无法加载权重")
                try:
                    # 尝试创建一个模型
                    from sc_afl_model import Model
                    config = Config()
                    in_channels = getattr(config.parameters.model, 'in_channels', 1)  # 默认为1通道
                    num_classes = getattr(config.parameters.model, 'num_classes', 10)
                    self.trainer.model = Model(in_channels=in_channels, num_classes=num_classes)
                    model = self.trainer.model
                    logging.info(f"[Algorithm] 自动创建了模型，输入通道数: {in_channels}, 类别数: {num_classes}")
                except Exception as e:
                    logging.error(f"[Algorithm] 创建模型失败: {str(e)}")
                    return False
            
            if weights is None or not isinstance(weights, dict) or len(weights) == 0:
                logging.error(f"[Algorithm] 权重无效，无法加载")
                return False
                
            # 检查模型的输入通道数和权重的输入通道数是否匹配
            model_in_channels = None
            weight_in_channels = None
            
            # 从模型结构获取输入通道数
            if hasattr(model, 'conv1') and hasattr(model.conv1, 'weight'):
                model_in_channels = model.conv1.weight.shape[1]
                logging.info(f"[Algorithm] 从模型结构检测到输入通道: {model_in_channels}")
            elif hasattr(model, 'in_channels'):
                model_in_channels = model.in_channels
                logging.info(f"[Algorithm] 从模型属性检测到输入通道: {model_in_channels}")
                
            # 从权重获取输入通道数
            if 'conv1.weight' in weights:
                weight_in_channels = weights['conv1.weight'].shape[1]
                logging.info(f"[Algorithm] 从权重检测到输入通道: {weight_in_channels}")
                
            if model_in_channels is not None and weight_in_channels is not None:
                logging.info(f"[Algorithm] 模型输入通道: {model_in_channels}, 待加载权重输入通道: {weight_in_channels}")
                
                # 如果通道数不匹配，需要处理
                if weight_in_channels != model_in_channels:
                    logging.warning(f"[Algorithm] 权重通道({weight_in_channels})与模型通道({model_in_channels})不匹配。进行通道转换...")
                    
                    # 创建一个新的模型，使用权重的通道数
                    try:
                        from sc_afl_model import Model
                        config = Config()
                        num_classes = getattr(config.parameters.model, 'num_classes', 10)
                        
                        # 使用权重的通道数创建新模型
                        new_model = Model(in_channels=weight_in_channels, num_classes=num_classes)
                        
                        # 如果训练器有设备属性，将模型移至相同设备
                        if hasattr(self.trainer, 'device'):
                            new_model = new_model.to(self.trainer.device)
                            
                        # 替换原模型
                        self.trainer.model = new_model
                        model = new_model
                        
                        logging.info(f"[Algorithm] 已创建新模型以匹配权重通道数: {weight_in_channels}")
                    except Exception as e:
                        logging.error(f"[Algorithm] 创建匹配通道数的新模型失败: {str(e)}")
                        # 如果创建新模型失败，尝试调整权重
                        adjusted_weights = {}
                        try:
                            for name, param in weights.items():
                                if name == 'conv1.weight':
                                    if model_in_channels == 1 and weight_in_channels == 3:
                                        # 对第一个卷积层的权重，取3个通道的平均值
                                        new_weight = param.mean(dim=1, keepdim=True)
                                        adjusted_weights[name] = new_weight
                                        logging.info(f"[Algorithm] 已将conv1.weight从形状{param.shape}调整为{new_weight.shape} (均值化)")
                                    elif model_in_channels == 3 and weight_in_channels == 1:
                                        # 如果模型是3通道，而权重是1通道，则将权重复制到3个通道
                                        new_weight = param.repeat(1, 3, 1, 1)
                                        adjusted_weights[name] = new_weight
                                        logging.info(f"[Algorithm] 已将conv1.weight从形状{param.shape}调整为{new_weight.shape} (通道复制)")
                                    else:
                                        logging.error(f"[Algorithm] 无法处理的通道转换: 从{weight_in_channels}到{model_in_channels}")
                                        return False
                                else:
                                    adjusted_weights[name] = param
                            
                            # 使用调整后的权重
                            weights = adjusted_weights
                        except Exception as adjust_err:
                            logging.error(f"[Algorithm] 调整权重通道失败: {str(adjust_err)}")
                            return False
            
            # 加载权重到模型
            try:
                # 确保权重在与模型相同的设备上
                if hasattr(self.trainer, 'device'):
                    device = self.trainer.device
                    weight_device = next(iter(weights.values())).device
                    
                    if device != weight_device:
                        logging.info(f"[Algorithm] 将权重从设备 {weight_device} 移至 {device}")
                        for key in weights:
                            weights[key] = weights[key].to(device)
                
                model.load_state_dict(weights)
                logging.info(f"[Algorithm] 成功加载权重到模型")
                return True
            except Exception as e:
                logging.error(f"[Algorithm] 加载权重到模型失败: {str(e)}")
                import traceback
                logging.error(f"[Algorithm] 异常堆栈: {traceback.format_exc()}")
                return False
                
        except Exception as e:
            logging.error(f"[Algorithm] 加载权重时出错: {str(e)}")
            import traceback
            logging.error(f"[Algorithm] 异常堆栈: {traceback.format_exc()}")
            return False
            
    def get_num_samples(self, update):
        """获取客户端的样本数量"""
        if update is None:
            return 0
            
        if hasattr(update, 'report') and hasattr(update.report, 'num_samples'):
            return update.report.num_samples
            
        return 0