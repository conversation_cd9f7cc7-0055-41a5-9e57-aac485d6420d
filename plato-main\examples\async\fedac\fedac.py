"""
A federated learning server using FedAC.

Reference:
<PERSON>1, <PERSON><PERSON>1*, <PERSON><PERSON>1, <PERSON><PERSON>2, <PERSON><PERSON> Du1, <PERSON><PERSON><PERSON>1
"Efficient Asynchronous Federated Learning with Prospective Momentum Aggregation and Fine-Grained Correction, "
im Proc. AAAI 2024
"""

import fedac_client
import fedac_server
import fedac_trainer
import os
import glob
import numpy as np
import matplotlib.pyplot as plt
from plato.config import Config

from fedac_callback import ScaffoldCallback


def analyze_client_distributions(save_dir='data_distribution'):
    """
    分析所有客户端的数据分布并生成摘要图表
    
    参数:
        save_dir: 客户端数据分布图和文本文件的保存目录
    """
    try:
        # 检查目录是否存在
        if not os.path.exists(save_dir):
            print(f"数据分布目录 {save_dir} 不存在。请确保先生成客户端数据分布。")
            return
            
        # 获取所有分布文本文件
        distribution_files = glob.glob(os.path.join(save_dir, 'client_*_distribution.txt'))
        
        if not distribution_files:
            print(f"在 {save_dir} 中未找到客户端分布文件。")
            return
            
        # 准备数据结构存储分析结果
        client_ids = []
        total_samples = []
        class_distributions = {}
        
        # 解析所有文本文件
        for file_path in distribution_files:
            with open(file_path, 'r') as f:
                lines = f.readlines()
                
                # 提取客户端ID
                client_id = int(lines[0].strip().split()[-1])
                client_ids.append(client_id)
                
                # 提取样本总数
                total = int(lines[1].strip().split()[-1])
                total_samples.append(total)
                
                # 提取类别分布
                for line in lines[3:]:
                    if line.strip():
                        parts = line.strip().split()
                        class_id = int(parts[1][:-1])  # 去掉冒号
                        count = int(parts[2])
                        
                        if class_id not in class_distributions:
                            class_distributions[class_id] = []
                            
                        while len(class_distributions[class_id]) < len(client_ids) - 1:
                            class_distributions[class_id].append(0)
                            
                        class_distributions[class_id].append(count)
        
        # 为每个类别确保有足够的数据点
        for class_id in class_distributions:
            while len(class_distributions[class_id]) < len(client_ids):
                class_distributions[class_id].append(0)
        
        # 创建摘要图表
        plt.figure(figsize=(15, 10))
        
        # 1. 样本数量分布
        plt.subplot(2, 2, 1)
        plt.hist(total_samples, bins=20, color='skyblue', edgecolor='black')
        plt.xlabel('样本数量')
        plt.ylabel('客户端数量')
        plt.title('客户端样本数量分布')
        
        # 2. 每个客户端的类别数
        class_counts_per_client = []
        for client_idx in range(len(client_ids)):
            count = sum(1 for class_id in class_distributions if class_distributions[class_id][client_idx] > 0)
            class_counts_per_client.append(count)
            
        plt.subplot(2, 2, 2)
        plt.hist(class_counts_per_client, bins=range(1, max(class_counts_per_client) + 2), 
                 color='lightgreen', edgecolor='black')
        plt.xlabel('类别数量')
        plt.ylabel('客户端数量')
        plt.title('客户端类别数量分布')
        
        # 3. 类别在客户端间的分布
        classes_present = [sum(1 for counts in class_distributions[class_id] if counts > 0) 
                          for class_id in sorted(class_distributions.keys())]
        
        plt.subplot(2, 2, 3)
        plt.bar(sorted(class_distributions.keys()), classes_present, color='coral')
        plt.xlabel('类别ID')
        plt.ylabel('包含该类别的客户端数量')
        plt.title('各类别在客户端间的分布情况')
        
        # 4. 热力图 - 类别在客户端间的分布
        plt.subplot(2, 2, 4)
        
        # 选择一个子集进行可视化，避免图表太拥挤
        max_clients_to_show = 10
        max_classes_to_show = 10
        
        sampled_clients = sorted(client_ids)[:max_clients_to_show]
        sampled_classes = sorted(class_distributions.keys())[:max_classes_to_show]
        
        # 准备热力图数据
        heatmap_data = np.zeros((len(sampled_classes), len(sampled_clients)))
        for i, class_id in enumerate(sampled_classes):
            for j, client_id in enumerate(sampled_clients):
                client_idx = client_ids.index(client_id)
                if client_idx < len(class_distributions[class_id]):
                    heatmap_data[i, j] = class_distributions[class_id][client_idx]
        
        plt.imshow(heatmap_data, cmap='YlGnBu', aspect='auto')
        plt.colorbar(label='样本数量')
        plt.xlabel('客户端ID')
        plt.ylabel('类别ID')
        plt.title(f'前{max_clients_to_show}个客户端的类别分布')
        plt.xticks(range(len(sampled_clients)), sampled_clients)
        plt.yticks(range(len(sampled_classes)), sampled_classes)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'client_distribution_summary.png'))
        plt.close()
        
        print(f"客户端数据分布摘要已保存到 {os.path.join(save_dir, 'client_distribution_summary.png')}")
        
        # 保存数据分布统计信息
        with open(os.path.join(save_dir, 'distribution_statistics.txt'), 'w') as f:
            f.write(f"总客户端数: {len(client_ids)}\n")
            f.write(f"平均样本数: {np.mean(total_samples):.2f}\n")
            f.write(f"样本数标准差: {np.std(total_samples):.2f}\n")
            f.write(f"最大样本数: {max(total_samples)}\n")
            f.write(f"最小样本数: {min(total_samples)}\n")
            f.write(f"平均类别数: {np.mean(class_counts_per_client):.2f}\n")
            f.write(f"类别数标准差: {np.std(class_counts_per_client):.2f}\n")
            
    except Exception as e:
        print(f"分析客户端分布时出错: {str(e)}")


def main():
    """A Plato federated learning training session using the FedAC algorithm."""
    trainer = fedac_trainer.Trainer
    client = fedac_client.Client(trainer=trainer, callbacks=[ScaffoldCallback])
    server = fedac_server.Server(trainer=trainer, )

    server.run(client)
    
    # 如果启用了数据分布可视化，生成摘要报告
    if hasattr(Config().data, 'visualize_distribution') and Config().data.visualize_distribution:
        print("正在生成客户端数据分布摘要...")
        analyze_client_distributions()


if __name__ == "__main__":
    main()