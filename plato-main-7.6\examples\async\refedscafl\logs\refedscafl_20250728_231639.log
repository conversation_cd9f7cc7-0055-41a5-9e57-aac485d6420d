[INFO][23:16:39]: 日志系统已初始化
[INFO][23:16:39]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250728_231639.log
[INFO][23:16:39]: 日志级别: INFO
[WARNING][23:16:39]: 无法获取系统信息: No module named 'psutil'
[INFO][23:16:39]: 🚀 ReFedScaFL 训练开始
[INFO][23:16:39]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][23:16:39]: 开始时间: 2025-07-28 23:16:39
[INFO][23:16:39]: [Client None] 基础初始化完成
[INFO][23:16:39]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][23:16:39]: 创建并缓存共享模型
[INFO][23:16:39]: [93m[1m[34688] Logging runtime results to: ././results/refedscafl_cifar10_resnet9/34688.csv.[0m
[INFO][23:16:39]: [Server #34688] Started training on 10 clients with 5 per round.
[INFO][23:16:39]: 服务器参数配置完成：
[INFO][23:16:39]: - 客户端数量: total=10, per_round=5
[INFO][23:16:39]: - 权重参数: success=0.8, distill=0.2
[INFO][23:16:39]: - SCAFL参数: V=1.0, tau_max=5
[INFO][23:16:39]: 从共享资源模型提取并缓存全局权重
[INFO][23:16:39]: [Server #34688] Configuring the server...
[INFO][23:16:39]: Training: 500 rounds or accuracy above 80.0%

[INFO][23:16:39]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][23:16:39]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][23:16:39]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][23:16:39]: [Trainer Init] 训练器初始化完成，参数：batch_size=64, learning_rate=0.1, epochs=3
[INFO][23:16:39]: Algorithm: fedavg
[INFO][23:16:39]: Data source: CIFAR10
[INFO][23:16:43]: Starting client #1's process.
[INFO][23:16:43]: Starting client #2's process.
[INFO][23:16:43]: Starting client #3's process.
[INFO][23:16:43]: Starting client #4's process.
[INFO][23:16:43]: Starting client #5's process.
[INFO][23:16:43]: Setting the random seed for selecting clients: 1
[INFO][23:16:43]: Starting a server at address 127.0.0.1 and port 8091.
[INFO][23:16:55]: [Server #34688] A new client just connected.
[INFO][23:16:55]: [Server #34688] New client with id #1 arrived.
[INFO][23:16:55]: [Server #34688] Client process #2084 registered.
[INFO][23:16:55]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][23:16:55]: [Server #34688] A new client just connected.
[INFO][23:16:55]: [Server #34688] A new client just connected.
[INFO][23:16:55]: [Server #34688] A new client just connected.
[INFO][23:16:55]: [Server #34688] A new client just connected.
[INFO][23:16:55]: [Server #34688] New client with id #3 arrived.
[INFO][23:16:55]: [Server #34688] Client process #23084 registered.
[INFO][23:16:55]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][23:16:55]: [Server #34688] New client with id #5 arrived.
[INFO][23:16:55]: [Server #34688] Client process #31852 registered.
[INFO][23:16:55]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][23:16:55]: [Server #34688] New client with id #2 arrived.
[INFO][23:16:55]: [Server #34688] Client process #3772 registered.
[INFO][23:16:55]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][23:16:55]: [Server #34688] New client with id #4 arrived.
[INFO][23:16:55]: [Server #34688] Client process #26608 registered.
[INFO][23:16:55]: [Server #34688] Starting training.
[INFO][23:16:55]: [93m[1m
[Server #34688] Starting round 1/500.[0m
[INFO][23:16:55]: [Server #34688] Selected clients: [3, 2, 5, 1, 4]
[INFO][23:16:55]: [Server #34688] Selecting client #3 for training.
[INFO][23:16:55]: [Server #34688] Sending the current model to client #3 (simulated).
[INFO][23:16:55]: [Server #34688] Sending 18.75 MB of payload data to client #3 (simulated).
[INFO][23:16:55]: [Server #34688] Selecting client #2 for training.
[INFO][23:16:55]: [Server #34688] Sending the current model to client #2 (simulated).
[INFO][23:16:55]: [Server #34688] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][23:16:55]: [Server #34688] Selecting client #5 for training.
[INFO][23:16:55]: [Server #34688] Sending the current model to client #5 (simulated).
[INFO][23:16:55]: [Server #34688] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][23:16:55]: [Server #34688] Selecting client #1 for training.
[INFO][23:16:55]: [Server #34688] Sending the current model to client #1 (simulated).
[INFO][23:16:56]: [Server #34688] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][23:16:56]: [Server #34688] Selecting client #4 for training.
[INFO][23:16:56]: [Server #34688] Sending the current model to client #4 (simulated).
[INFO][23:16:56]: [Server #34688] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][23:16:56]: 客户端4注册完成，已初始化ReFedScaFL状态
