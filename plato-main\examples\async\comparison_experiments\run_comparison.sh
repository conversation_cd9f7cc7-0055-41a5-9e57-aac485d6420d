#!/bin/bash

# ResNet9 CIFAR10 算法对比试验运行脚本
# 对比算法: FedBuff, FedAC, FADAS, FedAsync, SCAFL, ReFedSCAFL

echo "开始ResNet9 CIFAR10算法对比试验..."
echo "对比算法: FedBuff, FedAC, FADAS, FedAsync, SCAFL, ReFedSCAFL"
echo "=========================================="

# 创建结果目录
mkdir -p models/comparison
mkdir -p results/comparison

# 记录开始时间
start_time=$(date)
echo "开始时间: $start_time"

# 1. 运行FedBuff
echo "1. 运行FedBuff算法..."
cd ../fedbuff
python fedbuff.py -c ../comparison_experiments/fedbuff_resnet9_cifar10.yml
if [ $? -eq 0 ]; then
    echo "FedBuff 运行完成 ✓"
else
    echo "FedBuff 运行失败 ✗"
fi
echo ""

# 2. 运行FedAC
echo "2. 运行FedAC算法..."
cd ../fedac
python fedac.py -c ../comparison_experiments/fedac_resnet9_cifar10.yml
if [ $? -eq 0 ]; then
    echo "FedAC 运行完成 ✓"
else
    echo "FedAC 运行失败 ✗"
fi
echo ""

# 3. 运行FADAS
echo "3. 运行FADAS算法..."
cd ../fadas
python fadas.py -c ../comparison_experiments/fadas_resnet9_cifar10.yml
if [ $? -eq 0 ]; then
    echo "FADAS 运行完成 ✓"
else
    echo "FADAS 运行失败 ✗"
fi
echo ""

# 4. 运行FedAsync
echo "4. 运行FedAsync算法..."
cd ../fedasync
python fedasync.py -c ../comparison_experiments/fedasync_resnet9_cifar10.yml
if [ $? -eq 0 ]; then
    echo "FedAsync 运行完成 ✓"
else
    echo "FedAsync 运行失败 ✗"
fi
echo ""

# 5. 运行SCAFL
echo "5. 运行SCAFL算法..."
cd ../SC_AFL
python sc_afl.py -c ../comparison_experiments/scafl_resnet9_cifar10.yml
if [ $? -eq 0 ]; then
    echo "SCAFL 运行完成 ✓"
else
    echo "SCAFL 运行失败 ✗"
fi
echo ""

# 6. 运行ReFedSCAFL
echo "6. 运行ReFedSCAFL算法..."
cd ../refedscafl
python refedscafl.py -c ../comparison_experiments/refedscafl_resnet9_cifar10.yml
if [ $? -eq 0 ]; then
    echo "ReFedSCAFL 运行完成 ✓"
else
    echo "ReFedSCAFL 运行失败 ✗"
fi
echo ""

# 记录结束时间
end_time=$(date)
echo "=========================================="
echo "所有算法对比试验完成!"
echo "开始时间: $start_time"
echo "结束时间: $end_time"
echo ""
echo "结果文件位置:"
echo "- FedBuff:     results/comparison/fedbuff/"
echo "- FedAC:       results/comparison/fedac/"
echo "- FADAS:       results/comparison/fadas/"
echo "- FedAsync:    results/comparison/fedasync/"
echo "- SCAFL:       results/comparison/scafl/"
echo "- ReFedSCAFL:  results/comparison/refedscafl/"
echo ""
echo "模型文件位置:"
echo "- FedBuff:     models/comparison/fedbuff/"
echo "- FedAC:       models/comparison/fedac/"
echo "- FADAS:       models/comparison/fadas/"
echo "- FedAsync:    models/comparison/fedasync/"
echo "- SCAFL:       models/comparison/scafl/"
echo "- ReFedSCAFL:  models/comparison/refedscafl/"
