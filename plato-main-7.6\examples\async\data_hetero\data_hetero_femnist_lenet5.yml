clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 5

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8000

    disable_clients: false

data:
    # The training and testing dataset
    datasource: FEMNIST

    # Whether data needs to be reloaded in client simulation mode
    reload_data: true

    # Should concurrent client-side dataset download be allowed?
    concurrent_download: true

    # IID or non-IID?
    sampler: all_inclusive

    # The random seed for sampling data
    random_seed: 4

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 5

    # The maximum number of clients running concurrently
    max_concurrency: 1

    # The target accuracy
    target_accuracy: 0.94

    # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    model:
        num_classes: 62
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

results:
    types: accuracy, elapsed_time, round_time
