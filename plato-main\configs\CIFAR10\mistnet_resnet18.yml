clients:
    # Type
    type: mistnet

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

    outbound_processors:
        - feature_randomized_response
        - feature_unbatch

server:
    address: 127.0.0.1
    port: 8000

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition
    partition_size: 200

    # IID or non-IID?
    sampler: iid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 1

    # The maximum number of clients running concurrently
    max_concurrency: 3

    # The target accuracy
    target_accuracy: 0.95

    # The machine learning model
    model_name: resnet_18

    # Number of epoches for local training in each communication round
    epochs: 10
    batch_size: 16
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: mistnet
    epsilon: null

parameters:
    model:
        num_classes: 10
        cut_layer: layer1

    optimizer:
        lr: 0.01
        momentum: 0.5
        weight_decay: 0.0
