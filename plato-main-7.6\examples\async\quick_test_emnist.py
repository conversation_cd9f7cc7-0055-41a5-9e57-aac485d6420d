#!/usr/bin/env python3
"""
快速测试脚本 - 验证EMNIST配置文件
测试每个算法是否能正常启动和运行几轮
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../"))
sys.path.insert(0, project_root)

class QuickTester:
    def __init__(self):
        self.algorithms = {
            'fadas': {
                'path': 'examples/async/fadas',
                'config': 'fadas_EMNIST_lenet5_alpha0.1.yml',
                'script': 'fadas.py'
            },
            'fedac': {
                'path': 'examples/async/fedac',
                'config': 'fedac_EMNIST_lenet5_alpha0.1.yml',
                'script': 'fedac.py'
            },
            'fedbuff': {
                'path': 'examples/async/fedbuff',
                'config': 'fedbuff_EMNIST_lenet5_alpha0.1.yml',
                'script': 'fedbuff.py'
            },
            'refedscafl': {
                'path': 'examples/async/refedscafl',
                'config': 'refedscafl_EMNIST_lenet5_optimized.yml',
                'script': 'refedscafl.py'
            },
            'fedasync': {
                'path': 'examples/async/fedasync',
                'config': 'fedasync_EMNIST_lenet5_alpha0.1.yml',
                'script': 'fedasync.py'
            },
            'scafl': {
                'path': 'examples/async/SC_AFL',
                'config': 'sc_afl_emnist_lenet5_with_network.yml',
                'script': 'sc_afl.py'
            }
        }
    
    def modify_config_for_quick_test(self, config_path):
        """修改配置文件以进行快速测试"""
        print(f"📝 修改配置文件进行快速测试: {config_path}")
        
        # 读取原配置
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原配置
        backup_path = config_path.with_suffix('.yml.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 修改配置：减少轮次、客户端数量等
        lines = content.split('\n')
        modified_lines = []
        
        for line in lines:
            # 减少训练轮次
            if 'rounds:' in line and not line.strip().startswith('#'):
                modified_lines.append('    rounds: 3  # 快速测试：只运行3轮')
            # 减少客户端数量
            elif 'total_clients:' in line and not line.strip().startswith('#'):
                modified_lines.append('    total_clients: 5  # 快速测试：只用5个客户端')
            elif 'per_round:' in line and not line.strip().startswith('#'):
                modified_lines.append('    per_round: 3  # 快速测试：每轮3个客户端')
            # 减少并发数
            elif 'max_concurrency:' in line and not line.strip().startswith('#'):
                modified_lines.append('    max_concurrency: 2  # 快速测试：最大并发2')
            # 减少epoch数
            elif 'epochs:' in line and not line.strip().startswith('#'):
                modified_lines.append('    epochs: 1  # 快速测试：每轮1个epoch')
            else:
                modified_lines.append(line)
        
        # 写入修改后的配置
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(modified_lines))
        
        print(f"✅ 配置文件已修改，原文件备份为: {backup_path}")
        return backup_path
    
    def restore_config(self, config_path, backup_path):
        """恢复原配置文件"""
        if backup_path.exists():
            with open(backup_path, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(content)
            backup_path.unlink()  # 删除备份文件
            print(f"✅ 配置文件已恢复: {config_path}")
    
    def test_algorithm(self, alg_name, alg_info, timeout=300):
        """测试单个算法"""
        print(f"\n🧪 测试 {alg_name.upper()}...")
        
        alg_dir = Path(project_root) / alg_info['path']
        config_path = alg_dir / alg_info['config']
        
        # 检查配置文件是否存在
        if not config_path.exists():
            print(f"❌ 配置文件不存在: {config_path}")
            return False, f"配置文件不存在: {config_path}"
        
        # 修改配置文件进行快速测试
        backup_path = self.modify_config_for_quick_test(config_path)
        
        try:
            # 构建命令
            cmd = [sys.executable, alg_info['script'], '-c', alg_info['config']]
            
            print(f"📂 工作目录: {alg_dir}")
            print(f"🚀 执行命令: {' '.join(cmd)}")
            
            # 运行算法
            start_time = time.time()
            result = subprocess.run(
                cmd,
                cwd=alg_dir,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"✅ {alg_name.upper()} 测试成功 (耗时: {duration:.2f}秒)")
                return True, None
            else:
                error_msg = result.stderr[:500] if result.stderr else "未知错误"
                print(f"❌ {alg_name.upper()} 测试失败")
                print(f"错误信息: {error_msg}")
                return False, error_msg
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {alg_name.upper()} 测试超时 ({timeout}秒)")
            return False, "测试超时"
        except Exception as e:
            print(f"💥 {alg_name.upper()} 测试异常: {str(e)}")
            return False, str(e)
        finally:
            # 恢复原配置文件
            self.restore_config(config_path, backup_path)
    
    def run_quick_test(self, algorithms_to_test=None):
        """运行快速测试"""
        print("🚀 开始快速测试 - 验证EMNIST配置文件")

        # 首先验证指标统一性
        print("🔍 验证结果记录指标统一性...")
        try:
            result = subprocess.run([sys.executable, 'verify_config_metrics.py'],
                                  capture_output=True, text=True, timeout=30)
            if result.returncode != 0:
                print("❌ 配置文件指标验证失败")
                print(result.stderr)
                return False
            print("✅ 所有配置文件指标验证通过")
        except Exception as e:
            print(f"⚠️  指标验证过程出错: {e}")

        if algorithms_to_test is None:
            algorithms_to_test = list(self.algorithms.keys())

        print(f"将测试以下算法: {', '.join(algorithms_to_test)}")
        
        results = {}
        
        for alg_name in algorithms_to_test:
            if alg_name not in self.algorithms:
                print(f"⚠️  未知算法: {alg_name}")
                continue
            
            alg_info = self.algorithms[alg_name]
            success, error = self.test_algorithm(alg_name, alg_info)
            
            results[alg_name] = {
                'success': success,
                'error': error
            }
            
            # 测试间隔
            time.sleep(2)
        
        # 输出测试结果摘要
        print("\n📊 快速测试结果摘要:")
        print("=" * 50)
        
        success_count = 0
        for alg_name, result in results.items():
            status = "✅ 通过" if result['success'] else "❌ 失败"
            print(f"{alg_name.upper():12} | {status}")
            if result['success']:
                success_count += 1
            elif result['error']:
                print(f"             | 错误: {result['error'][:50]}...")
        
        print("=" * 50)
        print(f"总计: {success_count}/{len(results)} 个算法测试通过")
        
        if success_count == len(results):
            print("\n🎉 所有算法配置文件验证通过！可以运行完整对比实验。")
            return True
        else:
            print(f"\n⚠️  有 {len(results) - success_count} 个算法配置需要修复。")
            return False

def main():
    """主函数"""
    tester = QuickTester()
    
    # 可以选择测试特定算法
    # algorithms_to_test = ['fadas', 'fedac']  # 只测试指定算法
    algorithms_to_test = None  # 测试所有算法
    
    success = tester.run_quick_test(algorithms_to_test)
    
    if success:
        print("\n✅ 快速测试完成 - 所有配置验证通过")
        print("💡 现在可以运行: python run_emnist_comparison.py")
    else:
        print("\n❌ 快速测试发现问题 - 请修复配置文件")
        sys.exit(1)

if __name__ == "__main__":
    main()
