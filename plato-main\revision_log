2024.1.12
在fedbuff算法中增加利用本地数据测试全局模型准确性的功能
    - 修改\plato\clients\simple.py中的_train()函数，增加计算每个用户数据在全局模型上的性能，并在client.reports中以“global_accuracy”记录
    - 修改\plato\servers\fedavg.py中的_process_reports()函数，增加计算所有用户在全局模型上性能的平均值和方差；
    - 修改\plato\servers\fedavg.py中的get_accuracy_mean_std()函数，增加local变量指示性能计算;
    - 修改\plato\servers\fedavg.py中的get_logged_items()函数，记录新增属性值
    - 增加\plato\servers\base.py中的self.global_accuracy、self.global_accuracy_std属性记录global model on local data，
    增加self.local_accuracy、self.local_accuracy_std记录local model on local data
    - 在配置文件中增加“clients.do_global_test”字段进行功能控制;在配置文件中加入"data.tesst_simpler"字段控制用户测试数据集的划分(对应\plato\clients\simple.py中的config()函数)

2024.1.13
在fedasync/port算法中增加利用本地数据测试全局模型准确性的功能
    - 参考fedbuff的配置文件
