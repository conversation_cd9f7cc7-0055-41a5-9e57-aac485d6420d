clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 2

    # The number of clients selected in each round
    per_round: 2

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8000
    simulate_wall_time: false
    checkpoint_path: checkpoints/huggingface/fedavg
    model_path: models/huggingface/fedavg

data:
    # The training and testing dataset
    datasource: HuggingFace
    dataset_name: tiny_shakespeare

    # Number of samples in each partition
    partition_size: 1009

    # IID or non-IID?
    sampler: iid

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: HuggingFace

    # The maximum number of training rounds
    rounds: 5

    # The maximum number of clients running concurrently
    max_concurrency: 2

    # The target perplexity
    target_perplexity: 20

    # The machine learning model
    model_type: huggingface
    model_name: bert-base-uncased

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

results:
    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy
