clients:
    # Type
    type: mistnet

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    type: mistnet

    address: 127.0.0.1
    port: 8000

data:
    # The training and testing dataset
    datasource: MNIST

    # Where the dataset is located
    data_path: data

    # Number of samples in each partition
    partition_size: 20000

    # Fixed random seed
    random_seed: 1

    # IID, biased, or sharded?
    sampler: iid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 1

    # The target accuracy
    target_accuracy: 0.95

    # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 10
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: mistnet

    cut_layer: relu3
    epsilon: null

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0