clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 20

    # The number of clients selected in each round
    per_round: 5

    # *Should the clients compute test accuracy locally?(test local model on local dataset)
    do_test: true

    # *Should the clients compute test accuracy with global model?(test global model on local dataset)
    do_global_test: true

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 5

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 5

    # 网络波动模拟配置 - 与FedAC相同的恶劣网络环境
    network_simulation: true
    
    # 网络延迟配置 (毫秒) - 模拟移动网络和边缘设备
    network_delay:
        min_delay: 100      # 最小延迟100ms
        max_delay: 5000     # 最大延迟5秒
        distribution: exponential  # 指数分布模拟真实网络
        
    # 网络丢包率配置 - 高丢包率环境
    packet_loss:
        loss_rate: 0.15     # 25%丢包率
        burst_loss: true    # 突发丢包
        
    # 网络带宽限制 - 低带宽环境
    bandwidth_limit:
        upload_speed: 512   # 512KB/s上传
        download_speed: 1024 # 1MB/s下载

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8001
    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server? Useful if max_concurrency is specified
    simulate_wall_time: true

    # (fedbuff)
    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 3

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 5

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: true

    # The paths for storing temporary checkpoints and models
    checkpoint_path: examples/async/fedbuff/models/mnist_network_test_fedbuff/01
    model_path: examples/async/fedbuff/models/mnist_network_test_fedbuff/01

    random_seed: 1

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 300

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution(alpha)
    concentration: 0.1

    # The size of the testset on the server
    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

    # *get the local test sampler to obtain the test dataset
    testset_sampler: noniid
    
    # 是否可视化客户端数据分布
    visualize_distribution: false

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 10

    # The maximum number of clients running concurrently
    max_concurrency: 3

    # The target accuracy
    target_accuracy: 1

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD
    lr_scheduler: LambdaLR

    # The machine learning model
    model_name: lenet5

algorithm:
    # Aggregation algorithm
    type: fedavg
    lamda: 1.0

parameters:
    model:
        num_classes: 10

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001
    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    result_path: examples/async/fedbuff/results/mnist_network_test_fedbuff/01

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, network_success_rate, avg_communication_time
