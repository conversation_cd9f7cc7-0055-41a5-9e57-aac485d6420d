general:
    # The prefix of directories of dataset, models, checkpoints, and results
    base_path: ./cross_silo

clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 3

    # The number of clients selected in each round
    per_round: 3

    # Should the clients compute test accuracy locally?
    do_test: true

server:
    type: fedavg_cross_silo
    address: 127.0.0.1
    port: 8000

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 600

    # IID or non-IID?
    sampler: iid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 3

    # The maximum number of clients running concurrently
    max_concurrency: 1

    # The target accuracy
    target_accuracy: 0.95

    # The machine learning model
    model_name: lenet5

    # Number of epoches for local training in each communication round
    epochs: 2
    batch_size: 10
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

    # Cross-silo training
    cross_silo: true

    # The total number of silos (edge servers)
    total_silos: 2

    # The number of local aggregation rounds on edge servers before sending
    # aggreagted weights to the central server
    local_rounds: 4

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

results:
    # Write the following parameter(s) into a CSV
    types: round, accuracy, elapsed_time, round_time
