# 联邦学习算法对比实验指南

本指南介绍如何进行 ReFedScaFL、FedAC 和 SC_AFL 三种联邦学习算法的对比实验。

## 📋 实验配置

### 统一的实验参数

为了确保公平对比，所有算法使用相同的基础参数：

| 参数 | 值 | 说明 |
|------|----|----|
| 数据集 | CIFAR-10 | 图像分类数据集 |
| 模型 | ResNet-9 | 轻量级残差网络 |
| 客户端总数 | 100 | 模拟大规模联邦学习 |
| 每轮选择客户端数 | 20 | 异步聚合 |
| 本地训练轮数 | 5 | 每个客户端的本地epoch |
| 批次大小 | 50 | 训练批次大小 |
| 学习率 | 0.01 | 初始学习率 |
| 数据分布 | Non-IID | 非独立同分布，α=0.1 |
| 最大训练轮数 | 400 | 充分的训练轮数 |
| 陈旧度边界 | 5 | 异步训练的陈旧度限制 |

### 算法特定参数

#### ReFedScaFL
- **声誉机制**: success_weight=0.8, distill_weight=0.2
- **自适应权重**: 启用动态权重调整
- **知识蒸馏**: 温度=3.0, α=0.7
- **一致性评估**: 多维度模型一致性评估
- **SCAFL选择**: V=1.0, τ_max=5

#### FedAC
- **自适应优化**: β1=0.9, β2=0.99, ε=1e-8
- **全局学习率**: 0.01
- **动量优化**: 服务器端自适应优化

#### SC_AFL
- **缓冲池**: buffer_size=20
- **SCAFL参数**: V=1.0, τ_max=5
- **异步聚合**: 最小聚合客户端数=5

## 🚀 运行实验

### 1. 单个算法实验

```bash
# 运行 ReFedScaFL
python run_comparison_experiments.py --algorithm refedscafl

# 运行 FedAC
python run_comparison_experiments.py --algorithm fedac

# 运行 SC_AFL
python run_comparison_experiments.py --algorithm scafl
```

### 2. 批量对比实验

```bash
# 自动运行所有算法
python run_comparison_experiments.py --all
```

### 3. 手动运行（推荐用于调试）

```bash
# ReFedScaFL
cd refedscafl
python refedscafl.py -c refedscafl_comparison_config.yml

# FedAC
cd ../fedac
python fedac.py -c fedac_CIFAR10_resnet9_alpha0.1.yml

# SC_AFL
cd ../SC_AFL
python sc_afl.py -c sc_afl_cifar10_resnet9.yml
```

## 📊 结果分析

### 自动分析

```bash
# 生成对比图表和性能报告
python analyze_comparison_results.py

# 指定输出目录
python analyze_comparison_results.py --output-dir my_results
```

### 手动分析

实验结果保存在以下位置：

- **ReFedScaFL**: `results/refedscafl/comparison_cifar10_alpha01/`
- **FedAC**: `../fedac/results/cifar10/alpha01/`
- **SC_AFL**: `../SC_AFL/results/`

每个CSV文件包含以下指标：
- `round`: 训练轮次
- `elapsed_time`: 每轮耗时
- `accuracy`: 全局模型准确率
- `global_accuracy`: 全局测试准确率
- `avg_staleness`: 平均陈旧度（ReFedScaFL特有）
- `max_staleness`: 最大陈旧度（ReFedScaFL特有）

## 📈 评估指标

### 主要性能指标

1. **收敛速度**: 达到目标准确率所需的轮数和时间
2. **最终准确率**: 训练结束时的模型性能
3. **训练效率**: 每轮平均训练时间
4. **稳定性**: 准确率曲线的波动程度
5. **陈旧度处理**: 异步训练中的陈旧度管理效果

### 对比维度

- **准确率 vs 轮次**: 学习曲线对比
- **准确率 vs 时间**: 收敛效率对比
- **陈旧度分析**: 异步训练质量对比
- **资源消耗**: 计算和通信开销对比

## 🔧 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # Windows
   netstat -ano | findstr :8091
   taskkill /PID <PID> /F
   
   # Linux/Mac
   lsof -ti:8091 | xargs kill -9
   ```

2. **内存不足**
   - 减少 `max_concurrency` 参数
   - 降低 `batch_size`
   - 减少客户端数量进行小规模测试

3. **CUDA内存错误**
   ```bash
   export CUDA_VISIBLE_DEVICES=0  # 指定GPU
   # 或在代码中设置较小的batch_size
   ```

4. **依赖包问题**
   ```bash
   pip install torch torchvision matplotlib pandas numpy
   ```

### 调试模式

在配置文件中设置：
```yaml
trainer:
    rounds: 10  # 减少轮数用于快速测试
clients:
    total_clients: 10  # 减少客户端数
    per_round: 5
```

## 📝 实验记录

建议记录以下信息：

1. **实验环境**
   - 硬件配置（CPU、GPU、内存）
   - 软件版本（Python、PyTorch、CUDA）
   - 操作系统

2. **实验参数**
   - 具体的配置文件内容
   - 随机种子设置
   - 数据预处理方法

3. **实验结果**
   - 最终准确率
   - 收敛轮数
   - 总训练时间
   - 关键性能指标

4. **观察和分析**
   - 算法优缺点
   - 适用场景
   - 改进建议

## 🎯 预期结果

基于算法特性，预期的性能表现：

- **ReFedScaFL**: 在非IID数据上表现优异，具有更好的鲁棒性和收敛稳定性
- **FedAC**: 自适应优化带来更快的收敛速度
- **SC_AFL**: 在异步环境下具有良好的陈旧度处理能力

具体的性能对比需要通过实际实验来验证。
