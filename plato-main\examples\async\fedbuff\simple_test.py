#!/usr/bin/env python3
"""
简单的FedBuff测试
"""

import os
import csv
from datetime import datetime

def simple_csv_test():
    """简单的CSV测试"""
    print("🧪 简单CSV测试")
    print("=" * 30)
    
    try:
        # 创建测试目录
        test_dir = "results/test_output"
        os.makedirs(test_dir, exist_ok=True)
        print(f"✅ 创建目录: {test_dir}")
        
        # 创建测试CSV文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
        csv_file = f"{test_dir}/fedbuff_test_{timestamp}.csv"
        
        # 写入CSV
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            headers = ["round", "elapsed_time", "accuracy", "global_accuracy", "global_accuracy_std"]
            writer.writerow(headers)
            print(f"✅ 写入表头: {headers}")
            
            # 写入测试数据
            for i in range(1, 4):
                row = [i, i*10.5, 0.2+i*0.1, 0.18+i*0.09, 0.03]
                writer.writerow(row)
                print(f"✅ 写入第{i}行: {row}")
        
        print(f"✅ CSV文件创建成功: {csv_file}")
        
        # 验证文件
        if os.path.exists(csv_file):
            print(f"✅ 文件确实存在")
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📄 文件内容:")
                print(content)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """检查环境"""
    print(f"\n🔍 检查环境")
    print("-" * 30)
    
    print(f"📁 当前目录: {os.getcwd()}")
    print(f"🐍 Python版本: {os.sys.version}")
    print(f"🆔 进程ID: {os.getpid()}")
    
    # 检查目录权限
    try:
        test_file = "test_permission.txt"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print(f"✅ 目录有写权限")
    except Exception as e:
        print(f"❌ 目录权限问题: {e}")

def main():
    """主函数"""
    print("🚀 FedBuff简单测试")
    print("=" * 40)
    
    check_environment()
    success = simple_csv_test()
    
    if success:
        print(f"\n🎉 测试成功！")
        print("✅ 基本CSV功能正常")
        print("✅ 文件创建和写入正常")
    else:
        print(f"\n❌ 测试失败")

if __name__ == "__main__":
    main()
