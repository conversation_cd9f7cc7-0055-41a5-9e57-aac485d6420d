LICENSE
README.md
setup.py
plato/__init__.py
plato/client.py
plato/config.py
plato/algorithms/__init__.py
plato/algorithms/base.py
plato/algorithms/fedavg.py
plato/algorithms/fedavg_gan.py
plato/algorithms/fedavg_personalized.py
plato/algorithms/mistnet.py
plato/algorithms/registry.py
plato/algorithms/split_learning.py
plato/callbacks/__init__.py
plato/callbacks/client.py
plato/callbacks/handler.py
plato/callbacks/server.py
plato/callbacks/trainer.py
plato/clients/__init__.py
plato/clients/base.py
plato/clients/edge.py
plato/clients/fedavg_personalized.py
plato/clients/mistnet.py
plato/clients/registry.py
plato/clients/self_supervised_learning.py
plato/clients/simple.py
plato/clients/split_learning.py
plato/datasources/__init__.py
plato/datasources/base.py
plato/datasources/celeba.py
plato/datasources/cifar10.py
plato/datasources/cifar100.py
plato/datasources/cinic10.py
plato/datasources/coco.py
plato/datasources/emnist.py
plato/datasources/fashion_mnist.py
plato/datasources/feature.py
plato/datasources/feature_dataset.py
plato/datasources/femnist.py
plato/datasources/flickr30k_entities.py
plato/datasources/gym.py
plato/datasources/huggingface.py
plato/datasources/kinetics.py
plato/datasources/mnist.py
plato/datasources/multimodal_base.py
plato/datasources/pascal_voc.py
plato/datasources/purchase.py
plato/datasources/qoenflx.py
plato/datasources/referitgame.py
plato/datasources/registry.py
plato/datasources/self_supervised_learning.py
plato/datasources/shakespeare.py
plato/datasources/stl10.py
plato/datasources/texas.py
plato/datasources/tiny_imagenet.py
plato/datasources/yolov8.py
plato/datasources/datalib/__init__.py
plato/datasources/datalib/audio_extraction_tools.py
plato/datasources/datalib/data_utils.py
plato/datasources/datalib/flickr30kE_utils.py
plato/datasources/datalib/frames_extraction_tools.py
plato/datasources/datalib/modality_data_anntation_tools.py
plato/datasources/datalib/modality_extraction_base.py
plato/datasources/datalib/parse_datasets.py
plato/datasources/datalib/tiny_data_tools.py
plato/datasources/datalib/video_transform.py
plato/datasources/datalib/gym_utils/__init__.py
plato/datasources/datalib/gym_utils/gym_trim.py
plato/datasources/datalib/refer_utils/__init__.py
plato/datasources/datalib/refer_utils/referitgame_utils.py
plato/models/__init__.py
plato/models/cnn_encoder.py
plato/models/dcgan.py
plato/models/general_multilayer.py
plato/models/huggingface.py
plato/models/lenet5.py
plato/models/lstm.py
plato/models/multilayer.py
plato/models/registry.py
plato/models/resnet.py
plato/models/torch_hub.py
plato/models/vgg.py
plato/models/vit.py
plato/models/yolov8.py
plato/models/multimodal/__init__.py
plato/models/multimodal/base_net.py
plato/models/multimodal/blending.py
plato/models/multimodal/fc_net.py
plato/models/multimodal/fusion_net.py
plato/models/multimodal/multimodal_module.py
plato/processors/__init__.py
plato/processors/base.py
plato/processors/compress.py
plato/processors/decompress.py
plato/processors/feature.py
plato/processors/feature_additive_noise.py
plato/processors/feature_dequantize.py
plato/processors/feature_gaussian.py
plato/processors/feature_laplace.py
plato/processors/feature_quantize.py
plato/processors/feature_randomized_response.py
plato/processors/feature_unbatch.py
plato/processors/inbound_feature_tensors.py
plato/processors/model.py
plato/processors/model_compress.py
plato/processors/model_decompress.py
plato/processors/model_decrypt.py
plato/processors/model_deepcopy.py
plato/processors/model_dequantize.py
plato/processors/model_dequantize_qsgd.py
plato/processors/model_encrypt.py
plato/processors/model_quantize.py
plato/processors/model_quantize_qsgd.py
plato/processors/model_randomized_response.py
plato/processors/outbound_feature_ndarrays.py
plato/processors/pipeline.py
plato/processors/registry.py
plato/processors/structured_pruning.py
plato/processors/unstructured_pruning.py
plato/samplers/__init__.py
plato/samplers/all_inclusive.py
plato/samplers/base.py
plato/samplers/dirichlet.py
plato/samplers/distribution_noniid.py
plato/samplers/iid.py
plato/samplers/label_quantity_noniid.py
plato/samplers/mixed.py
plato/samplers/mixed_label_quantity_noniid.py
plato/samplers/modality_iid.py
plato/samplers/modality_quantity_noniid.py
plato/samplers/orthogonal.py
plato/samplers/registry.py
plato/samplers/sample_quantity_noniid.py
plato/samplers/sampler_utils.py
plato/servers/__init__.py
plato/servers/base.py
plato/servers/fedavg.py
plato/servers/fedavg_cs.py
plato/servers/fedavg_gan.py
plato/servers/fedavg_he.py
plato/servers/fedavg_personalized.py
plato/servers/mistnet.py
plato/servers/registry.py
plato/servers/split_learning.py
plato/trainers/__init__.py
plato/trainers/base.py
plato/trainers/basic.py
plato/trainers/diff_privacy.py
plato/trainers/gan.py
plato/trainers/huggingface.py
plato/trainers/loss_criterion.py
plato/trainers/lr_schedulers.py
plato/trainers/optimizers.py
plato/trainers/pascal_voc.py
plato/trainers/registry.py
plato/trainers/self_supervised_learning.py
plato/trainers/split_learning.py
plato/trainers/tracking.py
plato/trainers/yolov8.py
plato/trainers/mindspore/__init__.py
plato/trainers/mindspore/basic.py
plato/utils/__init__.py
plato/utils/count_parameters.py
plato/utils/csv_processor.py
plato/utils/data_loaders.py
plato/utils/decorators.py
plato/utils/fonts.py
plato/utils/homo_enc.py
plato/utils/rl_env.py
plato/utils/s3.py
plato/utils/trainer_utils.py
plato/utils/unary_encoding.py
plato/utils/lib_mia/__init__.py
plato/utils/lib_mia/mia.py
plato/utils/lib_mia/mia_client.py
plato/utils/lib_mia/mia_server.py
plato/utils/reinforcement_learning/__init__.py
plato/utils/reinforcement_learning/rl_agent.py
plato/utils/reinforcement_learning/rl_server.py
plato/utils/reinforcement_learning/policies/__init__.py
plato/utils/reinforcement_learning/policies/base.py
plato/utils/reinforcement_learning/policies/ddpg.py
plato/utils/reinforcement_learning/policies/registry.py
plato/utils/reinforcement_learning/policies/sac.py
plato/utils/reinforcement_learning/policies/td3.py
plato_learn.egg-info/PKG-INFO
plato_learn.egg-info/SOURCES.txt
plato_learn.egg-info/dependency_links.txt
plato_learn.egg-info/requires.txt
plato_learn.egg-info/top_level.txt