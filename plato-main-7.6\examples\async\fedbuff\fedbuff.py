"""
A federated learning training session using FedBuff.

Reference:

<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al., "Federated Learning with Buffered Asynchronous Aggregation,
" in Proc. International Conference on Artificial Intelligence and Statistics (AISTATS 2022).

https://proceedings.mlr.press/v151/nguyen22b/nguyen22b.pdf
"""

import os
import sys
import argparse

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../../"))
sys.path.insert(0, project_root)

import fedbuff_server

from Origin_trainer import Origin_trainer
from Origin_client import Origin_client
from Origin_server import Origin_server

def main():
    """A Plato federated learning training session using FedBuff."""
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='FedBuff联邦学习训练')
    parser.add_argument('-c', '--config', type=str, default='fedbuff_MNIST_lenet5.yml', 
                        help='配置文件路径 (默认: fedbuff_MNIST_lenet5.yml)')
    args = parser.parse_args()
    
    # 设置配置文件路径
    config_file = args.config
    if not os.path.exists(config_file):
        print(f"错误：配置文件不存在: {config_file}")
        return
    
    # 设置环境变量，使配置文件路径可被访问
    os.environ['config_file'] = os.path.abspath(config_file)
    print(f"使用配置文件: {config_file}")
    
    # 确保在导入plato.config之前设置环境变量
    # 将当前目录的父目录添加到sys.path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    if parent_dir not in sys.path:
        sys.path.append(parent_dir)
    
    # 导入Config以确认配置已加载
    from plato.config import Config
    try:
        config = Config()
        print(f"配置加载成功: {config.__dict__}")
    except Exception as e:
        print(f"配置加载失败: {e}")
        return
    
    trainer = Origin_trainer
    client = Origin_client(trainer=trainer)
    server = fedbuff_server.Server()  # 使用我们修改过的服务器类
    server.run(client)


if __name__ == "__main__":
    main()
