# rgb model settings
rgb_model:
  type: Recognizer3D

  backbone:
    type: ResNet2Plus1d
    depth: 50
    pretrained: null
    pretrained2d: False
    norm_eval: False

    conv_cfg:
      type: Conv2plus1d

    # norm_cfg:
      # type: BN
      # requires_grad: True
      # eps: 1e-3

    conv1_kernel:
      - 3
      - 7
      - 7

    conv1_stride_t: 1
    pool1_stride_t: 1

    inflate:
      - 1
      - 1
      - 1
      - 1

    spatial_strides:
      - 1
      - 2
      - 2
      - 2

    temporal_strides:
      - 1
      - 2
      - 2
      - 2

    zero_init_residual: False

  cls_head:
    type: I3DHead
    num_classes: 400
    in_channels: 2048
    spatial_type: avg
    dropout_ratio: 0.5
    init_std: 0.01

  # model training and testing settings
  train_cfg: null
  test_cfg:
    average_clips: prob

# optical flow model settings
flow_model:
  type: Recognizer3D

  backbone: 
    type: ResNet2Plus1d
    depth: 50
    pretrained: null
    pretrained2d: False
    norm_eval: False

    conv_cfg:
      type: Conv2plus1d

    # norm_cfg:
      # type: BN
      # requires_grad: True
      # eps: 1e-3

    conv1_kernel:
      - 3
      - 7
      - 7

    conv1_stride_t: 1
    pool1_stride_t: 1

    inflate:
      - 1
      - 1
      - 1
      - 1

    spatial_strides:
      - 1
      - 2
      - 2
      - 2

    temporal_strides:
      - 1
      - 2
      - 2
      - 2

    zero_init_residual: False

  cls_head:
    type: I3DHead
    num_classes: 400
    in_channels: 2048
    spatial_type: avg
    dropout_ratio: 0.5
    init_std: 0.01
  # model training and testing settings
  train_cfg: null
  test_cfg:
    average_clips: prob

# audio model settings
audio_model:
  type: AudioRecognizer

  backbone:
    type: ResNet
    depth: 50
    in_channels: 1
    norm_eval: False

  cls_head:
    type: AudioTSNHead
    num_classes: 400
    in_channels: 2048
    dropout_ratio: 0.5
    init_std: 0.01
  # model training and testing settings
  train_cfg: null
  test_cfg:
    average_clips: prob

# define the output dims of the modality features
fuse_model:
  type: FullyConnectedHead
  num_classes: 400
  in_channels: 6144

  hidden_layer_size:
    - 512

  dropout_ratio: 0.5

  loss_cls:
    type: CrossEntropyLoss
    loss_weight: 1.0
