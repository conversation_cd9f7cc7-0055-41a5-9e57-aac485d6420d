import logging
import math
import os
import random
import sys
import time
import threading
import traceback
import numpy as np
import torch
from datetime import datetime
import asyncio
from logging.handlers import RotatingFileHandler
import csv
from collections import OrderedDict
from types import SimpleNamespace

from plato.config import Config
from plato.servers import fedavg
from plato.models import registry as models
from plato.datasources import registry as datasources_registry
from plato.utils import csv_processor
from sc_afl_client import Client
from sc_afl_algorithm import Algorithm
from sc_afl_trainer import Trainer

# 配置日志
def setup_logging():
    """配置日志系统 - 每次启动创建新的日志文件"""
    try:
        # 获取根日志记录器
        root_logger = logging.getLogger()

        # 强制清除所有现有的处理器，确保创建新的日志文件
        for handler in root_logger.handlers[:]:
            try:
                handler.close()  # 关闭文件句柄
            except:
                pass
            root_logger.removeHandler(handler)

        # 重置日志系统
        try:
            logging.shutdown()
        except:
            pass

        # 创建日志目录 - 固定使用指定的logs目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        log_dir = os.path.join(current_dir, 'logs')
        os.makedirs(log_dir, exist_ok=True)

        # 生成唯一的日志文件名 - 包含更精确的时间戳和进程ID
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pid = os.getpid()
        log_file = os.path.join(log_dir, f'sc_afl_server_{timestamp}_{pid}.log')

        # 确保日志文件名唯一 - 如果文件已存在，添加序号
        counter = 1
        original_log_file = log_file
        while os.path.exists(log_file):
            base_name = original_log_file.replace('.log', '')
            log_file = f"{base_name}_{counter}.log"
            counter += 1
    
        # 使用basicConfig强制重新配置日志系统
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, mode='w', encoding='utf-8'),
                logging.StreamHandler()
            ],
            force=True  # 强制重新配置，覆盖现有配置
        )

        # 设置控制台日志级别为INFO
        for handler in logging.getLogger().handlers:
            if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                handler.setLevel(logging.INFO)
                break

        # 记录日志配置信息
        logging.info(f"🚀 SC-AFL服务器启动 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logging.info(f"✅ 新日志文件已创建")
        logging.info(f"📁 日志目录: {log_dir}")
        logging.info(f"📄 日志文件: {log_file}")
        logging.info(f"🔧 日志级别: DEBUG (文件), INFO (控制台)")
        logging.info(f"📊 文件模式: 新建模式 (每次启动创建新文件)")
        logging.info(f"🆔 进程ID: {pid}")

        # 验证日志文件是否正确创建
        try:
            if os.path.exists(log_file):
                file_size = os.path.getsize(log_file)
                logging.info(f"✅ 日志文件创建成功，当前大小: {file_size} 字节")
            else:
                logging.error(f"❌ 日志文件创建失败: 文件不存在")
        except Exception as e:
            logging.error(f"❌ 日志文件验证失败: {e}")

        return logging.getLogger()

    except Exception as e:
        # 如果日志配置失败，至少确保有基本的日志输出
        print(f"❌ 日志配置失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

        # 返回默认的日志记录器
        return logging.getLogger()

# 🛡️ 增强的错误处理函数
def log_exception(context: str, exception: Exception, client_id=None, round_num=None):
    """🛡️ 增强的异常日志记录函数

    Args:
        context: 异常发生的上下文描述
        exception: 异常对象
        client_id: 客户端ID（可选）
        round_num: 轮次编号（可选）
    """
    # 构建详细的上下文信息
    context_info = []
    if client_id is not None:
        context_info.append(f"客户端={client_id}")
    if round_num is not None:
        context_info.append(f"轮次={round_num}")

    context_str = f"[{', '.join(context_info)}] " if context_info else ""

    logging.error(f"❌ {context_str}{context}: {str(exception)}")
    logging.error(f"📋 异常堆栈: {traceback.format_exc()}")

    # 如果是关键异常，添加额外信息
    if isinstance(exception, (MemoryError, RuntimeError)):
        logging.error(f"🚨 检测到关键异常类型: {type(exception).__name__}")

        # 如果是CUDA相关错误，尝试释放显存
        if "CUDA" in str(exception) or "out of memory" in str(exception).lower():
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    logging.info("🧹 已尝试释放CUDA显存")
            except:
                pass

# 统一的动态模型创建函数
def create_dynamic_model(context: str = ""):
    """统一的动态模型创建函数"""
    try:
        from dynamic_loader import DynamicModelLoader, ConfigurationManager
        config = Config()
        dataset_name, num_classes, in_channels = ConfigurationManager.get_dataset_info_from_config(config)
        model_name = ConfigurationManager.get_model_info_from_config(config)
        model = DynamicModelLoader.create_model(model_name, num_classes, in_channels)
        logging.info(f"{context} 动态创建模型 {model_name}，输入通道数: {in_channels}, 类别数: {num_classes}")
        return model
    except Exception as e:
        logging.warning(f"{context} 动态模型创建失败: {e}，回退到LeNet5")
        try:
            from plato.models import lenet5
            config = Config()
            in_channels = getattr(config.parameters.model, 'in_channels', 1)
            num_classes = getattr(config.parameters.model, 'num_classes', 10)
            model = lenet5.Model(num_classes=num_classes, in_channels=in_channels)
            logging.info(f"{context} 回退创建LeNet5模型，输入通道数: {in_channels}, 类别数: {num_classes}")
            return model
        except Exception as fallback_e:
            log_exception(f"{context} 回退模型创建也失败", fallback_e)
            raise

# 日志将在Server初始化时设置

# 导入ClientIDManager
from sc_afl_client import ClientIDManager

class Server(fedavg.Server):
    """
    自定义的SCAFL联邦学习服务器类，继承自FedAvg服务器。
    实现了基于SCAFL（Staleness-aware Client-Adaptive Federated Learning）的联邦学习算法。
    主要特点：
    1. 考虑客户端陈旧度（staleness）设置虚拟队列，估计客户端的通信时间，并计算延迟估计值
    2. 自适应客户端选择，根据贪心策略选择客户端，并设置缓冲池大小
    3. 支持知识蒸馏补偿
    4. 动态聚合策略，根据缓冲池大小和客户端的陈旧度设置权重
    """
    def __init__(self, model=None, algorithm=None, trainer=None):
        """初始化服务器

        Args:
            model: 模型类或实例
            algorithm: 算法实例
            trainer: 训练器实例
        """
        # 首先设置日志系统 - 每次服务器初始化都创建新的日志文件
        setup_logging()

        logging.info("🚀 SC-AFL服务器初始化开始...")
        logging.info(f"📅 初始化时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 确保模块在方法内部可用
        # 这些导入放在这里是为了避免 UnboundLocalError，因为类级别的导入有时会被局部作用域覆盖
        # 使用已经导入的Model
        from plato.config import Config

        # 虚拟时间记录系统 - 类似FADAS的时间记录方式
        self.virtual_time = 0.0  # 虚拟时间戳
        self.virtual_time_step = 1.0  # 每轮虚拟时间增量
        self.client_virtual_times = {}  # 客户端虚拟时间记录
        self.virtual_time_log = []  # 虚拟时间日志
        
        # 从配置中获取客户端总数和每轮选择的客户端数量
        config = Config()
        self.total_clients = getattr(config.clients, 'total_clients')
        # 优先从server配置中读取max_aggregation_clients，如果没有则从clients配置中读取，最后使用默认值
        self.max_aggregation_clients = getattr(config.server, 'max_aggregation_clients',
                                               getattr(config.clients, 'max_aggregation_clients', 8))  # |M_max|，每轮最多聚合的客户端数量
        self.total_rounds = getattr(config.trainer, 'rounds', 50)  # 总训练轮数
        
        # 使用动态加载器创建模型
        if model is None:
            try:
                from dynamic_loader import DynamicModelLoader, ConfigurationManager

                # 获取配置信息
                dataset_name, num_classes, in_channels = ConfigurationManager.get_dataset_info_from_config(config)
                model_name = ConfigurationManager.get_model_info_from_config(config)

                # 自动配置参数
                ConfigurationManager.auto_configure_from_dataset(config, dataset_name)

                # 动态创建模型
                self.model = DynamicModelLoader.create_model(
                    model_name, num_classes=num_classes, in_channels=in_channels
                )

                logging.info(f"Server: 动态创建模型 {model_name}，数据集: {dataset_name}, 输入通道数: {in_channels}, 类别数: {num_classes}")

            except Exception as e:
                logging.error(f"动态模型创建失败: {e}")
                # 回退到默认模型
                from plato.models import lenet5
                self.model = lenet5.Model(num_classes=10, in_channels=1)
                logging.warning("回退到默认LeNet5模型")

        elif isinstance(model, type): # 如果传入的是类，则实例化
            in_channels = getattr(config.parameters.model, 'in_channels', 1)
            num_classes = getattr(config.parameters.model, 'num_classes', 10)
            self.model = model(in_channels=in_channels, num_classes=num_classes)
            logging.info(f"Server: 实例化了传入的模型类作为全局模型，输入通道数: {in_channels}, 类别数: {num_classes}")
        else: # 直接使用传入的实例
            self.model = model
            logging.info(f"Server: 使用传入的模型实例作为全局模型 ({type(self.model)})")
            # 检查模型的输入通道数
            if hasattr(self.model, 'conv1') and hasattr(self.model.conv1, 'weight'):
                in_channels = self.model.conv1.weight.shape[1]
                logging.info(f"Server: 传入模型的输入通道数: {in_channels}")
            elif hasattr(self.model, 'in_channels'):
                logging.info(f"Server: 传入模型记录的输入通道数: {self.model.in_channels}")
        
        # 确保 Trainer 和 Algorithm 实例被创建一次，并引用这个全局模型
        if trainer is None:
            # 导入自定义训练器
            from sc_afl_trainer import Trainer
            self.trainer = Trainer(model=self.model)
            logging.info("Server: 创建了新的Trainer实例")
        else:
            self.trainer = trainer
            if self.trainer.model is None or self.trainer.model is not self.model:
                self.trainer.model = self.model # 确保Trainer引用最新的全局模型实例
                logging.info("Server: 更新Trainer的模型引用")
            logging.info("Server: 使用传入的Trainer实例")
        
        # 确认训练器实例被正确创建，并具有所需的方法
        if self.trainer is None:
            logging.error("Server: 训练器初始化失败，为None")
            from sc_afl_trainer import Trainer
            self.trainer = Trainer(model=self.model)
            logging.info("Server: 重新创建训练器实例")
        elif not hasattr(self.trainer, 'test'):
            logging.error("Server: 训练器没有test方法")
            from sc_afl_trainer import Trainer
            self.trainer = Trainer(model=self.model)
            logging.info("Server: 由于方法缺失，重新创建了训练器实例")
        
        # 记录初始化时的算法实例引用 (修正：确保在self.algorithm赋值后捕获)
        initial_algorithm_instance = None
        if algorithm is None:
            # 导入自定义算法
            from sc_afl_algorithm import Algorithm
            self.algorithm = Algorithm(trainer=self.trainer)
            logging.info("Server: 创建了新的Algorithm实例")
        else:
            self.algorithm = algorithm
            if self.algorithm.trainer is None or self.algorithm.trainer is not self.trainer:
                self.algorithm.trainer = self.trainer # 确保Algorithm引用最新的Trainer实例
                logging.info("Server: 更新Algorithm的Trainer引用")
            logging.info("Server: 使用传入的Algorithm实例")
        initial_algorithm_instance = self.algorithm # 保存引用
        
        # 调用父类初始化
        # 父类可能会覆盖 self.algorithm，尽管它接受 algorithm 参数。
        super().__init__(model=self.model, algorithm=self.algorithm, trainer=self.trainer)

        # 手动设置simulate_wall_time从配置文件中读取
        config = Config()
        self.simulate_wall_time = getattr(config.server, 'simulate_wall_time', False)
        logging.info(f"[DEBUG] 从配置文件读取 simulate_wall_time={self.simulate_wall_time}")
        
        # --- 关键修正：确保 self.algorithm 在 super().__init__ 后仍然有效 ---
        # 如果 super().__init__ 不保留传入的 algorithm 实例，或者意外重置了它，
        # 我们在这里将其恢复。
        if self.algorithm is None or self.algorithm is not initial_algorithm_instance:
            logging.warning("Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。")
            self.algorithm = initial_algorithm_instance # 恢复到初始化时创建或传入的实例
            if self.algorithm is None: # 如果 initial_algorithm_instance 本身就是 None（不应该发生）
                 from sc_afl_algorithm import Algorithm
                 self.algorithm = Algorithm(trainer=self.trainer)
                 logging.info("Server: 重新创建了Algorithm实例（因为恢复也失败了）")
            
        # ------------------------------------------------------------------
        
        # 再次验证训练器状态
        if self.trainer is None:
            logging.warning("Server: 训练器在初始化后为None，重新创建")
            from sc_afl_trainer import Trainer
            self.trainer = Trainer(model=self.model)
            if self.algorithm is not None:
                self.algorithm.trainer = self.trainer
            logging.info("Server: 重新创建了Trainer实例")
        elif self.trainer.model is None or self.trainer.model is not self.model:
            self.trainer.model = self.model
            logging.info("Server: 更新了Trainer的模型引用")
            
        # 将算法与服务器关联
        if hasattr(self.algorithm, 'set_server_reference'): # 注意：方法名是 set_server_reference
            self.algorithm.set_server_reference(self)
            logging.info("Server: 算法类已设置服务器引用")
            
        # 使用动态加载器初始化数据源
        try:
            from dynamic_loader import DynamicDataLoader, ConfigurationManager

            # 获取配置信息
            config = Config()
            dataset_name, num_classes, in_channels = ConfigurationManager.get_dataset_info_from_config(config)
            data_path = getattr(config.parameters, 'data_path', './data')

            logging.info(f"动态加载数据集: {dataset_name}")

            # 动态加载测试集
            self.testset = DynamicDataLoader.load_dataset(
                dataset_name, data_path, is_train=False, download=True
            )

            logging.info(f"✅ 动态加载测试集成功: {dataset_name}, 大小: {len(self.testset)}")

            # 创建测试加载器
            from torch.utils.data import DataLoader
            self.test_loader = DataLoader(
                self.testset,
                batch_size=config.trainer.batch_size,
                shuffle=False
            )
            logging.info("✅ 测试加载器已创建")
        except Exception as e:
            log_exception("动态数据源初始化失败", e)
            logging.warning("回退到MNIST数据集")
            try:
                # 回退到MNIST
                from torchvision import datasets, transforms
                data_path = getattr(config.parameters, 'data_path', './data')
                test_transform = transforms.Compose([
                    transforms.ToTensor(),
                    transforms.Normalize((0.1307,), (0.3081,))
                ])
                self.testset = datasets.MNIST(
                    root=data_path, train=False, download=True,
                    transform=test_transform
                )
                from torch.utils.data import DataLoader
                self.test_loader = DataLoader(
                    self.testset,
                    batch_size=config.trainer.batch_size,
                    shuffle=False
                )
                logging.info("✅ 回退到MNIST数据集成功")
            except Exception as fallback_error:
                logging.error(f"回退到MNIST也失败: {fallback_error}")
                self.testset = None
                self.test_loader = None
        
        # 初始化全局权重
        self.global_model_cache = None  # 先将缓存设为None
        self.initialize_global_weights()  # 然后初始化全局权重，这会同时设置global_weights和global_model_cache
        
        # 初始化服务器状态
        self.is_training = False
        self.is_aggregating = False

        # 添加聚合锁机制，防止并发聚合
        self.aggregation_lock = asyncio.Lock()
        self.aggregation_in_progress = False  # 聚合进行中标志

        # 初始化聚合计数器
        self.aggregation_count = 0
        
        # 初始化结果保存路径
        self._init_results_path()
        
        # --- 关键修改：Server.__init__ 不再创建客户端实例 ---
        # 客户端实例将由 sc_afl.py 创建并注册到 self.clients
        self.clients = {} # 这是一个空的字典，等待 sc_afl.py 来填充
        self.client_status = {} # client_status 也由 sc_afl.py 来填充
        # ----------------------------------------------------
        
        self.client_estimated_duration = {}
        self.client_staleness = {}
        self.staleness_queue = {}
        self.client_beta = {}
        self.client_Hk_comp = {}
        self.client_Hk_comm = {}
        self.client_Hk = {}
        self.d_k_t_dict = {}
        self.client_start_time = {}
        self.historical_agg_round_durations = {}
        
        # 初始化客户端状态跟踪
        self.candidate_clients = set()
        self.selected_clients = []
        self.buffer_pool = []
        self.success_buffer_pool = []
        self.success_buffered_clients = []
        self.trigger_clients = []
            
        # 初始化其他属性
        self.aggregation_start_time = time.time()
        self.training_start_time = time.time()  # 用于Plato标准结果记录

        # 初始化权重变化跟踪
        self.previous_global_weights = None
        self.current_weight_change_mean = 0.0
        self.current_weight_change_std = 0.0

        # 初始化网络统计属性
        self.current_network_latency = 0.0
        self.current_network_bandwidth = 0.0
        self.current_network_reliability = 1.0

        # 初始化简化的网络模拟器
        try:
            enable_network = getattr(config.server, 'enable_network_simulation', False)
            if enable_network:
                # 创建简化的网络模拟器
                self.network_simulator = self._create_simple_network_simulator(config)
                logging.info("简化网络模拟器已启用")
            else:
                self.network_simulator = None
                logging.info("网络模拟器已禁用")
        except Exception as e:
            logging.warning(f"网络模拟器初始化失败: {e}")
            self.network_simulator = None
        
        # SC-AFL 相关属性
        self.current_round = 0
        self.global_round = 0
        self.last_aggregation_time = None  # 记录上次聚合的时间
        
        # SC-AFL算法参数
        config = Config()
        self.tau_max = getattr(config.algorithm, 'tau_max', 5)  # 陈旧度阈值
        self.V = getattr(config.algorithm, 'V', 1.0)  # 延迟权重参数
        logging.info(f"SC-AFL算法参数: tau_max={self.tau_max}, V={self.V}")
        
        # 用于记录每个客户端的H_k值
        self.client_H_values = {}
        
        # 用于记录每个客户端的tau_k值
        self.client_tau_values = {}
        
        # 用于记录每个客户端的Q_k值
        self.client_Q_values = {}
        
        # 用于记录每个客户端的beta_k值
        self.client_beta_values = {}
        
        # 用于记录每个客户端的D_k值
        self.client_D_values = {}
        
        # 用于记录每轮的总延迟D^t
        self.Dt_values = []
        
        # 用于记录每轮的平均延迟D^t/|K|
        self.avg_Dt_values = []
        
        # 用于记录每轮的最大延迟max_k{tau_k}
        self.max_tau_values = []
        
        # 用于记录每轮的平均延迟sum_k{tau_k}/|K|
        self.avg_tau_values = []
        
        # 用于记录每轮的延迟方差var_k{tau_k}
        self.var_tau_values = []
        
        # 用于记录每轮的聚合客户端数量|K|
        self.K_sizes = []
        
        # 用于记录每轮的聚合客户端ID列表
        self.K_ids = []
        
        # 用于记录每轮的聚合时间
        self.aggregation_times = []
        
        # 用于记录每轮的训练时间
        self.training_times = []
        
        # 用于记录每轮的总时间
        self.round_times = []
        
        # 用于记录每轮的准确率
        self.accuracies = []
        
        # 用于记录每轮的损失
        self.losses = []
        
        # 用于记录历史聚合的D^t值
        self.historical_Ds = []  # 存储每次聚合的D^t值
        # 新增：存储按轮次索引的聚合持续时间
        self.historical_agg_round_durations = {}
        
        self.staleness_history = []
        self.accuracy_log = []
        
        # 从配置中获取准确率阈值
        self.first_reach_time = {}
        accuracy_thresholds = getattr(config.server, 'accuracy_thresholds', [0.7, 0.8, 0.9])
        for threshold in accuracy_thresholds:
            self.first_reach_time[threshold] = None
            
        self.start_time = time.time()
        self.training_start_time = time.time()  # 记录训练开始时间
        
        # 初始化准确率相关属性
        self.accuracy = 0.0
        self.current_accuracy = 0.0
        self.best_accuracy = 0.0
        self.accuracy_history = []
        
        # 确保模型和全局权重已初始化
        if not hasattr(self, 'global_weights') or self.global_weights is None:
            logging.info("在服务器初始化结束前再次检查全局权重")
            if self.model is not None:
                self.initialize_global_weights()
            else:
                logging.warning("模型实例在初始化结束前变为None，正在尝试重新创建...")
                try:
                    logging.warning("全局模型实例为None，正在尝试重新创建...")
                    from plato.models import lenet5 # 再次导入确保局部可用
                    self.model = lenet5.Model(num_classes=Config().parameters.model.num_classes)
                    logging.info("成功重新创建了全局模型实例")
                    self.initialize_global_weights()
                except Exception as model_err:
                    logging.error(f"重新创建模型失败: {str(model_err)}")
                    raise ValueError("无法初始化全局权重：模型实例重新创建失败")
            
        logging.info("服务器初始化完成")
        logging.info(f"已创建并注册 {len(self.clients)} 个客户端（将在 Server.start() 中启动任务）") # 更新日志

        # 初始化客户端ID管理器
        self.id_manager = ClientIDManager()

        # 🔒 添加异步锁用于线程安全
        self.training_lock = asyncio.Lock()
        self.aggregation_lock = asyncio.Lock()

        # 添加训练完成标志
        self.training_completed = False

        # 初始化客户端状态跟踪
        self.client_status = {}
        self.client_staleness = {}  # 记录每个客户端的陈旧度
        
        # 用于记录客户端的计算和通信时间估计
        self.client_Hk = {}  # 总时间估计
        self.client_Hk_comp = {}  # 计算时间估计
        self.client_Hk_comm = {}  # 通信时间估计
        self.client_estimated_duration = {}  # 估计的训练持续时间
        
        # 用于记录客户端的开始时间
        self.client_start_time = {}
        
        # 用于记录每个客户端的延迟估计
        self.d_k_t_dict = {}
        
        # 聚合相关参数
        self.buffer_pool_size = getattr(Config().algorithm, 'buffer_pool_size', 5)
        self.success_buffer_pool = []  # 成功上传的客户端更新缓冲池
        self.success_buffered_clients = []  # 成功上传的客户端ID列表
        
        # 历史聚合轮次的持续时间
        self.historical_agg_round_durations = {}
        
        # 初始化数据源
        self.datasource = None
        
        # 初始化结果路径
        self._init_results_path()
        
        # 异步FL的状态 - 避免重复设置，使用已设置的值
        # self.tau_max 和 self.V 已在上面设置过了
        
        # 触发聚合的客户端列表
        self.trigger_clients = []
        
        # 异步FL的训练状态
        self.training_clients = {}  # 记录正在训练的客户端
        self.reported_clients = []  # 已报告的客户端
        self.staleness_bound = self.tau_max  # 陈旧度边界
        self.minimum_clients = 1  # 最小聚合客户端数
        self.asynchronous_mode = True  # 异步模式
        # 不要在这里覆盖simulate_wall_time，让基类从配置文件中读取
        # self.simulate_wall_time = False  # 注释掉，使用配置文件中的设置
        
        # 记录客户端上传状态
        self.client_upload_status = {}

        # 训练完成标志
        self.training_completed = False

    def _create_simple_network_simulator(self, config):
        """创建简化的网络模拟器"""
        class SimpleNetworkSimulator:
            def __init__(self, config):
                import random
                network_config = getattr(config.server, 'network_config', {})
                self.bandwidth_mean = network_config.get('bandwidth_mean', 10.0)
                self.bandwidth_std = network_config.get('bandwidth_std', 2.0)
                self.latency_mean = network_config.get('latency_mean', 50.0)
                self.latency_std = network_config.get('latency_std', 10.0)
                self.packet_loss_rate = network_config.get('packet_loss_rate', 0.01)

                # 网络环境状态
                self.current_network_condition = 'normal'
                self.weather_impact = 0.0
                self.success_rate = 1.0 - self.packet_loss_rate
                self.round_count = 0

            def simulate_network_delay(self, client_id=None):
                """模拟网络延迟"""
                import random
                return max(0, random.gauss(self.latency_mean, self.latency_std)) / 1000.0

            def simulate_packet_loss(self, client_id=None):
                """模拟丢包"""
                import random
                return random.random() < self.packet_loss_rate

            def simulate_client_communication(self, client_id, data_size_mb=1.0):
                """模拟客户端通信"""
                import random

                # 模拟通信成功/失败
                success = not self.simulate_packet_loss(client_id)

                # 计算传输时间
                bandwidth = self.simulate_bandwidth(client_id)
                delay = self.simulate_network_delay(client_id)
                transfer_time = (data_size_mb * 8) / bandwidth + delay  # 秒

                # 计算实际可靠性
                actual_reliability = self.success_rate * (1.0 - self.weather_impact * 0.3)

                details = {
                    'actual_reliability': actual_reliability,
                    'network_condition': self.current_network_condition,
                    'bandwidth': bandwidth,
                    'latency': delay * 1000,  # 转换为毫秒
                    'transfer_time': transfer_time
                }

                return success, transfer_time, details

            def get_network_statistics(self):
                """获取网络统计信息"""
                return {
                    'current_network_condition': self.current_network_condition,
                    'success_rate': self.success_rate,
                    'weather_impact': self.weather_impact,
                    'bandwidth_mean': self.bandwidth_mean,
                    'latency_mean': self.latency_mean,
                    'packet_loss_rate': self.packet_loss_rate
                }

            def update_global_conditions(self):
                """更新全局网络条件"""
                import random
                self.round_count += 1

                # 每10轮随机改变网络条件
                if self.round_count % 10 == 0:
                    conditions = ['normal', 'congested', 'unstable']
                    weights = [0.7, 0.2, 0.1]  # 正常情况更常见
                    self.current_network_condition = random.choices(conditions, weights=weights)[0]

                # 随机天气影响
                self.weather_impact = random.uniform(0.0, 0.3)

                # 根据网络条件调整成功率
                condition_factors = {
                    'normal': 1.0,
                    'congested': 0.8,
                    'unstable': 0.6,
                    'disaster': 0.3
                }
                base_success_rate = 1.0 - self.packet_loss_rate
                self.success_rate = base_success_rate * condition_factors.get(self.current_network_condition, 1.0)

        return SimpleNetworkSimulator(config)

    def register_client(self, client):
        """
        注册客户端到服务器
        参数:
            client: 要注册的客户端实例
        功能:
            1. 将客户端添加到服务器管理的客户端字典中
            2. 初始化客户端状态
            3. 记录客户端注册信息
        """
        if client.client_id is not None:
            # 使用ID管理器验证客户端ID
            validated_id = self.id_manager.get_valid_id(client.client_id)
            
            # 如果验证后的ID与原ID不同，更新客户端ID
            if validated_id != client.client_id:
                logging.warning(f"客户端ID {client.client_id} 无效，已更新为 {validated_id}")
                client.client_id = validated_id
            
            if client.client_id not in self.clients: # 避免重复注册
                self.clients[client.client_id] = client
                self.client_status[client.client_id] = {
                    'last_round': 0,
                    'last_aggregated_round': 0,  # 客户端最后一次被聚合的轮次
                    'last_model_pull_round': 0,  # 客户端最后一次拉取全局模型的轮次
                    'tau_k': 0,
                    'D_k': 0,
                    'Q_k': 1.0,
                    'is_upload_failed': False
                }
                logging.info("客户端%d已注册", client.client_id)
            else:
                logging.info(f"客户端{client.client_id}已存在，跳过注册")
        else:
            # 客户端ID为None，分配一个新ID
            new_id = self.id_manager.get_valid_id(None)
            client.client_id = new_id
            logging.info(f"客户端ID为None，已分配新ID: {new_id}")
            
            # 递归调用自身完成注册
            self.register_client(client)

    def get_cid(self, cid_obj):
        """从客户端ID对象中提取客户端ID"""
        try:
            if isinstance(cid_obj, SimpleNamespace):
                for key in ['id', 'client_id', 'sid', 'name']:
                    if hasattr(cid_obj, key):
                        cid = getattr(cid_obj, key)
                        if cid is not None:
                            return cid
                # 如果无法从SimpleNamespace获取有效ID，记录详细信息
                logger.error("无法从SimpleNamespace获取有效ID: %s", cid_obj)
                logger.error("SimpleNamespace属性: %s", dir(cid_obj))
                raise ValueError(f"无法从SimpleNamespace获取合法ID: {cid_obj}")
            elif cid_obj is None:
                logger.error("客户端ID对象为None")
                raise ValueError("客户端ID对象为None")
            else:
                return cid_obj
        except Exception as e:
            logger.error("get_cid方法出错，输入对象: %s, 类型: %s, 错误: %s", 
                        cid_obj, type(cid_obj), str(e))
            raise
        
    def estimate_client_times(self, cid):
        """
        估计客户端的计算和通信时间（更贴近现实无线边缘环境）
        """
        try:
            cid = self.get_cid(cid)
            a = 0.5  # 计算时间基线
            mu = 1.0
            param_count = 100000  # 模型参数量
            # 距离分布更广，模拟异构性
            distance = np.random.uniform(0.5, 3.0)  # 单位: km

            # 计算时间估计
            H_comp = a + np.random.exponential(1/mu)
            self.client_Hk_comp[cid] = H_comp

            # 通信参数
            B = np.random.uniform(50e3, 500e3)  # 带宽: 50~500kHz
            P = 10 ** ((10 - 30) / 10)  # 发射功率 (W)
            N0 = np.random.uniform(1e-15, 1e-13)  # 噪声功率谱密度 (W/Hz)
            L = 10 ** (3.5 * np.log10(distance))  # 路径损耗
            SNR = P / (N0 * B * L)  # 信噪比

            # 信道容量
            C = max(B * math.log2(1 + SNR), 1e3)  # 下限1kbps

            # 模型大小（100K参数，32位）
            model_size = param_count * 32  # bits

            # 基础延迟（如协议、排队、接入等）
            latency = np.random.uniform(0.05, 0.3)  # 50~300ms

            # 通信时间 = 传输时延 + 基础延迟
            H_comm = model_size / C + latency
            # 控制在0.2~2s区间
            H_comm = np.clip(H_comm, 0.2, 2.0)
            self.client_Hk_comm[cid] = H_comm

            # 总时间估计
            H_k = H_comp + H_comm
            self.client_Hk[cid] = H_k
            self.client_estimated_duration[cid] = H_k
            logging.debug(f"估计客户端{cid}训练+通信时间: {H_k}秒 (计算: {H_comp}秒, 通信: {H_comm}秒, 距离: {distance:.2f}km, 带宽: {B:.0f}Hz, SNR: {SNR:.2e})")
        except Exception as e:
            log_exception(f"估计客户端{cid}时间时出错", e)

    def get_dkt(self, cid):
        """
        基于客户端状态的延迟估计函数

        计算公式：d_k^t = max{Hk - ∑(j=t-τk(t))^(t-1) Dj, 0}
        其中：
        - Hk是客户端的计算和通信时间估计
        - τk(t)是客户端的陈旧度
        - Dj是历史聚合延迟
        - t-τk(t)是客户端上次拉取模型所基于的服务器全局轮次

        参数:
            cid: 客户端ID

        返回:
            float: 客户端当前的延迟估计值
        """
        try:
            cid = self.get_cid(cid)
            t = self.global_round

            # 获取客户端上次拉取模型的轮次
            if cid in self.client_status:
                last_pull_round = self.client_status[cid].get('last_model_pull_round', 0)
                tau_k = max(0, t - last_pull_round)
                self.client_staleness[cid] = tau_k
            else:
                last_pull_round = 0
                tau_k = 0
                self.client_staleness[cid] = 0

            # 确保客户端时间估计已计算
            if cid not in self.client_Hk:
                self.estimate_client_times(cid)

            # 获取客户端的估计计算+通信时间
            H_k = self.client_Hk.get(cid, 5.0)  # 默认5秒

            # 计算从last_pull_round+1到t-1的历史聚合时间总和
            D_sum = 0.0

            # 修正范围：从客户端上次拉取后的轮次开始到当前轮次-1
            start_round = last_pull_round + 1
            end_round = t

            for round_idx in range(start_round, end_round):
                D_sum += self.historical_agg_round_durations.get(round_idx, 0.0)

            # 计算延迟估计值：max{Hk - ∑Dj, 0}
            d_k_t = max(H_k - D_sum, 0)

            # 保存d_k_t计算结果
            self.d_k_t_dict[cid] = d_k_t

            logging.info(f"客户端{cid}延迟估计: H_k={H_k:.2f}s, tau={tau_k}, last_pull_round={last_pull_round}, D_sum={D_sum:.4f}s, d_k_t={d_k_t:.4f}s")

            return d_k_t
        except Exception as e:
            log_exception(f"计算客户端{cid}延迟估计时出错", e)
            return 5.0  # 返回默认值而不是0

    async def select_clients(self):
        """随机选择客户端参与训练"""
        try:
            # 确保当前轮次已初始化
            if not hasattr(self, 'current_round'):
                self.current_round = 0
                
            # 确保total_clients已初始化
            if not hasattr(self, 'total_clients') or self.total_clients is None or self.total_clients <= 0:
                config = Config()
                default_total_clients = getattr(config.clients, 'total_clients')
                logging.error(f"客户端总数未设置或无效，使用配置值: {default_total_clients}")
                self.total_clients = default_total_clients
                
            # 确保clients_per_round已初始化
            if not hasattr(self, 'clients_per_round'):
                default_clients_per_round = getattr(config.clients, 'per_round', 3)
                self.clients_per_round = default_clients_per_round  # 默认每轮选择3个客户端
                logging.info(f"设置每轮选择的客户端数量为: {self.clients_per_round}")
                
            # 初始化客户端相关字典（如果需要）
            if not hasattr(self, 'client_status'):
                self.client_status = {}
                
            if not hasattr(self, 'client_Hk'):
                self.client_Hk = {}
                
            # 智能选择客户端：优先选择陈旧度高的客户端，但过滤陈旧度过高的客户端
            all_clients = list(self.clients.keys())

            # 过滤陈旧度过高的客户端
            available_clients = []
            for client_id in all_clients:
                last_pull_round = self.client_status.get(client_id, {}).get('last_model_pull_round', 0)
                staleness = max(0, self.global_round - last_pull_round)

                # 只选择陈旧度不超过tau_max的客户端
                if staleness <= self.tau_max:
                    available_clients.append(client_id)
                else:
                    logging.warning(f"客户端 {client_id} 陈旧度过高({staleness} > {self.tau_max})，跳过选择")

            if not available_clients:
                logging.warning(f"所有客户端陈旧度都超过阈值 {self.tau_max}，强制选择陈旧度最低的客户端")
                # 如果所有客户端都超过阈值，选择陈旧度最低的几个
                client_staleness = {}
                for client_id in all_clients:
                    last_pull_round = self.client_status.get(client_id, {}).get('last_model_pull_round', 0)
                    staleness = max(0, self.global_round - last_pull_round)
                    client_staleness[client_id] = staleness

                # 按陈旧度排序，选择陈旧度最低的客户端
                sorted_by_staleness = sorted(all_clients, key=lambda x: client_staleness[x])
                available_clients = sorted_by_staleness[:self.clients_per_round]
                logging.info(f"强制选择陈旧度最低的客户端: {available_clients}")

            selected_clients_count = min(self.clients_per_round, len(available_clients))

            # 计算每个客户端的综合选择得分（考虑陈旧度和网络可靠性）
            client_scores = {}
            for client_id in available_clients:
                # 1. 计算陈旧度
                last_pull_round = self.client_status.get(client_id, {}).get('last_model_pull_round', 0)
                staleness = max(0, self.global_round - last_pull_round)

                # 2. 获取网络可靠性（通信成功率β_k）
                if self.network_simulator is not None:
                    # 预测客户端通信成功概率
                    success, _, details = self.network_simulator.simulate_client_communication(
                        client_id, data_size_mb=1.0
                    )
                    # 使用实际可靠性作为通信成功率
                    beta_k = details.get('actual_reliability', 0.8)
                    network_condition = details.get('network_condition', 'normal')
                else:
                    # 没有网络模拟器时使用默认值
                    beta_k = 0.8
                    network_condition = 'normal'

                # 3. 计算SCAFL综合得分
                # 陈旧度得分（陈旧度越高得分越高，但要考虑通信可靠性）
                staleness_score = staleness * beta_k

                # 网络可靠性得分（可靠性越高得分越高）
                reliability_score = beta_k * 10  # 放大系数

                # 综合得分：平衡陈旧度和网络可靠性
                total_score = staleness_score + reliability_score

                client_scores[client_id] = {
                    'total_score': total_score,
                    'staleness': staleness,
                    'beta_k': beta_k,
                    'network_condition': network_condition
                }

                logging.debug(f"客户端{client_id}: 陈旧度={staleness}, 可靠性={beta_k:.3f}, "
                            f"网络状态={network_condition}, 综合得分={total_score:.3f}")

            # 按综合得分排序（从高到低）
            sorted_clients = sorted(available_clients,
                                  key=lambda x: client_scores[x]['total_score'],
                                  reverse=True)

            # 智能选择策略：优先选择得分最高的客户端
            # 但保留一定的随机性以避免总是选择相同的客户端
            import random
            high_score_count = int(selected_clients_count * 0.8)  # 80%选择高分客户端
            random_count = selected_clients_count - high_score_count

            # 选择得分最高的客户端
            selected_high_score = sorted_clients[:high_score_count]

            # 从剩余客户端中随机选择
            remaining_clients = sorted_clients[high_score_count:]
            if remaining_clients and random_count > 0:
                selected_random = random.sample(remaining_clients, min(random_count, len(remaining_clients)))
            else:
                selected_random = []

            self.selected_clients = selected_high_score + selected_random

            # 记录选择信息（包含网络可靠性）
            selection_info = []
            for cid in self.selected_clients:
                score_info = client_scores[cid]
                selection_info.append((
                    cid,
                    score_info['staleness'],
                    score_info['beta_k'],
                    score_info['network_condition'],
                    score_info['total_score']
                ))

            logging.info(f"智能选择客户端 - 综合得分排序:")
            for cid, staleness, beta_k, net_cond, score in selection_info:
                logging.info(f"  客户端{cid}: 陈旧度={staleness}, 可靠性={beta_k:.3f}, "
                           f"网络={net_cond}, 得分={score:.3f}")

            # 存储客户端通信成功率，用于后续处理
            if not hasattr(self, 'client_beta'):
                self.client_beta = {}
            for cid in self.selected_clients:
                self.client_beta[cid] = client_scores[cid]['beta_k']
            
            # 为选中的客户端估算训练和通信时间
            for client_id in self.selected_clients:
                self.estimate_client_times(client_id)
                
            logging.info(f"本轮随机选择了 {len(self.selected_clients)} 个客户端参与本地训练: {self.selected_clients}")
            
            return self.selected_clients
            
        except Exception as e:
            log_exception("随机选择客户端时出错", e)
            self.selected_clients = []
            return []
            
    async def send_model_to_client(self, client_id):
        """
        向指定客户端发送当前全局模型
        
        参数:
            client_id: 客户端ID
            
        返回:
            bool: 是否成功发送模型
        """
        try:
            if client_id is None:
                logging.error("发送模型时客户端ID为None")
                return False
                
            client = self.clients.get(client_id)
            
            if client is None:
                logging.error(f"客户端 {client_id} 不存在")
                return False
                
            if not hasattr(client, 'receive_model') or not callable(getattr(client, 'receive_model')):
                logging.error(f"客户端 {client_id} 没有receive_model方法")
                return False
                
            if self.global_weights is None:
                logging.error(f"全局权重为None，无法发送给客户端 {client_id}")
                return False
            
            # 确保全局权重是有效的模型权重
            if self.global_weights is not None and len(self.global_weights) > 0:
                logging.info(f"向客户端 {client_id} 发送全局模型 - 参数数量: {len(self.global_weights)}")
                success = False

                # 如果客户端的receive_model方法是协程函数，使用await调用
                if hasattr(client.receive_model, '__awaitable__') or asyncio.iscoroutinefunction(client.receive_model):
                    success = await client.receive_model(self.global_weights)
                else:
                    # 否则直接调用
                    success = client.receive_model(self.global_weights)

                if success:
                    # 只有在模型成功发送后才更新客户端状态
                    if client_id not in self.client_status:
                        self.client_status[client_id] = {}

                    self.client_status[client_id]['last_model_pull_round'] = self.global_round
                    self.client_status[client_id]['last_model_pull_time'] = time.time()

                    # 记录客户端拉取模型的时间
                    logging.info(f"客户端 {client_id} 在轮次 {self.global_round} 成功拉取了最新模型")

                    # 重置客户端训练器的陈旧度
                    if hasattr(client, 'trainer') and client.trainer:
                        try:
                            client.trainer.reset_staleness(self.global_round)
                            logging.info(f"重置客户端 {client_id} 的陈旧度")
                        except Exception as e:
                            logging.error(f"重置客户端 {client_id} 陈旧度时出错: {str(e)}")

                    logging.info(f"成功发送全局模型到客户端 {client_id}")
                    return self.global_weights
                else:
                    logging.error(f"向客户端 {client_id} 发送全局模型失败")
                    return False
            else:
                logging.error(f"全局权重无效或为空，无法发送到客户端 {client_id}")
                return False
        except Exception as e:
            log_exception(f"向客户端 {client_id} 发送模型时出错", e)
            return False

    async def process_client_update(self, client_id, report, weights):
        """处理客户端更新"""
        try:
            # 使用预先计算的网络可靠性进行通信判断
            if self.network_simulator is not None and hasattr(self, 'client_beta'):
                # 获取预先计算的通信成功率
                beta_k = self.client_beta.get(client_id, 0.8)

                # 基于通信成功率判断是否成功
                import random
                random_value = random.random()
                communication_success = random_value < beta_k

                logging.info(f"[网络模拟] 客户端 {client_id} 通信检查: 成功率={beta_k:.3f}, 随机值={random_value:.3f}, 通信成功={communication_success}")

                if not communication_success:
                    logging.warning(f"[网络模拟] 客户端 {client_id} 通信失败 (成功率={beta_k:.3f}) - 更新被拒绝")
                    # 记录通信失败，但不立即返回False，而是标记为失败处理
                    self._handle_communication_failure(client_id, weights)
                    return False

                # 模拟网络延迟（基于网络条件）
                network_stats = self.network_simulator.get_network_statistics()
                current_condition = network_stats.get('current_network_condition', 'normal')

                # 根据网络条件模拟延迟
                delay_factors = {
                    'normal': 0.1,
                    'congested': 0.3,
                    'unstable': 0.5,
                    'disaster': 1.0
                }
                base_delay = delay_factors.get(current_condition, 0.1)
                actual_delay = min(base_delay * random.uniform(0.5, 1.5), 2.0)

                if actual_delay > 0.05:  # 如果延迟超过50ms
                    await asyncio.sleep(actual_delay)

                logging.info(f"客户端 {client_id} 通信成功: 成功率={beta_k:.3f}, "
                           f"网络状态={current_condition}, 延迟={actual_delay:.2f}s")

            if client_id is None:
                logging.error("处理客户端更新时客户端ID为None")
                logging.error(f"调用堆栈: {traceback.format_stack()}")
                return False
                
            if report is None:
                logging.error(f"客户端 {client_id} 的report为None")
                return False
                
            if weights is None:
                logging.error(f"客户端 {client_id} 的weights为None")
                return False
                
            if not isinstance(weights, dict):
                logging.error(f"客户端 {client_id} 的weights不是字典类型: {type(weights)}")
                return False
                
            if len(weights) == 0:
                logging.error(f"客户端 {client_id} 的weights为空字典")
                return False
                
            import torch
            try:
                all_params = torch.cat([v.flatten() for v in weights.values() if isinstance(v, torch.Tensor)])
                logging.info(f"[客户端权重摘要] 客户端{client_id} | 参数数量: {len(weights)}, 均值: {all_params.mean():.6f}, 最大: {all_params.max():.6f}, 最小: {all_params.min():.6f}")
            except Exception as e:
                logging.error(f"计算客户端 {client_id} 权重摘要时出错: {str(e)}")
            
            # 检查客户端是否已经有更新在缓冲池中
            existing_update_indices = []
            for i, update in enumerate(self.success_buffer_pool):
                if update['client_id'] == client_id:
                    existing_update_indices.append(i)
                    
            if existing_update_indices:
                logging.info(f"客户端 {client_id} 已有 {len(existing_update_indices)} 个更新在缓冲池中，删除旧更新")
                # 按照索引从大到小排序，以便从后往前删除
                for idx in sorted(existing_update_indices, reverse=True):
                    old_update = self.success_buffer_pool.pop(idx)
                    timestamp = old_update.get('timestamp', 0)
                    time_ago = time.time() - timestamp
                    logging.info(f"移除客户端 {client_id} 的旧更新，距今 {time_ago:.1f} 秒")
            
            # 计算客户端当前陈旧度
            last_pull_round = self.client_status.get(client_id, {}).get('last_model_pull_round', 0)
            current_staleness = max(0, self.global_round - last_pull_round)

            # 添加新更新记录，包含陈旧度信息
            update_record = {
                'client_id': client_id,
                'weights': weights,
                'report': report,
                'timestamp': time.time(),
                'staleness': current_staleness,  # 添加陈旧度信息
                'round_submitted': self.global_round  # 添加提交轮次信息
            }

            logging.info(f"客户端 {client_id} 更新记录 - 陈旧度: {current_staleness}, 提交轮次: {self.global_round}")
            
            self.success_buffer_pool.append(update_record)

            # 更新唯一客户端列表
            if client_id not in self.success_buffered_clients:
                self.success_buffered_clients.append(client_id)

            logging.info(f"✅ 客户端 {client_id} 的更新已加入成功缓冲池，当前池大小: {len(self.success_buffer_pool)}")
            logging.info(f"📊 当前缓冲池中的客户端: {[update['client_id'] for update in self.success_buffer_pool]}")

            # 更新客户端通信成功率（成功）
            self._update_communication_success_rate(client_id, success=True)

            # 客户端更新添加后记录状态，聚合将由主循环统一处理
            logging.info(f"🔍 客户端 {client_id} 更新处理完成，等待主循环检查聚合条件...")

            return True
        except Exception as e:
            log_exception(f"处理客户端 {client_id} 更新时出错", e)
            return False

    def start(self): # <--- 必须是同步方法 def
        """启动服务器并开始第一轮训练"""
        try:
            logging.info("[Server #%d] 启动中...", os.getpid())

            # 日志已在全局setup_logging()中配置，无需重复设置

            if self.global_weights is None:
                self.initialize_global_weights()

            self.training_start_time = time.time()
            
            loop = asyncio.get_event_loop() 
            logging.info(f"服务器将使用事件循环: {loop}")
            
            # --- 修复：避免重复创建客户端 ---
            if not self.clients:
                logging.warning("⚠️ 服务器中没有客户端！")
                logging.warning("⚠️ 客户端应该在sc_afl.py中创建，而不是在服务器start()方法中")
                logging.warning("⚠️ 请检查sc_afl.py中的客户端创建逻辑")
                logging.error("❌ 无法启动训练，因为没有客户端")
                return
            else:
                logging.info(f"✅ 服务器已有 {len(self.clients)} 个客户端，开始启动训练")

                # 确保clients_per_round已初始化
                if not hasattr(self, 'clients_per_round'):
                    config = Config()
                    default_clients_per_round = getattr(config.clients, 'per_round', 3)
                    self.clients_per_round = default_clients_per_round
                    logging.info(f"设置每轮选择的客户端数量为: {self.clients_per_round}")
            # --- 修复代码结束 ---
            
            # 异步模式：启动所有客户端的独立训练任务
            logging.info(f"🔄 开始为 {len(self.clients)} 个客户端启动训练任务...")

            started_clients = 0
            for client_id, client_instance in self.clients.items():
                try:
                    logging.info(f"📋 正在启动客户端 {client_id}...")

                    # 初始化客户端模型
                    if hasattr(client_instance, 'initialize_model') and callable(client_instance.initialize_model):
                        try:
                            client_instance.initialize_model()
                            logging.info(f"✅ 客户端 {client_id} 模型初始化成功")
                        except Exception as model_err:
                            logging.error(f"❌ 客户端 {client_id} 初始化模型失败: {str(model_err)}")
                            continue  # 跳过这个客户端

                    # 🔄 检查是否在异步上下文中，如果不是则使用线程
                    try:
                        # 尝试获取当前事件循环
                        loop = asyncio.get_running_loop()
                        # 如果成功，说明在异步上下文中，可以创建异步任务
                        task = asyncio.create_task(
                            self._async_client_training(client_instance),
                            name=f"client_{client_id}_training"
                        )
                        if not hasattr(self, 'client_tasks'):
                            self.client_tasks = {}
                        self.client_tasks[client_id] = task
                        logging.info(f"🚀 客户端 {client_id} 异步训练协程已创建")
                        started_clients += 1
                    except RuntimeError as no_loop_error:
                        # 没有运行的事件循环，使用线程模式
                        if "no running event loop" in str(no_loop_error):
                            logging.info(f"⚠️ 未检测到异步事件循环，客户端 {client_id} 使用线程模式")

                            # 🧵 启动线程模式训练
                            import threading
                            thread = threading.Thread(
                                target=self._start_client_training_thread,
                                args=(client_instance, client_id),
                                name=f"client_{client_id}_thread"
                            )
                            thread.daemon = True  # 设置为守护线程
                            thread.start()

                            logging.info(f"🧵 客户端 {client_id} 训练线程已启动")
                            started_clients += 1
                        else:
                            raise no_loop_error

                except Exception as e:
                    logging.error(f"❌ 启动客户端 {client_id} 异步任务时出错: {str(e)}")
                    import traceback
                    logging.error(f"启动客户端异常堆栈: {traceback.format_exc()}")

            logging.info(f"🎯 成功启动 {started_clients}/{len(self.clients)} 个客户端的训练任务")
            
            self.server_state = "选择客户端"

            # 🔄 尝试启动服务器主循环（如果在异步上下文中）
            try:
                loop = asyncio.get_running_loop()
                server_main_loop_task = loop.create_task(self.run())
                logging.info(f"🚀 服务器主循环任务已启动: {server_main_loop_task}")
            except RuntimeError:
                logging.info("⚠️ 未检测到异步事件循环，服务器将使用同步模式")

            super().start() # <--- 不要加 await
            
        except Exception as e:
            logging.error(f"服务器启动失败: {str(e)}")
            import traceback
            logging.error(f"异常堆栈: {traceback.format_exc()}")
            raise

    async def run(self):
        """运行服务器训练循环"""
        try:
            logging.info(f"[Server #{os.getpid()}] 开始训练，共有 {self.total_clients} 个客户端，每轮最多聚合 {self.max_aggregation_clients} 个客户端")

            # 从配置文件获取时间间隔参数
            config = Config()
            training_interval = getattr(config.server, 'training_interval', 3)  # 默认3秒

            if not hasattr(self, 'global_round'):
                self.global_round = 0

            if not hasattr(self, 'total_rounds'):
                self.total_rounds = getattr(config.trainer, 'rounds', 100)

            logging.info(f"总训练轮次: {self.total_rounds}")

            # 创建客户端（如果还没有创建）
            if len(self.clients) == 0:
                logging.info(f"开始创建 {self.total_clients} 个客户端...")
                created_clients = self.create_clients(self.total_clients)
                logging.info(f"已创建 {len(created_clients)} 个客户端，注册客户端数: {len(self.clients)}")

                # 确保客户端创建成功
                if len(created_clients) == 0:
                    logging.error("客户端创建失败，退出训练")
                    return
            
            # 初始化全局模型权重
            if self.global_weights is None:
                self.initialize_global_weights()
                logging.info("全局模型权重已初始化")

            # 主训练循环
            while self.global_round < self.total_rounds:
                logging.info(f"🚀 开始第 {self.global_round + 1} 轮训练（目标：{self.total_rounds} 轮）")

                # 异步模式下，持续检查聚合条件
                aggregation_triggered = False
                round_start_time = time.time()
                max_round_time = 300  # 每轮最大等待时间5分钟

                while not aggregation_triggered and (time.time() - round_start_time) < max_round_time:
                    # 检查是否有足够的客户端更新可以聚合
                    if self.should_trigger_aggregation():
                        current_round = self.global_round
                        logging.info(f"✅ 触发聚合条件，开始第 {current_round} 轮聚合")
                        success = await self.check_and_trigger_aggregation()
                        if success:
                            # 注意：global_round 在 _perform_aggregation 中已经被递增
                            logging.info(f"🎉 第 {current_round} 轮聚合完成，当前轮次: {self.global_round}")
                            aggregation_triggered = True

                            # 聚合完成后，重新启动客户端训练
                            await self.restart_client_training()
                        else:
                            logging.warning(f"第 {current_round} 轮聚合失败，继续等待")
                    else:
                        # 检查缓冲池状态
                        buffer_size = len(self.success_buffer_pool)
                        if buffer_size == 0:
                            logging.info(f"缓冲池为空，不触发聚合（缓冲池大小: {buffer_size}）")
                        else:
                            logging.info(f"缓冲池有 {buffer_size} 个更新，但未达到聚合条件")

                    # 异步模式下更频繁地检查（每秒检查一次）
                    await asyncio.sleep(1)

                if not aggregation_triggered:
                    current_round = self.global_round
                    logging.warning(f"第 {current_round} 轮等待超时，强制进行聚合检查")
                    if len(self.success_buffer_pool) > 0:
                        success = await self.check_and_trigger_aggregation()
                        if success:
                            logging.info(f"🎉 强制聚合完成，当前轮次: {self.global_round}")
                            await self.restart_client_training()
                        else:
                            logging.error(f"强制聚合失败，跳过第 {current_round} 轮")
                            # 轮次递增将在_perform_aggregation中统一处理
                    else:
                        logging.error(f"缓冲池为空，无法进行聚合，跳过第 {current_round} 轮")
                        # 轮次递增将在_perform_aggregation中统一处理

            logging.info(f"🏁 所有 {self.total_rounds} 轮训练已完成！")

            # 训练完成后的清理工作
            await self.training_completed_cleanup()
                
        except KeyboardInterrupt:
            logging.info("检测到键盘中断，停止训练...")
            return
        except Exception as e:
            logging.error(f"服务器运行出错: {str(e)}")
            import traceback
            logging.error(f"异常堆栈: {traceback.format_exc()}")
            raise

    async def restart_client_training(self):
        """聚合完成后重新启动客户端训练"""
        try:
            # 检查训练是否已完成
            if hasattr(self, 'training_completed') and self.training_completed:
                logging.info("训练已完成，跳过客户端重启")
                return

            # 检查是否达到训练轮次上限
            if self.global_round >= self.total_rounds:
                logging.info(f"已达到训练轮次上限 ({self.total_rounds})，跳过客户端重启")
                return

            logging.info(f"🔄 聚合完成，重新启动客户端训练（第 {self.global_round} 轮）")

            # 清空缓冲池，为新一轮训练做准备
            self.success_buffer_pool.clear()
            logging.info("缓冲池已清空，准备新一轮训练")

            # 重新启动所有客户端的训练任务
            active_clients = 0
            for client_id, client in self.clients.items():
                try:
                    # 检查客户端是否还在运行
                    if hasattr(client, 'is_training') and client.is_training:
                        logging.info(f"客户端 {client_id} 仍在训练中，跳过重启")
                        continue

                    # 启动客户端异步训练
                    logging.info(f"重新启动客户端 {client_id} 的训练任务")

                    # 创建新的事件循环来运行异步训练
                    def start_client_training():
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            loop.run_until_complete(client.train())
                            loop.close()
                        except Exception as e:
                            logging.error(f"客户端 {client_id} 训练任务出错: {str(e)}")

                    # 在新线程中启动客户端训练
                    training_thread = threading.Thread(target=start_client_training, daemon=True)
                    training_thread.start()

                    active_clients += 1

                except Exception as e:
                    logging.error(f"重启客户端 {client_id} 训练失败: {str(e)}")
                    continue

            logging.info(f"✅ 已重新启动 {active_clients} 个客户端的训练任务")

        except Exception as e:
            logging.error(f"重启客户端训练失败: {str(e)}")
            import traceback
            logging.error(f"重启异常堆栈: {traceback.format_exc()}")

    async def training_completed_cleanup(self):
        """
        训练完成后的清理工作
        """
        try:
            logging.info("🧹 开始训练完成后的清理工作...")

            # 1. 停止所有客户端训练
            await self.stop_all_clients()

            # 2. 清空缓冲池
            self.success_buffer_pool.clear()
            logging.info("缓冲池已清空")

            # 3. 设置训练完成标志
            self.training_completed = True
            logging.info("✅ 训练完成清理工作已完成")

            # 4. 调用父类的关闭方法
            await self._close()

        except Exception as e:
            logging.error(f"训练完成清理失败: {str(e)}")
            import traceback
            logging.error(f"清理异常堆栈: {traceback.format_exc()}")

    async def stop_all_clients(self):
        """
        停止所有客户端的训练
        """
        try:
            logging.info("🛑 停止所有客户端训练...")
            stopped_clients = 0

            for client_id, client in self.clients.items():
                try:
                    # 设置客户端停止标志
                    if hasattr(client, 'should_stop'):
                        client.should_stop = True
                        logging.info(f"已设置客户端 {client_id} 停止标志")

                    # 如果客户端有训练器，停止训练器
                    if hasattr(client, 'trainer') and client.trainer is not None:
                        if hasattr(client.trainer, 'stop_training'):
                            client.trainer.stop_training()
                            logging.info(f"已停止客户端 {client_id} 的训练器")

                    stopped_clients += 1

                except Exception as e:
                    logging.error(f"停止客户端 {client_id} 失败: {str(e)}")
                    continue

            logging.info(f"✅ 已停止 {stopped_clients} 个客户端的训练")

        except Exception as e:
            logging.error(f"停止所有客户端失败: {str(e)}")

    async def _async_client_training(self, client_instance):
        """🔄 异步客户端训练方法 - 使用 asyncio 协程替代线程

        Args:
            client_instance: 客户端实例
        """
        client_id = getattr(client_instance, 'client_id', 'Unknown')

        try:
            logging.info(f"🚀 开始客户端 {client_id} 异步训练协程")

            training_round = 0
            max_rounds = 10  # 最大训练轮数

            while not self.training_completed and training_round < max_rounds:
                try:
                    # 🔒 使用异步锁确保线程安全
                    async with self.training_lock:
                        # 检查是否有可用的全局模型
                        if self.model is not None:
                            logging.info(f"📚 客户端 {client_id} 开始第 {training_round + 1} 轮训练...")

                            # 🧠 模拟异步训练过程
                            training_result = await self._simulate_async_training(client_instance)

                            if training_result:
                                logging.info(f"✅ 客户端 {client_id} 第 {training_round + 1} 轮训练完成")

                                # 📤 模拟上传结果到缓冲池
                                await self._simulate_upload_result(client_instance, training_result)

                                training_round += 1
                            else:
                                logging.warning(f"⚠️ 客户端 {client_id} 第 {training_round + 1} 轮训练失败")

                        # 💾 手动释放显存
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()

                    # ⏱️ 等待下一轮训练
                    await asyncio.sleep(5)  # 5秒间隔

                except Exception as training_error:
                    log_exception(f"客户端 {client_id} 训练循环异常", training_error)
                    await asyncio.sleep(2)  # 错误后短暂等待

        except Exception as e:
            log_exception(f"客户端 {client_id} 异步训练协程失败", e)
        finally:
            logging.info(f"🏁 客户端 {client_id} 异步训练协程结束")

    async def _simulate_async_training(self, client_instance):
        """🧠 模拟异步训练过程

        Args:
            client_instance: 客户端实例

        Returns:
            训练结果或None
        """
        client_id = getattr(client_instance, 'client_id', 'Unknown')

        try:
            # 模拟训练时间（2-5秒随机）
            import random
            training_time = random.uniform(2, 5)
            await asyncio.sleep(training_time)

            # 模拟训练结果
            accuracy = random.uniform(0.7, 0.9)
            loss = random.uniform(0.1, 0.3)

            result = {
                "client_id": client_id,
                "accuracy": accuracy,
                "loss": loss,
                "training_time": training_time,
                "timestamp": time.time()
            }

            logging.info(f"📊 客户端 {client_id} 训练结果: 准确率={accuracy:.3f}, 损失={loss:.3f}")
            return result

        except Exception as e:
            log_exception(f"客户端 {client_id} 模拟训练失败", e)
            return None

    async def _simulate_upload_result(self, client_instance, training_result):
        """📤 模拟上传训练结果到缓冲池

        Args:
            client_instance: 客户端实例
            training_result: 训练结果
        """
        client_id = getattr(client_instance, 'client_id', 'Unknown')

        try:
            # 模拟上传时间
            await asyncio.sleep(0.5)

            # 添加到缓冲池
            self.success_buffer_pool.append(training_result)

            logging.info(f"📤 客户端 {client_id} 结果已添加到缓冲池，当前缓冲池大小: {len(self.success_buffer_pool)}")

        except Exception as e:
            log_exception(f"客户端 {client_id} 模拟上传失败", e)

    def _start_client_training_thread(self, client, client_id):
        """统一的客户端训练线程函数"""
        try:
            import asyncio
            import random
            import time

            # 🔧 减少随机延迟，确保所有客户端能快速启动
            initial_delay = random.uniform(0, 2)  # 改为0-2秒随机延迟
            logging.info(f"🕐 客户端 {client_id} 将在 {initial_delay:.1f} 秒后开始训练")
            time.sleep(initial_delay)

            logging.info(f"🚀 客户端 {client_id} 开始异步训练循环")

            # 异步训练循环
            while self.global_round < self.total_rounds:
                try:
                    # 检查训练是否已完成
                    if hasattr(self, 'training_completed') and self.training_completed:
                        logging.info(f"客户端 {client_id} 检测到训练已完成，退出训练循环")
                        break

                    # 检查是否达到训练轮次上限
                    if self.global_round >= self.total_rounds:
                        logging.info(f"客户端 {client_id} 检测到已达到训练轮次上限，退出训练循环")
                        break

                    # 创建新的事件循环来运行异步训练
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        # 客户端异步训练
                        if hasattr(client, 'train') and callable(client.train):
                            if asyncio.iscoroutinefunction(client.train):
                                result = loop.run_until_complete(client.train())
                            else:
                                result = loop.run_until_complete(asyncio.to_thread(client.train))
                            logging.info(f"客户端 {client_id} 训练完成")
                    finally:
                        loop.close()

                    # 训练间隔
                    training_interval = random.uniform(20, 60)  # 20-60秒随机间隔
                    time.sleep(training_interval)

                except Exception as train_error:
                    logging.error(f"客户端 {client_id} 训练过程中出错: {str(train_error)}")
                    time.sleep(30)  # 出错后等待30秒再重试

            logging.info(f"客户端 {client_id} 完成所有训练轮次")

        except Exception as e:
            logging.error(f"客户端 {client_id} 训练线程出错: {str(e)}")

    def should_trigger_aggregation(self):
        """检查是否应该触发聚合"""
        try:
            # 如果没有更新，则不触发聚合
            if not self.success_buffer_pool:
                logging.info(f"缓冲池为空，不触发聚合（缓冲池大小: {len(self.success_buffer_pool)}）")
                return False

            # 获取同步模式标志
            config = Config()
            synchronous_mode = getattr(config.server, 'synchronous_mode', False)  # 默认开启异步模式

            if synchronous_mode:
                # 获取唯一客户端数量和最小阈值
                unique_clients = set(update['client_id'] for update in self.success_buffer_pool)
                min_clients_for_aggregation = getattr(config.server, 'min_clients_for_aggregation', 1)

                # 只要达到最小客户端数量要求就可以聚合
                if len(unique_clients) >= min_clients_for_aggregation:
                    logging.info(f"同步模式下，当前有 {len(unique_clients)} 个客户端更新，满足最小阈值 {min_clients_for_aggregation}，可以触发聚合")
                    return True
                else:
                    logging.debug(f"同步模式下，当前有 {len(unique_clients)} 个客户端更新，未满足最小阈值 {min_clients_for_aggregation}，不触发聚合")
                    return False

            # 异步模式逻辑
            # 获取当前缓冲池中唯一客户端数量
            unique_clients = set(update['client_id'] for update in self.success_buffer_pool)
            unique_client_count = len(unique_clients)

            # 修复单客户端聚合问题：设置更合理的最小聚合客户端数量
            # 默认至少需要2个客户端才能进行聚合，避免每次只聚合一个客户端
            min_clients_for_aggregation = getattr(config.server, 'min_clients_for_aggregation', 2)

            # 但如果配置文件明确设置为1，则尊重配置
            if hasattr(config.server, 'min_clients_for_aggregation') and config.server.min_clients_for_aggregation == 1:
                min_clients_for_aggregation = 1
                logging.warning("配置文件设置min_clients_for_aggregation=1，可能导致每轮只聚合一个客户端")

            # 检查是否有足够的客户端更新
            if unique_client_count >= min_clients_for_aggregation:
                logging.info(f"异步模式下，当前有 {unique_client_count} 个客户端更新，满足最小阈值 {min_clients_for_aggregation}，可以触发聚合")
                return True

            # 检查是否有客户端更新等待时间过长（优化：减少等待时间）
            current_time = time.time()
            aggregation_timeout = getattr(config.server, 'aggregation_timeout', 30)  # 默认30秒超时

            for update_record in self.success_buffer_pool:
                wait_time = current_time - update_record.get('timestamp', current_time)
                if wait_time > aggregation_timeout:
                    client_id = update_record['client_id']
                    logging.info(f"客户端 {client_id} 更新等待时间过长({wait_time:.1f}秒 > {aggregation_timeout}秒)，触发聚合")
                    return True

            # 如果缓冲池中有更新但还没达到超时，记录等待状态
            if unique_client_count > 0:
                max_wait_time = max(current_time - update_record.get('timestamp', current_time)
                                  for update_record in self.success_buffer_pool)
                logging.debug(f"缓冲池中有 {unique_client_count} 个更新，最长等待时间: {max_wait_time:.1f}秒")

            return False
        except Exception as e:
            logging.error(f"检查聚合条件时出错: {str(e)}")
            return False

    async def train_round(self):
        """
        训练一轮
        
        返回:
            bool: 训练是否成功
        """
        # 如果没有注册的客户端，自动创建
        if len(self.clients) == 0:
            config = Config()
            total_clients = getattr(config.clients, 'total_clients', 10)
            logging.info(f"没有注册的客户端，自动创建 {total_clients} 个客户端")
            self.create_clients(total_clients)
        
        try:
            if self.global_weights is None:
                self.initialize_global_weights()
            
            # 不在这里递增global_round，而是在聚合后递增
            logging.info(f"开始第 {self.global_round} 轮训练")
            
            # 检查客户端数量
            if not self.clients:
                logging.warning("没有客户端注册，无法进行训练。尝试重新创建客户端...")
                # 尝试重新创建客户端
                from sc_afl_client import Client as SCClient
                config = Config()
                total_clients = self.total_clients
                id_start = getattr(config.clients, 'id_start', 1)  # 默认从1开始
                logging.info(f"重新创建客户端: 起始ID={id_start}, 总数={total_clients}")

                for i in range(total_clients):
                    client_id = id_start + i  # 使用配置的起始ID
                    try:
                        # 创建客户端实例
                        logging.info(f"开始创建客户端 {client_id}...")
                        client = SCClient(client_id=client_id)
                        logging.info(f"客户端 {client_id} 实例创建成功")

                        # 为客户端设置服务器引用
                        client.set_server_reference(self)
                        logging.info(f"客户端 {client_id} 已设置服务器引用")

                        # 注册客户端到服务器
                        self.register_client(client)
                        logging.info(f"客户端 {client_id} 已成功注册到服务器")
                    except Exception as e:
                        logging.error(f"创建客户端 {client_id} 时出错: {str(e)}")
                        import traceback
                        logging.error(f"客户端创建异常堆栈: {traceback.format_exc()}")
                
                if not self.clients:
                    logging.error("无法创建客户端，跳过本轮训练")
                    return
                logging.info(f"已重新创建 {len(self.clients)} 个客户端")
            
            selected_clients = await self.select_clients()
            if not selected_clients:
                logging.warning("没有可用的客户端，等待下一轮")
                return
            
            train_client_count = 0
            failed_clients = []
            client_updates = []
            
            # 修改: 向所有选中的客户端发送模型，然后等待它们全部完成训练
            for client_id in selected_clients:
                response = await self.send_model_to_client(client_id)
                if response is None:
                    logging.error(f"向客户端{client_id}发送模型失败")
                    failed_clients.append(client_id)
                    continue
                
                if client_id in self.client_status:
                    self.client_status[client_id]['last_model_pull_round'] = self.global_round
                    
                self.client_start_time[client_id] = time.time()
                self.estimate_client_times(client_id)
                train_client_count += 1
            
            if failed_clients:
                logging.warning(f"第 {self.global_round} 轮训练：有 {len(failed_clients)} 个客户端发送模型失败: {failed_clients}")
            
            logging.info(f"第 {self.global_round} 轮训练：已向 {train_client_count} 个客户端下发模型")
            
            # 修改: 等待所有客户端完成训练并上传模型
            logging.info(f"等待所有 {train_client_count} 个客户端完成训练...")
            
            # 设置最大等待时间
            max_wait_time = getattr(Config().clients, 'max_wait_time', 600)  # 默认600秒
            wait_start_time = time.time()
            all_received = False
            
            while not all_received and time.time() - wait_start_time < max_wait_time:
                # 检查是否所有客户端都已上传模型
                updated_clients = [update['client_id'] for update in self.success_buffer_pool 
                                  if update['client_id'] in selected_clients]
                
                if len(updated_clients) >= train_client_count:
                    all_received = True
                    logging.info(f"所有 {train_client_count} 个客户端均已完成训练并上传模型")
                    break
                    
                # 等待一小段时间再检查
                await asyncio.sleep(2)
                
                # 定期输出等待状态
                if (time.time() - wait_start_time) % 30 < 2:  # 大约每30秒输出一次
                    logging.info(f"仍在等待客户端上传: 已收到 {len(updated_clients)}/{train_client_count}")
            
            if not all_received:
                logging.warning(f"等待超时: 只收到 {len(updated_clients)}/{train_client_count} 个客户端的更新")
                
            # 修改: 强制执行聚合
            if self.success_buffer_pool:
                logging.info("所有可用客户端已完成训练，开始执行聚合...")
                await self.check_and_trigger_aggregation()
            else:
                logging.warning("没有收到任何客户端更新，无法执行聚合")
            
        except Exception as e:
            logging.error(f"训练轮次执行失败: {str(e)}")
            import traceback
            logging.error(f"异常堆栈: {traceback.format_exc()}")
            raise
    

        try:
            # 确保准确率数据存在
            if hasattr(self, 'accuracy') and self.accuracy is not None:
                # 设置current_accuracy为accuracy的值
                self.current_accuracy = self.accuracy
                
                if not hasattr(self, 'accuracy_log'):
                    self.accuracy_log = []
                
                self.accuracy_log.append(self.accuracy)
                
                if hasattr(self, 'first_reach_time'):
                    for threshold in self.first_reach_time:
                        if self.first_reach_time[threshold] is None and self.accuracy >= threshold:
                            self.first_reach_time[threshold] = time.time() - self.start_time
                            logging.info(f"首次达到{threshold*100:.1f}%准确率! 耗时: {self.first_reach_time[threshold]:.2f}秒")
                
                # 计算过去10轮的平均准确率
                if not hasattr(self, 'accuracy_history'):
                    self.accuracy_history = []
                    
                self.accuracy_history.append(self.accuracy)
                # 只保留最近10轮的准确率
                if len(self.accuracy_history) > 10:
                    self.accuracy_history.pop(0)
                    
                avg_last_10 = sum(self.accuracy_history) / len(self.accuracy_history)
                
                # 更新最佳准确率
                if not hasattr(self, 'best_accuracy') or self.accuracy > self.best_accuracy:
                    self.best_accuracy = self.accuracy
                
                # 使用正确的轮次记录（聚合后轮次已经+1，所以这里用global_round-1表示当前完成的轮次）
                current_round = max(0, self.global_round-1)
                logging.info("\n【第%d轮结束】\n当前准确率: %f", current_round, self.accuracy)
                logging.info("过去10轮平均准确率: %f, 最佳准确率: %f", avg_last_10, self.best_accuracy)
                
                # 准确率记录已通过Plato标准方式在clients_processed中处理
                
                # 结果记录已通过Plato标准方式在clients_processed中处理
                    
                # 输出到控制台
                print(f"\n【聚合轮次 {current_round}】")
                print(f"当前准确率: {self.accuracy:.4f}")
                print(f"过去10轮平均准确率: {avg_last_10:.4f}, 最佳准确率: {self.best_accuracy:.4f}")
            else:
                logging.warning("准确率未设置或为None，无法记录")
        except Exception as e:
            logging.error(f"记录准确率时出错: {str(e)}")
            import traceback
            logging.error(f"记录准确率异常堆栈: {traceback.format_exc()}")

    def _handle_communication_failure(self, client_id, weights):
        """处理客户端通信失败"""
        try:
            # 记录通信失败
            if not hasattr(self, 'communication_failures'):
                self.communication_failures = {}

            if client_id not in self.communication_failures:
                self.communication_failures[client_id] = 0
            self.communication_failures[client_id] += 1

            # 更新客户端通信成功率（降低）
            if hasattr(self, 'client_beta'):
                current_beta = self.client_beta.get(client_id, 0.8)
                # 失败后降低成功率，但不低于0.1
                new_beta = max(0.1, current_beta * 0.9)
                self.client_beta[client_id] = new_beta

                logging.info(f"客户端 {client_id} 通信失败，成功率从 {current_beta:.3f} 降至 {new_beta:.3f}")

            # 可以在这里添加失败补偿机制（如ReFedSCAFL的蒸馏）
            # 目前采用简单的丢弃策略

        except Exception as e:
            logging.error(f"处理客户端 {client_id} 通信失败时出错: {e}")

    def _update_communication_success_rate(self, client_id, success=True):
        """更新客户端通信成功率"""
        try:
            if not hasattr(self, 'client_beta'):
                self.client_beta = {}

            current_beta = self.client_beta.get(client_id, 0.8)

            if success:
                # 成功时略微提高成功率，但不超过0.99
                new_beta = min(0.99, current_beta * 1.01)
            else:
                # 失败时降低成功率，但不低于0.1
                new_beta = max(0.1, current_beta * 0.95)

            self.client_beta[client_id] = new_beta

        except Exception as e:
            logging.error(f"更新客户端 {client_id} 通信成功率时出错: {e}")

    async def _perform_aggregation(self, updates_to_aggregate, current_D_t):
        """
        执行聚合操作
        
        参数:
            updates_to_aggregate: 要聚合的客户端更新
            current_D_t: 当前计算的D_t值
            
        返回:
            list: 聚合的客户端ID列表
        """
        try:
            aggregation_start_time = time.time()
            client_staleness_records = []

            # 更新网络环境条件
            if self.network_simulator is not None:
                self.network_simulator.update_global_conditions()
                network_stats = self.network_simulator.get_network_statistics()
                logging.info(f"网络环境更新: 状态={network_stats['current_network_condition']}, "
                           f"成功率={network_stats['success_rate']:.2f}, "
                           f"天气影响={network_stats['weather_impact']:.2f}")

            logging.info(f"开始执行聚合 - 参与聚合的客户端数量: {len(updates_to_aggregate)}个")
            
            updates = []
            success_client_ids = []
            
            for update_record in updates_to_aggregate:
                client_id = update_record['client_id']
                success_client_ids.append(client_id)
                
                # 获取客户端实例
                client_instance = self.clients.get(client_id)
                
                # 计算客户端陈旧度（使用聚合前的轮次）
                client_last_pull_round = self.client_status[client_id].get('last_model_pull_round', 0)
                tau_k = max(0, self.global_round - client_last_pull_round)
                
                # 更新客户端训练器的陈旧度
                if client_instance and hasattr(client_instance, 'trainer') and client_instance.trainer:
                    try:
                        client_instance.trainer.update_staleness(self.global_round)
                        # 获取更准确的陈旧度
                        tau_k = client_instance.trainer.staleness
                    except Exception as e:
                        logging.error(f"更新客户端 {client_id} 训练器陈旧度时出错: {str(e)}")
                
                # 记录客户端陈旧度
                client_staleness_records.append((client_id, tau_k))
                logging.debug(f"参与聚合的客户端 {client_id} 陈旧度: {tau_k} (当前服务器轮次:{self.global_round}, 客户端拉取模型轮次:{client_last_pull_round})")
                
                # 更新客户端最后聚合轮次
                self.client_status[client_id]['last_aggregated_round'] = self.global_round
                logging.debug(f"客户端 {client_id} 在服务器轮次 {self.global_round} 被聚合")
                
                # 准备更新对象
                report = update_record['report']
                weights = update_record['weights']
                
                # 验证权重有效性
                if weights is None:
                    logging.error(f"客户端 {client_id} 的权重为None，跳过")
                    continue
                    
                if not isinstance(weights, dict):
                    logging.error(f"客户端 {client_id} 的权重不是字典类型: {type(weights)}，跳过")
                    continue
                    
                if len(weights) == 0:
                    logging.error(f"客户端 {client_id} 的权重为空字典，跳过")
                    continue
                
                # 检查权重是否包含张量
                import torch
                has_tensors = False
                for key, value in weights.items():
                    if isinstance(value, torch.Tensor):
                        has_tensors = True
                        break
                        
                if not has_tensors:
                    logging.error(f"客户端 {client_id} 的权重不包含张量，跳过")
                    continue
                
                # 创建带有陈旧度信息的更新对象
                update = SimpleNamespace()
                update.client_id = client_id
                update.report = report
                update.weights = weights
                update.staleness = tau_k  # 添加陈旧度信息
                
                updates.append(update)
            
            logging.info(f"聚合执行 - 聚合客户端ID: {success_client_ids}")
            
            if not updates:
                logging.warning("没有可聚合的客户端更新，跳过聚合")
                return []
                
            # 初始化陈旧度相关属性（将在稍后由ResultManager计算并更新）
            self.current_avg_staleness = 0
            self.current_max_staleness = 0
            self.current_min_staleness = 0
            self.current_staleness_clients = len(client_staleness_records)
            
            # 聚合计数器递增
            self.aggregation_count += 1
            self.is_aggregating = True
            
            try:
                # 记录聚合前的全局权重摘要
                if self.global_weights is not None:
                    try:
                        all_params_before = torch.cat([v.flatten() for v in self.global_weights.values() if isinstance(v, torch.Tensor)])
                        logging.info(f"[聚合前全局权重] 均值: {all_params_before.mean():.6f}, 最大: {all_params_before.max():.6f}, 最小: {all_params_before.min():.6f}")
                    except Exception as e:
                        logging.error(f"记录聚合前全局权重摘要时出错: {str(e)}")
                
                # 使用自定义的聚合算法
                updated_weights = await self.algorithm.aggregate_weights(updates)
                
                if updated_weights is not None and len(updated_weights) > 0:
                    # 验证聚合后的权重
                    import torch
                    has_tensors = False
                    has_nan = False
                    has_inf = False
                    
                    for key, value in updated_weights.items():
                        if isinstance(value, torch.Tensor):
                            has_tensors = True
                            if torch.isnan(value).any():
                                has_nan = True
                                logging.error(f"聚合后的权重 {key} 包含NaN值")
                            if torch.isinf(value).any():
                                has_inf = True
                                logging.error(f"聚合后的权重 {key} 包含Inf值")
                    
                    if not has_tensors:
                        logging.error("聚合后的权重不包含张量，聚合失败")
                        self.is_aggregating = False
                        return []
                        
                    if has_nan or has_inf:
                        logging.error("聚合后的权重包含NaN或Inf值，聚合失败")
                        self.is_aggregating = False
                        return []
                    
                    # 加载权重到模型
                    self.algorithm.load_weights(updated_weights)
                    self.global_weights = updated_weights
                    self.global_model_cache = updated_weights
                    
                    # 记录聚合后的全局权重摘要
                    try:
                        all_params_after = torch.cat([v.flatten() for v in self.global_weights.values() if isinstance(v, torch.Tensor)])
                        logging.info(f"[聚合后全局权重] 均值: {all_params_after.mean():.6f}, 最大: {all_params_after.max():.6f}, 最小: {all_params_after.min():.6f}")
                        
                        # 计算权重变化
                        if 'all_params_before' in locals():
                            try:
                                weight_diff = torch.abs(all_params_after - all_params_before).mean().item()
                                logging.info(f"[权重变化] 平均绝对差异: {weight_diff:.6f}")
                                
                                # 记录权重变化
                                self.log_weight_change(
                                    weight_diff=weight_diff,
                                    mean=all_params_after.mean().item(),
                                    max_val=all_params_after.max().item(),
                                    min_val=all_params_after.min().item()
                                )
                                
                                if weight_diff < 1e-10:
                                    logging.warning("权重变化极小，可能聚合未生效")
                            except Exception as diff_error:
                                logging.error(f"计算权重差异时出错: {str(diff_error)}")
                    except Exception as e:
                        logging.error(f"记录聚合后全局权重摘要时出错: {str(e)}")
                    
                    logging.info(f"服务器轮次 {self.global_round} 聚合完成: 更新了全局模型")
                else:
                    logging.error(f"服务器轮次 {self.global_round} 聚合失败: 聚合结果为空")
                    
            except Exception as e:
                logging.error(f"模型聚合失败: {str(e)}")
                import traceback
                logging.error(f"聚合异常堆栈: {traceback.format_exc()}")
            
            self.is_aggregating = False
            
            # 记录聚合相关统计信息
            aggregation_time = time.time() - aggregation_start_time

            # 在异步模式下手动更新wall_time以支持时间模拟
            logging.info(f"[DEBUG] simulate_wall_time={self.simulate_wall_time}, asynchronous_mode={self.asynchronous_mode}")
            if self.simulate_wall_time and self.asynchronous_mode:
                old_wall_time = self.wall_time
                # 累加聚合时间到模拟时间
                self.wall_time += aggregation_time
                # 添加网络通信时间模拟
                if hasattr(self, 'network_simulator'):
                    comm_time = len(success_client_ids) * 0.5  # 每个客户端0.5秒通信时间
                    self.wall_time += comm_time
                logging.info(f"[DEBUG] wall_time更新: {old_wall_time} -> {self.wall_time} (增加了{self.wall_time - old_wall_time}秒)")
            else:
                logging.info(f"[DEBUG] 跳过wall_time更新，条件不满足")

            # 计算elapsed_time - 与FADAS保持一致的计算方式
            elapsed_time = self.wall_time - self.initial_wall_time
            
            # 记录当前轮次的D_t值和聚合时间
            self.historical_Ds.append(current_D_t)
            self.historical_agg_round_durations[self.global_round] = aggregation_time  # 使用实际聚合时间而非预估值

            # 保存当前轮次用于结果记录（在递增之前保存）
            completed_round = self.global_round
            logging.info(f"记录轮次: {completed_round}")

            # 递增全局轮次计数器 - 确保只在聚合完全成功后递增一次
            self.global_round += 1
            logging.info(f"聚合完成，进入新轮次，当前全局轮次: {self.global_round}")

            # 更新最后聚合时间
            self.last_aggregation_time = time.time()
            logging.info(f"更新最后聚合时间: {self.last_aggregation_time}")

            # 检查是否需要评估全局模型（性能优化）
            should_evaluate = True
            eval_frequency = getattr(Config().server, 'eval_frequency', 1)  # 默认每轮都评估

            if eval_frequency > 1:
                # 只在特定轮次进行评估以节省CPU时间
                if completed_round % eval_frequency != 0:
                    should_evaluate = False
                    logging.info(f"跳过第 {completed_round} 轮的全局模型评估（评估频率: 每{eval_frequency}轮）")

            # 评估全局模型
            accuracy = None
            if should_evaluate and hasattr(self, 'testset') and self.testset is not None:
                try:
                    # 确保训练器已正确初始化
                    if self.trainer is None:
                        logging.warning("训练器为None，创建新的训练器用于评估模型")
                        from sc_afl_trainer import Trainer
                        self.trainer = Trainer(model=self.model, client_id=0)  # 显式设置client_id为0
                        logging.info("为评估创建的训练器设置client_id=0")
                    elif self.trainer.model is None:
                        logging.warning("训练器的模型为None，设置为当前全局模型")
                        self.trainer.model = self.model
                    
                    # 验证训练器是否正确初始化并具有必要的方法
                    if not hasattr(self.trainer, 'test') or not callable(self.trainer.test):
                        logging.error("训练器没有test方法或它不可调用，重新创建训练器")
                        from sc_afl_trainer import Trainer
                        self.trainer = Trainer(model=self.model, client_id=0)  # 显式设置client_id为0
                        logging.info("重新创建的训练器设置client_id=0")
                    
                    # 确保模型已加载最新权重
                    if self.model is not None and self.global_weights is not None:
                        try:
                            # 检查通道数是否匹配
                            model_in_channels = None
                            weight_in_channels = None
                            
                            if hasattr(self.model, 'conv1') and hasattr(self.model.conv1, 'weight'):
                                model_in_channels = self.model.conv1.weight.shape[1]
                            elif hasattr(self.model, 'in_channels'):
                                model_in_channels = self.model.in_channels
                                
                            if 'conv1.weight' in self.global_weights:
                                weight_in_channels = self.global_weights['conv1.weight'].shape[1]
                                
                            if model_in_channels is not None and weight_in_channels is not None and model_in_channels != weight_in_channels:
                                logging.warning(f"模型通道数({model_in_channels})与权重通道数({weight_in_channels})不匹配，重新创建模型")
                                # 使用动态加载器重新创建模型
                                try:
                                    from dynamic_loader import DynamicModelLoader, ConfigurationManager
                                    config = Config()
                                    dataset_name, num_classes, _ = ConfigurationManager.get_dataset_info_from_config(config)
                                    model_name = ConfigurationManager.get_model_info_from_config(config)
                                    self.model = DynamicModelLoader.create_model(
                                        model_name, num_classes=num_classes, in_channels=weight_in_channels
                                    )
                                    logging.info(f"已动态创建新模型以匹配权重通道数: {weight_in_channels}")
                                except Exception as e:
                                    logging.error(f"动态创建模型失败: {e}")
                                    # 回退到LeNet5
                                    from plato.models import lenet5
                                    self.model = lenet5.Model(num_classes=10, in_channels=weight_in_channels)
                                    logging.warning(f"回退到LeNet5模型，通道数: {weight_in_channels}")
                                
                            self.model.load_state_dict(self.global_weights)
                            # 确保训练器的模型也使用最新权重 - 使用深拷贝避免模型实例共享
                            if self.trainer.model is not self.model:
                                # 创建模型的深拷贝，避免多个客户端共享同一个模型实例
                                import copy
                                import torch
                                self.trainer.model = copy.deepcopy(self.model)

                                # 重置BatchNorm层的统计信息，避免共享running_mean和running_var
                                for module in self.trainer.model.modules():
                                    if isinstance(module, torch.nn.BatchNorm2d):
                                        module.reset_running_stats()
                                        module.momentum = 0.1
                                        module.track_running_stats = True

                                # 设置设备
                                if hasattr(self.trainer, 'device'):
                                    device = self.trainer.device
                                    # 确保模型在正确的设备上
                                    self.trainer.model = self.trainer.model.to(device)
                                    logging.info(f"为训练器创建了模型的深拷贝，重置BatchNorm统计信息，并移至设备: {device}")
                                else:
                                    logging.info(f"为训练器创建了模型的深拷贝，重置BatchNorm统计信息，避免实例共享")
                            logging.info("已将全局权重加载到评估模型中")
                        except Exception as load_error:
                            logging.error(f"加载全局权重到评估模型时出错: {str(load_error)}")
                            import traceback
                            logging.error(f"加载权重异常堆栈: {traceback.format_exc()}")
                    
                    logging.info(f"开始评估全局模型，测试集大小: {len(self.testset)}")
                    eval_start_time = time.time()
                    
                    accuracy = self.trainer.test(self.testset)
                    
                    eval_time = time.time() - eval_start_time
                    logging.info(f"全局模型评估完成，准确率: {accuracy:.4f}, 耗时: {eval_time:.2f}秒")
                    
                    self.accuracy = accuracy
                    self.current_accuracy = accuracy  # 确保current_accuracy也被设置
                    if hasattr(self, 'accuracy_log'):
                        self.accuracy_log.append(accuracy)
                    else:
                        self.accuracy_log = [accuracy]
                    
                    if hasattr(self, 'first_reach_time'):
                        for threshold in self.first_reach_time:
                            if self.first_reach_time[threshold] is None and accuracy >= threshold:
                                self.first_reach_time[threshold] = elapsed_time
                                logging.info(f"首次达到{threshold*100:.1f}%准确率! 耗时: {elapsed_time:.2f}秒")
                    
                    # 显式调用log_accuracy方法记录结果
                    self.log_accuracy()
                    
                except Exception as e:
                    logging.error(f"全局模型评估出错: {str(e)}")
                    import traceback
                    logging.error(f"评估异常堆栈: {traceback.format_exc()}")
                    accuracy = None
            else:
                logging.warning("服务器未分配测试集，无法评估全局模型准确率")
                try:
                    # 尝试从数据源重新获取测试集
                    logging.info("尝试从数据源重新加载测试集...")
                    from plato.datasources import registry as datasources_registry
                    try:
                        datasource = datasources_registry.get(client_id=0)
                        if datasource is not None:
                            self.testset = datasource.get_test_set()
                            logging.info(f"成功加载测试集，大小: {len(self.testset)}")
                            # 递归调用自己来完成评估
                            return await self._perform_aggregation(updates_to_aggregate, current_D_t)
                    except Exception as ds_error:
                        logging.error(f"重新加载测试集失败: {str(ds_error)}")
                except Exception as fallback_error:
                    logging.error(f"尝试恢复测试集失败: {str(fallback_error)}")
            
            # 计算陈旧度信息
            staleness_values = [tau for _, tau in client_staleness_records]
            if staleness_values:
                avg_staleness = sum(staleness_values) / len(staleness_values)
                max_staleness = max(staleness_values)
                min_staleness = min(staleness_values)

                # 更新服务器的陈旧度相关属性
                self.current_avg_staleness = avg_staleness
                self.current_max_staleness = max_staleness
                self.current_min_staleness = min_staleness

                # 陈旧度信息已通过Plato标准方式记录
            else:
                self.current_avg_staleness = 0
                self.current_max_staleness = 0
                self.current_min_staleness = 0

            # 记录基本信息到日志
            logging.info(f"轮次: {self.global_round}, 准确率: {accuracy}, 平均陈旧度: {self.current_avg_staleness}")
            for client_id, tau in client_staleness_records:
                logging.info(f"客户端 {client_id} 陈旧度: {tau}")
            
            # 记录全局模型权重信息并计算权重变化统计
            try:
                all_params = torch.cat([v.flatten() for v in self.global_weights.values() if isinstance(v, torch.Tensor)])

                # 计算权重变化统计
                if hasattr(self, 'previous_global_weights') and self.previous_global_weights is not None:
                    # 计算权重变化
                    prev_params = torch.cat([v.flatten() for v in self.previous_global_weights.values() if isinstance(v, torch.Tensor)])
                    weight_diff = all_params - prev_params
                    self.current_weight_change_mean = weight_diff.mean().item()
                    self.current_weight_change_std = weight_diff.std().item()

                    logging.info(f"[权重变化] 轮次: {self.global_round} | 均值: {self.current_weight_change_mean:.6f}, 标准差: {self.current_weight_change_std:.6f}")
                else:
                    # 第一轮没有权重变化
                    self.current_weight_change_mean = 0.0
                    self.current_weight_change_std = 0.0

                # 保存当前权重作为下一轮的参考
                self.previous_global_weights = {k: v.clone() if isinstance(v, torch.Tensor) else v for k, v in self.global_weights.items()}

                logging.info(f"[全局模型摘要] 轮次: {self.global_round} | 均值: {all_params.mean():.6f}, 最大: {all_params.max():.6f}, 最小: {all_params.min():.6f}")
            except Exception as e:
                logging.error(f"记录模型权重摘要时出错: {str(e)}")
                # 设置默认值
                self.current_weight_change_mean = 0.0
                self.current_weight_change_std = 0.0
            
            # 记录所有训练统计信息到统一的CSV文件
            if hasattr(self, 'result_csv_file') and hasattr(self, 'recorded_items'):
                try:
                    # 计算经过的时间 - 与FADAS保持一致的计算方式
                    # 使用模拟的wall_time而不是真实时间，以便与FADAS对比
                    # 为了确保每次记录的elapsed_time都是唯一的，我们在每次记录时更新wall_time
                    if self.simulate_wall_time and self.asynchronous_mode:
                        # 为每次结果记录添加少量时间增量，确保时间的唯一性
                        self.wall_time += 0.1  # 每次记录增加0.1秒

                    elapsed_time = self.wall_time - self.initial_wall_time
                    logging.info(f"[DEBUG] 结果记录时 - wall_time={self.wall_time}, initial_wall_time={self.initial_wall_time}, elapsed_time={elapsed_time}")

                    # 计算客户端平均准确率（从参与聚合的客户端报告中计算）
                    client_accuracies = []
                    for update in updates_to_aggregate:
                        if 'report' in update and hasattr(update['report'], 'accuracy'):
                            client_accuracies.append(update['report'].accuracy)

                    if client_accuracies:
                        avg_client_accuracy = sum(client_accuracies) / len(client_accuracies)
                        logging.info(f"参与聚合的客户端平均准确率: {avg_client_accuracy:.4f} (基于 {len(client_accuracies)} 个客户端)")
                    else:
                        avg_client_accuracy = 0.0
                        logging.warning("没有找到客户端准确率信息，使用0.0")

                    # 计算全局准确率统计（服务器在测试集上的评估结果）
                    global_accuracy = accuracy if accuracy is not None else 0.0  # 全局模型在服务器测试集上的准确率
                    global_accuracy_std = 0.0   # 标准差设为0（单一模型测试）

                    # 准确率含义说明：
                    # - accuracy: 参与聚合的客户端在其本地数据上的平均准确率
                    # - global_accuracy: 聚合后的全局模型在服务器测试集上的准确率

                    # 计算陈旧度统计
                    avg_staleness = getattr(self, 'current_avg_staleness', 0.0)
                    max_staleness = getattr(self, 'current_max_staleness', 0.0)
                    min_staleness = getattr(self, 'current_min_staleness', 0.0)
                    num_clients = len(getattr(self, 'success_buffered_clients', []))

                    # 计算权重变化统计
                    weight_change_mean = getattr(self, 'current_weight_change_mean', 0.0)
                    weight_change_std = getattr(self, 'current_weight_change_std', 0.0)

                    # 获取网络统计信息
                    network_latency = getattr(self, 'current_network_latency', 0.0)
                    network_bandwidth = getattr(self, 'current_network_bandwidth', 0.0)
                    network_reliability = getattr(self, 'current_network_reliability', 1.0)

                    # 获取网络环境状态
                    if self.network_simulator is not None:
                        network_stats = self.network_simulator.get_network_statistics()
                        network_condition = network_stats.get('current_network_condition', 'normal')
                        network_success_rate = network_stats.get('success_rate', 1.0)
                    else:
                        network_condition = 'normal'
                        network_success_rate = 1.0

                    # 准备要记录的所有数据（与ReFedScaFL格式一致）
                    # 使用在聚合开始时保存的completed_round确保轮次记录的正确性
                    logged_items = {
                        'round': completed_round,  # 使用已完成的轮次号
                        'elapsed_time': elapsed_time,
                        'accuracy': avg_client_accuracy,  # 客户端平均准确率
                        'global_accuracy': global_accuracy,  # 全局模型在测试集上的准确率
                        'global_accuracy_std': global_accuracy_std,  # 保持小数格式
                        'avg_staleness': avg_staleness,
                        'max_staleness': max_staleness,
                        'min_staleness': min_staleness,
                        'virtual_time': getattr(self, 'virtual_time', 0.0),  # 虚拟时间记录
                        'aggregated_clients_count': len(success_client_ids),  # 聚合客户端数量
                        'num_clients': num_clients,
                        'weight_change_mean': weight_change_mean,
                        'weight_change_std': weight_change_std,
                        'network_latency': network_latency,
                        'network_bandwidth': network_bandwidth,
                        'network_reliability': network_reliability,
                        'network_condition': network_condition,
                        'network_success_rate': network_success_rate
                    }

                    # 按照配置文件中定义的顺序准备数据行
                    new_row = []
                    for item in self.recorded_items:
                        if item in logged_items:
                            new_row.append(logged_items[item])
                        else:
                            new_row.append(0.0)  # 默认值

                    # 使用Plato标准方式写入CSV文件
                    csv_processor.write_csv(self.result_csv_file, new_row)

                    logging.info(f"所有训练统计信息已记录到统一结果文件: {self.result_csv_file}")
                    logging.info(f"记录数据: {dict(zip(self.recorded_items, new_row))}")

                except Exception as e:
                    logging.error(f"记录训练统计信息时出错: {str(e)}")
                    import traceback
                    logging.error(f"异常堆栈: {traceback.format_exc()}")

            # 检查是否完成了所有轮次
            if self.global_round > self.total_rounds:
                logging.info(f"完成所有 {self.total_rounds} 轮训练，当前轮次: {self.global_round-1}")
                # 注意不要立即退出，因为我们在异步环境中

            return success_client_ids
        except Exception as e:
            logging.error(f"模型聚合失败: {str(e)}")
            import traceback
            logging.error(f"聚合异常堆栈: {traceback.format_exc()}")
            return []

    def __del__(self):
        try:
            # Plato标准结果文件会自动处理关闭
            logging.info("SCAFL服务器已清理")
        except Exception as e:
            logging.error(f"清理服务器时出错: {str(e)}")

    async def check_and_trigger_aggregation(self):
        """
        检查并触发聚合过程

        实现SC-AFL算法1的贪心选择策略：
        1. 从所有待聚合的客户端中，根据d_k^t（实际训练时间）排序
        2. 增量评估目标函数P2，选择最优客户端子集进行聚合

        目标函数: min β(t) * V * D_t + Σ Q_k(t) * ((τ_k(t)+1)(1-β_k^t) - τ_max)

        返回：
            bool: 是否成功触发了聚合
        """
        async with self.aggregation_lock:
            if self.aggregation_in_progress:
                logging.debug("聚合已在进行中，跳过此次触发")
                return False

            # 1. 设置聚合进行中标志（外层已经检查过聚合条件）
            self.aggregation_in_progress = True
            logging.info(f"🔒 开始聚合过程，轮次: {self.global_round}")

            try:
                # 2. 获取所有已上传的客户端更新，并计算排序依据
                candidate_updates = []
                for update_record in self.success_buffer_pool:
                    client_id = update_record['client_id']

                    # 确保客户端时间估计已计算
                    if client_id not in self.client_Hk:
                        self.estimate_client_times(client_id)

                    # 使用client_Hk作为d_k^t（实际训练时间的估计）
                    # 这更符合理论中"优先聚合训练时间最短的客户端"
                    sorting_metric = self.client_Hk.get(client_id, 5.0)  # 默认5秒

                    # 计算其他用于目标函数评估的参数
                    last_pull_round = self.client_status.get(client_id, {}).get('last_model_pull_round', 0)
                    tau_k = max(0, self.global_round - last_pull_round)
                    q_k = self.staleness_queue.get(client_id, 0.0)

                    candidate_updates.append({
                        'client_id': client_id,
                        'update_record': update_record,
                        'sorting_metric': sorting_metric,  # 理论中的d_k^t
                        'tau_k': tau_k,
                        'q_k': q_k
                    })

                if not candidate_updates:
                    logging.warning("没有有效的候选更新，无法执行聚合")
                    return False

                # 3. 排序：根据d_k^t（sorting_metric）升序排序
                candidate_updates.sort(key=lambda x: x['sorting_metric'])

                client_delays = [(c['client_id'], c['sorting_metric']) for c in candidate_updates]
                logging.info(f"候选客户端按训练时间排序: {client_delays}")

                # 4. 贪心选择最优客户端子集
                best_aggregation_set = []
                min_objective_value = float('inf')

                # 获取算法参数
                V = getattr(self, 'V', 1.0)  # 延迟权重参数
                max_aggregation_clients = getattr(self, 'max_aggregation_clients', 8)  # 最大聚合客户端数

                # 确保tau_max已初始化
                if not hasattr(self, 'tau_max'):
                    self.tau_max = getattr(Config().algorithm, 'tau_max', 5)

                logging.info(f"算法参数: V={V}, max_clients={max_aggregation_clients}, tau_max={self.tau_max}")

                # 5. 增量聚合与评估：逐个添加客户端并计算目标函数
                current_aggregation_trial = []  # 存储当前尝试聚合的客户端

                for client_info in candidate_updates:
                    if len(current_aggregation_trial) >= max_aggregation_clients:
                        break  # 达到最大聚合客户端数限制

                    # 检查零训练时间客户端优先聚合（理论中 if d_k^t=0, must be aggregated）
                    if (client_info['sorting_metric'] == 0.0 and
                        client_info['client_id'] not in [c['client_id'] for c in current_aggregation_trial]):
                        current_aggregation_trial.append(client_info)
                        logging.debug(f"优先聚合零训练时间客户端 {client_info['client_id']}")
                        continue

                    # 尝试添加当前客户端
                    trial_set = current_aggregation_trial + [client_info]

                    # 计算D_t：聚合客户端中最大的d_k^t
                    simulated_D_t = max([c['sorting_metric'] for c in trial_set]) if trial_set else 0.0

                    # 计算目标函数P2的值: V*D_t + Σ Q_k(t) * ((τ_k(t)+1)(1-β_k^t) - τ_max)
                    objective_value = V * simulated_D_t  # V*D_t项

                    # 计算惩罚项：对所有已上传到缓冲池的客户端
                    penalty_term = 0.0
                    all_buffered_client_ids = {u['client_id'] for u in self.success_buffer_pool}
                    selected_client_ids_in_trial = {c['client_id'] for c in trial_set}

                    for cid in all_buffered_client_ids:
                        # 从candidate_updates中找到对应的客户端信息
                        client_info_full = next((info for info in candidate_updates if info['client_id'] == cid), None)
                        if client_info_full:
                            q_k_val = client_info_full['q_k']
                            tau_k_val = client_info_full['tau_k']

                            if cid in selected_client_ids_in_trial:
                                # β_k^t = 1，惩罚项中的(1-β_k^t) = 0
                                penalty_term += q_k_val * (0 - self.tau_max)  # = -q_k_val * tau_max
                            else:
                                # β_k^t = 0，惩罚项中的(1-β_k^t) = 1
                                penalty_term += q_k_val * ((tau_k_val + 1) - self.tau_max)

                    objective_value += penalty_term

                    logging.debug(f"尝试聚合 {len(trial_set)} 客户端 (当前客户端: {client_info['client_id']})")
                    logging.debug(f"  模拟D_t: {simulated_D_t:.4f}, V*D_t: {V * simulated_D_t:.4f}")
                    logging.debug(f"  惩罚项: {penalty_term:.4f}, 总目标值: {objective_value:.4f}")

                    # 更新最优集合
                    if objective_value < min_objective_value:
                        min_objective_value = objective_value
                        best_aggregation_set = trial_set.copy()
                        logging.debug(f"更新最佳集合：目标值 {objective_value:.4f} < 之前最小 {min_objective_value:.4f}")

                    # 将当前客户端加入到当前尝试聚合的集合中，以便下一次循环继续增量
                    current_aggregation_trial.append(client_info)

                # 6. 最终选择用于聚合的客户端更新
                updates_to_aggregate = [c['update_record'] for c in best_aggregation_set]

                if not updates_to_aggregate:
                    logging.warning("根据贪心策略，没有选择任何客户端进行聚合")
                    return False

                # 提取实际聚合的客户端ID
                aggregated_client_ids = [u['client_id'] for u in updates_to_aggregate]
                logging.info(f"贪心策略选择 {len(aggregated_client_ids)} 个客户端进行聚合: {aggregated_client_ids}")
                logging.info(f"最优目标函数值: {min_objective_value:.4f}")

                # 计算当前D_t（使用被选中客户端的最大d_k^t）
                current_D_t_for_agg = max([c['sorting_metric'] for c in best_aggregation_set]) if best_aggregation_set else 0.0

                # 7. 执行聚合
                actual_aggregated_ids = await self._perform_aggregation(updates_to_aggregate, current_D_t_for_agg)

                if not actual_aggregated_ids:
                    logging.warning(f"第 {self.global_round} 轮聚合失败")
                    return False

                # 8. 更新客户端陈旧度队列Q_k（按照理论公式）
                for cid in self.clients.keys():
                    last_pull_round = self.client_status.get(cid, {}).get('last_model_pull_round', 0)
                    tau_k = max(0, self.global_round - last_pull_round)  # 注意：global_round在_perform_aggregation中已经递增

                    prev_q = self.staleness_queue.get(cid, 0.0)

                    if cid in actual_aggregated_ids:
                        beta_k_t = 1
                    else:
                        beta_k_t = 0

                    # 更新Q_k(t+1) = max{Q_k(t) + (τ_k(t)+1)(1-β_k^t) - τ_max, 0}
                    self.staleness_queue[cid] = max(prev_q + (tau_k + 1) * (1 - beta_k_t) - self.tau_max, 0.0)
                    self.client_staleness[cid] = tau_k  # 更新实际陈旧度
                    self.client_Q_values[cid] = self.staleness_queue[cid]  # 记录Q_k值

                    logging.debug(f"聚合后客户端 {cid} 陈旧度: {tau_k}, Q_k 更新为: {self.staleness_queue[cid]:.4f}")

                # 9. 清空已聚合的客户端更新
                self.success_buffer_pool = [
                    update_record for update_record in self.success_buffer_pool
                    if update_record['client_id'] not in actual_aggregated_ids
                ]
                self.success_buffered_clients = [
                    cid for cid in self.success_buffered_clients
                    if cid not in actual_aggregated_ids
                ]

                remaining_unique_clients = len(set(update['client_id'] for update in self.success_buffer_pool))
                logging.info(f"聚合完成，缓冲池中还剩 {len(self.success_buffer_pool)} 个更新，来自 {remaining_unique_clients} 个不同客户端")
                return True

            except Exception as inner_e:
                logging.error(f"聚合执行过程中出错: {str(inner_e)}")
                import traceback
                logging.error(f"内部聚合异常堆栈: {traceback.format_exc()}")
                return False
            finally:
                self.aggregation_in_progress = False
                logging.info(f"🔓 聚合过程结束，轮次: {self.global_round}")


    async def receive_update_from_client(self, payload):
        """接收并处理客户端更新
        
        参数:
            payload: 客户端上传的数据
            
        返回:
            bool: 处理是否成功
        """
        try:
            if payload is None:
                logging.error("接收到的payload为None")
                return False
                
            client_id = getattr(payload, 'client_id', None)
            if client_id is None:
                logging.error("payload中没有client_id")
                return False
                
            if isinstance(client_id, str) and client_id.isdigit():
                client_id = int(client_id)
                
            weights = getattr(payload, 'weights', None)
            if weights is None:
                logging.error(f"客户端 {client_id} 的weights为None")
                return False
                
            report = getattr(payload, 'report', None)
            if report is None:
                logging.error(f"客户端 {client_id} 的report为None")
                return False
                
            logging.info(f"[服务器] 🔄 收到客户端 {client_id} 的模型更新，模型版本: {getattr(payload, 'model_version', 'unknown')}")
            logging.info(f"[服务器] 客户端 {client_id} 训练信息 - 样本数: {getattr(report, 'num_samples', 0)}, 训练时间: {getattr(report, 'training_time', 0):.2f}秒")
            logging.info(f"[服务器] 当前缓冲池大小: {len(self.success_buffer_pool)}, 全局轮次: {self.global_round}")
            
            # 更详细地检查权重格式
            try:
                import torch
                valid_weights = {}
                tensor_count = 0
                
                for key, value in weights.items():
                    if isinstance(value, torch.Tensor):
                        tensor_count += 1
                        valid_weights[key] = value
                    elif isinstance(value, (list, tuple, np.ndarray)):
                        # 尝试转换为torch.Tensor
                        valid_weights[key] = torch.tensor(value)
                        tensor_count += 1
                    else:
                        logging.warning(f"客户端 {client_id} 的权重 {key} 不是张量类型: {type(value)}")
                
                if tensor_count == 0:
                    logging.error(f"客户端 {client_id} 没有有效的张量权重")
                    return False
                
                # 替换为处理过的权重
                if len(valid_weights) < len(weights):
                    logging.warning(f"客户端 {client_id} 的权重中有 {len(weights) - len(valid_weights)} 个不是有效张量，已过滤")
                    weights = valid_weights
            except Exception as e:
                logging.error(f"处理客户端 {client_id} 权重时出错: {str(e)}")
                import traceback
                logging.error(f"权重处理异常堆栈: {traceback.format_exc()}")
            
            # 使用处理后的权重
            success = await self.process_client_update(client_id, report, weights)
            
            if success:
                logging.info(f"成功处理客户端 {client_id} 的更新")
            else:
                logging.error(f"处理客户端 {client_id} 的更新失败")
                
            return success
            
        except Exception as e:
            logging.error(f"接收客户端更新时出错: {str(e)}")
            import traceback
            logging.error(f"异常堆栈: {traceback.format_exc()}")
            return False

    # 已删除冗余的check_client_uploads调试方法

    def create_clients(self, num_clients):
        """
        创建指定数量的客户端

        参数:
            num_clients: 要创建的客户端数量

        返回:
            list: 创建的客户端列表
        """
        from sc_afl_client import Client as SCClient
        import asyncio

        created_clients = []
        max_retries = 3  # 最大重试次数

        for i in range(num_clients):
            client_created = False
            retry_count = 0

            while not client_created and retry_count < max_retries:
                try:
                    # 使用ID管理器获取有效的客户端ID
                    client_id = self.id_manager.get_valid_id(None)
                    logging.info(f"尝试创建客户端 {client_id} (第 {retry_count + 1} 次尝试)")

                    # 创建客户端时使用try-catch包装
                    client = SCClient(client_id=client_id)

                    # 为客户端设置服务器引用
                    client.set_server_reference(self)

                    # 注册客户端
                    self.register_client(client)

                    # 为客户端估计时间
                    self.estimate_client_times(client_id)

                    created_clients.append(client)
                    logging.info(f"✅ 成功创建客户端 {client_id}")
                    client_created = True

                except Exception as e:
                    retry_count += 1
                    logging.error(f"❌ 创建客户端 {client_id} 失败 (第 {retry_count} 次尝试): {str(e)}")
                    if retry_count >= max_retries:
                        logging.error(f"❌ 客户端 {client_id} 创建失败，已达到最大重试次数")
                    else:
                        import time
                        time.sleep(1)  # 等待1秒后重试

            # 只有成功创建的客户端才启动训练任务
            if client_created and len(created_clients) > 0:
                client = created_clients[-1]  # 获取最后创建的客户端
                client_id = client.client_id

                # 启动客户端异步训练任务（使用统一的训练函数）
                try:
                    training_thread = threading.Thread(
                        target=self._start_client_training_thread,
                        args=(client, client_id),
                        daemon=True
                    )
                    training_thread.start()
                    logging.info(f"客户端 {client_id} 异步训练线程已启动")

                except Exception as e:
                    logging.error(f"启动客户端 {client_id} 异步任务时出错: {str(e)}")

        logging.info(f"客户端创建完成，成功创建 {len(created_clients)} 个客户端")
        return created_clients

    def _init_results_path(self):
        """初始化结果保存路径（使用配置文件名+时间戳命名）"""
        try:
            # 使用Plato标准的结果路径配置
            result_path = Config().params["result_path"]
            result_types = Config().params["result_types"]

            logging.info(f"使用结果路径: {result_path}")
            logging.info(f"结果类型: {result_types}")

            # 确保目录存在
            os.makedirs(result_path, exist_ok=True)

            # 生成基于配置文件名和时间戳的文件名
            config_filename = self._get_config_filename()
            timestamp = time.strftime("%Y%m%d_%H%M%S", time.localtime())
            result_filename = f"{config_filename}_{timestamp}.csv"

            # 初始化标准结果CSV文件
            result_csv_file = f"{result_path}/{result_filename}"
            recorded_items = [x.strip() for x in result_types.split(",")]

            csv_processor.initialize_csv(
                result_csv_file, recorded_items, result_path
            )

            self.result_csv_file = result_csv_file
            self.recorded_items = recorded_items

            logging.info(f"初始化结果文件: {result_csv_file}")
            logging.info(f"记录字段: {recorded_items}")

        except Exception as e:
            logging.error(f"初始化结果路径失败: {str(e)}")
            import traceback
            logging.error(f"异常堆栈: {traceback.format_exc()}")

    def _get_config_filename(self):
        """获取配置文件名（不含扩展名）"""
        try:
            import sys
            # 从命令行参数中获取配置文件名
            config_file = None
            for i, arg in enumerate(sys.argv):
                if arg == '-c' and i + 1 < len(sys.argv):
                    config_file = sys.argv[i + 1]
                    break
                elif arg.startswith('--config='):
                    config_file = arg.split('=', 1)[1]
                    break

            if config_file:
                # 提取文件名（不含路径和扩展名）
                import os
                filename = os.path.basename(config_file)
                name_without_ext = os.path.splitext(filename)[0]
                logging.info(f"从命令行获取配置文件名: {name_without_ext}")
                return name_without_ext
            else:
                # 如果无法从命令行获取，使用默认名称
                logging.warning("无法从命令行获取配置文件名，使用默认名称")
                return "scafl_experiment"

        except Exception as e:
            logging.error(f"获取配置文件名失败: {str(e)}")
            return "scafl_experiment"



    def initialize_global_weights(self):
        """初始化全局模型权重"""
        try:
            logging.info("开始初始化全局模型权重")
            
            if self.model is None:
                logging.warning("全局模型实例为None，正在尝试重新创建...")
                try:
                    from dynamic_loader import DynamicModelLoader, ConfigurationManager
                    config = Config()

                    # 获取配置信息
                    dataset_name, num_classes, in_channels = ConfigurationManager.get_dataset_info_from_config(config)
                    model_name = ConfigurationManager.get_model_info_from_config(config)

                    # 自动配置参数
                    ConfigurationManager.auto_configure_from_dataset(config, dataset_name)

                    # 动态创建模型
                    self.model = DynamicModelLoader.create_model(
                        model_name, num_classes=num_classes, in_channels=in_channels
                    )

                    logging.info(f"成功重新创建了全局模型实例 {model_name}，数据集: {dataset_name}, 输入通道数: {in_channels}, 类别数: {num_classes}")

                except Exception as model_err:
                    logging.error(f"动态重新创建模型失败: {str(model_err)}")
                    # 回退到默认模型
                    try:
                        from plato.models import lenet5
                        self.model = lenet5.Model(num_classes=10, in_channels=1)
                        logging.warning("回退到默认LeNet5模型")
                    except Exception as fallback_err:
                        logging.error(f"回退模型创建也失败: {fallback_err}")
                        raise ValueError("无法初始化全局权重：模型实例重新创建失败")
                
            if not hasattr(self.model, 'state_dict'):
                raise ValueError("无法初始化全局权重：模型实例没有state_dict方法")
            
            self.global_weights = self.model.state_dict()
            
            if self.global_weights is None:
                raise ValueError("无法初始化全局权重：从模型提取的权重为None")
            
            if not isinstance(self.global_weights, dict):
                raise ValueError(f"无法初始化全局权重：从模型提取的权重不是字典类型: {type(self.global_weights)}")
            
            if len(self.global_weights) == 0:
                raise ValueError("无法初始化全局权重：从模型提取的权重为空字典")
            
            self.global_model_cache = self.global_weights
            
            import torch
            all_params = torch.cat([v.flatten() for v in self.global_weights.values() if isinstance(v, torch.Tensor)])
            logging.info(f"[全局权重摘要] 参数数量: {len(self.global_weights)}, 均值: {all_params.mean():.6f}, 最大: {all_params.max():.6f}, 最小: {all_params.min():.6f}")
            
            # 检查模型的输入通道数
            if hasattr(self.model, 'conv1') and hasattr(self.model.conv1, 'weight'):
                in_channels = self.model.conv1.weight.shape[1]
                logging.info(f"[全局模型] 输入通道数: {in_channels}")
            elif hasattr(self.model, 'in_channels'):
                logging.info(f"[全局模型] 记录的输入通道数: {self.model.in_channels}")
            
            logging.info("全局模型权重初始化成功")
            return True
        except Exception as e:
            logging.error(f"初始化全局模型权重失败: {str(e)}")
            import traceback
            logging.error(f"异常堆栈: {traceback.format_exc()}")
            raise

    def log_weight_change(self, weight_diff, mean, max_val, min_val):
        """记录模型权重变化信息到CSV文件
        
        Args:
            weight_diff: 权重变化量
            mean: 平均值
            max_val: 最大值
            min_val: 最小值
        """
        try:
            if not hasattr(self, 'weights_change_file'):
                # 初始化权重变化CSV文件
                try:
                    # 使用logs目录而不是results目录
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    base_dir = os.path.join(current_dir, 'logs')
                    os.makedirs(base_dir, exist_ok=True)
                    timestamp = time.strftime("%Y%m%d_%H%M%S", time.localtime())
                    self.weights_change_file = f"{base_dir}/weights_change_{timestamp}.csv"
                    with open(self.weights_change_file, 'w', encoding='utf-8') as f:
                        f.write("round,weight_diff,mean,max,val,min_val,timestamp\n")
                    logging.info(f"✅ 创建权重变化记录文件: {self.weights_change_file}")
                except Exception as create_err:
                    logging.error(f"创建权重变化CSV文件失败: {str(create_err)}")
                    return
                
            with open(self.weights_change_file, 'a') as f:
                timestamp = time.time()
                f.write(f"{self.global_round},{weight_diff:.6f},{mean:.6f},{max_val:.6f},{min_val:.6f},{timestamp}\n")
                
            logging.info(f"轮次 {self.global_round} 权重变化记录 - "
                      f"平均变化: {weight_diff:.6f}, 均值: {mean:.6f}, 最大: {max_val:.6f}, 最小: {min_val:.6f}")
                      
        except Exception as e:
            logging.error(f"记录权重变化时出错: {str(e)}")
            import traceback
            logging.error(f"异常堆栈: {traceback.format_exc()}")

    def log_accuracy(self):
        """记录全局模型准确率到CSV文件"""
        try:
            # 确保训练器已正确初始化
            if self.trainer is None:
                logging.warning("log_accuracy: 训练器为None，创建新的训练器")
                from sc_afl_trainer import Trainer
                self.trainer = Trainer(model=self.model, client_id=0)  # 显式设置client_id为0
                logging.info("log_accuracy: 已创建新的训练器实例，client_id=0")
            elif self.trainer.model is None:
                logging.warning("log_accuracy: 训练器的模型为None，设置为当前全局模型")
                self.trainer.model = self.model
                logging.info("log_accuracy: 已更新训练器的模型引用")
            elif self.trainer.model is not self.model:
                logging.warning("log_accuracy: 训练器的模型引用不一致，更新为当前全局模型")
                self.trainer.model = self.model
                logging.info("log_accuracy: 已同步训练器的模型引用")
            
            # 确保训练器有client_id属性
            if not hasattr(self.trainer, 'client_id') or self.trainer.client_id is None:
                self.trainer.client_id = 0
                logging.info("log_accuracy: 为训练器设置client_id=0")
            
            # 检查通道数是否匹配
            model_in_channels = None
            if hasattr(self.model, 'conv1') and hasattr(self.model.conv1, 'weight'):
                model_in_channels = self.model.conv1.weight.shape[1]
                logging.info(f"log_accuracy: 模型输入通道数: {model_in_channels}")
            elif hasattr(self.model, 'in_channels'):
                model_in_channels = self.model.in_channels
                logging.info(f"log_accuracy: 模型记录的输入通道数: {model_in_channels}")
            
            # 确保我们有准确率数据和文件路径
            if not hasattr(self, 'accuracy'):
                logging.warning("没有当前准确率数据，跳过记录")
                return
                
            # 设置current_accuracy为accuracy的值
            self.current_accuracy = self.accuracy
                
            if not hasattr(self, 'accuracy_csv_file'):
                logging.error("准确率CSV文件未初始化，跳过记录")
                # 尝试初始化准确率CSV文件
                try:
                    config = Config()
                    base_dir = getattr(config.results, 'path', './results/scafl/mnist') 
                    os.makedirs(base_dir, exist_ok=True)
                    timestamp = time.strftime("%Y%m%d_%H%M%S", time.localtime())
                    self.accuracy_csv_file = f"{base_dir}/accuracy_{timestamp}.csv"
                    with open(self.accuracy_csv_file, 'w') as f:
                        f.write("round,accuracy,avg_last_10,best_accuracy,timestamp\n")
                    logging.info(f"创建准确率记录文件: {self.accuracy_csv_file}")
                except Exception as create_err:
                    logging.error(f"创建准确率CSV文件失败: {str(create_err)}")
                    return
            
            # 计算过去10轮的平均准确率
            if not hasattr(self, 'accuracy_history'):
                self.accuracy_history = []
                
            self.accuracy_history.append(self.current_accuracy)
            # 只保留最近10轮的准确率
            if len(self.accuracy_history) > 10:
                self.accuracy_history.pop(0)
                
            avg_last_10 = sum(self.accuracy_history) / len(self.accuracy_history)
            
            # 更新最佳准确率
            if not hasattr(self, 'best_accuracy') or self.current_accuracy > self.best_accuracy:
                self.best_accuracy = self.current_accuracy
                
            # 记录到CSV文件
            with open(self.accuracy_csv_file, 'a') as f:
                timestamp = time.time()
                f.write(f"{self.global_round},{self.current_accuracy:.4f},{avg_last_10:.4f},{self.best_accuracy:.4f},{timestamp}\n")
                
            logging.info(f"轮次 {self.global_round} 准确率记录 - "
                      f"当前: {self.current_accuracy:.2%}, 平均(最近10轮): {avg_last_10:.2%}, "
                      f"最佳: {self.best_accuracy:.2%}")
                      
        except Exception as e:
            logging.error(f"记录准确率时出错: {str(e)}")
            import traceback
            logging.error(f"异常堆栈: {traceback.format_exc()}")