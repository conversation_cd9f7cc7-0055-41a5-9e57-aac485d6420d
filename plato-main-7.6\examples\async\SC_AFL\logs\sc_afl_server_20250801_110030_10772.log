2025-08-01 11:00:30,045 - INFO - 🚀 SC-AFL服务器启动 - 2025-08-01 11:00:30
2025-08-01 11:00:30,046 - INFO - ✅ 新日志文件已创建
2025-08-01 11:00:30,046 - INFO - 📁 日志目录: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs
2025-08-01 11:00:30,046 - INFO - 📄 日志文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs\sc_afl_server_20250801_110030_10772.log
2025-08-01 11:00:30,046 - INFO - 🔧 日志级别: DEBUG (文件), INFO (控制台)
2025-08-01 11:00:30,046 - INFO - 📊 文件模式: 新建模式 (每次启动创建新文件)
2025-08-01 11:00:30,046 - INFO - 🆔 进程ID: 10772
2025-08-01 11:00:30,046 - INFO - ✅ 日志文件创建成功，当前大小: 675 字节
2025-08-01 11:00:30,046 - INFO - 🚀 SC-AFL服务器初始化开始...
2025-08-01 11:00:30,046 - INFO - 📅 初始化时间: 2025-08-01 11:00:30
2025-08-01 11:00:30,049 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:30,070 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:30,070 - INFO - Server: 动态创建模型 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:00:30,070 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:30,088 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:30,089 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:30,089 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:30,089 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:30,090 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:30,090 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:30,090 - INFO - Server: 创建了新的Trainer实例
2025-08-01 11:00:30,090 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:30,090 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:30,090 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:30,090 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:30,090 - INFO - Server: 创建了新的Algorithm实例
2025-08-01 11:00:30,091 - INFO - [93m[1m[10772] Logging runtime results to: ./results/cifar10_with_network/10772.csv.[0m
2025-08-01 11:00:30,091 - INFO - [Server #10772] Started training on 10 clients with 3 per round.
2025-08-01 11:00:30,091 - INFO - [DEBUG] 从配置文件读取 simulate_wall_time=True
2025-08-01 11:00:30,091 - WARNING - Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。
2025-08-01 11:00:30,091 - WARNING - Server: 训练器在初始化后为None，重新创建
2025-08-01 11:00:30,092 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:30,092 - WARNING - [Trainer None] 模型为None，尝试创建默认模型
2025-08-01 11:00:30,092 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:30,107 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:30,107 - INFO - [Trainer None] 动态创建模型 resnet_9，输入通道: 3, 类别数: 10
2025-08-01 11:00:30,118 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:30,118 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:30,118 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:30,118 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:30,119 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:30,119 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:30,119 - INFO - Server: 重新创建了Trainer实例
2025-08-01 11:00:30,119 - INFO - [Algorithm] 已设置服务器引用 (客户端ID: None)
2025-08-01 11:00:30,119 - INFO - Server: 算法类已设置服务器引用
2025-08-01 11:00:30,120 - INFO - 动态加载数据集: CIFAR10
2025-08-01 11:00:30,609 - INFO - ✅ 成功加载数据集 CIFAR10: 10000 样本
2025-08-01 11:00:30,609 - INFO - ✅ 动态加载测试集成功: CIFAR10, 大小: 10000
2025-08-01 11:00:30,610 - INFO - ✅ 测试加载器已创建
2025-08-01 11:00:30,610 - INFO - 开始初始化全局模型权重
2025-08-01 11:00:30,610 - WARNING - 全局模型实例为None，正在尝试重新创建...
2025-08-01 11:00:30,610 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:30,622 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:30,623 - INFO - 成功重新创建了全局模型实例 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:00:30,638 - INFO - [全局权重摘要] 参数数量: 74, 均值: 0.001182, 最大: 1.000000, 最小: -0.192125
2025-08-01 11:00:30,638 - INFO - [全局模型] 输入通道数: 3
2025-08-01 11:00:30,638 - INFO - 全局模型权重初始化成功
2025-08-01 11:00:30,639 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:30,640 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:00:30,640 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:00:30,641 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:00:30,642 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_110030.csv
2025-08-01 11:00:30,642 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:00:30,642 - WARNING - 网络模拟器初始化失败: 'Config' object has no attribute 'get'
2025-08-01 11:00:30,642 - INFO - SC-AFL算法参数: tau_max=5, V=1.0
2025-08-01 11:00:30,642 - INFO - 服务器初始化完成
2025-08-01 11:00:30,642 - INFO - 已创建并注册 0 个客户端（将在 Server.start() 中启动任务）
2025-08-01 11:00:30,644 - INFO - 客户端ID管理器初始化完成，总客户端数: 10, ID起始值: 1
2025-08-01 11:00:30,644 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:00:30,644 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:00:30,644 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:00:30,645 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_110030.csv
2025-08-01 11:00:30,645 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:00:30,645 - INFO - 服务器实例创建成功
2025-08-01 11:00:30,645 - INFO - 正在创建和注册 10 个客户端...
2025-08-01 11:00:30,645 - INFO - 客户端ID配置: 起始ID=1, 总数=10
2025-08-01 11:00:30,645 - INFO - 开始创建客户端 1...
2025-08-01 11:00:30,645 - INFO - 初始化客户端, ID: 1
2025-08-01 11:00:30,645 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:30,663 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:30,663 - INFO - [Client 1] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:30,664 - INFO - [Trainer] 初始化训练器, client_id: 1
2025-08-01 11:00:30,672 - INFO - [Trainer 1] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:30,672 - INFO - [Trainer 1] 模型的输入通道数: 3
2025-08-01 11:00:30,672 - INFO - [Trainer 1] 强制使用CPU
2025-08-01 11:00:30,673 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:00:30,673 - INFO - [Trainer 1] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:30,674 - INFO - [Trainer 1] 初始化完成
2025-08-01 11:00:30,674 - INFO - [Client 1] 创建新训练器
2025-08-01 11:00:30,674 - INFO - [Algorithm] 从训练器获取客户端ID: 1
2025-08-01 11:00:30,674 - INFO - [Algorithm] 初始化后修正client_id: 1
2025-08-01 11:00:30,674 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:30,674 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:30,674 - INFO - [Client 1] 创建新算法
2025-08-01 11:00:30,674 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:00:30,674 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:00:30,674 - INFO - [Client None] 父类初始化完成
2025-08-01 11:00:30,674 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:30,690 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:30,690 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:30,690 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:30,699 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:30,699 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:30,699 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:30,699 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:30,700 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:30,700 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:30,700 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:00:30,700 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:30,700 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:30,700 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:30,700 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:30,700 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:00:30,700 - INFO - [Client None] 开始加载数据
2025-08-01 11:00:30,700 - INFO - 顺序分配客户端ID: 1
2025-08-01 11:00:30,700 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 1
2025-08-01 11:00:30,701 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:00:30,701 - WARNING - [Client 1] 数据源为None，已创建新数据源
2025-08-01 11:00:30,701 - WARNING - [Client 1] 数据源trainset为None，已设置为空列表
2025-08-01 11:00:31,308 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:00:31,308 - INFO - [Client 1] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:00:31,308 - INFO - [Client 1] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 11:00:31,318 - INFO - [Client 1] 成功划分数据集，分配到 300 个样本
2025-08-01 11:00:31,318 - INFO - [Client 1] 初始化时成功加载数据
2025-08-01 11:00:31,318 - INFO - [客户端 1] 初始化验证通过
2025-08-01 11:00:31,320 - INFO - 客户端 1 实例创建成功
2025-08-01 11:00:31,320 - INFO - 客户端1已设置服务器引用
2025-08-01 11:00:31,320 - INFO - 客户端 1 已设置服务器引用
2025-08-01 11:00:31,320 - INFO - 客户端1已注册
2025-08-01 11:00:31,320 - INFO - 客户端 1 已成功注册到服务器
2025-08-01 11:00:31,320 - INFO - 开始创建客户端 2...
2025-08-01 11:00:31,321 - INFO - 初始化客户端, ID: 2
2025-08-01 11:00:31,321 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:31,342 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:31,342 - INFO - [Client 2] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:31,342 - INFO - [Trainer] 初始化训练器, client_id: 2
2025-08-01 11:00:31,352 - INFO - [Trainer 2] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:31,353 - INFO - [Trainer 2] 模型的输入通道数: 3
2025-08-01 11:00:31,353 - INFO - [Trainer 2] 强制使用CPU
2025-08-01 11:00:31,353 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 11:00:31,353 - INFO - [Trainer 2] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:31,353 - INFO - [Trainer 2] 初始化完成
2025-08-01 11:00:31,355 - INFO - [Client 2] 创建新训练器
2025-08-01 11:00:31,355 - INFO - [Algorithm] 从训练器获取客户端ID: 2
2025-08-01 11:00:31,355 - INFO - [Algorithm] 初始化后修正client_id: 2
2025-08-01 11:00:31,355 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:31,355 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:31,355 - INFO - [Client 2] 创建新算法
2025-08-01 11:00:31,355 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:00:31,355 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:00:31,355 - INFO - [Client None] 父类初始化完成
2025-08-01 11:00:31,355 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:31,369 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:31,370 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:31,370 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:31,381 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:31,381 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:31,381 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:31,381 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:31,383 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:31,383 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:31,383 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:00:31,383 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:31,383 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:31,383 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:31,383 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:31,383 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:00:31,383 - INFO - [Client None] 开始加载数据
2025-08-01 11:00:31,383 - INFO - 顺序分配客户端ID: 2
2025-08-01 11:00:31,383 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 2
2025-08-01 11:00:31,384 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:00:31,384 - WARNING - [Client 2] 数据源为None，已创建新数据源
2025-08-01 11:00:31,384 - WARNING - [Client 2] 数据源trainset为None，已设置为空列表
2025-08-01 11:00:32,021 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:00:32,021 - INFO - [Client 2] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:00:32,021 - INFO - [Client 2] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 11:00:32,025 - INFO - [Client 2] 成功划分数据集，分配到 300 个样本
2025-08-01 11:00:32,025 - INFO - [Client 2] 初始化时成功加载数据
2025-08-01 11:00:32,026 - INFO - [客户端 2] 初始化验证通过
2025-08-01 11:00:32,026 - INFO - 客户端 2 实例创建成功
2025-08-01 11:00:32,026 - INFO - 客户端2已设置服务器引用
2025-08-01 11:00:32,026 - INFO - 客户端 2 已设置服务器引用
2025-08-01 11:00:32,026 - INFO - 客户端2已注册
2025-08-01 11:00:32,026 - INFO - 客户端 2 已成功注册到服务器
2025-08-01 11:00:32,026 - INFO - 开始创建客户端 3...
2025-08-01 11:00:32,026 - INFO - 初始化客户端, ID: 3
2025-08-01 11:00:32,026 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:32,041 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:32,041 - INFO - [Client 3] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:32,041 - INFO - [Trainer] 初始化训练器, client_id: 3
2025-08-01 11:00:32,053 - INFO - [Trainer 3] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:32,053 - INFO - [Trainer 3] 模型的输入通道数: 3
2025-08-01 11:00:32,053 - INFO - [Trainer 3] 强制使用CPU
2025-08-01 11:00:32,054 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:00:32,054 - INFO - [Trainer 3] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:32,054 - INFO - [Trainer 3] 初始化完成
2025-08-01 11:00:32,054 - INFO - [Client 3] 创建新训练器
2025-08-01 11:00:32,054 - INFO - [Algorithm] 从训练器获取客户端ID: 3
2025-08-01 11:00:32,054 - INFO - [Algorithm] 初始化后修正client_id: 3
2025-08-01 11:00:32,054 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:32,055 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:32,055 - INFO - [Client 3] 创建新算法
2025-08-01 11:00:32,055 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:00:32,055 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:00:32,055 - INFO - [Client None] 父类初始化完成
2025-08-01 11:00:32,055 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:32,069 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:32,069 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:32,070 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:32,078 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:32,078 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:32,079 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:32,079 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:32,079 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:32,079 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:32,079 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:00:32,079 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:32,079 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:32,079 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:32,080 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:32,080 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:00:32,080 - INFO - [Client None] 开始加载数据
2025-08-01 11:00:32,080 - INFO - 顺序分配客户端ID: 3
2025-08-01 11:00:32,080 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 3
2025-08-01 11:00:32,080 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:00:32,080 - WARNING - [Client 3] 数据源为None，已创建新数据源
2025-08-01 11:00:32,080 - WARNING - [Client 3] 数据源trainset为None，已设置为空列表
2025-08-01 11:00:32,672 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:00:32,672 - INFO - [Client 3] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:00:32,672 - INFO - [Client 3] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 11:00:32,676 - INFO - [Client 3] 成功划分数据集，分配到 300 个样本
2025-08-01 11:00:32,677 - INFO - [Client 3] 初始化时成功加载数据
2025-08-01 11:00:32,677 - INFO - [客户端 3] 初始化验证通过
2025-08-01 11:00:32,677 - INFO - 客户端 3 实例创建成功
2025-08-01 11:00:32,677 - INFO - 客户端3已设置服务器引用
2025-08-01 11:00:32,677 - INFO - 客户端 3 已设置服务器引用
2025-08-01 11:00:32,677 - INFO - 客户端3已注册
2025-08-01 11:00:32,678 - INFO - 客户端 3 已成功注册到服务器
2025-08-01 11:00:32,678 - INFO - 开始创建客户端 4...
2025-08-01 11:00:32,678 - INFO - 初始化客户端, ID: 4
2025-08-01 11:00:32,678 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:32,693 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:32,693 - INFO - [Client 4] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:32,693 - INFO - [Trainer] 初始化训练器, client_id: 4
2025-08-01 11:00:32,703 - INFO - [Trainer 4] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:32,703 - INFO - [Trainer 4] 模型的输入通道数: 3
2025-08-01 11:00:32,703 - INFO - [Trainer 4] 强制使用CPU
2025-08-01 11:00:32,704 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 11:00:32,704 - INFO - [Trainer 4] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:32,704 - INFO - [Trainer 4] 初始化完成
2025-08-01 11:00:32,704 - INFO - [Client 4] 创建新训练器
2025-08-01 11:00:32,704 - INFO - [Algorithm] 从训练器获取客户端ID: 4
2025-08-01 11:00:32,704 - INFO - [Algorithm] 初始化后修正client_id: 4
2025-08-01 11:00:32,704 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:32,704 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:32,704 - INFO - [Client 4] 创建新算法
2025-08-01 11:00:32,705 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:00:32,705 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:00:32,705 - INFO - [Client None] 父类初始化完成
2025-08-01 11:00:32,705 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:32,718 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:32,720 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:32,720 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:32,730 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:32,730 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:32,731 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:32,731 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:32,731 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:32,731 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:32,731 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:00:32,732 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:32,732 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:32,732 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:32,732 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:32,732 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:00:32,732 - INFO - [Client None] 开始加载数据
2025-08-01 11:00:32,732 - INFO - 顺序分配客户端ID: 4
2025-08-01 11:00:32,732 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 4
2025-08-01 11:00:32,732 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:00:32,732 - WARNING - [Client 4] 数据源为None，已创建新数据源
2025-08-01 11:00:32,733 - WARNING - [Client 4] 数据源trainset为None，已设置为空列表
2025-08-01 11:00:33,326 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:00:33,326 - INFO - [Client 4] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:00:33,326 - INFO - [Client 4] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 11:00:33,330 - INFO - [Client 4] 成功划分数据集，分配到 300 个样本
2025-08-01 11:00:33,330 - INFO - [Client 4] 初始化时成功加载数据
2025-08-01 11:00:33,330 - INFO - [客户端 4] 初始化验证通过
2025-08-01 11:00:33,330 - INFO - 客户端 4 实例创建成功
2025-08-01 11:00:33,330 - INFO - 客户端4已设置服务器引用
2025-08-01 11:00:33,330 - INFO - 客户端 4 已设置服务器引用
2025-08-01 11:00:33,330 - INFO - 客户端4已注册
2025-08-01 11:00:33,331 - INFO - 客户端 4 已成功注册到服务器
2025-08-01 11:00:33,332 - INFO - 开始创建客户端 5...
2025-08-01 11:00:33,332 - INFO - 初始化客户端, ID: 5
2025-08-01 11:00:33,333 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:33,350 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:33,350 - INFO - [Client 5] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:33,350 - INFO - [Trainer] 初始化训练器, client_id: 5
2025-08-01 11:00:33,362 - INFO - [Trainer 5] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:33,362 - INFO - [Trainer 5] 模型的输入通道数: 3
2025-08-01 11:00:33,362 - INFO - [Trainer 5] 强制使用CPU
2025-08-01 11:00:33,363 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 11:00:33,363 - INFO - [Trainer 5] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:33,363 - INFO - [Trainer 5] 初始化完成
2025-08-01 11:00:33,363 - INFO - [Client 5] 创建新训练器
2025-08-01 11:00:33,363 - INFO - [Algorithm] 从训练器获取客户端ID: 5
2025-08-01 11:00:33,363 - INFO - [Algorithm] 初始化后修正client_id: 5
2025-08-01 11:00:33,363 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:33,363 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:33,363 - INFO - [Client 5] 创建新算法
2025-08-01 11:00:33,364 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:00:33,364 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:00:33,364 - INFO - [Client None] 父类初始化完成
2025-08-01 11:00:33,364 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:33,378 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:33,378 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:33,378 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:33,387 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:33,388 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:33,388 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:33,388 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:33,388 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:33,388 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:33,388 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:00:33,388 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:33,388 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:33,389 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:33,389 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:33,389 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:00:33,389 - INFO - [Client None] 开始加载数据
2025-08-01 11:00:33,389 - INFO - 顺序分配客户端ID: 5
2025-08-01 11:00:33,389 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 5
2025-08-01 11:00:33,389 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:00:33,389 - WARNING - [Client 5] 数据源为None，已创建新数据源
2025-08-01 11:00:33,389 - WARNING - [Client 5] 数据源trainset为None，已设置为空列表
2025-08-01 11:00:34,002 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:00:34,002 - INFO - [Client 5] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:00:34,003 - INFO - [Client 5] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 11:00:34,006 - INFO - [Client 5] 成功划分数据集，分配到 300 个样本
2025-08-01 11:00:34,006 - INFO - [Client 5] 初始化时成功加载数据
2025-08-01 11:00:34,007 - INFO - [客户端 5] 初始化验证通过
2025-08-01 11:00:34,007 - INFO - 客户端 5 实例创建成功
2025-08-01 11:00:34,007 - INFO - 客户端5已设置服务器引用
2025-08-01 11:00:34,007 - INFO - 客户端 5 已设置服务器引用
2025-08-01 11:00:34,007 - INFO - 客户端5已注册
2025-08-01 11:00:34,007 - INFO - 客户端 5 已成功注册到服务器
2025-08-01 11:00:34,007 - INFO - 开始创建客户端 6...
2025-08-01 11:00:34,007 - INFO - 初始化客户端, ID: 6
2025-08-01 11:00:34,007 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:34,024 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:34,024 - INFO - [Client 6] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:34,024 - INFO - [Trainer] 初始化训练器, client_id: 6
2025-08-01 11:00:34,149 - INFO - [Trainer 6] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:34,149 - INFO - [Trainer 6] 模型的输入通道数: 3
2025-08-01 11:00:34,149 - INFO - [Trainer 6] 强制使用CPU
2025-08-01 11:00:34,151 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:00:34,151 - INFO - [Trainer 6] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:34,151 - INFO - [Trainer 6] 初始化完成
2025-08-01 11:00:34,151 - INFO - [Client 6] 创建新训练器
2025-08-01 11:00:34,151 - INFO - [Algorithm] 从训练器获取客户端ID: 6
2025-08-01 11:00:34,151 - INFO - [Algorithm] 初始化后修正client_id: 6
2025-08-01 11:00:34,151 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:34,151 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:34,151 - INFO - [Client 6] 创建新算法
2025-08-01 11:00:34,151 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:00:34,151 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:00:34,151 - INFO - [Client None] 父类初始化完成
2025-08-01 11:00:34,151 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:34,166 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:34,166 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:34,166 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:34,176 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:34,176 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:34,177 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:34,177 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:34,177 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:34,177 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:34,177 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:00:34,177 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:34,178 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:34,178 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:34,178 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:34,178 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:00:34,178 - INFO - [Client None] 开始加载数据
2025-08-01 11:00:34,178 - INFO - 顺序分配客户端ID: 6
2025-08-01 11:00:34,178 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 6
2025-08-01 11:00:34,178 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:00:34,178 - WARNING - [Client 6] 数据源为None，已创建新数据源
2025-08-01 11:00:34,178 - WARNING - [Client 6] 数据源trainset为None，已设置为空列表
2025-08-01 11:00:34,780 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:00:34,781 - INFO - [Client 6] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:00:34,781 - INFO - [Client 6] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 11:00:34,785 - INFO - [Client 6] 成功划分数据集，分配到 300 个样本
2025-08-01 11:00:34,785 - INFO - [Client 6] 初始化时成功加载数据
2025-08-01 11:00:34,785 - INFO - [客户端 6] 初始化验证通过
2025-08-01 11:00:34,785 - INFO - 客户端 6 实例创建成功
2025-08-01 11:00:34,786 - INFO - 客户端6已设置服务器引用
2025-08-01 11:00:34,786 - INFO - 客户端 6 已设置服务器引用
2025-08-01 11:00:34,786 - INFO - 客户端6已注册
2025-08-01 11:00:34,786 - INFO - 客户端 6 已成功注册到服务器
2025-08-01 11:00:34,786 - INFO - 开始创建客户端 7...
2025-08-01 11:00:34,786 - INFO - 初始化客户端, ID: 7
2025-08-01 11:00:34,786 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:34,802 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:34,802 - INFO - [Client 7] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:34,802 - INFO - [Trainer] 初始化训练器, client_id: 7
2025-08-01 11:00:34,811 - INFO - [Trainer 7] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:34,811 - INFO - [Trainer 7] 模型的输入通道数: 3
2025-08-01 11:00:34,812 - INFO - [Trainer 7] 强制使用CPU
2025-08-01 11:00:34,812 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 11:00:34,812 - INFO - [Trainer 7] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:34,812 - INFO - [Trainer 7] 初始化完成
2025-08-01 11:00:34,812 - INFO - [Client 7] 创建新训练器
2025-08-01 11:00:34,812 - INFO - [Algorithm] 从训练器获取客户端ID: 7
2025-08-01 11:00:34,812 - INFO - [Algorithm] 初始化后修正client_id: 7
2025-08-01 11:00:34,812 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:34,813 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:34,813 - INFO - [Client 7] 创建新算法
2025-08-01 11:00:34,813 - INFO - [Algorithm] 设置客户端ID: 7
2025-08-01 11:00:34,813 - INFO - [Algorithm] 同步更新trainer的client_id: 7
2025-08-01 11:00:34,813 - INFO - [Client None] 父类初始化完成
2025-08-01 11:00:34,813 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:34,827 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:34,827 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:34,827 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:34,837 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:34,838 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:34,838 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:34,838 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:34,838 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:34,838 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:34,838 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:00:34,838 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:34,839 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:34,839 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:34,839 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:34,839 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:00:34,839 - INFO - [Client None] 开始加载数据
2025-08-01 11:00:34,839 - INFO - 顺序分配客户端ID: 7
2025-08-01 11:00:34,839 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 7
2025-08-01 11:00:34,839 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:00:34,840 - WARNING - [Client 7] 数据源为None，已创建新数据源
2025-08-01 11:00:34,840 - WARNING - [Client 7] 数据源trainset为None，已设置为空列表
2025-08-01 11:00:35,477 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:00:35,478 - INFO - [Client 7] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:00:35,478 - INFO - [Client 7] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 11:00:35,482 - INFO - [Client 7] 成功划分数据集，分配到 300 个样本
2025-08-01 11:00:35,483 - INFO - [Client 7] 初始化时成功加载数据
2025-08-01 11:00:35,483 - INFO - [客户端 7] 初始化验证通过
2025-08-01 11:00:35,483 - INFO - 客户端 7 实例创建成功
2025-08-01 11:00:35,483 - INFO - 客户端7已设置服务器引用
2025-08-01 11:00:35,483 - INFO - 客户端 7 已设置服务器引用
2025-08-01 11:00:35,483 - INFO - 客户端7已注册
2025-08-01 11:00:35,483 - INFO - 客户端 7 已成功注册到服务器
2025-08-01 11:00:35,483 - INFO - 开始创建客户端 8...
2025-08-01 11:00:35,483 - INFO - 初始化客户端, ID: 8
2025-08-01 11:00:35,483 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:35,498 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:35,498 - INFO - [Client 8] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:35,498 - INFO - [Trainer] 初始化训练器, client_id: 8
2025-08-01 11:00:35,507 - INFO - [Trainer 8] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:35,508 - INFO - [Trainer 8] 模型的输入通道数: 3
2025-08-01 11:00:35,508 - INFO - [Trainer 8] 强制使用CPU
2025-08-01 11:00:35,508 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 11:00:35,508 - INFO - [Trainer 8] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:35,508 - INFO - [Trainer 8] 初始化完成
2025-08-01 11:00:35,508 - INFO - [Client 8] 创建新训练器
2025-08-01 11:00:35,509 - INFO - [Algorithm] 从训练器获取客户端ID: 8
2025-08-01 11:00:35,509 - INFO - [Algorithm] 初始化后修正client_id: 8
2025-08-01 11:00:35,509 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:35,509 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:35,509 - INFO - [Client 8] 创建新算法
2025-08-01 11:00:35,509 - INFO - [Algorithm] 设置客户端ID: 8
2025-08-01 11:00:35,509 - INFO - [Algorithm] 同步更新trainer的client_id: 8
2025-08-01 11:00:35,509 - INFO - [Client None] 父类初始化完成
2025-08-01 11:00:35,509 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:35,523 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:35,523 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:35,523 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:35,534 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:35,534 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:35,534 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:35,534 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:35,534 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:35,534 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:35,534 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:00:35,534 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:35,534 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:35,534 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:35,534 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:35,534 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:00:35,535 - INFO - [Client None] 开始加载数据
2025-08-01 11:00:35,535 - INFO - 顺序分配客户端ID: 8
2025-08-01 11:00:35,535 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 8
2025-08-01 11:00:35,535 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:00:35,535 - WARNING - [Client 8] 数据源为None，已创建新数据源
2025-08-01 11:00:35,535 - WARNING - [Client 8] 数据源trainset为None，已设置为空列表
2025-08-01 11:00:36,353 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:00:36,353 - INFO - [Client 8] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:00:36,353 - INFO - [Client 8] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 11:00:36,357 - INFO - [Client 8] 成功划分数据集，分配到 300 个样本
2025-08-01 11:00:36,358 - INFO - [Client 8] 初始化时成功加载数据
2025-08-01 11:00:36,358 - INFO - [客户端 8] 初始化验证通过
2025-08-01 11:00:36,358 - INFO - 客户端 8 实例创建成功
2025-08-01 11:00:36,358 - INFO - 客户端8已设置服务器引用
2025-08-01 11:00:36,358 - INFO - 客户端 8 已设置服务器引用
2025-08-01 11:00:36,358 - INFO - 客户端8已注册
2025-08-01 11:00:36,358 - INFO - 客户端 8 已成功注册到服务器
2025-08-01 11:00:36,358 - INFO - 开始创建客户端 9...
2025-08-01 11:00:36,358 - INFO - 初始化客户端, ID: 9
2025-08-01 11:00:36,358 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:36,375 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:36,375 - INFO - [Client 9] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:36,376 - INFO - [Trainer] 初始化训练器, client_id: 9
2025-08-01 11:00:36,388 - INFO - [Trainer 9] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:36,388 - INFO - [Trainer 9] 模型的输入通道数: 3
2025-08-01 11:00:36,388 - INFO - [Trainer 9] 强制使用CPU
2025-08-01 11:00:36,389 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 11:00:36,389 - INFO - [Trainer 9] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:36,389 - INFO - [Trainer 9] 初始化完成
2025-08-01 11:00:36,389 - INFO - [Client 9] 创建新训练器
2025-08-01 11:00:36,389 - INFO - [Algorithm] 从训练器获取客户端ID: 9
2025-08-01 11:00:36,389 - INFO - [Algorithm] 初始化后修正client_id: 9
2025-08-01 11:00:36,389 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:36,389 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:36,390 - INFO - [Client 9] 创建新算法
2025-08-01 11:00:36,390 - INFO - [Algorithm] 设置客户端ID: 9
2025-08-01 11:00:36,390 - INFO - [Algorithm] 同步更新trainer的client_id: 9
2025-08-01 11:00:36,390 - INFO - [Client None] 父类初始化完成
2025-08-01 11:00:36,390 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:36,403 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:36,404 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:36,404 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:36,414 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:36,414 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:36,414 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:36,414 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:36,415 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:36,415 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:36,415 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:00:36,415 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:36,415 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:36,415 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:36,415 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:36,415 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:00:36,415 - INFO - [Client None] 开始加载数据
2025-08-01 11:00:36,415 - INFO - 顺序分配客户端ID: 9
2025-08-01 11:00:36,415 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 9
2025-08-01 11:00:36,416 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:00:36,416 - WARNING - [Client 9] 数据源为None，已创建新数据源
2025-08-01 11:00:36,416 - WARNING - [Client 9] 数据源trainset为None，已设置为空列表
2025-08-01 11:00:37,243 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:00:37,244 - INFO - [Client 9] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:00:37,244 - INFO - [Client 9] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 11:00:37,247 - INFO - [Client 9] 成功划分数据集，分配到 300 个样本
2025-08-01 11:00:37,247 - INFO - [Client 9] 初始化时成功加载数据
2025-08-01 11:00:37,248 - INFO - [客户端 9] 初始化验证通过
2025-08-01 11:00:37,248 - INFO - 客户端 9 实例创建成功
2025-08-01 11:00:37,248 - INFO - 客户端9已设置服务器引用
2025-08-01 11:00:37,248 - INFO - 客户端 9 已设置服务器引用
2025-08-01 11:00:37,248 - INFO - 客户端9已注册
2025-08-01 11:00:37,248 - INFO - 客户端 9 已成功注册到服务器
2025-08-01 11:00:37,248 - INFO - 开始创建客户端 10...
2025-08-01 11:00:37,248 - INFO - 初始化客户端, ID: 10
2025-08-01 11:00:37,248 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:37,267 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:37,267 - INFO - [Client 10] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:37,268 - INFO - [Trainer] 初始化训练器, client_id: 10
2025-08-01 11:00:37,282 - INFO - [Trainer 10] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:37,282 - INFO - [Trainer 10] 模型的输入通道数: 3
2025-08-01 11:00:37,282 - INFO - [Trainer 10] 强制使用CPU
2025-08-01 11:00:37,283 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 11:00:37,283 - INFO - [Trainer 10] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:37,283 - INFO - [Trainer 10] 初始化完成
2025-08-01 11:00:37,283 - INFO - [Client 10] 创建新训练器
2025-08-01 11:00:37,283 - INFO - [Algorithm] 从训练器获取客户端ID: 10
2025-08-01 11:00:37,284 - INFO - [Algorithm] 初始化后修正client_id: 10
2025-08-01 11:00:37,284 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:37,284 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:37,284 - INFO - [Client 10] 创建新算法
2025-08-01 11:00:37,284 - INFO - [Algorithm] 设置客户端ID: 10
2025-08-01 11:00:37,284 - INFO - [Algorithm] 同步更新trainer的client_id: 10
2025-08-01 11:00:37,284 - INFO - [Client None] 父类初始化完成
2025-08-01 11:00:37,284 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:00:37,299 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:00:37,299 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:00:37,300 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:00:37,311 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:00:37,311 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:00:37,311 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:00:37,312 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:00:37,312 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:37,312 - INFO - [Trainer None] 初始化完成
2025-08-01 11:00:37,312 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:00:37,312 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:00:37,312 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:00:37,312 - INFO - [Algorithm] 初始化完成
2025-08-01 11:00:37,313 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:00:37,313 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:00:37,313 - INFO - [Client None] 开始加载数据
2025-08-01 11:00:37,313 - INFO - 顺序分配客户端ID: 10
2025-08-01 11:00:37,313 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 10
2025-08-01 11:00:37,314 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:00:37,314 - WARNING - [Client 10] 数据源为None，已创建新数据源
2025-08-01 11:00:37,314 - WARNING - [Client 10] 数据源trainset为None，已设置为空列表
2025-08-01 11:00:38,009 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:00:38,009 - INFO - [Client 10] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:00:38,009 - INFO - [Client 10] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 11:00:38,013 - INFO - [Client 10] 成功划分数据集，分配到 300 个样本
2025-08-01 11:00:38,013 - INFO - [Client 10] 初始化时成功加载数据
2025-08-01 11:00:38,013 - INFO - [客户端 10] 初始化验证通过
2025-08-01 11:00:38,013 - INFO - 客户端 10 实例创建成功
2025-08-01 11:00:38,013 - INFO - 客户端10已设置服务器引用
2025-08-01 11:00:38,013 - INFO - 客户端 10 已设置服务器引用
2025-08-01 11:00:38,013 - INFO - 客户端10已注册
2025-08-01 11:00:38,013 - INFO - 客户端 10 已成功注册到服务器
2025-08-01 11:00:38,014 - INFO - 已成功创建和注册 10 个客户端
2025-08-01 11:00:38,014 - INFO - 服务器属性检查:
2025-08-01 11:00:38,014 - INFO - - 客户端数量: 10
2025-08-01 11:00:38,014 - INFO - - 全局模型: 已初始化
2025-08-01 11:00:38,014 - INFO - - 算法: 已初始化
2025-08-01 11:00:38,014 - INFO - - 训练器: 已初始化
2025-08-01 11:00:38,015 - INFO - 准备启动服务器...
2025-08-01 11:00:38,015 - INFO - [Server #10772] 启动中...
2025-08-01 11:00:38,019 - INFO - 服务器将使用事件循环: <ProactorEventLoop running=False closed=False debug=False>
2025-08-01 11:00:38,021 - INFO - [Client 1] 模型已放置到设备: cpu
2025-08-01 11:00:38,030 - INFO - [Client 1] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,038 - INFO - [Client 1] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,039 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:00:38,039 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:00:38,039 - INFO - [Client 1] 已更新algorithm的client_id
2025-08-01 11:00:38,039 - INFO - [Client 1] 模型初始化完成
2025-08-01 11:00:38,039 - INFO - 客户端 1 模型初始化成功
2025-08-01 11:00:38,050 - INFO - 客户端 1 异步训练线程已启动
2025-08-01 11:00:38,051 - INFO - [Client 2] 模型已放置到设备: cpu
2025-08-01 11:00:38,058 - INFO - [Client 2] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,065 - INFO - [Client 2] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,066 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:00:38,066 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:00:38,066 - INFO - [Client 2] 已更新algorithm的client_id
2025-08-01 11:00:38,066 - INFO - [Client 2] 模型初始化完成
2025-08-01 11:00:38,066 - INFO - 客户端 2 模型初始化成功
2025-08-01 11:00:38,067 - INFO - 客户端 2 异步训练线程已启动
2025-08-01 11:00:38,068 - INFO - [Client 3] 模型已放置到设备: cpu
2025-08-01 11:00:38,075 - INFO - [Client 3] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,081 - INFO - [Client 3] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,082 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:00:38,082 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:00:38,082 - INFO - [Client 3] 已更新algorithm的client_id
2025-08-01 11:00:38,082 - INFO - [Client 3] 模型初始化完成
2025-08-01 11:00:38,082 - INFO - 客户端 3 模型初始化成功
2025-08-01 11:00:38,083 - INFO - 客户端 3 异步训练线程已启动
2025-08-01 11:00:38,084 - INFO - [Client 4] 模型已放置到设备: cpu
2025-08-01 11:00:38,091 - INFO - [Client 4] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,099 - INFO - [Client 4] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,099 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:00:38,099 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:00:38,100 - INFO - [Client 4] 已更新algorithm的client_id
2025-08-01 11:00:38,100 - INFO - [Client 4] 模型初始化完成
2025-08-01 11:00:38,100 - INFO - 客户端 4 模型初始化成功
2025-08-01 11:00:38,100 - INFO - 客户端 4 异步训练线程已启动
2025-08-01 11:00:38,101 - INFO - [Client 5] 模型已放置到设备: cpu
2025-08-01 11:00:38,108 - INFO - [Client 5] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,115 - INFO - [Client 5] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,115 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:00:38,116 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:00:38,116 - INFO - [Client 5] 已更新algorithm的client_id
2025-08-01 11:00:38,116 - INFO - [Client 5] 模型初始化完成
2025-08-01 11:00:38,116 - INFO - 客户端 5 模型初始化成功
2025-08-01 11:00:38,116 - INFO - 客户端 5 异步训练线程已启动
2025-08-01 11:00:38,118 - INFO - [Client 6] 模型已放置到设备: cpu
2025-08-01 11:00:38,125 - INFO - [Client 6] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,132 - INFO - [Client 6] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,132 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:00:38,132 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:00:38,133 - INFO - [Client 6] 已更新algorithm的client_id
2025-08-01 11:00:38,133 - INFO - [Client 6] 模型初始化完成
2025-08-01 11:00:38,133 - INFO - 客户端 6 模型初始化成功
2025-08-01 11:00:38,135 - INFO - 客户端 6 异步训练线程已启动
2025-08-01 11:00:38,137 - INFO - [Client 7] 模型已放置到设备: cpu
2025-08-01 11:00:38,144 - INFO - [Client 7] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,151 - INFO - [Client 7] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,152 - INFO - [Algorithm] 设置客户端ID: 7
2025-08-01 11:00:38,152 - INFO - [Algorithm] 同步更新trainer的client_id: 7
2025-08-01 11:00:38,152 - INFO - [Client 7] 已更新algorithm的client_id
2025-08-01 11:00:38,152 - INFO - [Client 7] 模型初始化完成
2025-08-01 11:00:38,152 - INFO - 客户端 7 模型初始化成功
2025-08-01 11:00:38,152 - INFO - 客户端 7 异步训练线程已启动
2025-08-01 11:00:38,153 - INFO - [Client 8] 模型已放置到设备: cpu
2025-08-01 11:00:38,159 - INFO - [Client 8] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,167 - INFO - [Client 8] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,167 - INFO - [Algorithm] 设置客户端ID: 8
2025-08-01 11:00:38,167 - INFO - [Algorithm] 同步更新trainer的client_id: 8
2025-08-01 11:00:38,167 - INFO - [Client 8] 已更新algorithm的client_id
2025-08-01 11:00:38,167 - INFO - [Client 8] 模型初始化完成
2025-08-01 11:00:38,167 - INFO - 客户端 8 模型初始化成功
2025-08-01 11:00:38,168 - INFO - 客户端 4 开始异步训练循环
2025-08-01 11:00:38,171 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:38,172 - INFO - 客户端 8 异步训练线程已启动
2025-08-01 11:00:38,173 - INFO - [Client 9] 模型已放置到设备: cpu
2025-08-01 11:00:38,180 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 11:00:38,181 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 11:00:38,181 - INFO - [Client 4] 开始验证训练集
2025-08-01 11:00:38,195 - INFO - [Client 9] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,206 - INFO - [Client 9] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,206 - INFO - [Algorithm] 设置客户端ID: 9
2025-08-01 11:00:38,206 - INFO - [Algorithm] 同步更新trainer的client_id: 9
2025-08-01 11:00:38,206 - INFO - [Client 9] 已更新algorithm的client_id
2025-08-01 11:00:38,206 - INFO - [Client 9] 模型初始化完成
2025-08-01 11:00:38,207 - INFO - 客户端 9 模型初始化成功
2025-08-01 11:00:38,208 - INFO - 客户端 9 异步训练线程已启动
2025-08-01 11:00:38,209 - INFO - [Client 10] 模型已放置到设备: cpu
2025-08-01 11:00:38,218 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:00:38,218 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 11:00:38,220 - INFO - [Trainer 4] 开始训练
2025-08-01 11:00:38,220 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 11:00:38,220 - INFO - [Client 10] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,221 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 11:00:38,222 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:38,233 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:00:38,236 - INFO - [Trainer 4] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:00:38,237 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 11:00:38,251 - INFO - [Client 10] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:00:38,252 - INFO - [Algorithm] 设置客户端ID: 10
2025-08-01 11:00:38,252 - INFO - [Algorithm] 同步更新trainer的client_id: 10
2025-08-01 11:00:38,252 - INFO - [Client 10] 已更新algorithm的client_id
2025-08-01 11:00:38,252 - INFO - [Client 10] 模型初始化完成
2025-08-01 11:00:38,252 - INFO - 客户端 10 模型初始化成功
2025-08-01 11:00:38,253 - INFO - 客户端 10 异步训练线程已启动
2025-08-01 11:00:38,253 - INFO - 服务器主循环任务已启动: <Task pending name='Task-2' coro=<Server.run() running at D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py:1302>>
2025-08-01 11:00:38,254 - INFO - Starting a server at address 127.0.0.1 and port 8000.
2025-08-01 11:00:38,257 - INFO - [Server #10772] 开始训练，共有 10 个客户端，每轮最多聚合 5 个客户端
2025-08-01 11:00:38,258 - INFO - 总训练轮次: 20
2025-08-01 11:00:38,258 - INFO - 🚀 开始第 1 轮训练（目标：20 轮）
2025-08-01 11:00:38,258 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:38,258 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:38,274 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3779, y=[5, 5, 0, 1, 1]
2025-08-01 11:00:38,275 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 11:00:38,280 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 11:00:38,292 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3195
2025-08-01 11:00:38,293 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:38,293 - INFO - [Trainer 4] 标签样本: [0, 0, 1, 1, 1]
2025-08-01 11:00:38,504 - INFO - [Trainer 4] Batch 0, Loss: 2.3292
2025-08-01 11:00:38,730 - INFO - 客户端 6 开始异步训练循环
2025-08-01 11:00:38,730 - INFO - 客户端 8 开始异步训练循环
2025-08-01 11:00:38,730 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:38,730 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:38,730 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 11:00:38,731 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 11:00:38,731 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 11:00:38,731 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 11:00:38,731 - INFO - [Client 8] 开始验证训练集
2025-08-01 11:00:38,731 - INFO - [Client 6] 开始验证训练集
2025-08-01 11:00:38,732 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:00:38,732 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:00:38,733 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 11:00:38,733 - INFO - [Trainer 6] 开始训练
2025-08-01 11:00:38,733 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 11:00:38,733 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 11:00:38,733 - INFO - [Trainer 8] 开始训练
2025-08-01 11:00:38,733 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 11:00:38,734 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:00:38,734 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 11:00:38,734 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:38,734 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:38,735 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:00:38,735 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:00:38,735 - INFO - [Trainer 6] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:00:38,736 - INFO - [Trainer 8] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:00:38,736 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 11:00:38,736 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 11:00:38,757 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1143, y=[0, 0, 0, 1, 6]
2025-08-01 11:00:38,758 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 11:00:38,758 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1040, y=[7, 7, 8, 0, 0]
2025-08-01 11:00:38,759 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 11:00:38,778 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 11:00:38,779 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 11:00:38,792 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0106
2025-08-01 11:00:38,792 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:38,792 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0195
2025-08-01 11:00:38,792 - INFO - [Trainer 8] 标签样本: [1, 0, 1, 1, 1]
2025-08-01 11:00:38,792 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:38,793 - INFO - [Trainer 6] 标签样本: [0, 8, 7, 0, 0]
2025-08-01 11:00:38,807 - INFO - 客户端 1 开始异步训练循环
2025-08-01 11:00:38,808 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:38,809 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 11:00:38,810 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 11:00:38,812 - INFO - [Client 1] 开始验证训练集
2025-08-01 11:00:38,813 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:00:38,813 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 11:00:38,813 - INFO - [Trainer 1] 开始训练
2025-08-01 11:00:38,814 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 11:00:38,815 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:00:38,817 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:38,818 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:00:38,819 - INFO - [Trainer 1] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:00:38,819 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 11:00:38,855 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2337, y=[8, 5, 5, 8, 5]
2025-08-01 11:00:38,855 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 11:00:38,878 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 11:00:38,886 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2564
2025-08-01 11:00:38,887 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:38,887 - INFO - [Trainer 1] 标签样本: [8, 5, 0, 5, 5]
2025-08-01 11:00:38,992 - INFO - [Trainer 8] Batch 0, Loss: 2.1621
2025-08-01 11:00:38,995 - INFO - [Trainer 6] Batch 0, Loss: 2.1773
2025-08-01 11:00:39,104 - INFO - [Trainer 1] Batch 0, Loss: 2.1880
2025-08-01 11:00:39,276 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:39,276 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:40,277 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:40,279 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:40,632 - INFO - [Trainer 4] Batch 5, Loss: 1.6490
2025-08-01 11:00:41,173 - INFO - [Trainer 6] Batch 5, Loss: 1.5509
2025-08-01 11:00:41,217 - INFO - [Trainer 8] Batch 5, Loss: 1.6627
2025-08-01 11:00:41,249 - INFO - [Trainer 1] Batch 5, Loss: 1.9470
2025-08-01 11:00:41,282 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:41,283 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:41,534 - INFO - 客户端 3 开始异步训练循环
2025-08-01 11:00:41,534 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:41,536 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 11:00:41,536 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 11:00:41,537 - INFO - [Client 3] 开始验证训练集
2025-08-01 11:00:41,537 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:00:41,539 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 11:00:41,539 - INFO - [Trainer 3] 开始训练
2025-08-01 11:00:41,540 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 11:00:41,543 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:00:41,545 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:41,546 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:00:41,548 - INFO - [Trainer 3] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:00:41,548 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 11:00:41,606 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3404, y=[7, 7, 2, 2, 2]
2025-08-01 11:00:41,607 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 11:00:41,613 - INFO - 客户端 5 开始异步训练循环
2025-08-01 11:00:41,614 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:41,617 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 11:00:41,617 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 11:00:41,617 - INFO - [Client 5] 开始验证训练集
2025-08-01 11:00:41,620 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:00:41,620 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 11:00:41,621 - INFO - [Trainer 5] 开始训练
2025-08-01 11:00:41,621 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 11:00:41,627 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 11:00:41,628 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:41,631 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:00:41,633 - INFO - [Trainer 5] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:00:41,634 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 11:00:41,699 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 11:00:41,707 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4668
2025-08-01 11:00:41,708 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:41,709 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:00:41,714 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5351, y=[7, 7, 7, 7, 7]
2025-08-01 11:00:41,716 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 11:00:41,721 - INFO - 客户端 10 开始异步训练循环
2025-08-01 11:00:41,722 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:41,725 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 11:00:41,726 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 11:00:41,728 - INFO - [Client 10] 开始验证训练集
2025-08-01 11:00:41,734 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:00:41,735 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 11:00:41,738 - INFO - [Trainer 10] 开始训练
2025-08-01 11:00:41,738 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 11:00:41,749 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 11:00:41,750 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:41,751 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:00:41,754 - INFO - [Trainer 10] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:00:41,755 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 11:00:41,845 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 11:00:41,856 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1545, y=[8, 8, 8, 6, 8]
2025-08-01 11:00:41,856 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 11:00:41,862 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3944
2025-08-01 11:00:41,863 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:41,863 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 3, 7]
2025-08-01 11:00:41,941 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 11:00:41,962 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4301
2025-08-01 11:00:41,963 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:41,965 - INFO - [Trainer 10] 标签样本: [8, 6, 6, 8, 6]
2025-08-01 11:00:42,136 - INFO - [Trainer 3] Batch 0, Loss: 3.1974
2025-08-01 11:00:42,140 - INFO - [Trainer 4] Epoch 1 进度: 10/10 批次
2025-08-01 11:00:42,144 - INFO - [Trainer 4] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0890
2025-08-01 11:00:42,145 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:42,146 - INFO - [Trainer 4] 标签样本: [0, 1, 0, 0, 0]
2025-08-01 11:00:42,287 - INFO - [Trainer 5] Batch 0, Loss: 2.3028
2025-08-01 11:00:42,300 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:42,301 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:42,332 - INFO - [Trainer 10] Batch 0, Loss: 2.3908
2025-08-01 11:00:42,403 - INFO - [Trainer 4] Batch 9, Loss: 1.0142
2025-08-01 11:00:42,678 - INFO - [Trainer 4] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:00:42,679 - INFO - [Trainer 4] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.4731, Accuracy: 51.00%
2025-08-01 11:00:42,680 - INFO - [Trainer 4] 开始第 2/2 个epoch
2025-08-01 11:00:42,744 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2778, y=[0, 1, 1, 0, 0]
2025-08-01 11:00:42,744 - INFO - [Trainer 4] Epoch 2 开始处理 10 个批次
2025-08-01 11:00:42,799 - INFO - [Trainer 4] Epoch 2 进度: 1/10 批次
2025-08-01 11:00:42,819 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2806
2025-08-01 11:00:42,819 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:42,819 - INFO - [Trainer 4] 标签样本: [5, 4, 1, 1, 0]
2025-08-01 11:00:43,062 - INFO - [Trainer 6] Epoch 1 进度: 10/10 批次
2025-08-01 11:00:43,084 - INFO - [Trainer 6] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1381
2025-08-01 11:00:43,086 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:43,088 - INFO - [Trainer 6] 标签样本: [0, 8, 0, 0, 8]
2025-08-01 11:00:43,117 - INFO - [Trainer 4] Batch 0, Loss: 1.4808
2025-08-01 11:00:43,231 - INFO - [Trainer 8] Epoch 1 进度: 10/10 批次
2025-08-01 11:00:43,255 - INFO - [Trainer 8] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3625
2025-08-01 11:00:43,258 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:43,261 - INFO - [Trainer 8] 标签样本: [1, 0, 1, 6, 6]
2025-08-01 11:00:43,314 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:43,314 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:43,326 - INFO - [Trainer 1] Epoch 1 进度: 10/10 批次
2025-08-01 11:00:43,331 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0250
2025-08-01 11:00:43,332 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:43,333 - INFO - [Trainer 1] 标签样本: [8, 5, 8, 0, 8]
2025-08-01 11:00:43,357 - INFO - [Trainer 6] Batch 9, Loss: 0.9855
2025-08-01 11:00:43,452 - INFO - [Trainer 8] Batch 9, Loss: 1.0954
2025-08-01 11:00:43,511 - INFO - [Trainer 1] Batch 9, Loss: 2.4350
2025-08-01 11:00:43,546 - INFO - [Trainer 6] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:00:43,547 - INFO - [Trainer 6] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.5832, Accuracy: 41.33%
2025-08-01 11:00:43,549 - INFO - [Trainer 6] 开始第 2/2 个epoch
2025-08-01 11:00:43,616 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2120, y=[8, 0, 0, 8, 0]
2025-08-01 11:00:43,617 - INFO - [Trainer 6] Epoch 2 开始处理 10 个批次
2025-08-01 11:00:43,660 - INFO - [Trainer 8] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:00:43,661 - INFO - [Trainer 8] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.2705, Accuracy: 55.67%
2025-08-01 11:00:43,661 - INFO - [Trainer 8] 开始第 2/2 个epoch
2025-08-01 11:00:43,694 - INFO - [Trainer 6] Epoch 2 进度: 1/10 批次
2025-08-01 11:00:43,704 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0306
2025-08-01 11:00:43,704 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:43,705 - INFO - [Trainer 6] 标签样本: [8, 7, 8, 8, 0]
2025-08-01 11:00:43,714 - INFO - [Trainer 1] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:00:43,715 - INFO - [Trainer 1] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.9155, Accuracy: 39.00%
2025-08-01 11:00:43,715 - INFO - [Trainer 1] 开始第 2/2 个epoch
2025-08-01 11:00:43,757 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2595, y=[6, 1, 6, 1, 0]
2025-08-01 11:00:43,759 - INFO - [Trainer 8] Epoch 2 开始处理 10 个批次
2025-08-01 11:00:43,801 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1869, y=[5, 0, 5, 5, 0]
2025-08-01 11:00:43,804 - INFO - [Trainer 1] Epoch 2 开始处理 10 个批次
2025-08-01 11:00:43,857 - INFO - [Trainer 8] Epoch 2 进度: 1/10 批次
2025-08-01 11:00:43,862 - INFO - [Trainer 1] Epoch 2 进度: 1/10 批次
2025-08-01 11:00:43,877 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2873
2025-08-01 11:00:43,878 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1351
2025-08-01 11:00:43,878 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:43,881 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:43,882 - INFO - [Trainer 8] 标签样本: [0, 0, 0, 1, 1]
2025-08-01 11:00:43,882 - INFO - [Trainer 1] 标签样本: [4, 4, 0, 0, 5]
2025-08-01 11:00:43,997 - INFO - [Trainer 6] Batch 0, Loss: 3.1443
2025-08-01 11:00:44,112 - INFO - [Trainer 1] Batch 0, Loss: 2.9207
2025-08-01 11:00:44,126 - INFO - [Trainer 8] Batch 0, Loss: 1.1736
2025-08-01 11:00:44,328 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:44,328 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:45,283 - INFO - 客户端 9 开始异步训练循环
2025-08-01 11:00:45,284 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:45,285 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 11:00:45,286 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 11:00:45,287 - INFO - [Client 9] 开始验证训练集
2025-08-01 11:00:45,292 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:00:45,293 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 11:00:45,294 - INFO - [Trainer 9] 开始训练
2025-08-01 11:00:45,294 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 11:00:45,296 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 11:00:45,301 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:45,303 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:00:45,312 - INFO - [Trainer 9] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:00:45,314 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 11:00:45,343 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:45,345 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:45,439 - INFO - [Trainer 3] Batch 5, Loss: 1.8627
2025-08-01 11:00:45,442 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5005, y=[6, 6, 8, 6, 6]
2025-08-01 11:00:45,442 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 11:00:45,575 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 11:00:45,583 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3693
2025-08-01 11:00:45,584 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:45,588 - INFO - [Trainer 9] 标签样本: [6, 6, 8, 8, 8]
2025-08-01 11:00:45,755 - INFO - [Trainer 10] Batch 5, Loss: 1.0339
2025-08-01 11:00:45,868 - INFO - [Trainer 5] Batch 5, Loss: 1.4359
2025-08-01 11:00:46,060 - INFO - [Trainer 9] Batch 0, Loss: 2.2672
2025-08-01 11:00:46,362 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:46,363 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:46,579 - INFO - [Trainer 4] Batch 5, Loss: 0.8890
2025-08-01 11:00:47,139 - INFO - 客户端 7 开始异步训练循环
2025-08-01 11:00:47,141 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:47,146 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 11:00:47,147 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 11:00:47,148 - INFO - [Client 7] 开始验证训练集
2025-08-01 11:00:47,152 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:00:47,152 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 11:00:47,153 - INFO - [Trainer 7] 开始训练
2025-08-01 11:00:47,156 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 11:00:47,162 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 11:00:47,164 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:47,166 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:00:47,167 - INFO - 客户端 2 开始异步训练循环
2025-08-01 11:00:47,169 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:00:47,175 - INFO - [Trainer 7] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:00:47,176 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 11:00:47,177 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 11:00:47,180 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 11:00:47,188 - INFO - [Client 2] 开始验证训练集
2025-08-01 11:00:47,200 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:00:47,202 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 11:00:47,204 - INFO - [Trainer 2] 开始训练
2025-08-01 11:00:47,207 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 11:00:47,223 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 11:00:47,227 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:00:47,228 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:00:47,233 - INFO - [Trainer 2] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:00:47,233 - INFO - [Trainer 2] 开始第 1/2 个epoch
2025-08-01 11:00:47,372 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:47,374 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:47,382 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2591, y=[2, 2, 2, 2, 2]
2025-08-01 11:00:47,383 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 11:00:47,389 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1956, y=[8, 8, 8, 8, 8]
2025-08-01 11:00:47,389 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 11:00:47,505 - INFO - [Trainer 6] Batch 5, Loss: 1.4692
2025-08-01 11:00:47,582 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 11:00:47,588 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0978
2025-08-01 11:00:47,589 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:47,589 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 11:00:47,592 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 11:00:47,611 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2990
2025-08-01 11:00:47,613 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:47,615 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:00:47,847 - INFO - [Trainer 1] Batch 5, Loss: 1.9155
2025-08-01 11:00:47,948 - INFO - [Trainer 8] Batch 5, Loss: 2.4374
2025-08-01 11:00:48,101 - INFO - [Trainer 2] Batch 0, Loss: 2.4224
2025-08-01 11:00:48,130 - INFO - [Trainer 7] Batch 0, Loss: 2.1125
2025-08-01 11:00:48,386 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:48,386 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:48,591 - INFO - [Trainer 3] Epoch 1 进度: 10/10 批次
2025-08-01 11:00:48,610 - INFO - [Trainer 3] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2481
2025-08-01 11:00:48,612 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:48,614 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:00:48,803 - INFO - [Trainer 10] Epoch 1 进度: 10/10 批次
2025-08-01 11:00:48,812 - INFO - [Trainer 10] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2557
2025-08-01 11:00:48,813 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:48,813 - INFO - [Trainer 10] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 11:00:48,931 - INFO - [Trainer 3] Batch 9, Loss: 0.3872
2025-08-01 11:00:49,022 - INFO - [Trainer 5] Epoch 1 进度: 10/10 批次
2025-08-01 11:00:49,034 - INFO - [Trainer 5] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4233
2025-08-01 11:00:49,035 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:49,036 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 3, 7]
2025-08-01 11:00:49,094 - INFO - [Trainer 10] Batch 9, Loss: 0.7205
2025-08-01 11:00:49,207 - INFO - [Trainer 3] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:00:49,207 - INFO - [Trainer 3] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.3996, Accuracy: 81.33%
2025-08-01 11:00:49,208 - INFO - [Trainer 3] 开始第 2/2 个epoch
2025-08-01 11:00:49,314 - INFO - [Trainer 5] Batch 9, Loss: 0.9370
2025-08-01 11:00:49,338 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3322, y=[2, 2, 2, 2, 2]
2025-08-01 11:00:49,340 - INFO - [Trainer 3] Epoch 2 开始处理 10 个批次
2025-08-01 11:00:49,388 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:49,390 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:49,449 - INFO - [Trainer 10] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:00:49,450 - INFO - [Trainer 10] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.4863, Accuracy: 64.67%
2025-08-01 11:00:49,452 - INFO - [Trainer 10] 开始第 2/2 个epoch
2025-08-01 11:00:49,467 - INFO - [Trainer 3] Epoch 2 进度: 1/10 批次
2025-08-01 11:00:49,489 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4639
2025-08-01 11:00:49,493 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:49,495 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:00:49,548 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2985, y=[8, 8, 6, 7, 8]
2025-08-01 11:00:49,552 - INFO - [Trainer 10] Epoch 2 开始处理 10 个批次
2025-08-01 11:00:49,592 - INFO - [Trainer 10] Epoch 2 进度: 1/10 批次
2025-08-01 11:00:49,609 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2990
2025-08-01 11:00:49,612 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:49,613 - INFO - [Trainer 10] 标签样本: [6, 8, 7, 0, 8]
2025-08-01 11:00:49,832 - INFO - [Trainer 5] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:00:49,835 - INFO - [Trainer 5] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.3287, Accuracy: 65.33%
2025-08-01 11:00:49,835 - INFO - [Trainer 5] 开始第 2/2 个epoch
2025-08-01 11:00:49,936 - INFO - [Trainer 4] Epoch 2 进度: 10/10 批次
2025-08-01 11:00:49,947 - INFO - [Trainer 3] Batch 0, Loss: 0.3422
2025-08-01 11:00:49,950 - INFO - [Trainer 4] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.6066
2025-08-01 11:00:49,950 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:49,952 - INFO - [Trainer 4] 标签样本: [0, 0, 0, 1, 1]
2025-08-01 11:00:49,985 - INFO - [Trainer 10] Batch 0, Loss: 0.6380
2025-08-01 11:00:50,036 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4025, y=[7, 7, 7, 7, 7]
2025-08-01 11:00:50,038 - INFO - [Trainer 5] Epoch 2 开始处理 10 个批次
2025-08-01 11:00:50,171 - INFO - [Trainer 5] Epoch 2 进度: 1/10 批次
2025-08-01 11:00:50,186 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4901
2025-08-01 11:00:50,188 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:50,189 - INFO - [Trainer 5] 标签样本: [3, 7, 7, 7, 7]
2025-08-01 11:00:50,262 - INFO - [Trainer 4] Batch 9, Loss: 2.7426
2025-08-01 11:00:50,402 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:50,403 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:00:50,470 - INFO - [Trainer 9] Batch 5, Loss: 0.6188
2025-08-01 11:00:50,557 - INFO - [Trainer 5] Batch 0, Loss: 1.2169
2025-08-01 11:00:50,613 - INFO - [Trainer 4] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:00:50,614 - INFO - [Trainer 4] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.4336, Accuracy: 54.33%
2025-08-01 11:00:50,625 - INFO - [Trainer 4] 参数 conv1.weight: 平均值=0.000073, 标准差=0.115780
2025-08-01 11:00:50,626 - INFO - [Trainer 4] 参数 bn1.weight: 平均值=1.000061, 标准差=0.011926
2025-08-01 11:00:50,630 - INFO - [Trainer 4] 参数 bn1.bias: 平均值=0.000209, 标准差=0.008141
2025-08-01 11:00:50,633 - INFO - [Trainer 4] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:00:50,635 - INFO - [Trainer.get_report] 客户端 4 训练报告 - Loss: 1.4534, Accuracy: 52.67%, 陈旧度: 0
2025-08-01 11:00:50,636 - INFO - [Trainer 4] 训练报告生成完成: Loss=1.4534, Accuracy=52.67%
2025-08-01 11:00:50,637 - INFO - [Client 4] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:00:50,643 - INFO - [Client 4] 使用训练准确率作为客户端准确率: 0.5267
2025-08-01 11:00:50,647 - INFO - [Client 4] 第 1 轮训练完成，耗时: 12.46秒, 准确率: 0.5267
2025-08-01 11:00:50,649 - INFO - [Client 4] 开始提取模型权重
2025-08-01 11:00:50,659 - INFO - [Client 4] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:00:50,663 - INFO - [Client 4] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:00:50,664 - INFO - [Client 4] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:00:50,665 - INFO - [Client 4] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:00:50,665 - INFO - [Client 4] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:00:50,667 - INFO - [服务器] 🔄 收到客户端 4 的模型更新，模型版本: unknown
2025-08-01 11:00:50,672 - INFO - [服务器] 客户端 4 训练信息 - 样本数: 300, 训练时间: 12.46秒
2025-08-01 11:00:50,676 - INFO - [服务器] 当前缓冲池大小: 0, 全局轮次: 0
2025-08-01 11:00:50,725 - INFO - [客户端权重摘要] 客户端4 | 参数数量: 74, 均值: 0.001334, 最大: 20.000000, 最小: -0.372431
2025-08-01 11:00:50,730 - INFO - 客户端 4 更新记录 - 陈旧度: 0, 提交轮次: 0
2025-08-01 11:00:50,731 - INFO - ✅ 客户端 4 的更新已加入成功缓冲池，当前池大小: 1
2025-08-01 11:00:50,733 - INFO - 📊 当前缓冲池中的客户端: [4]
2025-08-01 11:00:50,738 - INFO - 🔍 客户端 4 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:00:50,740 - INFO - 成功处理客户端 4 的更新
2025-08-01 11:00:50,750 - INFO - [Client 4] ✅ 成功上传训练结果到服务器
2025-08-01 11:00:50,753 - INFO - 客户端 4 训练完成
2025-08-01 11:00:50,872 - INFO - [Trainer 1] Epoch 2 进度: 10/10 批次
2025-08-01 11:00:50,893 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1523
2025-08-01 11:00:50,893 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:50,894 - INFO - [Trainer 1] 标签样本: [4, 5, 4, 0, 4]
2025-08-01 11:00:51,085 - INFO - [Trainer 1] Batch 9, Loss: 2.8561
2025-08-01 11:00:51,206 - INFO - [Trainer 6] Epoch 2 进度: 10/10 批次
2025-08-01 11:00:51,223 - INFO - [Trainer 6] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3346
2025-08-01 11:00:51,226 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:51,226 - INFO - [Trainer 6] 标签样本: [8, 8, 8, 7, 0]
2025-08-01 11:00:51,301 - INFO - [Trainer 8] Epoch 2 进度: 10/10 批次
2025-08-01 11:00:51,321 - INFO - [Trainer 8] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4489
2025-08-01 11:00:51,323 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:51,325 - INFO - [Trainer 8] 标签样本: [0, 0, 6, 1, 1]
2025-08-01 11:00:51,379 - INFO - [Trainer 6] Batch 9, Loss: 1.8200
2025-08-01 11:00:51,407 - WARNING - 配置文件设置min_clients_for_aggregation=1，可能导致每轮只聚合一个客户端
2025-08-01 11:00:51,412 - INFO - 异步模式下，当前有 1 个客户端更新，满足最小阈值 1，可以触发聚合
2025-08-01 11:00:51,414 - INFO - ✅ 触发聚合条件，开始第 0 轮聚合
2025-08-01 11:00:51,423 - INFO - 🔒 开始聚合过程，轮次: 0
2025-08-01 11:00:51,442 - DEBUG - 估计客户端4训练+通信时间: 2.5408120735760416秒 (计算: 1.6212678437985881秒, 通信: 0.9195442297774534秒, 距离: 1.43km, 带宽: 248825Hz, SNR: 2.59e+05)
2025-08-01 11:00:51,443 - INFO - 候选客户端按训练时间排序: [(4, np.float64(2.5408120735760416))]
2025-08-01 11:00:51,444 - INFO - 算法参数: V=1.0, max_clients=5, tau_max=5
2025-08-01 11:00:51,444 - DEBUG - 尝试聚合 1 客户端 (当前客户端: 4)
2025-08-01 11:00:51,444 - DEBUG -   模拟D_t: 2.5408, V*D_t: 2.5408
2025-08-01 11:00:51,446 - DEBUG -   惩罚项: 0.0000, 总目标值: 2.5408
2025-08-01 11:00:51,446 - DEBUG - 更新最佳集合：目标值 2.5408 < 之前最小 2.5408
2025-08-01 11:00:51,446 - INFO - 贪心策略选择 1 个客户端进行聚合: [4]
2025-08-01 11:00:51,447 - INFO - 最优目标函数值: 2.5408
2025-08-01 11:00:51,451 - INFO - 开始执行聚合 - 参与聚合的客户端数量: 1个
2025-08-01 11:00:51,453 - INFO - [Trainer.update_staleness] 客户端 4 陈旧度更新为: 0
2025-08-01 11:00:51,455 - DEBUG - 参与聚合的客户端 4 陈旧度: 0 (当前服务器轮次:0, 客户端拉取模型轮次:0)
2025-08-01 11:00:51,455 - DEBUG - 客户端 4 在服务器轮次 0 被聚合
2025-08-01 11:00:51,455 - INFO - 聚合执行 - 聚合客户端ID: [4]
2025-08-01 11:00:51,478 - INFO - [Trainer 8] Batch 9, Loss: 0.6712
2025-08-01 11:00:51,509 - INFO - [聚合前全局权重] 均值: 0.001182, 最大: 1.000000, 最小: -0.192125
2025-08-01 11:00:51,512 - INFO - [Algorithm] 客户端 4 - 样本数: 300, 陈旧度: 0, 陈旧度因子: 1.0000, 原始权重: 300.0000
2025-08-01 11:00:51,512 - INFO - [Algorithm] 客户端 4 最终权重: 1.0000
2025-08-01 11:00:51,516 - INFO - [Trainer 1] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:00:51,516 - INFO - [Trainer 1] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 2.4485, Accuracy: 30.00%
2025-08-01 11:00:51,519 - INFO - [Trainer 1] 参数 conv1.weight: 平均值=-0.001554, 标准差=0.114877
2025-08-01 11:00:51,522 - INFO - [Trainer 1] 参数 bn1.weight: 平均值=0.999849, 标准差=0.010262
2025-08-01 11:00:51,523 - INFO - [Trainer 1] 参数 bn1.bias: 平均值=-0.001069, 标准差=0.010269
2025-08-01 11:00:51,524 - INFO - [Trainer 1] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:00:51,524 - INFO - [Trainer.get_report] 客户端 1 训练报告 - Loss: 2.1820, Accuracy: 34.50%, 陈旧度: 0
2025-08-01 11:00:51,524 - INFO - [Trainer 1] 训练报告生成完成: Loss=2.1820, Accuracy=34.50%
2025-08-01 11:00:51,527 - INFO - [Client 1] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:00:51,527 - INFO - [Client 1] 使用训练准确率作为客户端准确率: 0.3450
2025-08-01 11:00:51,528 - INFO - [Client 1] 第 1 轮训练完成，耗时: 12.72秒, 准确率: 0.3450
2025-08-01 11:00:51,528 - INFO - [Client 1] 开始提取模型权重
2025-08-01 11:00:51,536 - INFO - [Client 1] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:00:51,536 - INFO - [Client 1] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:00:51,537 - INFO - [Client 1] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:00:51,537 - INFO - [Client 1] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:00:51,538 - INFO - [Client 1] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:00:51,538 - INFO - [服务器] 🔄 收到客户端 1 的模型更新，模型版本: unknown
2025-08-01 11:00:51,538 - INFO - [服务器] 客户端 1 训练信息 - 样本数: 300, 训练时间: 12.72秒
2025-08-01 11:00:51,539 - INFO - [服务器] 当前缓冲池大小: 1, 全局轮次: 0
2025-08-01 11:00:51,551 - INFO - [客户端权重摘要] 客户端1 | 参数数量: 74, 均值: 0.001250, 最大: 20.000000, 最小: -0.264114
2025-08-01 11:00:51,552 - INFO - 客户端 1 更新记录 - 陈旧度: 0, 提交轮次: 0
2025-08-01 11:00:51,554 - INFO - ✅ 客户端 1 的更新已加入成功缓冲池，当前池大小: 2
2025-08-01 11:00:51,556 - INFO - 📊 当前缓冲池中的客户端: [4, 1]
2025-08-01 11:00:51,558 - INFO - 🔍 客户端 1 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:00:51,558 - INFO - 成功处理客户端 1 的更新
2025-08-01 11:00:51,560 - INFO - [Client 1] ✅ 成功上传训练结果到服务器
2025-08-01 11:00:51,561 - INFO - 客户端 1 训练完成
2025-08-01 11:00:51,640 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-08-01 11:00:51,642 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-08-01 11:00:51,642 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-08-01 11:00:51,651 - INFO - [Trainer 6] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:00:51,652 - INFO - [Trainer 6] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.8097, Accuracy: 45.00%
2025-08-01 11:00:51,653 - INFO - [Trainer 6] 参数 conv1.weight: 平均值=0.004774, 标准差=0.114345
2025-08-01 11:00:51,656 - INFO - [Trainer 6] 参数 bn1.weight: 平均值=1.000222, 标准差=0.011265
2025-08-01 11:00:51,657 - INFO - [Trainer 6] 参数 bn1.bias: 平均值=0.000895, 标准差=0.008495
2025-08-01 11:00:51,659 - INFO - [Trainer 6] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:00:51,661 - INFO - [Trainer.get_report] 客户端 6 训练报告 - Loss: 1.6964, Accuracy: 43.17%, 陈旧度: 0
2025-08-01 11:00:51,664 - INFO - [Trainer 6] 训练报告生成完成: Loss=1.6964, Accuracy=43.17%
2025-08-01 11:00:51,669 - INFO - [Client 6] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:00:51,671 - INFO - [Client 6] 使用训练准确率作为客户端准确率: 0.4317
2025-08-01 11:00:51,674 - INFO - [Client 6] 第 1 轮训练完成，耗时: 12.94秒, 准确率: 0.4317
2025-08-01 11:00:51,676 - INFO - [Client 6] 开始提取模型权重
2025-08-01 11:00:51,683 - INFO - [Client 6] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:00:51,689 - INFO - [Client 6] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:00:51,691 - INFO - [Client 6] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:00:51,694 - INFO - [Client 6] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:00:51,695 - INFO - [Client 6] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:00:51,696 - INFO - [服务器] 🔄 收到客户端 6 的模型更新，模型版本: unknown
2025-08-01 11:00:51,699 - INFO - [服务器] 客户端 6 训练信息 - 样本数: 300, 训练时间: 12.94秒
2025-08-01 11:00:51,702 - INFO - [服务器] 当前缓冲池大小: 2, 全局轮次: 0
2025-08-01 11:00:51,711 - INFO - [Algorithm] 成功加载权重到模型
2025-08-01 11:00:51,729 - INFO - [客户端权重摘要] 客户端6 | 参数数量: 74, 均值: 0.001117, 最大: 20.000000, 最小: -0.327838
2025-08-01 11:00:51,730 - INFO - 客户端 6 更新记录 - 陈旧度: 0, 提交轮次: 0
2025-08-01 11:00:51,733 - INFO - ✅ 客户端 6 的更新已加入成功缓冲池，当前池大小: 3
2025-08-01 11:00:51,735 - INFO - 📊 当前缓冲池中的客户端: [4, 1, 6]
2025-08-01 11:00:51,736 - INFO - 🔍 客户端 6 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:00:51,736 - INFO - 成功处理客户端 6 的更新
2025-08-01 11:00:51,744 - INFO - [Client 6] ✅ 成功上传训练结果到服务器
2025-08-01 11:00:51,744 - INFO - [聚合后全局权重] 均值: 0.001334, 最大: 20.000000, 最小: -0.372431
2025-08-01 11:00:51,748 - INFO - 客户端 6 训练完成
2025-08-01 11:00:51,763 - INFO - [权重变化] 平均绝对差异: 0.014426
2025-08-01 11:00:51,778 - INFO - ✅ 创建权重变化记录文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs/weights_change_20250801_110051.csv
2025-08-01 11:00:51,784 - INFO - 轮次 0 权重变化记录 - 平均变化: 0.014426, 均值: 0.001334, 最大: 20.000000, 最小: -0.372431
2025-08-01 11:00:51,785 - INFO - 服务器轮次 0 聚合完成: 更新了全局模型
2025-08-01 11:00:51,789 - INFO - [DEBUG] simulate_wall_time=True, asynchronous_mode=True
2025-08-01 11:00:51,790 - INFO - [DEBUG] wall_time更新: 1754017230.0914042 -> 1754017230.9301312 (增加了0.8387269973754883秒)
2025-08-01 11:00:51,791 - INFO - 记录轮次: 0
2025-08-01 11:00:51,792 - INFO - 聚合完成，进入新轮次，当前全局轮次: 1
2025-08-01 11:00:51,792 - INFO - 更新最后聚合时间: 1754017251.7922866
2025-08-01 11:00:51,796 - INFO - [Trainer 8] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:00:51,797 - INFO - [Trainer 8] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.1911, Accuracy: 55.33%
2025-08-01 11:00:51,802 - INFO - [Trainer 8] 参数 conv1.weight: 平均值=0.002659, 标准差=0.117078
2025-08-01 11:00:51,804 - INFO - [Trainer 8] 参数 bn1.weight: 平均值=0.999275, 标准差=0.012372
2025-08-01 11:00:51,807 - INFO - [Trainer 8] 参数 bn1.bias: 平均值=0.000112, 标准差=0.008589
2025-08-01 11:00:51,808 - INFO - [Trainer 8] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:00:51,810 - INFO - [Trainer.get_report] 客户端 8 训练报告 - Loss: 1.2308, Accuracy: 55.50%, 陈旧度: 0
2025-08-01 11:00:51,810 - INFO - [Trainer 8] 训练报告生成完成: Loss=1.2308, Accuracy=55.50%
2025-08-01 11:00:51,814 - INFO - [Client 8] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:00:51,817 - INFO - [Client 8] 使用训练准确率作为客户端准确率: 0.5550
2025-08-01 11:00:51,821 - INFO - [Client 8] 第 1 轮训练完成，耗时: 13.09秒, 准确率: 0.5550
2025-08-01 11:00:51,821 - INFO - [Client 8] 开始提取模型权重
2025-08-01 11:00:51,853 - INFO - [Client 8] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:00:51,858 - INFO - [Client 8] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:00:51,859 - INFO - [Client 8] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:00:51,859 - INFO - [Client 8] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:00:51,860 - INFO - [Client 8] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:00:51,860 - INFO - [服务器] 🔄 收到客户端 8 的模型更新，模型版本: unknown
2025-08-01 11:00:51,862 - INFO - [服务器] 客户端 8 训练信息 - 样本数: 300, 训练时间: 13.09秒
2025-08-01 11:00:51,862 - INFO - [服务器] 当前缓冲池大小: 3, 全局轮次: 1
2025-08-01 11:00:51,894 - INFO - [客户端权重摘要] 客户端8 | 参数数量: 74, 均值: 0.001336, 最大: 20.000000, 最小: -0.365797
2025-08-01 11:00:51,895 - INFO - 客户端 8 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:00:51,896 - INFO - ✅ 客户端 8 的更新已加入成功缓冲池，当前池大小: 4
2025-08-01 11:00:51,897 - INFO - 📊 当前缓冲池中的客户端: [4, 1, 6, 8]
2025-08-01 11:00:51,897 - INFO - 🔍 客户端 8 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:00:51,897 - INFO - 成功处理客户端 8 的更新
2025-08-01 11:00:51,898 - INFO - [Client 8] ✅ 成功上传训练结果到服务器
2025-08-01 11:00:51,898 - INFO - 客户端 8 训练完成
2025-08-01 11:00:51,900 - INFO - 为训练器创建了模型的深拷贝，重置BatchNorm统计信息，并移至设备: cpu
2025-08-01 11:00:51,901 - INFO - 已将全局权重加载到评估模型中
2025-08-01 11:00:51,903 - INFO - 开始评估全局模型，测试集大小: 10000
2025-08-01 11:00:52,175 - INFO - [Trainer 2] Batch 5, Loss: 0.9106
2025-08-01 11:00:52,652 - INFO - [Trainer 7] Batch 5, Loss: 1.1113
2025-08-01 11:00:52,925 - INFO - [Trainer 9] Epoch 1 进度: 10/10 批次
2025-08-01 11:00:52,931 - INFO - [Trainer 9] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.6952, x.mean: -0.3710
2025-08-01 11:00:52,933 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:52,935 - INFO - [Trainer 9] 标签样本: [7, 6, 8, 8, 8]
2025-08-01 11:00:53,055 - INFO - [Trainer 9] Batch 9, Loss: 4.4003
2025-08-01 11:00:53,420 - INFO - [Trainer 9] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:00:53,420 - INFO - [Trainer 9] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.2042, Accuracy: 73.00%
2025-08-01 11:00:53,420 - INFO - [Trainer 9] 开始第 2/2 个epoch
2025-08-01 11:00:53,512 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1923, y=[6, 6, 8, 6, 8]
2025-08-01 11:00:53,514 - INFO - [Trainer 9] Epoch 2 开始处理 10 个批次
2025-08-01 11:00:53,620 - INFO - [Trainer 9] Epoch 2 进度: 1/10 批次
2025-08-01 11:00:53,632 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2961
2025-08-01 11:00:53,632 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:53,633 - INFO - [Trainer 9] 标签样本: [6, 6, 8, 8, 6]
2025-08-01 11:00:53,757 - INFO - [Trainer 9] Batch 0, Loss: 0.8515
2025-08-01 11:00:53,812 - INFO - [Trainer 10] Batch 5, Loss: 1.5736
2025-08-01 11:00:53,882 - INFO - [Trainer 3] Batch 5, Loss: 0.4210
2025-08-01 11:00:53,966 - INFO - [Trainer 5] Batch 5, Loss: 0.3300
2025-08-01 11:00:54,727 - INFO - [Trainer 2] Epoch 1 进度: 10/10 批次
2025-08-01 11:00:54,739 - INFO - [Trainer 2] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0666
2025-08-01 11:00:54,739 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:54,741 - INFO - [Trainer 2] 标签样本: [8, 8, 5, 8, 8]
2025-08-01 11:00:54,851 - INFO - [Trainer 2] Batch 9, Loss: 0.5375
2025-08-01 11:00:55,195 - INFO - [Trainer 2] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:00:55,199 - INFO - [Trainer 2] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.0856, Accuracy: 85.33%
2025-08-01 11:00:55,200 - INFO - [Trainer 2] 开始第 2/2 个epoch
2025-08-01 11:00:55,229 - INFO - [Trainer 7] Epoch 1 进度: 10/10 批次
2025-08-01 11:00:55,239 - INFO - [Trainer 7] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.6078
2025-08-01 11:00:55,244 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:55,247 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:00:55,282 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2383, y=[8, 8, 8, 8, 8]
2025-08-01 11:00:55,283 - INFO - [Trainer 2] Epoch 2 开始处理 10 个批次
2025-08-01 11:00:55,331 - INFO - [Trainer 7] Batch 9, Loss: 0.0000
2025-08-01 11:00:55,340 - INFO - [Trainer 2] Epoch 2 进度: 1/10 批次
2025-08-01 11:00:55,347 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1822
2025-08-01 11:00:55,349 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:55,350 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 11:00:55,438 - INFO - [Trainer 2] Batch 0, Loss: 3.0953
2025-08-01 11:00:55,598 - INFO - [Trainer 7] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:00:55,600 - INFO - [Trainer 7] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.4643, Accuracy: 92.33%
2025-08-01 11:00:55,601 - INFO - [Trainer 7] 开始第 2/2 个epoch
2025-08-01 11:00:55,647 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2498, y=[2, 2, 2, 2, 2]
2025-08-01 11:00:55,650 - INFO - [Trainer 7] Epoch 2 开始处理 10 个批次
2025-08-01 11:00:55,724 - INFO - [Trainer 7] Epoch 2 进度: 1/10 批次
2025-08-01 11:00:55,735 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3161
2025-08-01 11:00:55,736 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:55,737 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:00:55,950 - INFO - [Trainer 7] Batch 0, Loss: 1.0184
2025-08-01 11:00:56,166 - INFO - [Trainer 10] Epoch 2 进度: 10/10 批次
2025-08-01 11:00:56,179 - INFO - [Trainer 10] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4678
2025-08-01 11:00:56,181 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:56,181 - INFO - [Trainer 10] 标签样本: [6, 6, 6, 6, 8]
2025-08-01 11:00:56,257 - INFO - [Trainer 3] Epoch 2 进度: 10/10 批次
2025-08-01 11:00:56,266 - INFO - [Trainer 3] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4051
2025-08-01 11:00:56,267 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:56,268 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:00:56,281 - INFO - [Trainer 10] Batch 9, Loss: 0.2355
2025-08-01 11:00:56,324 - INFO - [Trainer 3] Batch 9, Loss: 0.3950
2025-08-01 11:00:56,418 - INFO - [Trainer 5] Epoch 2 进度: 10/10 批次
2025-08-01 11:00:56,442 - INFO - [Trainer 5] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3796
2025-08-01 11:00:56,443 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:56,444 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 7, 7]
2025-08-01 11:00:56,549 - INFO - [Trainer 10] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:00:56,549 - INFO - [Trainer 10] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.8422, Accuracy: 78.00%
2025-08-01 11:00:56,550 - INFO - [Trainer 10] 参数 conv1.weight: 平均值=0.002844, 标准差=0.112310
2025-08-01 11:00:56,551 - INFO - [Trainer 10] 参数 bn1.weight: 平均值=1.000298, 标准差=0.010327
2025-08-01 11:00:56,552 - INFO - [Trainer 10] 参数 bn1.bias: 平均值=-0.000210, 标准差=0.009477
2025-08-01 11:00:56,552 - INFO - [Trainer 10] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:00:56,553 - INFO - [Trainer.get_report] 客户端 10 训练报告 - Loss: 1.1643, Accuracy: 71.33%, 陈旧度: 0
2025-08-01 11:00:56,555 - INFO - [Trainer 10] 训练报告生成完成: Loss=1.1643, Accuracy=71.33%
2025-08-01 11:00:56,558 - INFO - [Client 10] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:00:56,562 - INFO - [Client 10] 使用训练准确率作为客户端准确率: 0.7133
2025-08-01 11:00:56,563 - INFO - [Client 10] 第 1 轮训练完成，耗时: 14.83秒, 准确率: 0.7133
2025-08-01 11:00:56,565 - INFO - [Client 10] 开始提取模型权重
2025-08-01 11:00:56,566 - INFO - [Client 10] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:00:56,571 - INFO - [Client 10] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:00:56,572 - INFO - [Client 10] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:00:56,574 - INFO - [Client 10] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:00:56,577 - INFO - [Client 10] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:00:56,577 - INFO - [服务器] 🔄 收到客户端 10 的模型更新，模型版本: unknown
2025-08-01 11:00:56,578 - INFO - [服务器] 客户端 10 训练信息 - 样本数: 300, 训练时间: 14.83秒
2025-08-01 11:00:56,578 - INFO - [服务器] 当前缓冲池大小: 4, 全局轮次: 1
2025-08-01 11:00:56,585 - INFO - [Trainer 5] Batch 9, Loss: 0.2676
2025-08-01 11:00:56,597 - INFO - [客户端权重摘要] 客户端10 | 参数数量: 74, 均值: 0.001220, 最大: 20.000000, 最小: -0.297372
2025-08-01 11:00:56,599 - INFO - 客户端 10 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:00:56,601 - INFO - ✅ 客户端 10 的更新已加入成功缓冲池，当前池大小: 5
2025-08-01 11:00:56,602 - INFO - 📊 当前缓冲池中的客户端: [4, 1, 6, 8, 10]
2025-08-01 11:00:56,603 - INFO - 🔍 客户端 10 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:00:56,604 - INFO - 成功处理客户端 10 的更新
2025-08-01 11:00:56,604 - INFO - [Client 10] ✅ 成功上传训练结果到服务器
2025-08-01 11:00:56,604 - INFO - 客户端 10 训练完成
2025-08-01 11:00:56,623 - INFO - [Trainer 3] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:00:56,626 - INFO - [Trainer 3] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.4879, Accuracy: 89.67%
2025-08-01 11:00:56,629 - INFO - [Trainer 3] 参数 conv1.weight: 平均值=0.018341, 标准差=0.135811
2025-08-01 11:00:56,629 - INFO - [Trainer 3] 参数 bn1.weight: 平均值=1.000186, 标准差=0.023460
2025-08-01 11:00:56,630 - INFO - [Trainer 3] 参数 bn1.bias: 平均值=-0.004554, 标准差=0.008849
2025-08-01 11:00:56,631 - INFO - [Trainer 3] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:00:56,633 - INFO - [Trainer.get_report] 客户端 3 训练报告 - Loss: 0.9438, Accuracy: 85.50%, 陈旧度: 0
2025-08-01 11:00:56,634 - INFO - [Trainer 3] 训练报告生成完成: Loss=0.9438, Accuracy=85.50%
2025-08-01 11:00:56,634 - INFO - [Client 3] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:00:56,635 - INFO - [Client 3] 使用训练准确率作为客户端准确率: 0.8550
2025-08-01 11:00:56,636 - INFO - [Client 3] 第 1 轮训练完成，耗时: 15.10秒, 准确率: 0.8550
2025-08-01 11:00:56,637 - INFO - [Client 3] 开始提取模型权重
2025-08-01 11:00:56,639 - INFO - [Client 3] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:00:56,639 - INFO - [Client 3] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:00:56,640 - INFO - [Client 3] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:00:56,641 - INFO - [Client 3] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:00:56,643 - INFO - [Client 3] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:00:56,647 - INFO - [服务器] 🔄 收到客户端 3 的模型更新，模型版本: unknown
2025-08-01 11:00:56,647 - INFO - [服务器] 客户端 3 训练信息 - 样本数: 300, 训练时间: 15.10秒
2025-08-01 11:00:56,648 - INFO - [服务器] 当前缓冲池大小: 5, 全局轮次: 1
2025-08-01 11:00:56,664 - INFO - [客户端权重摘要] 客户端3 | 参数数量: 74, 均值: 0.001574, 最大: 20.000000, 最小: -0.348376
2025-08-01 11:00:56,665 - INFO - 客户端 3 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:00:56,669 - INFO - ✅ 客户端 3 的更新已加入成功缓冲池，当前池大小: 6
2025-08-01 11:00:56,670 - INFO - 📊 当前缓冲池中的客户端: [4, 1, 6, 8, 10, 3]
2025-08-01 11:00:56,670 - INFO - 🔍 客户端 3 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:00:56,670 - INFO - 成功处理客户端 3 的更新
2025-08-01 11:00:56,672 - INFO - [Client 3] ✅ 成功上传训练结果到服务器
2025-08-01 11:00:56,673 - INFO - 客户端 3 训练完成
2025-08-01 11:00:56,851 - INFO - [Trainer 9] Batch 5, Loss: 0.7200
2025-08-01 11:00:56,861 - INFO - [Trainer 5] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:00:56,863 - INFO - [Trainer 5] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.7698, Accuracy: 73.33%
2025-08-01 11:00:56,867 - INFO - [Trainer 5] 参数 conv1.weight: 平均值=0.005136, 标准差=0.114134
2025-08-01 11:00:56,871 - INFO - [Trainer 5] 参数 bn1.weight: 平均值=0.999703, 标准差=0.008669
2025-08-01 11:00:56,872 - INFO - [Trainer 5] 参数 bn1.bias: 平均值=0.000311, 标准差=0.008278
2025-08-01 11:00:56,874 - INFO - [Trainer 5] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:00:56,874 - INFO - [Trainer.get_report] 客户端 5 训练报告 - Loss: 1.0493, Accuracy: 69.33%, 陈旧度: 0
2025-08-01 11:00:56,875 - INFO - [Trainer 5] 训练报告生成完成: Loss=1.0493, Accuracy=69.33%
2025-08-01 11:00:56,877 - INFO - [Client 5] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:00:56,878 - INFO - [Client 5] 使用训练准确率作为客户端准确率: 0.6933
2025-08-01 11:00:56,879 - INFO - [Client 5] 第 1 轮训练完成，耗时: 15.26秒, 准确率: 0.6933
2025-08-01 11:00:56,880 - INFO - [Client 5] 开始提取模型权重
2025-08-01 11:00:56,884 - INFO - [Client 5] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:00:56,886 - INFO - [Client 5] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:00:56,887 - INFO - [Client 5] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:00:56,887 - INFO - [Client 5] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:00:56,889 - INFO - [Client 5] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:00:56,889 - INFO - [服务器] 🔄 收到客户端 5 的模型更新，模型版本: unknown
2025-08-01 11:00:56,891 - INFO - [服务器] 客户端 5 训练信息 - 样本数: 300, 训练时间: 15.26秒
2025-08-01 11:00:56,892 - INFO - [服务器] 当前缓冲池大小: 6, 全局轮次: 1
2025-08-01 11:00:56,913 - INFO - [客户端权重摘要] 客户端5 | 参数数量: 74, 均值: 0.001053, 最大: 20.000000, 最小: -0.259872
2025-08-01 11:00:56,917 - INFO - 客户端 5 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:00:56,919 - INFO - ✅ 客户端 5 的更新已加入成功缓冲池，当前池大小: 7
2025-08-01 11:00:56,919 - INFO - 📊 当前缓冲池中的客户端: [4, 1, 6, 8, 10, 3, 5]
2025-08-01 11:00:56,921 - INFO - 🔍 客户端 5 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:00:56,923 - INFO - 成功处理客户端 5 的更新
2025-08-01 11:00:56,924 - INFO - [Client 5] ✅ 成功上传训练结果到服务器
2025-08-01 11:00:56,925 - INFO - 客户端 5 训练完成
2025-08-01 11:00:57,851 - INFO - [Trainer 2] Batch 5, Loss: 0.2547
2025-08-01 11:00:58,140 - INFO - [Trainer 7] Batch 5, Loss: 0.0000
2025-08-01 11:00:58,178 - INFO - [Trainer 9] Epoch 2 进度: 10/10 批次
2025-08-01 11:00:58,198 - INFO - [Trainer 9] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7342, x.mean: -0.4395
2025-08-01 11:00:58,202 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:58,206 - INFO - [Trainer 9] 标签样本: [6, 8, 6, 6, 8]
2025-08-01 11:00:58,312 - INFO - [Trainer 9] Batch 9, Loss: 0.4242
2025-08-01 11:00:58,474 - INFO - [Trainer 9] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:00:58,475 - INFO - [Trainer 9] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.7670, Accuracy: 80.33%
2025-08-01 11:00:58,478 - INFO - [Trainer 9] 参数 conv1.weight: 平均值=-0.015942, 标准差=0.118989
2025-08-01 11:00:58,480 - INFO - [Trainer 9] 参数 bn1.weight: 平均值=1.000975, 标准差=0.017957
2025-08-01 11:00:58,481 - INFO - [Trainer 9] 参数 bn1.bias: 平均值=-0.001992, 标准差=0.012759
2025-08-01 11:00:58,481 - INFO - [Trainer 9] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:00:58,483 - INFO - [Trainer.get_report] 客户端 9 训练报告 - Loss: 0.9856, Accuracy: 76.67%, 陈旧度: 0
2025-08-01 11:00:58,483 - INFO - [Trainer 9] 训练报告生成完成: Loss=0.9856, Accuracy=76.67%
2025-08-01 11:00:58,485 - INFO - [Client 9] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:00:58,485 - INFO - [Client 9] 使用训练准确率作为客户端准确率: 0.7667
2025-08-01 11:00:58,487 - INFO - [Client 9] 第 1 轮训练完成，耗时: 13.20秒, 准确率: 0.7667
2025-08-01 11:00:58,487 - INFO - [Client 9] 开始提取模型权重
2025-08-01 11:00:58,498 - INFO - [Client 9] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:00:58,501 - INFO - [Client 9] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:00:58,501 - INFO - [Client 9] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:00:58,503 - INFO - [Client 9] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:00:58,505 - INFO - [Client 9] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:00:58,505 - INFO - [服务器] 🔄 收到客户端 9 的模型更新，模型版本: unknown
2025-08-01 11:00:58,505 - INFO - [服务器] 客户端 9 训练信息 - 样本数: 300, 训练时间: 13.20秒
2025-08-01 11:00:58,506 - INFO - [服务器] 当前缓冲池大小: 7, 全局轮次: 1
2025-08-01 11:00:58,526 - INFO - [客户端权重摘要] 客户端9 | 参数数量: 74, 均值: 0.001186, 最大: 20.000000, 最小: -0.336123
2025-08-01 11:00:58,530 - INFO - 客户端 9 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:00:58,532 - INFO - ✅ 客户端 9 的更新已加入成功缓冲池，当前池大小: 8
2025-08-01 11:00:58,534 - INFO - 📊 当前缓冲池中的客户端: [4, 1, 6, 8, 10, 3, 5, 9]
2025-08-01 11:00:58,535 - INFO - 🔍 客户端 9 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:00:58,535 - INFO - 成功处理客户端 9 的更新
2025-08-01 11:00:58,535 - INFO - [Client 9] ✅ 成功上传训练结果到服务器
2025-08-01 11:00:58,538 - INFO - 客户端 9 训练完成
2025-08-01 11:00:59,113 - INFO - [Trainer 2] Epoch 2 进度: 10/10 批次
2025-08-01 11:00:59,116 - INFO - [Trainer 2] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0617
2025-08-01 11:00:59,117 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:59,117 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 11:00:59,189 - INFO - [Trainer 2] Batch 9, Loss: 0.0210
2025-08-01 11:00:59,251 - INFO - [Trainer 7] Epoch 2 进度: 10/10 批次
2025-08-01 11:00:59,258 - INFO - [Trainer 7] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3753
2025-08-01 11:00:59,258 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:00:59,260 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:00:59,300 - INFO - [Trainer 7] Batch 9, Loss: 0.0010
2025-08-01 11:00:59,301 - INFO - [Trainer 2] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:00:59,302 - INFO - [Trainer 2] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.5426, Accuracy: 95.67%
2025-08-01 11:00:59,302 - INFO - [Trainer 2] 参数 conv1.weight: 平均值=0.002628, 标准差=0.125919
2025-08-01 11:00:59,303 - INFO - [Trainer 2] 参数 bn1.weight: 平均值=0.999284, 标准差=0.028651
2025-08-01 11:00:59,303 - INFO - [Trainer 2] 参数 bn1.bias: 平均值=-0.000885, 标准差=0.014972
2025-08-01 11:00:59,304 - INFO - [Trainer 2] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:00:59,304 - INFO - [Trainer.get_report] 客户端 2 训练报告 - Loss: 0.8141, Accuracy: 90.50%, 陈旧度: 0
2025-08-01 11:00:59,304 - INFO - [Trainer 2] 训练报告生成完成: Loss=0.8141, Accuracy=90.50%
2025-08-01 11:00:59,306 - INFO - [Client 2] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:00:59,306 - INFO - [Client 2] 使用训练准确率作为客户端准确率: 0.9050
2025-08-01 11:00:59,306 - INFO - [Client 2] 第 1 轮训练完成，耗时: 12.12秒, 准确率: 0.9050
2025-08-01 11:00:59,306 - INFO - [Client 2] 开始提取模型权重
2025-08-01 11:00:59,308 - INFO - [Client 2] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:00:59,308 - INFO - [Client 2] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:00:59,309 - INFO - [Client 2] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:00:59,309 - INFO - [Client 2] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:00:59,310 - INFO - [Client 2] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:00:59,310 - INFO - [服务器] 🔄 收到客户端 2 的模型更新，模型版本: unknown
2025-08-01 11:00:59,310 - INFO - [服务器] 客户端 2 训练信息 - 样本数: 300, 训练时间: 12.12秒
2025-08-01 11:00:59,311 - INFO - [服务器] 当前缓冲池大小: 8, 全局轮次: 1
2025-08-01 11:00:59,316 - INFO - [客户端权重摘要] 客户端2 | 参数数量: 74, 均值: 0.001472, 最大: 20.000000, 最小: -0.458162
2025-08-01 11:00:59,316 - INFO - 客户端 2 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:00:59,316 - INFO - ✅ 客户端 2 的更新已加入成功缓冲池，当前池大小: 9
2025-08-01 11:00:59,317 - INFO - 📊 当前缓冲池中的客户端: [4, 1, 6, 8, 10, 3, 5, 9, 2]
2025-08-01 11:00:59,317 - INFO - 🔍 客户端 2 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:00:59,318 - INFO - 成功处理客户端 2 的更新
2025-08-01 11:00:59,318 - INFO - [Client 2] ✅ 成功上传训练结果到服务器
2025-08-01 11:00:59,318 - INFO - 客户端 2 训练完成
2025-08-01 11:00:59,374 - INFO - [Trainer 7] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:00:59,375 - INFO - [Trainer 7] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.2423, Accuracy: 99.00%
2025-08-01 11:00:59,375 - INFO - [Trainer 7] 参数 conv1.weight: 平均值=0.004188, 标准差=0.110893
2025-08-01 11:00:59,375 - INFO - [Trainer 7] 参数 bn1.weight: 平均值=0.999243, 标准差=0.009485
2025-08-01 11:00:59,376 - INFO - [Trainer 7] 参数 bn1.bias: 平均值=-0.000150, 标准差=0.004566
2025-08-01 11:00:59,376 - INFO - [Trainer 7] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:00:59,376 - INFO - [Trainer.get_report] 客户端 7 训练报告 - Loss: 0.3533, Accuracy: 95.67%, 陈旧度: 0
2025-08-01 11:00:59,376 - INFO - [Trainer 7] 训练报告生成完成: Loss=0.3533, Accuracy=95.67%
2025-08-01 11:00:59,376 - INFO - [Client 7] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:00:59,377 - INFO - [Client 7] 使用训练准确率作为客户端准确率: 0.9567
2025-08-01 11:00:59,377 - INFO - [Client 7] 第 1 轮训练完成，耗时: 12.23秒, 准确率: 0.9567
2025-08-01 11:00:59,377 - INFO - [Client 7] 开始提取模型权重
2025-08-01 11:00:59,378 - INFO - [Client 7] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:00:59,378 - INFO - [Client 7] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:00:59,378 - INFO - [Client 7] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:00:59,378 - INFO - [Client 7] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:00:59,378 - INFO - [Client 7] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:00:59,378 - INFO - [服务器] 🔄 收到客户端 7 的模型更新，模型版本: unknown
2025-08-01 11:00:59,379 - INFO - [服务器] 客户端 7 训练信息 - 样本数: 300, 训练时间: 12.23秒
2025-08-01 11:00:59,379 - INFO - [服务器] 当前缓冲池大小: 9, 全局轮次: 1
2025-08-01 11:00:59,383 - INFO - [客户端权重摘要] 客户端7 | 参数数量: 74, 均值: 0.001215, 最大: 20.000000, 最小: -0.235108
2025-08-01 11:00:59,384 - INFO - 客户端 7 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:00:59,384 - INFO - ✅ 客户端 7 的更新已加入成功缓冲池，当前池大小: 10
2025-08-01 11:00:59,384 - INFO - 📊 当前缓冲池中的客户端: [4, 1, 6, 8, 10, 3, 5, 9, 2, 7]
2025-08-01 11:00:59,385 - INFO - 🔍 客户端 7 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:00:59,385 - INFO - 成功处理客户端 7 的更新
2025-08-01 11:00:59,385 - INFO - [Client 7] ✅ 成功上传训练结果到服务器
2025-08-01 11:00:59,385 - INFO - 客户端 7 训练完成
2025-08-01 11:01:06,953 - INFO - [Trainer None] 测试结果 - 准确率: 0.1000 (1000/10000), 损失: 2425.4786
2025-08-01 11:01:06,954 - INFO - 全局模型评估完成，准确率: 0.1000, 耗时: 15.05秒
2025-08-01 11:01:06,954 - WARNING - log_accuracy: 训练器的模型引用不一致，更新为当前全局模型
2025-08-01 11:01:06,954 - INFO - log_accuracy: 已同步训练器的模型引用
2025-08-01 11:01:06,954 - INFO - log_accuracy: 为训练器设置client_id=0
2025-08-01 11:01:06,954 - INFO - log_accuracy: 模型输入通道数: 3
2025-08-01 11:01:06,954 - ERROR - 准确率CSV文件未初始化，跳过记录
2025-08-01 11:01:06,956 - INFO - 创建准确率记录文件: ./results/scafl/mnist/accuracy_20250801_110106.csv
2025-08-01 11:01:06,957 - INFO - 轮次 1 准确率记录 - 当前: 10.00%, 平均(最近10轮): 10.00%, 最佳: 10.00%
2025-08-01 11:01:06,957 - INFO - 轮次: 1, 准确率: 0.1, 平均陈旧度: 0.0
2025-08-01 11:01:06,957 - INFO - 客户端 4 陈旧度: 0
2025-08-01 11:01:06,961 - INFO - [全局模型摘要] 轮次: 1 | 均值: 0.001334, 最大: 20.000000, 最小: -0.372431
2025-08-01 11:01:06,961 - INFO - [DEBUG] 结果记录时 - wall_time=1754017231.030131, initial_wall_time=1754017230.0914042, elapsed_time=0.9387269020080566
2025-08-01 11:01:06,961 - INFO - 参与聚合的客户端平均准确率: 0.5267 (基于 1 个客户端)
2025-08-01 11:01:06,965 - INFO - 所有训练统计信息已记录到统一结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_110030.csv
2025-08-01 11:01:06,965 - INFO - 记录数据: {'round': 0, 'elapsed_time': 0.9387269020080566, 'accuracy': 0.5266666666666666, 'global_accuracy': 0.1, 'global_accuracy_std': 0.0, 'avg_staleness': 0.0, 'max_staleness': 0, 'min_staleness': 0, 'virtual_time': 0.0, 'aggregated_clients_count': 1}
2025-08-01 11:01:06,965 - DEBUG - 聚合后客户端 1 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:01:06,965 - DEBUG - 聚合后客户端 2 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:01:06,965 - DEBUG - 聚合后客户端 3 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:01:06,965 - DEBUG - 聚合后客户端 4 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:01:06,965 - DEBUG - 聚合后客户端 5 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:01:06,965 - DEBUG - 聚合后客户端 6 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:01:06,965 - DEBUG - 聚合后客户端 7 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:01:06,965 - DEBUG - 聚合后客户端 8 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:01:06,965 - DEBUG - 聚合后客户端 9 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:01:06,965 - DEBUG - 聚合后客户端 10 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:01:06,965 - INFO - 聚合完成，缓冲池中还剩 9 个更新，来自 9 个不同客户端
2025-08-01 11:01:06,965 - INFO - 🔓 聚合过程结束，轮次: 1
2025-08-01 11:01:06,966 - INFO - 🎉 第 0 轮聚合完成，当前轮次: 1
2025-08-01 11:01:06,966 - INFO - 🔄 聚合完成，重新启动客户端训练（第 1 轮）
2025-08-01 11:01:06,967 - INFO - 缓冲池已清空，准备新一轮训练
2025-08-01 11:01:06,967 - INFO - 重新启动客户端 1 的训练任务
2025-08-01 11:01:06,967 - INFO - 重新启动客户端 2 的训练任务
2025-08-01 11:01:06,968 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:01:06,968 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:01:06,968 - INFO - 重新启动客户端 3 的训练任务
2025-08-01 11:01:06,968 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 11:01:06,968 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 11:01:06,969 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:01:06,969 - INFO - 重新启动客户端 4 的训练任务
2025-08-01 11:01:06,969 - INFO - [Client 3] 开始验证训练集
2025-08-01 11:01:06,969 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 11:01:06,969 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:01:06,969 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 11:01:06,969 - INFO - 重新启动客户端 5 的训练任务
2025-08-01 11:01:06,970 - INFO - [Client 4] 开始验证训练集
2025-08-01 11:01:06,970 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 11:01:06,970 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:01:06,970 - INFO - 重新启动客户端 6 的训练任务
2025-08-01 11:01:06,970 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 11:01:06,971 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 11:01:06,971 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:01:06,971 - INFO - 重新启动客户端 7 的训练任务
2025-08-01 11:01:06,971 - INFO - [Client 5] 开始验证训练集
2025-08-01 11:01:06,971 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 11:01:06,972 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 11:01:06,972 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:01:06,972 - INFO - 重新启动客户端 8 的训练任务
2025-08-01 11:01:06,972 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:01:06,972 - INFO - [Client 6] 开始验证训练集
2025-08-01 11:01:06,972 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:01:06,972 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 11:01:06,973 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 11:01:06,973 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 11:01:06,973 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:01:06,973 - INFO - 重新启动客户端 9 的训练任务
2025-08-01 11:01:06,973 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 11:01:06,974 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 11:01:06,974 - INFO - [Client 7] 开始验证训练集
2025-08-01 11:01:06,974 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 11:01:06,974 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:01:06,974 - INFO - [Trainer 3] 开始训练
2025-08-01 11:01:06,975 - INFO - [Trainer 4] 开始训练
2025-08-01 11:01:06,975 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 11:01:06,975 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:01:06,975 - INFO - 重新启动客户端 10 的训练任务
2025-08-01 11:01:06,975 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 11:01:06,975 - INFO - [Client 8] 开始验证训练集
2025-08-01 11:01:06,975 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 11:01:06,975 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:01:06,975 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 11:01:06,976 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 11:01:06,976 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 11:01:06,976 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 11:01:06,976 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:01:06,976 - INFO - [Client 9] 开始验证训练集
2025-08-01 11:01:06,976 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:01:06,977 - INFO - ✅ 已重新启动 10 个客户端的训练任务
2025-08-01 11:01:06,977 - INFO - [Trainer 5] 开始训练
2025-08-01 11:01:06,977 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 11:01:06,978 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 11:01:06,978 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 11:01:06,978 - INFO - [Client 9] 开始验证训练集
2025-08-01 11:01:06,978 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 11:01:06,979 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:01:06,979 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 11:01:06,979 - INFO - [Trainer 6] 开始训练
2025-08-01 11:01:06,979 - INFO - [Client 10] 开始验证训练集
2025-08-01 11:01:06,980 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 11:01:06,980 - INFO - [Trainer 7] 开始训练
2025-08-01 11:01:06,980 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 11:01:06,981 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 11:01:06,981 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:01:06,981 - INFO - [Client 10] 开始验证训练集
2025-08-01 11:01:06,982 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:01:06,982 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 11:01:06,982 - INFO - [Trainer 8] 开始训练
2025-08-01 11:01:06,983 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 11:01:06,983 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 11:01:06,984 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 11:01:06,984 - INFO - [Trainer 9] 开始训练
2025-08-01 11:01:06,984 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:01:06,985 - INFO - [Trainer 9] 开始训练
2025-08-01 11:01:06,985 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:01:06,985 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 11:01:06,986 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 11:01:06,986 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 11:01:06,986 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 11:01:06,986 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:01:06,987 - INFO - [Trainer 10] 开始训练
2025-08-01 11:01:06,988 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 11:01:06,988 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 11:01:06,988 - INFO - [Trainer 10] 开始训练
2025-08-01 11:01:06,989 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 11:01:06,990 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:01:06,990 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:01:06,991 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 11:01:06,992 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:01:06,992 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:01:06,994 - INFO - [Trainer 4] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:01:06,994 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 11:01:06,995 - INFO - [Trainer 3] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:01:06,995 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 11:01:06,995 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 11:01:06,996 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 11:01:06,997 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:01:06,998 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:01:06,998 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:01:06,999 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 11:01:07,000 - INFO - [Trainer 5] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:01:07,000 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 11:01:07,000 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 11:01:07,000 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:01:07,001 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:01:07,001 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:01:07,002 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:01:07,002 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:01:07,002 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:01:07,003 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:01:07,003 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 11:01:07,003 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:01:07,004 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:01:07,004 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 11:01:07,005 - INFO - [Trainer 8] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:01:07,005 - INFO - [Trainer 9] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:01:07,006 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:01:07,006 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:01:07,006 - INFO - [Trainer 7] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:01:07,006 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:01:07,007 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 11:01:07,007 - INFO - [Trainer 6] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:01:07,007 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 11:01:07,008 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:01:07,008 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 11:01:07,008 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:01:07,009 - INFO - [Trainer 9] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:01:07,009 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 11:01:07,011 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 11:01:07,012 - INFO - [Trainer 10] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:01:07,012 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 11:01:07,013 - INFO - [Trainer 10] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:01:07,013 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 11:01:07,078 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3928, y=[2, 2, 6, 2, 2]
2025-08-01 11:01:07,079 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 11:01:07,081 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0727, y=[0, 1, 0, 1, 1]
2025-08-01 11:01:07,082 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 11:01:07,085 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4197, y=[7, 7, 7, 7, 7]
2025-08-01 11:01:07,085 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3424, y=[6, 6, 6, 6, 7]
2025-08-01 11:01:07,086 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 11:01:07,086 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 11:01:07,088 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2167, y=[2, 2, 2, 2, 2]
2025-08-01 11:01:07,088 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 11:01:07,091 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2583, y=[8, 8, 7, 8, 8]
2025-08-01 11:01:07,092 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 11:01:07,094 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0898, y=[7, 8, 8, 7, 7]
2025-08-01 11:01:07,094 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 11:01:07,096 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2669, y=[6, 6, 6, 6, 8]
2025-08-01 11:01:07,096 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 11:01:07,097 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2209, y=[8, 8, 8, 6, 8]
2025-08-01 11:01:07,097 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 11:01:07,097 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2536, y=[1, 0, 0, 1, 0]
2025-08-01 11:01:07,097 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 11:01:07,150 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 11:01:07,154 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 11:01:07,155 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 11:01:07,155 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 11:01:07,159 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 11:01:07,160 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4001
2025-08-01 11:01:07,161 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:01:07,161 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2987
2025-08-01 11:01:07,161 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4937
2025-08-01 11:01:07,161 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3397
2025-08-01 11:01:07,161 - INFO - [Trainer 5] 标签样本: [7, 7, 3, 7, 7]
2025-08-01 11:01:07,161 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:01:07,162 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:01:07,162 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:01:07,162 - INFO - [Trainer 4] 标签样本: [0, 0, 1, 1, 1]
2025-08-01 11:01:07,162 - INFO - [Trainer 9] 标签样本: [8, 6, 6, 6, 6]
2025-08-01 11:01:07,163 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:01:07,167 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 11:01:07,167 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 11:01:07,168 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 11:01:07,170 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 11:01:07,170 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 11:01:07,178 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1891
2025-08-01 11:01:07,178 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5014
2025-08-01 11:01:07,178 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3312
2025-08-01 11:01:07,178 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3788
2025-08-01 11:01:07,178 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3605
2025-08-01 11:01:07,179 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:01:07,179 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1342
2025-08-01 11:01:07,179 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:01:07,180 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:01:07,180 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:01:07,182 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:01:07,183 - INFO - [Trainer 8] 标签样本: [1, 6, 6, 0, 1]
2025-08-01 11:01:07,183 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:01:07,184 - INFO - [Trainer 9] 标签样本: [8, 6, 6, 6, 8]
2025-08-01 11:01:07,185 - INFO - [Trainer 10] 标签样本: [6, 8, 6, 8, 6]
2025-08-01 11:01:07,185 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:01:07,187 - INFO - [Trainer 10] 标签样本: [8, 8, 6, 7, 8]
2025-08-01 11:01:07,190 - INFO - [Trainer 6] 标签样本: [8, 0, 0, 0, 0]
