"""
ReFedScaFL客户端类，扩展自基础客户端
实现异步训练和通信功能
"""

import torch
import logging
from plato.clients import simple
from refedscafl_algorithm import Algorithm
from refedscafl_trainer import Trainer
from plato.datasources import base
from plato.datasources import mnist
from plato.models import registry as models
import asyncio
from plato.config import Config
from plato.models import lenet5
import numpy as np
from torch.utils.data import Subset
# from refedscafl_utils import custom_partition
import time
from types import SimpleNamespace
import copy
from torchvision import datasets, transforms
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，避免需要图形界面
import matplotlib.pyplot as plt
import os
import sys

# 移除自定义数据分区函数，使用标准Plato采样器
# def custom_partition(...): 已删除，确保使用标准数据分配


def visualize_data_distribution(client_id, dataset, save_dir='data_distribution'):
    """
    可视化客户端数据分布
    
    参数:
        client_id: 客户端ID
        dataset: 客户端数据集
        save_dir: 保存目录
    """
    try:
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 提取标签
        if hasattr(dataset, 'targets'):
            labels = dataset.targets
        else:
            # 如果是Subset类型，需要从原始数据集提取标签
            if isinstance(dataset, Subset):
                if hasattr(dataset.dataset, 'targets'):
                    all_labels = dataset.dataset.targets
                    indices = dataset.indices
                    labels = [all_labels[i] for i in indices]
                else:
                    # 尝试从数据集中提取标签
                    labels = []
                    for i in range(len(dataset)):
                        _, label = dataset[i]
                        labels.append(label)
            else:
                # 尝试从数据集中提取标签
                labels = []
                for i in range(len(dataset)):
                    _, label = dataset[i]
                    labels.append(label)
        
        # 转换为numpy数组
        if not isinstance(labels, np.ndarray):
            labels = np.array(labels)
        
        # 计算每个类别的样本数
        unique_labels = np.unique(labels)
        class_counts = [np.sum(labels == label) for label in unique_labels]
        
        # 绘制类别分布柱状图
        plt.figure(figsize=(10, 6))
        plt.bar(unique_labels, class_counts)
        plt.xlabel('类别')
        plt.ylabel('样本数')
        plt.title(f'客户端 {client_id} 的类别分布 (总样本数: {len(dataset)})')
        plt.xticks(unique_labels)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        
        # 保存图像
        save_path = os.path.join(save_dir, f'client_{client_id}_distribution.png')
        plt.savefig(save_path)
        plt.close()
        
        print(f"[INFO] 客户端 {client_id} 的数据分布可视化已保存到 {save_path}")
        
        # 保存数据分布的文本描述
        text_path = os.path.join(save_dir, f'client_{client_id}_distribution.txt')
        with open(text_path, 'w') as f:
            f.write(f"客户端 {client_id} 数据分布\n")
            f.write(f"总样本数: {len(dataset)}\n")
            f.write("类别分布:\n")
            for label, count in zip(unique_labels, class_counts):
                f.write(f"  类别 {label}: {count} 样本 ({count/len(dataset)*100:.2f}%)\n")
        
        return True
    except Exception as e:
        print(f"[ERROR] 可视化客户端 {client_id} 的数据分布时出错: {str(e)}")
        return False

class Client(simple.Client):
    def __init__(self, model=None, algorithm=None, trainer=None, **kwargs):
        """
        初始化ReFedScaFL客户端

        重要：完全遵循标准Plato客户端的初始化流程
        不接受client_id参数，让Plato框架自动处理
        """
        # 确保模型已初始化
        if model is None:
            # 从配置中获取模型参数
            config = Config()
            num_classes = getattr(config.parameters.model, 'num_classes', 10)
            in_channels = getattr(config.parameters.model, 'in_channels', 1)

            model = lenet5.Model(num_classes=num_classes, in_channels=in_channels)
            logging.info(f"[ReFedScaFL Client] 创建新模型: num_classes={num_classes}, in_channels={in_channels}")

        # 调用父类初始化，让Plato框架处理所有标准流程
        # 不传递自定义函数，让Plato使用标准的注册表机制
        super().__init__(
            model=model,
            datasource=None,  # 让Plato框架自动创建
            algorithm=algorithm,  # 让Plato框架自动创建
            trainer=trainer,      # 让Plato框架自动创建
            **kwargs
        )

        # ReFedScaFL特有的属性
        self.last_round = 0
        self.tau_k = 0  # 陈旧度
        self.D_k = 0    # 估计的训练+通信时间
        self.Q_k = 1.0  # 质量因子
        self.need_distill = False  # 是否需要蒸馏补偿
        self.model_weights = None  # 模型权重缓存
        self.last_model_receive_time = time.time()  # 上次接收模型的时间

        logging.info(f"[Client {self.client_id}] ReFedScaFL客户端初始化完成")
        
    def configure(self):
        """
        配置客户端

        重要：完全遵循标准Plato流程，不进行任何自定义干预
        """
        logging.info(f"[Client {self.client_id}] 🔧 开始配置客户端")
        logging.info(f"[Client {self.client_id}] 配置前状态: algorithm={self.algorithm}, trainer={self.trainer}")

        # 调用父类configure，这会：
        # 1. 创建数据源（通过_load_data）
        # 2. 配置训练器、算法、采样器
        # 3. 分配训练集和测试集（通过_allocate_data）
        super().configure()

        logging.info(f"[Client {self.client_id}] 父类配置后状态: algorithm={self.algorithm}, trainer={self.trainer}")

        # 验证数据源和训练集（应该已经由父类创建）
        if self.datasource is not None:
            logging.info(f"[Client {self.client_id}] ✅ 数据源已创建")

            if hasattr(self, 'trainset') and self.trainset is not None:
                logging.info(f"[Client {self.client_id}] ✅ 训练集大小: {len(self.trainset)}")

                # 验证数据集
                if self.validate_data(self.trainset):
                    logging.info(f"[Client {self.client_id}] ✅ 训练数据验证通过")
                    # 分析数据分布（用于调试）
                    self._analyze_data_distribution()
                else:
                    raise ValueError(f"[Client {self.client_id}] ❌ 训练数据无效")
            else:
                raise ValueError(f"[Client {self.client_id}] ❌ 训练集未分配")
        else:
            raise ValueError(f"[Client {self.client_id}] ❌ 数据源未创建")

        # 验证采样器
        if hasattr(self, 'sampler') and self.sampler is not None:
            logging.info(f"[Client {self.client_id}] ✅ 采样器已创建，样本数: {self.sampler.num_samples()}")
        else:
            logging.warning(f"[Client {self.client_id}] ⚠️ 采样器未创建")

        # 手动创建训练器和算法（如果Plato框架没有创建）
        if self.trainer is None:
            self.trainer = Trainer(model=self.model, client_id=self.client_id)
            logging.info(f"[Client {self.client_id}] ✅ 手动创建训练器")
        else:
            # 确保训练器有正确的client_id
            if hasattr(self.trainer, 'client_id'):
                self.trainer.client_id = self.client_id
                logging.info(f"[Client {self.client_id}] ✅ 训练器client_id已更新")

        if self.algorithm is None:
            self.algorithm = Algorithm(trainer=self.trainer)
            logging.info(f"[Client {self.client_id}] ✅ 手动创建算法")

        # 验证关键组件
        if self.algorithm is None:
            raise ValueError(f"[Client {self.client_id}] ❌ 算法未创建")
        if self.trainer is None:
            raise ValueError(f"[Client {self.client_id}] ❌ 训练器未创建")

        logging.info(f"[Client {self.client_id}] 🎉 客户端配置完成")

    def _is_selected_for_training(self):
        """检查客户端是否被选中参与本轮训练"""
        if not hasattr(self.server, 'client_start_time'):
            return False

        # 检查是否在最近的训练时间记录中
        current_time = time.time()
        if self.client_id in self.server.client_start_time:
            start_time = self.server.client_start_time[self.client_id]
            # 如果开始时间在最近10秒内，认为被选中
            if current_time - start_time < 10:
                return True

        return False

    def _load_data(self):
        """加载和分配数据"""
        try:
            # 使用Plato框架的标准数据加载流程
            from plato.datasources import registry as datasources_registry
            from plato.samplers import registry as samplers_registry
            from plato.config import Config

            config = Config()

            # 创建数据源
            if not hasattr(self, 'datasource') or self.datasource is None:
                datasource_name = config.data.datasource
                # datasources_registry.get() 已经返回实例，不需要再次调用
                self.datasource = datasources_registry.get(client_id=self.client_id)
                logging.info(f"[Client {self.client_id}] 创建数据源: {datasource_name}")

            # 创建采样器
            if not hasattr(self, 'sampler') or self.sampler is None:
                sampler_name = config.data.sampler
                self.sampler = samplers_registry.get(sampler_name)(
                    self.datasource, self.client_id, testing=False
                )
                logging.info(f"[Client {self.client_id}] 创建采样器: {sampler_name}")

            # 获取客户端特定的数据分区
            if hasattr(self.sampler, 'get'):
                # 尝试不同的调用方式
                try:
                    self.trainset = self.sampler.get()
                except TypeError:
                    # 如果get()需要参数，尝试传入client_id
                    try:
                        self.trainset = self.sampler.get(self.client_id)
                    except:
                        # 最后尝试直接使用采样器作为数据集
                        self.trainset = self.sampler
            else:
                # 如果采样器没有get方法，使用全局训练集
                self.trainset = self.datasource.get_train_set()

            logging.info(f"[Client {self.client_id}] 数据加载完成，训练集大小: {len(self.trainset) if self.trainset else 0}")

        except Exception as e:
            logging.error(f"[Client {self.client_id}] 数据加载失败: {str(e)}")
            logging.error(f"[Client {self.client_id}] 详细错误信息: {repr(e)}")

            # 尝试使用简单的MNIST数据作为备用
            try:
                import torchvision
                import torchvision.transforms as transforms
                from torch.utils.data import Subset

                logging.warning(f"[Client {self.client_id}] 尝试直接加载MNIST数据作为备用")

                transform = transforms.Compose([
                    transforms.ToTensor(),
                    transforms.Normalize((0.1307,), (0.3081,))
                ])

                full_trainset = torchvision.datasets.MNIST(
                    root='./data', train=True, download=True, transform=transform
                )

                # 为客户端分配一个简单的数据分区
                partition_size = 300
                start_idx = (self.client_id - 1) * partition_size
                end_idx = start_idx + partition_size

                if end_idx <= len(full_trainset):
                    indices = list(range(start_idx, end_idx))
                    self.trainset = Subset(full_trainset, indices)
                    logging.info(f"[Client {self.client_id}] 使用直接MNIST数据，大小: {len(self.trainset)}")
                else:
                    raise Exception("数据分区超出范围")

            except Exception as e2:
                logging.error(f"[Client {self.client_id}] 备用MNIST加载也失败: {str(e2)}")

                # 最后的备用方案：创建虚拟数据（但要明确标记）
                import torch
                from torch.utils.data import TensorDataset

                # 创建更接近MNIST的虚拟数据
                dummy_data = torch.randn(100, 1, 28, 28) * 0.3  # 减小方差
                dummy_labels = torch.randint(0, 10, (100,))
                self.trainset = TensorDataset(dummy_data, dummy_labels)
                self.sampler = None

                logging.error(f"[Client {self.client_id}] ❌ 被迫使用虚拟随机数据！这会导致训练失败！")
                logging.error(f"[Client {self.client_id}] 虚拟数据集大小: {len(self.trainset)}")

    def validate_data(self, trainset):
        """验证训练数据

        Args:
            trainset: 训练数据集

        Returns:
            bool: 数据是否有效
        """
        if trainset is None or len(trainset) == 0:
            return False
        try:
            sample = trainset[0]
            if not isinstance(sample, (tuple, list)) or len(sample) != 2:
                return False
            return True
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 数据验证失败: {str(e)}")
            return False

    def _analyze_data_distribution(self):
        """分析客户端数据分布（用于调试）"""
        try:
            if not hasattr(self, 'trainset') or self.trainset is None:
                return

            # 提取标签
            targets = []
            sample_size = min(len(self.trainset), 100)  # 只检查前100个样本

            for i in range(sample_size):
                _, label = self.trainset[i]
                targets.append(label)

            targets = np.array(targets)
            unique, counts = np.unique(targets, return_counts=True)
            class_distribution = dict(zip(unique, counts))

            # 计算统计信息
            total_samples = len(targets)
            max_class_count = max(counts) if len(counts) > 0 else 0
            max_class_ratio = max_class_count / total_samples if total_samples > 0 else 0
            num_classes_present = len(unique)

            logging.info(f"[Client {self.client_id}] 数据分布分析:")
            logging.info(f"  类别分布: {class_distribution}")
            logging.info(f"  最大类别占比: {max_class_ratio:.2%}")
            logging.info(f"  包含类别数: {num_classes_present}")

            # 检查数据分配是否正常
            if num_classes_present > 1:
                logging.info(f"[Client {self.client_id}] ✅ 数据分配正常 - 包含多个类别")
            else:
                logging.warning(f"[Client {self.client_id}] ⚠️ 数据分配可能异常 - 只有一个类别")

        except Exception as e:
            logging.error(f"[Client {self.client_id}] 数据分布分析失败: {str(e)}")

    def validate_model(self):
        """验证模型
        
        Returns:
            bool: 模型是否有效
        """
        if self.model is None:
            return False
        try:
            for param in self.model.parameters():
                if torch.isnan(param).any() or torch.isinf(param).any():
                    return False
            return True
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 模型验证失败: {str(e)}")
            return False

    async def receive_model(self):
        """持续监听并接收服务器发送的模型"""
        logging.info(f"[Client {self.client_id}] 🚀 启动客户端训练循环")

        while True:
            try:
                # 等待一段时间再检查是否有新的训练任务
                await asyncio.sleep(1)

                # 检查是否已经在成功缓冲池或蒸馏缓冲池中
                if (hasattr(self.server, 'success_buffered_clients') and
                    self.client_id in self.server.success_buffered_clients):
                    continue

                if (hasattr(self.server, 'distill_buffered_clients') and
                    self.client_id in self.server.distill_buffered_clients):
                    continue

                # 检查是否被选中参与本轮训练
                if not self._is_selected_for_training():
                    continue

                logging.info(f"[Client {self.client_id}] 📥 开始接收模型并训练")

                # 记录开始训练的时间
                self.training_start_time = time.time()

                # 新增健壮性检查
                if self.model is None:
                    # 从配置中获取模型参数
                    config = Config()
                    num_classes = getattr(config.parameters.model, 'num_classes', 10)
                    in_channels = getattr(config.parameters.model, 'in_channels', 1)

                    self.model = lenet5.Model(num_classes=num_classes, in_channels=in_channels)
                    logging.warning(f"[Client {self.client_id}] self.model为None，已自动新建模型: num_classes={num_classes}, in_channels={in_channels}")

                # 从服务器获取模型
                response = await self.server.send_model_to_client(self.client_id)

                if response is None:
                    logging.error(f"[Client {self.client_id}] 接收模型失败：服务器返回为空")
                    continue

                # 从响应中获取模型权重和通信阈值
                if hasattr(response, 'weights'):
                    self.model_weights = response.weights

                    # 加载模型权重
                    try:
                        # 检查算法状态，如果为None则进行懒加载初始化
                        logging.info(f"[Client {self.client_id}] 🔍 接收模型时算法状态: algorithm={self.algorithm}")
                        logging.info(f"[Client {self.client_id}] 🔍 接收模型时训练器状态: trainer={self.trainer}")

                        if self.algorithm is None or self.trainer is None:
                            logging.warning(f"[Client {self.client_id}] ⚠️ 算法或训练器为None，进行懒加载初始化")

                            # 手动创建训练器和算法
                            if self.trainer is None:
                                self.trainer = Trainer(model=self.model, client_id=self.client_id)
                                logging.info(f"[Client {self.client_id}] ✅ 懒加载创建训练器")

                            if self.algorithm is None:
                                self.algorithm = Algorithm(trainer=self.trainer)
                                logging.info(f"[Client {self.client_id}] ✅ 懒加载创建算法")

                            # 懒加载数据集和采样器
                            if not hasattr(self, 'trainset') or self.trainset is None:
                                self._load_data()
                                logging.info(f"[Client {self.client_id}] ✅ 懒加载创建数据集")

                            # 验证创建结果
                            if self.algorithm is None:
                                logging.error(f"[Client {self.client_id}] ❌ 懒加载后算法仍为None")
                                continue

                        self.algorithm.load_weights(self.model_weights)
                        logging.info(f"[Client {self.client_id}] 成功接收并加载全局模型")

                        # 更新模型接收时间
                        self.last_model_receive_time = time.time()
                    except Exception as e:
                        logging.error(f"[Client {self.client_id}] 加载模型权重时出错: {str(e)}")
                        continue

                    # 获取通信阈值
                    if hasattr(response, 'comm_threshold'):
                        self.comm_threshold = response.comm_threshold
                        logging.info(f"[Client {self.client_id}] 通信阈值: {self.comm_threshold:.6f}秒")

                    # 检查是否收到蒸馏补偿标志
                    self.need_distill = getattr(response, 'need_distill', False)
                    if self.need_distill:
                        logging.info(f"[Client {self.client_id}] 收到蒸馏补偿标志，将进行蒸馏补偿")
                        # 进行蒸馏补偿
                        pseudo_gradient = self.distill_pseudo_gradient(self.server.global_model_cache)
                        if pseudo_gradient is not None:
                            await self.server.process_client_update(self.client_id, None, pseudo_gradient)
                            logging.info(f"[Client {self.client_id}] ✅ 蒸馏补偿完成")
                        continue
                    else:
                        logging.info(f"[Client {self.client_id}] 开始正常训练")

                    # 进行正常训练
                    report, weights = await self.train()
                    if report is not None and weights is not None:
                        # 上传训练结果
                        await self.server.process_client_update(self.client_id, report, weights)
                        logging.info(f"[Client {self.client_id}] ✅ 训练完成并上传结果")
                    else:
                        logging.warning(f"[Client {self.client_id}] 训练失败")


                else:
                    logging.error(f"[Client {self.client_id}] 接收模型失败：响应中没有权重数据")
                    continue

            except Exception as e:
                logging.error(f"[Client {self.client_id}] 训练循环错误: {str(e)}")
                await asyncio.sleep(5)  # 出错后等待5秒再重试
            
    async def run(self):
        """
        客户端主循环
        """
        while True:
            # 1. 等待服务器下发模型
            await asyncio.sleep(0.1)
            
            # 2. 接收服务器模型
            receive_success = await self.receive_model()
            if not receive_success:
                logging.warning(f"[Client {self.client_id}] 接收模型失败，跳过本轮训练")
                continue
                
            # 2.5. 处理服务器响应，确定是否需要蒸馏补偿
            try:
                # 模拟服务器响应，包含通信阈值
                server_response = SimpleNamespace()
                server_response.comm_threshold = 0.5  # 默认阈值
                
                # 处理服务器响应，设置蒸馏补偿标志
                await self.process_server_response(server_response)
            except Exception as e:
                logging.error(f"[Client {self.client_id}] 处理服务器响应时出错: {str(e)}")
                continue

            # 3. 判断是否需要蒸馏补偿
            if self.need_distill:
                if self.model is None:
                    logging.error(f"[Client {self.client_id}] 伪梯度生成前self.model为None，跳过本轮")
                    continue
                logging.info(f"[Client {self.client_id}] 🔥 需要蒸馏补偿，生成并上传伪梯度")
                pseudo_gradient = self.distill_pseudo_gradient(self.server.global_model_cache)
                if pseudo_gradient is not None:
                    # 主动上传伪梯度到服务器
                    await self.server.receive_distill_update(self.client_id, pseudo_gradient)
                    logging.info(f"[Client {self.client_id}] ✅ 蒸馏补偿完成，伪梯度已上传")
                else:
                    logging.warning(f"[Client {self.client_id}] ❌ 伪梯度生成失败")
                continue  # 蒸馏补偿后本轮不再进行正常训练

            # 4. 正常训练
            report, weights = await self.train()
            if report is None or weights is None:
                logging.warning(f"[Client {self.client_id}] 训练失败，跳过本轮")
                continue
            
            # 5. 上传训练结果
            await self.server.process_client_update(self.client_id, report, weights)
            
            await asyncio.sleep(0.1)

    async def _receive_model(self):
        """接收服务器模型
        
        Returns:
            bool: 是否成功接收模型
        """
        try:
            if self.server is None:
                logging.error(f"[Client {self.client_id}] 服务器引用未设置")
                return False
                
            # 使用安全的配置获取方式
            config = Config()
            timeout = getattr(config, 'client_timeout', 60)  # 默认60秒超时
            
            # 使用 wait_for 替代 timeout 装饰器
            await asyncio.wait_for(
                self._receive_model_impl(),
                timeout=timeout
            )
            return True
            
        except asyncio.TimeoutError:
            logging.error(f"[Client {self.client_id}] 接收模型超时")
            return False
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 接收模型错误: {str(e)}")
            return False
            
    async def _receive_model_impl(self):
        """实际的模型接收逻辑"""
        if self.server is None:
            raise RuntimeError("服务器引用未设置")
            
        # 从服务器接收模型
        model_weights = await self.server.send_model_to_client(self.client_id)
        
        # 加载模型权重
        if model_weights is not None and len(model_weights) > 0:
            try:
                self.algorithm.load_weights(model_weights)
                logging.info(f"[Client {self.client_id}] 成功接收并加载模型")
            except Exception as e:
                logging.error(f"[Client {self.client_id}] 加载模型权重时出错: {str(e)}")
                raise
        else:
            logging.warning(f"[Client {self.client_id}] 接收到的模型权重为空")
            
    async def train(self):
        """执行本地训练"""
        try:
            current_round = self.last_round + 1
            self.last_round = current_round
            
            # 执行训练（使用标准Plato方式）
            report = await self.trainer.train(
                trainset=self.trainset,
                sampler=self.sampler
            )
            
            if report is None:
                logging.error(f"[Client {self.client_id}] 训练失败")
                return None, None
                
            # 获取模型权重
            weights = self.algorithm.extract_weights()
            
            # 获取训练报告
            training_report = self.trainer.get_report()
            
            # 创建报告对象，确保样本数为实际训练集大小
            report = SimpleNamespace(
                upload_start_time=time.time(),
                num_samples=len(self.trainset),  # 用实际训练集大小
                accuracy=training_report.get('train_accuracy', 0.0),
                training_time=training_report.get('training_time', 0.0),
                comm_success=True,  # 添加通信成功标志
                is_distilled=False  # 标记为正常训练
            )
            
            logging.info(f"[Client {self.client_id}] 完成第{current_round}轮训练 (准确率: {report.accuracy:.2%})")
            return report, weights
            
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 训练过程出错: {str(e)}")
            return None, None

    def set_server_reference(self, server):
        """设置服务器引用
        
        Args:
            server: 服务器实例
        """
        self.server = server
        logging.info(f"客户端{self.client_id}已设置服务器引用")

    def update_client_state(self):
        """
        更新客户端状态
        - 更新陈旧度
        - 更新估计的训练+通信时间
        - 更新质量因子
        """
        if hasattr(self.server, 'current_round'):
            self.tau_k = max(0, self.server.current_round - self.last_round)  # 确保tau_k不为负数
            self.last_round = self.server.current_round  # 更新last_round
        else:
            self.tau_k = max(0, self.tau_k + 1)  # 确保tau_k不为负数

        # 更新估计的训练+通信时间
        if hasattr(self.server, 'client_estimated_duration'):
            self.D_k = self.server.client_estimated_duration.get(self.client_id, 0)

        # 更新质量因子（可以根据需要调整）
        # 确保分母不为零
        self.Q_k = 1.0 / (1.0 + max(0, self.tau_k))
        
    def distill_pseudo_gradient(self, global_model):
        """
        优化的知识蒸馏生成伪梯度

        优化点：
        1. 使用更高效的数据采样
        2. 添加多种蒸馏损失
        3. 支持特征级蒸馏
        4. 减少内存占用

        :param global_model: 全局模型权重作为教师模型
        :return: 蒸馏后的伪梯度
        """
        # 健壮性检查
        if self.model is None:
            logging.error(f"[Client {self.client_id}] 蒸馏补偿失败：self.model为None")
            return None
        if global_model is None:
            logging.error(f"[Client {self.client_id}] 蒸馏补偿失败：global_model为None")
            return None
        try:
            self.model.eval()

            # 使用标准Plato方式访问训练集
            if len(self.trainset) == 0:
                logging.error(f"[Client {self.client_id}] 训练集为空，无法进行蒸馏")
                return None

            # 优化的数据采样 - 使用分层采样确保类别平衡
            batch_size = min(64, len(self.trainset))  # 增加批次大小提高稳定性

            # 分层采样：确保每个类别都有代表
            class_indices = {}
            for idx, (_, label) in enumerate(self.trainset):
                if label not in class_indices:
                    class_indices[label] = []
                class_indices[label].append(idx)

            # 从每个类别中采样
            selected_indices = []
            samples_per_class = max(1, batch_size // len(class_indices))
            for class_label, indices in class_indices.items():
                if len(indices) > 0:
                    sampled = torch.randperm(len(indices))[:samples_per_class].tolist()
                    selected_indices.extend([indices[i] for i in sampled])

            # 如果样本不够，随机补充
            if len(selected_indices) < batch_size:
                remaining = batch_size - len(selected_indices)
                all_indices = set(range(len(self.trainset)))
                available = list(all_indices - set(selected_indices))
                if available:
                    additional = torch.randperm(len(available))[:remaining].tolist()
                    selected_indices.extend([available[i] for i in additional])

            # 构建批次数据
            batch_data = [self.trainset[i] for i in selected_indices[:batch_size]]
            inputs = torch.stack([item[0] for item in batch_data])
            labels = torch.tensor([item[1] for item in batch_data])

            # 设备处理
            device = next(self.model.parameters()).device
            inputs = inputs.to(device)
            labels = labels.to(device)

            # 精度优化的蒸馏参数
            config = Config()
            T = getattr(config.algorithm, 'distill_temperature', 4.0)  # 更高温度增强软化效果
            alpha = getattr(config.algorithm, 'distill_alpha', 0.8)  # 增加KL散度权重
            beta = getattr(config.algorithm, 'distill_beta', 0.2)   # 减少交叉熵权重

            self.model.zero_grad()
            student_logits = self.model(inputs)

            # 创建教师模型（优化内存使用）
            teacher_model = copy.deepcopy(self.model)

            # 权重形状验证
            if 'conv1.weight' in global_model:
                global_conv1_shape = global_model['conv1.weight'].shape
                student_conv1_shape = self.model.state_dict()['conv1.weight'].shape

                logging.info(f"[Client {self.client_id}] 蒸馏补偿权重验证:")
                logging.info(f"  学生模型 conv1.weight 形状: {student_conv1_shape}")
                logging.info(f"  全局模型 conv1.weight 形状: {global_conv1_shape}")

                if global_conv1_shape != student_conv1_shape:
                    logging.error(f"[Client {self.client_id}] 🚨 权重形状不匹配！")
                    return None
                else:
                    logging.debug(f"[Client {self.client_id}] ✅ 权重形状匹配，可以进行蒸馏补偿")

            # 加载教师模型权重
            teacher_model.load_state_dict(global_model)
            teacher_model.eval()

            # 获取教师模型输出
            with torch.no_grad():
                teacher_logits = teacher_model(inputs)

            # 多重蒸馏损失计算
            # 1. KL散度损失（软目标）
            kl_loss_fn = torch.nn.KLDivLoss(reduction='batchmean')
            kl_loss = kl_loss_fn(
                torch.log_softmax(student_logits / T, dim=1),
                torch.softmax(teacher_logits / T, dim=1)
            ) * (T * T)

            # 2. 交叉熵损失（硬目标）
            ce_loss_fn = torch.nn.CrossEntropyLoss()
            ce_loss = ce_loss_fn(student_logits, labels)

            # 3. 特征对齐损失（如果模型支持）
            feature_loss = 0.0
            if hasattr(self.model, 'features') and hasattr(teacher_model, 'features'):
                try:
                    student_features = self.model.features(inputs)
                    with torch.no_grad():
                        teacher_features = teacher_model.features(inputs)

                    # L2特征对齐损失
                    feature_loss = torch.nn.functional.mse_loss(
                        student_features, teacher_features
                    )
                except:
                    feature_loss = 0.0

            # 组合损失
            total_loss = alpha * kl_loss + beta * ce_loss + 0.1 * feature_loss

            # 反向传播
            self.model.zero_grad()
            total_loss.backward()

            # 提取并处理梯度
            pseudo_gradients = {}
            total_grad_norm = 0.0

            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    grad = param.grad.clone()
                    # 精度优化的梯度处理 - 更宽松的裁剪
                    grad_norm = torch.norm(grad)
                    if grad_norm > 2.0:  # 提高裁剪阈值
                        grad = grad / grad_norm * 2.0
                    pseudo_gradients[name] = grad
                    total_grad_norm += grad_norm.item() ** 2

            total_grad_norm = total_grad_norm ** 0.5

            logging.info(f"[Client {self.client_id}] 蒸馏补偿成功，生成伪梯度 "
                        f"(KL: {kl_loss:.4f}, CE: {ce_loss:.4f}, Feature: {feature_loss:.4f}, "
                        f"Total: {total_loss:.4f}, GradNorm: {total_grad_norm:.4f})")

            # 清理内存
            del teacher_model, teacher_logits
            torch.cuda.empty_cache() if torch.cuda.is_available() else None

            return pseudo_gradients
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 蒸馏补偿失败: {str(e)}")
            return None

    async def process_server_response(self, server_response):
        """处理服务器响应"""
        try:
            if server_response is None:
                logging.warning(f"[Client {self.client_id}] 服务器响应为空")
                return

            # 获取通信时间 - 使用更合理的计算方式
            if hasattr(self, 'last_model_receive_time'):
                comm_time = time.time() - self.last_model_receive_time
            else:
                comm_time = 0.5  # 默认通信时间

            # 获取通信阈值
            comm_threshold = getattr(server_response, 'comm_threshold', 0.5)

            # 记录当前时间用于下次计算
            self.last_model_receive_time = time.time()
            
            # 使用配置的通信阈值
            if comm_time > comm_threshold:
                logging.info(f"[Client {self.client_id}] 通信时间({comm_time:.3f}s)超过阈值({comm_threshold:.3f}s)，需要蒸馏补偿")
                self.need_distill = True
            else:
                logging.info(f"[Client {self.client_id}] 通信时间({comm_time:.3f}s)正常，不需要蒸馏补偿")
                self.need_distill = False
            
            # 更新客户端状态
            self.update_client_state()
            
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 处理服务器响应时出错: {str(e)}")
            raise

    def __str__(self):
        """返回客户端的字符串表示"""
        return f"Client #{self.client_id}"

    def __repr__(self):
        """返回客户端的字符串表示（用于调试）"""
        return f"Client(client_id={self.client_id})"

# 确保客户端能被Plato框架识别
def client() -> Client:
    return Client()