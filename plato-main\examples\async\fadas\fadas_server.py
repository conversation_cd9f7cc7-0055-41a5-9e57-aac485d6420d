"""
A federated learning training session using FADAS.

Reference:

<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON>, "FADAS: Towards Federated Adaptive Asynchronous Optimization,"
in Proc. Forty-first International Conference on Machine Learning (ICML 2024).

https://openreview.net/forum?id=j56JAd29uH
"""
import asyncio
import copy
import logging
import math
import os
import sys
import time
import numpy as np
from datetime import datetime

import torch

from plato.config import Config
from plato.servers import fedavg
from plato.utils import csv_processor

# 导入复杂网络环境模拟器
try:
    from complex_network_environment import ComplexNetworkEnvironment
    NETWORK_SIMULATION_AVAILABLE = True
except ImportError:
    NETWORK_SIMULATION_AVAILABLE = False
    logging.warning("复杂网络环境模拟器不可用，将使用简单模拟")


class Server(fedavg.Server):
    """A federated learning server using the FedAsync algorithm."""

    def __init__(
            self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None
    ):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )

        self.m = None
        self.v = None
        self.hat_v = None

        # 初始化准确率相关变量，避免KeyError
        self.global_accuracy = 0.0
        self.global_accuracy_std = 0.0
        self.local_accuracy = 0.0
        self.local_accuracy_std = 0.0

        # 初始化复杂网络环境模拟器
        if NETWORK_SIMULATION_AVAILABLE and hasattr(Config().trainer, 'enable_complex_network'):
            self.network_env = ComplexNetworkEnvironment(
                num_clients=Config().clients.total_clients
            )
            self.network_simulation_enabled = True
            logging.info("🌐 启用复杂网络环境模拟")
        else:
            self.network_env = None
            self.network_simulation_enabled = False
            logging.info("📡 使用标准网络模拟")

        # 网络统计
        self.network_stats = {
            'total_communications': 0,
            'successful_communications': 0,
            'failed_communications': 0,
            'avg_communication_time': 0.0,
            'max_communication_time': 0.0,
            'min_communication_time': float('inf')
        }

        # 自定义文件命名
        self._setup_custom_result_file()

    def get_logged_items(self) -> dict:
        """重写get_logged_items方法，确保所有必需的字段都存在"""
        logged_items = {
            "round": self.current_round,
            "accuracy": getattr(self, 'accuracy', 0.0),
            "elapsed_time": self.wall_time - self.initial_wall_time,
        }

        # 只有在存在时才添加这些字段
        if hasattr(self, 'global_accuracy'):
            logged_items["global_accuracy"] = self.global_accuracy
        if hasattr(self, 'global_accuracy_std'):
            logged_items["global_accuracy_std"] = self.global_accuracy_std
        if hasattr(self, 'local_accuracy'):
            logged_items["local_accuracy"] = self.local_accuracy
        if hasattr(self, 'local_accuracy_std'):
            logged_items["local_accuracy_std"] = self.local_accuracy_std
        if hasattr(self, 'avg_staleness'):
            logged_items["avg_staleness"] = self.avg_staleness
        if hasattr(self, 'updates') and self.updates:
            logged_items["processing_time"] = max(
                update.report.processing_time for update in self.updates
            )

        return logged_items

    async def aggregate_deltas(self, updates, deltas_received):
        """Aggregate weight updates from the clients using federated averaging."""
        if self.m is None:
            logging.info("server, m is none, then initialize it")
            # params in AMSGrad
            self.m = {name: self.trainer.zeros(delta.shape) for name, delta in deltas_received[0].items()}
            self.v = {name: self.trainer.zeros(delta.shape) for name, delta in deltas_received[0].items()}
            self.hat_v = {name: self.trainer.zeros(delta.shape) for name, delta in deltas_received[0].items()}
        # update包括client_id, report, payload, staleness
        # report(client_id, num_samples, acc, global_acc, training_time, comm_time, update_response, distribution)
        # Extract the total number of samples
        total_updates = len(updates)

        # Perform weighted averaging
        global_delta = {
            name: self.trainer.zeros(delta.shape)
            for name, delta in deltas_received[0].items()
        }
        avg_update = {
            name: self.trainer.zeros(delta.shape)
            for name, delta in deltas_received[0].items()
        }

        # 设置全局学习率
        delay = [updates[i].staleness for i, _ in enumerate(deltas_received)]

        # 计算平均陈旧程度
        if delay:
            self.avg_staleness = sum(delay) / len(delay)
        else:
            self.avg_staleness = 0.0

        logging.info("round: %d, delay: ", self.current_round)
        logging.info(delay)
        logging.info(f"[Server] Average staleness for round {self.current_round}: {self.avg_staleness:.2f}")

        global_lr = Config().server.global_lr
        """
        if max(delay) > Config().server.tauc:
            lr_lamda = min(1 / (max(delay)), global_lr)
        else:
            lr_lamda = global_lr
        """
        # add
        if max(delay) > Config().server.tauc:
            lr_lambda = 1/(max(delay))
        else:
            lr_lambda = 1
        # 将用户的模型变化累加并平均
        for update in deltas_received: # client
            for name, delta in update.items(): # model layer-wise params delta
                # Use weighted average by the number of samples
                global_delta[name] += delta * (1 / total_updates)

        # 设置参数
        # beta1=0.9时，大概100轮往后，就不怎么其调节作用了
        beta1 = Config().server.beta1
        beta2 = Config().server.beta2
        bias_correction1 = 1 - beta1 ** (self.current_round + 1)
        bias_correction2 = 1 - beta2 ** (self.current_round + 1)

        # 更新AMSGrad算法的相关参数
        # set learning rate
        # step_size = Config().server.global_lr * lr_lambda
        for name, delta in global_delta.items():
            # m_t
            self.m[name].mul_(beta1).add_(delta, alpha=1 - beta1)
            # v_t
            self.v[name].mul_(beta2).addcmul_(delta, delta.conj(), value=1 - beta2)
            # \hat{v_t}
            torch.maximum(self.hat_v[name], self.v[name], out=self.hat_v[name])
            # \sqrt{\hat{v_t}}+\epsilon
            # denom = (self.hat_v[name].sqrt()).add_(Config().server.eps)
            denom = (self.hat_v[name].sqrt() / math.sqrt(bias_correction2)).add_(Config().server.eps) # add
            # step_size = lr_lambda
            step_size = global_lr * lr_lambda / bias_correction1 # add
            avg_update[name].addcdiv_(self.m[name], denom, value=step_size)

            # Yield to other tasks in the server
            await asyncio.sleep(0)

        return avg_update

    def simulate_client_communication(self, client_id: int, data_size_mb: float = 1.0) -> tuple:
        """模拟客户端通信，返回(是否成功, 通信时间, 详细信息)"""
        if self.network_simulation_enabled and self.network_env:
            # 使用复杂网络环境模拟
            success, comm_time, details = self.network_env.simulate_client_communication(
                client_id, data_size_mb
            )

            # 更新网络统计
            self.network_stats['total_communications'] += 1
            if success:
                self.network_stats['successful_communications'] += 1
            else:
                self.network_stats['failed_communications'] += 1

            self.network_stats['avg_communication_time'] = (
                self.network_stats['avg_communication_time'] *
                (self.network_stats['total_communications'] - 1) + comm_time
            ) / self.network_stats['total_communications']

            self.network_stats['max_communication_time'] = max(
                self.network_stats['max_communication_time'], comm_time
            )
            self.network_stats['min_communication_time'] = min(
                self.network_stats['min_communication_time'], comm_time
            )

            # 记录详细日志
            if not success:
                logging.warning(
                    f"❌ 客户端 {client_id} 通信失败 | "
                    f"设备: {details.get('device_type', 'unknown')} | "
                    f"时间: {comm_time:.2f}s | "
                    f"延迟: {details.get('actual_latency', 0):.0f}ms"
                )
            else:
                logging.info(
                    f"✅ 客户端 {client_id} 通信成功 | "
                    f"设备: {details.get('device_type', 'unknown')} | "
                    f"时间: {comm_time:.2f}s"
                )

            return success, comm_time, details
        else:
            # 简单网络模拟
            base_time = np.random.uniform(0.5, 2.0)  # 基础通信时间
            network_delay = np.random.exponential(1.0)  # 网络延迟
            total_time = base_time + network_delay

            # 简单的成功率模拟
            success_rate = 0.85  # 85%成功率
            success = np.random.random() < success_rate

            if not success:
                total_time += np.random.uniform(5, 15)  # 失败时的额外延迟

            self.network_stats['total_communications'] += 1
            if success:
                self.network_stats['successful_communications'] += 1
            else:
                self.network_stats['failed_communications'] += 1

            return success, total_time, {'simple_simulation': True}

    def get_network_statistics(self) -> dict:
        """获取网络统计信息"""
        if self.network_stats['total_communications'] > 0:
            success_rate = (
                self.network_stats['successful_communications'] /
                self.network_stats['total_communications']
            )
        else:
            success_rate = 0.0

        stats = {
            'success_rate': success_rate,
            'total_communications': self.network_stats['total_communications'],
            'avg_communication_time': self.network_stats['avg_communication_time'],
            'max_communication_time': self.network_stats['max_communication_time'],
            'min_communication_time': self.network_stats['min_communication_time']
        }

        if self.network_simulation_enabled and self.network_env:
            # 添加复杂网络环境的统计
            env_stats = self.network_env.get_comprehensive_stats()
            stats.update(env_stats)

        return stats

    def clients_processed(self):
        """重写clients_processed方法以使用自定义文件名"""
        # 调用父类方法
        super().clients_processed()

        # 如果有自定义文件，写入结果
        if hasattr(self, 'custom_result_file') and self.custom_result_file:
            try:
                # 获取要记录的项目
                logged_items = self.get_logged_items()

                # 按照配置文件中定义的顺序准备数据行
                recorded_items = Config().params["result_types"]
                recorded_items_list = [x.strip() for x in recorded_items.split(",")]

                new_row = []
                for item in recorded_items_list:
                    if item in logged_items:
                        new_row.append(logged_items[item])
                    else:
                        new_row.append(0.0)  # 默认值

                # 写入自定义CSV文件
                csv_processor.write_csv(self.custom_result_file, new_row)

            except Exception as e:
                logging.error(f"写入自定义结果文件失败: {e}")

    def _setup_custom_result_file(self):
        """设置自定义的结果文件命名格式"""
        try:
            # 获取配置文件名（从命令行参数）
            config_name = "fadas_default"
            if len(sys.argv) >= 3 and sys.argv[1] == "-c":
                config_file = sys.argv[2]
                # 提取配置文件名（去掉路径和扩展名）
                config_name = os.path.splitext(os.path.basename(config_file))[0]

            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")

            # 生成自定义文件名
            custom_filename = f"{config_name}_{timestamp}.csv"

            # 更新Config中的文件路径
            result_path = Config().params["result_path"]
            custom_result_file = os.path.join(result_path, custom_filename)

            # 保存自定义文件路径供后续使用
            self.custom_result_file = custom_result_file

            # 重新初始化CSV文件
            recorded_items = Config().params["result_types"]
            recorded_items_list = [x.strip() for x in recorded_items.split(",")]

            csv_processor.initialize_csv(
                custom_result_file, recorded_items_list, result_path
            )

            logging.info(f"📊 自定义结果文件: {custom_filename}")
            logging.info(f"📁 完整路径: {custom_result_file}")

        except Exception as e:
            logging.warning(f"设置自定义文件名失败，使用默认命名: {e}")
            self.custom_result_file = None
