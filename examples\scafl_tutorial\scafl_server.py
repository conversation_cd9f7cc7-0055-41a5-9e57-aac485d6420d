"""
SCAFL服务器实现
师妹学习版本 - 详细注释

这个文件实现了SCAFL的服务器端逻辑：
1. 基于Lyapunov优化的客户端选择 (Algorithm 1)
2. 异步聚合管理
3. 陈旧度和虚拟队列的维护
"""

import logging
import asyncio
import random
import time
from plato.servers import fedavg
from plato.config import Config
from scafl_algorithm import Algorithm


class Server(fedavg.Server):
    """SCAFL服务器类
    
    继承自FedAvg服务器，这样我们可以复用大部分功能，
    只需要重写客户端选择和聚合逻辑
    """
    
    def __init__(self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None):
        """初始化SCAFL服务器"""
        super().__init__(model, datasource, algorithm, trainer, callbacks)
        
        # 从配置读取SCAFL参数
        config = Config()
        self.tau_max = getattr(config.server, 'tau_max', 5)
        self.V = getattr(config.server, 'V', 1.0)
        self.max_aggregation_clients = getattr(config.server, 'max_aggregation_clients', 5)
        
        # SCAFL状态管理
        self.client_training_times = {}    # 客户端训练时间估计
        self.completed_clients = []        # 已完成训练的客户端
        self.current_round = 0            # 当前轮次
        
        logging.info(f"[SCAFL Server] 初始化完成")
        logging.info(f"[SCAFL Server] 参数: tau_max={self.tau_max}, V={self.V}, max_clients={self.max_aggregation_clients}")
    
    def configure(self):
        """配置服务器，确保使用SCAFL算法"""
        super().configure()
        
        # 确保使用SCAFL算法
        if not isinstance(self.algorithm, Algorithm):
            logging.info("[SCAFL Server] 创建SCAFL算法实例")
            self.algorithm = Algorithm(self.trainer)
    
    def estimate_client_training_time(self, client_id):
        """估计客户端训练时间
        
        在实际实现中，这可以基于：
        1. 历史训练时间
        2. 客户端硬件能力
        3. 网络状况
        4. 数据集大小
        
        现在我们使用简单的模拟
        
        Args:
            client_id: 客户端ID
            
        Returns:
            float: 估计的训练时间（秒）
        """
        if client_id not in self.client_training_times:
            # 模拟不同客户端的训练时间（2-10秒）
            # 使用固定种子确保可重复性
            random.seed(client_id)
            base_time = random.uniform(2.0, 10.0)
            self.client_training_times[client_id] = base_time
            
        return self.client_training_times[client_id]
    
    def compute_objective_function(self, candidate_clients):
        """计算SCAFL目标函数值
        
        实现论文公式(17): V*Dt + Σ Qk(t)*((τk(t)+1)(1-βt_k) - τmax)
        
        这个函数的作用：
        - 平衡训练时间和队列稳定性
        - V越大，越重视训练时间
        - V越小，越重视队列稳定性
        
        Args:
            candidate_clients: 候选聚合客户端列表
            
        Returns:
            float: 目标函数值（越小越好）
        """
        # 第一项：V * Dt（训练时间项）
        if candidate_clients:
            # Dt = 所有聚合客户端中的最大训练时间
            training_time = max(self.estimate_client_training_time(cid) for cid in candidate_clients)
        else:
            training_time = 0
        
        time_term = self.V * training_time
        
        # 第二项：队列稳定性项
        queue_term = 0.0
        
        # 假设我们有total_clients个客户端
        all_clients = list(range(1, self.total_clients + 1))
        
        for client_id in all_clients:
            # 获取虚拟队列长度和陈旧度
            queue_length = self.algorithm.get_virtual_queue_length(client_id)
            staleness = self.algorithm.get_client_staleness(client_id)
            
            if client_id in candidate_clients:
                # 参与聚合: βt_k = 1, (1-βt_k) = 0
                queue_contribution = queue_length * (0 - self.tau_max)
            else:
                # 未参与聚合: βt_k = 0, (1-βt_k) = 1
                queue_contribution = queue_length * ((staleness + 1) - self.tau_max)
            
            queue_term += queue_contribution
        
        objective_value = time_term + queue_term
        
        logging.debug(f"[SCAFL] 候选集合{candidate_clients}: "
                     f"时间项={time_term:.2f}, 队列项={queue_term:.2f}, "
                     f"总目标值={objective_value:.2f}")
        
        return objective_value
    
    def sc_afl_client_selection(self, available_clients):
        """SCAFL客户端选择算法
        
        实现论文Algorithm 1的逻辑：
        1. 按训练时间排序
        2. 逐个添加客户端
        3. 选择目标函数值最小的组合
        
        Args:
            available_clients: 可用客户端列表
            
        Returns:
            list: 选择的客户端列表
        """
        if not available_clients:
            return []
        
        logging.info(f"[SCAFL] 开始客户端选择，可用客户端: {available_clients}")
        
        # Step 1: 按训练时间排序（升序）
        # 优先考虑训练时间短的客户端
        sorted_clients = sorted(available_clients, 
                              key=lambda cid: self.estimate_client_training_time(cid))
        
        logging.debug(f"[SCAFL] 按训练时间排序: {sorted_clients}")
        
        # Step 2: 逐个添加客户端，寻找最优聚合集合
        best_clients = []
        min_objective = float('inf')
        
        # 最多考虑max_aggregation_clients个客户端
        max_clients = min(len(sorted_clients), self.max_aggregation_clients)
        
        for i in range(1, max_clients + 1):
            # 候选集合：前i个客户端
            candidate_clients = sorted_clients[:i]
            
            # 计算目标函数值
            objective_value = self.compute_objective_function(candidate_clients)
            
            logging.debug(f"[SCAFL] 候选集合大小{i}: {candidate_clients}, "
                         f"目标函数值={objective_value:.4f}")
            
            # 如果找到更好的解，更新最优解
            if objective_value < min_objective:
                min_objective = objective_value
                best_clients = candidate_clients.copy()
        
        logging.info(f"[SCAFL] 选择客户端: {best_clients}, "
                    f"最优目标函数值: {min_objective:.4f}")
        
        return best_clients
    
    async def select_clients(self):
        """选择参与本轮训练的客户端
        
        这里我们实现两阶段选择：
        1. 选择参与训练的客户端（可以是随机的）
        2. 从完成训练的客户端中选择参与聚合的客户端（SCAFL算法）
        
        注意：在真实的异步场景中，这个函数会被多次调用
        """
        # 获取所有可用客户端
        all_clients = list(range(1, self.total_clients + 1))
        
        # 过滤掉陈旧度过高的客户端
        valid_clients = []
        for client_id in all_clients:
            staleness = self.algorithm.get_client_staleness(client_id)
            if staleness <= self.tau_max:
                valid_clients.append(client_id)
            else:
                logging.warning(f"[SCAFL] 客户端{client_id}陈旧度过高({staleness} > {self.tau_max})，跳过")
        
        if not valid_clients:
            logging.warning("[SCAFL] 没有有效客户端，使用随机选择")
            selected = random.sample(all_clients, 
                                   min(self.clients_per_round, len(all_clients)))
        else:
            # 使用SCAFL算法选择客户端
            selected = self.sc_afl_client_selection(valid_clients)
            
            # 如果选择的客户端太少，补充一些随机客户端
            if len(selected) < min(self.clients_per_round, len(valid_clients)):
                remaining = [c for c in valid_clients if c not in selected]
                additional_count = min(self.clients_per_round - len(selected), len(remaining))
                if additional_count > 0:
                    additional = random.sample(remaining, additional_count)
                    selected.extend(additional)
                    logging.info(f"[SCAFL] 补充随机客户端: {additional}")
        
        self.current_round += 1
        logging.info(f"[SCAFL] 第{self.current_round}轮选择了{len(selected)}个客户端: {selected}")
        
        return selected
    
    async def aggregate_weights(self, updates, baseline_weights=None, weights_received=None):
        """聚合客户端权重更新

        这个函数协调整个聚合过程：
        1. 使用SCAFL算法聚合权重
        2. 更新所有客户端的陈旧度和虚拟队列

        Args:
            updates: 客户端更新列表
            baseline_weights: 基线权重（兼容父类接口）
            weights_received: 接收到的权重（兼容父类接口）
        """
        if not updates:
            logging.warning("[SCAFL Server] 没有收到客户端更新")
            return

        logging.info(f"[SCAFL Server] 开始聚合{len(updates)}个客户端的更新")

        # 提取权重和客户端信息
        if baseline_weights is None:
            baseline_weights = self.algorithm.extract_weights()
        if weights_received is None:
            weights_received = []
            for update in updates:
                weights_received.append(update.payload)

        participating_clients = []
        for update in updates:
            participating_clients.append(update.client_id)
        
        # 使用SCAFL算法聚合权重
        aggregated_weights = await self.algorithm.aggregate_weights(
            baseline_weights, weights_received
        )
        
        # 更新全局模型
        self.algorithm.load_weights(aggregated_weights)
        
        # 更新所有客户端的陈旧度和虚拟队列
        all_clients = list(range(1, self.total_clients + 1))
        for client_id in all_clients:
            participated = client_id in participating_clients
            self.algorithm.update_client_staleness(client_id, participated)
            self.algorithm.update_virtual_queue(client_id, participated)
        
        logging.info(f"[SCAFL Server] 第{self.current_round}轮聚合完成，"
                    f"参与客户端: {participating_clients}")
        
        return aggregated_weights
