"""
SCAFL服务器实现
师妹学习版本 - 详细注释和设计思路

设计思路：
1. 继承FedAvg服务器，复用基础功能
2. 重写客户端选择逻辑，实现SCAFL的智能选择
3. 重写聚合逻辑，支持异步聚合和陈旧度管理
4. 添加SCAFL特有的状态管理（陈旧度、虚拟队列）

核心创新：
- 不是所有完成训练的客户端都参与聚合
- 用Lyapunov优化选择最优聚合集合
- 平衡训练效率和模型精度
"""

import logging
import asyncio
import random
import time
from plato.servers import fedavg
from plato.config import Config
from scafl_algorithm import Algorithm


class Server(fedavg.Server):
    """SCAFL服务器类

    设计思路：
    1. 继承FedAvg服务器 - 复用连接管理、模型分发等基础功能
    2. 添加SCAFL特有状态 - 陈旧度跟踪、虚拟队列管理
    3. 重写关键方法 - 客户端选择和聚合逻辑

    SCAFL vs FedAvg的区别：
    - FedAvg: 随机选择 → 等待所有客户端 → 简单平均
    - SCAFL: 智能选择 → 异步等待 → 陈旧度感知聚合
    """

    def __init__(self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None):
        """初始化SCAFL服务器

        初始化步骤：
        1. 调用父类初始化 - 获得基础服务器功能
        2. 读取SCAFL配置参数
        3. 初始化SCAFL特有状态变量
        """
        # 第一步：调用父类初始化，获得基础功能
        super().__init__(model, datasource, algorithm, trainer, callbacks)

        # 第二步：从配置读取SCAFL参数
        config = Config()
        self.tau_max = getattr(config.server, 'tau_max')  # 最大陈旧度阈值
        self.V = getattr(config.server, 'V')            # Lyapunov权衡参数
        self.max_aggregation_clients = getattr(config.server, 'max_aggregation_clients')  # 最大聚合客户端数

        # 第三步：初始化SCAFL状态管理变量
        self.client_training_times = {}    # 客户端训练时间估计（用于目标函数计算）
        self.completed_clients = []        # 已完成训练等待聚合的客户端
        self.current_round = 0            # 当前轮次计数器

        logging.info(f"[SCAFL Server] 初始化完成")
        logging.info(f"[SCAFL Server] 参数: tau_max={self.tau_max}, V={self.V}, max_clients={self.max_aggregation_clients}")
        logging.info(f"[SCAFL Server] 设计理念: 智能客户端选择 + 陈旧度感知聚合")
    
    def configure(self):
        """配置服务器，确保使用SCAFL算法

        配置步骤：
        1. 调用父类配置 - 设置基础组件（模型、数据源等）
        2. 确保使用SCAFL算法 - 替换默认的FedAvg算法

        为什么需要这一步？
        - Plato默认使用FedAvg算法
        - 我们需要替换为SCAFL算法来获得陈旧度感知功能
        """
        # 第一步：调用父类配置，设置基础组件
        super().configure()

        # 第二步：确保使用SCAFL算法而不是默认的FedAvg
        if not isinstance(self.algorithm, Algorithm):
            logging.info("[SCAFL Server] 检测到非SCAFL算法，正在创建SCAFL算法实例")
            self.algorithm = Algorithm(self.trainer)
            logging.info("[SCAFL Server] SCAFL算法实例创建完成")
        else:
            logging.info("[SCAFL Server] 已使用SCAFL算法")
    
    def estimate_client_training_time(self, client_id):
        """估计客户端训练时间

        设计思路：
        - 在真实场景中，这是SCAFL的关键组件
        - 需要准确估计每个客户端的训练时间来优化选择

        实际实现可以基于：
        1. 历史训练时间统计
        2. 客户端硬件能力（CPU、内存、GPU）
        3. 网络状况（带宽、延迟）
        4. 数据集大小和复杂度
        5. 当前系统负载

        当前实现：
        - 使用简单模拟，为每个客户端分配固定的训练时间
        - 模拟设备异构性（不同客户端有不同的训练速度）

        Args:
            client_id: 客户端ID

        Returns:
            float: 估计的训练时间（秒）
        """
        if client_id not in self.client_training_times:
            # 模拟设备异构性：不同客户端有不同的训练时间
            # 使用客户端ID作为种子，确保结果可重复
            random.seed(client_id)
            base_time = random.uniform(2.0, 10.0)  # 2-10秒的训练时间
            self.client_training_times[client_id] = base_time

            logging.debug(f"[SCAFL Server] 客户端{client_id}估计训练时间: {base_time:.2f}秒")

        return self.client_training_times[client_id]
    
    def compute_objective_function(self, candidate_clients):
        """计算SCAFL目标函数值 - 这是SCAFL算法的数学核心！

        实现论文公式(17): V*Dt + Σ Qk(t)*((τk(t)+1)(1-βt_k) - τmax)

        数学含义解释：
        - 第一项 V*Dt: 训练时间成本
          * Dt = max(训练时间) 所有聚合客户端中的最大训练时间
          * V 是权衡参数，V越大越重视训练速度

        - 第二项 Σ队列项: 陈旧度稳定性成本
          * 对每个客户端计算队列贡献
          * 参与聚合的客户端：队列减少（负贡献）
          * 未参与聚合的客户端：队列增加（正贡献）

        优化目标：选择使总成本最小的客户端组合

        Args:
            candidate_clients: 候选聚合客户端列表

        Returns:
            float: 目标函数值（越小越好）
        """
        # 第一项：V * Dt（训练时间项）
        if candidate_clients:
            # Dt = 所有聚合客户端中的最大训练时间
            # 为什么是最大值？因为聚合要等最慢的客户端完成
            training_time = max(self.estimate_client_training_time(cid) for cid in candidate_clients)
        else:
            training_time = 0

        time_term = self.V * training_time

        # 第二项：队列稳定性项
        queue_term = 0.0

        # 对所有客户端计算队列贡献
        all_clients = list(range(1, self.total_clients + 1))

        for client_id in all_clients:
            # 获取当前状态
            queue_length = self.algorithm.get_virtual_queue_length(client_id)
            staleness = self.algorithm.get_client_staleness(client_id)

            if client_id in candidate_clients:
                # 参与聚合: βt_k = 1, (1-βt_k) = 0
                # 队列减少，这是好事（负贡献）
                queue_contribution = queue_length * (0 - self.tau_max)
            else:
                # 未参与聚合: βt_k = 0, (1-βt_k) = 1
                # 队列增加，这是坏事（正贡献）
                queue_contribution = queue_length * ((staleness + 1) - self.tau_max)

            queue_term += queue_contribution

        # 总目标函数值
        objective_value = time_term + queue_term

        logging.debug(f"[SCAFL] 目标函数计算:")
        logging.debug(f"  候选客户端: {candidate_clients}")
        logging.debug(f"  训练时间项: V({self.V}) × Dt({training_time:.2f}) = {time_term:.2f}")
        logging.debug(f"  队列稳定项: {queue_term:.2f}")
        logging.debug(f"  总目标值: {objective_value:.2f}")

        return objective_value
    
    def sc_afl_client_selection(self, available_clients):
        """SCAFL客户端选择算法 - 实现论文Algorithm 1

        算法思路（这是SCAFL的核心创新！）：
        1. 不是随机选择客户端
        2. 不是选择所有完成训练的客户端
        3. 而是用数学优化选择最优的客户端子集

        Algorithm 1步骤：
        Step 1: 按训练时间排序（优先选择快的客户端）
        Step 2: 逐个尝试不同大小的聚合集合
        Step 3: 计算每个集合的目标函数值
        Step 4: 选择目标函数值最小的集合

        为什么这样做？
        - 平衡训练速度和模型精度
        - 避免等待太慢的客户端
        - 同时保证陈旧度不会过高

        Args:
            available_clients: 可用客户端列表（已完成训练的客户端）

        Returns:
            list: 选择参与聚合的客户端列表
        """
        if not available_clients:
            logging.warning("[SCAFL] 没有可用客户端")
            return []

        logging.info(f"[SCAFL] 开始智能客户端选择")
        logging.info(f"[SCAFL] 可用客户端: {available_clients}")

        # Step 1: 按训练时间排序（升序）
        # 为什么按训练时间排序？优先考虑训练快的客户端，减少等待时间
        sorted_clients = sorted(available_clients,
                              key=lambda cid: self.estimate_client_training_time(cid))

        training_times = [self.estimate_client_training_time(cid) for cid in sorted_clients]
        logging.info(f"[SCAFL] 按训练时间排序: {list(zip(sorted_clients, training_times))}")

        # Step 2: 逐个添加客户端，寻找最优聚合集合
        best_clients = []
        min_objective = float('inf')

        # 最多考虑max_aggregation_clients个客户端
        max_clients = min(len(sorted_clients), self.max_aggregation_clients)

        logging.info(f"[SCAFL] 尝试1到{max_clients}个客户端的组合")

        for i in range(1, max_clients + 1):
            # 候选集合：前i个训练时间最短的客户端
            candidate_clients = sorted_clients[:i]

            # 计算这个组合的目标函数值
            objective_value = self.compute_objective_function(candidate_clients)

            logging.info(f"[SCAFL] 尝试{i}个客户端: {candidate_clients}")
            logging.info(f"[SCAFL] 目标函数值: {objective_value:.4f}")

            # 如果找到更好的解，更新最优解
            if objective_value < min_objective:
                min_objective = objective_value
                best_clients = candidate_clients.copy()
                logging.info(f"[SCAFL] 找到更优解！当前最优: {best_clients}")

        logging.info(f"[SCAFL] 最终选择: {best_clients}")
        logging.info(f"[SCAFL] 最优目标函数值: {min_objective:.4f}")

        return best_clients
    
    def choose_clients(self, clients_pool, clients_count):
        """重写客户端选择逻辑 - 在训练阶段就应用SCAFL智能选择

        设计思路变更：
        - 原计划：训练阶段沿用随机选择，聚合阶段应用SCAFL选择
        - 新方案：在训练阶段就应用SCAFL的智能选择逻辑

        SCAFL客户端选择的核心思想：
        1. 不是简单的随机选择
        2. 考虑客户端的陈旧度状态
        3. 使用Lyapunov优化选择最优客户端子集

        Args:
            clients_pool: 可用客户端池
            clients_count: 需要选择的客户端数量

        Returns:
            list: 选择的客户端列表
        """
        logging.info(f"[SCAFL] 开始SCAFL智能客户端选择")
        logging.info(f"[SCAFL] 客户端池: {clients_pool}, 需要选择: {clients_count}个")

        # 第一步：基本检查
        assert clients_count <= len(clients_pool), f"需要选择{clients_count}个客户端，但池中只有{len(clients_pool)}个"

        # 第二步：过滤陈旧度过高的客户端
        valid_clients = []
        filtered_clients = []

        for client_id in clients_pool:
            staleness = self.algorithm.get_client_staleness(client_id)
            if staleness <= self.tau_max:
                valid_clients.append(client_id)
            else:
                filtered_clients.append(client_id)
                logging.debug(f"[SCAFL] 客户端{client_id}陈旧度过高({staleness} > {self.tau_max})，过滤")

        if filtered_clients:
            logging.info(f"[SCAFL] 过滤了{len(filtered_clients)}个陈旧度过高的客户端")

        # 第三步：如果有效客户端不足，回退到随机选择
        if len(valid_clients) < clients_count:
            logging.warning(f"[SCAFL] 有效客户端不足({len(valid_clients)} < {clients_count})，回退到随机选择")
            # 保持原有的随机选择逻辑
            random.setstate(self.prng_state)
            selected_clients = random.sample(clients_pool, clients_count)
            self.prng_state = random.getstate()
        else:
            # 第四步：使用SCAFL智能选择算法
            logging.info(f"[SCAFL] 从{len(valid_clients)}个有效客户端中智能选择{clients_count}个")

            # 如果有效客户端数量正好等于需要的数量，直接返回
            if len(valid_clients) == clients_count:
                selected_clients = valid_clients
            else:
                # 使用SCAFL算法选择最优子集
                selected_clients = self.sc_afl_client_selection(valid_clients)

                # 如果选择的数量不足，补充随机客户端
                if len(selected_clients) < clients_count:
                    remaining = [c for c in valid_clients if c not in selected_clients]
                    need_count = clients_count - len(selected_clients)
                    if remaining and need_count > 0:
                        additional = random.sample(remaining, min(need_count, len(remaining)))
                        selected_clients.extend(additional)
                        logging.info(f"[SCAFL] 补充{len(additional)}个随机客户端")

                # 如果选择的数量过多，截取前N个
                if len(selected_clients) > clients_count:
                    selected_clients = selected_clients[:clients_count]

        # 第五步：记录日志
        logging.info(f"[SCAFL] 最终选择的客户端: {selected_clients}")
        logging.info(f"[SCAFL] 选择策略: SCAFL智能选择（考虑陈旧度和Lyapunov优化）")

        return selected_clients
    
    async def aggregate_weights(self, updates, baseline_weights=None, weights_received=None):
        """聚合客户端权重更新 - 简化版聚合逻辑

        设计变更说明：
        - 由于客户端选择逻辑已经前移到choose_clients函数
        - 这里主要负责陈旧度感知的权重聚合
        - 不再需要在聚合阶段进行二次客户端选择

        SCAFL聚合特色：
        1. 陈旧度感知权重聚合（根据陈旧度分配权重）
        2. 更新所有客户端的陈旧度和虚拟队列状态

        Args:
            updates: 收到的客户端更新列表
            baseline_weights: 基线权重（兼容父类接口）
            weights_received: 接收到的权重（兼容父类接口）
        """
        if not updates:
            logging.warning("[SCAFL Server] 没有收到客户端更新，跳过聚合")
            return

        logging.info(f"[SCAFL Server] 开始SCAFL陈旧度感知聚合")
        logging.info(f"[SCAFL Server] 收到{len(updates)}个客户端更新")

        # 第一步：提取参与聚合的客户端信息
        participating_clients = []
        for update in updates:
            participating_clients.append(update.client_id)

        logging.info(f"[SCAFL Server] 参与聚合的客户端: {participating_clients}")

        # 第二步：准备聚合数据
        if baseline_weights is None:
            baseline_weights = self.algorithm.extract_weights()
        if weights_received is None:
            weights_received = []
            for update in updates:
                weights_received.append(update.payload)

        # 第三步：执行SCAFL陈旧度感知聚合
        logging.info("[SCAFL Server] 执行陈旧度感知权重聚合")
        aggregated_weights = await self.algorithm.aggregate_weights(
            baseline_weights, weights_received
        )

        # 第四步：更新全局模型
        self.algorithm.load_weights(aggregated_weights)
        logging.info("[SCAFL Server] 全局模型更新完成")

        # 第五步：更新所有客户端的陈旧度和虚拟队列状态
        logging.info("[SCAFL Server] 更新客户端陈旧度和虚拟队列")
        all_clients = list(range(1, self.total_clients + 1))

        for client_id in all_clients:
            # 参与本轮聚合的客户端
            participated = client_id in participating_clients

            # 更新陈旧度
            old_staleness = self.algorithm.get_client_staleness(client_id)
            self.algorithm.update_client_staleness(client_id, participated)
            new_staleness = self.algorithm.get_client_staleness(client_id)

            # 更新虚拟队列
            old_queue = self.algorithm.get_virtual_queue_length(client_id)
            self.algorithm.update_virtual_queue(client_id, participated)
            new_queue = self.algorithm.get_virtual_queue_length(client_id)

            if participated:
                logging.debug(f"[SCAFL] 客户端{client_id}参与聚合: "
                            f"陈旧度{old_staleness}→{new_staleness}, "
                            f"队列{old_queue:.2f}→{new_queue:.2f}")
            else:
                logging.debug(f"[SCAFL] 客户端{client_id}未参与聚合: "
                            f"陈旧度{old_staleness}→{new_staleness}, "
                            f"队列{old_queue:.2f}→{new_queue:.2f}")

        logging.info(f"[SCAFL Server] SCAFL聚合完成")
        logging.info(f"[SCAFL Server] 特色: 陈旧度感知聚合 + 状态管理")

        return aggregated_weights
