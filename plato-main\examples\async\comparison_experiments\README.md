# ResNet9 CIFAR10 算法对比试验

本目录包含了6个联邦学习算法在ResNet9模型和CIFAR10数据集上的对比试验配置文件。

## 算法列表

1. **FedBuff** - 基于异步联邦学习的缓冲聚合算法
2. **FedAC** - 基于异步联邦学习的自适应聚合算法  
3. **FADAS** - 基于异步联邦学习的自适应延迟感知算法
4. **FedAsync** - 基于异步联邦学习的基础算法
5. **SCAFL** - 基于陈旧度感知的客户端自适应联邦学习算法
6. **ReFedSCAFL** - 基于声誉的联邦脚手架学习与知识蒸馏补偿算法

## 配置文件

| 算法 | 配置文件 | 端口 | 结果路径 |
|------|----------|------|----------|
| FedBuff | `fedbuff_resnet9_cifar10.yml` | 8001 | `results/comparison/fedbuff` |
| FedAC | `fedac_resnet9_cifar10.yml` | 8002 | `results/comparison/fedac` |
| FADAS | `fadas_resnet9_cifar10.yml` | 8003 | `results/comparison/fadas` |
| FedAsync | `fedasync_resnet9_cifar10.yml` | 8004 | `results/comparison/fedasync` |
| SCAFL | `scafl_resnet9_cifar10.yml` | 8005 | `results/comparison/scafl` |
| ReFedSCAFL | `refedscafl_resnet9_cifar10.yml` | 8006 | `results/comparison/refedscafl` |

## 统一实验设置

所有算法使用相同的基础配置以确保公平对比：

### 数据设置
- **数据集**: CIFAR10
- **客户端总数**: 100
- **每轮选择客户端数**: 20
- **每客户端数据量**: 300样本
- **数据分布**: Non-IID (concentration=0.5)
- **测试集大小**: 100

### 模型设置
- **模型**: ResNet-9
- **类别数**: 10 (CIFAR10)
- **本地训练轮数**: 5 epochs
- **批大小**: 50
- **优化器**: SGD
- **学习率**: 0.01
- **动量**: 0.9
- **权重衰减**: 0.0001

### 训练设置
- **总训练轮数**: 400
- **最大并发客户端**: 10
- **目标准确率**: 100%
- **学习率调度**: LambdaLR (gamma=0.1, milestone_steps=80ep,120ep)

### 异步设置
- **同步模式**: False (异步)
- **陈旧度阈值**: 5
- **最小聚合客户端数**: 10
- **客户端速度模拟**: Pareto分布 (alpha=1)

## 使用方法

### 方法1: 使用运行脚本（推荐）

```bash
# 给脚本执行权限
chmod +x run_comparison.sh

# 运行所有算法对比试验
./run_comparison.sh
```

### 方法2: 单独运行算法

```bash
# 运行FedBuff
cd ../fedbuff
python fedbuff.py -c ../comparison_experiments/fedbuff_resnet9_cifar10.yml

# 运行FedAC
cd ../fedac
python fedac.py -c ../comparison_experiments/fedac_resnet9_cifar10.yml

# 运行FADAS
cd ../fadas
python fadas.py -c ../comparison_experiments/fadas_resnet9_cifar10.yml

# 运行FedAsync
cd ../fedasync
python fedasync.py -c ../comparison_experiments/fedasync_resnet9_cifar10.yml

# 运行SCAFL
cd ../SC_AFL
python sc_afl.py -c ../comparison_experiments/scafl_resnet9_cifar10.yml

# 运行ReFedSCAFL
cd ../refedscafl
python refedscafl.py -c ../comparison_experiments/refedscafl_resnet9_cifar10.yml
```

## 结果分析

实验完成后，可以在以下位置找到结果：

- **CSV结果文件**: `results/comparison/{algorithm}/`
- **模型检查点**: `models/comparison/{algorithm}/`

每个结果文件包含以下指标：
- `round`: 训练轮数
- `elapsed_time`: 经过时间
- `accuracy`: 本地准确率
- `global_accuracy`: 全局准确率
- `global_accuracy_std`: 全局准确率标准差

## 算法特点对比

| 算法 | 主要特点 | 优势 |
|------|----------|------|
| FedBuff | 缓冲聚合 | 减少通信频率，提高效率 |
| FedAC | 自适应聚合 | 动态调整聚合策略 |
| FADAS | 延迟感知 | 考虑网络延迟，优化性能 |
| FedAsync | 基础异步 | 简单有效的异步实现 |
| SCAFL | 陈旧度感知 | 智能客户端选择策略 |
| ReFedSCAFL | 声誉+知识蒸馏 | 综合多种优化技术 |

## 注意事项

1. 确保所有算法的依赖已正确安装
2. 运行前检查端口是否被占用
3. 建议在GPU环境下运行以加速训练
4. 实验可能需要较长时间完成（数小时到数天）
5. 确保有足够的磁盘空间存储结果和模型文件
