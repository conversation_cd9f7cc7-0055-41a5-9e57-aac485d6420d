"""
A federated learning client using SCAFFOLD.

Reference:

<PERSON><PERSON><PERSON><PERSON> et al., "SCAFFOLD: Stochastic Controlled Averaging for Federated Learning,"
in Proceedings of the 37th International Conference on Machine Learning (ICML), 2020.

https://arxiv.org/pdf/1910.06378.pdf
"""

import logging
import os
import pickle
import numpy as np
from torch.utils.data import Subset
import matplotlib.pyplot as plt

from plato.clients import simple
from plato.config import Config


def visualize_data_distribution(client_id, dataset, save_dir='data_distribution'):
    """
    可视化客户端数据分布
    
    参数:
        client_id: 客户端ID
        dataset: 客户端数据集
        save_dir: 保存目录
    """
    try:
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 提取标签
        if hasattr(dataset, 'targets'):
            labels = dataset.targets
        else:
            # 如果是Subset类型，需要从原始数据集提取标签
            if isinstance(dataset, Subset):
                if hasattr(dataset.dataset, 'targets'):
                    all_labels = dataset.dataset.targets
                    indices = dataset.indices
                    labels = [all_labels[i] for i in indices]
                else:
                    # 尝试从数据集中提取标签
                    labels = []
                    for i in range(len(dataset)):
                        _, label = dataset[i]
                        labels.append(label)
            else:
                # 尝试从数据集中提取标签
                labels = []
                for i in range(len(dataset)):
                    try:
                        _, label = dataset[i]
                        labels.append(label)
                    except:
                        # 对于特殊数据集(如Shakespeare)可能有不同的格式
                        logging.info(f"[Client #{client_id}] 无法提取标签，可能是特殊格式数据集")
                        return False
        
        # 转换为numpy数组
        if not isinstance(labels, np.ndarray):
            labels = np.array(labels)
        
        # 计算每个类别的样本数
        unique_labels = np.unique(labels)
        class_counts = [np.sum(labels == label) for label in unique_labels]
        
        # 绘制类别分布柱状图
        plt.figure(figsize=(10, 6))
        plt.bar(unique_labels, class_counts)
        plt.xlabel('Class')
        plt.ylabel('Number of Samples')
        plt.title(f'Class Distribution for Client {client_id} (Total: {len(dataset)} samples)')
        plt.xticks(unique_labels)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        
        # 保存图像
        save_path = os.path.join(save_dir, f'client_{client_id}_distribution.png')
        plt.savefig(save_path)
        plt.close()
        
        logging.info(f"Client {client_id} data distribution saved to {save_path}")
        
        # 保存数据分布的文本描述
        text_path = os.path.join(save_dir, f'client_{client_id}_distribution.txt')
        with open(text_path, 'w') as f:
            f.write(f"Client {client_id} Data Distribution\n")
            f.write(f"Total samples: {len(dataset)}\n")
            f.write("Class distribution:\n")
            for label, count in zip(unique_labels, class_counts):
                f.write(f"  Class {label}: {count} samples ({count/len(dataset)*100:.2f}%)\n")
        
        return True
    except Exception as e:
        logging.error(f"Error visualizing data distribution for client {client_id}: {str(e)}")
        return False


class Client(simple.Client):
    """A SCAFFOLD federated learning client who sends weight updates
    and client control variate."""

    def __init__(
        self,
        model=None,
        datasource=None,
        algorithm=None,
        trainer=None,
        callbacks=None,
    ):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )

        self.client_control_variate = None

    def configure(self) -> None:
        """Initialize the server control variate and client control variate for the trainer."""
        super().configure()

        # Load the client control variate if the client has participated before
        model_path = Config().params["model_path"]
        model_name = Config().trainer.model_name
        filename = f"{model_name}_{self.client_id}_control_variate.pth"
        client_control_variate_path = f"{model_path}/{filename}"

        if os.path.exists(client_control_variate_path):
            logging.info(
                "[Client #%d] Loading the control variate from %s.",
                self.client_id,
                client_control_variate_path,
            )
            # 从文件中加载cleint_control_variate
            with open(client_control_variate_path, "rb") as path:
                self.client_control_variate = pickle.load(path)
            self.trainer.client_control_variate = self.client_control_variate
        else:
            self.trainer.client_control_variate = None
            self.client_control_variate = None

        self.trainer.client_control_variate_path = client_control_variate_path

        # client control variate delta
        delta_filename = f"{model_name}_{self.client_id}_control_variate_delta.pth"
        client_control_variate_delta_path = f"{model_path}/{delta_filename}"
        self.trainer.client_control_variate_delta_path = client_control_variate_delta_path

    def _allocate_data(self) -> None:
        """重写_allocate_data方法以添加数据可视化功能"""
        # 调用父类的方法分配数据
        super()._allocate_data()
        
        # 检查是否需要可视化数据分布
        if hasattr(Config().data, 'visualize_distribution') and Config().data.visualize_distribution:
            try:
                if self.trainset is not None:
                    visualize_data_distribution(self.client_id, self.trainset)
                    logging.info(f"[Client #{self.client_id}] 已可视化训练数据分布")
            except Exception as e:
                logging.error(f"[Client #{self.client_id}] 可视化数据分布失败: {str(e)}")
