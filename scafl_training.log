2025-08-08 11:40:50,534 - root - INFO - 🚀 启动SCAFL (Staleness-Controlled Asynchronous Federated Learning)
2025-08-08 11:40:50,535 - root - INFO - 👩‍🏫 师妹学习版本 - 详细注释
2025-08-08 11:40:50,535 - root - INFO - 📋 第一步：加载配置文件...
2025-08-08 11:40:50,556 - root - INFO - ✅ 成功加载配置文件: D:\Experiment\orgin-plato-main\examples\scafl_tutorial\scafl_config.yml
2025-08-08 11:40:50,556 - root - INFO - 📊 第二步：显示实验配置...
2025-08-08 11:40:50,556 - root - INFO - ============================================================
2025-08-08 11:40:50,556 - root - INFO - 🎯 SCAFL实验配置信息
2025-08-08 11:40:50,557 - root - INFO - ============================================================
2025-08-08 11:40:50,557 - root - INFO - 📊 基本设置:
2025-08-08 11:40:50,557 - root - INFO -    - 总客户端数: 20
2025-08-08 11:40:50,557 - root - INFO -    - 每轮训练客户端数: 10
2025-08-08 11:40:50,557 - root - INFO -    - 训练轮数: 20
2025-08-08 11:40:50,557 - root - INFO -    - 目标准确率: 0.9
2025-08-08 11:40:50,557 - root - INFO - 🔧 SCAFL参数:
2025-08-08 11:40:50,557 - root - INFO -    - 最大陈旧度 (τmax): 5
2025-08-08 11:40:50,557 - root - INFO -    - Lyapunov参数 (V): 1.0
2025-08-08 11:40:50,557 - root - INFO -    - 最大聚合客户端数: 5
2025-08-08 11:40:50,557 - root - INFO - 📚 数据和模型:
2025-08-08 11:40:50,557 - root - INFO -    - 数据集: MNIST
2025-08-08 11:40:50,557 - root - INFO -    - 模型: lenet5
2025-08-08 11:40:50,557 - root - INFO -    - 每客户端数据量: 300
2025-08-08 11:40:50,557 - root - INFO -    - 数据分布: noniid
2025-08-08 11:40:50,557 - root - INFO - ⚙️ 训练参数:
2025-08-08 11:40:50,557 - root - INFO -    - 学习率: 0.01
2025-08-08 11:40:50,557 - root - INFO -    - 批大小: 32
2025-08-08 11:40:50,558 - root - INFO -    - 本地训练轮数: 1
2025-08-08 11:40:50,558 - root - INFO - ============================================================
2025-08-08 11:40:50,558 - root - INFO - 🔧 第三步：创建SCAFL组件...
2025-08-08 11:40:56,699 - root - INFO - 📦 开始创建SCAFL组件...
2025-08-08 11:40:56,700 - root - INFO - [93m[1m[19404] Logging runtime results to: ./results/19404.csv.[0m
2025-08-08 11:40:56,700 - root - INFO - [Server #19404] Started training on 20 clients with 10 per round.
2025-08-08 11:40:56,700 - root - INFO - [SCAFL Server] 初始化完成
2025-08-08 11:40:56,701 - root - INFO - [SCAFL Server] 参数: tau_max=5, V=1.0, max_clients=5
2025-08-08 11:40:56,701 - root - INFO - ✅ SCAFL服务器创建成功
2025-08-08 11:40:56,701 - root - INFO - [SCAFL Client] 客户端None初始化完成
2025-08-08 11:40:56,701 - root - INFO - ✅ SCAFL客户端创建成功
2025-08-08 11:40:56,701 - root - INFO - 🎯 第四步：开始SCAFL训练...
2025-08-08 11:40:56,701 - root - INFO - 💡 提示：训练过程中会显示详细的SCAFL算法执行信息
2025-08-08 11:40:56,701 - root - INFO - 📝 日志文件：scafl_training.log
2025-08-08 11:40:56,701 - root - INFO - [Server #19404] Configuring the server...
2025-08-08 11:40:56,702 - root - INFO - Training: 20 rounds or accuracy above 90.0%

2025-08-08 11:40:56,702 - root - INFO - Trainer: basic
2025-08-08 11:40:56,703 - root - INFO - Algorithm: fedavg
2025-08-08 11:40:56,703 - root - INFO - Data source: MNIST
2025-08-08 11:40:56,731 - root - INFO - [SCAFL Server] 创建SCAFL算法实例
2025-08-08 11:40:56,731 - root - INFO - [SCAFL Algorithm] 初始化完成
2025-08-08 11:40:56,731 - root - INFO - [SCAFL Algorithm] 参数: tau_max=5, V=1.0
2025-08-08 11:40:56,731 - root - INFO - Starting client #1's process.
2025-08-08 11:40:57,027 - root - INFO - Starting client #2's process.
2025-08-08 11:40:57,320 - root - INFO - Starting client #3's process.
2025-08-08 11:40:57,597 - root - INFO - Starting a server at address 127.0.0.1 and port 8000.
2025-08-08 11:41:07,883 - aiohttp.access - INFO - 127.0.0.1 [08/Aug/2025:11:41:07 +0800] "GET /socket.io/?transport=polling&EIO=4&t=1754624467.8813183 HTTP/1.1" 200 320 "-" "Python/3.13 aiohttp/3.12.14"
2025-08-08 11:41:07,886 - root - INFO - [Server #19404] A new client just connected.
2025-08-08 11:41:07,887 - root - INFO - [Server #19404] New client with id #1 arrived.
2025-08-08 11:41:07,887 - root - INFO - [Server #19404] Client process #9620 registered.
2025-08-08 11:41:08,037 - aiohttp.access - INFO - 127.0.0.1 [08/Aug/2025:11:41:08 +0800] "GET /socket.io/?transport=polling&EIO=4&t=1754624468.0363228 HTTP/1.1" 200 320 "-" "Python/3.13 aiohttp/3.12.14"
2025-08-08 11:41:08,038 - root - INFO - [Server #19404] A new client just connected.
2025-08-08 11:41:08,039 - root - INFO - [Server #19404] New client with id #2 arrived.
2025-08-08 11:41:08,039 - root - INFO - [Server #19404] Client process #24008 registered.
2025-08-08 11:41:08,333 - aiohttp.access - INFO - 127.0.0.1 [08/Aug/2025:11:41:08 +0800] "GET /socket.io/?transport=polling&EIO=4&t=1754624468.332572 HTTP/1.1" 200 320 "-" "Python/3.13 aiohttp/3.12.14"
2025-08-08 11:41:08,334 - root - INFO - [Server #19404] A new client just connected.
2025-08-08 11:41:08,335 - root - INFO - [Server #19404] New client with id #3 arrived.
2025-08-08 11:41:08,335 - root - INFO - [Server #19404] Client process #18340 registered.
2025-08-08 11:41:08,335 - root - INFO - [Server #19404] Starting training.
2025-08-08 11:41:08,335 - root - INFO - [93m[1m
[Server #19404] Starting round 1/20.[0m
2025-08-08 11:41:08,336 - root - INFO - [Server #19404] Selected clients: [20, 3, 1, 8, 13, 7, 4, 16, 6, 12]
2025-08-08 11:41:08,336 - root - INFO - [Server #19404] Selecting client #20 for training.
2025-08-08 11:41:08,336 - root - INFO - [Server #19404] Sending the current model to client #20 (simulated).
2025-08-08 11:41:08,338 - root - INFO - [Server #19404] Sending 0.24 MB of payload data to client #20 (simulated).
2025-08-08 11:41:08,339 - root - INFO - [Server #19404] Selecting client #3 for training.
2025-08-08 11:41:08,339 - root - INFO - [Server #19404] Sending the current model to client #3 (simulated).
2025-08-08 11:41:08,342 - root - INFO - [Server #19404] Sending 0.24 MB of payload data to client #3 (simulated).
2025-08-08 11:41:08,342 - root - INFO - [Server #19404] Selecting client #1 for training.
2025-08-08 11:41:08,342 - root - INFO - [Server #19404] Sending the current model to client #1 (simulated).
2025-08-08 11:41:08,344 - root - INFO - [Server #19404] Sending 0.24 MB of payload data to client #1 (simulated).
2025-08-08 11:41:21,727 - root - INFO - [Server #19404] Received 0.24 MB of payload data from client #20 (simulated).
2025-08-08 11:41:21,743 - root - INFO - [Server #19404] Received 0.24 MB of payload data from client #3 (simulated).
2025-08-08 11:41:21,859 - root - INFO - [Server #19404] Received 0.24 MB of payload data from client #1 (simulated).
2025-08-08 11:41:21,859 - root - INFO - [Server #19404] Selecting client #8 for training.
2025-08-08 11:41:21,859 - root - INFO - [Server #19404] Sending the current model to client #8 (simulated).
2025-08-08 11:41:21,861 - root - INFO - [Server #19404] Sending 0.24 MB of payload data to client #8 (simulated).
2025-08-08 11:41:21,861 - root - INFO - [Server #19404] Selecting client #13 for training.
2025-08-08 11:41:21,861 - root - INFO - [Server #19404] Sending the current model to client #13 (simulated).
2025-08-08 11:41:21,865 - root - INFO - [Server #19404] Sending 0.24 MB of payload data to client #13 (simulated).
2025-08-08 11:41:21,866 - root - INFO - [Server #19404] Selecting client #7 for training.
2025-08-08 11:41:21,866 - root - INFO - [Server #19404] Sending the current model to client #7 (simulated).
2025-08-08 11:41:21,867 - root - INFO - [Server #19404] Sending 0.24 MB of payload data to client #7 (simulated).
2025-08-08 11:41:34,506 - root - INFO - [Server #19404] Received 0.24 MB of payload data from client #13 (simulated).
2025-08-08 11:41:34,515 - root - INFO - [Server #19404] Received 0.24 MB of payload data from client #8 (simulated).
2025-08-08 11:41:34,602 - root - INFO - [Server #19404] Received 0.24 MB of payload data from client #7 (simulated).
2025-08-08 11:41:34,603 - root - INFO - [Server #19404] Selecting client #4 for training.
2025-08-08 11:41:34,603 - root - INFO - [Server #19404] Sending the current model to client #4 (simulated).
2025-08-08 11:41:34,605 - root - INFO - [Server #19404] Sending 0.24 MB of payload data to client #4 (simulated).
2025-08-08 11:41:34,605 - root - INFO - [Server #19404] Selecting client #16 for training.
2025-08-08 11:41:34,605 - root - INFO - [Server #19404] Sending the current model to client #16 (simulated).
2025-08-08 11:41:34,607 - root - INFO - [Server #19404] Sending 0.24 MB of payload data to client #16 (simulated).
2025-08-08 11:41:34,608 - root - INFO - [Server #19404] Selecting client #6 for training.
2025-08-08 11:41:34,608 - root - INFO - [Server #19404] Sending the current model to client #6 (simulated).
2025-08-08 11:41:34,610 - root - INFO - [Server #19404] Sending 0.24 MB of payload data to client #6 (simulated).
2025-08-08 11:41:47,359 - root - INFO - [Server #19404] Received 0.24 MB of payload data from client #4 (simulated).
2025-08-08 11:41:47,609 - root - INFO - [Server #19404] Received 0.24 MB of payload data from client #16 (simulated).
2025-08-08 11:41:47,636 - root - INFO - [Server #19404] Received 0.24 MB of payload data from client #6 (simulated).
2025-08-08 11:41:47,636 - root - INFO - [Server #19404] Selecting client #12 for training.
2025-08-08 11:41:47,636 - root - INFO - [Server #19404] Sending the current model to client #12 (simulated).
2025-08-08 11:41:47,638 - root - INFO - [Server #19404] Sending 0.24 MB of payload data to client #12 (simulated).
2025-08-08 11:41:58,112 - root - INFO - [Server #19404] Received 0.24 MB of payload data from client #12 (simulated).
2025-08-08 11:41:58,112 - root - INFO - [Server #19404] Adding client #7 to the list of clients for aggregation.
2025-08-08 11:41:58,112 - root - INFO - [Server #19404] Aggregating 1 clients in total.
2025-08-08 11:41:58,112 - root - INFO - [Server #19404] Updated weights have been received.
2025-08-08 11:41:58,112 - root - INFO - [Server #19404] Aggregating model weights directly rather than weight deltas.
2025-08-08 11:41:58,112 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-74' coro=<AsyncServer._handle_event_internal() done, defined at D:\Develop\Miniconda\Lib\site-packages\socketio\async_server.py:610> exception=TypeError('Server.aggregate_weights() takes 2 positional arguments but 4 were given')>
Traceback (most recent call last):
  File "D:\Develop\Miniconda\Lib\site-packages\socketio\async_server.py", line 612, in _handle_event_internal
    r = await server._trigger_event(data[0], namespace, sid, *data[1:])
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Develop\Miniconda\Lib\site-packages\socketio\async_server.py", line 662, in _trigger_event
    return await handler.trigger_event(event, *args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Develop\Miniconda\Lib\site-packages\socketio\async_namespace.py", line 38, in trigger_event
    ret = await handler(*args)
          ^^^^^^^^^^^^^^^^^^^^
  File "d:\experiment\orgin-plato-main\plato-main\plato\servers\base.py", line 53, in on_client_report
    await self.plato_server._client_report_arrived(sid, data["id"], data["report"])
  File "d:\experiment\orgin-plato-main\plato-main\plato\servers\base.py", line 914, in _client_report_arrived
    await self.process_client_info(client_id, sid)
  File "d:\experiment\orgin-plato-main\plato-main\plato\servers\base.py", line 1023, in process_client_info
    await self._process_clients(client_info)
  File "d:\experiment\orgin-plato-main\plato-main\plato\servers\base.py", line 1195, in _process_clients
    await self._process_reports()
  File "d:\experiment\orgin-plato-main\plato-main\plato\servers\fedavg.py", line 181, in _process_reports
    updated_weights = await self.aggregate_weights(
                            ~~~~~~~~~~~~~~~~~~~~~~^
        self.updates, baseline_weights, weights_received
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
TypeError: Server.aggregate_weights() takes 2 positional arguments but 4 were given
2025-08-08 11:43:29,999 - root - INFO - [Server #19404] An existing client just disconnected.
2025-08-08 11:43:30,000 - root - WARNING - [Server #19404] Client process #9620 disconnected and removed from this server, 2 client processes are remaining.
2025-08-08 11:43:30,000 - root - WARNING - [93m[1m[Server #19404] Closing the server due to a failed client.[0m
2025-08-08 11:43:30,000 - root - INFO - [Server #19404] Training concluded.
2025-08-08 11:43:30,002 - root - INFO - [Server #19404] Model saved to ./models/scafl/tutorial/lenet5.pth.
2025-08-08 11:43:30,002 - root - INFO - Data source: MNIST
2025-08-08 11:43:30,030 - root - INFO - [Client #1] Test set sampler: noniid
2025-08-08 11:43:34,701 - root - INFO - [client 1] Test global accuracy: 7.3333%
2025-08-08 11:43:34,701 - root - INFO - Data source: MNIST
2025-08-08 11:43:34,731 - root - INFO - [Client #2] Test set sampler: noniid
2025-08-08 11:43:39,312 - root - INFO - [client 2] Test global accuracy: 14.6667%
2025-08-08 11:43:39,313 - root - INFO - Data source: MNIST
2025-08-08 11:43:39,342 - root - INFO - [Client #3] Test set sampler: noniid
2025-08-08 11:43:44,000 - root - INFO - [client 3] Test global accuracy: 20.0000%
2025-08-08 11:43:44,000 - root - INFO - Data source: MNIST
2025-08-08 11:43:44,028 - root - INFO - [Client #4] Test set sampler: noniid
2025-08-08 11:43:48,881 - root - INFO - [client 4] Test global accuracy: 10.6667%
2025-08-08 11:43:48,881 - root - INFO - Data source: MNIST
2025-08-08 11:43:48,911 - root - INFO - [Client #5] Test set sampler: noniid
2025-08-08 11:43:53,440 - root - INFO - [client 5] Test global accuracy: 1.0000%
2025-08-08 11:43:53,440 - root - INFO - Data source: MNIST
2025-08-08 11:43:53,469 - root - INFO - [Client #6] Test set sampler: noniid
2025-08-08 11:43:58,028 - root - INFO - [client 6] Test global accuracy: 8.0000%
2025-08-08 11:43:58,029 - root - INFO - Data source: MNIST
2025-08-08 11:43:58,059 - root - INFO - [Client #7] Test set sampler: noniid
2025-08-08 11:44:02,850 - root - INFO - [client 7] Test global accuracy: 2.0000%
2025-08-08 11:44:02,850 - root - INFO - Data source: MNIST
2025-08-08 11:44:02,877 - root - INFO - [Client #8] Test set sampler: noniid
2025-08-08 11:44:07,385 - root - INFO - [client 8] Test global accuracy: 4.3333%
2025-08-08 11:44:07,385 - root - INFO - Data source: MNIST
2025-08-08 11:44:07,412 - root - INFO - [Client #9] Test set sampler: noniid
2025-08-08 11:44:11,934 - root - INFO - [client 9] Test global accuracy: 30.6667%
2025-08-08 11:44:11,934 - root - INFO - Data source: MNIST
2025-08-08 11:44:11,967 - root - INFO - [Client #10] Test set sampler: noniid
2025-08-08 11:44:16,467 - root - INFO - [client 10] Test global accuracy: 18.3333%
2025-08-08 11:44:16,468 - root - INFO - Data source: MNIST
2025-08-08 11:44:16,496 - root - INFO - [Client #11] Test set sampler: noniid
2025-08-08 11:44:21,128 - root - INFO - [client 11] Test global accuracy: 1.6667%
2025-08-08 11:44:21,129 - root - INFO - Data source: MNIST
2025-08-08 11:44:21,160 - root - INFO - [Client #12] Test set sampler: noniid
2025-08-08 11:44:25,639 - root - INFO - [client 12] Test global accuracy: 3.3333%
2025-08-08 11:44:25,639 - root - INFO - Data source: MNIST
2025-08-08 11:44:25,670 - root - INFO - [Client #13] Test set sampler: noniid
2025-08-08 11:44:30,370 - root - INFO - [client 13] Test global accuracy: 2.0000%
2025-08-08 11:44:30,370 - root - INFO - Data source: MNIST
2025-08-08 11:44:30,403 - root - INFO - [Client #14] Test set sampler: noniid
2025-08-08 11:44:34,893 - root - INFO - [client 14] Test global accuracy: 17.0000%
2025-08-08 11:44:34,893 - root - INFO - Data source: MNIST
2025-08-08 11:44:34,920 - root - INFO - [Client #15] Test set sampler: noniid
2025-08-08 11:44:39,461 - root - INFO - [client 15] Test global accuracy: 5.3333%
2025-08-08 11:44:39,461 - root - INFO - Data source: MNIST
2025-08-08 11:44:39,492 - root - INFO - [Client #16] Test set sampler: noniid
2025-08-08 11:44:43,996 - root - INFO - [client 16] Test global accuracy: 15.3333%
2025-08-08 11:44:43,996 - root - INFO - Data source: MNIST
2025-08-08 11:44:44,025 - root - INFO - [Client #17] Test set sampler: noniid
2025-08-08 11:44:48,470 - root - INFO - [client 17] Test global accuracy: 1.0000%
2025-08-08 11:44:48,470 - root - INFO - Data source: MNIST
2025-08-08 11:44:48,498 - root - INFO - [Client #18] Test set sampler: noniid
2025-08-08 11:44:53,206 - root - INFO - [client 18] Test global accuracy: 69.6667%
2025-08-08 11:44:53,206 - root - INFO - Data source: MNIST
2025-08-08 11:44:53,235 - root - INFO - [Client #19] Test set sampler: noniid
2025-08-08 11:44:57,505 - root - INFO - [client 19] Test global accuracy: 28.3333%
2025-08-08 11:44:57,505 - root - INFO - Data source: MNIST
2025-08-08 11:44:57,535 - root - INFO - [Client #20] Test set sampler: noniid
2025-08-08 11:45:01,982 - root - INFO - [client 20] Test global accuracy: 0.3333%
2025-08-08 11:45:01,990 - root - INFO - [Server #19404] Closing the server.
2025-08-08 11:45:01,990 - root - INFO - Closing the connection to client #24008.
2025-08-08 11:45:01,991 - root - INFO - Closing the connection to client #18340.
2025-08-08 11:45:01,991 - root - INFO - [Server #19404] An existing client just disconnected.
2025-08-08 11:45:01,991 - root - WARNING - [Server #19404] Client process #24008 disconnected and removed from this server, 1 client processes are remaining.
2025-08-08 11:45:01,992 - root - WARNING - [93m[1m[Server #19404] Closing the server due to a failed client.[0m
2025-08-08 11:45:01,992 - root - INFO - [Server #19404] Training concluded.
2025-08-08 11:45:01,993 - root - INFO - [Server #19404] Model saved to ./models/scafl/tutorial/lenet5.pth.
2025-08-08 11:45:01,994 - root - INFO - Data source: MNIST
2025-08-08 11:45:02,022 - root - INFO - [Client #1] Test set sampler: noniid
2025-08-08 11:45:06,490 - root - INFO - [client 1] Test global accuracy: 7.3333%
2025-08-08 11:45:06,490 - root - INFO - Data source: MNIST
2025-08-08 11:45:06,520 - root - INFO - [Client #2] Test set sampler: noniid
2025-08-08 11:45:11,141 - root - INFO - [client 2] Test global accuracy: 14.6667%
2025-08-08 11:45:11,141 - root - INFO - Data source: MNIST
2025-08-08 11:45:11,170 - root - INFO - [Client #3] Test set sampler: noniid
2025-08-08 11:45:15,780 - root - INFO - [client 3] Test global accuracy: 20.0000%
2025-08-08 11:45:15,780 - root - INFO - Data source: MNIST
2025-08-08 11:45:15,809 - root - INFO - [Client #4] Test set sampler: noniid
2025-08-08 11:45:20,256 - root - INFO - [client 4] Test global accuracy: 10.6667%
2025-08-08 11:45:20,256 - root - INFO - Data source: MNIST
2025-08-08 11:45:20,285 - root - INFO - [Client #5] Test set sampler: noniid
2025-08-08 11:45:24,776 - root - INFO - [client 5] Test global accuracy: 1.0000%
2025-08-08 11:45:24,776 - root - INFO - Data source: MNIST
2025-08-08 11:45:24,804 - root - INFO - [Client #6] Test set sampler: noniid
2025-08-08 11:45:29,164 - root - INFO - [client 6] Test global accuracy: 8.0000%
2025-08-08 11:45:29,164 - root - INFO - Data source: MNIST
2025-08-08 11:45:29,192 - root - INFO - [Client #7] Test set sampler: noniid
2025-08-08 11:45:33,736 - root - INFO - [client 7] Test global accuracy: 2.0000%
2025-08-08 11:45:33,736 - root - INFO - Data source: MNIST
2025-08-08 11:45:33,764 - root - INFO - [Client #8] Test set sampler: noniid
2025-08-08 11:45:38,108 - root - INFO - [client 8] Test global accuracy: 4.3333%
2025-08-08 11:45:38,109 - root - INFO - Data source: MNIST
2025-08-08 11:45:38,136 - root - INFO - [Client #9] Test set sampler: noniid
2025-08-08 11:45:42,582 - root - INFO - [client 9] Test global accuracy: 30.6667%
2025-08-08 11:45:42,582 - root - INFO - Data source: MNIST
2025-08-08 11:45:42,610 - root - INFO - [Client #10] Test set sampler: noniid
2025-08-08 11:45:47,276 - root - INFO - [client 10] Test global accuracy: 18.3333%
2025-08-08 11:45:47,277 - root - INFO - Data source: MNIST
2025-08-08 11:45:47,306 - root - INFO - [Client #11] Test set sampler: noniid
2025-08-08 11:45:51,660 - root - INFO - [client 11] Test global accuracy: 1.6667%
2025-08-08 11:45:51,660 - root - INFO - Data source: MNIST
2025-08-08 11:45:51,688 - root - INFO - [Client #12] Test set sampler: noniid
2025-08-08 11:45:56,203 - root - INFO - [client 12] Test global accuracy: 3.3333%
2025-08-08 11:45:56,203 - root - INFO - Data source: MNIST
2025-08-08 11:45:56,233 - root - INFO - [Client #13] Test set sampler: noniid
2025-08-08 11:46:00,624 - root - INFO - [client 13] Test global accuracy: 2.0000%
2025-08-08 11:46:00,625 - root - INFO - Data source: MNIST
2025-08-08 11:46:00,654 - root - INFO - [Client #14] Test set sampler: noniid
2025-08-08 11:46:05,057 - root - INFO - [client 14] Test global accuracy: 17.0000%
2025-08-08 11:46:05,058 - root - INFO - Data source: MNIST
2025-08-08 11:46:05,085 - root - INFO - [Client #15] Test set sampler: noniid
2025-08-08 11:46:09,488 - root - INFO - [client 15] Test global accuracy: 5.3333%
2025-08-08 11:46:09,488 - root - INFO - Data source: MNIST
2025-08-08 11:46:09,518 - root - INFO - [Client #16] Test set sampler: noniid
2025-08-08 11:46:13,950 - root - INFO - [client 16] Test global accuracy: 15.3333%
2025-08-08 11:46:13,950 - root - INFO - Data source: MNIST
2025-08-08 11:46:13,977 - root - INFO - [Client #17] Test set sampler: noniid
2025-08-08 11:46:18,453 - root - INFO - [client 17] Test global accuracy: 1.0000%
2025-08-08 11:46:18,454 - root - INFO - Data source: MNIST
2025-08-08 11:46:18,480 - root - INFO - [Client #18] Test set sampler: noniid
2025-08-08 11:46:22,964 - root - INFO - [client 18] Test global accuracy: 69.6667%
2025-08-08 11:46:22,964 - root - INFO - Data source: MNIST
2025-08-08 11:46:22,995 - root - INFO - [Client #19] Test set sampler: noniid
2025-08-08 11:46:27,455 - root - INFO - [client 19] Test global accuracy: 28.3333%
2025-08-08 11:46:27,456 - root - INFO - Data source: MNIST
2025-08-08 11:46:27,483 - root - INFO - [Client #20] Test set sampler: noniid
2025-08-08 11:46:31,903 - root - INFO - [client 20] Test global accuracy: 0.3333%
2025-08-08 11:46:31,907 - root - INFO - [Server #19404] Closing the server.
2025-08-08 11:46:31,907 - root - INFO - Closing the connection to client #18340.
2025-08-08 11:46:31,908 - aiohttp.access - INFO - 127.0.0.1 [08/Aug/2025:11:41:07 +0800] "GET /socket.io/?transport=websocket&EIO=4&sid=U4TIFGa18pb1uFLJAAAA&t=1754624467.8840828 HTTP/1.1" 101 0 "-" "Python/3.13 aiohttp/3.12.14"
2025-08-08 11:46:31,908 - aiohttp.access - INFO - 127.0.0.1 [08/Aug/2025:11:41:08 +0800] "GET /socket.io/?transport=websocket&EIO=4&sid=JU5vScP2qzC8Qs_EAAAC&t=1754624468.03775 HTTP/1.1" 101 0 "-" "Python/3.13 aiohttp/3.12.14"
2025-08-08 11:46:31,909 - root - INFO - 🎉 SCAFL训练完成！
2025-08-08 11:46:31,909 - root - INFO - 📈 请查看日志文件了解详细的训练过程
2025-08-08 11:46:31,909 - root - INFO - 🔚 SCAFL程序结束
