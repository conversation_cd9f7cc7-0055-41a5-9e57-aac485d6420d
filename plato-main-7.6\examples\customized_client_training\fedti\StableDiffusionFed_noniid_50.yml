clients:

    # The total number of clients
    total_clients: 50

    # The number of clients selected in each round
    per_round: 5

    # Should the clients compute test accuracy locally?
    do_test: false

server:

    type: fedavg_personalized
    address: 127.0.0.1
    port: 8014

    do_test: false

data:
    # The training and testing dataset
    datasource: Flowers102OneClass

    # IID or non-IID?
    sampler: sample_quantity_noniid

    min_partition_size: 1
    client_quantity_concentration: 0.3

    # The random seed for sampling data
    random_seed: 1

trainer:

    # The maximum number of training rounds
    rounds: 30

    # The maximum number of clients running concurrently
    max_concurrency: 2

    # The target accuracy
    target_accuracy: 0.94

    # Number of epoches for local training in each communication round
    epochs: 2
    batch_size: 2
    gradient_accumulation_steps: 1
    repeats: 100
    gradient_checkpointing: true
    mixed_precision: fp16
    learning_rate: 5.0E-4

    model_name: generation_prompt


algorithm:
    # Aggregation algorithm
    type: fedavg_personalized
        
    target_class: clematis
    what_to_teach: object
    concept_name: IClematis
    initializer_token: flower

    personalization:

        model_type: stabilityai
        model_name: stable-diffusion-2


parameters:
    optimizer:
        lr: 0.03
        momentum: 0.9
        weight_decay: 0.0

    learning_rate:
        gamma: 0.1
        milestone_steps: 150ep,350ep

    loss_criterion:
        label_smoothing: 0.5

    model:
        prompt_length: 4


    personalization:

        model:
            prompt_length: 4