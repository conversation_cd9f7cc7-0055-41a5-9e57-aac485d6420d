clients:
    type: simple
    total_clients: 100
    per_round: 20
    do_test: false
    speed_simulation: true
    simulation_distribution:
        distribution: pareto
        alpha: 1
    max_sleep_time: 30
    sleep_simulation: true
    avg_training_time: 10
    random_seed: 1

server:
    address: 127.0.0.1
    port: 8000
    synchronous: false
    simulate_wall_time: true
    
    # SC-AFL 专属参数
    tau_max: 10        # 最大陈旧度阈值
    V: 10             # Lyapunov权衡参数
    max_aggregation_clients: 8  # 每轮最多聚合的客户端数量

data:
    datasource: MNIST
    partition_size: 600
    sampler: noniid
    concentration: 0.1
    random_seed: 1

trainer:
    type: basic
    rounds: 100
    max_concurrency: 2
    target_accuracy: 0.95
    model_name: lenet5
    epochs: 1
    batch_size: 32
    optimizer: SGD
    learning_rate: 0.01

algorithm:
    type: fedavg

parameters:
    model:
        num_classes: 10
    optimizer:
        type: SGD
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001
