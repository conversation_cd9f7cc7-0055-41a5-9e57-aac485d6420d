"""
Base class for trainers.
"""

from abc import ABC, abstractmethod
import os

from plato.config import Config


class Trainer(ABC):
    """Base class for all the trainers."""
    # 中文注释：
    # Trainer 类是所有训练器的基类。
    # 这个类定义了训练器的基本结构和接口，包括模型的初始化、训练和测试等方法。
    # 子类需要实现这些方法，以实现特定的训练逻辑。

    def __init__(self):
        self.device = Config().device()
        self.client_id = 0

#  中文注释：设置客户端ID
    def set_client_id(self, client_id):
        """Setting the client ID."""
        self.client_id = client_id
#  中文注释：保存模型到文件
    @abstractmethod
    def save_model(self, filename=None, location=None):
        """Saving the model to a file."""
        raise TypeError("save_model() not implemented.")
# 中文注释：加载预训练模型权重
    @abstractmethod
    def load_model(self, filename=None, location=None):
        """Loading pre-trained model weights from a file."""
        raise TypeError("load_model() not implemented.")
# 中文注释：保存测试准确率到文件
    @staticmethod
    def save_accuracy(accuracy, filename=None):
        """Saving the test accuracy to a file."""
        model_path = Config().params["model_path"]
        model_name = Config().trainer.model_name

        if not os.path.exists(model_path):
            os.makedirs(model_path)

        if filename is not None:
            accuracy_path = f"{model_path}/{filename}"
        else:
            accuracy_path = f"{model_path}/{model_name}.acc"

        with open(accuracy_path, "w", encoding="utf-8") as file:
            file.write(str(accuracy))
#  中文注释：加载测试准确率
    @staticmethod
    def load_accuracy(filename=None):
        """Loading the test accuracy from a file."""
        model_path = Config().params["model_path"]
        model_name = Config().trainer.model_name

        if filename is not None:
            accuracy_path = f"{model_path}/{filename}"
        else:
            accuracy_path = f"{model_path}/{model_name}.acc"

        with open(accuracy_path, "r", encoding="utf-8") as file:
            accuracy = float(file.read())

        return accuracy
# 中文注释：暂停训练
    def pause_training(self):
        """Remove files of running trainers."""
        # 这个方法用于暂停训练，删除正在运行的训练器的文件。
        # 具体来说，它会检查是否设置了 Config().trainer.max_concurrency 参数，
        # 如果设置了，它会删除指定模型名称、客户端ID和运行ID的模型文件和准确率文件。
        # 如果文件存在，它会使用 os.remove() 方法删除文件。

        if hasattr(Config().trainer, "max_concurrency"):
            model_name = Config().trainer.model_name
            model_path = Config().params["model_path"]
            model_file = f"{model_path}/{model_name}_{self.client_id}_{Config().params['run_id']}.pth"
            accuracy_file = f"{model_path}/{model_name}_{self.client_id}_{Config().params['run_id']}.acc"

            if os.path.exists(model_file):
                os.remove(model_file)
                os.remove(model_file + ".pkl")

            if os.path.exists(accuracy_file):
                os.remove(accuracy_file)
#  中文注释：抽象方法，子类必须实现该方法，用于训练模型。
    @abstractmethod
    def train(self, trainset, sampler, **kwargs) -> float:
        """The main training loop in a federated learning workload.

        Arguments:
        trainset: The training dataset.
        sampler: the sampler that extracts a partition for this client.

        Returns:
        float: The training time.
        """
        # 该方法接受训练数据集和采样器作为参数，返回训练时间。
        # 具体的训练逻辑由子类实现。
#  中文注释：抽象方法，子类必须实现该方法，用于测试模型。
    @abstractmethod
    def test(self, testset, sampler=None, **kwargs) -> float:
        """Testing the model using the provided test dataset.

        Arguments:
        testset: The test dataset.
        sampler: The sampler that extracts a partition of the test dataset.
        """
        # 该方法接受测试数据集和采样器作为参数，返回测试时间。
        # 具体的测试逻辑由子类实现。
        
