general:

    # performing different running mode
    # for different purposes
    # - central_code_test
    # - central
    # - script
    # - code_test
    # - user

    running_mode: user

    file_logging: True

clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 5

    do_data_tranform_logging: True

    # Should the clients compute test accuracy locally?
    do_test: True

    # Should the client only perfrom the eval test without training
    # under the contrastive ssl.
    # only perfrom the training and test for the downstream task,
    # i.e., personalziation in each client
    only_personalization: False

    # whether perform the eval test at the final round
    # on all clients
    do_final_personalization: True
    # Minotor the self-supervised learning by knn for
    # every 10 round
    # test_interval: 10
    # Train the personalized model based on the
    # learned representation every 10 rounds
    pers_learning_interval: 10

    # Whether simulate clients or not
    simulation: true

    random_seed: 1

    comm_simulation: true 
    compute_comm_time: true

server:
    address: 127.0.0.1
    port: 8022
    synchronous: true
    do_test: false

    ping_interval: 12000
    ping_timeout: 12000

    #model_path: ../plato_models/fedtp/cifar10_noniid/vit
    #checkpoint_path: ../plato_models/fedtp/cifar10_noniid/vit

    simulate_wall_time: true

data:
    # The training and testing dataset
    datasource: CIFAR10
    #data_path: ../data
    # Number of samples in each partition
    #
    # This is one important parameter that
    # may lead the failure of the loss criterion part
    # if your loss criterion relies on the batch_size
    # as its argument. For example, the NT_Xent loss utilized
    # by the SimCLR method set the defined batch_size as the parameter.
    # However, at the end of one epoch, the left samples may smaller than
    # the batch_size. This makes the #loaded samples != batch_size.
    # Working on criterion that is defined with batch_size but receives loaded
    # samples whose size is smaller than the batch size may causes problems.
    # drop_last can alleviate this issue
    # partition_size: 50000
    # test_partition_size: 10000
    partition_size: 600
    test_partition_size: 600

    # IID or non-IID?
    sampler: noniid
    testset_sampler: noniid

    # The concentration parameter for the Dirichlet distribution
    concentration: 0.3

    # The random seed for sampling data
    random_seed: 1


trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 1500

    # Whether the training should use multiple GPUs if available
    parallelized: false

    # The maximum number of clients running concurrently
    max_concurrency: 2

    # The target accuracy
    target_accuracy: 0.99

    # Number of epochs for local training in each communication round
    #   The user is expected to carefully choose the batch_size to avoid
    # the memory allocation issue as the contrastive samples occupy more
    # space as more samples will be contained within one loading.
    epochs: 5
    batch_size: 32
    optimizer: SGD
    #SGD

    epoch_log_interval: 1
    epoch_model_log_interval: 50
    batch_log_interval: 50

    # Number of epochs for local training in each communication round
    #   The batch size for the downstream task can be larger as it
    # does not utilize the
    pers_epochs: 10
    pers_batch_size: 32
    pers_optimizer: SGD

    pers_epoch_log_interval: 2
    pers_epoch_model_log_interval: 10

    # The machine learning model,
    # it behaves as the encoder for the ssl method
    # the final fc layer will be removed
    # however, in the central test, we do not use this
    #   but use the custom model
    model_type: vit
    model_name: google@vit-base-patch16-224 
    personalized_model_name: google@vit-base-patch16-224 

    # encoder, whole
    global_submodules_prefix: whole
    # whether to maintain the state of the trained personalized
    # model in each round
    # In general, for the contrastive self-supervised learning
    # every eval test should be a new learning process to train
    # the randomly initialized personalized model
    # Thus, setting do_maintain_per_state to be false to follow
    # this rule.
    # However, in some cases, we want to maintain the state of the
    # trained personalized model in each round.
    # Then, setting do_maintain_per_state to be True to follow this
    # rule.
    do_maintain_per_state: False

    num_classes: 10

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.00001

    pers_optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.00001
    
    hypernet:
        attention: model.vit.encoder.layer.%d.attention.attention.key.weight,model.vit.encoder.layer.%d.attention.attention.query.weight,model.vit.encoder.layer.%d.attention.attention.value.weight
        depth: 6
        embed_dim: 128
        hidden_dim: 100
        dim: 128
        num_heads: 8
        dim_head: 16

    model:
        image_size: 32
        patch_size: 4
        num_classes: 10 
        hidden_size: 128
        num_hidden_layers: 6
        num_attention_heads: 8
        intermediate_size: 256
        attention_probs_dropout_prob: 0.1
        hidden_dropout_prob: 0.1
        num_labels: 10
        pretrained: false
        ignore_mismatched_sizes: true
    #architect:
    #    pretrain_path: ../pretrained/ML_Models/VIT/T2T-VIT/T2T_ViT_14.pth.tar

results:
    #result_path: ../plato_results/fedtp/cifar10_noniid/vit
    types: round, accuracy, elapsed_time, comm_time, round_time, comm_overhead
