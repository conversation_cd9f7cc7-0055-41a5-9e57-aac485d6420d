# 联邦学习算法对比实验 - EMNIST + LeNet5

本目录包含了对比6种异步联邦学习算法在EMNIST数据集上使用LeNet5模型的完整实验框架。

## 对比算法

1. **RefedSCAFL** - 基于声誉的SCAFL算法，结合知识蒸馏补偿机制
2. **SCAFL** - 陈旧度感知的客户端自适应联邦学习算法  
3. **FADAS** - 联邦自适应异步优化算法，使用AMSGrad优化器
4. **FedAC** - 异步联邦学习算法，具有前瞻性动量聚合
5. **FedBuff** - 缓冲异步聚合的联邦学习算法
6. **FedAsync** - 经典异步联邦学习算法

## 实验配置

- **数据集**: EMNIST (47类字符识别)
- **模型**: LeNet5
- **数据分布**: Non-IID (Dirichlet分布，α=0.1)
- **客户端数量**: 100个客户端
- **每轮选择**: 20个客户端
- **训练轮次**: 各算法根据收敛情况设定

### 网络波动模拟

所有算法都配置了统一的网络波动模拟，以更真实地反映联邦学习的实际环境：

- **网络延迟**: 50ms - 2000ms (指数分布)
- **丢包率**: 15% (包含突发丢包)
- **带宽限制**: 上传1MB/s, 下载2MB/s
- **网络模拟**: 启用真实网络条件模拟

> 📖 详细的网络配置说明请参考 [NETWORK_CONFIGURATION.md](NETWORK_CONFIGURATION.md)

## 文件结构

```
examples/async/
├── README_EMNIST_Comparison.md     # 本说明文件
├── NETWORK_CONFIGURATION.md       # 网络波动配置说明
├── verify_config_metrics.py       # 配置文件指标验证工具
├── quick_test_emnist.py            # 快速测试脚本
├── run_emnist_comparison.py        # 完整对比实验脚本
├── analyze_emnist_results.py       # 结果分析脚本
├── comparison_results/             # 实验结果目录
│   ├── analysis/                   # 分析结果
│   └── *.csv                      # 各算法结果文件
├── fadas/
│   └── fadas_EMNIST_lenet5_alpha0.1.yml
├── fedac/
│   └── fedac_EMNIST_lenet5_alpha0.1.yml
├── fedbuff/
│   └── fedbuff_EMNIST_lenet5_alpha0.1.yml
├── refedscafl/
│   └── refedscafl_EMNIST_lenet5_optimized.yml
├── fedasync/
│   └── fedasync_EMNIST_lenet5_alpha0.1.yml
└── SC_AFL/
    └── sc_afl_emnist_lenet5_with_network.yml
```

## 使用方法

### 1. 快速测试（推荐先运行）

验证所有配置文件是否正确：

```bash
cd plato-main-7.6/examples/async
python quick_test_emnist.py
```

这个脚本会：
- 检查所有配置文件是否存在
- 修改配置为快速测试模式（3轮，5个客户端）
- 运行每个算法进行验证
- 自动恢复原配置文件

### 2. 完整对比实验

运行所有算法的完整对比实验：

```bash
cd plato-main-7.6/examples/async
python run_emnist_comparison.py
```

这个脚本会：
- 逐个运行所有6种算法
- 自动收集实验结果
- 生成实验日志和摘要
- 将结果保存到 `comparison_results/` 目录

### 3. 结果分析

分析实验结果并生成可视化图表：

```bash
cd plato-main-7.6/examples/async
python analyze_emnist_results.py
```

这个脚本会生成：
- 准确率对比曲线图
- 收敛性分析图表
- 性能摘要表格
- 详细分析报告

### 4. 运行单个算法

如果只想运行特定算法：

```bash
# 运行FADAS
cd plato-main-7.6/examples/async/fadas
python fadas.py -c fadas_EMNIST_lenet5_alpha0.1.yml

# 运行FedAC
cd plato-main-7.6/examples/async/fedac
python fedac.py -c fedac_EMNIST_lenet5_alpha0.1.yml

# 其他算法类似...
```

## 配置文件说明

所有EMNIST配置文件都基于以下统一设置：

```yaml
data:
    datasource: EMNIST
    partition_size: 1128        # 每个客户端的样本数
    sampler: noniid            # 非独立同分布
    concentration: 0.1         # Dirichlet分布参数

parameters:
    model:
        num_classes: 47        # EMNIST有47个类别
        in_channels: 1         # 灰度图像
```

各算法的特定参数保持其原有设计。

## 实验结果

实验完成后，结果将保存在以下位置：

- **原始结果**: `comparison_results/*.csv`
- **分析图表**: `comparison_results/analysis/*.png`
- **性能摘要**: `comparison_results/analysis/performance_summary_*.csv`
- **分析报告**: `comparison_results/analysis/analysis_report_*.md`

## 评估指标

所有算法统一记录以下指标，确保公平对比：

### 核心指标
- `round` - 训练轮次
- `elapsed_time` - 累计运行时间
- `accuracy` - 本地测试准确率
- `global_accuracy` - 全局模型准确率
- `global_accuracy_std` - 全局准确率标准差

### 异步特有指标
- `avg_staleness` - 平均陈旧度
- `max_staleness` - 最大陈旧度
- `min_staleness` - 最小陈旧度

### 网络波动指标
- `network_latency` - 网络延迟 (ms)
- `network_bandwidth` - 网络带宽 (MB/s)
- `network_reliability` - 网络可靠性
- `network_success_rate` - 网络成功率

### 分析维度

1. **准确率指标**
   - 最终本地准确率
   - 最终全局准确率
   - 最高准确率
   - 平均准确率

2. **收敛性指标**
   - 收敛速度（达到90%准确率的轮次）
   - 总训练轮次

3. **稳定性指标**
   - 准确率标准差
   - 最后10轮的稳定性

4. **异步性能指标**
   - 平均陈旧度
   - 陈旧度变化范围
   - 陈旧度对准确率的影响

5. **网络性能指标**
   - 网络延迟对训练的影响
   - 网络带宽利用率
   - 网络可靠性和成功率
   - 网络波动对收敛性的影响

## 注意事项

1. **端口冲突**: 各算法使用不同端口，避免同时运行
2. **资源需求**: 完整实验可能需要较长时间，建议先运行快速测试
3. **依赖检查**: 确保已安装所有必要的Python包
4. **数据下载**: 首次运行会自动下载EMNIST数据集

## 故障排除

### 常见问题

1. **配置文件不存在**
   ```bash
   # 检查配置文件是否在正确位置
   python quick_test_emnist.py
   ```

2. **端口被占用**
   ```bash
   # 检查端口使用情况
   netstat -an | grep 800[2-9]
   ```

3. **内存不足**
   - 减少客户端数量或并发数
   - 修改配置文件中的 `total_clients` 和 `max_concurrency`

4. **CUDA相关错误**
   - 确保PyTorch CUDA版本匹配
   - 或在配置中禁用GPU使用

### 获取帮助

如果遇到问题，请检查：
1. 运行日志文件 (`comparison_results/*.log`)
2. 各算法目录下的错误输出
3. 确保Plato框架正确安装

## 扩展实验

可以通过修改配置文件进行扩展实验：

1. **不同数据分布**: 修改 `concentration` 参数
2. **不同客户端数量**: 修改 `total_clients` 和 `per_round`
3. **不同模型**: 将 `model_name` 改为其他支持的模型
4. **不同数据集**: 将 `datasource` 改为 MNIST、CIFAR-10 等

---

**实验目标**: 通过统一的实验设置，公平对比各种异步联邦学习算法在EMNIST字符识别任务上的性能表现。
