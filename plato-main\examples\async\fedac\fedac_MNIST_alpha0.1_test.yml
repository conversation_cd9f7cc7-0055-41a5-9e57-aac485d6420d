clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 5

    # The number of clients selected in each round
    per_round: 3

    # *Should the clients compute test accuracy locally?(test local model on local dataset)
    do_test: true

    # *Should the clients compute test accuracy with global model?(test global model on local dataset)
    do_global_test: true

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 5

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 5

    random_seed: 1

    # (fedasync) the hyperparameter for regularizer
    proximal_term_penalty_constant: 0.005

server:
    address: 127.0.0.1
    port: 8004
    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server? Useful if max_concurrency is specified
    simulate_wall_time: true

    # (fedbuff)
    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 2

    # (FedAsync)
    # staleness_bound: 1000 # FedAsync doesn't have any staleness bound
    # minimum_clients_aggregated: 1
    mixing_hyperparameter: 0.9
    adaptive_mixing: true
    staleness_weighting_function:
        type: Polynomial
        a: 2

    # (ApFL-HD)the function to compute the weight discounter (e.g., div1, add1, div2, add2)
    weight_discounter_func: div1
    # the weight for staleness
    staleness_weight: 1.0
    # tradeoff between history global distribution and current distribution
    global_dist_beta: 0.01

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 1000

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: false

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/mnist/01
    model_path: models/mnist/01

    random_seed: 1

    # (FedAC)
    fedac_beta1: 0.9
    fedac_beta2: 0.99
    fedac_eps: 0.00000001
    fedac_global_lr: 0.01

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 300

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution(alpha)
    concentration: 0.1

    # The size of the testset on the server
    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

    # *get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 2

    # The maximum number of clients running concurrently
    max_concurrency: 1

    # The target accuracy
    target_accuracy: 1

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 50
    optimizer: SGD
    lr_scheduler: LambdaLR

    # The machine learning model
    model_name: lenet5

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001
    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    result_path: results/mnist/01

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std
