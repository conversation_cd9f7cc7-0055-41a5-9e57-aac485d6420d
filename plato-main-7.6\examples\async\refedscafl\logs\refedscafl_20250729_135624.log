[INFO][13:56:24]: 日志系统已初始化
[INFO][13:56:24]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_135624.log
[INFO][13:56:24]: 日志级别: INFO
[WARNING][13:56:24]: 无法获取系统信息: No module named 'psutil'
[INFO][13:56:24]: 🚀 ReFedScaFL 训练开始
[INFO][13:56:24]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][13:56:24]: 开始时间: 2025-07-29 13:56:24
[INFO][13:56:24]: [Client None] 基础初始化完成
[INFO][13:56:24]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][13:56:24]: 创建并缓存共享模型
[INFO][13:56:24]: [93m[1m[20252] Logging runtime results to: ./results/refedscafl/cifar10_alpha01/20252.csv.[0m
[INFO][13:56:24]: [Server #20252] Started training on 100 clients with 20 per round.
[INFO][13:56:24]: 服务器参数配置完成：
[INFO][13:56:24]: - 客户端数量: total=100, per_round=20
[INFO][13:56:24]: - 权重参数: success=0.8, distill=0.2
[INFO][13:56:24]: - SCAFL参数: V=1.0, tau_max=5
[INFO][13:56:24]: 从共享资源模型提取并缓存全局权重
[INFO][13:56:24]: [Server #20252] Configuring the server...
[INFO][13:56:24]: Training: 400 rounds or accuracy above 100.0%

[INFO][13:56:24]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][13:56:24]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][13:56:24]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][13:56:24]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][13:56:24]: Algorithm: fedavg
[INFO][13:56:24]: Data source: CIFAR10
[INFO][13:56:26]: Starting client #1's process.
[INFO][13:56:26]: Starting client #2's process.
[INFO][13:56:26]: Starting client #3's process.
[INFO][13:56:26]: Starting client #4's process.
[INFO][13:56:26]: Starting client #5's process.
[INFO][13:56:26]: Starting client #6's process.
[INFO][13:56:26]: Starting client #7's process.
[INFO][13:56:26]: Starting client #8's process.
[INFO][13:56:26]: Starting client #9's process.
[INFO][13:56:26]: Starting client #10's process.
[INFO][13:56:26]: Setting the random seed for selecting clients: 1
[INFO][13:56:26]: Starting a server at address 127.0.0.1 and port 8095.
[INFO][13:56:42]: [Server #20252] A new client just connected.
[INFO][13:56:42]: [Server #20252] New client with id #4 arrived.
[INFO][13:56:42]: [Server #20252] Client process #16024 registered.
[INFO][13:56:42]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][13:56:43]: [Server #20252] A new client just connected.
[INFO][13:56:43]: [Server #20252] New client with id #1 arrived.
[INFO][13:56:43]: [Server #20252] Client process #16388 registered.
[INFO][13:56:43]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][13:56:43]: [Server #20252] A new client just connected.
[INFO][13:56:43]: [Server #20252] New client with id #10 arrived.
[INFO][13:56:43]: [Server #20252] Client process #35872 registered.
[INFO][13:56:43]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][13:56:43]: [Server #20252] A new client just connected.
[INFO][13:56:43]: [Server #20252] New client with id #2 arrived.
[INFO][13:56:43]: [Server #20252] Client process #15828 registered.
[INFO][13:56:43]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][13:56:43]: [Server #20252] A new client just connected.
[INFO][13:56:43]: [Server #20252] New client with id #5 arrived.
[INFO][13:56:43]: [Server #20252] Client process #10704 registered.
[INFO][13:56:43]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][13:56:43]: [Server #20252] A new client just connected.
[INFO][13:56:43]: [Server #20252] New client with id #9 arrived.
[INFO][13:56:43]: [Server #20252] Client process #20124 registered.
[INFO][13:56:43]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][13:56:43]: [Server #20252] A new client just connected.
[INFO][13:56:43]: [Server #20252] A new client just connected.
[INFO][13:56:43]: [Server #20252] New client with id #7 arrived.
[INFO][13:56:43]: [Server #20252] Client process #22844 registered.
[INFO][13:56:43]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][13:56:43]: [Server #20252] New client with id #6 arrived.
[INFO][13:56:43]: [Server #20252] Client process #38688 registered.
[INFO][13:56:43]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][13:56:43]: [Server #20252] A new client just connected.
[INFO][13:56:43]: [Server #20252] New client with id #8 arrived.
[INFO][13:56:43]: [Server #20252] Client process #568 registered.
[INFO][13:56:43]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][13:56:43]: [Server #20252] A new client just connected.
[INFO][13:56:43]: [Server #20252] New client with id #3 arrived.
[INFO][13:56:43]: [Server #20252] Client process #16184 registered.
[INFO][13:56:43]: [Server #20252] Starting training.
[INFO][13:56:43]: [93m[1m
[Server #20252] Starting round 1/400.[0m
[INFO][13:56:43]: [Server #20252] Selected clients: [18, 73, 98, 9, 33, 16, 64, 58, 61, 84, 49, 27, 13, 63, 4, 50, 56, 78, 99, 1]
[INFO][13:56:43]: [Server #20252] Selecting client #18 for training.
[INFO][13:56:43]: [Server #20252] Sending the current model to client #18 (simulated).
[INFO][13:56:43]: [Server #20252] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][13:56:43]: [Server #20252] Selecting client #73 for training.
[INFO][13:56:43]: [Server #20252] Sending the current model to client #73 (simulated).
[INFO][13:56:43]: [Server #20252] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][13:56:43]: [Server #20252] Selecting client #98 for training.
[INFO][13:56:43]: [Server #20252] Sending the current model to client #98 (simulated).
[INFO][13:56:43]: [Server #20252] Sending 18.75 MB of payload data to client #98 (simulated).
[INFO][13:56:43]: [Server #20252] Selecting client #9 for training.
[INFO][13:56:43]: [Server #20252] Sending the current model to client #9 (simulated).
[INFO][13:56:43]: [Server #20252] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][13:56:43]: [Server #20252] Selecting client #33 for training.
[INFO][13:56:43]: [Server #20252] Sending the current model to client #33 (simulated).
[INFO][13:56:43]: [Server #20252] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][13:56:43]: [Server #20252] Selecting client #16 for training.
[INFO][13:56:43]: [Server #20252] Sending the current model to client #16 (simulated).
[INFO][13:56:43]: [Server #20252] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][13:56:43]: [Server #20252] Selecting client #64 for training.
[INFO][13:56:43]: [Server #20252] Sending the current model to client #64 (simulated).
[INFO][13:56:43]: [Server #20252] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][13:56:43]: [Server #20252] Selecting client #58 for training.
[INFO][13:56:43]: [Server #20252] Sending the current model to client #58 (simulated).
[INFO][13:56:43]: [Server #20252] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][13:56:43]: [Server #20252] Selecting client #61 for training.
[INFO][13:56:43]: [Server #20252] Sending the current model to client #61 (simulated).
[INFO][13:56:43]: [Server #20252] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][13:56:43]: [Server #20252] Selecting client #84 for training.
[INFO][13:56:43]: [Server #20252] Sending the current model to client #84 (simulated).
[INFO][13:56:43]: [Server #20252] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][13:56:43]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][13:59:41]: [Server #20252] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][13:59:45]: [Server #20252] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][13:59:45]: [Server #20252] Received 18.75 MB of payload data from client #98 (simulated).
[INFO][13:59:47]: [Server #20252] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][13:59:47]: [Server #20252] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][13:59:47]: [Server #20252] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][13:59:47]: [Server #20252] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][13:59:47]: [Server #20252] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][13:59:47]: [Server #20252] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][13:59:48]: [Server #20252] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][13:59:48]: [Server #20252] Selecting client #49 for training.
[INFO][13:59:48]: [Server #20252] Sending the current model to client #49 (simulated).
[INFO][13:59:48]: [Server #20252] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][13:59:48]: [Server #20252] Selecting client #27 for training.
[INFO][13:59:48]: [Server #20252] Sending the current model to client #27 (simulated).
[INFO][13:59:48]: [Server #20252] Sending 18.75 MB of payload data to client #27 (simulated).
[INFO][13:59:48]: [Server #20252] Selecting client #13 for training.
[INFO][13:59:48]: [Server #20252] Sending the current model to client #13 (simulated).
[INFO][13:59:48]: [Server #20252] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][13:59:48]: [Server #20252] Selecting client #63 for training.
[INFO][13:59:48]: [Server #20252] Sending the current model to client #63 (simulated).
[INFO][13:59:48]: [Server #20252] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][13:59:48]: [Server #20252] Selecting client #4 for training.
[INFO][13:59:48]: [Server #20252] Sending the current model to client #4 (simulated).
[INFO][13:59:49]: [Server #20252] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][13:59:49]: [Server #20252] Selecting client #50 for training.
[INFO][13:59:49]: [Server #20252] Sending the current model to client #50 (simulated).
[INFO][13:59:49]: [Server #20252] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][13:59:49]: [Server #20252] Selecting client #56 for training.
[INFO][13:59:49]: [Server #20252] Sending the current model to client #56 (simulated).
[INFO][13:59:50]: [Server #20252] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][13:59:50]: [Server #20252] Selecting client #78 for training.
[INFO][13:59:50]: [Server #20252] Sending the current model to client #78 (simulated).
[INFO][13:59:50]: [Server #20252] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][13:59:50]: [Server #20252] Selecting client #99 for training.
[INFO][13:59:50]: [Server #20252] Sending the current model to client #99 (simulated).
[INFO][13:59:51]: [Server #20252] Sending 18.75 MB of payload data to client #99 (simulated).
[INFO][13:59:51]: [Server #20252] Selecting client #1 for training.
[INFO][13:59:51]: [Server #20252] Sending the current model to client #1 (simulated).
[INFO][13:59:51]: [Server #20252] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][14:00:11]: [Server #20252] An existing client just disconnected.
[WARNING][14:00:11]: [Server #20252] Client process #35872 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][14:00:11]: [93m[1m[Server #20252] Closing the server due to a failed client.[0m
[INFO][14:00:11]: [Server #20252] Training concluded.
[INFO][14:00:12]: [Server #20252] Model saved to ./models/refedscafl/cifar10_alpha01/resnet_9.pth.
[INFO][14:00:12]: [Server #20252] Closing the server.
[INFO][14:00:12]: Closing the connection to client #16024.
[INFO][14:00:12]: Closing the connection to client #16388.
[INFO][14:00:12]: Closing the connection to client #15828.
[INFO][14:00:12]: Closing the connection to client #10704.
[INFO][14:00:12]: Closing the connection to client #20124.
[INFO][14:00:12]: Closing the connection to client #22844.
[INFO][14:00:12]: Closing the connection to client #38688.
[INFO][14:00:12]: Closing the connection to client #568.
[INFO][14:00:12]: Closing the connection to client #16184.
