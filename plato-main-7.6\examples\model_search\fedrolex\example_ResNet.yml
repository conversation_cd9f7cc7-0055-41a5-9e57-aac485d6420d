clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 50

    # The number of clients selected in each round
    per_round: 5

    # Should the clients compute test accuracy locally?
    do_test: false

    random_seed: 12345

    comm_simulation: true
    compute_comm_time: true

server:
    address: 127.0.0.1
    port: 8027
    do_test: true
    random_seed: 1

    simulate_wall_time: true

data:
    # The training and testing dataset
    datasource:  CIFAR10

    # Number of samples in each partition
    partition_size: 1000
    test_partition_size: 10000

    sampler: iid
    testset_sampler: iid
##
##    # The random seed for sampling data
    random_seed: 1234

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 400

    # The maximum number of clients running concurrently
    max_concurrency: 5

    # The target accuracy
    target_accuracy: 1.

    # Number of epochs for local training in each communication round
    epochs: 1
    batch_size: 64
    loss_criterion: CrossEntropyLoss
    optimizer: SGD
    global_lr_scheduler: true
    lr_scheduler: MultiStepLR

    model_type: torch_hub
    model_name: resnet18


algorithm:
    # A aggregation algorithm
    type: fedavg
    
results:
    types: round, accuracy, elapsed_time, comm_time, round_time, comm_overhead

parameters:
    model:
        model_rate: 1.0
        track: false
    client_model:
        track: false
    optimizer:
        lr: 0.1
        weight_decay: 0.0005
        momentum: 0.9
    learning_rate:
        milestone_steps: 150, 250
        gamma: 0.1
    limitation:
        activated: true
        min_size: 43
        max_size: 82
        min_flops: 531
        max_flops: 1108

