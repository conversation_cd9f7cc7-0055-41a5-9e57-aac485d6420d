clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8048
    checkpoint_path: checkpoints/resnet152_cifar100
    model_path: models/resnet152_cifar100

data:
    # The training and testing dataset
    datasource: CIFAR100

    # Number of samples in each partition
    partition_size: 60000

    # IID or non-IID?
    sampler: iid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 100

    # The maximum number of clients running concurrently
    max_concurrency: 5

    # The target accuracy
    target_accuracy: 0.60

    # The machine learning model
    model_name: resnet_152

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD
    lr_scheduler: LambdaLR

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    model:
        # Number of classes
        num_classes: 100

    optimizer:
        lr: 0.1
        momentum: 0.9
        weight_decay: 0.0001

    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    result_path: results/pretraining/resnet152_cifar100/

    # Write the following parameter(s) into a CSV
    types: round, accuracy, elapsed_time, round_time

    # Plot results (x_axis-y_axis)
    plot: round-accuracy, elapsed_time-accuracy
