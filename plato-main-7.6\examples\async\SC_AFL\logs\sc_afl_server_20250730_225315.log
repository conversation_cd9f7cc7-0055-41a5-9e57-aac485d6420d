2025-07-30 22:53:15,067 - INFO - 日志文件已创建: logs/sc_afl_server_20250730_225315.log
2025-07-30 22:53:15,071 - INFO - 服务器将使用事件循环: <ProactorEventLoop running=False closed=False debug=False>
2025-07-30 22:53:15,076 - INFO - [Client 1] 模型已放置到设备: cpu
2025-07-30 22:53:15,076 - INFO - [Client 1] 已更新trainer的模型引用
2025-07-30 22:53:15,076 - INFO - [Client 1] 已更新algorithm的模型引用
2025-07-30 22:53:15,077 - INFO - [Algorithm] 设置客户端ID: 1
2025-07-30 22:53:15,077 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-07-30 22:53:15,077 - INFO - [Client 1] 已更新algorithm的client_id
2025-07-30 22:53:15,078 - INFO - [Client 1] 模型初始化完成
2025-07-30 22:53:15,078 - INFO - 客户端 1 模型初始化成功
2025-07-30 22:53:15,084 - INFO - 客户端 1 异步训练任务已启动
2025-07-30 22:53:15,085 - INFO - [Client 2] 模型已放置到设备: cpu
2025-07-30 22:53:15,085 - INFO - [Client 2] 已更新trainer的模型引用
2025-07-30 22:53:15,085 - INFO - [Client 2] 已更新algorithm的模型引用
2025-07-30 22:53:15,086 - INFO - [Algorithm] 设置客户端ID: 2
2025-07-30 22:53:15,086 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-07-30 22:53:15,086 - INFO - [Client 2] 已更新algorithm的client_id
2025-07-30 22:53:15,086 - INFO - [Client 2] 模型初始化完成
2025-07-30 22:53:15,086 - INFO - 客户端 2 模型初始化成功
2025-07-30 22:53:15,087 - INFO - 客户端 2 异步训练任务已启动
2025-07-30 22:53:15,088 - INFO - [Client 3] 模型已放置到设备: cpu
2025-07-30 22:53:15,088 - INFO - [Client 3] 已更新trainer的模型引用
2025-07-30 22:53:15,088 - INFO - [Client 3] 已更新algorithm的模型引用
2025-07-30 22:53:15,089 - INFO - [Algorithm] 设置客户端ID: 3
2025-07-30 22:53:15,089 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-07-30 22:53:15,089 - INFO - [Client 3] 已更新algorithm的client_id
2025-07-30 22:53:15,089 - INFO - [Client 3] 模型初始化完成
2025-07-30 22:53:15,089 - INFO - 客户端 3 模型初始化成功
2025-07-30 22:53:15,089 - INFO - 客户端 3 异步训练任务已启动
2025-07-30 22:53:15,090 - INFO - [Client 4] 模型已放置到设备: cpu
2025-07-30 22:53:15,090 - INFO - [Client 4] 已更新trainer的模型引用
2025-07-30 22:53:15,090 - INFO - [Client 4] 已更新algorithm的模型引用
2025-07-30 22:53:15,090 - INFO - [Algorithm] 设置客户端ID: 4
2025-07-30 22:53:15,090 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-07-30 22:53:15,090 - INFO - [Client 4] 已更新algorithm的client_id
2025-07-30 22:53:15,090 - INFO - [Client 4] 模型初始化完成
2025-07-30 22:53:15,090 - INFO - 客户端 4 模型初始化成功
2025-07-30 22:53:15,091 - INFO - 客户端 4 异步训练任务已启动
2025-07-30 22:53:15,092 - INFO - [Client 5] 模型已放置到设备: cpu
2025-07-30 22:53:15,092 - INFO - [Client 5] 已更新trainer的模型引用
2025-07-30 22:53:15,092 - INFO - [Client 5] 已更新algorithm的模型引用
2025-07-30 22:53:15,092 - INFO - [Algorithm] 设置客户端ID: 5
2025-07-30 22:53:15,092 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-07-30 22:53:15,092 - INFO - [Client 5] 已更新algorithm的client_id
2025-07-30 22:53:15,092 - INFO - [Client 5] 模型初始化完成
2025-07-30 22:53:15,092 - INFO - 客户端 5 模型初始化成功
2025-07-30 22:53:15,092 - INFO - 客户端 5 异步训练任务已启动
2025-07-30 22:53:15,093 - INFO - [Client 6] 模型已放置到设备: cpu
2025-07-30 22:53:15,093 - INFO - [Client 6] 已更新trainer的模型引用
2025-07-30 22:53:15,093 - INFO - [Client 6] 已更新algorithm的模型引用
2025-07-30 22:53:15,093 - INFO - [Algorithm] 设置客户端ID: 6
2025-07-30 22:53:15,093 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-07-30 22:53:15,093 - INFO - [Client 6] 已更新algorithm的client_id
2025-07-30 22:53:15,093 - INFO - [Client 6] 模型初始化完成
2025-07-30 22:53:15,093 - INFO - 客户端 6 模型初始化成功
2025-07-30 22:53:15,093 - INFO - 客户端 6 异步训练任务已启动
2025-07-30 22:53:15,095 - INFO - [Client 7] 模型已放置到设备: cpu
2025-07-30 22:53:15,095 - INFO - [Client 7] 已更新trainer的模型引用
2025-07-30 22:53:15,095 - INFO - [Client 7] 已更新algorithm的模型引用
2025-07-30 22:53:15,095 - INFO - [Algorithm] 设置客户端ID: 7
2025-07-30 22:53:15,095 - INFO - [Algorithm] 同步更新trainer的client_id: 7
2025-07-30 22:53:15,095 - INFO - [Client 7] 已更新algorithm的client_id
2025-07-30 22:53:15,095 - INFO - [Client 7] 模型初始化完成
2025-07-30 22:53:15,095 - INFO - 客户端 7 模型初始化成功
2025-07-30 22:53:15,095 - INFO - 客户端 7 异步训练任务已启动
2025-07-30 22:53:15,096 - INFO - [Client 8] 模型已放置到设备: cpu
2025-07-30 22:53:15,096 - INFO - [Client 8] 已更新trainer的模型引用
2025-07-30 22:53:15,096 - INFO - [Client 8] 已更新algorithm的模型引用
2025-07-30 22:53:15,096 - INFO - [Algorithm] 设置客户端ID: 8
2025-07-30 22:53:15,097 - INFO - [Algorithm] 同步更新trainer的client_id: 8
2025-07-30 22:53:15,097 - INFO - [Client 8] 已更新algorithm的client_id
2025-07-30 22:53:15,097 - INFO - [Client 8] 模型初始化完成
2025-07-30 22:53:15,097 - INFO - 客户端 8 模型初始化成功
2025-07-30 22:53:15,097 - INFO - 客户端 8 异步训练任务已启动
2025-07-30 22:53:15,098 - INFO - [Client 9] 模型已放置到设备: cpu
2025-07-30 22:53:15,098 - INFO - [Client 9] 已更新trainer的模型引用
2025-07-30 22:53:15,098 - INFO - [Client 9] 已更新algorithm的模型引用
2025-07-30 22:53:15,098 - INFO - [Algorithm] 设置客户端ID: 9
2025-07-30 22:53:15,098 - INFO - [Algorithm] 同步更新trainer的client_id: 9
2025-07-30 22:53:15,098 - INFO - [Client 9] 已更新algorithm的client_id
2025-07-30 22:53:15,098 - INFO - [Client 9] 模型初始化完成
2025-07-30 22:53:15,098 - INFO - 客户端 9 模型初始化成功
2025-07-30 22:53:15,099 - INFO - 客户端 9 异步训练任务已启动
2025-07-30 22:53:15,099 - INFO - [Client 10] 模型已放置到设备: cpu
2025-07-30 22:53:15,099 - INFO - [Client 10] 已更新trainer的模型引用
2025-07-30 22:53:15,099 - INFO - [Client 10] 已更新algorithm的模型引用
2025-07-30 22:53:15,099 - INFO - [Algorithm] 设置客户端ID: 10
2025-07-30 22:53:15,099 - INFO - [Algorithm] 同步更新trainer的client_id: 10
2025-07-30 22:53:15,099 - INFO - [Client 10] 已更新algorithm的client_id
2025-07-30 22:53:15,099 - INFO - [Client 10] 模型初始化完成
2025-07-30 22:53:15,099 - INFO - 客户端 10 模型初始化成功
2025-07-30 22:53:15,099 - INFO - 客户端 10 异步训练任务已启动
2025-07-30 22:53:15,101 - INFO - [Client 11] 模型已放置到设备: cpu
2025-07-30 22:53:15,101 - INFO - [Client 11] 已更新trainer的模型引用
2025-07-30 22:53:15,101 - INFO - [Client 11] 已更新algorithm的模型引用
2025-07-30 22:53:15,101 - INFO - [Algorithm] 设置客户端ID: 11
2025-07-30 22:53:15,101 - INFO - [Algorithm] 同步更新trainer的client_id: 11
2025-07-30 22:53:15,101 - INFO - [Client 11] 已更新algorithm的client_id
2025-07-30 22:53:15,101 - INFO - [Client 11] 模型初始化完成
2025-07-30 22:53:15,101 - INFO - 客户端 11 模型初始化成功
2025-07-30 22:53:15,101 - INFO - 客户端 11 异步训练任务已启动
2025-07-30 22:53:15,102 - INFO - [Client 12] 模型已放置到设备: cpu
2025-07-30 22:53:15,102 - INFO - [Client 12] 已更新trainer的模型引用
2025-07-30 22:53:15,102 - INFO - [Client 12] 已更新algorithm的模型引用
2025-07-30 22:53:15,102 - INFO - [Algorithm] 设置客户端ID: 12
2025-07-30 22:53:15,102 - INFO - [Algorithm] 同步更新trainer的client_id: 12
2025-07-30 22:53:15,102 - INFO - [Client 12] 已更新algorithm的client_id
2025-07-30 22:53:15,102 - INFO - [Client 12] 模型初始化完成
2025-07-30 22:53:15,102 - INFO - 客户端 12 模型初始化成功
2025-07-30 22:53:15,102 - INFO - 客户端 12 异步训练任务已启动
2025-07-30 22:53:15,103 - INFO - [Client 13] 模型已放置到设备: cpu
2025-07-30 22:53:15,103 - INFO - [Client 13] 已更新trainer的模型引用
2025-07-30 22:53:15,103 - INFO - [Client 13] 已更新algorithm的模型引用
2025-07-30 22:53:15,103 - INFO - [Algorithm] 设置客户端ID: 13
2025-07-30 22:53:15,104 - INFO - [Algorithm] 同步更新trainer的client_id: 13
2025-07-30 22:53:15,104 - INFO - [Client 13] 已更新algorithm的client_id
2025-07-30 22:53:15,104 - INFO - [Client 13] 模型初始化完成
2025-07-30 22:53:15,104 - INFO - 客户端 13 模型初始化成功
2025-07-30 22:53:15,104 - INFO - 客户端 13 异步训练任务已启动
2025-07-30 22:53:15,104 - INFO - [Client 14] 模型已放置到设备: cpu
2025-07-30 22:53:15,104 - INFO - [Client 14] 已更新trainer的模型引用
2025-07-30 22:53:15,104 - INFO - [Client 14] 已更新algorithm的模型引用
2025-07-30 22:53:15,104 - INFO - [Algorithm] 设置客户端ID: 14
2025-07-30 22:53:15,104 - INFO - [Algorithm] 同步更新trainer的client_id: 14
2025-07-30 22:53:15,104 - INFO - [Client 14] 已更新algorithm的client_id
2025-07-30 22:53:15,105 - INFO - [Client 14] 模型初始化完成
2025-07-30 22:53:15,105 - INFO - 客户端 14 模型初始化成功
2025-07-30 22:53:15,105 - INFO - 客户端 14 异步训练任务已启动
2025-07-30 22:53:15,107 - INFO - [Client 15] 模型已放置到设备: cpu
2025-07-30 22:53:15,107 - INFO - [Client 15] 已更新trainer的模型引用
2025-07-30 22:53:15,107 - INFO - [Client 15] 已更新algorithm的模型引用
2025-07-30 22:53:15,107 - INFO - [Algorithm] 设置客户端ID: 15
2025-07-30 22:53:15,107 - INFO - [Algorithm] 同步更新trainer的client_id: 15
2025-07-30 22:53:15,107 - INFO - [Client 15] 已更新algorithm的client_id
2025-07-30 22:53:15,107 - INFO - [Client 15] 模型初始化完成
2025-07-30 22:53:15,107 - INFO - 客户端 15 模型初始化成功
2025-07-30 22:53:15,107 - INFO - 客户端 15 异步训练任务已启动
2025-07-30 22:53:15,108 - INFO - [Client 16] 模型已放置到设备: cpu
2025-07-30 22:53:15,108 - INFO - [Client 16] 已更新trainer的模型引用
2025-07-30 22:53:15,108 - INFO - [Client 16] 已更新algorithm的模型引用
2025-07-30 22:53:15,108 - INFO - [Algorithm] 设置客户端ID: 16
2025-07-30 22:53:15,108 - INFO - [Algorithm] 同步更新trainer的client_id: 16
2025-07-30 22:53:15,108 - INFO - [Client 16] 已更新algorithm的client_id
2025-07-30 22:53:15,108 - INFO - [Client 16] 模型初始化完成
2025-07-30 22:53:15,108 - INFO - 客户端 16 模型初始化成功
2025-07-30 22:53:15,108 - INFO - 客户端 16 异步训练任务已启动
2025-07-30 22:53:15,109 - INFO - [Client 17] 模型已放置到设备: cpu
2025-07-30 22:53:15,109 - INFO - [Client 17] 已更新trainer的模型引用
2025-07-30 22:53:15,109 - INFO - [Client 17] 已更新algorithm的模型引用
2025-07-30 22:53:15,109 - INFO - [Algorithm] 设置客户端ID: 17
2025-07-30 22:53:15,110 - INFO - [Algorithm] 同步更新trainer的client_id: 17
2025-07-30 22:53:15,110 - INFO - [Client 17] 已更新algorithm的client_id
2025-07-30 22:53:15,110 - INFO - [Client 17] 模型初始化完成
2025-07-30 22:53:15,110 - INFO - 客户端 17 模型初始化成功
2025-07-30 22:53:15,110 - INFO - 客户端 17 异步训练任务已启动
2025-07-30 22:53:15,112 - INFO - [Client 18] 模型已放置到设备: cpu
2025-07-30 22:53:15,112 - INFO - [Client 18] 已更新trainer的模型引用
2025-07-30 22:53:15,112 - INFO - [Client 18] 已更新algorithm的模型引用
2025-07-30 22:53:15,112 - INFO - [Algorithm] 设置客户端ID: 18
2025-07-30 22:53:15,112 - INFO - [Algorithm] 同步更新trainer的client_id: 18
2025-07-30 22:53:15,112 - INFO - [Client 18] 已更新algorithm的client_id
2025-07-30 22:53:15,112 - INFO - [Client 18] 模型初始化完成
2025-07-30 22:53:15,112 - INFO - 客户端 18 模型初始化成功
2025-07-30 22:53:15,112 - INFO - 客户端 18 异步训练任务已启动
2025-07-30 22:53:15,113 - INFO - [Client 19] 模型已放置到设备: cpu
2025-07-30 22:53:15,113 - INFO - [Client 19] 已更新trainer的模型引用
2025-07-30 22:53:15,113 - INFO - [Client 19] 已更新algorithm的模型引用
2025-07-30 22:53:15,113 - INFO - [Algorithm] 设置客户端ID: 19
2025-07-30 22:53:15,113 - INFO - [Algorithm] 同步更新trainer的client_id: 19
2025-07-30 22:53:15,113 - INFO - [Client 19] 已更新algorithm的client_id
2025-07-30 22:53:15,113 - INFO - [Client 19] 模型初始化完成
2025-07-30 22:53:15,113 - INFO - 客户端 19 模型初始化成功
2025-07-30 22:53:15,114 - INFO - 客户端 19 异步训练任务已启动
2025-07-30 22:53:15,114 - INFO - [Client 20] 模型已放置到设备: cpu
2025-07-30 22:53:15,114 - INFO - [Client 20] 已更新trainer的模型引用
2025-07-30 22:53:15,114 - INFO - [Client 20] 已更新algorithm的模型引用
2025-07-30 22:53:15,114 - INFO - [Algorithm] 设置客户端ID: 20
2025-07-30 22:53:15,114 - INFO - [Algorithm] 同步更新trainer的client_id: 20
2025-07-30 22:53:15,115 - INFO - [Client 20] 已更新algorithm的client_id
2025-07-30 22:53:15,115 - INFO - [Client 20] 模型初始化完成
2025-07-30 22:53:15,115 - INFO - 客户端 20 模型初始化成功
2025-07-30 22:53:15,115 - INFO - 客户端 20 异步训练任务已启动
2025-07-30 22:53:15,115 - INFO - [Client 21] 模型已放置到设备: cpu
2025-07-30 22:53:15,115 - INFO - [Client 21] 已更新trainer的模型引用
2025-07-30 22:53:15,115 - INFO - [Client 21] 已更新algorithm的模型引用
2025-07-30 22:53:15,115 - INFO - [Algorithm] 设置客户端ID: 21
2025-07-30 22:53:15,115 - INFO - [Algorithm] 同步更新trainer的client_id: 21
2025-07-30 22:53:15,116 - INFO - [Client 21] 已更新algorithm的client_id
2025-07-30 22:53:15,116 - INFO - [Client 21] 模型初始化完成
2025-07-30 22:53:15,116 - INFO - 客户端 21 模型初始化成功
2025-07-30 22:53:15,116 - INFO - 客户端 21 异步训练任务已启动
2025-07-30 22:53:15,116 - INFO - [Client 22] 模型已放置到设备: cpu
2025-07-30 22:53:15,117 - INFO - [Client 22] 已更新trainer的模型引用
2025-07-30 22:53:15,117 - INFO - [Client 22] 已更新algorithm的模型引用
2025-07-30 22:53:15,117 - INFO - [Algorithm] 设置客户端ID: 22
2025-07-30 22:53:15,117 - INFO - [Algorithm] 同步更新trainer的client_id: 22
2025-07-30 22:53:15,117 - INFO - [Client 22] 已更新algorithm的client_id
2025-07-30 22:53:15,117 - INFO - [Client 22] 模型初始化完成
2025-07-30 22:53:15,117 - INFO - 客户端 22 模型初始化成功
2025-07-30 22:53:15,117 - INFO - 客户端 22 异步训练任务已启动
2025-07-30 22:53:15,118 - INFO - [Client 23] 模型已放置到设备: cpu
2025-07-30 22:53:15,118 - INFO - [Client 23] 已更新trainer的模型引用
2025-07-30 22:53:15,118 - INFO - [Client 23] 已更新algorithm的模型引用
2025-07-30 22:53:15,118 - INFO - [Algorithm] 设置客户端ID: 23
2025-07-30 22:53:15,118 - INFO - [Algorithm] 同步更新trainer的client_id: 23
2025-07-30 22:53:15,118 - INFO - [Client 23] 已更新algorithm的client_id
2025-07-30 22:53:15,118 - INFO - [Client 23] 模型初始化完成
2025-07-30 22:53:15,118 - INFO - 客户端 23 模型初始化成功
2025-07-30 22:53:15,118 - INFO - 客户端 23 异步训练任务已启动
2025-07-30 22:53:15,119 - INFO - [Client 24] 模型已放置到设备: cpu
2025-07-30 22:53:15,119 - INFO - [Client 24] 已更新trainer的模型引用
2025-07-30 22:53:15,119 - INFO - [Client 24] 已更新algorithm的模型引用
2025-07-30 22:53:15,119 - INFO - [Algorithm] 设置客户端ID: 24
2025-07-30 22:53:15,119 - INFO - [Algorithm] 同步更新trainer的client_id: 24
2025-07-30 22:53:15,119 - INFO - [Client 24] 已更新algorithm的client_id
2025-07-30 22:53:15,119 - INFO - [Client 24] 模型初始化完成
2025-07-30 22:53:15,119 - INFO - 客户端 24 模型初始化成功
2025-07-30 22:53:15,119 - INFO - 客户端 24 异步训练任务已启动
2025-07-30 22:53:15,120 - INFO - [Client 25] 模型已放置到设备: cpu
2025-07-30 22:53:15,121 - INFO - [Client 25] 已更新trainer的模型引用
2025-07-30 22:53:15,121 - INFO - [Client 25] 已更新algorithm的模型引用
2025-07-30 22:53:15,121 - INFO - [Algorithm] 设置客户端ID: 25
2025-07-30 22:53:15,121 - INFO - [Algorithm] 同步更新trainer的client_id: 25
2025-07-30 22:53:15,121 - INFO - [Client 25] 已更新algorithm的client_id
2025-07-30 22:53:15,121 - INFO - [Client 25] 模型初始化完成
2025-07-30 22:53:15,121 - INFO - 客户端 25 模型初始化成功
2025-07-30 22:53:15,121 - INFO - 客户端 25 异步训练任务已启动
2025-07-30 22:53:15,123 - INFO - [Client 26] 模型已放置到设备: cpu
2025-07-30 22:53:15,123 - INFO - [Client 26] 已更新trainer的模型引用
2025-07-30 22:53:15,123 - INFO - [Client 26] 已更新algorithm的模型引用
2025-07-30 22:53:15,123 - INFO - [Algorithm] 设置客户端ID: 26
2025-07-30 22:53:15,123 - INFO - [Algorithm] 同步更新trainer的client_id: 26
2025-07-30 22:53:15,123 - INFO - [Client 26] 已更新algorithm的client_id
2025-07-30 22:53:15,123 - INFO - [Client 26] 模型初始化完成
2025-07-30 22:53:15,123 - INFO - 客户端 26 模型初始化成功
2025-07-30 22:53:15,123 - INFO - 客户端 26 异步训练任务已启动
2025-07-30 22:53:15,124 - INFO - [Client 27] 模型已放置到设备: cpu
2025-07-30 22:53:15,124 - INFO - [Client 27] 已更新trainer的模型引用
2025-07-30 22:53:15,124 - INFO - [Client 27] 已更新algorithm的模型引用
2025-07-30 22:53:15,125 - INFO - [Algorithm] 设置客户端ID: 27
2025-07-30 22:53:15,125 - INFO - [Algorithm] 同步更新trainer的client_id: 27
2025-07-30 22:53:15,125 - INFO - [Client 27] 已更新algorithm的client_id
2025-07-30 22:53:15,125 - INFO - [Client 27] 模型初始化完成
2025-07-30 22:53:15,125 - INFO - 客户端 27 模型初始化成功
2025-07-30 22:53:15,125 - INFO - 客户端 27 异步训练任务已启动
2025-07-30 22:53:15,126 - INFO - [Client 28] 模型已放置到设备: cpu
2025-07-30 22:53:15,126 - INFO - [Client 28] 已更新trainer的模型引用
2025-07-30 22:53:15,126 - INFO - [Client 28] 已更新algorithm的模型引用
2025-07-30 22:53:15,126 - INFO - [Algorithm] 设置客户端ID: 28
2025-07-30 22:53:15,126 - INFO - [Algorithm] 同步更新trainer的client_id: 28
2025-07-30 22:53:15,126 - INFO - [Client 28] 已更新algorithm的client_id
2025-07-30 22:53:15,126 - INFO - [Client 28] 模型初始化完成
2025-07-30 22:53:15,126 - INFO - 客户端 28 模型初始化成功
2025-07-30 22:53:15,126 - INFO - 客户端 28 异步训练任务已启动
2025-07-30 22:53:15,128 - INFO - [Client 29] 模型已放置到设备: cpu
2025-07-30 22:53:15,128 - INFO - [Client 29] 已更新trainer的模型引用
2025-07-30 22:53:15,128 - INFO - [Client 29] 已更新algorithm的模型引用
2025-07-30 22:53:15,128 - INFO - [Algorithm] 设置客户端ID: 29
2025-07-30 22:53:15,128 - INFO - [Algorithm] 同步更新trainer的client_id: 29
2025-07-30 22:53:15,128 - INFO - [Client 29] 已更新algorithm的client_id
2025-07-30 22:53:15,128 - INFO - [Client 29] 模型初始化完成
2025-07-30 22:53:15,128 - INFO - 客户端 29 模型初始化成功
2025-07-30 22:53:15,128 - INFO - 客户端 29 异步训练任务已启动
2025-07-30 22:53:15,129 - INFO - [Client 30] 模型已放置到设备: cpu
2025-07-30 22:53:15,129 - INFO - [Client 30] 已更新trainer的模型引用
2025-07-30 22:53:15,129 - INFO - [Client 30] 已更新algorithm的模型引用
2025-07-30 22:53:15,129 - INFO - [Algorithm] 设置客户端ID: 30
2025-07-30 22:53:15,129 - INFO - [Algorithm] 同步更新trainer的client_id: 30
2025-07-30 22:53:15,129 - INFO - [Client 30] 已更新algorithm的client_id
2025-07-30 22:53:15,130 - INFO - [Client 30] 模型初始化完成
2025-07-30 22:53:15,130 - INFO - 客户端 30 模型初始化成功
2025-07-30 22:53:15,130 - INFO - 客户端 30 异步训练任务已启动
2025-07-30 22:53:15,131 - INFO - [Client 31] 模型已放置到设备: cpu
2025-07-30 22:53:15,131 - INFO - [Client 31] 已更新trainer的模型引用
2025-07-30 22:53:15,131 - INFO - [Client 31] 已更新algorithm的模型引用
2025-07-30 22:53:15,131 - INFO - [Algorithm] 设置客户端ID: 31
2025-07-30 22:53:15,131 - INFO - [Algorithm] 同步更新trainer的client_id: 31
2025-07-30 22:53:15,131 - INFO - [Client 31] 已更新algorithm的client_id
2025-07-30 22:53:15,131 - INFO - [Client 31] 模型初始化完成
2025-07-30 22:53:15,131 - INFO - 客户端 31 模型初始化成功
2025-07-30 22:53:15,131 - INFO - 客户端 31 异步训练任务已启动
2025-07-30 22:53:15,132 - INFO - [Client 32] 模型已放置到设备: cpu
2025-07-30 22:53:15,132 - INFO - [Client 32] 已更新trainer的模型引用
2025-07-30 22:53:15,132 - INFO - [Client 32] 已更新algorithm的模型引用
2025-07-30 22:53:15,132 - INFO - [Algorithm] 设置客户端ID: 32
2025-07-30 22:53:15,132 - INFO - [Algorithm] 同步更新trainer的client_id: 32
2025-07-30 22:53:15,132 - INFO - [Client 32] 已更新algorithm的client_id
2025-07-30 22:53:15,132 - INFO - [Client 32] 模型初始化完成
2025-07-30 22:53:15,132 - INFO - 客户端 32 模型初始化成功
2025-07-30 22:53:15,132 - INFO - 客户端 32 异步训练任务已启动
2025-07-30 22:53:15,133 - INFO - [Client 33] 模型已放置到设备: cpu
2025-07-30 22:53:15,133 - INFO - [Client 33] 已更新trainer的模型引用
2025-07-30 22:53:15,133 - INFO - [Client 33] 已更新algorithm的模型引用
2025-07-30 22:53:15,133 - INFO - [Algorithm] 设置客户端ID: 33
2025-07-30 22:53:15,133 - INFO - [Algorithm] 同步更新trainer的client_id: 33
2025-07-30 22:53:15,133 - INFO - [Client 33] 已更新algorithm的client_id
2025-07-30 22:53:15,134 - INFO - [Client 33] 模型初始化完成
2025-07-30 22:53:15,134 - INFO - 客户端 33 模型初始化成功
2025-07-30 22:53:15,134 - INFO - 客户端 33 异步训练任务已启动
2025-07-30 22:53:15,135 - INFO - [Client 34] 模型已放置到设备: cpu
2025-07-30 22:53:15,135 - INFO - [Client 34] 已更新trainer的模型引用
2025-07-30 22:53:15,135 - INFO - [Client 34] 已更新algorithm的模型引用
2025-07-30 22:53:15,135 - INFO - [Algorithm] 设置客户端ID: 34
2025-07-30 22:53:15,135 - INFO - [Algorithm] 同步更新trainer的client_id: 34
2025-07-30 22:53:15,135 - INFO - [Client 34] 已更新algorithm的client_id
2025-07-30 22:53:15,135 - INFO - [Client 34] 模型初始化完成
2025-07-30 22:53:15,135 - INFO - 客户端 34 模型初始化成功
2025-07-30 22:53:15,135 - INFO - 客户端 34 异步训练任务已启动
2025-07-30 22:53:15,136 - INFO - [Client 35] 模型已放置到设备: cpu
2025-07-30 22:53:15,136 - INFO - [Client 35] 已更新trainer的模型引用
2025-07-30 22:53:15,137 - INFO - [Client 35] 已更新algorithm的模型引用
2025-07-30 22:53:15,137 - INFO - [Algorithm] 设置客户端ID: 35
2025-07-30 22:53:15,137 - INFO - [Algorithm] 同步更新trainer的client_id: 35
2025-07-30 22:53:15,137 - INFO - [Client 35] 已更新algorithm的client_id
2025-07-30 22:53:15,137 - INFO - [Client 35] 模型初始化完成
2025-07-30 22:53:15,137 - INFO - 客户端 35 模型初始化成功
2025-07-30 22:53:15,137 - INFO - 客户端 35 异步训练任务已启动
2025-07-30 22:53:15,138 - INFO - [Client 36] 模型已放置到设备: cpu
2025-07-30 22:53:15,138 - INFO - [Client 36] 已更新trainer的模型引用
2025-07-30 22:53:15,138 - INFO - [Client 36] 已更新algorithm的模型引用
2025-07-30 22:53:15,138 - INFO - [Algorithm] 设置客户端ID: 36
2025-07-30 22:53:15,138 - INFO - [Algorithm] 同步更新trainer的client_id: 36
2025-07-30 22:53:15,138 - INFO - [Client 36] 已更新algorithm的client_id
2025-07-30 22:53:15,138 - INFO - [Client 36] 模型初始化完成
2025-07-30 22:53:15,138 - INFO - 客户端 36 模型初始化成功
2025-07-30 22:53:15,138 - INFO - 客户端 36 异步训练任务已启动
2025-07-30 22:53:15,139 - INFO - [Client 37] 模型已放置到设备: cpu
2025-07-30 22:53:15,139 - INFO - [Client 37] 已更新trainer的模型引用
2025-07-30 22:53:15,139 - INFO - [Client 37] 已更新algorithm的模型引用
2025-07-30 22:53:15,139 - INFO - [Algorithm] 设置客户端ID: 37
2025-07-30 22:53:15,139 - INFO - [Algorithm] 同步更新trainer的client_id: 37
2025-07-30 22:53:15,139 - INFO - [Client 37] 已更新algorithm的client_id
2025-07-30 22:53:15,139 - INFO - [Client 37] 模型初始化完成
2025-07-30 22:53:15,139 - INFO - 客户端 37 模型初始化成功
2025-07-30 22:53:15,140 - INFO - 客户端 37 异步训练任务已启动
2025-07-30 22:53:15,141 - INFO - [Client 38] 模型已放置到设备: cpu
2025-07-30 22:53:15,141 - INFO - [Client 38] 已更新trainer的模型引用
2025-07-30 22:53:15,141 - INFO - [Client 38] 已更新algorithm的模型引用
2025-07-30 22:53:15,141 - INFO - [Algorithm] 设置客户端ID: 38
2025-07-30 22:53:15,141 - INFO - [Algorithm] 同步更新trainer的client_id: 38
2025-07-30 22:53:15,141 - INFO - [Client 38] 已更新algorithm的client_id
2025-07-30 22:53:15,141 - INFO - [Client 38] 模型初始化完成
2025-07-30 22:53:15,141 - INFO - 客户端 38 模型初始化成功
2025-07-30 22:53:15,141 - INFO - 客户端 38 异步训练任务已启动
2025-07-30 22:53:15,142 - INFO - [Client 39] 模型已放置到设备: cpu
2025-07-30 22:53:15,142 - INFO - [Client 39] 已更新trainer的模型引用
2025-07-30 22:53:15,142 - INFO - [Client 39] 已更新algorithm的模型引用
2025-07-30 22:53:15,142 - INFO - [Algorithm] 设置客户端ID: 39
2025-07-30 22:53:15,142 - INFO - [Algorithm] 同步更新trainer的client_id: 39
2025-07-30 22:53:15,142 - INFO - [Client 39] 已更新algorithm的client_id
2025-07-30 22:53:15,142 - INFO - [Client 39] 模型初始化完成
2025-07-30 22:53:15,142 - INFO - 客户端 39 模型初始化成功
2025-07-30 22:53:15,142 - INFO - 客户端 39 异步训练任务已启动
2025-07-30 22:53:15,143 - INFO - [Client 40] 模型已放置到设备: cpu
2025-07-30 22:53:15,143 - INFO - [Client 40] 已更新trainer的模型引用
2025-07-30 22:53:15,143 - INFO - [Client 40] 已更新algorithm的模型引用
2025-07-30 22:53:15,144 - INFO - [Algorithm] 设置客户端ID: 40
2025-07-30 22:53:15,144 - INFO - [Algorithm] 同步更新trainer的client_id: 40
2025-07-30 22:53:15,144 - INFO - [Client 40] 已更新algorithm的client_id
2025-07-30 22:53:15,144 - INFO - [Client 40] 模型初始化完成
2025-07-30 22:53:15,144 - INFO - 客户端 40 模型初始化成功
2025-07-30 22:53:15,144 - INFO - 客户端 40 异步训练任务已启动
2025-07-30 22:53:15,145 - INFO - [Client 41] 模型已放置到设备: cpu
2025-07-30 22:53:15,145 - INFO - [Client 41] 已更新trainer的模型引用
2025-07-30 22:53:15,145 - INFO - [Client 41] 已更新algorithm的模型引用
2025-07-30 22:53:15,145 - INFO - [Algorithm] 设置客户端ID: 41
2025-07-30 22:53:15,145 - INFO - [Algorithm] 同步更新trainer的client_id: 41
2025-07-30 22:53:15,145 - INFO - [Client 41] 已更新algorithm的client_id
2025-07-30 22:53:15,145 - INFO - [Client 41] 模型初始化完成
2025-07-30 22:53:15,146 - INFO - 客户端 41 模型初始化成功
2025-07-30 22:53:15,146 - INFO - 客户端 41 异步训练任务已启动
2025-07-30 22:53:15,146 - INFO - [Client 42] 模型已放置到设备: cpu
2025-07-30 22:53:15,146 - INFO - [Client 42] 已更新trainer的模型引用
2025-07-30 22:53:15,146 - INFO - [Client 42] 已更新algorithm的模型引用
2025-07-30 22:53:15,146 - INFO - [Algorithm] 设置客户端ID: 42
2025-07-30 22:53:15,146 - INFO - [Algorithm] 同步更新trainer的client_id: 42
2025-07-30 22:53:15,146 - INFO - [Client 42] 已更新algorithm的client_id
2025-07-30 22:53:15,146 - INFO - [Client 42] 模型初始化完成
2025-07-30 22:53:15,146 - INFO - 客户端 42 模型初始化成功
2025-07-30 22:53:15,146 - INFO - 客户端 42 异步训练任务已启动
2025-07-30 22:53:15,148 - INFO - [Client 43] 模型已放置到设备: cpu
2025-07-30 22:53:15,148 - INFO - [Client 43] 已更新trainer的模型引用
2025-07-30 22:53:15,148 - INFO - [Client 43] 已更新algorithm的模型引用
2025-07-30 22:53:15,148 - INFO - [Algorithm] 设置客户端ID: 43
2025-07-30 22:53:15,148 - INFO - [Algorithm] 同步更新trainer的client_id: 43
2025-07-30 22:53:15,148 - INFO - [Client 43] 已更新algorithm的client_id
2025-07-30 22:53:15,148 - INFO - [Client 43] 模型初始化完成
2025-07-30 22:53:15,148 - INFO - 客户端 43 模型初始化成功
2025-07-30 22:53:15,148 - INFO - 客户端 43 异步训练任务已启动
2025-07-30 22:53:15,149 - INFO - [Client 44] 模型已放置到设备: cpu
2025-07-30 22:53:15,149 - INFO - [Client 44] 已更新trainer的模型引用
2025-07-30 22:53:15,149 - INFO - [Client 44] 已更新algorithm的模型引用
2025-07-30 22:53:15,149 - INFO - [Algorithm] 设置客户端ID: 44
2025-07-30 22:53:15,149 - INFO - [Algorithm] 同步更新trainer的client_id: 44
2025-07-30 22:53:15,149 - INFO - [Client 44] 已更新algorithm的client_id
2025-07-30 22:53:15,150 - INFO - [Client 44] 模型初始化完成
2025-07-30 22:53:15,150 - INFO - 客户端 44 模型初始化成功
2025-07-30 22:53:15,150 - INFO - 客户端 44 异步训练任务已启动
2025-07-30 22:53:15,151 - INFO - [Client 45] 模型已放置到设备: cpu
2025-07-30 22:53:15,151 - INFO - [Client 45] 已更新trainer的模型引用
2025-07-30 22:53:15,151 - INFO - [Client 45] 已更新algorithm的模型引用
2025-07-30 22:53:15,151 - INFO - [Algorithm] 设置客户端ID: 45
2025-07-30 22:53:15,151 - INFO - [Algorithm] 同步更新trainer的client_id: 45
2025-07-30 22:53:15,151 - INFO - [Client 45] 已更新algorithm的client_id
2025-07-30 22:53:15,151 - INFO - [Client 45] 模型初始化完成
2025-07-30 22:53:15,151 - INFO - 客户端 45 模型初始化成功
2025-07-30 22:53:15,151 - INFO - 客户端 45 异步训练任务已启动
2025-07-30 22:53:15,152 - INFO - [Client 46] 模型已放置到设备: cpu
2025-07-30 22:53:15,152 - INFO - [Client 46] 已更新trainer的模型引用
2025-07-30 22:53:15,152 - INFO - [Client 46] 已更新algorithm的模型引用
2025-07-30 22:53:15,152 - INFO - [Algorithm] 设置客户端ID: 46
2025-07-30 22:53:15,152 - INFO - [Algorithm] 同步更新trainer的client_id: 46
2025-07-30 22:53:15,152 - INFO - [Client 46] 已更新algorithm的client_id
2025-07-30 22:53:15,152 - INFO - [Client 46] 模型初始化完成
2025-07-30 22:53:15,152 - INFO - 客户端 46 模型初始化成功
2025-07-30 22:53:15,152 - INFO - 客户端 46 异步训练任务已启动
2025-07-30 22:53:15,153 - INFO - [Client 47] 模型已放置到设备: cpu
2025-07-30 22:53:15,153 - INFO - [Client 47] 已更新trainer的模型引用
2025-07-30 22:53:15,153 - INFO - [Client 47] 已更新algorithm的模型引用
2025-07-30 22:53:15,153 - INFO - [Algorithm] 设置客户端ID: 47
2025-07-30 22:53:15,153 - INFO - [Algorithm] 同步更新trainer的client_id: 47
2025-07-30 22:53:15,154 - INFO - [Client 47] 已更新algorithm的client_id
2025-07-30 22:53:15,154 - INFO - [Client 47] 模型初始化完成
2025-07-30 22:53:15,154 - INFO - 客户端 47 模型初始化成功
2025-07-30 22:53:15,154 - INFO - 客户端 47 异步训练任务已启动
2025-07-30 22:53:15,154 - INFO - [Client 48] 模型已放置到设备: cpu
2025-07-30 22:53:15,155 - INFO - [Client 48] 已更新trainer的模型引用
2025-07-30 22:53:15,155 - INFO - [Client 48] 已更新algorithm的模型引用
2025-07-30 22:53:15,155 - INFO - [Algorithm] 设置客户端ID: 48
2025-07-30 22:53:15,155 - INFO - [Algorithm] 同步更新trainer的client_id: 48
2025-07-30 22:53:15,155 - INFO - [Client 48] 已更新algorithm的client_id
2025-07-30 22:53:15,155 - INFO - [Client 48] 模型初始化完成
2025-07-30 22:53:15,155 - INFO - 客户端 48 模型初始化成功
2025-07-30 22:53:15,155 - INFO - 客户端 48 异步训练任务已启动
2025-07-30 22:53:15,156 - INFO - [Client 49] 模型已放置到设备: cpu
2025-07-30 22:53:15,156 - INFO - [Client 49] 已更新trainer的模型引用
2025-07-30 22:53:15,156 - INFO - [Client 49] 已更新algorithm的模型引用
2025-07-30 22:53:15,157 - INFO - [Algorithm] 设置客户端ID: 49
2025-07-30 22:53:15,157 - INFO - [Algorithm] 同步更新trainer的client_id: 49
2025-07-30 22:53:15,157 - INFO - [Client 49] 已更新algorithm的client_id
2025-07-30 22:53:15,157 - INFO - [Client 49] 模型初始化完成
2025-07-30 22:53:15,157 - INFO - 客户端 49 模型初始化成功
2025-07-30 22:53:15,157 - INFO - 客户端 49 异步训练任务已启动
2025-07-30 22:53:15,158 - INFO - [Client 50] 模型已放置到设备: cpu
2025-07-30 22:53:15,158 - INFO - [Client 50] 已更新trainer的模型引用
2025-07-30 22:53:15,158 - INFO - [Client 50] 已更新algorithm的模型引用
2025-07-30 22:53:15,159 - INFO - [Algorithm] 设置客户端ID: 50
2025-07-30 22:53:15,159 - INFO - [Algorithm] 同步更新trainer的client_id: 50
2025-07-30 22:53:15,159 - INFO - [Client 50] 已更新algorithm的client_id
2025-07-30 22:53:15,159 - INFO - [Client 50] 模型初始化完成
2025-07-30 22:53:15,159 - INFO - 客户端 50 模型初始化成功
2025-07-30 22:53:15,159 - INFO - 客户端 50 异步训练任务已启动
2025-07-30 22:53:15,160 - INFO - [Client 51] 模型已放置到设备: cpu
2025-07-30 22:53:15,160 - INFO - [Client 51] 已更新trainer的模型引用
2025-07-30 22:53:15,160 - INFO - [Client 51] 已更新algorithm的模型引用
2025-07-30 22:53:15,160 - INFO - [Algorithm] 设置客户端ID: 51
2025-07-30 22:53:15,160 - INFO - [Algorithm] 同步更新trainer的client_id: 51
2025-07-30 22:53:15,160 - INFO - [Client 51] 已更新algorithm的client_id
2025-07-30 22:53:15,160 - INFO - [Client 51] 模型初始化完成
2025-07-30 22:53:15,160 - INFO - 客户端 51 模型初始化成功
2025-07-30 22:53:15,160 - INFO - 客户端 51 异步训练任务已启动
2025-07-30 22:53:15,161 - INFO - [Client 52] 模型已放置到设备: cpu
2025-07-30 22:53:15,161 - INFO - [Client 52] 已更新trainer的模型引用
2025-07-30 22:53:15,162 - INFO - [Client 52] 已更新algorithm的模型引用
2025-07-30 22:53:15,162 - INFO - [Algorithm] 设置客户端ID: 52
2025-07-30 22:53:15,162 - INFO - [Algorithm] 同步更新trainer的client_id: 52
2025-07-30 22:53:15,162 - INFO - [Client 52] 已更新algorithm的client_id
2025-07-30 22:53:15,162 - INFO - [Client 52] 模型初始化完成
2025-07-30 22:53:15,162 - INFO - 客户端 52 模型初始化成功
2025-07-30 22:53:15,162 - INFO - 客户端 52 异步训练任务已启动
2025-07-30 22:53:15,164 - INFO - [Client 53] 模型已放置到设备: cpu
2025-07-30 22:53:15,164 - INFO - [Client 53] 已更新trainer的模型引用
2025-07-30 22:53:15,164 - INFO - [Client 53] 已更新algorithm的模型引用
2025-07-30 22:53:15,164 - INFO - [Algorithm] 设置客户端ID: 53
2025-07-30 22:53:15,164 - INFO - [Algorithm] 同步更新trainer的client_id: 53
2025-07-30 22:53:15,164 - INFO - [Client 53] 已更新algorithm的client_id
2025-07-30 22:53:15,164 - INFO - [Client 53] 模型初始化完成
2025-07-30 22:53:15,164 - INFO - 客户端 53 模型初始化成功
2025-07-30 22:53:15,165 - INFO - 客户端 53 异步训练任务已启动
2025-07-30 22:53:15,165 - INFO - [Client 54] 模型已放置到设备: cpu
2025-07-30 22:53:15,165 - INFO - [Client 54] 已更新trainer的模型引用
2025-07-30 22:53:15,165 - INFO - [Client 54] 已更新algorithm的模型引用
2025-07-30 22:53:15,166 - INFO - [Algorithm] 设置客户端ID: 54
2025-07-30 22:53:15,166 - INFO - [Algorithm] 同步更新trainer的client_id: 54
2025-07-30 22:53:15,166 - INFO - [Client 54] 已更新algorithm的client_id
2025-07-30 22:53:15,166 - INFO - [Client 54] 模型初始化完成
2025-07-30 22:53:15,166 - INFO - 客户端 54 模型初始化成功
2025-07-30 22:53:15,166 - INFO - 客户端 54 异步训练任务已启动
2025-07-30 22:53:15,167 - INFO - [Client 55] 模型已放置到设备: cpu
2025-07-30 22:53:15,167 - INFO - [Client 55] 已更新trainer的模型引用
2025-07-30 22:53:15,167 - INFO - [Client 55] 已更新algorithm的模型引用
2025-07-30 22:53:15,167 - INFO - [Algorithm] 设置客户端ID: 55
2025-07-30 22:53:15,167 - INFO - [Algorithm] 同步更新trainer的client_id: 55
2025-07-30 22:53:15,168 - INFO - [Client 55] 已更新algorithm的client_id
2025-07-30 22:53:15,168 - INFO - [Client 55] 模型初始化完成
2025-07-30 22:53:15,168 - INFO - 客户端 55 模型初始化成功
2025-07-30 22:53:15,168 - INFO - 客户端 55 异步训练任务已启动
2025-07-30 22:53:15,169 - INFO - [Client 56] 模型已放置到设备: cpu
2025-07-30 22:53:15,169 - INFO - [Client 56] 已更新trainer的模型引用
2025-07-30 22:53:15,169 - INFO - [Client 56] 已更新algorithm的模型引用
2025-07-30 22:53:15,169 - INFO - [Algorithm] 设置客户端ID: 56
2025-07-30 22:53:15,169 - INFO - [Algorithm] 同步更新trainer的client_id: 56
2025-07-30 22:53:15,169 - INFO - [Client 56] 已更新algorithm的client_id
2025-07-30 22:53:15,169 - INFO - [Client 56] 模型初始化完成
2025-07-30 22:53:15,169 - INFO - 客户端 56 模型初始化成功
2025-07-30 22:53:15,169 - INFO - 客户端 56 异步训练任务已启动
2025-07-30 22:53:15,169 - INFO - [Client 57] 模型已放置到设备: cpu
2025-07-30 22:53:15,169 - INFO - [Client 57] 已更新trainer的模型引用
2025-07-30 22:53:15,169 - INFO - [Client 57] 已更新algorithm的模型引用
2025-07-30 22:53:15,171 - INFO - [Algorithm] 设置客户端ID: 57
2025-07-30 22:53:15,171 - INFO - [Algorithm] 同步更新trainer的client_id: 57
2025-07-30 22:53:15,171 - INFO - [Client 57] 已更新algorithm的client_id
2025-07-30 22:53:15,171 - INFO - [Client 57] 模型初始化完成
2025-07-30 22:53:15,171 - INFO - 客户端 57 模型初始化成功
2025-07-30 22:53:15,171 - INFO - 客户端 57 异步训练任务已启动
2025-07-30 22:53:15,172 - INFO - [Client 58] 模型已放置到设备: cpu
2025-07-30 22:53:15,172 - INFO - [Client 58] 已更新trainer的模型引用
2025-07-30 22:53:15,172 - INFO - [Client 58] 已更新algorithm的模型引用
2025-07-30 22:53:15,172 - INFO - [Algorithm] 设置客户端ID: 58
2025-07-30 22:53:15,172 - INFO - [Algorithm] 同步更新trainer的client_id: 58
2025-07-30 22:53:15,172 - INFO - [Client 58] 已更新algorithm的client_id
2025-07-30 22:53:15,172 - INFO - [Client 58] 模型初始化完成
2025-07-30 22:53:15,172 - INFO - 客户端 58 模型初始化成功
2025-07-30 22:53:15,172 - INFO - 客户端 58 异步训练任务已启动
2025-07-30 22:53:15,172 - INFO - [Client 59] 模型已放置到设备: cpu
2025-07-30 22:53:15,173 - INFO - [Client 59] 已更新trainer的模型引用
2025-07-30 22:53:15,173 - INFO - [Client 59] 已更新algorithm的模型引用
2025-07-30 22:53:15,173 - INFO - [Algorithm] 设置客户端ID: 59
2025-07-30 22:53:15,173 - INFO - [Algorithm] 同步更新trainer的client_id: 59
2025-07-30 22:53:15,173 - INFO - [Client 59] 已更新algorithm的client_id
2025-07-30 22:53:15,173 - INFO - [Client 59] 模型初始化完成
2025-07-30 22:53:15,173 - INFO - 客户端 59 模型初始化成功
2025-07-30 22:53:15,173 - INFO - 客户端 59 异步训练任务已启动
2025-07-30 22:53:15,173 - INFO - [Client 60] 模型已放置到设备: cpu
2025-07-30 22:53:15,173 - INFO - [Client 60] 已更新trainer的模型引用
2025-07-30 22:53:15,174 - INFO - [Client 60] 已更新algorithm的模型引用
2025-07-30 22:53:15,174 - INFO - [Algorithm] 设置客户端ID: 60
2025-07-30 22:53:15,174 - INFO - [Algorithm] 同步更新trainer的client_id: 60
2025-07-30 22:53:15,174 - INFO - [Client 60] 已更新algorithm的client_id
2025-07-30 22:53:15,174 - INFO - [Client 60] 模型初始化完成
2025-07-30 22:53:15,174 - INFO - 客户端 60 模型初始化成功
2025-07-30 22:53:15,174 - INFO - 客户端 60 异步训练任务已启动
2025-07-30 22:53:15,174 - INFO - [Client 61] 模型已放置到设备: cpu
2025-07-30 22:53:15,174 - INFO - [Client 61] 已更新trainer的模型引用
2025-07-30 22:53:15,174 - INFO - [Client 61] 已更新algorithm的模型引用
2025-07-30 22:53:15,174 - INFO - [Algorithm] 设置客户端ID: 61
2025-07-30 22:53:15,174 - INFO - [Algorithm] 同步更新trainer的client_id: 61
2025-07-30 22:53:15,174 - INFO - [Client 61] 已更新algorithm的client_id
2025-07-30 22:53:15,174 - INFO - [Client 61] 模型初始化完成
2025-07-30 22:53:15,174 - INFO - 客户端 61 模型初始化成功
2025-07-30 22:53:15,174 - INFO - 客户端 61 异步训练任务已启动
2025-07-30 22:53:15,175 - INFO - [Client 62] 模型已放置到设备: cpu
2025-07-30 22:53:15,175 - INFO - [Client 62] 已更新trainer的模型引用
2025-07-30 22:53:15,175 - INFO - [Client 62] 已更新algorithm的模型引用
2025-07-30 22:53:15,175 - INFO - [Algorithm] 设置客户端ID: 62
2025-07-30 22:53:15,175 - INFO - [Algorithm] 同步更新trainer的client_id: 62
2025-07-30 22:53:15,175 - INFO - [Client 62] 已更新algorithm的client_id
2025-07-30 22:53:15,175 - INFO - [Client 62] 模型初始化完成
2025-07-30 22:53:15,175 - INFO - 客户端 62 模型初始化成功
2025-07-30 22:53:15,175 - INFO - 客户端 62 异步训练任务已启动
2025-07-30 22:53:15,175 - INFO - [Client 63] 模型已放置到设备: cpu
2025-07-30 22:53:15,175 - INFO - [Client 63] 已更新trainer的模型引用
2025-07-30 22:53:15,175 - INFO - [Client 63] 已更新algorithm的模型引用
2025-07-30 22:53:15,177 - INFO - [Algorithm] 设置客户端ID: 63
2025-07-30 22:53:15,177 - INFO - [Algorithm] 同步更新trainer的client_id: 63
2025-07-30 22:53:15,177 - INFO - [Client 63] 已更新algorithm的client_id
2025-07-30 22:53:15,177 - INFO - [Client 63] 模型初始化完成
2025-07-30 22:53:15,177 - INFO - 客户端 63 模型初始化成功
2025-07-30 22:53:15,177 - INFO - 客户端 63 异步训练任务已启动
2025-07-30 22:53:15,177 - INFO - [Client 64] 模型已放置到设备: cpu
2025-07-30 22:53:15,177 - INFO - [Client 64] 已更新trainer的模型引用
2025-07-30 22:53:15,177 - INFO - [Client 64] 已更新algorithm的模型引用
2025-07-30 22:53:15,177 - INFO - [Algorithm] 设置客户端ID: 64
2025-07-30 22:53:15,177 - INFO - [Algorithm] 同步更新trainer的client_id: 64
2025-07-30 22:53:15,177 - INFO - [Client 64] 已更新algorithm的client_id
2025-07-30 22:53:15,177 - INFO - [Client 64] 模型初始化完成
2025-07-30 22:53:15,178 - INFO - 客户端 64 模型初始化成功
2025-07-30 22:53:15,178 - INFO - 客户端 64 异步训练任务已启动
2025-07-30 22:53:15,178 - INFO - [Client 65] 模型已放置到设备: cpu
2025-07-30 22:53:15,178 - INFO - [Client 65] 已更新trainer的模型引用
2025-07-30 22:53:15,178 - INFO - [Client 65] 已更新algorithm的模型引用
2025-07-30 22:53:15,178 - INFO - [Algorithm] 设置客户端ID: 65
2025-07-30 22:53:15,178 - INFO - [Algorithm] 同步更新trainer的client_id: 65
2025-07-30 22:53:15,178 - INFO - [Client 65] 已更新algorithm的client_id
2025-07-30 22:53:15,178 - INFO - [Client 65] 模型初始化完成
2025-07-30 22:53:15,178 - INFO - 客户端 65 模型初始化成功
2025-07-30 22:53:15,178 - INFO - 客户端 65 异步训练任务已启动
2025-07-30 22:53:15,179 - INFO - [Client 66] 模型已放置到设备: cpu
2025-07-30 22:53:15,179 - INFO - [Client 66] 已更新trainer的模型引用
2025-07-30 22:53:15,179 - INFO - [Client 66] 已更新algorithm的模型引用
2025-07-30 22:53:15,179 - INFO - [Algorithm] 设置客户端ID: 66
2025-07-30 22:53:15,179 - INFO - [Algorithm] 同步更新trainer的client_id: 66
2025-07-30 22:53:15,179 - INFO - [Client 66] 已更新algorithm的client_id
2025-07-30 22:53:15,179 - INFO - [Client 66] 模型初始化完成
2025-07-30 22:53:15,179 - INFO - 客户端 66 模型初始化成功
2025-07-30 22:53:15,179 - INFO - 客户端 66 异步训练任务已启动
2025-07-30 22:53:15,179 - INFO - [Client 67] 模型已放置到设备: cpu
2025-07-30 22:53:15,179 - INFO - [Client 67] 已更新trainer的模型引用
2025-07-30 22:53:15,179 - INFO - [Client 67] 已更新algorithm的模型引用
2025-07-30 22:53:15,179 - INFO - [Algorithm] 设置客户端ID: 67
2025-07-30 22:53:15,179 - INFO - [Algorithm] 同步更新trainer的client_id: 67
2025-07-30 22:53:15,180 - INFO - [Client 67] 已更新algorithm的client_id
2025-07-30 22:53:15,180 - INFO - [Client 67] 模型初始化完成
2025-07-30 22:53:15,180 - INFO - 客户端 67 模型初始化成功
2025-07-30 22:53:15,180 - INFO - 客户端 67 异步训练任务已启动
2025-07-30 22:53:15,180 - INFO - [Client 68] 模型已放置到设备: cpu
2025-07-30 22:53:15,180 - INFO - [Client 68] 已更新trainer的模型引用
2025-07-30 22:53:15,180 - INFO - [Client 68] 已更新algorithm的模型引用
2025-07-30 22:53:15,180 - INFO - [Algorithm] 设置客户端ID: 68
2025-07-30 22:53:15,180 - INFO - [Algorithm] 同步更新trainer的client_id: 68
2025-07-30 22:53:15,180 - INFO - [Client 68] 已更新algorithm的client_id
2025-07-30 22:53:15,180 - INFO - [Client 68] 模型初始化完成
2025-07-30 22:53:15,180 - INFO - 客户端 68 模型初始化成功
2025-07-30 22:53:15,180 - INFO - 客户端 68 异步训练任务已启动
2025-07-30 22:53:15,181 - INFO - [Client 69] 模型已放置到设备: cpu
2025-07-30 22:53:15,181 - INFO - [Client 69] 已更新trainer的模型引用
2025-07-30 22:53:15,181 - INFO - [Client 69] 已更新algorithm的模型引用
2025-07-30 22:53:15,181 - INFO - [Algorithm] 设置客户端ID: 69
2025-07-30 22:53:15,181 - INFO - [Algorithm] 同步更新trainer的client_id: 69
2025-07-30 22:53:15,181 - INFO - [Client 69] 已更新algorithm的client_id
2025-07-30 22:53:15,181 - INFO - [Client 69] 模型初始化完成
2025-07-30 22:53:15,181 - INFO - 客户端 69 模型初始化成功
2025-07-30 22:53:15,181 - INFO - 客户端 69 异步训练任务已启动
2025-07-30 22:53:15,181 - INFO - [Client 70] 模型已放置到设备: cpu
2025-07-30 22:53:15,181 - INFO - [Client 70] 已更新trainer的模型引用
2025-07-30 22:53:15,182 - INFO - [Client 70] 已更新algorithm的模型引用
2025-07-30 22:53:15,182 - INFO - [Algorithm] 设置客户端ID: 70
2025-07-30 22:53:15,182 - INFO - [Algorithm] 同步更新trainer的client_id: 70
2025-07-30 22:53:15,182 - INFO - [Client 70] 已更新algorithm的client_id
2025-07-30 22:53:15,182 - INFO - [Client 70] 模型初始化完成
2025-07-30 22:53:15,182 - INFO - 客户端 70 模型初始化成功
2025-07-30 22:53:15,182 - INFO - 客户端 70 异步训练任务已启动
2025-07-30 22:53:15,182 - INFO - [Client 71] 模型已放置到设备: cpu
2025-07-30 22:53:15,182 - INFO - [Client 71] 已更新trainer的模型引用
2025-07-30 22:53:15,182 - INFO - [Client 71] 已更新algorithm的模型引用
2025-07-30 22:53:15,182 - INFO - [Algorithm] 设置客户端ID: 71
2025-07-30 22:53:15,182 - INFO - [Algorithm] 同步更新trainer的client_id: 71
2025-07-30 22:53:15,182 - INFO - [Client 71] 已更新algorithm的client_id
2025-07-30 22:53:15,182 - INFO - [Client 71] 模型初始化完成
2025-07-30 22:53:15,183 - INFO - 客户端 71 模型初始化成功
2025-07-30 22:53:15,183 - INFO - 客户端 71 异步训练任务已启动
2025-07-30 22:53:15,183 - INFO - [Client 72] 模型已放置到设备: cpu
2025-07-30 22:53:15,183 - INFO - [Client 72] 已更新trainer的模型引用
2025-07-30 22:53:15,183 - INFO - [Client 72] 已更新algorithm的模型引用
2025-07-30 22:53:15,183 - INFO - [Algorithm] 设置客户端ID: 72
2025-07-30 22:53:15,183 - INFO - [Algorithm] 同步更新trainer的client_id: 72
2025-07-30 22:53:15,183 - INFO - [Client 72] 已更新algorithm的client_id
2025-07-30 22:53:15,183 - INFO - [Client 72] 模型初始化完成
2025-07-30 22:53:15,183 - INFO - 客户端 72 模型初始化成功
2025-07-30 22:53:15,183 - INFO - 客户端 72 异步训练任务已启动
2025-07-30 22:53:15,184 - INFO - [Client 73] 模型已放置到设备: cpu
2025-07-30 22:53:15,184 - INFO - [Client 73] 已更新trainer的模型引用
2025-07-30 22:53:15,184 - INFO - [Client 73] 已更新algorithm的模型引用
2025-07-30 22:53:15,184 - INFO - [Algorithm] 设置客户端ID: 73
2025-07-30 22:53:15,184 - INFO - [Algorithm] 同步更新trainer的client_id: 73
2025-07-30 22:53:15,184 - INFO - [Client 73] 已更新algorithm的client_id
2025-07-30 22:53:15,184 - INFO - [Client 73] 模型初始化完成
2025-07-30 22:53:15,184 - INFO - 客户端 73 模型初始化成功
2025-07-30 22:53:15,184 - INFO - 客户端 73 异步训练任务已启动
2025-07-30 22:53:15,184 - INFO - [Client 74] 模型已放置到设备: cpu
2025-07-30 22:53:15,184 - INFO - [Client 74] 已更新trainer的模型引用
2025-07-30 22:53:15,184 - INFO - [Client 74] 已更新algorithm的模型引用
2025-07-30 22:53:15,184 - INFO - [Algorithm] 设置客户端ID: 74
2025-07-30 22:53:15,184 - INFO - [Algorithm] 同步更新trainer的client_id: 74
2025-07-30 22:53:15,185 - INFO - [Client 74] 已更新algorithm的client_id
2025-07-30 22:53:15,185 - INFO - [Client 74] 模型初始化完成
2025-07-30 22:53:15,185 - INFO - 客户端 74 模型初始化成功
2025-07-30 22:53:15,185 - INFO - 客户端 74 异步训练任务已启动
2025-07-30 22:53:15,185 - INFO - [Client 75] 模型已放置到设备: cpu
2025-07-30 22:53:15,185 - INFO - [Client 75] 已更新trainer的模型引用
2025-07-30 22:53:15,185 - INFO - [Client 75] 已更新algorithm的模型引用
2025-07-30 22:53:15,185 - INFO - [Algorithm] 设置客户端ID: 75
2025-07-30 22:53:15,185 - INFO - [Algorithm] 同步更新trainer的client_id: 75
2025-07-30 22:53:15,185 - INFO - [Client 75] 已更新algorithm的client_id
2025-07-30 22:53:15,185 - INFO - [Client 75] 模型初始化完成
2025-07-30 22:53:15,185 - INFO - 客户端 75 模型初始化成功
2025-07-30 22:53:15,186 - INFO - 客户端 75 异步训练任务已启动
2025-07-30 22:53:15,186 - INFO - [Client 76] 模型已放置到设备: cpu
2025-07-30 22:53:15,186 - INFO - [Client 76] 已更新trainer的模型引用
2025-07-30 22:53:15,186 - INFO - [Client 76] 已更新algorithm的模型引用
2025-07-30 22:53:15,186 - INFO - [Algorithm] 设置客户端ID: 76
2025-07-30 22:53:15,186 - INFO - [Algorithm] 同步更新trainer的client_id: 76
2025-07-30 22:53:15,186 - INFO - [Client 76] 已更新algorithm的client_id
2025-07-30 22:53:15,186 - INFO - [Client 76] 模型初始化完成
2025-07-30 22:53:15,186 - INFO - 客户端 76 模型初始化成功
2025-07-30 22:53:15,186 - INFO - 客户端 76 异步训练任务已启动
2025-07-30 22:53:15,187 - INFO - [Client 77] 模型已放置到设备: cpu
2025-07-30 22:53:15,187 - INFO - [Client 77] 已更新trainer的模型引用
2025-07-30 22:53:15,187 - INFO - [Client 77] 已更新algorithm的模型引用
2025-07-30 22:53:15,187 - INFO - [Algorithm] 设置客户端ID: 77
2025-07-30 22:53:15,187 - INFO - [Algorithm] 同步更新trainer的client_id: 77
2025-07-30 22:53:15,187 - INFO - [Client 77] 已更新algorithm的client_id
2025-07-30 22:53:15,187 - INFO - [Client 77] 模型初始化完成
2025-07-30 22:53:15,187 - INFO - 客户端 77 模型初始化成功
2025-07-30 22:53:15,187 - INFO - 客户端 77 异步训练任务已启动
2025-07-30 22:53:15,187 - INFO - [Client 78] 模型已放置到设备: cpu
2025-07-30 22:53:15,187 - INFO - [Client 78] 已更新trainer的模型引用
2025-07-30 22:53:15,187 - INFO - [Client 78] 已更新algorithm的模型引用
2025-07-30 22:53:15,188 - INFO - [Algorithm] 设置客户端ID: 78
2025-07-30 22:53:15,188 - INFO - [Algorithm] 同步更新trainer的client_id: 78
2025-07-30 22:53:15,188 - INFO - [Client 78] 已更新algorithm的client_id
2025-07-30 22:53:15,188 - INFO - [Client 78] 模型初始化完成
2025-07-30 22:53:15,188 - INFO - 客户端 78 模型初始化成功
2025-07-30 22:53:15,188 - INFO - 客户端 78 异步训练任务已启动
2025-07-30 22:53:15,188 - INFO - [Client 79] 模型已放置到设备: cpu
2025-07-30 22:53:15,188 - INFO - [Client 79] 已更新trainer的模型引用
2025-07-30 22:53:15,188 - INFO - [Client 79] 已更新algorithm的模型引用
2025-07-30 22:53:15,188 - INFO - [Algorithm] 设置客户端ID: 79
2025-07-30 22:53:15,188 - INFO - [Algorithm] 同步更新trainer的client_id: 79
2025-07-30 22:53:15,188 - INFO - [Client 79] 已更新algorithm的client_id
2025-07-30 22:53:15,188 - INFO - [Client 79] 模型初始化完成
2025-07-30 22:53:15,188 - INFO - 客户端 79 模型初始化成功
2025-07-30 22:53:15,188 - INFO - 客户端 79 异步训练任务已启动
2025-07-30 22:53:15,189 - INFO - [Client 80] 模型已放置到设备: cpu
2025-07-30 22:53:15,189 - INFO - [Client 80] 已更新trainer的模型引用
2025-07-30 22:53:15,189 - INFO - [Client 80] 已更新algorithm的模型引用
2025-07-30 22:53:15,189 - INFO - [Algorithm] 设置客户端ID: 80
2025-07-30 22:53:15,189 - INFO - [Algorithm] 同步更新trainer的client_id: 80
2025-07-30 22:53:15,189 - INFO - [Client 80] 已更新algorithm的client_id
2025-07-30 22:53:15,189 - INFO - [Client 80] 模型初始化完成
2025-07-30 22:53:15,189 - INFO - 客户端 80 模型初始化成功
2025-07-30 22:53:15,189 - INFO - 客户端 80 异步训练任务已启动
2025-07-30 22:53:15,189 - INFO - [Client 81] 模型已放置到设备: cpu
2025-07-30 22:53:15,189 - INFO - [Client 81] 已更新trainer的模型引用
2025-07-30 22:53:15,189 - INFO - [Client 81] 已更新algorithm的模型引用
2025-07-30 22:53:15,190 - INFO - [Algorithm] 设置客户端ID: 81
2025-07-30 22:53:15,190 - INFO - [Algorithm] 同步更新trainer的client_id: 81
2025-07-30 22:53:15,190 - INFO - [Client 81] 已更新algorithm的client_id
2025-07-30 22:53:15,190 - INFO - [Client 81] 模型初始化完成
2025-07-30 22:53:15,190 - INFO - 客户端 81 模型初始化成功
2025-07-30 22:53:15,190 - INFO - 客户端 81 异步训练任务已启动
2025-07-30 22:53:15,190 - INFO - [Client 82] 模型已放置到设备: cpu
2025-07-30 22:53:15,190 - INFO - [Client 82] 已更新trainer的模型引用
2025-07-30 22:53:15,190 - INFO - [Client 82] 已更新algorithm的模型引用
2025-07-30 22:53:15,190 - INFO - [Algorithm] 设置客户端ID: 82
2025-07-30 22:53:15,190 - INFO - [Algorithm] 同步更新trainer的client_id: 82
2025-07-30 22:53:15,190 - INFO - [Client 82] 已更新algorithm的client_id
2025-07-30 22:53:15,190 - INFO - [Client 82] 模型初始化完成
2025-07-30 22:53:15,190 - INFO - 客户端 82 模型初始化成功
2025-07-30 22:53:15,191 - INFO - 客户端 82 异步训练任务已启动
2025-07-30 22:53:15,191 - INFO - [Client 83] 模型已放置到设备: cpu
2025-07-30 22:53:15,191 - INFO - [Client 83] 已更新trainer的模型引用
2025-07-30 22:53:15,191 - INFO - [Client 83] 已更新algorithm的模型引用
2025-07-30 22:53:15,191 - INFO - [Algorithm] 设置客户端ID: 83
2025-07-30 22:53:15,191 - INFO - [Algorithm] 同步更新trainer的client_id: 83
2025-07-30 22:53:15,191 - INFO - [Client 83] 已更新algorithm的client_id
2025-07-30 22:53:15,191 - INFO - [Client 83] 模型初始化完成
2025-07-30 22:53:15,191 - INFO - 客户端 83 模型初始化成功
2025-07-30 22:53:15,191 - INFO - 客户端 83 异步训练任务已启动
2025-07-30 22:53:15,191 - INFO - [Client 84] 模型已放置到设备: cpu
2025-07-30 22:53:15,191 - INFO - [Client 84] 已更新trainer的模型引用
2025-07-30 22:53:15,191 - INFO - [Client 84] 已更新algorithm的模型引用
2025-07-30 22:53:15,191 - INFO - [Algorithm] 设置客户端ID: 84
2025-07-30 22:53:15,192 - INFO - [Algorithm] 同步更新trainer的client_id: 84
2025-07-30 22:53:15,192 - INFO - [Client 84] 已更新algorithm的client_id
2025-07-30 22:53:15,192 - INFO - [Client 84] 模型初始化完成
2025-07-30 22:53:15,192 - INFO - 客户端 84 模型初始化成功
2025-07-30 22:53:15,192 - INFO - 客户端 84 异步训练任务已启动
2025-07-30 22:53:15,192 - INFO - [Client 85] 模型已放置到设备: cpu
2025-07-30 22:53:15,192 - INFO - [Client 85] 已更新trainer的模型引用
2025-07-30 22:53:15,192 - INFO - [Client 85] 已更新algorithm的模型引用
2025-07-30 22:53:15,192 - INFO - [Algorithm] 设置客户端ID: 85
2025-07-30 22:53:15,192 - INFO - [Algorithm] 同步更新trainer的client_id: 85
2025-07-30 22:53:15,192 - INFO - [Client 85] 已更新algorithm的client_id
2025-07-30 22:53:15,193 - INFO - [Client 85] 模型初始化完成
2025-07-30 22:53:15,193 - INFO - 客户端 85 模型初始化成功
2025-07-30 22:53:15,193 - INFO - 客户端 85 异步训练任务已启动
2025-07-30 22:53:15,193 - INFO - [Client 86] 模型已放置到设备: cpu
2025-07-30 22:53:15,193 - INFO - [Client 86] 已更新trainer的模型引用
2025-07-30 22:53:15,193 - INFO - [Client 86] 已更新algorithm的模型引用
2025-07-30 22:53:15,193 - INFO - [Algorithm] 设置客户端ID: 86
2025-07-30 22:53:15,193 - INFO - [Algorithm] 同步更新trainer的client_id: 86
2025-07-30 22:53:15,193 - INFO - [Client 86] 已更新algorithm的client_id
2025-07-30 22:53:15,193 - INFO - [Client 86] 模型初始化完成
2025-07-30 22:53:15,193 - INFO - 客户端 86 模型初始化成功
2025-07-30 22:53:15,193 - INFO - 客户端 86 异步训练任务已启动
2025-07-30 22:53:15,194 - INFO - [Client 87] 模型已放置到设备: cpu
2025-07-30 22:53:15,194 - INFO - [Client 87] 已更新trainer的模型引用
2025-07-30 22:53:15,194 - INFO - [Client 87] 已更新algorithm的模型引用
2025-07-30 22:53:15,194 - INFO - [Algorithm] 设置客户端ID: 87
2025-07-30 22:53:15,194 - INFO - [Algorithm] 同步更新trainer的client_id: 87
2025-07-30 22:53:15,194 - INFO - [Client 87] 已更新algorithm的client_id
2025-07-30 22:53:15,194 - INFO - [Client 87] 模型初始化完成
2025-07-30 22:53:15,194 - INFO - 客户端 87 模型初始化成功
2025-07-30 22:53:15,194 - INFO - 客户端 87 异步训练任务已启动
2025-07-30 22:53:15,194 - INFO - [Client 88] 模型已放置到设备: cpu
2025-07-30 22:53:15,194 - INFO - [Client 88] 已更新trainer的模型引用
2025-07-30 22:53:15,194 - INFO - [Client 88] 已更新algorithm的模型引用
2025-07-30 22:53:15,194 - INFO - [Algorithm] 设置客户端ID: 88
2025-07-30 22:53:15,194 - INFO - [Algorithm] 同步更新trainer的client_id: 88
2025-07-30 22:53:15,195 - INFO - [Client 88] 已更新algorithm的client_id
2025-07-30 22:53:15,195 - INFO - [Client 88] 模型初始化完成
2025-07-30 22:53:15,195 - INFO - 客户端 88 模型初始化成功
2025-07-30 22:53:15,195 - INFO - 客户端 88 异步训练任务已启动
2025-07-30 22:53:15,195 - INFO - [Client 89] 模型已放置到设备: cpu
2025-07-30 22:53:15,195 - INFO - [Client 89] 已更新trainer的模型引用
2025-07-30 22:53:15,195 - INFO - [Client 89] 已更新algorithm的模型引用
2025-07-30 22:53:15,195 - INFO - [Algorithm] 设置客户端ID: 89
2025-07-30 22:53:15,195 - INFO - [Algorithm] 同步更新trainer的client_id: 89
2025-07-30 22:53:15,195 - INFO - [Client 89] 已更新algorithm的client_id
2025-07-30 22:53:15,195 - INFO - [Client 89] 模型初始化完成
2025-07-30 22:53:15,195 - INFO - 客户端 89 模型初始化成功
2025-07-30 22:53:15,196 - INFO - 客户端 89 异步训练任务已启动
2025-07-30 22:53:15,196 - INFO - [Client 90] 模型已放置到设备: cpu
2025-07-30 22:53:15,196 - INFO - [Client 90] 已更新trainer的模型引用
2025-07-30 22:53:15,196 - INFO - [Client 90] 已更新algorithm的模型引用
2025-07-30 22:53:15,196 - INFO - [Algorithm] 设置客户端ID: 90
2025-07-30 22:53:15,196 - INFO - [Algorithm] 同步更新trainer的client_id: 90
2025-07-30 22:53:15,196 - INFO - [Client 90] 已更新algorithm的client_id
2025-07-30 22:53:15,196 - INFO - [Client 90] 模型初始化完成
2025-07-30 22:53:15,196 - INFO - 客户端 90 模型初始化成功
2025-07-30 22:53:15,196 - INFO - 客户端 90 异步训练任务已启动
2025-07-30 22:53:15,197 - INFO - [Client 91] 模型已放置到设备: cpu
2025-07-30 22:53:15,197 - INFO - [Client 91] 已更新trainer的模型引用
2025-07-30 22:53:15,197 - INFO - [Client 91] 已更新algorithm的模型引用
2025-07-30 22:53:15,197 - INFO - [Algorithm] 设置客户端ID: 91
2025-07-30 22:53:15,197 - INFO - [Algorithm] 同步更新trainer的client_id: 91
2025-07-30 22:53:15,197 - INFO - [Client 91] 已更新algorithm的client_id
2025-07-30 22:53:15,197 - INFO - [Client 91] 模型初始化完成
2025-07-30 22:53:15,197 - INFO - 客户端 91 模型初始化成功
2025-07-30 22:53:15,197 - INFO - 客户端 91 异步训练任务已启动
2025-07-30 22:53:15,197 - INFO - [Client 92] 模型已放置到设备: cpu
2025-07-30 22:53:15,197 - INFO - [Client 92] 已更新trainer的模型引用
2025-07-30 22:53:15,197 - INFO - [Client 92] 已更新algorithm的模型引用
2025-07-30 22:53:15,197 - INFO - [Algorithm] 设置客户端ID: 92
2025-07-30 22:53:15,197 - INFO - [Algorithm] 同步更新trainer的client_id: 92
2025-07-30 22:53:15,198 - INFO - [Client 92] 已更新algorithm的client_id
2025-07-30 22:53:15,198 - INFO - [Client 92] 模型初始化完成
2025-07-30 22:53:15,198 - INFO - 客户端 92 模型初始化成功
2025-07-30 22:53:15,198 - INFO - 客户端 92 异步训练任务已启动
2025-07-30 22:53:15,198 - INFO - [Client 93] 模型已放置到设备: cpu
2025-07-30 22:53:15,198 - INFO - [Client 93] 已更新trainer的模型引用
2025-07-30 22:53:15,198 - INFO - [Client 93] 已更新algorithm的模型引用
2025-07-30 22:53:15,198 - INFO - [Algorithm] 设置客户端ID: 93
2025-07-30 22:53:15,198 - INFO - [Algorithm] 同步更新trainer的client_id: 93
2025-07-30 22:53:15,198 - INFO - [Client 93] 已更新algorithm的client_id
2025-07-30 22:53:15,199 - INFO - [Client 93] 模型初始化完成
2025-07-30 22:53:15,199 - INFO - 客户端 93 模型初始化成功
2025-07-30 22:53:15,199 - INFO - 客户端 93 异步训练任务已启动
2025-07-30 22:53:15,199 - INFO - [Client 94] 模型已放置到设备: cpu
2025-07-30 22:53:15,199 - INFO - [Client 94] 已更新trainer的模型引用
2025-07-30 22:53:15,199 - INFO - [Client 94] 已更新algorithm的模型引用
2025-07-30 22:53:15,199 - INFO - [Algorithm] 设置客户端ID: 94
2025-07-30 22:53:15,199 - INFO - [Algorithm] 同步更新trainer的client_id: 94
2025-07-30 22:53:15,201 - INFO - [Client 94] 已更新algorithm的client_id
2025-07-30 22:53:15,201 - INFO - [Client 94] 模型初始化完成
2025-07-30 22:53:15,201 - INFO - 客户端 94 模型初始化成功
2025-07-30 22:53:15,201 - INFO - 客户端 94 异步训练任务已启动
2025-07-30 22:53:15,201 - INFO - [Client 95] 模型已放置到设备: cpu
2025-07-30 22:53:15,201 - INFO - [Client 95] 已更新trainer的模型引用
2025-07-30 22:53:15,201 - INFO - [Client 95] 已更新algorithm的模型引用
2025-07-30 22:53:15,201 - INFO - [Algorithm] 设置客户端ID: 95
2025-07-30 22:53:15,201 - INFO - [Algorithm] 同步更新trainer的client_id: 95
2025-07-30 22:53:15,201 - INFO - [Client 95] 已更新algorithm的client_id
2025-07-30 22:53:15,201 - INFO - [Client 95] 模型初始化完成
2025-07-30 22:53:15,202 - INFO - 客户端 95 模型初始化成功
2025-07-30 22:53:15,202 - INFO - 客户端 95 异步训练任务已启动
2025-07-30 22:53:15,202 - INFO - [Client 96] 模型已放置到设备: cpu
2025-07-30 22:53:15,202 - INFO - [Client 96] 已更新trainer的模型引用
2025-07-30 22:53:15,202 - INFO - [Client 96] 已更新algorithm的模型引用
2025-07-30 22:53:15,202 - INFO - [Algorithm] 设置客户端ID: 96
2025-07-30 22:53:15,202 - INFO - [Algorithm] 同步更新trainer的client_id: 96
2025-07-30 22:53:15,202 - INFO - [Client 96] 已更新algorithm的client_id
2025-07-30 22:53:15,202 - INFO - [Client 96] 模型初始化完成
2025-07-30 22:53:15,202 - INFO - 客户端 96 模型初始化成功
2025-07-30 22:53:15,202 - INFO - 客户端 96 异步训练任务已启动
2025-07-30 22:53:15,203 - INFO - [Client 97] 模型已放置到设备: cpu
2025-07-30 22:53:15,203 - INFO - [Client 97] 已更新trainer的模型引用
2025-07-30 22:53:15,203 - INFO - [Client 97] 已更新algorithm的模型引用
2025-07-30 22:53:15,203 - INFO - [Algorithm] 设置客户端ID: 97
2025-07-30 22:53:15,203 - INFO - [Algorithm] 同步更新trainer的client_id: 97
2025-07-30 22:53:15,203 - INFO - [Client 97] 已更新algorithm的client_id
2025-07-30 22:53:15,203 - INFO - [Client 97] 模型初始化完成
2025-07-30 22:53:15,203 - INFO - 客户端 97 模型初始化成功
2025-07-30 22:53:15,203 - INFO - 客户端 97 异步训练任务已启动
2025-07-30 22:53:15,204 - INFO - [Client 98] 模型已放置到设备: cpu
2025-07-30 22:53:15,204 - INFO - [Client 98] 已更新trainer的模型引用
2025-07-30 22:53:15,204 - INFO - [Client 98] 已更新algorithm的模型引用
2025-07-30 22:53:15,204 - INFO - [Algorithm] 设置客户端ID: 98
2025-07-30 22:53:15,204 - INFO - [Algorithm] 同步更新trainer的client_id: 98
2025-07-30 22:53:15,204 - INFO - [Client 98] 已更新algorithm的client_id
2025-07-30 22:53:15,204 - INFO - [Client 98] 模型初始化完成
2025-07-30 22:53:15,204 - INFO - 客户端 98 模型初始化成功
2025-07-30 22:53:15,204 - INFO - 客户端 98 异步训练任务已启动
2025-07-30 22:53:15,205 - INFO - [Client 99] 模型已放置到设备: cpu
2025-07-30 22:53:15,205 - INFO - [Client 99] 已更新trainer的模型引用
2025-07-30 22:53:15,205 - INFO - [Client 99] 已更新algorithm的模型引用
2025-07-30 22:53:15,205 - INFO - [Algorithm] 设置客户端ID: 99
2025-07-30 22:53:15,205 - INFO - [Algorithm] 同步更新trainer的client_id: 99
2025-07-30 22:53:15,205 - INFO - [Client 99] 已更新algorithm的client_id
2025-07-30 22:53:15,205 - INFO - [Client 99] 模型初始化完成
2025-07-30 22:53:15,205 - INFO - 客户端 99 模型初始化成功
2025-07-30 22:53:15,205 - INFO - 客户端 99 异步训练任务已启动
2025-07-30 22:53:15,205 - INFO - [Client 100] 模型已放置到设备: cpu
2025-07-30 22:53:15,205 - INFO - [Client 100] 已更新trainer的模型引用
2025-07-30 22:53:15,206 - INFO - [Client 100] 已更新algorithm的模型引用
2025-07-30 22:53:15,206 - INFO - [Algorithm] 设置客户端ID: 100
2025-07-30 22:53:15,206 - INFO - [Algorithm] 同步更新trainer的client_id: 100
2025-07-30 22:53:15,206 - INFO - [Client 100] 已更新algorithm的client_id
2025-07-30 22:53:15,206 - INFO - [Client 100] 模型初始化完成
2025-07-30 22:53:15,206 - INFO - 客户端 100 模型初始化成功
2025-07-30 22:53:15,206 - INFO - 客户端 100 异步训练任务已启动
2025-07-30 22:53:15,210 - INFO - 服务器主循环任务已启动: <Task pending name='Task-101' coro=<Server.run() running at D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py:1314>>
2025-07-30 22:53:15,213 - INFO - Starting a server at address 127.0.0.1 and port 8000.
2025-07-30 22:53:15,391 - INFO - [Server #36944] 开始训练，共有 100 个客户端，每轮最多聚合 3 个客户端
2025-07-30 22:53:15,392 - INFO - 总训练轮次: 500
2025-07-30 22:53:15,392 - INFO - 🚀 开始第 1 轮训练（目标：500 轮）
2025-07-30 22:53:15,394 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:53:15,395 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:53:15,400 - INFO - 客户端 94 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:15,401 - INFO - 向客户端 94 发送全局模型 - 参数数量: 74
2025-07-30 22:53:15,401 - INFO - [Client 94] 收到直接传入的模型权重
2025-07-30 22:53:15,403 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:15,404 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:15,404 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:15,450 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:15,450 - INFO - [Client 94] 成功接收并加载模型
2025-07-30 22:53:15,450 - INFO - 客户端 94 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:15,450 - INFO - [Trainer.reset_staleness] 客户端 94 陈旧度重置
2025-07-30 22:53:15,450 - INFO - 重置客户端 94 的陈旧度
2025-07-30 22:53:15,450 - INFO - 成功发送全局模型到客户端 94
2025-07-30 22:53:15,450 - INFO - 客户端 94 开始本地训练
2025-07-30 22:53:15,452 - INFO - [客户端类型: Client, ID: 94] 准备开始训练
2025-07-30 22:53:15,452 - INFO - [客户端 94] 使用的训练器 client_id: 94
2025-07-30 22:53:15,452 - INFO - [Client 94] 开始验证训练集
2025-07-30 22:53:15,531 - INFO - [Client 94] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:15,531 - INFO - [Client 94] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:15,532 - INFO - [Trainer 94] 开始训练
2025-07-30 22:53:15,532 - INFO - [Trainer 94] 训练集大小: 300
2025-07-30 22:53:15,533 - INFO - [Trainer 94] 模型已移至设备: cpu
2025-07-30 22:53:15,533 - INFO - [Trainer 94] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:15,547 - INFO - [Trainer 94] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:15,547 - INFO - [Trainer 94] 开始训练 5 个epoch
2025-07-30 22:53:15,548 - INFO - [Trainer 94] 开始第 1/5 个epoch
2025-07-30 22:53:15,640 - INFO - [Trainer 94] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4502, y=[4, 4, 5, 5, 4]
2025-07-30 22:53:15,640 - INFO - [Trainer 94] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:15,646 - INFO - [Trainer 94] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:15,656 - INFO - 客户端 65 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:15,657 - INFO - 向客户端 65 发送全局模型 - 参数数量: 74
2025-07-30 22:53:15,657 - INFO - [Client 65] 收到直接传入的模型权重
2025-07-30 22:53:15,657 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:15,657 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:15,657 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:15,660 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:15,660 - INFO - [Client 65] 成功接收并加载模型
2025-07-30 22:53:15,660 - INFO - 客户端 65 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:15,660 - INFO - [Trainer.reset_staleness] 客户端 65 陈旧度重置
2025-07-30 22:53:15,660 - INFO - 重置客户端 65 的陈旧度
2025-07-30 22:53:15,660 - INFO - 成功发送全局模型到客户端 65
2025-07-30 22:53:15,660 - INFO - 客户端 65 开始本地训练
2025-07-30 22:53:15,660 - INFO - [客户端类型: Client, ID: 65] 准备开始训练
2025-07-30 22:53:15,660 - INFO - [客户端 65] 使用的训练器 client_id: 65
2025-07-30 22:53:15,661 - INFO - [Client 65] 开始验证训练集
2025-07-30 22:53:15,661 - INFO - [Client 65] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:15,661 - INFO - [Client 65] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:15,661 - INFO - [Trainer 65] 开始训练
2025-07-30 22:53:15,661 - INFO - [Trainer 65] 训练集大小: 300
2025-07-30 22:53:15,661 - INFO - [Trainer 65] 模型已移至设备: cpu
2025-07-30 22:53:15,662 - INFO - [Trainer 65] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:15,662 - INFO - [Trainer 65] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:15,662 - INFO - [Trainer 65] 开始训练 5 个epoch
2025-07-30 22:53:15,662 - INFO - [Trainer 65] 开始第 1/5 个epoch
2025-07-30 22:53:15,669 - INFO - [Trainer 65] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1557, y=[3, 3, 8, 3, 3]
2025-07-30 22:53:15,669 - INFO - [Trainer 65] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:15,674 - INFO - [Trainer 65] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:15,674 - INFO - 客户端 33 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:15,674 - INFO - 向客户端 33 发送全局模型 - 参数数量: 74
2025-07-30 22:53:15,674 - INFO - [Client 33] 收到直接传入的模型权重
2025-07-30 22:53:15,674 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:15,674 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:15,674 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:15,699 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:15,699 - INFO - [Client 33] 成功接收并加载模型
2025-07-30 22:53:15,699 - INFO - 客户端 33 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:15,700 - INFO - [Trainer.reset_staleness] 客户端 33 陈旧度重置
2025-07-30 22:53:15,700 - INFO - 重置客户端 33 的陈旧度
2025-07-30 22:53:15,700 - INFO - 成功发送全局模型到客户端 33
2025-07-30 22:53:15,700 - INFO - 客户端 33 开始本地训练
2025-07-30 22:53:15,700 - INFO - [客户端类型: Client, ID: 33] 准备开始训练
2025-07-30 22:53:15,700 - INFO - [客户端 33] 使用的训练器 client_id: 33
2025-07-30 22:53:15,700 - INFO - [Client 33] 开始验证训练集
2025-07-30 22:53:15,702 - INFO - [Client 33] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:15,702 - INFO - [Client 33] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:15,703 - INFO - [Trainer 33] 开始训练
2025-07-30 22:53:15,703 - INFO - [Trainer 33] 训练集大小: 300
2025-07-30 22:53:15,703 - INFO - [Trainer 33] 模型已移至设备: cpu
2025-07-30 22:53:15,703 - INFO - [Trainer 33] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:15,703 - INFO - [Trainer 33] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:15,704 - INFO - [Trainer 33] 开始训练 5 个epoch
2025-07-30 22:53:15,704 - INFO - [Trainer 33] 开始第 1/5 个epoch
2025-07-30 22:53:15,725 - INFO - [Trainer 33] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3817, y=[4, 4, 4, 9, 9]
2025-07-30 22:53:15,725 - INFO - [Trainer 33] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:15,744 - INFO - [Trainer 33] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:15,745 - INFO - [Trainer 94] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4500
2025-07-30 22:53:15,745 - INFO - [Trainer 94] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:15,745 - INFO - [Trainer 94] 标签样本: [5, 4, 4, 4, 5]
2025-07-30 22:53:16,051 - INFO - [Trainer 94] Batch 0, Loss: 2.0199
2025-07-30 22:53:16,353 - INFO - [Trainer 65] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2708
2025-07-30 22:53:16,353 - INFO - [Trainer 65] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:16,354 - INFO - [Trainer 65] 标签样本: [3, 8, 8, 3, 8]
2025-07-30 22:53:16,414 - INFO - [Trainer 65] Batch 0, Loss: 2.1489
2025-07-30 22:53:16,553 - INFO - [Trainer 33] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3984
2025-07-30 22:53:16,554 - INFO - [Trainer 33] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:16,554 - INFO - [Trainer 33] 标签样本: [4, 7, 9, 4, 4]
2025-07-30 22:53:16,612 - INFO - [Trainer 33] Batch 0, Loss: 2.1174
2025-07-30 22:53:16,775 - INFO - 客户端 34 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:16,776 - INFO - 向客户端 34 发送全局模型 - 参数数量: 74
2025-07-30 22:53:16,776 - INFO - [Client 34] 收到直接传入的模型权重
2025-07-30 22:53:16,776 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:16,776 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:16,776 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:16,810 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:16,810 - INFO - [Client 34] 成功接收并加载模型
2025-07-30 22:53:16,810 - INFO - 客户端 34 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:16,810 - INFO - [Trainer.reset_staleness] 客户端 34 陈旧度重置
2025-07-30 22:53:16,811 - INFO - 重置客户端 34 的陈旧度
2025-07-30 22:53:16,811 - INFO - 成功发送全局模型到客户端 34
2025-07-30 22:53:16,811 - INFO - 客户端 34 开始本地训练
2025-07-30 22:53:16,811 - INFO - [客户端类型: Client, ID: 34] 准备开始训练
2025-07-30 22:53:16,811 - INFO - [客户端 34] 使用的训练器 client_id: 34
2025-07-30 22:53:16,811 - INFO - [Client 34] 开始验证训练集
2025-07-30 22:53:16,813 - INFO - [Client 34] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:16,813 - INFO - [Client 34] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:16,814 - INFO - [Trainer 34] 开始训练
2025-07-30 22:53:16,814 - INFO - [Trainer 34] 训练集大小: 300
2025-07-30 22:53:16,815 - INFO - [Trainer 34] 模型已移至设备: cpu
2025-07-30 22:53:16,815 - INFO - [Trainer 34] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:16,815 - INFO - [Trainer 34] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:16,816 - INFO - [Trainer 34] 开始训练 5 个epoch
2025-07-30 22:53:16,816 - INFO - [Trainer 34] 开始第 1/5 个epoch
2025-07-30 22:53:16,856 - INFO - [Trainer 34] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4282, y=[6, 7, 7, 7, 7]
2025-07-30 22:53:16,856 - INFO - [Trainer 34] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:16,882 - INFO - [Trainer 34] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:16,882 - INFO - 客户端 27 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:16,882 - INFO - 向客户端 27 发送全局模型 - 参数数量: 74
2025-07-30 22:53:16,883 - INFO - [Client 27] 收到直接传入的模型权重
2025-07-30 22:53:16,883 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:16,883 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:16,883 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:16,910 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:16,911 - INFO - [Client 27] 成功接收并加载模型
2025-07-30 22:53:16,911 - INFO - 客户端 27 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:16,911 - INFO - [Trainer.reset_staleness] 客户端 27 陈旧度重置
2025-07-30 22:53:16,911 - INFO - 重置客户端 27 的陈旧度
2025-07-30 22:53:16,911 - INFO - 成功发送全局模型到客户端 27
2025-07-30 22:53:16,911 - INFO - 客户端 27 开始本地训练
2025-07-30 22:53:16,911 - INFO - [客户端类型: Client, ID: 27] 准备开始训练
2025-07-30 22:53:16,911 - INFO - [客户端 27] 使用的训练器 client_id: 27
2025-07-30 22:53:16,912 - INFO - [Client 27] 开始验证训练集
2025-07-30 22:53:16,913 - INFO - [Client 27] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:16,913 - INFO - [Client 27] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:16,913 - INFO - [Trainer 27] 开始训练
2025-07-30 22:53:16,913 - INFO - [Trainer 27] 训练集大小: 300
2025-07-30 22:53:16,914 - INFO - [Trainer 27] 模型已移至设备: cpu
2025-07-30 22:53:16,915 - INFO - [Trainer 27] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:16,915 - INFO - [Trainer 27] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:16,915 - INFO - [Trainer 27] 开始训练 5 个epoch
2025-07-30 22:53:16,916 - INFO - [Trainer 27] 开始第 1/5 个epoch
2025-07-30 22:53:16,945 - INFO - [Trainer 27] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4779, y=[5, 4, 1, 4, 3]
2025-07-30 22:53:16,945 - INFO - [Trainer 27] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:16,966 - INFO - [Trainer 27] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:16,966 - INFO - 客户端 91 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:16,966 - INFO - 向客户端 91 发送全局模型 - 参数数量: 74
2025-07-30 22:53:16,966 - INFO - [Client 91] 收到直接传入的模型权重
2025-07-30 22:53:16,966 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:16,967 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:16,967 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:16,970 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:16,970 - INFO - [Client 91] 成功接收并加载模型
2025-07-30 22:53:16,970 - INFO - 客户端 91 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:16,970 - INFO - [Trainer.reset_staleness] 客户端 91 陈旧度重置
2025-07-30 22:53:16,970 - INFO - 重置客户端 91 的陈旧度
2025-07-30 22:53:16,970 - INFO - 成功发送全局模型到客户端 91
2025-07-30 22:53:16,970 - INFO - 客户端 91 开始本地训练
2025-07-30 22:53:16,970 - INFO - [客户端类型: Client, ID: 91] 准备开始训练
2025-07-30 22:53:16,970 - INFO - [客户端 91] 使用的训练器 client_id: 91
2025-07-30 22:53:16,971 - INFO - [Client 91] 开始验证训练集
2025-07-30 22:53:16,971 - INFO - [Client 91] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:16,971 - INFO - [Client 91] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:16,971 - INFO - [Trainer 91] 开始训练
2025-07-30 22:53:16,971 - INFO - [Trainer 91] 训练集大小: 300
2025-07-30 22:53:16,972 - INFO - [Trainer 91] 模型已移至设备: cpu
2025-07-30 22:53:16,972 - INFO - [Trainer 91] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:16,972 - INFO - [Trainer 91] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:16,972 - INFO - [Trainer 91] 开始训练 5 个epoch
2025-07-30 22:53:16,972 - INFO - [Trainer 91] 开始第 1/5 个epoch
2025-07-30 22:53:16,978 - INFO - [Trainer 91] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7147, x.mean=-0.5673, y=[4, 4, 4, 4, 4]
2025-07-30 22:53:16,978 - INFO - [Trainer 91] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:16,984 - INFO - [Trainer 91] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:16,984 - INFO - 客户端 93 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:16,985 - INFO - 向客户端 93 发送全局模型 - 参数数量: 74
2025-07-30 22:53:16,985 - INFO - [Client 93] 收到直接传入的模型权重
2025-07-30 22:53:16,985 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:16,985 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:16,985 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:16,987 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:16,987 - INFO - [Client 93] 成功接收并加载模型
2025-07-30 22:53:16,987 - INFO - 客户端 93 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:16,987 - INFO - [Trainer.reset_staleness] 客户端 93 陈旧度重置
2025-07-30 22:53:16,987 - INFO - 重置客户端 93 的陈旧度
2025-07-30 22:53:16,987 - INFO - 成功发送全局模型到客户端 93
2025-07-30 22:53:16,987 - INFO - 客户端 93 开始本地训练
2025-07-30 22:53:16,989 - INFO - [客户端类型: Client, ID: 93] 准备开始训练
2025-07-30 22:53:16,989 - INFO - [客户端 93] 使用的训练器 client_id: 93
2025-07-30 22:53:16,989 - INFO - [Client 93] 开始验证训练集
2025-07-30 22:53:16,989 - INFO - [Client 93] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:16,989 - INFO - [Client 93] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:16,989 - INFO - [Trainer 93] 开始训练
2025-07-30 22:53:16,989 - INFO - [Trainer 93] 训练集大小: 300
2025-07-30 22:53:16,990 - INFO - [Trainer 93] 模型已移至设备: cpu
2025-07-30 22:53:16,990 - INFO - [Trainer 93] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:16,990 - INFO - [Trainer 93] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:16,991 - INFO - [Trainer 93] 开始训练 5 个epoch
2025-07-30 22:53:16,991 - INFO - [Trainer 93] 开始第 1/5 个epoch
2025-07-30 22:53:16,999 - INFO - [Trainer 93] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4150, y=[5, 5, 5, 8, 9]
2025-07-30 22:53:16,999 - INFO - [Trainer 93] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,005 - INFO - [Trainer 93] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:17,005 - INFO - 客户端 17 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:17,005 - INFO - 向客户端 17 发送全局模型 - 参数数量: 74
2025-07-30 22:53:17,006 - INFO - [Client 17] 收到直接传入的模型权重
2025-07-30 22:53:17,006 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:17,006 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:17,006 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:17,033 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:17,033 - INFO - [Client 17] 成功接收并加载模型
2025-07-30 22:53:17,033 - INFO - 客户端 17 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:17,033 - INFO - [Trainer.reset_staleness] 客户端 17 陈旧度重置
2025-07-30 22:53:17,033 - INFO - 重置客户端 17 的陈旧度
2025-07-30 22:53:17,033 - INFO - 成功发送全局模型到客户端 17
2025-07-30 22:53:17,033 - INFO - 客户端 17 开始本地训练
2025-07-30 22:53:17,033 - INFO - [客户端类型: Client, ID: 17] 准备开始训练
2025-07-30 22:53:17,033 - INFO - [客户端 17] 使用的训练器 client_id: 17
2025-07-30 22:53:17,034 - INFO - [Client 17] 开始验证训练集
2025-07-30 22:53:17,036 - INFO - [Client 17] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:17,036 - INFO - [Client 17] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:17,036 - INFO - [Trainer 17] 开始训练
2025-07-30 22:53:17,036 - INFO - [Trainer 17] 训练集大小: 300
2025-07-30 22:53:17,037 - INFO - [Trainer 17] 模型已移至设备: cpu
2025-07-30 22:53:17,038 - INFO - [Trainer 17] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:17,038 - INFO - [Trainer 17] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:17,039 - INFO - [Trainer 17] 开始训练 5 个epoch
2025-07-30 22:53:17,039 - INFO - [Trainer 17] 开始第 1/5 个epoch
2025-07-30 22:53:17,076 - INFO - [Trainer 17] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5152, y=[6, 5, 5, 5, 5]
2025-07-30 22:53:17,076 - INFO - [Trainer 17] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,102 - INFO - [Trainer 17] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:17,102 - INFO - 客户端 97 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:17,102 - INFO - 向客户端 97 发送全局模型 - 参数数量: 74
2025-07-30 22:53:17,102 - INFO - [Client 97] 收到直接传入的模型权重
2025-07-30 22:53:17,103 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:17,103 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:17,103 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:17,107 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:17,107 - INFO - [Client 97] 成功接收并加载模型
2025-07-30 22:53:17,107 - INFO - 客户端 97 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:17,107 - INFO - [Trainer.reset_staleness] 客户端 97 陈旧度重置
2025-07-30 22:53:17,107 - INFO - 重置客户端 97 的陈旧度
2025-07-30 22:53:17,107 - INFO - 成功发送全局模型到客户端 97
2025-07-30 22:53:17,107 - INFO - 客户端 97 开始本地训练
2025-07-30 22:53:17,108 - INFO - [客户端类型: Client, ID: 97] 准备开始训练
2025-07-30 22:53:17,108 - INFO - [客户端 97] 使用的训练器 client_id: 97
2025-07-30 22:53:17,108 - INFO - [Client 97] 开始验证训练集
2025-07-30 22:53:17,108 - INFO - [Client 97] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:17,108 - INFO - [Client 97] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:17,108 - INFO - [Trainer 97] 开始训练
2025-07-30 22:53:17,108 - INFO - [Trainer 97] 训练集大小: 300
2025-07-30 22:53:17,109 - INFO - [Trainer 97] 模型已移至设备: cpu
2025-07-30 22:53:17,109 - INFO - [Trainer 97] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:17,109 - INFO - [Trainer 97] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:17,109 - INFO - [Trainer 97] 开始训练 5 个epoch
2025-07-30 22:53:17,109 - INFO - [Trainer 97] 开始第 1/5 个epoch
2025-07-30 22:53:17,115 - INFO - [Trainer 97] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4510, y=[3, 3, 3, 3, 3]
2025-07-30 22:53:17,115 - INFO - [Trainer 97] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,122 - INFO - [Trainer 97] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:17,122 - INFO - 客户端 85 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:17,122 - INFO - 向客户端 85 发送全局模型 - 参数数量: 74
2025-07-30 22:53:17,122 - INFO - [Client 85] 收到直接传入的模型权重
2025-07-30 22:53:17,123 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:17,123 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:17,123 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:17,125 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:17,125 - INFO - [Client 85] 成功接收并加载模型
2025-07-30 22:53:17,125 - INFO - 客户端 85 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:17,125 - INFO - [Trainer.reset_staleness] 客户端 85 陈旧度重置
2025-07-30 22:53:17,125 - INFO - 重置客户端 85 的陈旧度
2025-07-30 22:53:17,125 - INFO - 成功发送全局模型到客户端 85
2025-07-30 22:53:17,125 - INFO - 客户端 85 开始本地训练
2025-07-30 22:53:17,126 - INFO - [客户端类型: Client, ID: 85] 准备开始训练
2025-07-30 22:53:17,126 - INFO - [客户端 85] 使用的训练器 client_id: 85
2025-07-30 22:53:17,126 - INFO - [Client 85] 开始验证训练集
2025-07-30 22:53:17,126 - INFO - [Client 85] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:17,126 - INFO - [Client 85] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:17,126 - INFO - [Trainer 85] 开始训练
2025-07-30 22:53:17,126 - INFO - [Trainer 85] 训练集大小: 300
2025-07-30 22:53:17,127 - INFO - [Trainer 85] 模型已移至设备: cpu
2025-07-30 22:53:17,127 - INFO - [Trainer 85] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:17,127 - INFO - [Trainer 85] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:17,127 - INFO - [Trainer 85] 开始训练 5 个epoch
2025-07-30 22:53:17,127 - INFO - [Trainer 85] 开始第 1/5 个epoch
2025-07-30 22:53:17,135 - INFO - [Trainer 85] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3315, y=[5, 5, 5, 5, 5]
2025-07-30 22:53:17,135 - INFO - [Trainer 85] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,142 - INFO - [Trainer 85] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:17,142 - INFO - 客户端 70 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:17,142 - INFO - 向客户端 70 发送全局模型 - 参数数量: 74
2025-07-30 22:53:17,142 - INFO - [Client 70] 收到直接传入的模型权重
2025-07-30 22:53:17,142 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:17,142 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:17,143 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:17,148 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:17,148 - INFO - [Client 70] 成功接收并加载模型
2025-07-30 22:53:17,149 - INFO - 客户端 70 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:17,149 - INFO - [Trainer.reset_staleness] 客户端 70 陈旧度重置
2025-07-30 22:53:17,149 - INFO - 重置客户端 70 的陈旧度
2025-07-30 22:53:17,149 - INFO - 成功发送全局模型到客户端 70
2025-07-30 22:53:17,149 - INFO - 客户端 70 开始本地训练
2025-07-30 22:53:17,149 - INFO - [客户端类型: Client, ID: 70] 准备开始训练
2025-07-30 22:53:17,149 - INFO - [客户端 70] 使用的训练器 client_id: 70
2025-07-30 22:53:17,149 - INFO - [Client 70] 开始验证训练集
2025-07-30 22:53:17,150 - INFO - [Client 70] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:17,150 - INFO - [Client 70] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:17,150 - INFO - [Trainer 70] 开始训练
2025-07-30 22:53:17,150 - INFO - [Trainer 70] 训练集大小: 300
2025-07-30 22:53:17,151 - INFO - [Trainer 70] 模型已移至设备: cpu
2025-07-30 22:53:17,151 - INFO - [Trainer 70] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:17,151 - INFO - [Trainer 70] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:17,151 - INFO - [Trainer 70] 开始训练 5 个epoch
2025-07-30 22:53:17,151 - INFO - [Trainer 70] 开始第 1/5 个epoch
2025-07-30 22:53:17,158 - INFO - [Trainer 70] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1342, y=[0, 0, 0, 0, 0]
2025-07-30 22:53:17,159 - INFO - [Trainer 70] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,166 - INFO - [Trainer 70] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:17,166 - INFO - 客户端 12 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:17,166 - INFO - 向客户端 12 发送全局模型 - 参数数量: 74
2025-07-30 22:53:17,166 - INFO - [Client 12] 收到直接传入的模型权重
2025-07-30 22:53:17,167 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:17,167 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:17,167 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:17,201 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:17,201 - INFO - [Client 12] 成功接收并加载模型
2025-07-30 22:53:17,201 - INFO - 客户端 12 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:17,201 - INFO - [Trainer.reset_staleness] 客户端 12 陈旧度重置
2025-07-30 22:53:17,202 - INFO - 重置客户端 12 的陈旧度
2025-07-30 22:53:17,202 - INFO - 成功发送全局模型到客户端 12
2025-07-30 22:53:17,202 - INFO - 客户端 12 开始本地训练
2025-07-30 22:53:17,202 - INFO - [客户端类型: Client, ID: 12] 准备开始训练
2025-07-30 22:53:17,202 - INFO - [客户端 12] 使用的训练器 client_id: 12
2025-07-30 22:53:17,202 - INFO - [Client 12] 开始验证训练集
2025-07-30 22:53:17,208 - INFO - [Client 12] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:17,208 - INFO - [Client 12] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:17,208 - INFO - [Trainer 12] 开始训练
2025-07-30 22:53:17,208 - INFO - [Trainer 12] 训练集大小: 300
2025-07-30 22:53:17,209 - INFO - [Trainer 12] 模型已移至设备: cpu
2025-07-30 22:53:17,209 - INFO - [Trainer 12] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:17,209 - INFO - [Trainer 12] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:17,209 - INFO - [Trainer 12] 开始训练 5 个epoch
2025-07-30 22:53:17,209 - INFO - [Trainer 12] 开始第 1/5 个epoch
2025-07-30 22:53:17,244 - INFO - [Trainer 12] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4290, y=[3, 3, 5, 5, 3]
2025-07-30 22:53:17,244 - INFO - [Trainer 12] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,275 - INFO - [Trainer 12] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:17,474 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:53:17,474 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:53:17,474 - INFO - 客户端 72 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:17,475 - INFO - 向客户端 72 发送全局模型 - 参数数量: 74
2025-07-30 22:53:17,475 - INFO - [Client 72] 收到直接传入的模型权重
2025-07-30 22:53:17,475 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:17,475 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:17,475 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:17,478 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:17,478 - INFO - [Client 72] 成功接收并加载模型
2025-07-30 22:53:17,479 - INFO - 客户端 72 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:17,479 - INFO - [Trainer.reset_staleness] 客户端 72 陈旧度重置
2025-07-30 22:53:17,479 - INFO - 重置客户端 72 的陈旧度
2025-07-30 22:53:17,479 - INFO - 成功发送全局模型到客户端 72
2025-07-30 22:53:17,479 - INFO - 客户端 72 开始本地训练
2025-07-30 22:53:17,479 - INFO - [客户端类型: Client, ID: 72] 准备开始训练
2025-07-30 22:53:17,479 - INFO - [客户端 72] 使用的训练器 client_id: 72
2025-07-30 22:53:17,479 - INFO - [Client 72] 开始验证训练集
2025-07-30 22:53:17,480 - INFO - [Client 72] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:17,480 - INFO - [Client 72] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:17,481 - INFO - [Trainer 72] 开始训练
2025-07-30 22:53:17,481 - INFO - [Trainer 72] 训练集大小: 300
2025-07-30 22:53:17,482 - INFO - [Trainer 72] 模型已移至设备: cpu
2025-07-30 22:53:17,482 - INFO - [Trainer 72] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:17,482 - INFO - [Trainer 72] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:17,483 - INFO - [Trainer 72] 开始训练 5 个epoch
2025-07-30 22:53:17,483 - INFO - [Trainer 72] 开始第 1/5 个epoch
2025-07-30 22:53:17,493 - INFO - [Trainer 72] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2471, y=[3, 3, 9, 3, 9]
2025-07-30 22:53:17,493 - INFO - [Trainer 72] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,500 - INFO - [Trainer 72] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:17,500 - INFO - 客户端 40 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:17,501 - INFO - 向客户端 40 发送全局模型 - 参数数量: 74
2025-07-30 22:53:17,501 - INFO - [Client 40] 收到直接传入的模型权重
2025-07-30 22:53:17,501 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:17,501 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:17,501 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:17,530 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:17,530 - INFO - [Client 40] 成功接收并加载模型
2025-07-30 22:53:17,530 - INFO - 客户端 40 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:17,530 - INFO - [Trainer.reset_staleness] 客户端 40 陈旧度重置
2025-07-30 22:53:17,531 - INFO - 重置客户端 40 的陈旧度
2025-07-30 22:53:17,531 - INFO - 成功发送全局模型到客户端 40
2025-07-30 22:53:17,531 - INFO - 客户端 40 开始本地训练
2025-07-30 22:53:17,531 - INFO - [客户端类型: Client, ID: 40] 准备开始训练
2025-07-30 22:53:17,531 - INFO - [客户端 40] 使用的训练器 client_id: 40
2025-07-30 22:53:17,531 - INFO - [Client 40] 开始验证训练集
2025-07-30 22:53:17,534 - INFO - [Client 40] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:17,535 - INFO - [Client 40] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:17,535 - INFO - [Trainer 40] 开始训练
2025-07-30 22:53:17,535 - INFO - [Trainer 40] 训练集大小: 300
2025-07-30 22:53:17,535 - INFO - [Trainer 40] 模型已移至设备: cpu
2025-07-30 22:53:17,536 - INFO - [Trainer 40] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:17,536 - INFO - [Trainer 40] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:17,536 - INFO - [Trainer 40] 开始训练 5 个epoch
2025-07-30 22:53:17,536 - INFO - [Trainer 40] 开始第 1/5 个epoch
2025-07-30 22:53:17,579 - INFO - [Trainer 40] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2999, y=[5, 5, 5, 5, 9]
2025-07-30 22:53:17,580 - INFO - [Trainer 40] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,610 - INFO - [Trainer 40] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:17,610 - INFO - 客户端 57 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:17,610 - INFO - 向客户端 57 发送全局模型 - 参数数量: 74
2025-07-30 22:53:17,610 - INFO - [Client 57] 收到直接传入的模型权重
2025-07-30 22:53:17,610 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:17,611 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:17,611 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:17,640 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:17,641 - INFO - [Client 57] 成功接收并加载模型
2025-07-30 22:53:17,641 - INFO - 客户端 57 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:17,641 - INFO - [Trainer.reset_staleness] 客户端 57 陈旧度重置
2025-07-30 22:53:17,641 - INFO - 重置客户端 57 的陈旧度
2025-07-30 22:53:17,641 - INFO - 成功发送全局模型到客户端 57
2025-07-30 22:53:17,641 - INFO - 客户端 57 开始本地训练
2025-07-30 22:53:17,642 - INFO - [客户端类型: Client, ID: 57] 准备开始训练
2025-07-30 22:53:17,642 - INFO - [客户端 57] 使用的训练器 client_id: 57
2025-07-30 22:53:17,642 - INFO - [Client 57] 开始验证训练集
2025-07-30 22:53:17,645 - INFO - [Client 57] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:17,646 - INFO - [Client 57] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:17,646 - INFO - [Trainer 57] 开始训练
2025-07-30 22:53:17,646 - INFO - [Trainer 57] 训练集大小: 300
2025-07-30 22:53:17,646 - INFO - [Trainer 57] 模型已移至设备: cpu
2025-07-30 22:53:17,646 - INFO - [Trainer 57] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:17,646 - INFO - [Trainer 57] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:17,646 - INFO - [Trainer 57] 开始训练 5 个epoch
2025-07-30 22:53:17,646 - INFO - [Trainer 57] 开始第 1/5 个epoch
2025-07-30 22:53:17,685 - INFO - [Trainer 57] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2470, y=[9, 9, 9, 9, 9]
2025-07-30 22:53:17,686 - INFO - [Trainer 57] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,718 - INFO - [Trainer 57] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:17,890 - INFO - 客户端 64 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:17,891 - INFO - 向客户端 64 发送全局模型 - 参数数量: 74
2025-07-30 22:53:17,891 - INFO - [Client 64] 收到直接传入的模型权重
2025-07-30 22:53:17,891 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:17,891 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:17,891 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:17,894 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:17,895 - INFO - [Client 64] 成功接收并加载模型
2025-07-30 22:53:17,895 - INFO - 客户端 64 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:17,895 - INFO - [Trainer.reset_staleness] 客户端 64 陈旧度重置
2025-07-30 22:53:17,895 - INFO - 重置客户端 64 的陈旧度
2025-07-30 22:53:17,895 - INFO - 成功发送全局模型到客户端 64
2025-07-30 22:53:17,896 - INFO - 客户端 64 开始本地训练
2025-07-30 22:53:17,896 - INFO - [客户端类型: Client, ID: 64] 准备开始训练
2025-07-30 22:53:17,896 - INFO - [客户端 64] 使用的训练器 client_id: 64
2025-07-30 22:53:17,896 - INFO - [Client 64] 开始验证训练集
2025-07-30 22:53:17,897 - INFO - [Client 64] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:17,897 - INFO - [Client 64] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:17,898 - INFO - [Trainer 64] 开始训练
2025-07-30 22:53:17,898 - INFO - [Trainer 64] 训练集大小: 300
2025-07-30 22:53:17,898 - INFO - [Trainer 64] 模型已移至设备: cpu
2025-07-30 22:53:17,899 - INFO - [Trainer 64] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:17,899 - INFO - [Trainer 64] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:17,899 - INFO - [Trainer 64] 开始训练 5 个epoch
2025-07-30 22:53:17,899 - INFO - [Trainer 64] 开始第 1/5 个epoch
2025-07-30 22:53:17,918 - INFO - [Trainer 64] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4291, y=[5, 5, 5, 5, 5]
2025-07-30 22:53:17,918 - INFO - [Trainer 64] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,932 - INFO - [Trainer 64] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:17,932 - INFO - 客户端 90 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:17,932 - INFO - 向客户端 90 发送全局模型 - 参数数量: 74
2025-07-30 22:53:17,934 - INFO - [Client 90] 收到直接传入的模型权重
2025-07-30 22:53:17,934 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:17,934 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:17,934 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:17,937 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:17,937 - INFO - [Client 90] 成功接收并加载模型
2025-07-30 22:53:17,937 - INFO - 客户端 90 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:17,937 - INFO - [Trainer.reset_staleness] 客户端 90 陈旧度重置
2025-07-30 22:53:17,937 - INFO - 重置客户端 90 的陈旧度
2025-07-30 22:53:17,938 - INFO - 成功发送全局模型到客户端 90
2025-07-30 22:53:17,938 - INFO - 客户端 90 开始本地训练
2025-07-30 22:53:17,938 - INFO - [客户端类型: Client, ID: 90] 准备开始训练
2025-07-30 22:53:17,938 - INFO - [客户端 90] 使用的训练器 client_id: 90
2025-07-30 22:53:17,938 - INFO - [Client 90] 开始验证训练集
2025-07-30 22:53:17,938 - INFO - [Client 90] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:17,939 - INFO - [Client 90] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:17,939 - INFO - [Trainer 90] 开始训练
2025-07-30 22:53:17,939 - INFO - [Trainer 90] 训练集大小: 300
2025-07-30 22:53:17,939 - INFO - [Trainer 90] 模型已移至设备: cpu
2025-07-30 22:53:17,939 - INFO - [Trainer 90] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:17,940 - INFO - [Trainer 90] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:17,940 - INFO - [Trainer 90] 开始训练 5 个epoch
2025-07-30 22:53:17,940 - INFO - [Trainer 90] 开始第 1/5 个epoch
2025-07-30 22:53:17,948 - INFO - [Trainer 90] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5828, y=[1, 1, 1, 1, 1]
2025-07-30 22:53:17,948 - INFO - [Trainer 90] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:17,954 - INFO - [Trainer 90] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:18,147 - INFO - [Trainer 34] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5514
2025-07-30 22:53:18,147 - INFO - [Trainer 34] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:18,147 - INFO - [Trainer 34] 标签样本: [6, 6, 6, 7, 7]
2025-07-30 22:53:18,199 - INFO - [Trainer 34] Batch 0, Loss: 2.6707
2025-07-30 22:53:18,384 - INFO - 客户端 24 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:18,384 - INFO - 向客户端 24 发送全局模型 - 参数数量: 74
2025-07-30 22:53:18,385 - INFO - [Client 24] 收到直接传入的模型权重
2025-07-30 22:53:18,385 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:18,385 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:18,385 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:18,423 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:18,423 - INFO - [Client 24] 成功接收并加载模型
2025-07-30 22:53:18,424 - INFO - 客户端 24 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:18,424 - INFO - [Trainer.reset_staleness] 客户端 24 陈旧度重置
2025-07-30 22:53:18,424 - INFO - 重置客户端 24 的陈旧度
2025-07-30 22:53:18,424 - INFO - 成功发送全局模型到客户端 24
2025-07-30 22:53:18,424 - INFO - 客户端 24 开始本地训练
2025-07-30 22:53:18,424 - INFO - [客户端类型: Client, ID: 24] 准备开始训练
2025-07-30 22:53:18,424 - INFO - [客户端 24] 使用的训练器 client_id: 24
2025-07-30 22:53:18,424 - INFO - [Client 24] 开始验证训练集
2025-07-30 22:53:18,427 - INFO - [Client 24] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:18,427 - INFO - [Client 24] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:18,427 - INFO - [Trainer 24] 开始训练
2025-07-30 22:53:18,427 - INFO - [Trainer 24] 训练集大小: 300
2025-07-30 22:53:18,427 - INFO - [Trainer 24] 模型已移至设备: cpu
2025-07-30 22:53:18,427 - INFO - [Trainer 24] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:18,428 - INFO - [Trainer 24] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:18,428 - INFO - [Trainer 24] 开始训练 5 个epoch
2025-07-30 22:53:18,428 - INFO - [Trainer 24] 开始第 1/5 个epoch
2025-07-30 22:53:18,473 - INFO - [Trainer 24] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=0.0343, y=[0, 0, 8, 0, 0]
2025-07-30 22:53:18,474 - INFO - [Trainer 24] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:18,507 - INFO - [Trainer 24] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:18,507 - INFO - 客户端 89 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:18,507 - INFO - 向客户端 89 发送全局模型 - 参数数量: 74
2025-07-30 22:53:18,507 - INFO - [Client 89] 收到直接传入的模型权重
2025-07-30 22:53:18,508 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:18,508 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:18,508 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:18,511 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:18,511 - INFO - [Client 89] 成功接收并加载模型
2025-07-30 22:53:18,511 - INFO - 客户端 89 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:18,511 - INFO - [Trainer.reset_staleness] 客户端 89 陈旧度重置
2025-07-30 22:53:18,511 - INFO - 重置客户端 89 的陈旧度
2025-07-30 22:53:18,511 - INFO - 成功发送全局模型到客户端 89
2025-07-30 22:53:18,511 - INFO - 客户端 89 开始本地训练
2025-07-30 22:53:18,511 - INFO - [客户端类型: Client, ID: 89] 准备开始训练
2025-07-30 22:53:18,511 - INFO - [客户端 89] 使用的训练器 client_id: 89
2025-07-30 22:53:18,512 - INFO - [Client 89] 开始验证训练集
2025-07-30 22:53:18,512 - INFO - [Client 89] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:18,512 - INFO - [Client 89] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:18,512 - INFO - [Trainer 89] 开始训练
2025-07-30 22:53:18,513 - INFO - [Trainer 89] 训练集大小: 300
2025-07-30 22:53:18,514 - INFO - [Trainer 89] 模型已移至设备: cpu
2025-07-30 22:53:18,514 - INFO - [Trainer 89] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:18,514 - INFO - [Trainer 89] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:18,515 - INFO - [Trainer 89] 开始训练 5 个epoch
2025-07-30 22:53:18,515 - INFO - [Trainer 89] 开始第 1/5 个epoch
2025-07-30 22:53:18,521 - INFO - [Trainer 89] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4593, y=[5, 5, 5, 5, 5]
2025-07-30 22:53:18,521 - INFO - [Trainer 89] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:18,528 - INFO - [Trainer 89] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:18,528 - INFO - [Trainer 27] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4264
2025-07-30 22:53:18,529 - INFO - [Trainer 27] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:18,529 - INFO - [Trainer 27] 标签样本: [4, 4, 4, 1, 4]
2025-07-30 22:53:18,579 - INFO - [Trainer 27] Batch 0, Loss: 2.0167
2025-07-30 22:53:18,745 - INFO - [Trainer 91] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4641
2025-07-30 22:53:18,746 - INFO - [Trainer 91] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:18,746 - INFO - [Trainer 91] 标签样本: [4, 4, 4, 6, 4]
2025-07-30 22:53:18,789 - INFO - [Trainer 91] Batch 0, Loss: 1.8244
2025-07-30 22:53:18,915 - INFO - [Trainer 93] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4530
2025-07-30 22:53:18,915 - INFO - [Trainer 93] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:18,915 - INFO - [Trainer 93] 标签样本: [5, 0, 5, 0, 5]
2025-07-30 22:53:18,960 - INFO - [Trainer 93] Batch 0, Loss: 2.5543
2025-07-30 22:53:19,080 - INFO - [Trainer 17] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4143
2025-07-30 22:53:19,081 - INFO - [Trainer 17] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:19,081 - INFO - [Trainer 17] 标签样本: [5, 5, 5, 6, 6]
2025-07-30 22:53:19,130 - INFO - [Trainer 17] Batch 0, Loss: 2.4960
2025-07-30 22:53:19,278 - INFO - [Trainer 97] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4384
2025-07-30 22:53:19,278 - INFO - [Trainer 97] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:19,278 - INFO - [Trainer 97] 标签样本: [3, 3, 3, 0, 3]
2025-07-30 22:53:19,320 - INFO - [Trainer 97] Batch 0, Loss: 2.2789
2025-07-30 22:53:19,450 - INFO - [Trainer 85] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4503
2025-07-30 22:53:19,450 - INFO - [Trainer 85] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:19,450 - INFO - [Trainer 85] 标签样本: [5, 5, 5, 5, 5]
2025-07-30 22:53:19,497 - INFO - [Trainer 85] Batch 0, Loss: 2.4682
2025-07-30 22:53:19,612 - INFO - [Trainer 70] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2858
2025-07-30 22:53:19,612 - INFO - [Trainer 70] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:19,613 - INFO - [Trainer 70] 标签样本: [0, 0, 0, 3, 0]
2025-07-30 22:53:19,661 - INFO - [Trainer 70] Batch 0, Loss: 2.4699
2025-07-30 22:53:19,784 - INFO - [Trainer 12] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4470
2025-07-30 22:53:19,785 - INFO - [Trainer 12] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:19,785 - INFO - [Trainer 12] 标签样本: [5, 3, 8, 5, 3]
2025-07-30 22:53:19,833 - INFO - [Trainer 12] Batch 0, Loss: 2.3963
2025-07-30 22:53:20,149 - INFO - [Trainer 72] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.6944
2025-07-30 22:53:20,149 - INFO - [Trainer 72] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:20,149 - INFO - [Trainer 72] 标签样本: [3, 9, 3, 3, 3]
2025-07-30 22:53:20,192 - INFO - [Trainer 72] Batch 0, Loss: 2.3474
2025-07-30 22:53:20,310 - INFO - [Trainer 40] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4003
2025-07-30 22:53:20,311 - INFO - [Trainer 40] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:20,311 - INFO - [Trainer 40] 标签样本: [5, 4, 4, 5, 5]
2025-07-30 22:53:20,357 - INFO - [Trainer 40] Batch 0, Loss: 2.5393
2025-07-30 22:53:20,511 - INFO - 客户端 32 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:20,512 - INFO - 向客户端 32 发送全局模型 - 参数数量: 74
2025-07-30 22:53:20,512 - INFO - [Client 32] 收到直接传入的模型权重
2025-07-30 22:53:20,512 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:20,512 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:20,512 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:20,542 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:20,542 - INFO - [Client 32] 成功接收并加载模型
2025-07-30 22:53:20,543 - INFO - 客户端 32 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:20,543 - INFO - [Trainer.reset_staleness] 客户端 32 陈旧度重置
2025-07-30 22:53:20,543 - INFO - 重置客户端 32 的陈旧度
2025-07-30 22:53:20,543 - INFO - 成功发送全局模型到客户端 32
2025-07-30 22:53:20,543 - INFO - 客户端 32 开始本地训练
2025-07-30 22:53:20,543 - INFO - [客户端类型: Client, ID: 32] 准备开始训练
2025-07-30 22:53:20,543 - INFO - [客户端 32] 使用的训练器 client_id: 32
2025-07-30 22:53:20,544 - INFO - [Client 32] 开始验证训练集
2025-07-30 22:53:20,546 - INFO - [Client 32] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:20,547 - INFO - [Client 32] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:20,547 - INFO - [Trainer 32] 开始训练
2025-07-30 22:53:20,547 - INFO - [Trainer 32] 训练集大小: 300
2025-07-30 22:53:20,549 - INFO - [Trainer 32] 模型已移至设备: cpu
2025-07-30 22:53:20,549 - INFO - [Trainer 32] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:20,550 - INFO - [Trainer 32] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:20,550 - INFO - [Trainer 32] 开始训练 5 个epoch
2025-07-30 22:53:20,550 - INFO - [Trainer 32] 开始第 1/5 个epoch
2025-07-30 22:53:20,591 - INFO - [Trainer 32] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0037, y=[0, 0, 0, 8, 0]
2025-07-30 22:53:20,592 - INFO - [Trainer 32] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:20,620 - INFO - [Trainer 32] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:20,621 - INFO - 客户端 4 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:20,621 - INFO - 向客户端 4 发送全局模型 - 参数数量: 74
2025-07-30 22:53:20,621 - INFO - [Client 4] 收到直接传入的模型权重
2025-07-30 22:53:20,621 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:20,621 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:20,621 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:20,656 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:20,656 - INFO - [Client 4] 成功接收并加载模型
2025-07-30 22:53:20,656 - INFO - 客户端 4 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:20,657 - INFO - [Trainer.reset_staleness] 客户端 4 陈旧度重置
2025-07-30 22:53:20,657 - INFO - 重置客户端 4 的陈旧度
2025-07-30 22:53:20,657 - INFO - 成功发送全局模型到客户端 4
2025-07-30 22:53:20,657 - INFO - 客户端 4 开始本地训练
2025-07-30 22:53:20,657 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-07-30 22:53:20,657 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-07-30 22:53:20,657 - INFO - [Client 4] 开始验证训练集
2025-07-30 22:53:20,661 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:20,661 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:20,661 - INFO - [Trainer 4] 开始训练
2025-07-30 22:53:20,661 - INFO - [Trainer 4] 训练集大小: 300
2025-07-30 22:53:20,663 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-07-30 22:53:20,663 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:20,663 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:20,663 - INFO - [Trainer 4] 开始训练 5 个epoch
2025-07-30 22:53:20,663 - INFO - [Trainer 4] 开始第 1/5 个epoch
2025-07-30 22:53:20,698 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2701, y=[0, 0, 0, 1, 0]
2025-07-30 22:53:20,698 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:20,733 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:20,734 - INFO - [Trainer 57] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2335
2025-07-30 22:53:20,734 - INFO - [Trainer 57] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:20,734 - INFO - [Trainer 57] 标签样本: [9, 9, 9, 3, 9]
2025-07-30 22:53:20,783 - INFO - [Trainer 57] Batch 0, Loss: 2.8550
2025-07-30 22:53:20,966 - INFO - 客户端 20 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:20,966 - INFO - 向客户端 20 发送全局模型 - 参数数量: 74
2025-07-30 22:53:20,967 - INFO - [Client 20] 收到直接传入的模型权重
2025-07-30 22:53:20,967 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:20,967 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:20,967 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:21,001 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:21,001 - INFO - [Client 20] 成功接收并加载模型
2025-07-30 22:53:21,002 - INFO - 客户端 20 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:21,002 - INFO - [Trainer.reset_staleness] 客户端 20 陈旧度重置
2025-07-30 22:53:21,002 - INFO - 重置客户端 20 的陈旧度
2025-07-30 22:53:21,002 - INFO - 成功发送全局模型到客户端 20
2025-07-30 22:53:21,002 - INFO - 客户端 20 开始本地训练
2025-07-30 22:53:21,002 - INFO - [客户端类型: Client, ID: 20] 准备开始训练
2025-07-30 22:53:21,002 - INFO - [客户端 20] 使用的训练器 client_id: 20
2025-07-30 22:53:21,005 - INFO - [Client 20] 开始验证训练集
2025-07-30 22:53:21,008 - INFO - [Client 20] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:21,008 - INFO - [Client 20] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:21,008 - INFO - [Trainer 20] 开始训练
2025-07-30 22:53:21,008 - INFO - [Trainer 20] 训练集大小: 300
2025-07-30 22:53:21,008 - INFO - [Trainer 20] 模型已移至设备: cpu
2025-07-30 22:53:21,009 - INFO - [Trainer 20] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:21,010 - INFO - [Trainer 20] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:21,010 - INFO - [Trainer 20] 开始训练 5 个epoch
2025-07-30 22:53:21,010 - INFO - [Trainer 20] 开始第 1/5 个epoch
2025-07-30 22:53:21,049 - INFO - [Trainer 20] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5535, y=[6, 1, 9, 1, 9]
2025-07-30 22:53:21,049 - INFO - [Trainer 20] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:21,078 - INFO - [Trainer 20] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:21,078 - INFO - 客户端 10 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:21,079 - INFO - 向客户端 10 发送全局模型 - 参数数量: 74
2025-07-30 22:53:21,079 - INFO - [Client 10] 收到直接传入的模型权重
2025-07-30 22:53:21,079 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:21,079 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:21,079 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:21,116 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:21,117 - INFO - [Client 10] 成功接收并加载模型
2025-07-30 22:53:21,117 - INFO - 客户端 10 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:21,117 - INFO - [Trainer.reset_staleness] 客户端 10 陈旧度重置
2025-07-30 22:53:21,117 - INFO - 重置客户端 10 的陈旧度
2025-07-30 22:53:21,117 - INFO - 成功发送全局模型到客户端 10
2025-07-30 22:53:21,117 - INFO - 客户端 10 开始本地训练
2025-07-30 22:53:21,117 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-07-30 22:53:21,117 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-07-30 22:53:21,118 - INFO - [Client 10] 开始验证训练集
2025-07-30 22:53:21,128 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:21,129 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:21,129 - INFO - [Trainer 10] 开始训练
2025-07-30 22:53:21,129 - INFO - [Trainer 10] 训练集大小: 300
2025-07-30 22:53:21,129 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-07-30 22:53:21,130 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:21,130 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:21,130 - INFO - [Trainer 10] 开始训练 5 个epoch
2025-07-30 22:53:21,130 - INFO - [Trainer 10] 开始第 1/5 个epoch
2025-07-30 22:53:21,169 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2368, y=[8, 7, 8, 6, 8]
2025-07-30 22:53:21,169 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:21,200 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:21,201 - INFO - 客户端 58 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:21,201 - INFO - 向客户端 58 发送全局模型 - 参数数量: 74
2025-07-30 22:53:21,201 - INFO - [Client 58] 收到直接传入的模型权重
2025-07-30 22:53:21,201 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:21,201 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:21,201 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:21,233 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:21,233 - INFO - [Client 58] 成功接收并加载模型
2025-07-30 22:53:21,233 - INFO - 客户端 58 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:21,233 - INFO - [Trainer.reset_staleness] 客户端 58 陈旧度重置
2025-07-30 22:53:21,233 - INFO - 重置客户端 58 的陈旧度
2025-07-30 22:53:21,233 - INFO - 成功发送全局模型到客户端 58
2025-07-30 22:53:21,234 - INFO - 客户端 58 开始本地训练
2025-07-30 22:53:21,234 - INFO - [客户端类型: Client, ID: 58] 准备开始训练
2025-07-30 22:53:21,234 - INFO - [客户端 58] 使用的训练器 client_id: 58
2025-07-30 22:53:21,234 - INFO - [Client 58] 开始验证训练集
2025-07-30 22:53:21,237 - INFO - [Client 58] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:21,237 - INFO - [Client 58] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:21,237 - INFO - [Trainer 58] 开始训练
2025-07-30 22:53:21,237 - INFO - [Trainer 58] 训练集大小: 300
2025-07-30 22:53:21,238 - INFO - [Trainer 58] 模型已移至设备: cpu
2025-07-30 22:53:21,238 - INFO - [Trainer 58] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:21,238 - INFO - [Trainer 58] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:21,239 - INFO - [Trainer 58] 开始训练 5 个epoch
2025-07-30 22:53:21,239 - INFO - [Trainer 58] 开始第 1/5 个epoch
2025-07-30 22:53:21,276 - INFO - [Trainer 58] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5202, y=[4, 5, 7, 5, 5]
2025-07-30 22:53:21,276 - INFO - [Trainer 58] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:21,307 - INFO - [Trainer 58] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:21,499 - INFO - [Trainer 64] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4469
2025-07-30 22:53:21,499 - INFO - [Trainer 64] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:21,500 - INFO - [Trainer 64] 标签样本: [6, 5, 5, 1, 5]
2025-07-30 22:53:21,543 - INFO - [Trainer 64] Batch 0, Loss: 2.5090
2025-07-30 22:53:21,668 - INFO - [Trainer 90] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3804
2025-07-30 22:53:21,669 - INFO - [Trainer 90] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:21,669 - INFO - [Trainer 90] 标签样本: [9, 1, 1, 8, 1]
2025-07-30 22:53:21,716 - INFO - [Trainer 90] Batch 0, Loss: 2.3207
2025-07-30 22:53:21,840 - INFO - 客户端 43 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:21,840 - INFO - 向客户端 43 发送全局模型 - 参数数量: 74
2025-07-30 22:53:21,840 - INFO - [Client 43] 收到直接传入的模型权重
2025-07-30 22:53:21,841 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:21,841 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:21,841 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:21,873 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:21,873 - INFO - [Client 43] 成功接收并加载模型
2025-07-30 22:53:21,873 - INFO - 客户端 43 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:21,873 - INFO - [Trainer.reset_staleness] 客户端 43 陈旧度重置
2025-07-30 22:53:21,873 - INFO - 重置客户端 43 的陈旧度
2025-07-30 22:53:21,873 - INFO - 成功发送全局模型到客户端 43
2025-07-30 22:53:21,873 - INFO - 客户端 43 开始本地训练
2025-07-30 22:53:21,874 - INFO - [客户端类型: Client, ID: 43] 准备开始训练
2025-07-30 22:53:21,874 - INFO - [客户端 43] 使用的训练器 client_id: 43
2025-07-30 22:53:21,874 - INFO - [Client 43] 开始验证训练集
2025-07-30 22:53:21,877 - INFO - [Client 43] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:21,877 - INFO - [Client 43] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:21,878 - INFO - [Trainer 43] 开始训练
2025-07-30 22:53:21,878 - INFO - [Trainer 43] 训练集大小: 300
2025-07-30 22:53:21,878 - INFO - [Trainer 43] 模型已移至设备: cpu
2025-07-30 22:53:21,880 - INFO - [Trainer 43] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:21,880 - INFO - [Trainer 43] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:21,881 - INFO - [Trainer 43] 开始训练 5 个epoch
2025-07-30 22:53:21,881 - INFO - [Trainer 43] 开始第 1/5 个epoch
2025-07-30 22:53:21,925 - INFO - [Trainer 43] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4767, y=[7, 7, 7, 7, 7]
2025-07-30 22:53:21,925 - INFO - [Trainer 43] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:21,956 - INFO - [Trainer 43] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:22,280 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:53:22,281 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:53:22,281 - INFO - [Trainer 24] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0401
2025-07-30 22:53:22,282 - INFO - [Trainer 24] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:22,282 - INFO - [Trainer 24] 标签样本: [0, 0, 0, 0, 0]
2025-07-30 22:53:22,320 - INFO - [Trainer 24] Batch 0, Loss: 2.3603
2025-07-30 22:53:22,447 - INFO - [Trainer 89] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2247
2025-07-30 22:53:22,449 - INFO - [Trainer 89] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:22,449 - INFO - [Trainer 89] 标签样本: [5, 5, 5, 8, 5]
2025-07-30 22:53:22,490 - INFO - [Trainer 89] Batch 0, Loss: 2.5500
2025-07-30 22:53:22,610 - INFO - 客户端 49 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:22,610 - INFO - 向客户端 49 发送全局模型 - 参数数量: 74
2025-07-30 22:53:22,610 - INFO - [Client 49] 收到直接传入的模型权重
2025-07-30 22:53:22,610 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:22,610 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:22,610 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:22,632 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:22,633 - INFO - [Client 49] 成功接收并加载模型
2025-07-30 22:53:22,633 - INFO - 客户端 49 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:22,633 - INFO - [Trainer.reset_staleness] 客户端 49 陈旧度重置
2025-07-30 22:53:22,633 - INFO - 重置客户端 49 的陈旧度
2025-07-30 22:53:22,633 - INFO - 成功发送全局模型到客户端 49
2025-07-30 22:53:22,633 - INFO - 客户端 49 开始本地训练
2025-07-30 22:53:22,633 - INFO - [客户端类型: Client, ID: 49] 准备开始训练
2025-07-30 22:53:22,633 - INFO - [客户端 49] 使用的训练器 client_id: 49
2025-07-30 22:53:22,633 - INFO - [Client 49] 开始验证训练集
2025-07-30 22:53:22,634 - INFO - [Client 49] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:22,635 - INFO - [Client 49] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:22,635 - INFO - [Trainer 49] 开始训练
2025-07-30 22:53:22,635 - INFO - [Trainer 49] 训练集大小: 300
2025-07-30 22:53:22,635 - INFO - [Trainer 49] 模型已移至设备: cpu
2025-07-30 22:53:22,636 - INFO - [Trainer 49] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:22,636 - INFO - [Trainer 49] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:22,636 - INFO - [Trainer 49] 开始训练 5 个epoch
2025-07-30 22:53:22,636 - INFO - [Trainer 49] 开始第 1/5 个epoch
2025-07-30 22:53:22,658 - INFO - [Trainer 49] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3381, y=[8, 8, 8, 1, 8]
2025-07-30 22:53:22,658 - INFO - [Trainer 49] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:22,678 - INFO - [Trainer 49] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:22,679 - INFO - 客户端 67 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:22,679 - INFO - 向客户端 67 发送全局模型 - 参数数量: 74
2025-07-30 22:53:22,679 - INFO - [Client 67] 收到直接传入的模型权重
2025-07-30 22:53:22,679 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:22,679 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:22,680 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:22,683 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:22,684 - INFO - [Client 67] 成功接收并加载模型
2025-07-30 22:53:22,684 - INFO - 客户端 67 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:22,684 - INFO - [Trainer.reset_staleness] 客户端 67 陈旧度重置
2025-07-30 22:53:22,684 - INFO - 重置客户端 67 的陈旧度
2025-07-30 22:53:22,684 - INFO - 成功发送全局模型到客户端 67
2025-07-30 22:53:22,684 - INFO - 客户端 67 开始本地训练
2025-07-30 22:53:22,685 - INFO - [客户端类型: Client, ID: 67] 准备开始训练
2025-07-30 22:53:22,685 - INFO - [客户端 67] 使用的训练器 client_id: 67
2025-07-30 22:53:22,685 - INFO - [Client 67] 开始验证训练集
2025-07-30 22:53:22,686 - INFO - [Client 67] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:22,686 - INFO - [Client 67] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:22,686 - INFO - [Trainer 67] 开始训练
2025-07-30 22:53:22,686 - INFO - [Trainer 67] 训练集大小: 300
2025-07-30 22:53:22,686 - INFO - [Trainer 67] 模型已移至设备: cpu
2025-07-30 22:53:22,686 - INFO - [Trainer 67] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:22,686 - INFO - [Trainer 67] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:22,687 - INFO - [Trainer 67] 开始训练 5 个epoch
2025-07-30 22:53:22,687 - INFO - [Trainer 67] 开始第 1/5 个epoch
2025-07-30 22:53:22,697 - INFO - [Trainer 67] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2807, y=[5, 0, 5, 4, 4]
2025-07-30 22:53:22,698 - INFO - [Trainer 67] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:22,708 - INFO - [Trainer 67] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:22,920 - INFO - 客户端 11 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:22,920 - INFO - 向客户端 11 发送全局模型 - 参数数量: 74
2025-07-30 22:53:22,920 - INFO - [Client 11] 收到直接传入的模型权重
2025-07-30 22:53:22,920 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:22,921 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:22,922 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:22,940 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:22,940 - INFO - [Client 11] 成功接收并加载模型
2025-07-30 22:53:22,941 - INFO - 客户端 11 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:22,941 - INFO - [Trainer.reset_staleness] 客户端 11 陈旧度重置
2025-07-30 22:53:22,941 - INFO - 重置客户端 11 的陈旧度
2025-07-30 22:53:22,941 - INFO - 成功发送全局模型到客户端 11
2025-07-30 22:53:22,941 - INFO - 客户端 11 开始本地训练
2025-07-30 22:53:22,941 - INFO - [客户端类型: Client, ID: 11] 准备开始训练
2025-07-30 22:53:22,941 - INFO - [客户端 11] 使用的训练器 client_id: 11
2025-07-30 22:53:22,941 - INFO - [Client 11] 开始验证训练集
2025-07-30 22:53:22,943 - INFO - [Client 11] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:22,943 - INFO - [Client 11] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:22,943 - INFO - [Trainer 11] 开始训练
2025-07-30 22:53:22,943 - INFO - [Trainer 11] 训练集大小: 300
2025-07-30 22:53:22,944 - INFO - [Trainer 11] 模型已移至设备: cpu
2025-07-30 22:53:22,944 - INFO - [Trainer 11] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:22,944 - INFO - [Trainer 11] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:22,944 - INFO - [Trainer 11] 开始训练 5 个epoch
2025-07-30 22:53:22,944 - INFO - [Trainer 11] 开始第 1/5 个epoch
2025-07-30 22:53:22,966 - INFO - [Trainer 11] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3877, y=[6, 4, 4, 6, 4]
2025-07-30 22:53:22,968 - INFO - [Trainer 11] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:22,984 - INFO - [Trainer 11] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:22,984 - INFO - 客户端 69 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:22,984 - INFO - 向客户端 69 发送全局模型 - 参数数量: 74
2025-07-30 22:53:22,984 - INFO - [Client 69] 收到直接传入的模型权重
2025-07-30 22:53:22,985 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:22,985 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:22,985 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:22,988 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:22,988 - INFO - [Client 69] 成功接收并加载模型
2025-07-30 22:53:22,988 - INFO - 客户端 69 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:22,989 - INFO - [Trainer.reset_staleness] 客户端 69 陈旧度重置
2025-07-30 22:53:22,989 - INFO - 重置客户端 69 的陈旧度
2025-07-30 22:53:22,989 - INFO - 成功发送全局模型到客户端 69
2025-07-30 22:53:22,989 - INFO - 客户端 69 开始本地训练
2025-07-30 22:53:22,989 - INFO - [客户端类型: Client, ID: 69] 准备开始训练
2025-07-30 22:53:22,989 - INFO - [客户端 69] 使用的训练器 client_id: 69
2025-07-30 22:53:22,989 - INFO - [Client 69] 开始验证训练集
2025-07-30 22:53:22,989 - INFO - [Client 69] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:22,989 - INFO - [Client 69] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:22,990 - INFO - [Trainer 69] 开始训练
2025-07-30 22:53:22,990 - INFO - [Trainer 69] 训练集大小: 300
2025-07-30 22:53:22,990 - INFO - [Trainer 69] 模型已移至设备: cpu
2025-07-30 22:53:22,991 - INFO - [Trainer 69] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:22,991 - INFO - [Trainer 69] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:22,991 - INFO - [Trainer 69] 开始训练 5 个epoch
2025-07-30 22:53:22,991 - INFO - [Trainer 69] 开始第 1/5 个epoch
2025-07-30 22:53:23,001 - INFO - [Trainer 69] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2339, y=[5, 5, 7, 5, 5]
2025-07-30 22:53:23,001 - INFO - [Trainer 69] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:23,009 - INFO - [Trainer 69] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:23,009 - INFO - 客户端 29 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:23,009 - INFO - 向客户端 29 发送全局模型 - 参数数量: 74
2025-07-30 22:53:23,009 - INFO - [Client 29] 收到直接传入的模型权重
2025-07-30 22:53:23,009 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:23,009 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:23,009 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:23,027 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:23,027 - INFO - [Client 29] 成功接收并加载模型
2025-07-30 22:53:23,027 - INFO - 客户端 29 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:23,027 - INFO - [Trainer.reset_staleness] 客户端 29 陈旧度重置
2025-07-30 22:53:23,027 - INFO - 重置客户端 29 的陈旧度
2025-07-30 22:53:23,027 - INFO - 成功发送全局模型到客户端 29
2025-07-30 22:53:23,027 - INFO - 客户端 29 开始本地训练
2025-07-30 22:53:23,027 - INFO - [客户端类型: Client, ID: 29] 准备开始训练
2025-07-30 22:53:23,027 - INFO - [客户端 29] 使用的训练器 client_id: 29
2025-07-30 22:53:23,027 - INFO - [Client 29] 开始验证训练集
2025-07-30 22:53:23,028 - INFO - [Client 29] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:23,029 - INFO - [Client 29] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:23,029 - INFO - [Trainer 29] 开始训练
2025-07-30 22:53:23,029 - INFO - [Trainer 29] 训练集大小: 300
2025-07-30 22:53:23,029 - INFO - [Trainer 29] 模型已移至设备: cpu
2025-07-30 22:53:23,029 - INFO - [Trainer 29] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:23,029 - INFO - [Trainer 29] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:23,031 - INFO - [Trainer 29] 开始训练 5 个epoch
2025-07-30 22:53:23,031 - INFO - [Trainer 29] 开始第 1/5 个epoch
2025-07-30 22:53:23,045 - INFO - [Trainer 29] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3009, y=[9, 3, 0, 0, 0]
2025-07-30 22:53:23,045 - INFO - [Trainer 29] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:23,061 - INFO - [Trainer 29] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:23,061 - INFO - 客户端 86 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:23,061 - INFO - 向客户端 86 发送全局模型 - 参数数量: 74
2025-07-30 22:53:23,061 - INFO - [Client 86] 收到直接传入的模型权重
2025-07-30 22:53:23,062 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:23,062 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:23,062 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:23,064 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:23,064 - INFO - [Client 86] 成功接收并加载模型
2025-07-30 22:53:23,064 - INFO - 客户端 86 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:23,064 - INFO - [Trainer.reset_staleness] 客户端 86 陈旧度重置
2025-07-30 22:53:23,064 - INFO - 重置客户端 86 的陈旧度
2025-07-30 22:53:23,064 - INFO - 成功发送全局模型到客户端 86
2025-07-30 22:53:23,064 - INFO - 客户端 86 开始本地训练
2025-07-30 22:53:23,064 - INFO - [客户端类型: Client, ID: 86] 准备开始训练
2025-07-30 22:53:23,064 - INFO - [客户端 86] 使用的训练器 client_id: 86
2025-07-30 22:53:23,064 - INFO - [Client 86] 开始验证训练集
2025-07-30 22:53:23,065 - INFO - [Client 86] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:23,065 - INFO - [Client 86] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:23,065 - INFO - [Trainer 86] 开始训练
2025-07-30 22:53:23,065 - INFO - [Trainer 86] 训练集大小: 300
2025-07-30 22:53:23,065 - INFO - [Trainer 86] 模型已移至设备: cpu
2025-07-30 22:53:23,065 - INFO - [Trainer 86] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:23,065 - INFO - [Trainer 86] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:23,067 - INFO - [Trainer 86] 开始训练 5 个epoch
2025-07-30 22:53:23,067 - INFO - [Trainer 86] 开始第 1/5 个epoch
2025-07-30 22:53:23,072 - INFO - [Trainer 86] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3876, y=[4, 4, 4, 6, 4]
2025-07-30 22:53:23,072 - INFO - [Trainer 86] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:23,078 - INFO - [Trainer 86] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:23,299 - INFO - 客户端 50 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:23,299 - INFO - 向客户端 50 发送全局模型 - 参数数量: 74
2025-07-30 22:53:23,300 - INFO - [Client 50] 收到直接传入的模型权重
2025-07-30 22:53:23,300 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:23,300 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:23,300 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:23,321 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:23,321 - INFO - [Client 50] 成功接收并加载模型
2025-07-30 22:53:23,321 - INFO - 客户端 50 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:23,321 - INFO - [Trainer.reset_staleness] 客户端 50 陈旧度重置
2025-07-30 22:53:23,322 - INFO - 重置客户端 50 的陈旧度
2025-07-30 22:53:23,322 - INFO - 成功发送全局模型到客户端 50
2025-07-30 22:53:23,322 - INFO - 客户端 50 开始本地训练
2025-07-30 22:53:23,322 - INFO - [客户端类型: Client, ID: 50] 准备开始训练
2025-07-30 22:53:23,322 - INFO - [客户端 50] 使用的训练器 client_id: 50
2025-07-30 22:53:23,322 - INFO - [Client 50] 开始验证训练集
2025-07-30 22:53:23,323 - INFO - [Client 50] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:23,323 - INFO - [Client 50] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:23,323 - INFO - [Trainer 50] 开始训练
2025-07-30 22:53:23,323 - INFO - [Trainer 50] 训练集大小: 300
2025-07-30 22:53:23,324 - INFO - [Trainer 50] 模型已移至设备: cpu
2025-07-30 22:53:23,324 - INFO - [Trainer 50] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:23,324 - INFO - [Trainer 50] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:23,324 - INFO - [Trainer 50] 开始训练 5 个epoch
2025-07-30 22:53:23,324 - INFO - [Trainer 50] 开始第 1/5 个epoch
2025-07-30 22:53:23,336 - INFO - [Trainer 50] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3591, y=[4, 7, 4, 7, 7]
2025-07-30 22:53:23,336 - INFO - [Trainer 50] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:23,344 - INFO - [Trainer 50] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:23,486 - INFO - 客户端 76 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:23,486 - INFO - 向客户端 76 发送全局模型 - 参数数量: 74
2025-07-30 22:53:23,486 - INFO - [Client 76] 收到直接传入的模型权重
2025-07-30 22:53:23,486 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:23,487 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:23,487 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:23,491 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:23,491 - INFO - [Client 76] 成功接收并加载模型
2025-07-30 22:53:23,491 - INFO - 客户端 76 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:23,491 - INFO - [Trainer.reset_staleness] 客户端 76 陈旧度重置
2025-07-30 22:53:23,491 - INFO - 重置客户端 76 的陈旧度
2025-07-30 22:53:23,491 - INFO - 成功发送全局模型到客户端 76
2025-07-30 22:53:23,491 - INFO - 客户端 76 开始本地训练
2025-07-30 22:53:23,491 - INFO - [客户端类型: Client, ID: 76] 准备开始训练
2025-07-30 22:53:23,491 - INFO - [客户端 76] 使用的训练器 client_id: 76
2025-07-30 22:53:23,491 - INFO - [Client 76] 开始验证训练集
2025-07-30 22:53:23,492 - INFO - [Client 76] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:23,492 - INFO - [Client 76] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:23,492 - INFO - [Trainer 76] 开始训练
2025-07-30 22:53:23,494 - INFO - [Trainer 76] 训练集大小: 300
2025-07-30 22:53:23,494 - INFO - [Trainer 76] 模型已移至设备: cpu
2025-07-30 22:53:23,494 - INFO - [Trainer 76] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:23,495 - INFO - [Trainer 76] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:23,495 - INFO - [Trainer 76] 开始训练 5 个epoch
2025-07-30 22:53:23,495 - INFO - [Trainer 76] 开始第 1/5 个epoch
2025-07-30 22:53:23,505 - INFO - [Trainer 76] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4507, y=[6, 6, 5, 6, 3]
2025-07-30 22:53:23,505 - INFO - [Trainer 76] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:23,513 - INFO - [Trainer 76] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:23,513 - INFO - 客户端 71 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:23,514 - INFO - 向客户端 71 发送全局模型 - 参数数量: 74
2025-07-30 22:53:23,514 - INFO - [Client 71] 收到直接传入的模型权重
2025-07-30 22:53:23,514 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:23,514 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:23,514 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:23,518 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:23,519 - INFO - [Client 71] 成功接收并加载模型
2025-07-30 22:53:23,519 - INFO - 客户端 71 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:23,519 - INFO - [Trainer.reset_staleness] 客户端 71 陈旧度重置
2025-07-30 22:53:23,519 - INFO - 重置客户端 71 的陈旧度
2025-07-30 22:53:23,519 - INFO - 成功发送全局模型到客户端 71
2025-07-30 22:53:23,519 - INFO - 客户端 71 开始本地训练
2025-07-30 22:53:23,519 - INFO - [客户端类型: Client, ID: 71] 准备开始训练
2025-07-30 22:53:23,519 - INFO - [客户端 71] 使用的训练器 client_id: 71
2025-07-30 22:53:23,519 - INFO - [Client 71] 开始验证训练集
2025-07-30 22:53:23,520 - INFO - [Client 71] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:23,520 - INFO - [Client 71] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:23,520 - INFO - [Trainer 71] 开始训练
2025-07-30 22:53:23,520 - INFO - [Trainer 71] 训练集大小: 300
2025-07-30 22:53:23,521 - INFO - [Trainer 71] 模型已移至设备: cpu
2025-07-30 22:53:23,521 - INFO - [Trainer 71] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:23,521 - INFO - [Trainer 71] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:23,522 - INFO - [Trainer 71] 开始训练 5 个epoch
2025-07-30 22:53:23,522 - INFO - [Trainer 71] 开始第 1/5 个epoch
2025-07-30 22:53:23,531 - INFO - [Trainer 71] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4962, y=[2, 2, 2, 4, 4]
2025-07-30 22:53:23,531 - INFO - [Trainer 71] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:23,538 - INFO - [Trainer 71] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:23,844 - INFO - 客户端 3 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:23,844 - INFO - 向客户端 3 发送全局模型 - 参数数量: 74
2025-07-30 22:53:23,844 - INFO - [Client 3] 收到直接传入的模型权重
2025-07-30 22:53:23,844 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:23,844 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:23,845 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:23,863 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:23,864 - INFO - [Client 3] 成功接收并加载模型
2025-07-30 22:53:23,864 - INFO - 客户端 3 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:23,864 - INFO - [Trainer.reset_staleness] 客户端 3 陈旧度重置
2025-07-30 22:53:23,864 - INFO - 重置客户端 3 的陈旧度
2025-07-30 22:53:23,864 - INFO - 成功发送全局模型到客户端 3
2025-07-30 22:53:23,864 - INFO - 客户端 3 开始本地训练
2025-07-30 22:53:23,864 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-07-30 22:53:23,864 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-07-30 22:53:23,864 - INFO - [Client 3] 开始验证训练集
2025-07-30 22:53:23,866 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:23,866 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:23,866 - INFO - [Trainer 3] 开始训练
2025-07-30 22:53:23,866 - INFO - [Trainer 3] 训练集大小: 300
2025-07-30 22:53:23,867 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-07-30 22:53:23,867 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:23,868 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:23,868 - INFO - [Trainer 3] 开始训练 5 个epoch
2025-07-30 22:53:23,868 - INFO - [Trainer 3] 开始第 1/5 个epoch
2025-07-30 22:53:23,891 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4574, y=[2, 2, 2, 2, 6]
2025-07-30 22:53:23,891 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:23,910 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:23,910 - INFO - 客户端 60 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:23,911 - INFO - 向客户端 60 发送全局模型 - 参数数量: 74
2025-07-30 22:53:23,911 - INFO - [Client 60] 收到直接传入的模型权重
2025-07-30 22:53:23,911 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:23,911 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:23,911 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:23,930 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:23,930 - INFO - [Client 60] 成功接收并加载模型
2025-07-30 22:53:23,930 - INFO - 客户端 60 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:23,930 - INFO - [Trainer.reset_staleness] 客户端 60 陈旧度重置
2025-07-30 22:53:23,930 - INFO - 重置客户端 60 的陈旧度
2025-07-30 22:53:23,930 - INFO - 成功发送全局模型到客户端 60
2025-07-30 22:53:23,930 - INFO - 客户端 60 开始本地训练
2025-07-30 22:53:23,930 - INFO - [客户端类型: Client, ID: 60] 准备开始训练
2025-07-30 22:53:23,930 - INFO - [客户端 60] 使用的训练器 client_id: 60
2025-07-30 22:53:23,930 - INFO - [Client 60] 开始验证训练集
2025-07-30 22:53:23,931 - INFO - [Client 60] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:23,932 - INFO - [Client 60] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:23,932 - INFO - [Trainer 60] 开始训练
2025-07-30 22:53:23,932 - INFO - [Trainer 60] 训练集大小: 300
2025-07-30 22:53:23,932 - INFO - [Trainer 60] 模型已移至设备: cpu
2025-07-30 22:53:23,932 - INFO - [Trainer 60] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:23,932 - INFO - [Trainer 60] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:23,933 - INFO - [Trainer 60] 开始训练 5 个epoch
2025-07-30 22:53:23,933 - INFO - [Trainer 60] 开始第 1/5 个epoch
2025-07-30 22:53:23,951 - INFO - [Trainer 60] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3357, y=[5, 5, 5, 5, 5]
2025-07-30 22:53:23,951 - INFO - [Trainer 60] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:23,966 - INFO - [Trainer 60] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:23,966 - INFO - 客户端 51 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:23,966 - INFO - 向客户端 51 发送全局模型 - 参数数量: 74
2025-07-30 22:53:23,966 - INFO - [Client 51] 收到直接传入的模型权重
2025-07-30 22:53:23,967 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:23,967 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:23,967 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:23,984 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:23,984 - INFO - [Client 51] 成功接收并加载模型
2025-07-30 22:53:23,985 - INFO - 客户端 51 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:23,985 - INFO - [Trainer.reset_staleness] 客户端 51 陈旧度重置
2025-07-30 22:53:23,985 - INFO - 重置客户端 51 的陈旧度
2025-07-30 22:53:23,985 - INFO - 成功发送全局模型到客户端 51
2025-07-30 22:53:23,985 - INFO - 客户端 51 开始本地训练
2025-07-30 22:53:23,985 - INFO - [客户端类型: Client, ID: 51] 准备开始训练
2025-07-30 22:53:23,985 - INFO - [客户端 51] 使用的训练器 client_id: 51
2025-07-30 22:53:23,985 - INFO - [Client 51] 开始验证训练集
2025-07-30 22:53:23,987 - INFO - [Client 51] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:23,988 - INFO - [Client 51] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:23,988 - INFO - [Trainer 51] 开始训练
2025-07-30 22:53:23,988 - INFO - [Trainer 51] 训练集大小: 300
2025-07-30 22:53:23,988 - INFO - [Trainer 51] 模型已移至设备: cpu
2025-07-30 22:53:23,988 - INFO - [Trainer 51] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:23,988 - INFO - [Trainer 51] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:23,989 - INFO - [Trainer 51] 开始训练 5 个epoch
2025-07-30 22:53:23,989 - INFO - [Trainer 51] 开始第 1/5 个epoch
2025-07-30 22:53:24,006 - INFO - [Trainer 51] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1699, y=[0, 0, 4, 0, 0]
2025-07-30 22:53:24,007 - INFO - [Trainer 51] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:24,022 - INFO - [Trainer 51] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:24,252 - INFO - 客户端 19 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:24,253 - INFO - 向客户端 19 发送全局模型 - 参数数量: 74
2025-07-30 22:53:24,253 - INFO - [Client 19] 收到直接传入的模型权重
2025-07-30 22:53:24,253 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:24,253 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:24,253 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:24,277 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:24,277 - INFO - [Client 19] 成功接收并加载模型
2025-07-30 22:53:24,277 - INFO - 客户端 19 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:24,277 - INFO - [Trainer.reset_staleness] 客户端 19 陈旧度重置
2025-07-30 22:53:24,278 - INFO - 重置客户端 19 的陈旧度
2025-07-30 22:53:24,278 - INFO - 成功发送全局模型到客户端 19
2025-07-30 22:53:24,278 - INFO - 客户端 19 开始本地训练
2025-07-30 22:53:24,278 - INFO - [客户端类型: Client, ID: 19] 准备开始训练
2025-07-30 22:53:24,278 - INFO - [客户端 19] 使用的训练器 client_id: 19
2025-07-30 22:53:24,278 - INFO - [Client 19] 开始验证训练集
2025-07-30 22:53:24,279 - INFO - [Client 19] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:24,279 - INFO - [Client 19] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:24,279 - INFO - [Trainer 19] 开始训练
2025-07-30 22:53:24,279 - INFO - [Trainer 19] 训练集大小: 300
2025-07-30 22:53:24,280 - INFO - [Trainer 19] 模型已移至设备: cpu
2025-07-30 22:53:24,280 - INFO - [Trainer 19] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:24,280 - INFO - [Trainer 19] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:24,281 - INFO - [Trainer 19] 开始训练 5 个epoch
2025-07-30 22:53:24,281 - INFO - [Trainer 19] 开始第 1/5 个epoch
2025-07-30 22:53:24,297 - INFO - [Trainer 19] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5058, y=[4, 4, 4, 4, 4]
2025-07-30 22:53:24,298 - INFO - [Trainer 19] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:24,312 - INFO - [Trainer 19] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:24,456 - INFO - 客户端 59 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:24,456 - INFO - 向客户端 59 发送全局模型 - 参数数量: 74
2025-07-30 22:53:24,457 - INFO - [Client 59] 收到直接传入的模型权重
2025-07-30 22:53:24,457 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:24,457 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:24,457 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:24,474 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:24,474 - INFO - [Client 59] 成功接收并加载模型
2025-07-30 22:53:24,474 - INFO - 客户端 59 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:24,474 - INFO - [Trainer.reset_staleness] 客户端 59 陈旧度重置
2025-07-30 22:53:24,474 - INFO - 重置客户端 59 的陈旧度
2025-07-30 22:53:24,474 - INFO - 成功发送全局模型到客户端 59
2025-07-30 22:53:24,474 - INFO - 客户端 59 开始本地训练
2025-07-30 22:53:24,474 - INFO - [客户端类型: Client, ID: 59] 准备开始训练
2025-07-30 22:53:24,474 - INFO - [客户端 59] 使用的训练器 client_id: 59
2025-07-30 22:53:24,476 - INFO - [Client 59] 开始验证训练集
2025-07-30 22:53:24,476 - INFO - [Client 59] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:24,476 - INFO - [Client 59] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:24,476 - INFO - [Trainer 59] 开始训练
2025-07-30 22:53:24,476 - INFO - [Trainer 59] 训练集大小: 300
2025-07-30 22:53:24,477 - INFO - [Trainer 59] 模型已移至设备: cpu
2025-07-30 22:53:24,477 - INFO - [Trainer 59] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:24,477 - INFO - [Trainer 59] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:24,477 - INFO - [Trainer 59] 开始训练 5 个epoch
2025-07-30 22:53:24,478 - INFO - [Trainer 59] 开始第 1/5 个epoch
2025-07-30 22:53:24,496 - INFO - [Trainer 59] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1993, y=[8, 8, 8, 8, 8]
2025-07-30 22:53:24,496 - INFO - [Trainer 59] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:24,511 - INFO - [Trainer 59] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:24,511 - INFO - 客户端 84 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:24,511 - INFO - 向客户端 84 发送全局模型 - 参数数量: 74
2025-07-30 22:53:24,511 - INFO - [Client 84] 收到直接传入的模型权重
2025-07-30 22:53:24,511 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:24,513 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:24,513 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:24,515 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:24,516 - INFO - [Client 84] 成功接收并加载模型
2025-07-30 22:53:24,516 - INFO - 客户端 84 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:24,516 - INFO - [Trainer.reset_staleness] 客户端 84 陈旧度重置
2025-07-30 22:53:24,516 - INFO - 重置客户端 84 的陈旧度
2025-07-30 22:53:24,516 - INFO - 成功发送全局模型到客户端 84
2025-07-30 22:53:24,516 - INFO - 客户端 84 开始本地训练
2025-07-30 22:53:24,516 - INFO - [客户端类型: Client, ID: 84] 准备开始训练
2025-07-30 22:53:24,516 - INFO - [客户端 84] 使用的训练器 client_id: 84
2025-07-30 22:53:24,516 - INFO - [Client 84] 开始验证训练集
2025-07-30 22:53:24,516 - INFO - [Client 84] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:24,517 - INFO - [Client 84] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:24,517 - INFO - [Trainer 84] 开始训练
2025-07-30 22:53:24,517 - INFO - [Trainer 84] 训练集大小: 300
2025-07-30 22:53:24,517 - INFO - [Trainer 84] 模型已移至设备: cpu
2025-07-30 22:53:24,517 - INFO - [Trainer 84] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:24,517 - INFO - [Trainer 84] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:24,518 - INFO - [Trainer 84] 开始训练 5 个epoch
2025-07-30 22:53:24,518 - INFO - [Trainer 84] 开始第 1/5 个epoch
2025-07-30 22:53:24,523 - INFO - [Trainer 84] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3501, y=[7, 5, 8, 8, 5]
2025-07-30 22:53:24,523 - INFO - [Trainer 84] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:24,527 - INFO - [Trainer 84] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:24,528 - INFO - 客户端 54 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:24,528 - INFO - 向客户端 54 发送全局模型 - 参数数量: 74
2025-07-30 22:53:24,528 - INFO - [Client 54] 收到直接传入的模型权重
2025-07-30 22:53:24,528 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:24,528 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:24,528 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:24,553 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:24,553 - INFO - [Client 54] 成功接收并加载模型
2025-07-30 22:53:24,553 - INFO - 客户端 54 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:24,553 - INFO - [Trainer.reset_staleness] 客户端 54 陈旧度重置
2025-07-30 22:53:24,553 - INFO - 重置客户端 54 的陈旧度
2025-07-30 22:53:24,553 - INFO - 成功发送全局模型到客户端 54
2025-07-30 22:53:24,554 - INFO - 客户端 54 开始本地训练
2025-07-30 22:53:24,554 - INFO - [客户端类型: Client, ID: 54] 准备开始训练
2025-07-30 22:53:24,554 - INFO - [客户端 54] 使用的训练器 client_id: 54
2025-07-30 22:53:24,554 - INFO - [Client 54] 开始验证训练集
2025-07-30 22:53:24,555 - INFO - [Client 54] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:24,555 - INFO - [Client 54] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:24,555 - INFO - [Trainer 54] 开始训练
2025-07-30 22:53:24,555 - INFO - [Trainer 54] 训练集大小: 300
2025-07-30 22:53:24,556 - INFO - [Trainer 54] 模型已移至设备: cpu
2025-07-30 22:53:24,556 - INFO - [Trainer 54] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:24,556 - INFO - [Trainer 54] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:24,556 - INFO - [Trainer 54] 开始训练 5 个epoch
2025-07-30 22:53:24,556 - INFO - [Trainer 54] 开始第 1/5 个epoch
2025-07-30 22:53:24,575 - INFO - [Trainer 54] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4410, y=[3, 3, 9, 3, 4]
2025-07-30 22:53:24,575 - INFO - [Trainer 54] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:24,588 - INFO - [Trainer 54] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:24,588 - INFO - 客户端 100 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:24,588 - INFO - 向客户端 100 发送全局模型 - 参数数量: 74
2025-07-30 22:53:24,588 - INFO - [Client 100] 收到直接传入的模型权重
2025-07-30 22:53:24,588 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:24,588 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:24,588 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:24,590 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:24,590 - INFO - [Client 100] 成功接收并加载模型
2025-07-30 22:53:24,591 - INFO - 客户端 100 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:24,591 - INFO - [Trainer.reset_staleness] 客户端 100 陈旧度重置
2025-07-30 22:53:24,591 - INFO - 重置客户端 100 的陈旧度
2025-07-30 22:53:24,591 - INFO - 成功发送全局模型到客户端 100
2025-07-30 22:53:24,591 - INFO - 客户端 100 开始本地训练
2025-07-30 22:53:24,591 - INFO - [客户端类型: Client, ID: 100] 准备开始训练
2025-07-30 22:53:24,591 - INFO - [客户端 100] 使用的训练器 client_id: 100
2025-07-30 22:53:24,591 - INFO - [Client 100] 开始验证训练集
2025-07-30 22:53:24,591 - INFO - [Client 100] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:24,591 - INFO - [Client 100] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:24,592 - INFO - [Trainer 100] 开始训练
2025-07-30 22:53:24,592 - INFO - [Trainer 100] 训练集大小: 300
2025-07-30 22:53:24,592 - INFO - [Trainer 100] 模型已移至设备: cpu
2025-07-30 22:53:24,592 - INFO - [Trainer 100] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:24,592 - INFO - [Trainer 100] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:24,593 - INFO - [Trainer 100] 开始训练 5 个epoch
2025-07-30 22:53:24,593 - INFO - [Trainer 100] 开始第 1/5 个epoch
2025-07-30 22:53:24,599 - INFO - [Trainer 100] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2247, y=[8, 7, 8, 8, 8]
2025-07-30 22:53:24,599 - INFO - [Trainer 100] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:24,604 - INFO - [Trainer 100] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:24,856 - INFO - 客户端 9 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:24,856 - INFO - 向客户端 9 发送全局模型 - 参数数量: 74
2025-07-30 22:53:24,856 - INFO - [Client 9] 收到直接传入的模型权重
2025-07-30 22:53:24,857 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:24,857 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:24,857 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:24,885 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:24,885 - INFO - [Client 9] 成功接收并加载模型
2025-07-30 22:53:24,886 - INFO - 客户端 9 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:24,886 - INFO - [Trainer.reset_staleness] 客户端 9 陈旧度重置
2025-07-30 22:53:24,886 - INFO - 重置客户端 9 的陈旧度
2025-07-30 22:53:24,886 - INFO - 成功发送全局模型到客户端 9
2025-07-30 22:53:24,886 - INFO - 客户端 9 开始本地训练
2025-07-30 22:53:24,886 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-07-30 22:53:24,886 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-07-30 22:53:24,886 - INFO - [Client 9] 开始验证训练集
2025-07-30 22:53:24,887 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:24,887 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:24,887 - INFO - [Trainer 9] 开始训练
2025-07-30 22:53:24,887 - INFO - [Trainer 9] 训练集大小: 300
2025-07-30 22:53:24,889 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-07-30 22:53:24,889 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:24,889 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:24,890 - INFO - [Trainer 9] 开始训练 5 个epoch
2025-07-30 22:53:24,890 - INFO - [Trainer 9] 开始第 1/5 个epoch
2025-07-30 22:53:24,908 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3713, y=[6, 6, 6, 6, 6]
2025-07-30 22:53:24,909 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:24,925 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:25,099 - INFO - 客户端 98 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:25,099 - INFO - 向客户端 98 发送全局模型 - 参数数量: 74
2025-07-30 22:53:25,100 - INFO - [Client 98] 收到直接传入的模型权重
2025-07-30 22:53:25,100 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:25,100 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:25,100 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:25,104 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:25,104 - INFO - [Client 98] 成功接收并加载模型
2025-07-30 22:53:25,104 - INFO - 客户端 98 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:25,104 - INFO - [Trainer.reset_staleness] 客户端 98 陈旧度重置
2025-07-30 22:53:25,104 - INFO - 重置客户端 98 的陈旧度
2025-07-30 22:53:25,104 - INFO - 成功发送全局模型到客户端 98
2025-07-30 22:53:25,104 - INFO - 客户端 98 开始本地训练
2025-07-30 22:53:25,105 - INFO - [客户端类型: Client, ID: 98] 准备开始训练
2025-07-30 22:53:25,105 - INFO - [客户端 98] 使用的训练器 client_id: 98
2025-07-30 22:53:25,105 - INFO - [Client 98] 开始验证训练集
2025-07-30 22:53:25,105 - INFO - [Client 98] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:25,105 - INFO - [Client 98] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:25,105 - INFO - [Trainer 98] 开始训练
2025-07-30 22:53:25,105 - INFO - [Trainer 98] 训练集大小: 300
2025-07-30 22:53:25,106 - INFO - [Trainer 98] 模型已移至设备: cpu
2025-07-30 22:53:25,106 - INFO - [Trainer 98] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:25,107 - INFO - [Trainer 98] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:25,107 - INFO - [Trainer 98] 开始训练 5 个epoch
2025-07-30 22:53:25,107 - INFO - [Trainer 98] 开始第 1/5 个epoch
2025-07-30 22:53:25,116 - INFO - [Trainer 98] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4094, y=[9, 9, 9, 6, 6]
2025-07-30 22:53:25,116 - INFO - [Trainer 98] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:25,123 - INFO - [Trainer 98] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:25,123 - INFO - 客户端 7 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:25,123 - INFO - 向客户端 7 发送全局模型 - 参数数量: 74
2025-07-30 22:53:25,124 - INFO - [Client 7] 收到直接传入的模型权重
2025-07-30 22:53:25,124 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:25,124 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:25,124 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:25,173 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:25,173 - INFO - [Client 7] 成功接收并加载模型
2025-07-30 22:53:25,173 - INFO - 客户端 7 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:25,173 - INFO - [Trainer.reset_staleness] 客户端 7 陈旧度重置
2025-07-30 22:53:25,173 - INFO - 重置客户端 7 的陈旧度
2025-07-30 22:53:25,173 - INFO - 成功发送全局模型到客户端 7
2025-07-30 22:53:25,173 - INFO - 客户端 7 开始本地训练
2025-07-30 22:53:25,173 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-07-30 22:53:25,173 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-07-30 22:53:25,174 - INFO - [Client 7] 开始验证训练集
2025-07-30 22:53:25,178 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:25,178 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:25,178 - INFO - [Trainer 7] 开始训练
2025-07-30 22:53:25,178 - INFO - [Trainer 7] 训练集大小: 300
2025-07-30 22:53:25,179 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-07-30 22:53:25,179 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:25,179 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:25,179 - INFO - [Trainer 7] 开始训练 5 个epoch
2025-07-30 22:53:25,180 - INFO - [Trainer 7] 开始第 1/5 个epoch
2025-07-30 22:53:25,219 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4463, y=[2, 2, 2, 2, 2]
2025-07-30 22:53:25,219 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:25,247 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:25,248 - INFO - 客户端 41 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:25,248 - INFO - 向客户端 41 发送全局模型 - 参数数量: 74
2025-07-30 22:53:25,248 - INFO - [Client 41] 收到直接传入的模型权重
2025-07-30 22:53:25,248 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:25,248 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:25,248 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:25,272 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:25,273 - INFO - [Client 41] 成功接收并加载模型
2025-07-30 22:53:25,273 - INFO - 客户端 41 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:25,273 - INFO - [Trainer.reset_staleness] 客户端 41 陈旧度重置
2025-07-30 22:53:25,273 - INFO - 重置客户端 41 的陈旧度
2025-07-30 22:53:25,273 - INFO - 成功发送全局模型到客户端 41
2025-07-30 22:53:25,273 - INFO - 客户端 41 开始本地训练
2025-07-30 22:53:25,273 - INFO - [客户端类型: Client, ID: 41] 准备开始训练
2025-07-30 22:53:25,273 - INFO - [客户端 41] 使用的训练器 client_id: 41
2025-07-30 22:53:25,274 - INFO - [Client 41] 开始验证训练集
2025-07-30 22:53:25,275 - INFO - [Client 41] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:25,276 - INFO - [Client 41] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:25,276 - INFO - [Trainer 41] 开始训练
2025-07-30 22:53:25,276 - INFO - [Trainer 41] 训练集大小: 300
2025-07-30 22:53:25,276 - INFO - [Trainer 41] 模型已移至设备: cpu
2025-07-30 22:53:25,276 - INFO - [Trainer 41] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:25,277 - INFO - [Trainer 41] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:25,277 - INFO - [Trainer 41] 开始训练 5 个epoch
2025-07-30 22:53:25,277 - INFO - [Trainer 41] 开始第 1/5 个epoch
2025-07-30 22:53:25,298 - INFO - [Trainer 41] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5147, y=[9, 4, 4, 9, 4]
2025-07-30 22:53:25,299 - INFO - [Trainer 41] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:25,325 - INFO - [Trainer 41] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:25,326 - INFO - 客户端 42 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:25,326 - INFO - 向客户端 42 发送全局模型 - 参数数量: 74
2025-07-30 22:53:25,326 - INFO - [Client 42] 收到直接传入的模型权重
2025-07-30 22:53:25,326 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:25,326 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:25,326 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:25,354 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:25,354 - INFO - [Client 42] 成功接收并加载模型
2025-07-30 22:53:25,354 - INFO - 客户端 42 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:25,354 - INFO - [Trainer.reset_staleness] 客户端 42 陈旧度重置
2025-07-30 22:53:25,354 - INFO - 重置客户端 42 的陈旧度
2025-07-30 22:53:25,354 - INFO - 成功发送全局模型到客户端 42
2025-07-30 22:53:25,355 - INFO - 客户端 42 开始本地训练
2025-07-30 22:53:25,355 - INFO - [客户端类型: Client, ID: 42] 准备开始训练
2025-07-30 22:53:25,355 - INFO - [客户端 42] 使用的训练器 client_id: 42
2025-07-30 22:53:25,355 - INFO - [Client 42] 开始验证训练集
2025-07-30 22:53:25,359 - INFO - [Client 42] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:25,359 - INFO - [Client 42] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:25,359 - INFO - [Trainer 42] 开始训练
2025-07-30 22:53:25,359 - INFO - [Trainer 42] 训练集大小: 300
2025-07-30 22:53:25,360 - INFO - [Trainer 42] 模型已移至设备: cpu
2025-07-30 22:53:25,360 - INFO - [Trainer 42] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:25,360 - INFO - [Trainer 42] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:25,360 - INFO - [Trainer 42] 开始训练 5 个epoch
2025-07-30 22:53:25,360 - INFO - [Trainer 42] 开始第 1/5 个epoch
2025-07-30 22:53:25,398 - INFO - [Trainer 42] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4799, y=[6, 6, 1, 6, 6]
2025-07-30 22:53:25,398 - INFO - [Trainer 42] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:25,428 - INFO - [Trainer 42] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:25,646 - INFO - 客户端 92 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:25,646 - INFO - 向客户端 92 发送全局模型 - 参数数量: 74
2025-07-30 22:53:25,647 - INFO - [Client 92] 收到直接传入的模型权重
2025-07-30 22:53:25,647 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:25,647 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:25,647 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:25,650 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:25,651 - INFO - [Client 92] 成功接收并加载模型
2025-07-30 22:53:25,651 - INFO - 客户端 92 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:25,651 - INFO - [Trainer.reset_staleness] 客户端 92 陈旧度重置
2025-07-30 22:53:25,651 - INFO - 重置客户端 92 的陈旧度
2025-07-30 22:53:25,651 - INFO - 成功发送全局模型到客户端 92
2025-07-30 22:53:25,652 - INFO - 客户端 92 开始本地训练
2025-07-30 22:53:25,652 - INFO - [客户端类型: Client, ID: 92] 准备开始训练
2025-07-30 22:53:25,652 - INFO - [客户端 92] 使用的训练器 client_id: 92
2025-07-30 22:53:25,652 - INFO - [Client 92] 开始验证训练集
2025-07-30 22:53:25,653 - INFO - [Client 92] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:25,653 - INFO - [Client 92] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:25,653 - INFO - [Trainer 92] 开始训练
2025-07-30 22:53:25,653 - INFO - [Trainer 92] 训练集大小: 300
2025-07-30 22:53:25,654 - INFO - [Trainer 92] 模型已移至设备: cpu
2025-07-30 22:53:25,654 - INFO - [Trainer 92] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:25,655 - INFO - [Trainer 92] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:25,655 - INFO - [Trainer 92] 开始训练 5 个epoch
2025-07-30 22:53:25,655 - INFO - [Trainer 92] 开始第 1/5 个epoch
2025-07-30 22:53:25,663 - INFO - [Trainer 92] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0847, y=[0, 0, 0, 9, 0]
2025-07-30 22:53:25,663 - INFO - [Trainer 92] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:25,671 - INFO - [Trainer 92] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:25,878 - INFO - 客户端 63 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:25,878 - INFO - 向客户端 63 发送全局模型 - 参数数量: 74
2025-07-30 22:53:25,879 - INFO - [Client 63] 收到直接传入的模型权重
2025-07-30 22:53:25,879 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:25,879 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:25,879 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:25,882 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:25,882 - INFO - [Client 63] 成功接收并加载模型
2025-07-30 22:53:25,882 - INFO - 客户端 63 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:25,882 - INFO - [Trainer.reset_staleness] 客户端 63 陈旧度重置
2025-07-30 22:53:25,882 - INFO - 重置客户端 63 的陈旧度
2025-07-30 22:53:25,883 - INFO - 成功发送全局模型到客户端 63
2025-07-30 22:53:25,883 - INFO - 客户端 63 开始本地训练
2025-07-30 22:53:25,883 - INFO - [客户端类型: Client, ID: 63] 准备开始训练
2025-07-30 22:53:25,883 - INFO - [客户端 63] 使用的训练器 client_id: 63
2025-07-30 22:53:25,883 - INFO - [Client 63] 开始验证训练集
2025-07-30 22:53:25,884 - INFO - [Client 63] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:25,884 - INFO - [Client 63] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:25,885 - INFO - [Trainer 63] 开始训练
2025-07-30 22:53:25,885 - INFO - [Trainer 63] 训练集大小: 300
2025-07-30 22:53:25,885 - INFO - [Trainer 63] 模型已移至设备: cpu
2025-07-30 22:53:25,885 - INFO - [Trainer 63] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:25,886 - INFO - [Trainer 63] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:25,886 - INFO - [Trainer 63] 开始训练 5 个epoch
2025-07-30 22:53:25,886 - INFO - [Trainer 63] 开始第 1/5 个epoch
2025-07-30 22:53:25,920 - INFO - [Trainer 63] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4816, y=[6, 7, 9, 9, 7]
2025-07-30 22:53:25,920 - INFO - [Trainer 63] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:25,948 - INFO - [Trainer 63] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:25,949 - INFO - [Trainer 32] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1682
2025-07-30 22:53:25,949 - INFO - [Trainer 32] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:25,949 - INFO - [Trainer 32] 标签样本: [0, 6, 4, 8, 0]
2025-07-30 22:53:25,996 - INFO - [Trainer 32] Batch 0, Loss: 2.3159
2025-07-30 22:53:26,128 - INFO - 客户端 62 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:26,128 - INFO - 向客户端 62 发送全局模型 - 参数数量: 74
2025-07-30 22:53:26,128 - INFO - [Client 62] 收到直接传入的模型权重
2025-07-30 22:53:26,129 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:26,129 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:26,129 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:26,133 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:26,133 - INFO - [Client 62] 成功接收并加载模型
2025-07-30 22:53:26,134 - INFO - 客户端 62 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:26,134 - INFO - [Trainer.reset_staleness] 客户端 62 陈旧度重置
2025-07-30 22:53:26,134 - INFO - 重置客户端 62 的陈旧度
2025-07-30 22:53:26,134 - INFO - 成功发送全局模型到客户端 62
2025-07-30 22:53:26,134 - INFO - 客户端 62 开始本地训练
2025-07-30 22:53:26,134 - INFO - [客户端类型: Client, ID: 62] 准备开始训练
2025-07-30 22:53:26,134 - INFO - [客户端 62] 使用的训练器 client_id: 62
2025-07-30 22:53:26,134 - INFO - [Client 62] 开始验证训练集
2025-07-30 22:53:26,135 - INFO - [Client 62] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:26,135 - INFO - [Client 62] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:26,136 - INFO - [Trainer 62] 开始训练
2025-07-30 22:53:26,136 - INFO - [Trainer 62] 训练集大小: 300
2025-07-30 22:53:26,136 - INFO - [Trainer 62] 模型已移至设备: cpu
2025-07-30 22:53:26,136 - INFO - [Trainer 62] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:26,137 - INFO - [Trainer 62] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:26,137 - INFO - [Trainer 62] 开始训练 5 个epoch
2025-07-30 22:53:26,137 - INFO - [Trainer 62] 开始第 1/5 个epoch
2025-07-30 22:53:26,160 - INFO - [Trainer 62] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3309, y=[1, 9, 9, 1, 9]
2025-07-30 22:53:26,160 - INFO - [Trainer 62] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:26,177 - INFO - [Trainer 62] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:26,177 - INFO - 客户端 48 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:26,177 - INFO - 向客户端 48 发送全局模型 - 参数数量: 74
2025-07-30 22:53:26,177 - INFO - [Client 48] 收到直接传入的模型权重
2025-07-30 22:53:26,178 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:26,178 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:26,178 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:26,200 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:26,200 - INFO - [Client 48] 成功接收并加载模型
2025-07-30 22:53:26,200 - INFO - 客户端 48 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:26,200 - INFO - [Trainer.reset_staleness] 客户端 48 陈旧度重置
2025-07-30 22:53:26,200 - INFO - 重置客户端 48 的陈旧度
2025-07-30 22:53:26,201 - INFO - 成功发送全局模型到客户端 48
2025-07-30 22:53:26,201 - INFO - 客户端 48 开始本地训练
2025-07-30 22:53:26,201 - INFO - [客户端类型: Client, ID: 48] 准备开始训练
2025-07-30 22:53:26,201 - INFO - [客户端 48] 使用的训练器 client_id: 48
2025-07-30 22:53:26,201 - INFO - [Client 48] 开始验证训练集
2025-07-30 22:53:26,203 - INFO - [Client 48] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:26,203 - INFO - [Client 48] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:26,203 - INFO - [Trainer 48] 开始训练
2025-07-30 22:53:26,203 - INFO - [Trainer 48] 训练集大小: 300
2025-07-30 22:53:26,203 - INFO - [Trainer 48] 模型已移至设备: cpu
2025-07-30 22:53:26,203 - INFO - [Trainer 48] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:26,204 - INFO - [Trainer 48] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:26,204 - INFO - [Trainer 48] 开始训练 5 个epoch
2025-07-30 22:53:26,204 - INFO - [Trainer 48] 开始第 1/5 个epoch
2025-07-30 22:53:26,225 - INFO - [Trainer 48] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2826, y=[2, 2, 3, 3, 3]
2025-07-30 22:53:26,225 - INFO - [Trainer 48] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:26,246 - INFO - [Trainer 48] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:26,247 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0327
2025-07-30 22:53:26,247 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:26,247 - INFO - [Trainer 4] 标签样本: [1, 0, 0, 0, 0]
2025-07-30 22:53:26,287 - INFO - [Trainer 4] Batch 0, Loss: 2.3829
2025-07-30 22:53:26,455 - INFO - 客户端 35 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:26,455 - INFO - 向客户端 35 发送全局模型 - 参数数量: 74
2025-07-30 22:53:26,455 - INFO - [Client 35] 收到直接传入的模型权重
2025-07-30 22:53:26,456 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:26,456 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:26,456 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:26,485 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:26,485 - INFO - [Client 35] 成功接收并加载模型
2025-07-30 22:53:26,486 - INFO - 客户端 35 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:26,486 - INFO - [Trainer.reset_staleness] 客户端 35 陈旧度重置
2025-07-30 22:53:26,486 - INFO - 重置客户端 35 的陈旧度
2025-07-30 22:53:26,486 - INFO - 成功发送全局模型到客户端 35
2025-07-30 22:53:26,486 - INFO - 客户端 35 开始本地训练
2025-07-30 22:53:26,486 - INFO - [客户端类型: Client, ID: 35] 准备开始训练
2025-07-30 22:53:26,486 - INFO - [客户端 35] 使用的训练器 client_id: 35
2025-07-30 22:53:26,486 - INFO - [Client 35] 开始验证训练集
2025-07-30 22:53:26,489 - INFO - [Client 35] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:26,489 - INFO - [Client 35] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:26,489 - INFO - [Trainer 35] 开始训练
2025-07-30 22:53:26,489 - INFO - [Trainer 35] 训练集大小: 300
2025-07-30 22:53:26,490 - INFO - [Trainer 35] 模型已移至设备: cpu
2025-07-30 22:53:26,490 - INFO - [Trainer 35] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:26,490 - INFO - [Trainer 35] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:26,490 - INFO - [Trainer 35] 开始训练 5 个epoch
2025-07-30 22:53:26,490 - INFO - [Trainer 35] 开始第 1/5 个epoch
2025-07-30 22:53:26,511 - INFO - [Trainer 35] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4769, y=[6, 6, 6, 6, 6]
2025-07-30 22:53:26,511 - INFO - [Trainer 35] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:26,529 - INFO - [Trainer 35] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:26,529 - INFO - 客户端 23 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:26,529 - INFO - 向客户端 23 发送全局模型 - 参数数量: 74
2025-07-30 22:53:26,529 - INFO - [Client 23] 收到直接传入的模型权重
2025-07-30 22:53:26,529 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:26,529 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:26,529 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:26,558 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:26,558 - INFO - [Client 23] 成功接收并加载模型
2025-07-30 22:53:26,558 - INFO - 客户端 23 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:26,558 - INFO - [Trainer.reset_staleness] 客户端 23 陈旧度重置
2025-07-30 22:53:26,558 - INFO - 重置客户端 23 的陈旧度
2025-07-30 22:53:26,558 - INFO - 成功发送全局模型到客户端 23
2025-07-30 22:53:26,559 - INFO - 客户端 23 开始本地训练
2025-07-30 22:53:26,559 - INFO - [客户端类型: Client, ID: 23] 准备开始训练
2025-07-30 22:53:26,559 - INFO - [客户端 23] 使用的训练器 client_id: 23
2025-07-30 22:53:26,559 - INFO - [Client 23] 开始验证训练集
2025-07-30 22:53:26,561 - INFO - [Client 23] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:26,561 - INFO - [Client 23] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:26,561 - INFO - [Trainer 23] 开始训练
2025-07-30 22:53:26,561 - INFO - [Trainer 23] 训练集大小: 300
2025-07-30 22:53:26,562 - INFO - [Trainer 23] 模型已移至设备: cpu
2025-07-30 22:53:26,562 - INFO - [Trainer 23] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:26,562 - INFO - [Trainer 23] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:26,563 - INFO - [Trainer 23] 开始训练 5 个epoch
2025-07-30 22:53:26,563 - INFO - [Trainer 23] 开始第 1/5 个epoch
2025-07-30 22:53:26,582 - INFO - [Trainer 23] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4393, y=[6, 6, 6, 6, 6]
2025-07-30 22:53:26,582 - INFO - [Trainer 23] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:26,598 - INFO - [Trainer 23] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:26,599 - INFO - 客户端 66 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:26,599 - INFO - 向客户端 66 发送全局模型 - 参数数量: 74
2025-07-30 22:53:26,599 - INFO - [Client 66] 收到直接传入的模型权重
2025-07-30 22:53:26,599 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:26,599 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:26,599 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:26,602 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:26,602 - INFO - [Client 66] 成功接收并加载模型
2025-07-30 22:53:26,602 - INFO - 客户端 66 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:26,602 - INFO - [Trainer.reset_staleness] 客户端 66 陈旧度重置
2025-07-30 22:53:26,602 - INFO - 重置客户端 66 的陈旧度
2025-07-30 22:53:26,602 - INFO - 成功发送全局模型到客户端 66
2025-07-30 22:53:26,602 - INFO - 客户端 66 开始本地训练
2025-07-30 22:53:26,602 - INFO - [客户端类型: Client, ID: 66] 准备开始训练
2025-07-30 22:53:26,602 - INFO - [客户端 66] 使用的训练器 client_id: 66
2025-07-30 22:53:26,602 - INFO - [Client 66] 开始验证训练集
2025-07-30 22:53:26,603 - INFO - [Client 66] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:26,603 - INFO - [Client 66] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:26,603 - INFO - [Trainer 66] 开始训练
2025-07-30 22:53:26,603 - INFO - [Trainer 66] 训练集大小: 300
2025-07-30 22:53:26,603 - INFO - [Trainer 66] 模型已移至设备: cpu
2025-07-30 22:53:26,604 - INFO - [Trainer 66] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:26,604 - INFO - [Trainer 66] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:26,604 - INFO - [Trainer 66] 开始训练 5 个epoch
2025-07-30 22:53:26,604 - INFO - [Trainer 66] 开始第 1/5 个epoch
2025-07-30 22:53:26,614 - INFO - [Trainer 66] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3578, y=[9, 3, 7, 8, 7]
2025-07-30 22:53:26,614 - INFO - [Trainer 66] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:26,624 - INFO - [Trainer 66] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:26,624 - INFO - 客户端 36 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:26,624 - INFO - 向客户端 36 发送全局模型 - 参数数量: 74
2025-07-30 22:53:26,624 - INFO - [Client 36] 收到直接传入的模型权重
2025-07-30 22:53:26,625 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:26,625 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:26,625 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:26,653 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:26,653 - INFO - [Client 36] 成功接收并加载模型
2025-07-30 22:53:26,653 - INFO - 客户端 36 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:26,653 - INFO - [Trainer.reset_staleness] 客户端 36 陈旧度重置
2025-07-30 22:53:26,653 - INFO - 重置客户端 36 的陈旧度
2025-07-30 22:53:26,653 - INFO - 成功发送全局模型到客户端 36
2025-07-30 22:53:26,653 - INFO - 客户端 36 开始本地训练
2025-07-30 22:53:26,653 - INFO - [客户端类型: Client, ID: 36] 准备开始训练
2025-07-30 22:53:26,653 - INFO - [客户端 36] 使用的训练器 client_id: 36
2025-07-30 22:53:26,653 - INFO - [Client 36] 开始验证训练集
2025-07-30 22:53:26,655 - INFO - [Client 36] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:26,655 - INFO - [Client 36] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:26,655 - INFO - [Trainer 36] 开始训练
2025-07-30 22:53:26,655 - INFO - [Trainer 36] 训练集大小: 300
2025-07-30 22:53:26,655 - INFO - [Trainer 36] 模型已移至设备: cpu
2025-07-30 22:53:26,656 - INFO - [Trainer 36] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:26,656 - INFO - [Trainer 36] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:26,656 - INFO - [Trainer 36] 开始训练 5 个epoch
2025-07-30 22:53:26,656 - INFO - [Trainer 36] 开始第 1/5 个epoch
2025-07-30 22:53:26,674 - INFO - [Trainer 36] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3836, y=[0, 1, 5, 5, 1]
2025-07-30 22:53:26,674 - INFO - [Trainer 36] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:26,688 - INFO - [Trainer 36] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:26,894 - INFO - 客户端 21 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:26,894 - INFO - 向客户端 21 发送全局模型 - 参数数量: 74
2025-07-30 22:53:26,895 - INFO - [Client 21] 收到直接传入的模型权重
2025-07-30 22:53:26,895 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:26,895 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:26,895 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:26,928 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:26,928 - INFO - [Client 21] 成功接收并加载模型
2025-07-30 22:53:26,928 - INFO - 客户端 21 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:26,928 - INFO - [Trainer.reset_staleness] 客户端 21 陈旧度重置
2025-07-30 22:53:26,928 - INFO - 重置客户端 21 的陈旧度
2025-07-30 22:53:26,928 - INFO - 成功发送全局模型到客户端 21
2025-07-30 22:53:26,928 - INFO - 客户端 21 开始本地训练
2025-07-30 22:53:26,928 - INFO - [客户端类型: Client, ID: 21] 准备开始训练
2025-07-30 22:53:26,928 - INFO - [客户端 21] 使用的训练器 client_id: 21
2025-07-30 22:53:26,928 - INFO - [Client 21] 开始验证训练集
2025-07-30 22:53:26,930 - INFO - [Client 21] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:26,930 - INFO - [Client 21] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:26,930 - INFO - [Trainer 21] 开始训练
2025-07-30 22:53:26,930 - INFO - [Trainer 21] 训练集大小: 300
2025-07-30 22:53:26,930 - INFO - [Trainer 21] 模型已移至设备: cpu
2025-07-30 22:53:26,932 - INFO - [Trainer 21] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:26,932 - INFO - [Trainer 21] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:26,932 - INFO - [Trainer 21] 开始训练 5 个epoch
2025-07-30 22:53:26,932 - INFO - [Trainer 21] 开始第 1/5 个epoch
2025-07-30 22:53:26,952 - INFO - [Trainer 21] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2847, y=[7, 7, 7, 7, 7]
2025-07-30 22:53:26,952 - INFO - [Trainer 21] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:26,969 - INFO - [Trainer 21] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:26,970 - INFO - [Trainer 20] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5924
2025-07-30 22:53:26,970 - INFO - [Trainer 20] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:26,971 - INFO - [Trainer 20] 标签样本: [9, 1, 9, 9, 1]
2025-07-30 22:53:27,011 - INFO - [Trainer 20] Batch 0, Loss: 2.5796
2025-07-30 22:53:27,140 - INFO - 客户端 46 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:27,140 - INFO - 向客户端 46 发送全局模型 - 参数数量: 74
2025-07-30 22:53:27,141 - INFO - [Client 46] 收到直接传入的模型权重
2025-07-30 22:53:27,141 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:27,141 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:27,142 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:27,187 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:27,187 - INFO - [Client 46] 成功接收并加载模型
2025-07-30 22:53:27,188 - INFO - 客户端 46 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:27,188 - INFO - [Trainer.reset_staleness] 客户端 46 陈旧度重置
2025-07-30 22:53:27,188 - INFO - 重置客户端 46 的陈旧度
2025-07-30 22:53:27,188 - INFO - 成功发送全局模型到客户端 46
2025-07-30 22:53:27,188 - INFO - 客户端 46 开始本地训练
2025-07-30 22:53:27,188 - INFO - [客户端类型: Client, ID: 46] 准备开始训练
2025-07-30 22:53:27,188 - INFO - [客户端 46] 使用的训练器 client_id: 46
2025-07-30 22:53:27,188 - INFO - [Client 46] 开始验证训练集
2025-07-30 22:53:27,190 - INFO - [Client 46] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:27,190 - INFO - [Client 46] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:27,190 - INFO - [Trainer 46] 开始训练
2025-07-30 22:53:27,190 - INFO - [Trainer 46] 训练集大小: 300
2025-07-30 22:53:27,191 - INFO - [Trainer 46] 模型已移至设备: cpu
2025-07-30 22:53:27,191 - INFO - [Trainer 46] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:27,191 - INFO - [Trainer 46] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:27,191 - INFO - [Trainer 46] 开始训练 5 个epoch
2025-07-30 22:53:27,191 - INFO - [Trainer 46] 开始第 1/5 个epoch
2025-07-30 22:53:27,237 - INFO - [Trainer 46] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2188, y=[4, 4, 7, 0, 4]
2025-07-30 22:53:27,237 - INFO - [Trainer 46] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:27,264 - INFO - [Trainer 46] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:27,264 - INFO - 客户端 55 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:27,265 - INFO - 向客户端 55 发送全局模型 - 参数数量: 74
2025-07-30 22:53:27,265 - INFO - [Client 55] 收到直接传入的模型权重
2025-07-30 22:53:27,265 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:27,265 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:27,265 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:27,300 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:27,300 - INFO - [Client 55] 成功接收并加载模型
2025-07-30 22:53:27,300 - INFO - 客户端 55 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:27,300 - INFO - [Trainer.reset_staleness] 客户端 55 陈旧度重置
2025-07-30 22:53:27,300 - INFO - 重置客户端 55 的陈旧度
2025-07-30 22:53:27,300 - INFO - 成功发送全局模型到客户端 55
2025-07-30 22:53:27,300 - INFO - 客户端 55 开始本地训练
2025-07-30 22:53:27,300 - INFO - [客户端类型: Client, ID: 55] 准备开始训练
2025-07-30 22:53:27,300 - INFO - [客户端 55] 使用的训练器 client_id: 55
2025-07-30 22:53:27,300 - INFO - [Client 55] 开始验证训练集
2025-07-30 22:53:27,303 - INFO - [Client 55] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:27,303 - INFO - [Client 55] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:27,303 - INFO - [Trainer 55] 开始训练
2025-07-30 22:53:27,303 - INFO - [Trainer 55] 训练集大小: 300
2025-07-30 22:53:27,304 - INFO - [Trainer 55] 模型已移至设备: cpu
2025-07-30 22:53:27,304 - INFO - [Trainer 55] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:27,304 - INFO - [Trainer 55] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:27,304 - INFO - [Trainer 55] 开始训练 5 个epoch
2025-07-30 22:53:27,304 - INFO - [Trainer 55] 开始第 1/5 个epoch
2025-07-30 22:53:27,340 - INFO - [Trainer 55] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4397, y=[7, 7, 7, 7, 7]
2025-07-30 22:53:27,340 - INFO - [Trainer 55] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:27,370 - INFO - [Trainer 55] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:27,371 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1564
2025-07-30 22:53:27,371 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:27,371 - INFO - [Trainer 10] 标签样本: [7, 8, 8, 6, 6]
2025-07-30 22:53:27,417 - INFO - [Trainer 10] Batch 0, Loss: 2.1539
2025-07-30 22:53:27,599 - INFO - 客户端 81 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:27,599 - INFO - 向客户端 81 发送全局模型 - 参数数量: 74
2025-07-30 22:53:27,599 - INFO - [Client 81] 收到直接传入的模型权重
2025-07-30 22:53:27,599 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:27,600 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:27,600 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:27,606 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:27,606 - INFO - [Client 81] 成功接收并加载模型
2025-07-30 22:53:27,606 - INFO - 客户端 81 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:27,606 - INFO - [Trainer.reset_staleness] 客户端 81 陈旧度重置
2025-07-30 22:53:27,607 - INFO - 重置客户端 81 的陈旧度
2025-07-30 22:53:27,607 - INFO - 成功发送全局模型到客户端 81
2025-07-30 22:53:27,607 - INFO - 客户端 81 开始本地训练
2025-07-30 22:53:27,607 - INFO - [客户端类型: Client, ID: 81] 准备开始训练
2025-07-30 22:53:27,607 - INFO - [客户端 81] 使用的训练器 client_id: 81
2025-07-30 22:53:27,609 - INFO - [Client 81] 开始验证训练集
2025-07-30 22:53:27,609 - INFO - [Client 81] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:27,610 - INFO - [Client 81] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:27,610 - INFO - [Trainer 81] 开始训练
2025-07-30 22:53:27,610 - INFO - [Trainer 81] 训练集大小: 300
2025-07-30 22:53:27,611 - INFO - [Trainer 81] 模型已移至设备: cpu
2025-07-30 22:53:27,611 - INFO - [Trainer 81] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:27,611 - INFO - [Trainer 81] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:27,611 - INFO - [Trainer 81] 开始训练 5 个epoch
2025-07-30 22:53:27,612 - INFO - [Trainer 81] 开始第 1/5 个epoch
2025-07-30 22:53:27,620 - INFO - [Trainer 81] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4130, y=[9, 9, 9, 1, 9]
2025-07-30 22:53:27,621 - INFO - [Trainer 81] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:27,634 - INFO - [Trainer 81] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:27,634 - INFO - 客户端 99 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:27,634 - INFO - 向客户端 99 发送全局模型 - 参数数量: 74
2025-07-30 22:53:27,634 - INFO - [Client 99] 收到直接传入的模型权重
2025-07-30 22:53:27,635 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:27,635 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:27,635 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:27,639 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:27,640 - INFO - [Client 99] 成功接收并加载模型
2025-07-30 22:53:27,640 - INFO - 客户端 99 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:27,640 - INFO - [Trainer.reset_staleness] 客户端 99 陈旧度重置
2025-07-30 22:53:27,640 - INFO - 重置客户端 99 的陈旧度
2025-07-30 22:53:27,640 - INFO - 成功发送全局模型到客户端 99
2025-07-30 22:53:27,640 - INFO - 客户端 99 开始本地训练
2025-07-30 22:53:27,641 - INFO - [客户端类型: Client, ID: 99] 准备开始训练
2025-07-30 22:53:27,641 - INFO - [客户端 99] 使用的训练器 client_id: 99
2025-07-30 22:53:27,641 - INFO - [Client 99] 开始验证训练集
2025-07-30 22:53:27,642 - INFO - [Client 99] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:27,643 - INFO - [Client 99] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:27,643 - INFO - [Trainer 99] 开始训练
2025-07-30 22:53:27,643 - INFO - [Trainer 99] 训练集大小: 300
2025-07-30 22:53:27,644 - INFO - [Trainer 99] 模型已移至设备: cpu
2025-07-30 22:53:27,644 - INFO - [Trainer 99] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:27,644 - INFO - [Trainer 99] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:27,645 - INFO - [Trainer 99] 开始训练 5 个epoch
2025-07-30 22:53:27,645 - INFO - [Trainer 99] 开始第 1/5 个epoch
2025-07-30 22:53:27,655 - INFO - [Trainer 99] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4557, y=[1, 6, 6, 3, 6]
2025-07-30 22:53:27,655 - INFO - [Trainer 99] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:27,663 - INFO - [Trainer 99] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:27,664 - INFO - [Trainer 58] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3469
2025-07-30 22:53:27,665 - INFO - [Trainer 58] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:27,665 - INFO - [Trainer 58] 标签样本: [7, 5, 5, 7, 1]
2025-07-30 22:53:27,715 - INFO - [Trainer 58] Batch 0, Loss: 2.4373
2025-07-30 22:53:27,883 - INFO - 客户端 18 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:27,884 - INFO - 向客户端 18 发送全局模型 - 参数数量: 74
2025-07-30 22:53:27,884 - INFO - [Client 18] 收到直接传入的模型权重
2025-07-30 22:53:27,884 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:27,884 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:27,884 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:27,924 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:27,925 - INFO - [Client 18] 成功接收并加载模型
2025-07-30 22:53:27,925 - INFO - 客户端 18 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:27,925 - INFO - [Trainer.reset_staleness] 客户端 18 陈旧度重置
2025-07-30 22:53:27,925 - INFO - 重置客户端 18 的陈旧度
2025-07-30 22:53:27,925 - INFO - 成功发送全局模型到客户端 18
2025-07-30 22:53:27,925 - INFO - 客户端 18 开始本地训练
2025-07-30 22:53:27,925 - INFO - [客户端类型: Client, ID: 18] 准备开始训练
2025-07-30 22:53:27,925 - INFO - [客户端 18] 使用的训练器 client_id: 18
2025-07-30 22:53:27,925 - INFO - [Client 18] 开始验证训练集
2025-07-30 22:53:27,928 - INFO - [Client 18] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:27,928 - INFO - [Client 18] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:27,928 - INFO - [Trainer 18] 开始训练
2025-07-30 22:53:27,928 - INFO - [Trainer 18] 训练集大小: 300
2025-07-30 22:53:27,929 - INFO - [Trainer 18] 模型已移至设备: cpu
2025-07-30 22:53:27,929 - INFO - [Trainer 18] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:27,929 - INFO - [Trainer 18] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:27,929 - INFO - [Trainer 18] 开始训练 5 个epoch
2025-07-30 22:53:27,929 - INFO - [Trainer 18] 开始第 1/5 个epoch
2025-07-30 22:53:27,976 - INFO - [Trainer 18] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4160, y=[6, 6, 9, 8, 1]
2025-07-30 22:53:27,976 - INFO - [Trainer 18] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:28,018 - INFO - [Trainer 18] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:28,378 - INFO - 客户端 77 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:28,378 - INFO - 向客户端 77 发送全局模型 - 参数数量: 74
2025-07-30 22:53:28,378 - INFO - [Client 77] 收到直接传入的模型权重
2025-07-30 22:53:28,379 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:28,379 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:28,379 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:28,386 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:28,386 - INFO - [Client 77] 成功接收并加载模型
2025-07-30 22:53:28,386 - INFO - 客户端 77 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:28,386 - INFO - [Trainer.reset_staleness] 客户端 77 陈旧度重置
2025-07-30 22:53:28,386 - INFO - 重置客户端 77 的陈旧度
2025-07-30 22:53:28,386 - INFO - 成功发送全局模型到客户端 77
2025-07-30 22:53:28,386 - INFO - 客户端 77 开始本地训练
2025-07-30 22:53:28,386 - INFO - [客户端类型: Client, ID: 77] 准备开始训练
2025-07-30 22:53:28,386 - INFO - [客户端 77] 使用的训练器 client_id: 77
2025-07-30 22:53:28,387 - INFO - [Client 77] 开始验证训练集
2025-07-30 22:53:28,387 - INFO - [Client 77] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:28,387 - INFO - [Client 77] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:28,388 - INFO - [Trainer 77] 开始训练
2025-07-30 22:53:28,388 - INFO - [Trainer 77] 训练集大小: 300
2025-07-30 22:53:28,388 - INFO - [Trainer 77] 模型已移至设备: cpu
2025-07-30 22:53:28,388 - INFO - [Trainer 77] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:28,389 - INFO - [Trainer 77] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:28,389 - INFO - [Trainer 77] 开始训练 5 个epoch
2025-07-30 22:53:28,389 - INFO - [Trainer 77] 开始第 1/5 个epoch
2025-07-30 22:53:28,397 - INFO - [Trainer 77] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1043, y=[0, 0, 0, 0, 1]
2025-07-30 22:53:28,397 - INFO - [Trainer 77] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:28,405 - INFO - [Trainer 77] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:28,405 - INFO - 客户端 47 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:28,405 - INFO - 向客户端 47 发送全局模型 - 参数数量: 74
2025-07-30 22:53:28,405 - INFO - [Client 47] 收到直接传入的模型权重
2025-07-30 22:53:28,405 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:28,406 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:28,406 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:28,430 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:28,430 - INFO - [Client 47] 成功接收并加载模型
2025-07-30 22:53:28,432 - INFO - 客户端 47 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:28,432 - INFO - [Trainer.reset_staleness] 客户端 47 陈旧度重置
2025-07-30 22:53:28,432 - INFO - 重置客户端 47 的陈旧度
2025-07-30 22:53:28,432 - INFO - 成功发送全局模型到客户端 47
2025-07-30 22:53:28,432 - INFO - 客户端 47 开始本地训练
2025-07-30 22:53:28,432 - INFO - [客户端类型: Client, ID: 47] 准备开始训练
2025-07-30 22:53:28,432 - INFO - [客户端 47] 使用的训练器 client_id: 47
2025-07-30 22:53:28,432 - INFO - [Client 47] 开始验证训练集
2025-07-30 22:53:28,433 - INFO - [Client 47] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:28,433 - INFO - [Client 47] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:28,433 - INFO - [Trainer 47] 开始训练
2025-07-30 22:53:28,434 - INFO - [Trainer 47] 训练集大小: 300
2025-07-30 22:53:28,434 - INFO - [Trainer 47] 模型已移至设备: cpu
2025-07-30 22:53:28,434 - INFO - [Trainer 47] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:28,434 - INFO - [Trainer 47] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:28,434 - INFO - [Trainer 47] 开始训练 5 个epoch
2025-07-30 22:53:28,434 - INFO - [Trainer 47] 开始第 1/5 个epoch
2025-07-30 22:53:28,451 - INFO - [Trainer 47] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3087, y=[7, 1, 1, 4, 2]
2025-07-30 22:53:28,451 - INFO - [Trainer 47] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:28,466 - INFO - [Trainer 47] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:28,466 - INFO - 客户端 78 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:28,466 - INFO - 向客户端 78 发送全局模型 - 参数数量: 74
2025-07-30 22:53:28,466 - INFO - [Client 78] 收到直接传入的模型权重
2025-07-30 22:53:28,467 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:28,467 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:28,467 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:28,470 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:28,470 - INFO - [Client 78] 成功接收并加载模型
2025-07-30 22:53:28,470 - INFO - 客户端 78 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:28,470 - INFO - [Trainer.reset_staleness] 客户端 78 陈旧度重置
2025-07-30 22:53:28,470 - INFO - 重置客户端 78 的陈旧度
2025-07-30 22:53:28,470 - INFO - 成功发送全局模型到客户端 78
2025-07-30 22:53:28,470 - INFO - 客户端 78 开始本地训练
2025-07-30 22:53:28,470 - INFO - [客户端类型: Client, ID: 78] 准备开始训练
2025-07-30 22:53:28,471 - INFO - [客户端 78] 使用的训练器 client_id: 78
2025-07-30 22:53:28,471 - INFO - [Client 78] 开始验证训练集
2025-07-30 22:53:28,471 - INFO - [Client 78] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:28,471 - INFO - [Client 78] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:28,471 - INFO - [Trainer 78] 开始训练
2025-07-30 22:53:28,471 - INFO - [Trainer 78] 训练集大小: 300
2025-07-30 22:53:28,472 - INFO - [Trainer 78] 模型已移至设备: cpu
2025-07-30 22:53:28,472 - INFO - [Trainer 78] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:28,472 - INFO - [Trainer 78] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:28,472 - INFO - [Trainer 78] 开始训练 5 个epoch
2025-07-30 22:53:28,472 - INFO - [Trainer 78] 开始第 1/5 个epoch
2025-07-30 22:53:28,478 - INFO - [Trainer 78] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2951, y=[2, 3, 9, 3, 2]
2025-07-30 22:53:28,478 - INFO - [Trainer 78] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:28,484 - INFO - [Trainer 78] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:28,672 - INFO - 客户端 38 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:28,672 - INFO - 向客户端 38 发送全局模型 - 参数数量: 74
2025-07-30 22:53:28,673 - INFO - [Client 38] 收到直接传入的模型权重
2025-07-30 22:53:28,673 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:28,673 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:28,673 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:28,702 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:28,702 - INFO - [Client 38] 成功接收并加载模型
2025-07-30 22:53:28,702 - INFO - 客户端 38 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:28,702 - INFO - [Trainer.reset_staleness] 客户端 38 陈旧度重置
2025-07-30 22:53:28,702 - INFO - 重置客户端 38 的陈旧度
2025-07-30 22:53:28,702 - INFO - 成功发送全局模型到客户端 38
2025-07-30 22:53:28,702 - INFO - 客户端 38 开始本地训练
2025-07-30 22:53:28,703 - INFO - [客户端类型: Client, ID: 38] 准备开始训练
2025-07-30 22:53:28,703 - INFO - [客户端 38] 使用的训练器 client_id: 38
2025-07-30 22:53:28,703 - INFO - [Client 38] 开始验证训练集
2025-07-30 22:53:28,703 - INFO - [Client 38] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:28,704 - INFO - [Client 38] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:28,704 - INFO - [Trainer 38] 开始训练
2025-07-30 22:53:28,704 - INFO - [Trainer 38] 训练集大小: 300
2025-07-30 22:53:28,704 - INFO - [Trainer 38] 模型已移至设备: cpu
2025-07-30 22:53:28,705 - INFO - [Trainer 38] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:28,705 - INFO - [Trainer 38] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:28,705 - INFO - [Trainer 38] 开始训练 5 个epoch
2025-07-30 22:53:28,705 - INFO - [Trainer 38] 开始第 1/5 个epoch
2025-07-30 22:53:28,724 - INFO - [Trainer 38] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3638, y=[1, 1, 1, 1, 1]
2025-07-30 22:53:28,724 - INFO - [Trainer 38] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:28,740 - INFO - [Trainer 38] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:28,740 - INFO - 客户端 22 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:28,741 - INFO - 向客户端 22 发送全局模型 - 参数数量: 74
2025-07-30 22:53:28,741 - INFO - [Client 22] 收到直接传入的模型权重
2025-07-30 22:53:28,741 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:28,741 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:28,741 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:28,764 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:28,765 - INFO - [Client 22] 成功接收并加载模型
2025-07-30 22:53:28,765 - INFO - 客户端 22 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:28,765 - INFO - [Trainer.reset_staleness] 客户端 22 陈旧度重置
2025-07-30 22:53:28,765 - INFO - 重置客户端 22 的陈旧度
2025-07-30 22:53:28,765 - INFO - 成功发送全局模型到客户端 22
2025-07-30 22:53:28,765 - INFO - 客户端 22 开始本地训练
2025-07-30 22:53:28,765 - INFO - [客户端类型: Client, ID: 22] 准备开始训练
2025-07-30 22:53:28,765 - INFO - [客户端 22] 使用的训练器 client_id: 22
2025-07-30 22:53:28,765 - INFO - [Client 22] 开始验证训练集
2025-07-30 22:53:28,767 - INFO - [Client 22] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:28,767 - INFO - [Client 22] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:28,767 - INFO - [Trainer 22] 开始训练
2025-07-30 22:53:28,767 - INFO - [Trainer 22] 训练集大小: 300
2025-07-30 22:53:28,768 - INFO - [Trainer 22] 模型已移至设备: cpu
2025-07-30 22:53:28,768 - INFO - [Trainer 22] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:28,768 - INFO - [Trainer 22] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:28,768 - INFO - [Trainer 22] 开始训练 5 个epoch
2025-07-30 22:53:28,768 - INFO - [Trainer 22] 开始第 1/5 个epoch
2025-07-30 22:53:28,783 - INFO - [Trainer 22] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3748, y=[6, 6, 6, 8, 8]
2025-07-30 22:53:28,783 - INFO - [Trainer 22] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:28,795 - INFO - [Trainer 22] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:28,795 - INFO - 客户端 79 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:28,795 - INFO - 向客户端 79 发送全局模型 - 参数数量: 74
2025-07-30 22:53:28,795 - INFO - [Client 79] 收到直接传入的模型权重
2025-07-30 22:53:28,795 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:28,795 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:28,795 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:28,799 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:28,799 - INFO - [Client 79] 成功接收并加载模型
2025-07-30 22:53:28,799 - INFO - 客户端 79 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:28,799 - INFO - [Trainer.reset_staleness] 客户端 79 陈旧度重置
2025-07-30 22:53:28,800 - INFO - 重置客户端 79 的陈旧度
2025-07-30 22:53:28,800 - INFO - 成功发送全局模型到客户端 79
2025-07-30 22:53:28,800 - INFO - 客户端 79 开始本地训练
2025-07-30 22:53:28,800 - INFO - [客户端类型: Client, ID: 79] 准备开始训练
2025-07-30 22:53:28,800 - INFO - [客户端 79] 使用的训练器 client_id: 79
2025-07-30 22:53:28,800 - INFO - [Client 79] 开始验证训练集
2025-07-30 22:53:28,800 - INFO - [Client 79] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:28,800 - INFO - [Client 79] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:28,800 - INFO - [Trainer 79] 开始训练
2025-07-30 22:53:28,800 - INFO - [Trainer 79] 训练集大小: 300
2025-07-30 22:53:28,802 - INFO - [Trainer 79] 模型已移至设备: cpu
2025-07-30 22:53:28,802 - INFO - [Trainer 79] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:28,802 - INFO - [Trainer 79] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:28,802 - INFO - [Trainer 79] 开始训练 5 个epoch
2025-07-30 22:53:28,802 - INFO - [Trainer 79] 开始第 1/5 个epoch
2025-07-30 22:53:28,808 - INFO - [Trainer 79] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4550, y=[6, 6, 2, 2, 2]
2025-07-30 22:53:28,808 - INFO - [Trainer 79] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:28,814 - INFO - [Trainer 79] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:28,814 - INFO - [Trainer 43] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1518
2025-07-30 22:53:28,814 - INFO - [Trainer 43] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:28,814 - INFO - [Trainer 43] 标签样本: [7, 7, 7, 7, 7]
2025-07-30 22:53:28,869 - INFO - [Trainer 43] Batch 0, Loss: 2.7991
2025-07-30 22:53:29,046 - INFO - 客户端 80 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:29,046 - INFO - 向客户端 80 发送全局模型 - 参数数量: 74
2025-07-30 22:53:29,046 - INFO - [Client 80] 收到直接传入的模型权重
2025-07-30 22:53:29,046 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:29,046 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:29,046 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:29,053 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:29,053 - INFO - [Client 80] 成功接收并加载模型
2025-07-30 22:53:29,053 - INFO - 客户端 80 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:29,053 - INFO - [Trainer.reset_staleness] 客户端 80 陈旧度重置
2025-07-30 22:53:29,053 - INFO - 重置客户端 80 的陈旧度
2025-07-30 22:53:29,053 - INFO - 成功发送全局模型到客户端 80
2025-07-30 22:53:29,053 - INFO - 客户端 80 开始本地训练
2025-07-30 22:53:29,053 - INFO - [客户端类型: Client, ID: 80] 准备开始训练
2025-07-30 22:53:29,053 - INFO - [客户端 80] 使用的训练器 client_id: 80
2025-07-30 22:53:29,053 - INFO - [Client 80] 开始验证训练集
2025-07-30 22:53:29,054 - INFO - [Client 80] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:29,054 - INFO - [Client 80] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:29,055 - INFO - [Trainer 80] 开始训练
2025-07-30 22:53:29,055 - INFO - [Trainer 80] 训练集大小: 300
2025-07-30 22:53:29,055 - INFO - [Trainer 80] 模型已移至设备: cpu
2025-07-30 22:53:29,055 - INFO - [Trainer 80] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:29,056 - INFO - [Trainer 80] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:29,056 - INFO - [Trainer 80] 开始训练 5 个epoch
2025-07-30 22:53:29,056 - INFO - [Trainer 80] 开始第 1/5 个epoch
2025-07-30 22:53:29,063 - INFO - [Trainer 80] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3388, y=[6, 6, 2, 2, 6]
2025-07-30 22:53:29,063 - INFO - [Trainer 80] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:29,069 - INFO - [Trainer 80] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:29,069 - INFO - 客户端 61 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:29,070 - INFO - 向客户端 61 发送全局模型 - 参数数量: 74
2025-07-30 22:53:29,070 - INFO - [Client 61] 收到直接传入的模型权重
2025-07-30 22:53:29,070 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:29,070 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:29,070 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:29,097 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:29,097 - INFO - [Client 61] 成功接收并加载模型
2025-07-30 22:53:29,097 - INFO - 客户端 61 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:29,097 - INFO - [Trainer.reset_staleness] 客户端 61 陈旧度重置
2025-07-30 22:53:29,097 - INFO - 重置客户端 61 的陈旧度
2025-07-30 22:53:29,097 - INFO - 成功发送全局模型到客户端 61
2025-07-30 22:53:29,098 - INFO - 客户端 61 开始本地训练
2025-07-30 22:53:29,098 - INFO - [客户端类型: Client, ID: 61] 准备开始训练
2025-07-30 22:53:29,098 - INFO - [客户端 61] 使用的训练器 client_id: 61
2025-07-30 22:53:29,098 - INFO - [Client 61] 开始验证训练集
2025-07-30 22:53:29,098 - INFO - [Client 61] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:29,098 - INFO - [Client 61] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:29,099 - INFO - [Trainer 61] 开始训练
2025-07-30 22:53:29,099 - INFO - [Trainer 61] 训练集大小: 300
2025-07-30 22:53:29,099 - INFO - [Trainer 61] 模型已移至设备: cpu
2025-07-30 22:53:29,099 - INFO - [Trainer 61] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:29,099 - INFO - [Trainer 61] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:29,099 - INFO - [Trainer 61] 开始训练 5 个epoch
2025-07-30 22:53:29,099 - INFO - [Trainer 61] 开始第 1/5 个epoch
2025-07-30 22:53:29,116 - INFO - [Trainer 61] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3538, y=[2, 6, 1, 6, 3]
2025-07-30 22:53:29,116 - INFO - [Trainer 61] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:29,131 - INFO - [Trainer 61] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:29,131 - INFO - 客户端 37 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:29,131 - INFO - 向客户端 37 发送全局模型 - 参数数量: 74
2025-07-30 22:53:29,131 - INFO - [Client 37] 收到直接传入的模型权重
2025-07-30 22:53:29,132 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:29,132 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:29,132 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:29,158 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:29,159 - INFO - [Client 37] 成功接收并加载模型
2025-07-30 22:53:29,159 - INFO - 客户端 37 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:29,159 - INFO - [Trainer.reset_staleness] 客户端 37 陈旧度重置
2025-07-30 22:53:29,159 - INFO - 重置客户端 37 的陈旧度
2025-07-30 22:53:29,159 - INFO - 成功发送全局模型到客户端 37
2025-07-30 22:53:29,159 - INFO - 客户端 37 开始本地训练
2025-07-30 22:53:29,159 - INFO - [客户端类型: Client, ID: 37] 准备开始训练
2025-07-30 22:53:29,159 - INFO - [客户端 37] 使用的训练器 client_id: 37
2025-07-30 22:53:29,159 - INFO - [Client 37] 开始验证训练集
2025-07-30 22:53:29,160 - INFO - [Client 37] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:29,160 - INFO - [Client 37] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:29,160 - INFO - [Trainer 37] 开始训练
2025-07-30 22:53:29,160 - INFO - [Trainer 37] 训练集大小: 300
2025-07-30 22:53:29,161 - INFO - [Trainer 37] 模型已移至设备: cpu
2025-07-30 22:53:29,161 - INFO - [Trainer 37] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:29,161 - INFO - [Trainer 37] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:29,161 - INFO - [Trainer 37] 开始训练 5 个epoch
2025-07-30 22:53:29,161 - INFO - [Trainer 37] 开始第 1/5 个epoch
2025-07-30 22:53:29,176 - INFO - [Trainer 37] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0230, y=[0, 0, 5, 0, 0]
2025-07-30 22:53:29,177 - INFO - [Trainer 37] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:29,190 - INFO - [Trainer 37] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:29,190 - INFO - 客户端 45 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:29,190 - INFO - 向客户端 45 发送全局模型 - 参数数量: 74
2025-07-30 22:53:29,190 - INFO - [Client 45] 收到直接传入的模型权重
2025-07-30 22:53:29,191 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:29,191 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:29,191 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:29,217 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:29,217 - INFO - [Client 45] 成功接收并加载模型
2025-07-30 22:53:29,217 - INFO - 客户端 45 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:29,217 - INFO - [Trainer.reset_staleness] 客户端 45 陈旧度重置
2025-07-30 22:53:29,217 - INFO - 重置客户端 45 的陈旧度
2025-07-30 22:53:29,217 - INFO - 成功发送全局模型到客户端 45
2025-07-30 22:53:29,217 - INFO - 客户端 45 开始本地训练
2025-07-30 22:53:29,217 - INFO - [客户端类型: Client, ID: 45] 准备开始训练
2025-07-30 22:53:29,217 - INFO - [客户端 45] 使用的训练器 client_id: 45
2025-07-30 22:53:29,217 - INFO - [Client 45] 开始验证训练集
2025-07-30 22:53:29,219 - INFO - [Client 45] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:29,219 - INFO - [Client 45] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:29,219 - INFO - [Trainer 45] 开始训练
2025-07-30 22:53:29,219 - INFO - [Trainer 45] 训练集大小: 300
2025-07-30 22:53:29,219 - INFO - [Trainer 45] 模型已移至设备: cpu
2025-07-30 22:53:29,219 - INFO - [Trainer 45] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:29,220 - INFO - [Trainer 45] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:29,220 - INFO - [Trainer 45] 开始训练 5 个epoch
2025-07-30 22:53:29,220 - INFO - [Trainer 45] 开始第 1/5 个epoch
2025-07-30 22:53:29,238 - INFO - [Trainer 45] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5742, y=[6, 6, 6, 6, 4]
2025-07-30 22:53:29,238 - INFO - [Trainer 45] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:29,256 - INFO - [Trainer 45] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:29,256 - INFO - 客户端 39 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:29,256 - INFO - 向客户端 39 发送全局模型 - 参数数量: 74
2025-07-30 22:53:29,256 - INFO - [Client 39] 收到直接传入的模型权重
2025-07-30 22:53:29,256 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:29,256 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:29,257 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:29,289 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:29,289 - INFO - [Client 39] 成功接收并加载模型
2025-07-30 22:53:29,289 - INFO - 客户端 39 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:29,289 - INFO - [Trainer.reset_staleness] 客户端 39 陈旧度重置
2025-07-30 22:53:29,289 - INFO - 重置客户端 39 的陈旧度
2025-07-30 22:53:29,289 - INFO - 成功发送全局模型到客户端 39
2025-07-30 22:53:29,289 - INFO - 客户端 39 开始本地训练
2025-07-30 22:53:29,289 - INFO - [客户端类型: Client, ID: 39] 准备开始训练
2025-07-30 22:53:29,289 - INFO - [客户端 39] 使用的训练器 client_id: 39
2025-07-30 22:53:29,289 - INFO - [Client 39] 开始验证训练集
2025-07-30 22:53:29,290 - INFO - [Client 39] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:29,290 - INFO - [Client 39] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:29,290 - INFO - [Trainer 39] 开始训练
2025-07-30 22:53:29,290 - INFO - [Trainer 39] 训练集大小: 300
2025-07-30 22:53:29,291 - INFO - [Trainer 39] 模型已移至设备: cpu
2025-07-30 22:53:29,291 - INFO - [Trainer 39] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:29,291 - INFO - [Trainer 39] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:29,291 - INFO - [Trainer 39] 开始训练 5 个epoch
2025-07-30 22:53:29,291 - INFO - [Trainer 39] 开始第 1/5 个epoch
2025-07-30 22:53:29,303 - INFO - [Trainer 39] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4327, y=[5, 5, 5, 5, 5]
2025-07-30 22:53:29,303 - INFO - [Trainer 39] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:29,315 - INFO - [Trainer 39] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:29,548 - INFO - 客户端 13 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:29,548 - INFO - 向客户端 13 发送全局模型 - 参数数量: 74
2025-07-30 22:53:29,549 - INFO - [Client 13] 收到直接传入的模型权重
2025-07-30 22:53:29,549 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:29,549 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:29,549 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:29,599 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:29,599 - INFO - [Client 13] 成功接收并加载模型
2025-07-30 22:53:29,599 - INFO - 客户端 13 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:29,599 - INFO - [Trainer.reset_staleness] 客户端 13 陈旧度重置
2025-07-30 22:53:29,599 - INFO - 重置客户端 13 的陈旧度
2025-07-30 22:53:29,599 - INFO - 成功发送全局模型到客户端 13
2025-07-30 22:53:29,600 - INFO - 客户端 13 开始本地训练
2025-07-30 22:53:29,600 - INFO - [客户端类型: Client, ID: 13] 准备开始训练
2025-07-30 22:53:29,600 - INFO - [客户端 13] 使用的训练器 client_id: 13
2025-07-30 22:53:29,600 - INFO - [Client 13] 开始验证训练集
2025-07-30 22:53:29,601 - INFO - [Client 13] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:29,601 - INFO - [Client 13] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:29,601 - INFO - [Trainer 13] 开始训练
2025-07-30 22:53:29,601 - INFO - [Trainer 13] 训练集大小: 300
2025-07-30 22:53:29,601 - INFO - [Trainer 13] 模型已移至设备: cpu
2025-07-30 22:53:29,602 - INFO - [Trainer 13] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:29,602 - INFO - [Trainer 13] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:29,602 - INFO - [Trainer 13] 开始训练 5 个epoch
2025-07-30 22:53:29,602 - INFO - [Trainer 13] 开始第 1/5 个epoch
2025-07-30 22:53:29,625 - INFO - [Trainer 13] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4129, y=[2, 2, 2, 2, 2]
2025-07-30 22:53:29,625 - INFO - [Trainer 13] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:29,641 - INFO - [Trainer 13] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:29,642 - INFO - 客户端 14 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:29,642 - INFO - 向客户端 14 发送全局模型 - 参数数量: 74
2025-07-30 22:53:29,642 - INFO - [Client 14] 收到直接传入的模型权重
2025-07-30 22:53:29,642 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:29,642 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:29,642 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:29,677 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:29,677 - INFO - [Client 14] 成功接收并加载模型
2025-07-30 22:53:29,677 - INFO - 客户端 14 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:29,677 - INFO - [Trainer.reset_staleness] 客户端 14 陈旧度重置
2025-07-30 22:53:29,677 - INFO - 重置客户端 14 的陈旧度
2025-07-30 22:53:29,677 - INFO - 成功发送全局模型到客户端 14
2025-07-30 22:53:29,677 - INFO - 客户端 14 开始本地训练
2025-07-30 22:53:29,677 - INFO - [客户端类型: Client, ID: 14] 准备开始训练
2025-07-30 22:53:29,677 - INFO - [客户端 14] 使用的训练器 client_id: 14
2025-07-30 22:53:29,677 - INFO - [Client 14] 开始验证训练集
2025-07-30 22:53:29,679 - INFO - [Client 14] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:29,680 - INFO - [Client 14] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:29,680 - INFO - [Trainer 14] 开始训练
2025-07-30 22:53:29,680 - INFO - [Trainer 14] 训练集大小: 300
2025-07-30 22:53:29,680 - INFO - [Trainer 14] 模型已移至设备: cpu
2025-07-30 22:53:29,680 - INFO - [Trainer 14] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:29,680 - INFO - [Trainer 14] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:29,680 - INFO - [Trainer 14] 开始训练 5 个epoch
2025-07-30 22:53:29,680 - INFO - [Trainer 14] 开始第 1/5 个epoch
2025-07-30 22:53:29,696 - INFO - [Trainer 14] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3075, y=[7, 7, 4, 7, 7]
2025-07-30 22:53:29,696 - INFO - [Trainer 14] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:29,711 - INFO - [Trainer 14] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:29,711 - INFO - 客户端 83 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:29,711 - INFO - 向客户端 83 发送全局模型 - 参数数量: 74
2025-07-30 22:53:29,711 - INFO - [Client 83] 收到直接传入的模型权重
2025-07-30 22:53:29,712 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:29,712 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:29,712 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:29,725 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:29,725 - INFO - [Client 83] 成功接收并加载模型
2025-07-30 22:53:29,726 - INFO - 客户端 83 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:29,726 - INFO - [Trainer.reset_staleness] 客户端 83 陈旧度重置
2025-07-30 22:53:29,726 - INFO - 重置客户端 83 的陈旧度
2025-07-30 22:53:29,726 - INFO - 成功发送全局模型到客户端 83
2025-07-30 22:53:29,726 - INFO - 客户端 83 开始本地训练
2025-07-30 22:53:29,726 - INFO - [客户端类型: Client, ID: 83] 准备开始训练
2025-07-30 22:53:29,727 - INFO - [客户端 83] 使用的训练器 client_id: 83
2025-07-30 22:53:29,727 - INFO - [Client 83] 开始验证训练集
2025-07-30 22:53:29,727 - INFO - [Client 83] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:29,728 - INFO - [Client 83] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:29,728 - INFO - [Trainer 83] 开始训练
2025-07-30 22:53:29,728 - INFO - [Trainer 83] 训练集大小: 300
2025-07-30 22:53:29,728 - INFO - [Trainer 83] 模型已移至设备: cpu
2025-07-30 22:53:29,728 - INFO - [Trainer 83] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:29,729 - INFO - [Trainer 83] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:29,729 - INFO - [Trainer 83] 开始训练 5 个epoch
2025-07-30 22:53:29,729 - INFO - [Trainer 83] 开始第 1/5 个epoch
2025-07-30 22:53:29,735 - INFO - [Trainer 83] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4332, y=[2, 1, 1, 7, 2]
2025-07-30 22:53:29,735 - INFO - [Trainer 83] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:29,739 - INFO - [Trainer 83] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:30,359 - INFO - [Trainer 49] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2916
2025-07-30 22:53:30,361 - INFO - [Trainer 49] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:30,361 - INFO - [Trainer 49] 标签样本: [8, 8, 7, 8, 8]
2025-07-30 22:53:30,405 - INFO - [Trainer 49] Batch 0, Loss: 2.1536
2025-07-30 22:53:30,556 - INFO - [Trainer 67] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4713
2025-07-30 22:53:30,556 - INFO - [Trainer 67] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:30,556 - INFO - [Trainer 67] 标签样本: [4, 5, 4, 4, 5]
2025-07-30 22:53:30,620 - INFO - [Trainer 67] Batch 0, Loss: 2.3542
2025-07-30 22:53:30,818 - INFO - 客户端 8 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:30,818 - INFO - 向客户端 8 发送全局模型 - 参数数量: 74
2025-07-30 22:53:30,818 - INFO - [Client 8] 收到直接传入的模型权重
2025-07-30 22:53:30,818 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:30,818 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:30,819 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:30,859 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:30,859 - INFO - [Client 8] 成功接收并加载模型
2025-07-30 22:53:30,859 - INFO - 客户端 8 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:30,859 - INFO - [Trainer.reset_staleness] 客户端 8 陈旧度重置
2025-07-30 22:53:30,859 - INFO - 重置客户端 8 的陈旧度
2025-07-30 22:53:30,859 - INFO - 成功发送全局模型到客户端 8
2025-07-30 22:53:30,859 - INFO - 客户端 8 开始本地训练
2025-07-30 22:53:30,859 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-07-30 22:53:30,860 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-07-30 22:53:30,860 - INFO - [Client 8] 开始验证训练集
2025-07-30 22:53:30,863 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:30,863 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:30,863 - INFO - [Trainer 8] 开始训练
2025-07-30 22:53:30,863 - INFO - [Trainer 8] 训练集大小: 300
2025-07-30 22:53:30,864 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-07-30 22:53:30,864 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:30,864 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:30,864 - INFO - [Trainer 8] 开始训练 5 个epoch
2025-07-30 22:53:30,866 - INFO - [Trainer 8] 开始第 1/5 个epoch
2025-07-30 22:53:30,908 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=0.0401, y=[0, 6, 1, 0, 0]
2025-07-30 22:53:30,908 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:30,938 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:30,938 - INFO - 客户端 26 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:30,938 - INFO - 向客户端 26 发送全局模型 - 参数数量: 74
2025-07-30 22:53:30,939 - INFO - [Client 26] 收到直接传入的模型权重
2025-07-30 22:53:30,939 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:30,939 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:30,939 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:30,967 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:30,968 - INFO - [Client 26] 成功接收并加载模型
2025-07-30 22:53:30,968 - INFO - 客户端 26 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:30,968 - INFO - [Trainer.reset_staleness] 客户端 26 陈旧度重置
2025-07-30 22:53:30,968 - INFO - 重置客户端 26 的陈旧度
2025-07-30 22:53:30,968 - INFO - 成功发送全局模型到客户端 26
2025-07-30 22:53:30,968 - INFO - 客户端 26 开始本地训练
2025-07-30 22:53:30,968 - INFO - [客户端类型: Client, ID: 26] 准备开始训练
2025-07-30 22:53:30,968 - INFO - [客户端 26] 使用的训练器 client_id: 26
2025-07-30 22:53:30,968 - INFO - [Client 26] 开始验证训练集
2025-07-30 22:53:30,970 - INFO - [Client 26] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:30,970 - INFO - [Client 26] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:30,970 - INFO - [Trainer 26] 开始训练
2025-07-30 22:53:30,971 - INFO - [Trainer 26] 训练集大小: 300
2025-07-30 22:53:30,971 - INFO - [Trainer 26] 模型已移至设备: cpu
2025-07-30 22:53:30,972 - INFO - [Trainer 26] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:30,972 - INFO - [Trainer 26] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:30,972 - INFO - [Trainer 26] 开始训练 5 个epoch
2025-07-30 22:53:30,972 - INFO - [Trainer 26] 开始第 1/5 个epoch
2025-07-30 22:53:31,001 - INFO - [Trainer 26] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4150, y=[7, 7, 7, 7, 3]
2025-07-30 22:53:31,002 - INFO - [Trainer 26] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:31,027 - INFO - [Trainer 26] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:31,257 - INFO - [Trainer 11] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5617
2025-07-30 22:53:31,257 - INFO - [Trainer 11] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:31,257 - INFO - [Trainer 11] 标签样本: [6, 4, 6, 4, 4]
2025-07-30 22:53:31,319 - INFO - [Trainer 11] Batch 0, Loss: 2.1690
2025-07-30 22:53:31,516 - INFO - [Trainer 69] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3124
2025-07-30 22:53:31,516 - INFO - [Trainer 69] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:31,516 - INFO - [Trainer 69] 标签样本: [7, 7, 5, 5, 5]
2025-07-30 22:53:31,573 - INFO - [Trainer 69] Batch 0, Loss: 2.6012
2025-07-30 22:53:31,725 - INFO - 客户端 96 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:31,725 - INFO - 向客户端 96 发送全局模型 - 参数数量: 74
2025-07-30 22:53:31,726 - INFO - [Client 96] 收到直接传入的模型权重
2025-07-30 22:53:31,726 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:31,726 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:31,726 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:31,732 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:31,732 - INFO - [Client 96] 成功接收并加载模型
2025-07-30 22:53:31,732 - INFO - 客户端 96 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:31,732 - INFO - [Trainer.reset_staleness] 客户端 96 陈旧度重置
2025-07-30 22:53:31,732 - INFO - 重置客户端 96 的陈旧度
2025-07-30 22:53:31,732 - INFO - 成功发送全局模型到客户端 96
2025-07-30 22:53:31,732 - INFO - 客户端 96 开始本地训练
2025-07-30 22:53:31,732 - INFO - [客户端类型: Client, ID: 96] 准备开始训练
2025-07-30 22:53:31,733 - INFO - [客户端 96] 使用的训练器 client_id: 96
2025-07-30 22:53:31,733 - INFO - [Client 96] 开始验证训练集
2025-07-30 22:53:31,733 - INFO - [Client 96] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:31,733 - INFO - [Client 96] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:31,734 - INFO - [Trainer 96] 开始训练
2025-07-30 22:53:31,734 - INFO - [Trainer 96] 训练集大小: 300
2025-07-30 22:53:31,734 - INFO - [Trainer 96] 模型已移至设备: cpu
2025-07-30 22:53:31,735 - INFO - [Trainer 96] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:31,735 - INFO - [Trainer 96] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:31,735 - INFO - [Trainer 96] 开始训练 5 个epoch
2025-07-30 22:53:31,736 - INFO - [Trainer 96] 开始第 1/5 个epoch
2025-07-30 22:53:31,743 - INFO - [Trainer 96] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3888, y=[9, 1, 1, 9, 1]
2025-07-30 22:53:31,743 - INFO - [Trainer 96] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:31,752 - INFO - [Trainer 96] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:31,752 - INFO - 客户端 31 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:31,752 - INFO - 向客户端 31 发送全局模型 - 参数数量: 74
2025-07-30 22:53:31,752 - INFO - [Client 31] 收到直接传入的模型权重
2025-07-30 22:53:31,752 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:31,752 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:31,753 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:31,797 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:31,797 - INFO - [Client 31] 成功接收并加载模型
2025-07-30 22:53:31,798 - INFO - 客户端 31 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:31,798 - INFO - [Trainer.reset_staleness] 客户端 31 陈旧度重置
2025-07-30 22:53:31,798 - INFO - 重置客户端 31 的陈旧度
2025-07-30 22:53:31,798 - INFO - 成功发送全局模型到客户端 31
2025-07-30 22:53:31,798 - INFO - 客户端 31 开始本地训练
2025-07-30 22:53:31,798 - INFO - [客户端类型: Client, ID: 31] 准备开始训练
2025-07-30 22:53:31,798 - INFO - [客户端 31] 使用的训练器 client_id: 31
2025-07-30 22:53:31,798 - INFO - [Client 31] 开始验证训练集
2025-07-30 22:53:31,800 - INFO - [Client 31] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:31,800 - INFO - [Client 31] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:31,800 - INFO - [Trainer 31] 开始训练
2025-07-30 22:53:31,801 - INFO - [Trainer 31] 训练集大小: 300
2025-07-30 22:53:31,801 - INFO - [Trainer 31] 模型已移至设备: cpu
2025-07-30 22:53:31,801 - INFO - [Trainer 31] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:31,801 - INFO - [Trainer 31] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:31,801 - INFO - [Trainer 31] 开始训练 5 个epoch
2025-07-30 22:53:31,802 - INFO - [Trainer 31] 开始第 1/5 个epoch
2025-07-30 22:53:31,852 - INFO - [Trainer 31] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1983, y=[5, 1, 1, 5, 1]
2025-07-30 22:53:31,853 - INFO - [Trainer 31] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:31,884 - INFO - [Trainer 31] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:31,886 - INFO - [Trainer 29] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0266
2025-07-30 22:53:31,886 - INFO - [Trainer 29] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:31,886 - INFO - [Trainer 29] 标签样本: [9, 3, 9, 0, 0]
2025-07-30 22:53:31,932 - INFO - [Trainer 29] Batch 0, Loss: 2.4672
2025-07-30 22:53:32,086 - INFO - [Trainer 86] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5015
2025-07-30 22:53:32,086 - INFO - [Trainer 86] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:32,086 - INFO - [Trainer 86] 标签样本: [4, 6, 6, 4, 4]
2025-07-30 22:53:32,129 - INFO - [Trainer 86] Batch 0, Loss: 2.0600
2025-07-30 22:53:32,247 - INFO - 客户端 30 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:32,247 - INFO - 向客户端 30 发送全局模型 - 参数数量: 74
2025-07-30 22:53:32,247 - INFO - [Client 30] 收到直接传入的模型权重
2025-07-30 22:53:32,247 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:32,248 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:32,248 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:32,287 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:32,288 - INFO - [Client 30] 成功接收并加载模型
2025-07-30 22:53:32,288 - INFO - 客户端 30 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:32,288 - INFO - [Trainer.reset_staleness] 客户端 30 陈旧度重置
2025-07-30 22:53:32,288 - INFO - 重置客户端 30 的陈旧度
2025-07-30 22:53:32,288 - INFO - 成功发送全局模型到客户端 30
2025-07-30 22:53:32,289 - INFO - 客户端 30 开始本地训练
2025-07-30 22:53:32,289 - INFO - [客户端类型: Client, ID: 30] 准备开始训练
2025-07-30 22:53:32,289 - INFO - [客户端 30] 使用的训练器 client_id: 30
2025-07-30 22:53:32,289 - INFO - [Client 30] 开始验证训练集
2025-07-30 22:53:32,291 - INFO - [Client 30] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:32,292 - INFO - [Client 30] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:32,292 - INFO - [Trainer 30] 开始训练
2025-07-30 22:53:32,292 - INFO - [Trainer 30] 训练集大小: 300
2025-07-30 22:53:32,292 - INFO - [Trainer 30] 模型已移至设备: cpu
2025-07-30 22:53:32,292 - INFO - [Trainer 30] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:32,292 - INFO - [Trainer 30] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:32,293 - INFO - [Trainer 30] 开始训练 5 个epoch
2025-07-30 22:53:32,293 - INFO - [Trainer 30] 开始第 1/5 个epoch
2025-07-30 22:53:32,351 - INFO - [Trainer 30] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3171, y=[2, 2, 2, 2, 2]
2025-07-30 22:53:32,351 - INFO - [Trainer 30] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:32,379 - INFO - [Trainer 30] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:32,379 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:53:32,380 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:53:32,566 - INFO - 客户端 25 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:32,566 - INFO - 向客户端 25 发送全局模型 - 参数数量: 74
2025-07-30 22:53:32,566 - INFO - [Client 25] 收到直接传入的模型权重
2025-07-30 22:53:32,566 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:32,566 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:32,567 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:32,601 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:32,601 - INFO - [Client 25] 成功接收并加载模型
2025-07-30 22:53:32,601 - INFO - 客户端 25 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:32,601 - INFO - [Trainer.reset_staleness] 客户端 25 陈旧度重置
2025-07-30 22:53:32,601 - INFO - 重置客户端 25 的陈旧度
2025-07-30 22:53:32,601 - INFO - 成功发送全局模型到客户端 25
2025-07-30 22:53:32,602 - INFO - 客户端 25 开始本地训练
2025-07-30 22:53:32,602 - INFO - [客户端类型: Client, ID: 25] 准备开始训练
2025-07-30 22:53:32,602 - INFO - [客户端 25] 使用的训练器 client_id: 25
2025-07-30 22:53:32,602 - INFO - [Client 25] 开始验证训练集
2025-07-30 22:53:32,606 - INFO - [Client 25] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:32,606 - INFO - [Client 25] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:32,606 - INFO - [Trainer 25] 开始训练
2025-07-30 22:53:32,606 - INFO - [Trainer 25] 训练集大小: 300
2025-07-30 22:53:32,608 - INFO - [Trainer 25] 模型已移至设备: cpu
2025-07-30 22:53:32,608 - INFO - [Trainer 25] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:32,608 - INFO - [Trainer 25] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:32,608 - INFO - [Trainer 25] 开始训练 5 个epoch
2025-07-30 22:53:32,609 - INFO - [Trainer 25] 开始第 1/5 个epoch
2025-07-30 22:53:32,651 - INFO - [Trainer 25] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0192, y=[0, 3, 0, 0, 0]
2025-07-30 22:53:32,651 - INFO - [Trainer 25] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:32,681 - INFO - [Trainer 25] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:32,681 - INFO - [Trainer 50] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4416
2025-07-30 22:53:32,682 - INFO - [Trainer 50] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:32,682 - INFO - [Trainer 50] 标签样本: [7, 7, 7, 7, 7]
2025-07-30 22:53:32,734 - INFO - [Trainer 50] Batch 0, Loss: 2.7149
2025-07-30 22:53:32,892 - INFO - 客户端 1 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:32,892 - INFO - 向客户端 1 发送全局模型 - 参数数量: 74
2025-07-30 22:53:32,892 - INFO - [Client 1] 收到直接传入的模型权重
2025-07-30 22:53:32,892 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:32,892 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:32,893 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:32,930 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:32,931 - INFO - [Client 1] 成功接收并加载模型
2025-07-30 22:53:32,931 - INFO - 客户端 1 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:32,931 - INFO - [Trainer.reset_staleness] 客户端 1 陈旧度重置
2025-07-30 22:53:32,931 - INFO - 重置客户端 1 的陈旧度
2025-07-30 22:53:32,931 - INFO - 成功发送全局模型到客户端 1
2025-07-30 22:53:32,931 - INFO - 客户端 1 开始本地训练
2025-07-30 22:53:32,931 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-07-30 22:53:32,932 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-07-30 22:53:32,932 - INFO - [Client 1] 开始验证训练集
2025-07-30 22:53:32,936 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:32,936 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:32,936 - INFO - [Trainer 1] 开始训练
2025-07-30 22:53:32,936 - INFO - [Trainer 1] 训练集大小: 300
2025-07-30 22:53:32,937 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-07-30 22:53:32,938 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:32,940 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:32,940 - INFO - [Trainer 1] 开始训练 5 个epoch
2025-07-30 22:53:32,940 - INFO - [Trainer 1] 开始第 1/5 个epoch
2025-07-30 22:53:32,977 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2702, y=[0, 8, 0, 5, 5]
2025-07-30 22:53:32,977 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:33,008 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:33,009 - INFO - 客户端 6 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:33,009 - INFO - 向客户端 6 发送全局模型 - 参数数量: 74
2025-07-30 22:53:33,009 - INFO - [Client 6] 收到直接传入的模型权重
2025-07-30 22:53:33,009 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:33,010 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:33,010 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:33,043 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:33,044 - INFO - [Client 6] 成功接收并加载模型
2025-07-30 22:53:33,044 - INFO - 客户端 6 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:33,044 - INFO - [Trainer.reset_staleness] 客户端 6 陈旧度重置
2025-07-30 22:53:33,044 - INFO - 重置客户端 6 的陈旧度
2025-07-30 22:53:33,044 - INFO - 成功发送全局模型到客户端 6
2025-07-30 22:53:33,044 - INFO - 客户端 6 开始本地训练
2025-07-30 22:53:33,044 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-07-30 22:53:33,044 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-07-30 22:53:33,044 - INFO - [Client 6] 开始验证训练集
2025-07-30 22:53:33,046 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:33,046 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:33,046 - INFO - [Trainer 6] 开始训练
2025-07-30 22:53:33,047 - INFO - [Trainer 6] 训练集大小: 300
2025-07-30 22:53:33,047 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-07-30 22:53:33,047 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:33,047 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:33,048 - INFO - [Trainer 6] 开始训练 5 个epoch
2025-07-30 22:53:33,048 - INFO - [Trainer 6] 开始第 1/5 个epoch
2025-07-30 22:53:33,089 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1536, y=[8, 0, 8, 8, 7]
2025-07-30 22:53:33,090 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:33,121 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:33,310 - INFO - 客户端 44 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:33,310 - INFO - 向客户端 44 发送全局模型 - 参数数量: 74
2025-07-30 22:53:33,310 - INFO - [Client 44] 收到直接传入的模型权重
2025-07-30 22:53:33,311 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:33,311 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:33,311 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:33,346 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:33,347 - INFO - [Client 44] 成功接收并加载模型
2025-07-30 22:53:33,347 - INFO - 客户端 44 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:33,347 - INFO - [Trainer.reset_staleness] 客户端 44 陈旧度重置
2025-07-30 22:53:33,347 - INFO - 重置客户端 44 的陈旧度
2025-07-30 22:53:33,347 - INFO - 成功发送全局模型到客户端 44
2025-07-30 22:53:33,347 - INFO - 客户端 44 开始本地训练
2025-07-30 22:53:33,347 - INFO - [客户端类型: Client, ID: 44] 准备开始训练
2025-07-30 22:53:33,347 - INFO - [客户端 44] 使用的训练器 client_id: 44
2025-07-30 22:53:33,347 - INFO - [Client 44] 开始验证训练集
2025-07-30 22:53:33,350 - INFO - [Client 44] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:33,350 - INFO - [Client 44] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:33,350 - INFO - [Trainer 44] 开始训练
2025-07-30 22:53:33,351 - INFO - [Trainer 44] 训练集大小: 300
2025-07-30 22:53:33,351 - INFO - [Trainer 44] 模型已移至设备: cpu
2025-07-30 22:53:33,351 - INFO - [Trainer 44] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:33,351 - INFO - [Trainer 44] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:33,352 - INFO - [Trainer 44] 开始训练 5 个epoch
2025-07-30 22:53:33,352 - INFO - [Trainer 44] 开始第 1/5 个epoch
2025-07-30 22:53:33,401 - INFO - [Trainer 44] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4234, y=[7, 7, 7, 7, 7]
2025-07-30 22:53:33,402 - INFO - [Trainer 44] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:33,430 - INFO - [Trainer 44] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:33,430 - INFO - [Trainer 76] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4756
2025-07-30 22:53:33,430 - INFO - [Trainer 76] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:33,430 - INFO - [Trainer 76] 标签样本: [9, 3, 3, 6, 6]
2025-07-30 22:53:33,480 - INFO - [Trainer 76] Batch 0, Loss: 2.5689
2025-07-30 22:53:33,632 - INFO - 客户端 15 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:33,633 - INFO - 向客户端 15 发送全局模型 - 参数数量: 74
2025-07-30 22:53:33,633 - INFO - [Client 15] 收到直接传入的模型权重
2025-07-30 22:53:33,633 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:33,633 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:33,634 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:33,675 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:33,675 - INFO - [Client 15] 成功接收并加载模型
2025-07-30 22:53:33,675 - INFO - 客户端 15 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:33,675 - INFO - [Trainer.reset_staleness] 客户端 15 陈旧度重置
2025-07-30 22:53:33,675 - INFO - 重置客户端 15 的陈旧度
2025-07-30 22:53:33,676 - INFO - 成功发送全局模型到客户端 15
2025-07-30 22:53:33,676 - INFO - 客户端 15 开始本地训练
2025-07-30 22:53:33,676 - INFO - [客户端类型: Client, ID: 15] 准备开始训练
2025-07-30 22:53:33,676 - INFO - [客户端 15] 使用的训练器 client_id: 15
2025-07-30 22:53:33,676 - INFO - [Client 15] 开始验证训练集
2025-07-30 22:53:33,679 - INFO - [Client 15] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:33,679 - INFO - [Client 15] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:33,679 - INFO - [Trainer 15] 开始训练
2025-07-30 22:53:33,679 - INFO - [Trainer 15] 训练集大小: 300
2025-07-30 22:53:33,680 - INFO - [Trainer 15] 模型已移至设备: cpu
2025-07-30 22:53:33,680 - INFO - [Trainer 15] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:33,681 - INFO - [Trainer 15] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:33,681 - INFO - [Trainer 15] 开始训练 5 个epoch
2025-07-30 22:53:33,681 - INFO - [Trainer 15] 开始第 1/5 个epoch
2025-07-30 22:53:33,730 - INFO - [Trainer 15] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1757, y=[0, 5, 7, 5, 7]
2025-07-30 22:53:33,730 - INFO - [Trainer 15] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:33,762 - INFO - [Trainer 15] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:33,763 - INFO - [Trainer 71] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5900
2025-07-30 22:53:33,763 - INFO - [Trainer 71] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:33,764 - INFO - [Trainer 71] 标签样本: [9, 2, 2, 2, 4]
2025-07-30 22:53:33,804 - INFO - [Trainer 71] Batch 0, Loss: 2.3004
2025-07-30 22:53:33,945 - INFO - 客户端 68 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:33,945 - INFO - 向客户端 68 发送全局模型 - 参数数量: 74
2025-07-30 22:53:33,945 - INFO - [Client 68] 收到直接传入的模型权重
2025-07-30 22:53:33,945 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:33,945 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:33,946 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:33,955 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:33,955 - INFO - [Client 68] 成功接收并加载模型
2025-07-30 22:53:33,955 - INFO - 客户端 68 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:33,955 - INFO - [Trainer.reset_staleness] 客户端 68 陈旧度重置
2025-07-30 22:53:33,955 - INFO - 重置客户端 68 的陈旧度
2025-07-30 22:53:33,955 - INFO - 成功发送全局模型到客户端 68
2025-07-30 22:53:33,956 - INFO - 客户端 68 开始本地训练
2025-07-30 22:53:33,956 - INFO - [客户端类型: Client, ID: 68] 准备开始训练
2025-07-30 22:53:33,956 - INFO - [客户端 68] 使用的训练器 client_id: 68
2025-07-30 22:53:33,956 - INFO - [Client 68] 开始验证训练集
2025-07-30 22:53:33,958 - INFO - [Client 68] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:33,958 - INFO - [Client 68] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:33,958 - INFO - [Trainer 68] 开始训练
2025-07-30 22:53:33,958 - INFO - [Trainer 68] 训练集大小: 300
2025-07-30 22:53:33,959 - INFO - [Trainer 68] 模型已移至设备: cpu
2025-07-30 22:53:33,959 - INFO - [Trainer 68] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:33,959 - INFO - [Trainer 68] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:33,959 - INFO - [Trainer 68] 开始训练 5 个epoch
2025-07-30 22:53:33,959 - INFO - [Trainer 68] 开始第 1/5 个epoch
2025-07-30 22:53:33,987 - INFO - [Trainer 68] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2346, y=[5, 5, 5, 7, 5]
2025-07-30 22:53:33,987 - INFO - [Trainer 68] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:34,008 - INFO - [Trainer 68] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:34,008 - INFO - 客户端 73 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:34,008 - INFO - 向客户端 73 发送全局模型 - 参数数量: 74
2025-07-30 22:53:34,009 - INFO - [Client 73] 收到直接传入的模型权重
2025-07-30 22:53:34,009 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:34,009 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:34,009 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:34,025 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:34,025 - INFO - [Client 73] 成功接收并加载模型
2025-07-30 22:53:34,026 - INFO - 客户端 73 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:34,026 - INFO - [Trainer.reset_staleness] 客户端 73 陈旧度重置
2025-07-30 22:53:34,026 - INFO - 重置客户端 73 的陈旧度
2025-07-30 22:53:34,026 - INFO - 成功发送全局模型到客户端 73
2025-07-30 22:53:34,026 - INFO - 客户端 73 开始本地训练
2025-07-30 22:53:34,026 - INFO - [客户端类型: Client, ID: 73] 准备开始训练
2025-07-30 22:53:34,027 - INFO - [客户端 73] 使用的训练器 client_id: 73
2025-07-30 22:53:34,027 - INFO - [Client 73] 开始验证训练集
2025-07-30 22:53:34,029 - INFO - [Client 73] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:34,029 - INFO - [Client 73] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:34,029 - INFO - [Trainer 73] 开始训练
2025-07-30 22:53:34,029 - INFO - [Trainer 73] 训练集大小: 300
2025-07-30 22:53:34,030 - INFO - [Trainer 73] 模型已移至设备: cpu
2025-07-30 22:53:34,030 - INFO - [Trainer 73] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:34,031 - INFO - [Trainer 73] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:34,031 - INFO - [Trainer 73] 开始训练 5 个epoch
2025-07-30 22:53:34,031 - INFO - [Trainer 73] 开始第 1/5 个epoch
2025-07-30 22:53:34,066 - INFO - [Trainer 73] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3600, y=[9, 9, 9, 9, 9]
2025-07-30 22:53:34,075 - INFO - [Trainer 73] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:34,167 - INFO - [Trainer 73] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:34,168 - INFO - 客户端 88 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:34,168 - INFO - 向客户端 88 发送全局模型 - 参数数量: 74
2025-07-30 22:53:34,168 - INFO - [Client 88] 收到直接传入的模型权重
2025-07-30 22:53:34,169 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:34,169 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:34,169 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:34,173 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:34,173 - INFO - [Client 88] 成功接收并加载模型
2025-07-30 22:53:34,174 - INFO - 客户端 88 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:34,174 - INFO - [Trainer.reset_staleness] 客户端 88 陈旧度重置
2025-07-30 22:53:34,174 - INFO - 重置客户端 88 的陈旧度
2025-07-30 22:53:34,174 - INFO - 成功发送全局模型到客户端 88
2025-07-30 22:53:34,174 - INFO - 客户端 88 开始本地训练
2025-07-30 22:53:34,176 - INFO - [客户端类型: Client, ID: 88] 准备开始训练
2025-07-30 22:53:34,176 - INFO - [客户端 88] 使用的训练器 client_id: 88
2025-07-30 22:53:34,176 - INFO - [Client 88] 开始验证训练集
2025-07-30 22:53:34,177 - INFO - [Client 88] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:34,177 - INFO - [Client 88] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:34,177 - INFO - [Trainer 88] 开始训练
2025-07-30 22:53:34,177 - INFO - [Trainer 88] 训练集大小: 300
2025-07-30 22:53:34,178 - INFO - [Trainer 88] 模型已移至设备: cpu
2025-07-30 22:53:34,178 - INFO - [Trainer 88] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:34,178 - INFO - [Trainer 88] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:34,179 - INFO - [Trainer 88] 开始训练 5 个epoch
2025-07-30 22:53:34,179 - INFO - [Trainer 88] 开始第 1/5 个epoch
2025-07-30 22:53:34,190 - INFO - [Trainer 88] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3097, y=[3, 9, 2, 8, 9]
2025-07-30 22:53:34,190 - INFO - [Trainer 88] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:34,198 - INFO - [Trainer 88] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:34,417 - INFO - 客户端 16 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:34,417 - INFO - 向客户端 16 发送全局模型 - 参数数量: 74
2025-07-30 22:53:34,417 - INFO - [Client 16] 收到直接传入的模型权重
2025-07-30 22:53:34,417 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:34,417 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:34,418 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:34,453 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:34,453 - INFO - [Client 16] 成功接收并加载模型
2025-07-30 22:53:34,453 - INFO - 客户端 16 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:34,453 - INFO - [Trainer.reset_staleness] 客户端 16 陈旧度重置
2025-07-30 22:53:34,453 - INFO - 重置客户端 16 的陈旧度
2025-07-30 22:53:34,453 - INFO - 成功发送全局模型到客户端 16
2025-07-30 22:53:34,454 - INFO - 客户端 16 开始本地训练
2025-07-30 22:53:34,454 - INFO - [客户端类型: Client, ID: 16] 准备开始训练
2025-07-30 22:53:34,454 - INFO - [客户端 16] 使用的训练器 client_id: 16
2025-07-30 22:53:34,454 - INFO - [Client 16] 开始验证训练集
2025-07-30 22:53:34,457 - INFO - [Client 16] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:34,457 - INFO - [Client 16] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:34,457 - INFO - [Trainer 16] 开始训练
2025-07-30 22:53:34,457 - INFO - [Trainer 16] 训练集大小: 300
2025-07-30 22:53:34,457 - INFO - [Trainer 16] 模型已移至设备: cpu
2025-07-30 22:53:34,457 - INFO - [Trainer 16] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:34,459 - INFO - [Trainer 16] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:34,459 - INFO - [Trainer 16] 开始训练 5 个epoch
2025-07-30 22:53:34,459 - INFO - [Trainer 16] 开始第 1/5 个epoch
2025-07-30 22:53:34,493 - INFO - [Trainer 16] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5709, y=[8, 6, 6, 8, 6]
2025-07-30 22:53:34,494 - INFO - [Trainer 16] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:34,515 - INFO - [Trainer 16] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:34,516 - INFO - 客户端 75 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:34,516 - INFO - 向客户端 75 发送全局模型 - 参数数量: 74
2025-07-30 22:53:34,516 - INFO - [Client 75] 收到直接传入的模型权重
2025-07-30 22:53:34,516 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:34,516 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:34,516 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:34,544 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:34,544 - INFO - [Client 75] 成功接收并加载模型
2025-07-30 22:53:34,544 - INFO - 客户端 75 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:34,544 - INFO - [Trainer.reset_staleness] 客户端 75 陈旧度重置
2025-07-30 22:53:34,546 - INFO - 重置客户端 75 的陈旧度
2025-07-30 22:53:34,546 - INFO - 成功发送全局模型到客户端 75
2025-07-30 22:53:34,546 - INFO - 客户端 75 开始本地训练
2025-07-30 22:53:34,546 - INFO - [客户端类型: Client, ID: 75] 准备开始训练
2025-07-30 22:53:34,546 - INFO - [客户端 75] 使用的训练器 client_id: 75
2025-07-30 22:53:34,546 - INFO - [Client 75] 开始验证训练集
2025-07-30 22:53:34,547 - INFO - [Client 75] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:34,548 - INFO - [Client 75] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:34,548 - INFO - [Trainer 75] 开始训练
2025-07-30 22:53:34,548 - INFO - [Trainer 75] 训练集大小: 300
2025-07-30 22:53:34,548 - INFO - [Trainer 75] 模型已移至设备: cpu
2025-07-30 22:53:34,548 - INFO - [Trainer 75] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:34,549 - INFO - [Trainer 75] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:34,549 - INFO - [Trainer 75] 开始训练 5 个epoch
2025-07-30 22:53:34,549 - INFO - [Trainer 75] 开始第 1/5 个epoch
2025-07-30 22:53:34,574 - INFO - [Trainer 75] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4626, y=[6, 6, 1, 6, 6]
2025-07-30 22:53:34,574 - INFO - [Trainer 75] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:34,591 - INFO - [Trainer 75] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:34,591 - INFO - 客户端 5 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:34,592 - INFO - 向客户端 5 发送全局模型 - 参数数量: 74
2025-07-30 22:53:34,592 - INFO - [Client 5] 收到直接传入的模型权重
2025-07-30 22:53:34,592 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:34,592 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:34,592 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:34,635 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:34,635 - INFO - [Client 5] 成功接收并加载模型
2025-07-30 22:53:34,635 - INFO - 客户端 5 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:34,635 - INFO - [Trainer.reset_staleness] 客户端 5 陈旧度重置
2025-07-30 22:53:34,635 - INFO - 重置客户端 5 的陈旧度
2025-07-30 22:53:34,636 - INFO - 成功发送全局模型到客户端 5
2025-07-30 22:53:34,636 - INFO - 客户端 5 开始本地训练
2025-07-30 22:53:34,636 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-07-30 22:53:34,636 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-07-30 22:53:34,636 - INFO - [Client 5] 开始验证训练集
2025-07-30 22:53:34,637 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:34,637 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:34,637 - INFO - [Trainer 5] 开始训练
2025-07-30 22:53:34,637 - INFO - [Trainer 5] 训练集大小: 300
2025-07-30 22:53:34,638 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-07-30 22:53:34,639 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:34,639 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:34,639 - INFO - [Trainer 5] 开始训练 5 个epoch
2025-07-30 22:53:34,640 - INFO - [Trainer 5] 开始第 1/5 个epoch
2025-07-30 22:53:34,684 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4063, y=[7, 3, 7, 3, 3]
2025-07-30 22:53:34,684 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:34,716 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:34,717 - INFO - 客户端 2 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:34,717 - INFO - 向客户端 2 发送全局模型 - 参数数量: 74
2025-07-30 22:53:34,717 - INFO - [Client 2] 收到直接传入的模型权重
2025-07-30 22:53:34,717 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:34,717 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:34,717 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:34,756 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:34,757 - INFO - [Client 2] 成功接收并加载模型
2025-07-30 22:53:34,757 - INFO - 客户端 2 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:34,757 - INFO - [Trainer.reset_staleness] 客户端 2 陈旧度重置
2025-07-30 22:53:34,757 - INFO - 重置客户端 2 的陈旧度
2025-07-30 22:53:34,757 - INFO - 成功发送全局模型到客户端 2
2025-07-30 22:53:34,757 - INFO - 客户端 2 开始本地训练
2025-07-30 22:53:34,757 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-07-30 22:53:34,757 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-07-30 22:53:34,757 - INFO - [Client 2] 开始验证训练集
2025-07-30 22:53:34,759 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:34,760 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:34,760 - INFO - [Trainer 2] 开始训练
2025-07-30 22:53:34,760 - INFO - [Trainer 2] 训练集大小: 300
2025-07-30 22:53:34,760 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-07-30 22:53:34,762 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:34,762 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:34,762 - INFO - [Trainer 2] 开始训练 5 个epoch
2025-07-30 22:53:34,762 - INFO - [Trainer 2] 开始第 1/5 个epoch
2025-07-30 22:53:34,801 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1527, y=[8, 8, 8, 8, 8]
2025-07-30 22:53:34,801 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:34,829 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:35,041 - INFO - 客户端 28 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:35,041 - INFO - 向客户端 28 发送全局模型 - 参数数量: 74
2025-07-30 22:53:35,041 - INFO - [Client 28] 收到直接传入的模型权重
2025-07-30 22:53:35,042 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:35,042 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:35,042 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:35,091 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:35,092 - INFO - [Client 28] 成功接收并加载模型
2025-07-30 22:53:35,092 - INFO - 客户端 28 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:35,092 - INFO - [Trainer.reset_staleness] 客户端 28 陈旧度重置
2025-07-30 22:53:35,092 - INFO - 重置客户端 28 的陈旧度
2025-07-30 22:53:35,092 - INFO - 成功发送全局模型到客户端 28
2025-07-30 22:53:35,092 - INFO - 客户端 28 开始本地训练
2025-07-30 22:53:35,092 - INFO - [客户端类型: Client, ID: 28] 准备开始训练
2025-07-30 22:53:35,092 - INFO - [客户端 28] 使用的训练器 client_id: 28
2025-07-30 22:53:35,093 - INFO - [Client 28] 开始验证训练集
2025-07-30 22:53:35,095 - INFO - [Client 28] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:35,095 - INFO - [Client 28] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:35,095 - INFO - [Trainer 28] 开始训练
2025-07-30 22:53:35,095 - INFO - [Trainer 28] 训练集大小: 300
2025-07-30 22:53:35,096 - INFO - [Trainer 28] 模型已移至设备: cpu
2025-07-30 22:53:35,096 - INFO - [Trainer 28] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:35,096 - INFO - [Trainer 28] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:35,097 - INFO - [Trainer 28] 开始训练 5 个epoch
2025-07-30 22:53:35,097 - INFO - [Trainer 28] 开始第 1/5 个epoch
2025-07-30 22:53:35,135 - INFO - [Trainer 28] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1350, y=[4, 0, 7, 7, 4]
2025-07-30 22:53:35,135 - INFO - [Trainer 28] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:35,158 - INFO - [Trainer 28] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:35,158 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2876
2025-07-30 22:53:35,159 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:35,159 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-07-30 22:53:35,205 - INFO - [Trainer 3] Batch 0, Loss: 2.2973
2025-07-30 22:53:35,350 - INFO - [Trainer 60] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2400
2025-07-30 22:53:35,351 - INFO - [Trainer 60] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:35,351 - INFO - [Trainer 60] 标签样本: [5, 5, 5, 5, 5]
2025-07-30 22:53:35,393 - INFO - [Trainer 60] Batch 0, Loss: 2.5003
2025-07-30 22:53:35,526 - INFO - [Trainer 51] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2529
2025-07-30 22:53:35,527 - INFO - [Trainer 51] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:35,527 - INFO - [Trainer 51] 标签样本: [6, 0, 0, 0, 0]
2025-07-30 22:53:35,569 - INFO - [Trainer 51] Batch 0, Loss: 2.4112
2025-07-30 22:53:35,713 - INFO - 客户端 53 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:35,714 - INFO - 向客户端 53 发送全局模型 - 参数数量: 74
2025-07-30 22:53:35,714 - INFO - [Client 53] 收到直接传入的模型权重
2025-07-30 22:53:35,714 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:35,714 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:35,714 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:35,755 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:35,756 - INFO - [Client 53] 成功接收并加载模型
2025-07-30 22:53:35,756 - INFO - 客户端 53 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:35,756 - INFO - [Trainer.reset_staleness] 客户端 53 陈旧度重置
2025-07-30 22:53:35,756 - INFO - 重置客户端 53 的陈旧度
2025-07-30 22:53:35,756 - INFO - 成功发送全局模型到客户端 53
2025-07-30 22:53:35,756 - INFO - 客户端 53 开始本地训练
2025-07-30 22:53:35,756 - INFO - [客户端类型: Client, ID: 53] 准备开始训练
2025-07-30 22:53:35,758 - INFO - [客户端 53] 使用的训练器 client_id: 53
2025-07-30 22:53:35,758 - INFO - [Client 53] 开始验证训练集
2025-07-30 22:53:35,761 - INFO - [Client 53] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:35,761 - INFO - [Client 53] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:35,761 - INFO - [Trainer 53] 开始训练
2025-07-30 22:53:35,761 - INFO - [Trainer 53] 训练集大小: 300
2025-07-30 22:53:35,762 - INFO - [Trainer 53] 模型已移至设备: cpu
2025-07-30 22:53:35,762 - INFO - [Trainer 53] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:35,762 - INFO - [Trainer 53] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:35,763 - INFO - [Trainer 53] 开始训练 5 个epoch
2025-07-30 22:53:35,763 - INFO - [Trainer 53] 开始第 1/5 个epoch
2025-07-30 22:53:35,798 - INFO - [Trainer 53] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2676, y=[3, 0, 0, 3, 0]
2025-07-30 22:53:35,798 - INFO - [Trainer 53] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:35,826 - INFO - [Trainer 53] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:36,029 - INFO - 客户端 82 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:36,030 - INFO - 向客户端 82 发送全局模型 - 参数数量: 74
2025-07-30 22:53:36,030 - INFO - [Client 82] 收到直接传入的模型权重
2025-07-30 22:53:36,030 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:36,030 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:36,030 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:36,048 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:36,049 - INFO - [Client 82] 成功接收并加载模型
2025-07-30 22:53:36,049 - INFO - 客户端 82 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:36,049 - INFO - [Trainer.reset_staleness] 客户端 82 陈旧度重置
2025-07-30 22:53:36,049 - INFO - 重置客户端 82 的陈旧度
2025-07-30 22:53:36,049 - INFO - 成功发送全局模型到客户端 82
2025-07-30 22:53:36,049 - INFO - 客户端 82 开始本地训练
2025-07-30 22:53:36,050 - INFO - [客户端类型: Client, ID: 82] 准备开始训练
2025-07-30 22:53:36,050 - INFO - [客户端 82] 使用的训练器 client_id: 82
2025-07-30 22:53:36,050 - INFO - [Client 82] 开始验证训练集
2025-07-30 22:53:36,051 - INFO - [Client 82] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:36,051 - INFO - [Client 82] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:36,052 - INFO - [Trainer 82] 开始训练
2025-07-30 22:53:36,052 - INFO - [Trainer 82] 训练集大小: 300
2025-07-30 22:53:36,052 - INFO - [Trainer 82] 模型已移至设备: cpu
2025-07-30 22:53:36,053 - INFO - [Trainer 82] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:36,053 - INFO - [Trainer 82] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:36,053 - INFO - [Trainer 82] 开始训练 5 个epoch
2025-07-30 22:53:36,053 - INFO - [Trainer 82] 开始第 1/5 个epoch
2025-07-30 22:53:36,065 - INFO - [Trainer 82] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3799, y=[9, 9, 9, 9, 5]
2025-07-30 22:53:36,066 - INFO - [Trainer 82] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:36,075 - INFO - [Trainer 82] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:36,075 - INFO - [Trainer 19] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3399
2025-07-30 22:53:36,076 - INFO - [Trainer 19] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:36,076 - INFO - [Trainer 19] 标签样本: [4, 4, 4, 4, 4]
2025-07-30 22:53:36,122 - INFO - [Trainer 19] Batch 0, Loss: 1.7169
2025-07-30 22:53:36,450 - INFO - [Trainer 59] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2178
2025-07-30 22:53:36,451 - INFO - [Trainer 59] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:36,451 - INFO - [Trainer 59] 标签样本: [8, 8, 3, 8, 8]
2025-07-30 22:53:36,496 - INFO - [Trainer 59] Batch 0, Loss: 2.0825
2025-07-30 22:53:36,633 - INFO - [Trainer 84] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2369
2025-07-30 22:53:36,633 - INFO - [Trainer 84] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:36,633 - INFO - [Trainer 84] 标签样本: [8, 8, 5, 5, 7]
2025-07-30 22:53:36,672 - INFO - [Trainer 84] Batch 0, Loss: 2.3883
2025-07-30 22:53:36,779 - INFO - 客户端 87 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:36,779 - INFO - 向客户端 87 发送全局模型 - 参数数量: 74
2025-07-30 22:53:36,779 - INFO - [Client 87] 收到直接传入的模型权重
2025-07-30 22:53:36,779 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:36,779 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:36,779 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:36,782 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:36,782 - INFO - [Client 87] 成功接收并加载模型
2025-07-30 22:53:36,782 - INFO - 客户端 87 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:36,782 - INFO - [Trainer.reset_staleness] 客户端 87 陈旧度重置
2025-07-30 22:53:36,782 - INFO - 重置客户端 87 的陈旧度
2025-07-30 22:53:36,782 - INFO - 成功发送全局模型到客户端 87
2025-07-30 22:53:36,782 - INFO - 客户端 87 开始本地训练
2025-07-30 22:53:36,784 - INFO - [客户端类型: Client, ID: 87] 准备开始训练
2025-07-30 22:53:36,784 - INFO - [客户端 87] 使用的训练器 client_id: 87
2025-07-30 22:53:36,784 - INFO - [Client 87] 开始验证训练集
2025-07-30 22:53:36,785 - INFO - [Client 87] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:36,785 - INFO - [Client 87] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:36,785 - INFO - [Trainer 87] 开始训练
2025-07-30 22:53:36,785 - INFO - [Trainer 87] 训练集大小: 300
2025-07-30 22:53:36,786 - INFO - [Trainer 87] 模型已移至设备: cpu
2025-07-30 22:53:36,786 - INFO - [Trainer 87] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:36,786 - INFO - [Trainer 87] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:36,786 - INFO - [Trainer 87] 开始训练 5 个epoch
2025-07-30 22:53:36,786 - INFO - [Trainer 87] 开始第 1/5 个epoch
2025-07-30 22:53:36,793 - INFO - [Trainer 87] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4433, y=[1, 1, 1, 1, 1]
2025-07-30 22:53:36,794 - INFO - [Trainer 87] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:36,800 - INFO - [Trainer 87] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:36,801 - INFO - [Trainer 54] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2728
2025-07-30 22:53:36,801 - INFO - [Trainer 54] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:36,801 - INFO - [Trainer 54] 标签样本: [3, 3, 3, 3, 3]
2025-07-30 22:53:36,841 - INFO - [Trainer 54] Batch 0, Loss: 2.3987
2025-07-30 22:53:36,965 - INFO - [Trainer 100] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2939
2025-07-30 22:53:36,965 - INFO - [Trainer 100] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:36,965 - INFO - [Trainer 100] 标签样本: [7, 3, 7, 7, 8]
2025-07-30 22:53:37,006 - INFO - [Trainer 100] Batch 0, Loss: 2.5218
2025-07-30 22:53:37,282 - INFO - 客户端 95 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:37,282 - INFO - 向客户端 95 发送全局模型 - 参数数量: 74
2025-07-30 22:53:37,282 - INFO - [Client 95] 收到直接传入的模型权重
2025-07-30 22:53:37,282 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:37,282 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:37,283 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:37,288 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:37,288 - INFO - [Client 95] 成功接收并加载模型
2025-07-30 22:53:37,290 - INFO - 客户端 95 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:37,290 - INFO - [Trainer.reset_staleness] 客户端 95 陈旧度重置
2025-07-30 22:53:37,290 - INFO - 重置客户端 95 的陈旧度
2025-07-30 22:53:37,290 - INFO - 成功发送全局模型到客户端 95
2025-07-30 22:53:37,290 - INFO - 客户端 95 开始本地训练
2025-07-30 22:53:37,290 - INFO - [客户端类型: Client, ID: 95] 准备开始训练
2025-07-30 22:53:37,290 - INFO - [客户端 95] 使用的训练器 client_id: 95
2025-07-30 22:53:37,290 - INFO - [Client 95] 开始验证训练集
2025-07-30 22:53:37,291 - INFO - [Client 95] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:37,291 - INFO - [Client 95] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:37,291 - INFO - [Trainer 95] 开始训练
2025-07-30 22:53:37,292 - INFO - [Trainer 95] 训练集大小: 300
2025-07-30 22:53:37,292 - INFO - [Trainer 95] 模型已移至设备: cpu
2025-07-30 22:53:37,292 - INFO - [Trainer 95] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:37,292 - INFO - [Trainer 95] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:37,292 - INFO - [Trainer 95] 开始训练 5 个epoch
2025-07-30 22:53:37,292 - INFO - [Trainer 95] 开始第 1/5 个epoch
2025-07-30 22:53:37,300 - INFO - [Trainer 95] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4567, y=[9, 4, 1, 4, 1]
2025-07-30 22:53:37,300 - INFO - [Trainer 95] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:37,307 - INFO - [Trainer 95] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:37,308 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4012
2025-07-30 22:53:37,308 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:37,308 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 6, 6]
2025-07-30 22:53:37,349 - INFO - [Trainer 9] Batch 0, Loss: 2.4473
2025-07-30 22:53:37,646 - INFO - [Trainer 98] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3852
2025-07-30 22:53:37,646 - INFO - [Trainer 98] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:37,647 - INFO - [Trainer 98] 标签样本: [6, 9, 9, 6, 7]
2025-07-30 22:53:37,690 - INFO - [Trainer 98] Batch 0, Loss: 2.7763
2025-07-30 22:53:37,804 - INFO - 客户端 74 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:37,804 - INFO - 向客户端 74 发送全局模型 - 参数数量: 74
2025-07-30 22:53:37,805 - INFO - [Client 74] 收到直接传入的模型权重
2025-07-30 22:53:37,805 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:37,805 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:37,805 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:37,827 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:37,827 - INFO - [Client 74] 成功接收并加载模型
2025-07-30 22:53:37,827 - INFO - 客户端 74 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:37,827 - INFO - [Trainer.reset_staleness] 客户端 74 陈旧度重置
2025-07-30 22:53:37,828 - INFO - 重置客户端 74 的陈旧度
2025-07-30 22:53:37,828 - INFO - 成功发送全局模型到客户端 74
2025-07-30 22:53:37,828 - INFO - 客户端 74 开始本地训练
2025-07-30 22:53:37,828 - INFO - [客户端类型: Client, ID: 74] 准备开始训练
2025-07-30 22:53:37,828 - INFO - [客户端 74] 使用的训练器 client_id: 74
2025-07-30 22:53:37,828 - INFO - [Client 74] 开始验证训练集
2025-07-30 22:53:37,830 - INFO - [Client 74] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:37,830 - INFO - [Client 74] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:37,830 - INFO - [Trainer 74] 开始训练
2025-07-30 22:53:37,831 - INFO - [Trainer 74] 训练集大小: 300
2025-07-30 22:53:37,831 - INFO - [Trainer 74] 模型已移至设备: cpu
2025-07-30 22:53:37,832 - INFO - [Trainer 74] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:37,832 - INFO - [Trainer 74] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:37,832 - INFO - [Trainer 74] 开始训练 5 个epoch
2025-07-30 22:53:37,832 - INFO - [Trainer 74] 开始第 1/5 个epoch
2025-07-30 22:53:37,855 - INFO - [Trainer 74] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2636, y=[1, 5, 1, 5, 1]
2025-07-30 22:53:37,855 - INFO - [Trainer 74] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:37,871 - INFO - [Trainer 74] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:37,872 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5185
2025-07-30 22:53:37,872 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:37,873 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-07-30 22:53:37,913 - INFO - [Trainer 7] Batch 0, Loss: 2.1883
2025-07-30 22:53:38,049 - INFO - 客户端 56 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:38,049 - INFO - 向客户端 56 发送全局模型 - 参数数量: 74
2025-07-30 22:53:38,049 - INFO - [Client 56] 收到直接传入的模型权重
2025-07-30 22:53:38,049 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:38,050 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:38,050 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:38,083 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:38,083 - INFO - [Client 56] 成功接收并加载模型
2025-07-30 22:53:38,083 - INFO - 客户端 56 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:38,083 - INFO - [Trainer.reset_staleness] 客户端 56 陈旧度重置
2025-07-30 22:53:38,083 - INFO - 重置客户端 56 的陈旧度
2025-07-30 22:53:38,083 - INFO - 成功发送全局模型到客户端 56
2025-07-30 22:53:38,084 - INFO - 客户端 56 开始本地训练
2025-07-30 22:53:38,084 - INFO - [客户端类型: Client, ID: 56] 准备开始训练
2025-07-30 22:53:38,084 - INFO - [客户端 56] 使用的训练器 client_id: 56
2025-07-30 22:53:38,084 - INFO - [Client 56] 开始验证训练集
2025-07-30 22:53:38,085 - INFO - [Client 56] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:38,085 - INFO - [Client 56] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:38,085 - INFO - [Trainer 56] 开始训练
2025-07-30 22:53:38,086 - INFO - [Trainer 56] 训练集大小: 300
2025-07-30 22:53:38,086 - INFO - [Trainer 56] 模型已移至设备: cpu
2025-07-30 22:53:38,087 - INFO - [Trainer 56] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:38,087 - INFO - [Trainer 56] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:38,087 - INFO - [Trainer 56] 开始训练 5 个epoch
2025-07-30 22:53:38,087 - INFO - [Trainer 56] 开始第 1/5 个epoch
2025-07-30 22:53:38,110 - INFO - [Trainer 56] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0846, y=[0, 0, 0, 0, 9]
2025-07-30 22:53:38,110 - INFO - [Trainer 56] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:38,130 - INFO - [Trainer 56] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:38,130 - INFO - 客户端 52 拉取最新模型（当前陈旧度: 0，全局轮次: 0）
2025-07-30 22:53:38,130 - INFO - 向客户端 52 发送全局模型 - 参数数量: 74
2025-07-30 22:53:38,131 - INFO - [Client 52] 收到直接传入的模型权重
2025-07-30 22:53:38,131 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-07-30 22:53:38,131 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-07-30 22:53:38,131 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-07-30 22:53:38,214 - INFO - [Algorithm] 成功加载权重到模型
2025-07-30 22:53:38,215 - INFO - [Client 52] 成功接收并加载模型
2025-07-30 22:53:38,215 - INFO - 客户端 52 在轮次 0 成功拉取了最新模型
2025-07-30 22:53:38,215 - INFO - [Trainer.reset_staleness] 客户端 52 陈旧度重置
2025-07-30 22:53:38,215 - INFO - 重置客户端 52 的陈旧度
2025-07-30 22:53:38,215 - INFO - 成功发送全局模型到客户端 52
2025-07-30 22:53:38,215 - INFO - 客户端 52 开始本地训练
2025-07-30 22:53:38,215 - INFO - [客户端类型: Client, ID: 52] 准备开始训练
2025-07-30 22:53:38,215 - INFO - [客户端 52] 使用的训练器 client_id: 52
2025-07-30 22:53:38,216 - INFO - [Client 52] 开始验证训练集
2025-07-30 22:53:38,223 - INFO - [Client 52] 成功访问第一个训练样本: <class 'tuple'>
2025-07-30 22:53:38,224 - INFO - [Client 52] 🚀 开始训练，数据集大小: 300
2025-07-30 22:53:38,224 - INFO - [Trainer 52] 开始训练
2025-07-30 22:53:38,224 - INFO - [Trainer 52] 训练集大小: 300
2025-07-30 22:53:38,225 - INFO - [Trainer 52] 模型已移至设备: cpu
2025-07-30 22:53:38,225 - INFO - [Trainer 52] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-30 22:53:38,225 - INFO - [Trainer 52] 创建数据加载器，批次大小: 32, 批次数: 10
2025-07-30 22:53:38,225 - INFO - [Trainer 52] 开始训练 5 个epoch
2025-07-30 22:53:38,225 - INFO - [Trainer 52] 开始第 1/5 个epoch
2025-07-30 22:53:38,287 - INFO - [Trainer 52] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3464, y=[3, 3, 3, 3, 6]
2025-07-30 22:53:38,287 - INFO - [Trainer 52] Epoch 1 开始处理 10 个批次
2025-07-30 22:53:38,328 - INFO - [Trainer 52] Epoch 1 进度: 1/10 批次
2025-07-30 22:53:38,330 - INFO - [Trainer 41] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4136
2025-07-30 22:53:38,330 - INFO - [Trainer 41] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:38,331 - INFO - [Trainer 41] 标签样本: [4, 9, 4, 4, 4]
2025-07-30 22:53:38,378 - INFO - [Trainer 41] Batch 0, Loss: 1.8998
2025-07-30 22:53:38,553 - INFO - [Trainer 42] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4559
2025-07-30 22:53:38,553 - INFO - [Trainer 42] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:38,554 - INFO - [Trainer 42] 标签样本: [6, 1, 6, 6, 6]
2025-07-30 22:53:38,599 - INFO - [Trainer 42] Batch 0, Loss: 2.5179
2025-07-30 22:53:39,001 - INFO - [Trainer 92] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1256
2025-07-30 22:53:39,001 - INFO - [Trainer 92] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:39,002 - INFO - [Trainer 92] 标签样本: [0, 0, 0, 0, 9]
2025-07-30 22:53:39,065 - INFO - [Trainer 92] Batch 0, Loss: 2.5253
2025-07-30 22:53:39,339 - INFO - [Trainer 63] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5073
2025-07-30 22:53:39,339 - INFO - [Trainer 63] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:39,339 - INFO - [Trainer 63] 标签样本: [9, 7, 9, 7, 7]
2025-07-30 22:53:39,377 - INFO - [Trainer 63] Batch 0, Loss: 2.8449
2025-07-30 22:53:39,669 - INFO - [Trainer 62] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3464
2025-07-30 22:53:39,669 - INFO - [Trainer 62] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:39,669 - INFO - [Trainer 62] 标签样本: [1, 1, 9, 9, 9]
2025-07-30 22:53:39,707 - INFO - [Trainer 62] Batch 0, Loss: 2.6952
2025-07-30 22:53:39,823 - INFO - [Trainer 48] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3044
2025-07-30 22:53:39,823 - INFO - [Trainer 48] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:39,823 - INFO - [Trainer 48] 标签样本: [3, 3, 2, 2, 2]
2025-07-30 22:53:39,861 - INFO - [Trainer 48] Batch 0, Loss: 2.2438
2025-07-30 22:53:40,146 - INFO - [Trainer 35] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5291
2025-07-30 22:53:40,147 - INFO - [Trainer 35] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:40,147 - INFO - [Trainer 35] 标签样本: [3, 3, 3, 3, 2]
2025-07-30 22:53:40,185 - INFO - [Trainer 35] Batch 0, Loss: 2.4142
2025-07-30 22:53:40,306 - INFO - [Trainer 23] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5308
2025-07-30 22:53:40,307 - INFO - [Trainer 23] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:40,307 - INFO - [Trainer 23] 标签样本: [1, 6, 9, 6, 6]
2025-07-30 22:53:40,347 - INFO - [Trainer 23] Batch 0, Loss: 2.6458
2025-07-30 22:53:40,475 - INFO - [Trainer 66] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5417
2025-07-30 22:53:40,475 - INFO - [Trainer 66] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:40,475 - INFO - [Trainer 66] 标签样本: [3, 9, 3, 9, 3]
2025-07-30 22:53:40,519 - INFO - [Trainer 66] Batch 0, Loss: 2.4730
2025-07-30 22:53:40,646 - INFO - [Trainer 36] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3779
2025-07-30 22:53:40,647 - INFO - [Trainer 36] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:40,647 - INFO - [Trainer 36] 标签样本: [5, 5, 1, 1, 1]
2025-07-30 22:53:40,687 - INFO - [Trainer 36] Batch 0, Loss: 2.3557
2025-07-30 22:53:40,971 - INFO - [Trainer 21] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2237
2025-07-30 22:53:40,972 - INFO - [Trainer 21] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:40,972 - INFO - [Trainer 21] 标签样本: [7, 7, 7, 8, 7]
2025-07-30 22:53:41,016 - INFO - [Trainer 21] Batch 0, Loss: 2.5483
2025-07-30 22:53:41,330 - INFO - [Trainer 46] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3648
2025-07-30 22:53:41,330 - INFO - [Trainer 46] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:41,330 - INFO - [Trainer 46] 标签样本: [4, 7, 4, 4, 7]
2025-07-30 22:53:41,376 - INFO - [Trainer 46] Batch 0, Loss: 2.2857
2025-07-30 22:53:41,512 - INFO - [Trainer 55] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4344
2025-07-30 22:53:41,513 - INFO - [Trainer 55] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:41,513 - INFO - [Trainer 55] 标签样本: [7, 7, 7, 6, 6]
2025-07-30 22:53:41,554 - INFO - [Trainer 55] Batch 0, Loss: 2.8474
2025-07-30 22:53:41,868 - INFO - [Trainer 81] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3844
2025-07-30 22:53:41,868 - INFO - [Trainer 81] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:41,868 - INFO - [Trainer 81] 标签样本: [9, 1, 1, 1, 9]
2025-07-30 22:53:41,907 - INFO - [Trainer 81] Batch 0, Loss: 2.6894
2025-07-30 22:53:42,056 - INFO - [Trainer 99] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4721
2025-07-30 22:53:42,057 - INFO - [Trainer 99] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:42,057 - INFO - [Trainer 99] 标签样本: [5, 6, 5, 1, 5]
2025-07-30 22:53:42,108 - INFO - [Trainer 99] Batch 0, Loss: 2.5304
2025-07-30 22:53:42,444 - INFO - [Trainer 18] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2777
2025-07-30 22:53:42,446 - INFO - [Trainer 18] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:42,446 - INFO - [Trainer 18] 标签样本: [9, 1, 9, 6, 6]
2025-07-30 22:53:42,493 - INFO - [Trainer 18] Batch 0, Loss: 2.4456
2025-07-30 22:53:43,151 - INFO - [Trainer 77] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0888
2025-07-30 22:53:43,151 - INFO - [Trainer 77] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:43,152 - INFO - [Trainer 77] 标签样本: [6, 0, 0, 0, 0]
2025-07-30 22:53:43,190 - INFO - [Trainer 77] Batch 0, Loss: 2.4271
2025-07-30 22:53:43,337 - INFO - [Trainer 47] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2962
2025-07-30 22:53:43,337 - INFO - [Trainer 47] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:43,337 - INFO - [Trainer 47] 标签样本: [2, 2, 2, 1, 1]
2025-07-30 22:53:43,410 - INFO - [Trainer 47] Batch 0, Loss: 2.3043
2025-07-30 22:53:43,669 - INFO - [Trainer 78] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3356
2025-07-30 22:53:43,669 - INFO - [Trainer 78] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:43,669 - INFO - [Trainer 78] 标签样本: [1, 2, 2, 2, 3]
2025-07-30 22:53:43,710 - INFO - [Trainer 78] Batch 0, Loss: 2.4247
2025-07-30 22:53:44,089 - INFO - [Trainer 38] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4739
2025-07-30 22:53:44,089 - INFO - [Trainer 38] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:44,090 - INFO - [Trainer 38] 标签样本: [1, 1, 1, 1, 1]
2025-07-30 22:53:44,132 - INFO - [Trainer 38] Batch 0, Loss: 2.2998
2025-07-30 22:53:44,309 - INFO - [Trainer 22] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3229
2025-07-30 22:53:44,309 - INFO - [Trainer 22] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:44,309 - INFO - [Trainer 22] 标签样本: [9, 6, 6, 8, 8]
2025-07-30 22:53:44,373 - INFO - [Trainer 22] Batch 0, Loss: 2.3878
2025-07-30 22:53:44,547 - INFO - [Trainer 79] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4221
2025-07-30 22:53:44,547 - INFO - [Trainer 79] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:44,547 - INFO - [Trainer 79] 标签样本: [2, 6, 6, 6, 3]
2025-07-30 22:53:44,598 - INFO - [Trainer 79] Batch 0, Loss: 2.3749
2025-07-30 22:53:45,034 - INFO - [Trainer 80] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3498
2025-07-30 22:53:45,034 - INFO - [Trainer 80] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:45,034 - INFO - [Trainer 80] 标签样本: [2, 6, 2, 2, 6]
2025-07-30 22:53:45,077 - INFO - [Trainer 80] Batch 0, Loss: 2.4666
2025-07-30 22:53:45,260 - INFO - [Trainer 61] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4333
2025-07-30 22:53:45,261 - INFO - [Trainer 61] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:45,261 - INFO - [Trainer 61] 标签样本: [6, 2, 2, 2, 3]
2025-07-30 22:53:45,305 - INFO - [Trainer 61] Batch 0, Loss: 2.3172
2025-07-30 22:53:45,489 - INFO - [Trainer 37] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0962
2025-07-30 22:53:45,490 - INFO - [Trainer 37] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:45,490 - INFO - [Trainer 37] 标签样本: [5, 5, 0, 0, 8]
2025-07-30 22:53:45,540 - INFO - [Trainer 37] Batch 0, Loss: 2.3781
2025-07-30 22:53:45,716 - INFO - [Trainer 45] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.6443
2025-07-30 22:53:45,716 - INFO - [Trainer 45] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:45,716 - INFO - [Trainer 45] 标签样本: [6, 6, 6, 4, 6]
2025-07-30 22:53:45,765 - INFO - [Trainer 45] Batch 0, Loss: 2.3865
2025-07-30 22:53:45,937 - INFO - [Trainer 39] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4245
2025-07-30 22:53:45,938 - INFO - [Trainer 39] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:45,938 - INFO - [Trainer 39] 标签样本: [5, 5, 6, 5, 4]
2025-07-30 22:53:45,983 - INFO - [Trainer 39] Batch 0, Loss: 2.4394
2025-07-30 22:53:46,405 - INFO - [Trainer 13] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3102
2025-07-30 22:53:46,405 - INFO - [Trainer 13] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:46,406 - INFO - [Trainer 13] 标签样本: [2, 2, 2, 2, 2]
2025-07-30 22:53:46,460 - INFO - [Trainer 13] Batch 0, Loss: 2.2012
2025-07-30 22:53:46,653 - INFO - [Trainer 14] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3388
2025-07-30 22:53:46,653 - INFO - [Trainer 14] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:46,653 - INFO - [Trainer 14] 标签样本: [7, 7, 7, 7, 4]
2025-07-30 22:53:46,711 - INFO - [Trainer 14] Batch 0, Loss: 2.5831
2025-07-30 22:53:46,879 - INFO - [Trainer 83] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5200
2025-07-30 22:53:46,879 - INFO - [Trainer 83] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:46,879 - INFO - [Trainer 83] 标签样本: [2, 2, 2, 2, 2]
2025-07-30 22:53:46,932 - INFO - [Trainer 83] Batch 0, Loss: 2.2195
2025-07-30 22:53:48,261 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3033
2025-07-30 22:53:48,262 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:48,262 - INFO - [Trainer 8] 标签样本: [0, 0, 0, 6, 0]
2025-07-30 22:53:48,320 - INFO - [Trainer 8] Batch 0, Loss: 2.3605
2025-07-30 22:53:48,485 - INFO - [Trainer 26] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4151
2025-07-30 22:53:48,487 - INFO - [Trainer 26] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:48,487 - INFO - [Trainer 26] 标签样本: [7, 7, 7, 7, 7]
2025-07-30 22:53:48,533 - INFO - [Trainer 26] Batch 0, Loss: 2.8322
2025-07-30 22:53:49,316 - INFO - [Trainer 96] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3408
2025-07-30 22:53:49,316 - INFO - [Trainer 96] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:49,316 - INFO - [Trainer 96] 标签样本: [2, 2, 1, 1, 1]
2025-07-30 22:53:49,366 - INFO - [Trainer 96] Batch 0, Loss: 2.4601
2025-07-30 22:53:49,488 - INFO - [Trainer 31] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4941
2025-07-30 22:53:49,489 - INFO - [Trainer 31] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:49,489 - INFO - [Trainer 31] 标签样本: [1, 1, 1, 1, 1]
2025-07-30 22:53:49,539 - INFO - [Trainer 31] Batch 0, Loss: 2.4019
2025-07-30 22:53:50,110 - INFO - [Trainer 30] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3039
2025-07-30 22:53:50,110 - INFO - [Trainer 30] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:50,110 - INFO - [Trainer 30] 标签样本: [2, 2, 2, 2, 9]
2025-07-30 22:53:50,157 - INFO - [Trainer 30] Batch 0, Loss: 2.2764
2025-07-30 22:53:50,511 - INFO - [Trainer 25] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0242
2025-07-30 22:53:50,511 - INFO - [Trainer 25] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:50,511 - INFO - [Trainer 25] 标签样本: [0, 0, 0, 0, 0]
2025-07-30 22:53:50,559 - INFO - [Trainer 25] Batch 0, Loss: 2.3699
2025-07-30 22:53:50,916 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1054
2025-07-30 22:53:50,916 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:50,916 - INFO - [Trainer 1] 标签样本: [8, 8, 5, 8, 5]
2025-07-30 22:53:50,977 - INFO - [Trainer 1] Batch 0, Loss: 2.3009
2025-07-30 22:53:51,161 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0250
2025-07-30 22:53:51,161 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:51,161 - INFO - [Trainer 6] 标签样本: [8, 7, 8, 0, 0]
2025-07-30 22:53:51,207 - INFO - [Trainer 6] Batch 0, Loss: 2.3604
2025-07-30 22:53:51,574 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:53:51,574 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:53:51,576 - INFO - [Trainer 44] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5235
2025-07-30 22:53:51,576 - INFO - [Trainer 44] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:51,576 - INFO - [Trainer 44] 标签样本: [4, 7, 7, 7, 7]
2025-07-30 22:53:51,625 - INFO - [Trainer 44] Batch 0, Loss: 2.3233
2025-07-30 22:53:52,007 - INFO - [Trainer 15] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3142
2025-07-30 22:53:52,007 - INFO - [Trainer 15] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:52,007 - INFO - [Trainer 15] 标签样本: [5, 0, 5, 0, 5]
2025-07-30 22:53:52,051 - INFO - [Trainer 15] Batch 0, Loss: 2.5627
2025-07-30 22:53:52,418 - INFO - [Trainer 68] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3111
2025-07-30 22:53:52,418 - INFO - [Trainer 68] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:52,418 - INFO - [Trainer 68] 标签样本: [5, 5, 5, 5, 5]
2025-07-30 22:53:52,474 - INFO - [Trainer 68] Batch 0, Loss: 2.5182
2025-07-30 22:53:52,664 - INFO - [Trainer 73] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2028
2025-07-30 22:53:52,664 - INFO - [Trainer 73] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:52,665 - INFO - [Trainer 73] 标签样本: [9, 9, 9, 4, 9]
2025-07-30 22:53:52,717 - INFO - [Trainer 73] Batch 0, Loss: 2.8811
2025-07-30 22:53:52,877 - INFO - [Trainer 88] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2510
2025-07-30 22:53:52,877 - INFO - [Trainer 88] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:52,877 - INFO - [Trainer 88] 标签样本: [9, 9, 9, 3, 3]
2025-07-30 22:53:52,924 - INFO - [Trainer 88] Batch 0, Loss: 2.6754
2025-07-30 22:53:53,263 - INFO - [Trainer 16] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.6427
2025-07-30 22:53:53,263 - INFO - [Trainer 16] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:53,263 - INFO - [Trainer 16] 标签样本: [6, 6, 3, 3, 3]
2025-07-30 22:53:53,305 - INFO - [Trainer 16] Batch 0, Loss: 2.4246
2025-07-30 22:53:53,422 - INFO - [Trainer 75] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3880
2025-07-30 22:53:53,422 - INFO - [Trainer 75] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:53,422 - INFO - [Trainer 75] 标签样本: [6, 6, 1, 6, 1]
2025-07-30 22:53:53,468 - INFO - [Trainer 75] Batch 0, Loss: 2.4849
2025-07-30 22:53:53,596 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3508
2025-07-30 22:53:53,596 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:53,597 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 7, 3]
2025-07-30 22:53:53,638 - INFO - [Trainer 5] Batch 0, Loss: 2.7716
2025-07-30 22:53:53,766 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2140
2025-07-30 22:53:53,766 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:53,767 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-07-30 22:53:53,810 - INFO - [Trainer 2] Batch 0, Loss: 2.1256
2025-07-30 22:53:54,091 - INFO - [Trainer 28] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3107
2025-07-30 22:53:54,091 - INFO - [Trainer 28] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:54,091 - INFO - [Trainer 28] 标签样本: [7, 4, 7, 7, 7]
2025-07-30 22:53:54,133 - INFO - [Trainer 28] Batch 0, Loss: 2.4625
2025-07-30 22:53:54,758 - INFO - [Trainer 53] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0191
2025-07-30 22:53:54,758 - INFO - [Trainer 53] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:54,758 - INFO - [Trainer 53] 标签样本: [3, 3, 0, 3, 0]
2025-07-30 22:53:54,797 - INFO - [Trainer 53] Batch 0, Loss: 2.3692
2025-07-30 22:53:55,078 - INFO - [Trainer 82] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4270
2025-07-30 22:53:55,078 - INFO - [Trainer 82] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:55,078 - INFO - [Trainer 82] 标签样本: [9, 9, 9, 9, 9]
2025-07-30 22:53:55,124 - INFO - [Trainer 82] Batch 0, Loss: 2.8861
2025-07-30 22:53:55,891 - INFO - [Trainer 87] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4515
2025-07-30 22:53:55,891 - INFO - [Trainer 87] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:55,891 - INFO - [Trainer 87] 标签样本: [4, 4, 4, 1, 1]
2025-07-30 22:53:55,933 - INFO - [Trainer 87] Batch 0, Loss: 2.1342
2025-07-30 22:53:56,499 - INFO - [Trainer 95] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4830
2025-07-30 22:53:56,499 - INFO - [Trainer 95] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:56,499 - INFO - [Trainer 95] 标签样本: [1, 1, 1, 1, 9]
2025-07-30 22:53:56,542 - INFO - [Trainer 95] Batch 0, Loss: 2.4983
2025-07-30 22:53:56,852 - INFO - [Trainer 94] Batch 5, Loss: 1.5951
2025-07-30 22:53:57,109 - INFO - [Trainer 74] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4402
2025-07-30 22:53:57,110 - INFO - [Trainer 74] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:57,110 - INFO - [Trainer 74] 标签样本: [1, 5, 1, 5, 1]
2025-07-30 22:53:57,151 - INFO - [Trainer 74] Batch 0, Loss: 2.3997
2025-07-30 22:53:57,424 - INFO - [Trainer 56] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1961
2025-07-30 22:53:57,424 - INFO - [Trainer 56] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:57,424 - INFO - [Trainer 56] 标签样本: [0, 7, 0, 0, 0]
2025-07-30 22:53:57,462 - INFO - [Trainer 56] Batch 0, Loss: 2.2941
2025-07-30 22:53:57,575 - INFO - [Trainer 52] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5608
2025-07-30 22:53:57,575 - INFO - [Trainer 52] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:53:57,575 - INFO - [Trainer 52] 标签样本: [3, 3, 3, 3, 3]
2025-07-30 22:53:57,615 - INFO - [Trainer 52] Batch 0, Loss: 2.3112
2025-07-30 22:54:01,907 - INFO - [Trainer 65] Batch 5, Loss: 0.5376
2025-07-30 22:54:04,225 - INFO - [Trainer 33] Batch 5, Loss: 1.0581
2025-07-30 22:54:08,700 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:54:08,701 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:54:19,891 - INFO - [Trainer 34] Batch 5, Loss: 0.3177
2025-07-30 22:54:20,976 - INFO - [Trainer 27] Batch 5, Loss: 1.2456
2025-07-30 22:54:22,142 - INFO - [Trainer 91] Batch 5, Loss: 0.3398
2025-07-30 22:54:22,862 - INFO - [Trainer 93] Batch 5, Loss: 2.3517
2025-07-30 22:54:24,073 - INFO - [Trainer 17] Batch 5, Loss: 1.2087
2025-07-30 22:54:24,477 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:54:24,477 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:54:24,835 - INFO - [Trainer 97] Batch 5, Loss: 1.1815
2025-07-30 22:54:25,719 - INFO - [Trainer 85] Batch 5, Loss: 0.7463
2025-07-30 22:54:26,158 - INFO - [Trainer 70] Batch 5, Loss: 0.9881
2025-07-30 22:54:27,001 - INFO - [Trainer 12] Batch 5, Loss: 1.8776
2025-07-30 22:54:28,599 - INFO - [Trainer 72] Batch 5, Loss: 1.6049
2025-07-30 22:54:28,951 - INFO - [Trainer 40] Batch 5, Loss: 1.5409
2025-07-30 22:54:30,673 - INFO - [Trainer 57] Batch 5, Loss: 0.5067
2025-07-30 22:54:32,627 - INFO - [Trainer 64] Batch 5, Loss: 1.1228
2025-07-30 22:54:33,417 - INFO - [Trainer 90] Batch 5, Loss: 0.9695
2025-07-30 22:54:35,728 - INFO - [Trainer 24] Batch 5, Loss: 1.4517
2025-07-30 22:54:35,866 - INFO - [Trainer 89] Batch 5, Loss: 4.0195
2025-07-30 22:54:41,212 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:54:41,213 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:54:43,529 - INFO - [Trainer 94] Epoch 1 进度: 10/10 批次
2025-07-30 22:54:45,248 - INFO - [Trainer 32] Batch 5, Loss: 1.7391
2025-07-30 22:54:45,694 - INFO - [Trainer 4] Batch 5, Loss: 1.4934
2025-07-30 22:54:46,781 - INFO - [Trainer 20] Batch 5, Loss: 1.4550
2025-07-30 22:54:47,229 - INFO - [Trainer 10] Batch 5, Loss: 0.9678
2025-07-30 22:54:47,709 - INFO - [Trainer 58] Batch 5, Loss: 3.0949
2025-07-30 22:54:48,109 - INFO - [Trainer 65] Epoch 1 进度: 10/10 批次
2025-07-30 22:54:49,363 - INFO - [Trainer 43] Batch 5, Loss: 0.0001
2025-07-30 22:54:50,413 - INFO - [Trainer 33] Epoch 1 进度: 10/10 批次
2025-07-30 22:54:51,337 - INFO - [Trainer 49] Batch 5, Loss: 1.2546
2025-07-30 22:54:51,494 - INFO - [Trainer 67] Batch 5, Loss: 1.0583
2025-07-30 22:54:52,065 - INFO - [Trainer 11] Batch 5, Loss: 1.3077
2025-07-30 22:54:52,207 - INFO - [Trainer 69] Batch 5, Loss: 1.2883
2025-07-30 22:54:52,641 - INFO - [Trainer 29] Batch 5, Loss: 0.9457
2025-07-30 22:54:52,789 - INFO - [Trainer 86] Batch 5, Loss: 1.6705
2025-07-30 22:54:53,368 - INFO - [Trainer 50] Batch 5, Loss: 0.2322
2025-07-30 22:54:54,150 - INFO - [Trainer 76] Batch 5, Loss: 1.3414
2025-07-30 22:54:54,454 - INFO - [Trainer 71] Batch 5, Loss: 2.5637
2025-07-30 22:54:56,045 - INFO - [Trainer 3] Batch 5, Loss: 0.0007
2025-07-30 22:54:56,194 - INFO - [Trainer 60] Batch 5, Loss: 1.4323
2025-07-30 22:54:56,358 - INFO - [Trainer 51] Batch 5, Loss: 0.6703
2025-07-30 22:54:56,994 - INFO - [Trainer 19] Batch 5, Loss: 0.0000
2025-07-30 22:54:57,263 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:54:57,263 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-07-30 22:54:57,303 - INFO - [Trainer 59] Batch 5, Loss: 0.4972
2025-07-30 22:54:57,449 - INFO - [Trainer 84] Batch 5, Loss: 1.2009
2025-07-30 22:54:57,745 - INFO - [Trainer 54] Batch 5, Loss: 1.5545
2025-07-30 22:54:57,894 - INFO - [Trainer 100] Batch 5, Loss: 1.0425
2025-07-30 22:54:58,320 - INFO - [Trainer 9] Batch 5, Loss: 1.0061
2025-07-30 22:54:58,428 - INFO - [Trainer 94] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3695
2025-07-30 22:54:58,429 - INFO - [Trainer 94] 模型设备: cpu, 输入设备: cpu
2025-07-30 22:54:58,429 - INFO - [Trainer 94] 标签样本: [4, 4, 5, 5, 5]
2025-07-30 22:54:58,487 - INFO - [Trainer 94] Batch 9, Loss: 1.0678
2025-07-30 22:54:58,691 - INFO - [Trainer 94] Epoch 1 批次循环完成，处理了 10 个批次
2025-07-30 22:54:58,692 - INFO - [Trainer 94] Epoch 1/5 完成 - 处理了 10 个批次, Loss: 1.7839, Accuracy: 50.33%
2025-07-30 22:54:58,692 - INFO - [Trainer 94] 开始第 2/5 个epoch
2025-07-30 22:54:58,707 - INFO - [Trainer 94] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4024, y=[4, 5, 4, 4, 5]
2025-07-30 22:54:58,707 - INFO - [Trainer 94] Epoch 2 开始处理 10 个批次
2025-07-30 22:54:58,716 - INFO - [Trainer 94] Epoch 2 进度: 1/10 批次
2025-07-30 22:54:58,743 - INFO - 服务器启动完成
2025-07-30 22:55:00,416 - INFO - SCAFL服务器已清理
2025-07-30 22:55:00,421 - ERROR - Task exception was never retrieved
future: <Task finished name='Task-5' coro=<Server.start.<locals>.client_async_training() done, defined at D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py:1247> exception=KeyboardInterrupt()>
Traceback (most recent call last):
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\aiohttp\web.py", line 523, in run_app
    loop.run_until_complete(main_task)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\asyncio\base_events.py", line 1905, in _run_once
    handle._run()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1269, in client_async_training
    await client.train()
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1193, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 355, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\resnet.py", line 131, in forward
    out = self.layer1(out)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\container.py", line 240, in forward
    input = module(input)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\resnet.py", line 42, in forward
    out = F.relu(self.bn1(self.conv1(x)))
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\conv.py", line 554, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\conv.py", line 549, in _conv_forward
    return F.conv2d(
KeyboardInterrupt
