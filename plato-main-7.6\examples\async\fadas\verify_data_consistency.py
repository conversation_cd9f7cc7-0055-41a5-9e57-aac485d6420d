#!/usr/bin/env python3
"""
数据分配一致性验证工具
验证ReFedScaFL和FedAS是否使用相同的数据分配逻辑

功能：
1. 模拟两种算法的数据分配过程
2. 对比分配结果的一致性
3. 生成详细的对比报告
"""

import numpy as np
import sys
import os

# 添加Plato路径
current_dir = os.path.dirname(os.path.abspath(__file__))
plato_dir = os.path.join(current_dir, '../../..')
sys.path.insert(0, plato_dir)


def simulate_standard_noniid_sampling(num_clients=100, num_classes=10, 
                                     concentration=0.1, partition_size=300, 
                                     random_seed=1):
    """
    模拟标准Plato noniid采样器的数据分配
    基于plato/samplers/dirichlet.py的实现
    """
    print("🔄 模拟标准Plato noniid采样器...")
    
    client_distributions = []
    client_sample_counts = []
    
    for client_id in range(1, num_clients + 1):
        # 设置客户端特定的随机种子 (模拟Plato的做法)
        np.random.seed(random_seed * client_id)
        
        # 生成Dirichlet分布的类别比例
        target_proportions = np.random.dirichlet(
            np.repeat(concentration, num_classes)
        )
        
        # 根据比例分配样本数
        samples_per_class = (target_proportions * partition_size).astype(int)
        
        # 处理舍入误差
        total_assigned = np.sum(samples_per_class)
        remaining = partition_size - total_assigned
        
        # 将剩余样本分配给比例最大的类别
        if remaining > 0:
            for _ in range(remaining):
                max_prop_idx = np.argmax(target_proportions)
                samples_per_class[max_prop_idx] += 1
                target_proportions[max_prop_idx] = 0  # 避免重复选择
        
        client_distributions.append(target_proportions)
        client_sample_counts.append(samples_per_class)
    
    return np.array(client_distributions), np.array(client_sample_counts)


def simulate_refedscafl_custom_sampling(num_clients=100, num_classes=10,
                                       concentration=0.1, min_size=300,
                                       max_size=1000, random_seed=1):
    """
    模拟ReFedScaFL自定义数据分配逻辑
    基于refedscafl_client.py的实现
    """
    print("🔄 模拟ReFedScaFL自定义数据分配...")
    
    np.random.seed(random_seed)
    
    client_distributions = []
    client_sample_counts = []
    
    for client_id in range(num_clients):
        # 使用Beta分布决定数据量
        quantity_factor = np.random.beta(2, 5)
        partition_size = int(min_size + (max_size - min_size) * quantity_factor)
        
        # Dirichlet分布方式
        alpha = np.ones(num_clients) * concentration
        proportions = np.random.dirichlet(alpha, num_classes)
        client_proportions = proportions[:, client_id]
        
        # 根据比例分配样本数
        samples_per_class = (client_proportions * partition_size).astype(int)
        
        # 处理剩余样本
        remaining_samples = partition_size - np.sum(samples_per_class)
        while remaining_samples > 0:
            min_class_idx = np.argmin(samples_per_class)
            samples_per_class[min_class_idx] += 1
            remaining_samples -= 1
        
        client_distributions.append(client_proportions)
        client_sample_counts.append(samples_per_class)
    
    return np.array(client_distributions), np.array(client_sample_counts)


def compare_distributions(standard_counts, custom_counts, method_names):
    """对比两种分配方法的结果"""
    print(f"\n📊 数据分配对比分析:")
    print("=" * 60)
    
    # 基本统计对比
    print(f"方法对比:")
    for i, (counts, name) in enumerate(zip([standard_counts, custom_counts], method_names)):
        total_samples = np.sum(counts)
        avg_samples_per_client = np.mean(np.sum(counts, axis=1))
        std_samples_per_client = np.std(np.sum(counts, axis=1))
        
        print(f"\n{name}:")
        print(f"  总样本数: {total_samples}")
        print(f"  平均每客户端样本数: {avg_samples_per_client:.1f} ± {std_samples_per_client:.1f}")
        print(f"  样本数范围: [{np.min(np.sum(counts, axis=1))}, {np.max(np.sum(counts, axis=1))}]")
    
    # 分布一致性检查
    print(f"\n🔍 一致性检查:")
    
    # 1. 总样本数对比
    total_diff = abs(np.sum(standard_counts) - np.sum(custom_counts))
    print(f"总样本数差异: {total_diff}")
    
    # 2. 客户端样本数分布对比
    standard_client_totals = np.sum(standard_counts, axis=1)
    custom_client_totals = np.sum(custom_counts, axis=1)
    
    # 统计检验 (Kolmogorov-Smirnov test)
    try:
        from scipy import stats
        ks_stat, ks_pvalue = stats.ks_2samp(standard_client_totals, custom_client_totals)
        print(f"客户端样本数分布KS检验: 统计量={ks_stat:.4f}, p值={ks_pvalue:.4f}")
        
        if ks_pvalue > 0.05:
            print("✅ 客户端样本数分布无显著差异")
        else:
            print("❌ 客户端样本数分布存在显著差异")
    except ImportError:
        print("⚠️ scipy未安装，跳过统计检验")
    
    # 3. 类别分布对比
    print(f"\n📈 类别分布对比:")
    
    for method_idx, (counts, name) in enumerate(zip([standard_counts, custom_counts], method_names)):
        # 计算每个类别的总样本数
        class_totals = np.sum(counts, axis=0)
        class_proportions = class_totals / np.sum(class_totals)
        
        print(f"\n{name} - 各类别样本比例:")
        for class_id in range(len(class_totals)):
            print(f"  类别{class_id}: {class_proportions[class_id]:.3f} ({class_totals[class_id]}样本)")
    
    # 4. 异构程度对比
    print(f"\n🎯 异构程度对比:")
    
    for counts, name in zip([standard_counts, custom_counts], method_names):
        # 计算每个客户端的最大类别占比
        max_ratios = []
        effective_classes = []
        
        for client_id in range(len(counts)):
            client_total = np.sum(counts[client_id])
            if client_total > 0:
                max_ratio = np.max(counts[client_id]) / client_total
                effective = np.sum(counts[client_id] > client_total * 0.05)
            else:
                max_ratio = 0
                effective = 0
            
            max_ratios.append(max_ratio)
            effective_classes.append(effective)
        
        avg_max_ratio = np.mean(max_ratios)
        avg_effective = np.mean(effective_classes)
        
        print(f"{name}:")
        print(f"  平均最大类别占比: {avg_max_ratio:.3f}")
        print(f"  平均有效类别数: {avg_effective:.1f}")
    
    return {
        'total_diff': total_diff,
        'standard_totals': standard_client_totals,
        'custom_totals': custom_client_totals
    }


def generate_consistency_report(comparison_results):
    """生成一致性报告"""
    print(f"\n📋 一致性评估报告:")
    print("=" * 50)
    
    total_diff = comparison_results['total_diff']
    standard_totals = comparison_results['standard_totals']
    custom_totals = comparison_results['custom_totals']
    
    # 总体一致性评分
    consistency_score = 0
    max_score = 100
    
    # 1. 总样本数一致性 (30分)
    if total_diff == 0:
        consistency_score += 30
        print("✅ 总样本数完全一致 (+30分)")
    elif total_diff < 1000:
        score = max(0, 30 - (total_diff / 100))
        consistency_score += score
        print(f"⚠️ 总样本数基本一致 (+{score:.1f}分)")
    else:
        print("❌ 总样本数差异较大 (+0分)")
    
    # 2. 客户端样本数分布一致性 (40分)
    std_diff = abs(np.std(standard_totals) - np.std(custom_totals))
    mean_diff = abs(np.mean(standard_totals) - np.mean(custom_totals))
    
    if std_diff < 10 and mean_diff < 10:
        consistency_score += 40
        print("✅ 客户端样本数分布高度一致 (+40分)")
    elif std_diff < 50 and mean_diff < 50:
        score = max(0, 40 - (std_diff + mean_diff) / 5)
        consistency_score += score
        print(f"⚠️ 客户端样本数分布基本一致 (+{score:.1f}分)")
    else:
        print("❌ 客户端样本数分布差异较大 (+0分)")
    
    # 3. 随机性一致性 (30分)
    # 通过相关性检验
    try:
        correlation = np.corrcoef(standard_totals, custom_totals)[0, 1]
        if correlation > 0.9:
            consistency_score += 30
            print(f"✅ 随机性高度相关 (r={correlation:.3f}) (+30分)")
        elif correlation > 0.5:
            score = (correlation - 0.5) / 0.4 * 30
            consistency_score += score
            print(f"⚠️ 随机性中度相关 (r={correlation:.3f}) (+{score:.1f}分)")
        else:
            print(f"❌ 随机性相关性低 (r={correlation:.3f}) (+0分)")
    except:
        print("⚠️ 无法计算相关性")
    
    # 最终评估
    print(f"\n🎯 最终一致性评分: {consistency_score:.1f}/{max_score}")
    
    if consistency_score >= 90:
        print("🎉 数据分配高度一致！可以进行公平对比")
        return "EXCELLENT"
    elif consistency_score >= 70:
        print("✅ 数据分配基本一致，对比结果可信")
        return "GOOD"
    elif consistency_score >= 50:
        print("⚠️ 数据分配存在一定差异，需要注意")
        return "FAIR"
    else:
        print("❌ 数据分配差异较大，对比结果可能不公平")
        return "POOR"


def main():
    """主函数"""
    print("🔍 ReFedScaFL vs FedAS 数据分配一致性验证")
    print("=" * 60)
    
    # 配置参数
    config = {
        'num_clients': 100,
        'num_classes': 10,
        'concentration': 0.1,
        'partition_size': 300,
        'random_seed': 1
    }
    
    print(f"验证配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 模拟标准Plato采样
    standard_dist, standard_counts = simulate_standard_noniid_sampling(**config)
    
    # 模拟ReFedScaFL自定义采样
    custom_dist, custom_counts = simulate_refedscafl_custom_sampling(
        num_clients=config['num_clients'],
        num_classes=config['num_classes'],
        concentration=config['concentration'],
        min_size=300,
        max_size=1000,
        random_seed=config['random_seed']
    )
    
    # 对比分析
    method_names = ["标准Plato noniid采样", "ReFedScaFL自定义采样"]
    comparison_results = compare_distributions(standard_counts, custom_counts, method_names)
    
    # 生成报告
    consistency_level = generate_consistency_report(comparison_results)
    
    # 建议
    print(f"\n💡 建议:")
    if consistency_level in ["EXCELLENT", "GOOD"]:
        print("✅ 当前配置下数据分配足够一致，可以进行公平对比")
        print("✅ 实验结果将具有很好的可信度")
    elif consistency_level == "FAIR":
        print("⚠️ 建议使用标准Plato采样器确保完全一致")
        print("⚠️ 或者在结果分析时考虑数据分配差异的影响")
    else:
        print("❌ 强烈建议修改配置，使用相同的数据分配逻辑")
        print("❌ 当前差异可能严重影响对比结果的公平性")
    
    print(f"\n🎯 结论:")
    print("为确保最公平的对比，建议ReFedScaFL使用标准Plato noniid采样器")


if __name__ == "__main__":
    main()
