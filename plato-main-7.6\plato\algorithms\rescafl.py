"""
ReSCAFL (Reputation-based Stale-Synchronous Parallel Communication-efficient Asynchronous Federated Learning) algorithm.
"""

from plato.algorithms import fedavg
import torch

class Algorithm(fedavg.Algorithm):
    """The ReSCAFL algorithm for asynchronous federated learning."""
    
    def compute_model_difference(self, local_weights, global_weights):
        """计算模型差异"""
        dot_product = 0
        global_norm_square = 0
        
        for name in global_weights.keys():
            local_w = local_weights[name]
            global_w = global_weights[name]
            
            dot_product += torch.sum(local_w * global_w)
            global_norm_square += torch.sum(global_w * global_w)
            
        lambda_k = (dot_product / global_norm_square) - 1
        return lambda_k 