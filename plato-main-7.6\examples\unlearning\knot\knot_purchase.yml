general:
    # The prefix of directories of dataset, models, checkpoints, and results
    base_path: .

clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 10

    # The number of clients selected in each round
    per_round: 3

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 30

    # Should clients really go to sleep, or should we just simulate the sleep times? The sleep_simulation is true only when speed_simulation is true
    sleep_simulation: true

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 20

    random_seed: 1

    # The round to remove the local data by clients
    data_deletion_round: 2

    # The clients which need to delete their local data samples
    clients_requesting_deletion: [2, 3, 6]

    # The percentage to delete the local data by clients
    deleted_data_ratio: 0.1

    # What is the minimum number of clients that need to report before aggregation begins?
    # Shoule smaller than clients per_round
    #minimum_clients_aggregated: 10

server:
    address: 127.0.0.1
    port: 8080
    simulate_wall_time: true

    # The total number of clusters
    clusters: 5

    # Should we do the cluster accuracy test during learning?
    do_test: true

    do_clustered_test: true

    #
    window_size: 2

    # Should be false. Specified in knot
    do_global_test: false

data:
    # The training and testing dataset
    datasource: Purchase

    # Number of samples in each partition
    partition_size: 2000

    # IID or non-IID?
    sampler: noniid

    test_sampler: noniid

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic
    # type: diff_privacy
    # dp_epsilon: 2
    # dp_delta: 0.00001
    # dp_max_grad_norm: 1

    # The maximum number of training rounds
    rounds: 100

    # The maximum number of clients running concurrently
    max_concurrency: 2

    # The target accuracy
    target_accuracy: 0.6
    target_accuracy_count: 5

    target_accuracy_std: 0.01

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD
    learning_rate: 0.01
    momentum: 0.9
    weight_decay: 0.0

    # The machine learning model
    model_name: multilayer
    input_dim: 600
    num_classes: 100

algorithm:
    # Aggregation algorithm
    type: fedavg

results:
    # Write the following parameter(s) into a CSV
    types: round, accuracy, clusters_accuracy, elapsed_time

    # Plot results (x_axis-y_axis)
    plot: round-accuracy, elapsed_time-accuracy
