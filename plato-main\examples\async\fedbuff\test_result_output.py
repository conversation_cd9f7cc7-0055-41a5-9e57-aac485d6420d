#!/usr/bin/env python3
"""
测试FedBuff结果输出功能
"""

import os
import sys
import logging

# 添加plato路径
sys.path.append('../../../')

from plato.config import Config

def test_fedbuff_result_output():
    """测试FedBuff结果输出"""
    print("🧪 测试FedBuff结果输出功能")
    print("=" * 50)
    
    try:
        # 加载配置
        config_file = "fedbuff_MNIST_network_test.yml"
        Config.load_config(config_file)
        print(f"✅ 配置加载成功: {config_file}")
        
        # 检查配置
        result_path = Config().params.get("result_path")
        result_types = Config().params.get("result_types")
        
        print(f"📁 结果路径: {result_path}")
        print(f"📊 结果类型: {result_types}")
        
        # 确保目录存在
        os.makedirs(result_path, exist_ok=True)
        print(f"✅ 目录已创建/存在: {result_path}")
        
        # 导入FedBuff服务器
        from fedbuff_server import Server
        
        # 创建服务器实例（仅用于测试结果输出）
        print(f"🔧 创建FedBuff服务器实例...")
        server = Server()
        
        # 检查自定义文件是否设置成功
        if hasattr(server, 'custom_result_file') and server.custom_result_file:
            print(f"✅ 自定义结果文件设置成功: {server.custom_result_file}")
            
            # 检查文件是否存在
            if os.path.exists(server.custom_result_file):
                print(f"✅ 结果文件已创建")
                
                # 读取文件内容
                with open(server.custom_result_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"📄 文件内容:")
                    print(content)
            else:
                print(f"❌ 结果文件不存在")
        else:
            print(f"❌ 自定义结果文件设置失败")
        
        # 模拟一轮结果写入
        print(f"\n🔄 模拟结果写入...")
        
        # 设置一些测试数据
        server.current_round = 1
        server.accuracy = 0.75
        server.wall_time = 100.0
        server.initial_wall_time = 0.0
        
        # 模拟网络统计
        if hasattr(server, 'network_stats'):
            server.network_stats.update({
                'network_success_rate': 0.85,
                'avg_communication_time': 2.5
            })
        
        # 模拟陈旧度统计
        if hasattr(server, 'staleness_stats'):
            server.staleness_stats.update({
                'avg_staleness': 2.3,
                'max_staleness': 5,
                'min_staleness': 1
            })
        
        # 调用clients_processed方法
        server.clients_processed()
        
        print(f"🎉 测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_existing_results():
    """检查现有的结果文件"""
    print(f"\n🔍 检查现有结果文件")
    print("-" * 30)
    
    result_dirs = [
        "results/mnist_network_test_fedbuff/01",
        "results/mnist_standard_fedbuff/01",
        "results/mnist_original_fedbuff/01"
    ]
    
    for result_dir in result_dirs:
        print(f"\n📂 检查目录: {result_dir}")
        if os.path.exists(result_dir):
            files = os.listdir(result_dir)
            csv_files = [f for f in files if f.endswith('.csv')]
            
            if csv_files:
                print(f"✅ 找到CSV文件: {csv_files}")
                for csv_file in csv_files:
                    filepath = os.path.join(result_dir, csv_file)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            print(f"   📄 {csv_file}: {len(lines)} 行")
                            if len(lines) > 0:
                                print(f"   📋 表头: {lines[0].strip()}")
                            if len(lines) > 1:
                                print(f"   📊 最后一行: {lines[-1].strip()}")
                    except Exception as e:
                        print(f"   ❌ 读取失败: {e}")
            else:
                print(f"❌ 没有找到CSV文件")
        else:
            print(f"❌ 目录不存在")

def main():
    """主函数"""
    print("🚀 FedBuff结果输出测试")
    print("=" * 60)
    
    # 检查现有结果
    check_existing_results()
    
    # 测试结果输出功能
    success = test_fedbuff_result_output()
    
    if success:
        print(f"\n🎉 测试成功！")
        print("✅ 结果输出功能正常")
    else:
        print(f"\n❌ 测试失败")
        print("🔧 需要进一步调试")
    
    # 再次检查结果
    check_existing_results()

if __name__ == "__main__":
    main()
