clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 5

    # Should the clients compute test accuracy locally?
    do_test: false

server:
    address: 127.0.0.1
    port: 8080
    simulate_wall_time: true

data:
    # The training and testing dataset
    datasource: Purchase

    # Number of samples in each partition
    partition_size: 2000

    # IID or non-IID?
    sampler: noniid

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic
    # type: diff_privacy
    # dp_epsilon: 2
    # dp_delta: 0.00001
    # dp_max_grad_norm: 1

    # The maximum number of training rounds
    rounds: 100

    # The maximum number of clients running concurrently
    max_concurrency: 10

    # The target accuracy
    target_accuracy: 0.80

    # The machine learning model
    model_type: general_multilayer
    model_name: customized_mlp

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    model:
        input_dim: 600
        output_dim: 100
        hidden_layers_dim:
          - 1024
          - 512
          - 256
          - 128
        batch_norms:
          - null
          - null
          - null
          - null
          - null
        activations:
          - tanh
          - tanh
          - tanh
          - tanh
          - null
        dropout_ratios:
          - 0.0
          - 0.0
          - 0.0
          - 0.0
          - 0.0

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

results:
    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy
