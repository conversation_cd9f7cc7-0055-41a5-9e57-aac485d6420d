#!/usr/bin/env python3
"""
测试优化后的SC-AFL配置
解决ResNet-9内存压力和客户端重启问题
"""

import os
import sys
import time
import logging
import subprocess
import psutil
from datetime import datetime

def setup_logging():
    """设置日志"""
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    logging.basicConfig(level=logging.INFO, format=log_format)
    return logging.getLogger(__name__)

def check_system_resources():
    """检查系统资源"""
    logger = logging.getLogger(__name__)
    
    # 检查内存
    memory = psutil.virtual_memory()
    logger.info(f"💾 系统内存: 总计 {memory.total / (1024**3):.1f}GB, 可用 {memory.available / (1024**3):.1f}GB, 使用率 {memory.percent}%")
    
    # 检查CPU
    cpu_percent = psutil.cpu_percent(interval=1)
    logger.info(f"🖥️ CPU使用率: {cpu_percent}%")
    
    # 检查磁盘空间
    disk = psutil.disk_usage('.')
    logger.info(f"💿 磁盘空间: 总计 {disk.total / (1024**3):.1f}GB, 可用 {disk.free / (1024**3):.1f}GB, 使用率 {(disk.used / disk.total) * 100:.1f}%")
    
    return memory.available > 2 * (1024**3)  # 至少需要2GB可用内存

def test_configuration():
    """测试配置文件"""
    logger = logging.getLogger(__name__)
    
    config_file = "sc_afl_cifar10_resnet9_with_network.yml"
    if not os.path.exists(config_file):
        logger.error(f"❌ 配置文件不存在: {config_file}")
        return False
    
    logger.info(f"✅ 配置文件存在: {config_file}")
    
    # 读取配置内容
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
    # 检查关键配置
    key_configs = {
        'total_clients: 6': '客户端数量',
        'per_round: 3': '每轮客户端数',
        'max_concurrency: 1': '最大并发数',
        'rounds: 10': '训练轮数',
        'epochs: 2': '本地训练轮数',
        'model_name: resnet_9': '模型名称'
    }
    
    for config, desc in key_configs.items():
        if config in content:
            logger.info(f"✅ {desc}: {config}")
        else:
            logger.warning(f"⚠️ {desc}配置可能不正确")
    
    return True

def run_scafl_experiment():
    """运行SC-AFL实验"""
    logger = logging.getLogger(__name__)
    
    # 检查系统资源
    if not check_system_resources():
        logger.warning("⚠️ 系统可用内存不足2GB，可能影响实验运行")
    
    # 检查配置
    if not test_configuration():
        return False
    
    logger.info("🚀 开始运行优化后的SC-AFL实验...")
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 运行实验
        cmd = [sys.executable, "sc_afl.py", "-c", "sc_afl_cifar10_resnet9_with_network"]
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 实时输出日志
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
                output_lines.append(output.strip())
                
                # 检查关键信息
                if "训练完成" in output:
                    logger.info("🎉 检测到训练完成信息")
                elif "ERROR" in output or "Exception" in output:
                    logger.error(f"❌ 检测到错误: {output.strip()}")
                elif "聚合完成" in output:
                    logger.info("✅ 检测到聚合完成")
        
        # 等待进程结束
        return_code = process.poll()
        
        # 记录结束时间
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"⏱️ 实验运行时间: {duration:.2f}秒")
        logger.info(f"🔚 进程返回码: {return_code}")
        
        # 检查日志文件
        logs_dir = "logs"
        if os.path.exists(logs_dir):
            log_files = [f for f in os.listdir(logs_dir) if f.startswith("sc_afl_server_")]
            if log_files:
                latest_log = max(log_files, key=lambda x: os.path.getctime(os.path.join(logs_dir, x)))
                logger.info(f"📋 最新日志文件: {os.path.join(logs_dir, latest_log)}")
                
                # 检查日志文件大小
                log_path = os.path.join(logs_dir, latest_log)
                log_size = os.path.getsize(log_path)
                logger.info(f"📊 日志文件大小: {log_size} 字节")
                
                if log_size > 1000:  # 如果日志文件大于1KB，说明有实质性内容
                    logger.info("✅ 日志文件包含实质性内容，实验可能成功运行")
                else:
                    logger.warning("⚠️ 日志文件较小，可能实验运行时间很短")
        
        return return_code == 0
        
    except Exception as e:
        logger.error(f"❌ 运行实验时发生异常: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🔧 SC-AFL优化配置测试")
    logger.info("=" * 50)
    
    # 显示优化内容
    optimizations = [
        "📉 客户端数量: 10 → 6",
        "📉 每轮客户端: 5 → 3", 
        "📉 最大并发: 2 → 1",
        "📉 训练轮数: 20 → 10",
        "📉 最大聚合客户端: 5 → 3",
        "🎯 目标: 解决ResNet-9内存压力和客户端重启问题"
    ]
    
    for opt in optimizations:
        logger.info(opt)
    
    logger.info("=" * 50)
    
    # 运行测试
    success = run_scafl_experiment()
    
    if success:
        logger.info("🎉 优化配置测试成功！")
    else:
        logger.error("❌ 优化配置测试失败，需要进一步调整")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
