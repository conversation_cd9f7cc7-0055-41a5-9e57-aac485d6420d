"""
A federated learning client at the edge server in a cross-silo training workload.
"""

import time
from types import SimpleNamespace

from plato.clients import simple
from plato.config import Config
from plato.processors import registry as processor_registry


class Client(simple.Client):
    """A federated learning client at the edge server in a cross-silo training workload."""
    #  这个类是一个联邦学习客户端，它是在一个跨域训练工作负载中运行在边缘服务器上的。
    #  它继承自simple.Client类，它是一个简单的客户端，它没有本地数据，而是从中央服务器加载模型。
    #  比如configure()，它用于准备这个边缘客户端进行训练。
    #  比如_load_payload()，它用于从中央服务器加载模型。  
    #  比如_train()，它用于聚合工作负载。
    #  比如process_server_response()，它用于处理中央服务器的响应。
    #  比如customize_report()，它用于自定义报告。
    #  比如get_payload()，它用于获取负载。
    #  比如get_trainer()，它用于获取训练器。
    #  它还实现了一些额外的方法，比如get_dataloader()，它用于获取数据加载器。
    #  它还实现了一些额外的方法，比如get_testset()，它用于获取测试集。
    #  它还实现了一些额外的方法，比如get_test_dataloader()，它用于获取测试数据加载器。
    #  它还实现了一些额外的方法，比如get_dataset_size()，它用于获取数据集大小。
    #  它还实现了一些额外的方法，比如get_model()，它用于获取模型。
    #  它还实现了一些额外的方法，比如get_algorithm()，它用于获取算法。
    #  它还实现了一些额外的方法，比如get_train_samples()，它用于获取训练样本。
    #  它还实现了一些额外的方法，比如get_train_set()，它用于获取训练集。
    #  它还实现了一些额外的方法，比如get_train_loader()，它用于获取训练数据加载器。
    #  它还实现了一些额外的方法，比如get_dataloader_test()，它用于获取测试数据加载器。
    #  它还实现了一些额外的方法，比如get_train_loader_test()，它用于获取训练数据加载器。
    #  它还实现了一些额外的方法，比如get_train_loader_test()，它用于获取训练数据加载器。

    def __init__(
        self,
        server,
        model=None,
        datasource=None,
        algorithm=None,
        trainer=None,
        callbacks=None,
    ):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )
        self.server = server

    def configure(self) -> None:
        """Prepare this edge client for training."""
        super().configure()

        # Pass inbound and outbound data payloads through processors for
        # additional data processing
        self.outbound_processor, self.inbound_processor = processor_registry.get(
            "Client", client_id=self.client_id, trainer=self.server.trainer
        )

    def load_data(self) -> None:
        """The edge client does not need to train models using local data."""

    def _load_payload(self, server_payload) -> None:
        """The edge client loads the model from the central server."""
        self.server.algorithm.load_weights(server_payload)

    def process_server_response(self, server_response):
        """Additional client-specific processing on the server response."""
        if "current_global_round" in server_response:
            self.server.current_global_round = server_response["current_global_round"]

    async def _train(self):
        """The aggregation workload on an edge client."""
        training_start_time = time.perf_counter()
        # Signal edge server to select clients to start a new round of local aggregation
        self.server.new_global_round_begins.set()

        # Wait for the edge server to finish model aggregation
        await self.server.model_aggregated.wait()
        self.server.model_aggregated.clear()

        # Extract model weights and biases
        weights = self.server.algorithm.extract_weights()

        average_accuracy = self.server.average_accuracy
        accuracy = self.server.accuracy

        if (
            hasattr(Config().clients, "sleep_simulation")
            and Config().clients.sleep_simulation
        ):
            training_time = self.server.edge_training_time
            self.server.edge_training_time = 0
        else:
            training_time = time.perf_counter() - training_start_time

        comm_time = time.time()

        edge_server_comm_time = self.server.edge_comm_time
        self.server.edge_comm_time = 0

        # Generate a report for the central server
        report = SimpleNamespace(
            client_id=self.client_id,
            num_samples=self.server.total_samples,
            accuracy=accuracy,
            training_time=training_time,
            comm_time=comm_time,
            update_response=False,
            average_accuracy=average_accuracy,
            edge_server_comm_overhead=self.server.comm_overhead,
            edge_server_comm_time=edge_server_comm_time,
        )

        self._report = self.customize_report(report)

        self.server.comm_overhead = 0

        return self._report, weights
