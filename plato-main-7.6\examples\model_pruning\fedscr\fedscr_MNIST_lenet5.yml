clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 10

    # The number of clients selected in each round
    per_round: 2

    # Should the clients compute test accuracy locally?
    do_test: true

    # Threshold at which updates will be pruned
    update_threshold: 1.2

    # Is the adaptive algorithm being used?
    adaptive: true

    # Hyperparameters for the adaptive algorithm
    delta1: 1
    delta2: 1
    delta3: 1

    outbound_processors:
        - model_compress

    compute_comm_time: true

    comm_simulation: true

server:
    address: 127.0.0.1
    port: 8020
    do_test: true

    random_seed: 1

    checkpoint_path: results/test/checkpoint
    model_path: results/test/model

    inbound_processors:
        - model_decompress

data:
    # The training and testing dataset
    datasource: MNIST

    # Where the dataset is located
    data_path: ../../data

    # Number of samples in each partition
    partition_size: 100

    # IID or non-IID?
    sampler: noniid

    concentration: 5

    testset_sampler: noniid

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 3

    # The maximum number of clients running concurrently
    max_concurrency: 2

    # Number of epoches for local training in each communication round
    epochs: 2
    batch_size: 20
    optimizer: SGD

    # The machine learning model
    model_name: lenet5

algorithm:
    # Aggregation algorithm
    type: fedavg

results:
    # Write the following parameter(s) into a CSV
    types: round, accuracy, elapsed_time, comm_time, round_time, comm_overhead

    result_path: results/test

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0
