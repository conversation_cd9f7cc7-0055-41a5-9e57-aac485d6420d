# This workflow will delete old workflow runs, on a schedule or manually triggered.

name: Delete old workflow runs

on:
  workflow_dispatch:
    inputs:
      days:
        description: 'Number of days.'
        required: true
        default: 30
      minimum_runs:
        description: 'The minimum runs to keep for each workflow.'
        required: true
        default: 6

jobs:
  del_runs:
    runs-on: ubuntu-20.04
    steps:
      - name: Delete workflow runs
        uses: Mattraks/delete-workflow-runs@v2
        with:
          token: ${{ github.token }}
          repository: ${{ github.repository }}
          retain_days: ${{ github.event.inputs.days }}
          keep_minimum_runs: ${{ github.event.inputs.minimum_runs }}
