clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 1

    # Do client side validation.
    do_test: false 

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8011
    random_seed: 1

    do_test: true 

data:
    # The training and testing dataset
    datasource: YOLOv8
    data_params: coco128.yaml

    # number of training examples
    num_train_examples: 128

    # number of testing examples
    num_test_examples: 128

    # image size
    image_size: 640

    # class names
    classes:
        [
            "person",
            "bicycle",
            "car",
            "motorcycle",
            "airplane",
            "bus",
            "train",
            "truck",
            "boat",
            "traffic light",
            "fire hydrant",
            "stop sign",
            "parking meter",
            "bench",
            "bird",
            "cat",
            "dog",
            "horse",
            "sheep",
            "cow",
            "elephant",
            "bear",
            "zebra",
            "giraffe",
            "backpack",
            "umbrella",
            "handbag",
            "tie",
            "suitcase",
            "frisbee",
            "skis",
            "snowboard",
            "sports ball",
            "kite",
            "baseball bat",
            "baseball glove",
            "skateboard",
            "surfboard",
            "tennis racket",
            "bottle",
            "wine glass",
            "cup",
            "fork",
            "knife",
            "spoon",
            "bowl",
            "banana",
            "apple",
            "sandwich",
            "orange",
            "broccoli",
            "carrot",
            "hot dog",
            "pizza",
            "donut",
            "cake",
            "chair",
            "couch",
            "potted plant",
            "bed",
            "dining table",
            "toilet",
            "tv",
            "laptop",
            "mouse",
            "remote",
            "keyboard",
            "cell phone",
            "microwave",
            "oven",
            "toaster",
            "sink",
            "refrigerator",
            "book",
            "clock",
            "vase",
            "scissors",
            "teddy bear",
            "hair drier",
            "toothbrush",
        ]

    # Number of samples in each partition
    partition_size: 128 

    # IID or non-IID?
    sampler: iid
    testset_sampler: iid

trainer:
    # The type of the trainer
    type: yolov8
    batch_size: 32

    # test function
    testtype: map

    # The maximum number of training rounds
    rounds: 60

    # The maximum number of clients running concurrently
    max_concurrency: 10

    # The target accuracy
    target_accuracy: 0.99

    # Number of epoches for local training in each communication round
    epochs: 1

    # The machine learning model
    model_name: yolov8

parameters:
    grid_size: 32

    model:
        type: yolov8n.pt
        cfg: yolov8n.yaml
        num_classes: 80

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

algorithm:
    # Aggregation algorithm
    type: fedavg

results:
    result_path: results/yolov8
    types: round, accuracy, elapsed_time

