clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

    # Whether client-server communication should be simulated with files
    comm_simulation: true

server:
    address: 127.0.0.1
    port: 8020

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition
    partition_size: 50000

    # IID or non-IID?
    sampler: iid

trainer:
    # Using the differential privacy trainer
    type: diff_privacy
    dp_epsilon: 10
    dp_delta: 0.00001
    dp_max_grad_norm: 1

    # The maximum number of training rounds
    rounds: 100

    # The maximum number of clients running concurrently
    max_concurrency: 1

    # The target accuracy
    target_accuracy: 0.80

    # The machine learning model
    model_name: resnet_18

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 32
    optimizer: SGD
    lr_scheduler: LambdaLR

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy
