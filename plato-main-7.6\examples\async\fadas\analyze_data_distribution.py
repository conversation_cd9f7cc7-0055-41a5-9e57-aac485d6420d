#!/usr/bin/env python3
"""
FedAS数据异构程度分析工具
分析Dirichlet浓度0.1下的客户端数据分布情况

功能：
1. 模拟Dirichlet分布的数据分配
2. 计算各种异构程度指标
3. 可视化数据分布
4. 对比不同浓度参数的效果
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import os


class DataDistributionAnalyzer:
    """数据分布分析器"""
    
    def __init__(self, num_clients=10, num_classes=10, total_samples=60000):
        self.num_clients = num_clients
        self.num_classes = num_classes
        self.total_samples = total_samples
        self.samples_per_client = total_samples // num_clients
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def generate_dirichlet_distribution(self, concentration=0.1, random_seed=1):
        """
        生成Dirichlet分布的数据分配
        
        Args:
            concentration: Dirichlet分布的浓度参数
            random_seed: 随机种子
            
        Returns:
            client_distributions: 每个客户端的类别分布
            client_samples: 每个客户端每个类别的样本数
        """
        np.random.seed(random_seed)
        
        # 为每个客户端生成Dirichlet分布
        client_distributions = []
        client_samples = []
        
        for client_id in range(self.num_clients):
            # 生成Dirichlet分布的概率
            alpha = np.ones(self.num_classes) * concentration
            probs = np.random.dirichlet(alpha)
            client_distributions.append(probs)
            
            # 根据概率分配样本数量
            samples = np.random.multinomial(self.samples_per_client, probs)
            client_samples.append(samples)
        
        return np.array(client_distributions), np.array(client_samples)
    
    def calculate_heterogeneity_metrics(self, client_samples):
        """
        计算数据异构程度指标
        
        Args:
            client_samples: 每个客户端的样本分布
            
        Returns:
            metrics: 异构程度指标字典
        """
        metrics = {}
        
        # 1. KL散度 (相对于均匀分布)
        uniform_dist = np.ones(self.num_classes) / self.num_classes
        kl_divergences = []
        
        for client_id in range(self.num_clients):
            client_dist = client_samples[client_id] / client_samples[client_id].sum()
            # 避免log(0)
            client_dist = np.clip(client_dist, 1e-10, 1.0)
            kl_div = np.sum(client_dist * np.log(client_dist / uniform_dist))
            kl_divergences.append(kl_div)
        
        metrics['avg_kl_divergence'] = np.mean(kl_divergences)
        metrics['std_kl_divergence'] = np.std(kl_divergences)
        
        # 2. 基尼系数 (衡量不平衡程度)
        gini_coefficients = []
        for client_id in range(self.num_clients):
            samples = sorted(client_samples[client_id])
            n = len(samples)
            cumsum = np.cumsum(samples)
            gini = (n + 1 - 2 * np.sum(cumsum) / cumsum[-1]) / n
            gini_coefficients.append(gini)
        
        metrics['avg_gini_coefficient'] = np.mean(gini_coefficients)
        metrics['std_gini_coefficient'] = np.std(gini_coefficients)
        
        # 3. 最大类别占比
        max_class_ratios = []
        for client_id in range(self.num_clients):
            max_ratio = np.max(client_samples[client_id]) / np.sum(client_samples[client_id])
            max_class_ratios.append(max_ratio)
        
        metrics['avg_max_class_ratio'] = np.mean(max_class_ratios)
        metrics['std_max_class_ratio'] = np.std(max_class_ratios)
        
        # 4. 有效类别数 (样本数>总数5%的类别)
        effective_classes = []
        threshold = self.samples_per_client * 0.05
        
        for client_id in range(self.num_clients):
            effective = np.sum(client_samples[client_id] > threshold)
            effective_classes.append(effective)
        
        metrics['avg_effective_classes'] = np.mean(effective_classes)
        metrics['std_effective_classes'] = np.std(effective_classes)
        
        # 5. 类别覆盖率 (有样本的类别比例)
        coverage_ratios = []
        for client_id in range(self.num_clients):
            coverage = np.sum(client_samples[client_id] > 0) / self.num_classes
            coverage_ratios.append(coverage)
        
        metrics['avg_coverage_ratio'] = np.mean(coverage_ratios)
        metrics['std_coverage_ratio'] = np.std(coverage_ratios)
        
        return metrics
    
    def visualize_distribution(self, client_samples, concentration, save_path=None):
        """可视化数据分布"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'FedAS数据分布分析 (Dirichlet浓度={concentration})', fontsize=16)
        
        # 1. 热力图：客户端-类别分布
        ax1 = axes[0, 0]
        sns.heatmap(client_samples, annot=True, fmt='d', cmap='YlOrRd', 
                   xticklabels=[f'类别{i}' for i in range(self.num_classes)],
                   yticklabels=[f'客户端{i}' for i in range(self.num_clients)],
                   ax=ax1)
        ax1.set_title('客户端-类别样本分布热力图')
        
        # 2. 每个客户端的类别分布
        ax2 = axes[0, 1]
        for client_id in range(min(5, self.num_clients)):  # 只显示前5个客户端
            ax2.bar(range(self.num_classes), client_samples[client_id], 
                   alpha=0.7, label=f'客户端{client_id}')
        ax2.set_xlabel('类别')
        ax2.set_ylabel('样本数')
        ax2.set_title('各客户端类别分布对比')
        ax2.legend()
        
        # 3. 最大类别占比分布
        ax3 = axes[1, 0]
        max_ratios = [np.max(client_samples[i]) / np.sum(client_samples[i]) 
                     for i in range(self.num_clients)]
        ax3.hist(max_ratios, bins=10, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.set_xlabel('最大类别占比')
        ax3.set_ylabel('客户端数量')
        ax3.set_title('最大类别占比分布')
        ax3.axvline(np.mean(max_ratios), color='red', linestyle='--', 
                   label=f'平均值: {np.mean(max_ratios):.3f}')
        ax3.legend()
        
        # 4. 有效类别数分布
        ax4 = axes[1, 1]
        threshold = self.samples_per_client * 0.05
        effective_classes = [np.sum(client_samples[i] > threshold) 
                           for i in range(self.num_clients)]
        ax4.hist(effective_classes, bins=range(self.num_classes + 2), 
                alpha=0.7, color='lightgreen', edgecolor='black')
        ax4.set_xlabel('有效类别数 (>5%样本)')
        ax4.set_ylabel('客户端数量')
        ax4.set_title('有效类别数分布')
        ax4.axvline(np.mean(effective_classes), color='red', linestyle='--',
                   label=f'平均值: {np.mean(effective_classes):.1f}')
        ax4.legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")
        
        plt.show()
    
    def compare_concentrations(self, concentrations=[0.01, 0.1, 0.5, 1.0, 10.0]):
        """对比不同浓度参数的效果"""
        results = []
        
        for concentration in concentrations:
            _, client_samples = self.generate_dirichlet_distribution(concentration)
            metrics = self.calculate_heterogeneity_metrics(client_samples)
            metrics['concentration'] = concentration
            results.append(metrics)
        
        # 转换为DataFrame
        df = pd.DataFrame(results)
        
        # 可视化对比
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('不同Dirichlet浓度参数的异构程度对比', fontsize=16)
        
        metrics_to_plot = [
            ('avg_kl_divergence', 'KL散度'),
            ('avg_gini_coefficient', '基尼系数'),
            ('avg_max_class_ratio', '最大类别占比'),
            ('avg_effective_classes', '有效类别数'),
            ('avg_coverage_ratio', '类别覆盖率')
        ]
        
        for i, (metric, title) in enumerate(metrics_to_plot):
            ax = axes[i // 3, i % 3]
            ax.plot(df['concentration'], df[metric], 'o-', linewidth=2, markersize=8)
            ax.set_xlabel('Dirichlet浓度参数')
            ax.set_ylabel(title)
            ax.set_title(f'{title} vs 浓度参数')
            ax.set_xscale('log')
            ax.grid(True, alpha=0.3)
            
            # 标记concentration=0.1的点
            idx_01 = df[df['concentration'] == 0.1].index[0]
            ax.scatter(0.1, df.loc[idx_01, metric], color='red', s=100, zorder=5)
            ax.annotate(f'浓度=0.1\n{df.loc[idx_01, metric]:.3f}', 
                       xy=(0.1, df.loc[idx_01, metric]),
                       xytext=(10, 10), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        # 删除多余的子图
        if len(metrics_to_plot) < 6:
            axes[1, 2].remove()
        
        plt.tight_layout()
        plt.savefig('concentration_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return df


def main():
    """主函数"""
    print("🔍 FedAS数据异构程度分析")
    print("=" * 50)
    
    # 创建分析器 (使用与ReFedScaFL相同的配置)
    analyzer = DataDistributionAnalyzer(
        num_clients=10,  # 与您修改后的配置一致
        num_classes=10,
        total_samples=60000
    )
    
    # 分析浓度0.1的情况
    print("📊 分析Dirichlet浓度=0.1的数据分布...")
    concentration = 0.1
    client_distributions, client_samples = analyzer.generate_dirichlet_distribution(concentration)
    
    # 计算异构程度指标
    metrics = analyzer.calculate_heterogeneity_metrics(client_samples)
    
    print(f"\n📈 异构程度指标 (浓度={concentration}):")
    print("-" * 40)
    print(f"平均KL散度: {metrics['avg_kl_divergence']:.4f} ± {metrics['std_kl_divergence']:.4f}")
    print(f"平均基尼系数: {metrics['avg_gini_coefficient']:.4f} ± {metrics['std_gini_coefficient']:.4f}")
    print(f"平均最大类别占比: {metrics['avg_max_class_ratio']:.3f} ± {metrics['std_max_class_ratio']:.3f}")
    print(f"平均有效类别数: {metrics['avg_effective_classes']:.1f} ± {metrics['std_effective_classes']:.1f}")
    print(f"平均类别覆盖率: {metrics['avg_coverage_ratio']:.3f} ± {metrics['std_coverage_ratio']:.3f}")
    
    # 详细分析每个客户端
    print(f"\n👥 各客户端详细分析:")
    print("-" * 60)
    print("客户端ID | 最大类别占比 | 有效类别数 | 覆盖率 | 主要类别")
    print("-" * 60)
    
    for client_id in range(analyzer.num_clients):
        samples = client_samples[client_id]
        max_ratio = np.max(samples) / np.sum(samples)
        effective_classes = np.sum(samples > analyzer.samples_per_client * 0.05)
        coverage = np.sum(samples > 0) / analyzer.num_classes
        dominant_class = np.argmax(samples)
        
        print(f"客户端{client_id:2d}  |    {max_ratio:.3f}     |     {effective_classes}      | {coverage:.3f} |   类别{dominant_class}")
    
    # 可视化分布
    print(f"\n📊 生成可视化图表...")
    analyzer.visualize_distribution(client_samples, concentration, 
                                   save_path=f'fadas_distribution_alpha_{concentration}.png')
    
    # 对比不同浓度参数
    print(f"\n🔄 对比不同浓度参数的效果...")
    comparison_df = analyzer.compare_concentrations()
    
    # 保存详细结果
    timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存客户端分布详情
    client_df = pd.DataFrame(client_samples, 
                           columns=[f'类别{i}' for i in range(analyzer.num_classes)],
                           index=[f'客户端{i}' for i in range(analyzer.num_clients)])
    client_df.to_csv(f'fadas_client_distribution_{timestamp}.csv')
    
    # 保存对比结果
    comparison_df.to_csv(f'fadas_concentration_comparison_{timestamp}.csv', index=False)
    
    print(f"\n💾 结果已保存:")
    print(f"  - 客户端分布: fadas_client_distribution_{timestamp}.csv")
    print(f"  - 浓度对比: fadas_concentration_comparison_{timestamp}.csv")
    print(f"  - 分布图表: fadas_distribution_alpha_{concentration}.png")
    print(f"  - 对比图表: concentration_comparison.png")
    
    # 异构程度评估
    print(f"\n🎯 异构程度评估:")
    if metrics['avg_max_class_ratio'] > 0.7:
        print("✅ 高度异构 - 大部分客户端数据严重不平衡")
    elif metrics['avg_max_class_ratio'] > 0.5:
        print("⚠️ 中度异构 - 客户端数据存在明显不平衡")
    else:
        print("ℹ️ 轻度异构 - 客户端数据相对平衡")
    
    if metrics['avg_effective_classes'] < 3:
        print("✅ 强Non-IID特性 - 每个客户端只专注少数类别")
    elif metrics['avg_effective_classes'] < 6:
        print("⚠️ 中等Non-IID特性 - 客户端有一定的类别专业化")
    else:
        print("ℹ️ 弱Non-IID特性 - 客户端类别分布相对均匀")


if __name__ == "__main__":
    main()
