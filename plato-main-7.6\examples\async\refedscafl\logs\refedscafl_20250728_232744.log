[INFO][23:27:44]: 日志系统已初始化
[INFO][23:27:44]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250728_232744.log
[INFO][23:27:44]: 日志级别: INFO
[WARNING][23:27:44]: 无法获取系统信息: No module named 'psutil'
[INFO][23:27:44]: 🚀 ReFedScaFL 训练开始
[INFO][23:27:44]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][23:27:44]: 开始时间: 2025-07-28 23:27:44
[INFO][23:27:44]: [Client None] 基础初始化完成
[INFO][23:27:44]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][23:27:44]: 创建并缓存共享模型
[INFO][23:27:44]: [93m[1m[39736] Logging runtime results to: ././results/refedscafl_cifar10_resnet9/39736.csv.[0m
[INFO][23:27:44]: [Server #39736] Started training on 10 clients with 5 per round.
[INFO][23:27:44]: 服务器参数配置完成：
[INFO][23:27:44]: - 客户端数量: total=10, per_round=5
[INFO][23:27:44]: - 权重参数: success=0.8, distill=0.2
[INFO][23:27:45]: - SCAFL参数: V=1.0, tau_max=5
[INFO][23:27:45]: 从共享资源模型提取并缓存全局权重
[INFO][23:27:45]: [Server #39736] Configuring the server...
[INFO][23:27:45]: Training: 500 rounds or accuracy above 80.0%

[INFO][23:27:45]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][23:27:45]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][23:27:45]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][23:27:45]: [Trainer Init] 训练器初始化完成，参数：batch_size=64, learning_rate=0.1, epochs=3
[INFO][23:27:45]: Algorithm: fedavg
[INFO][23:27:45]: Data source: CIFAR10
[INFO][23:27:49]: Starting client #1's process.
[INFO][23:27:49]: Starting client #2's process.
[INFO][23:27:49]: Starting client #3's process.
[INFO][23:27:49]: Starting client #4's process.
[INFO][23:27:49]: Starting client #5's process.
[INFO][23:27:49]: Setting the random seed for selecting clients: 1
[INFO][23:27:49]: Starting a server at address 127.0.0.1 and port 8091.
[INFO][23:28:01]: [Server #39736] A new client just connected.
[INFO][23:28:01]: [Server #39736] A new client just connected.
[INFO][23:28:01]: [Server #39736] New client with id #5 arrived.
[INFO][23:28:01]: [Server #39736] Client process #39180 registered.
[INFO][23:28:01]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][23:28:01]: [Server #39736] New client with id #4 arrived.
[INFO][23:28:01]: [Server #39736] Client process #23852 registered.
[INFO][23:28:01]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][23:28:01]: [Server #39736] A new client just connected.
[INFO][23:28:01]: [Server #39736] A new client just connected.
[INFO][23:28:01]: [Server #39736] New client with id #2 arrived.
[INFO][23:28:01]: [Server #39736] Client process #12416 registered.
[INFO][23:28:01]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][23:28:01]: [Server #39736] New client with id #1 arrived.
[INFO][23:28:01]: [Server #39736] Client process #11036 registered.
[INFO][23:28:01]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][23:28:01]: [Server #39736] A new client just connected.
[INFO][23:28:01]: [Server #39736] New client with id #3 arrived.
[INFO][23:28:01]: [Server #39736] Client process #28088 registered.
[INFO][23:28:01]: [Server #39736] Starting training.
[INFO][23:28:01]: [93m[1m
[Server #39736] Starting round 1/500.[0m
[INFO][23:28:01]: [Server #39736] Selected clients: [3, 2, 5, 1, 4]
[INFO][23:28:01]: [Server #39736] Selecting client #3 for training.
[INFO][23:28:01]: [Server #39736] Sending the current model to client #3 (simulated).
[INFO][23:28:01]: [Server #39736] Sending 18.75 MB of payload data to client #3 (simulated).
[INFO][23:28:01]: [Server #39736] Selecting client #2 for training.
[INFO][23:28:01]: [Server #39736] Sending the current model to client #2 (simulated).
[INFO][23:28:01]: [Server #39736] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][23:28:01]: [Server #39736] Selecting client #5 for training.
[INFO][23:28:01]: [Server #39736] Sending the current model to client #5 (simulated).
[INFO][23:28:01]: [Server #39736] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][23:28:01]: [Server #39736] Selecting client #1 for training.
[INFO][23:28:01]: [Server #39736] Sending the current model to client #1 (simulated).
[INFO][23:28:01]: [Server #39736] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][23:28:01]: [Server #39736] Selecting client #4 for training.
[INFO][23:28:01]: [Server #39736] Sending the current model to client #4 (simulated).
[INFO][23:28:02]: [Server #39736] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][23:28:02]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][23:28:30]: [Server #39736] Received 18.75 MB of payload data from client #3 (simulated).
[INFO][23:28:30]: [Server #39736] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][23:28:30]: [Server #39736] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][23:28:30]: [Server #39736] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][23:28:30]: [Server #39736] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][23:28:30]: [Server #39736] Adding client #4 to the list of clients for aggregation.
[INFO][23:28:30]: [Server #39736] Adding client #5 to the list of clients for aggregation.
[INFO][23:28:30]: [Server #39736] Aggregating 2 clients in total.
[INFO][23:28:30]: [Server #39736] Updated weights have been received.
[INFO][23:28:30]: [Server #39736] Aggregating model weight deltas.
[INFO][23:28:30]: [Server #39736] Finished aggregating updated weights.
[INFO][23:28:30]: [Server #39736] Started model testing.
[INFO][23:28:40]: [Trainer.test] 测试完成 - 准确率: 18.14% (1814/10000)
[INFO][23:28:40]: [93m[1m[Server #39736] Global model accuracy: 18.14%
[0m
[INFO][23:28:40]: [Server #39736] All client reports have been processed.
[INFO][23:28:40]: [Server #39736] Saving the checkpoint to ./models/refedscafl/cifar10_resnet9/checkpoint_resnet_9_1.pth.
[INFO][23:28:40]: [Server #39736] Model saved to ./models/refedscafl/cifar10_resnet9/checkpoint_resnet_9_1.pth.
[INFO][23:28:40]: [93m[1m
[Server #39736] Starting round 2/500.[0m
[INFO][23:28:40]: [Server #39736] Selected clients: [10, 7]
[INFO][23:28:40]: [Server #39736] Selecting client #10 for training.
[INFO][23:28:40]: [Server #39736] Sending the current model to client #10 (simulated).
[INFO][23:28:40]: [Server #39736] Sending 18.75 MB of payload data to client #10 (simulated).
[INFO][23:28:40]: [Server #39736] Selecting client #7 for training.
[INFO][23:28:40]: [Server #39736] Sending the current model to client #7 (simulated).
[INFO][23:28:40]: [Server #39736] Sending 18.75 MB of payload data to client #7 (simulated).
[INFO][23:28:50]: [Server #39736] Received 18.75 MB of payload data from client #10 (simulated).
[INFO][23:28:51]: [Server #39736] Received 18.75 MB of payload data from client #7 (simulated).
[INFO][23:28:51]: [Server #39736] Adding client #3 to the list of clients for aggregation.
[INFO][23:28:51]: [Server #39736] Adding client #1 to the list of clients for aggregation.
[INFO][23:28:51]: [Server #39736] Aggregating 2 clients in total.
[INFO][23:28:51]: [Server #39736] Updated weights have been received.
[INFO][23:28:51]: [Server #39736] Aggregating model weight deltas.
[INFO][23:28:51]: [Server #39736] Finished aggregating updated weights.
[INFO][23:28:51]: [Server #39736] Started model testing.
[INFO][23:29:01]: [Trainer.test] 测试完成 - 准确率: 12.18% (1218/10000)
[INFO][23:29:01]: [93m[1m[Server #39736] Global model accuracy: 12.18%
[0m
[INFO][23:29:01]: [Server #39736] All client reports have been processed.
[INFO][23:29:01]: [Server #39736] Saving the checkpoint to ./models/refedscafl/cifar10_resnet9/checkpoint_resnet_9_2.pth.
[INFO][23:29:01]: [Server #39736] Model saved to ./models/refedscafl/cifar10_resnet9/checkpoint_resnet_9_2.pth.
[INFO][23:29:01]: [93m[1m
[Server #39736] Starting round 3/500.[0m
[INFO][23:29:01]: [Server #39736] Selected clients: [5, 8]
[INFO][23:29:01]: [Server #39736] Selecting client #5 for training.
[INFO][23:29:01]: [Server #39736] Sending the current model to client #5 (simulated).
[INFO][23:29:01]: [Server #39736] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][23:29:01]: [Server #39736] Selecting client #8 for training.
[INFO][23:29:01]: [Server #39736] Sending the current model to client #8 (simulated).
[INFO][23:29:01]: [Server #39736] Sending 18.75 MB of payload data to client #8 (simulated).
[INFO][23:29:11]: [Server #39736] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][23:29:11]: [Server #39736] Received 18.75 MB of payload data from client #8 (simulated).
[INFO][23:29:11]: [Server #39736] Adding client #2 to the list of clients for aggregation.
[INFO][23:29:11]: [Server #39736] Adding client #8 to the list of clients for aggregation.
[INFO][23:29:11]: [Server #39736] Aggregating 2 clients in total.
[INFO][23:29:11]: [Server #39736] Updated weights have been received.
[INFO][23:29:12]: [Server #39736] Aggregating model weight deltas.
[INFO][23:29:12]: [Server #39736] Finished aggregating updated weights.
[INFO][23:29:12]: [Server #39736] Started model testing.
[INFO][23:29:21]: [Trainer.test] 测试完成 - 准确率: 18.85% (1885/10000)
[INFO][23:29:21]: [93m[1m[Server #39736] Global model accuracy: 18.85%
[0m
[INFO][23:29:21]: [Server #39736] All client reports have been processed.
[INFO][23:29:21]: [Server #39736] Saving the checkpoint to ./models/refedscafl/cifar10_resnet9/checkpoint_resnet_9_3.pth.
[INFO][23:29:21]: [Server #39736] Model saved to ./models/refedscafl/cifar10_resnet9/checkpoint_resnet_9_3.pth.
[INFO][23:29:21]: [93m[1m
[Server #39736] Starting round 4/500.[0m
[INFO][23:29:21]: [Server #39736] Selected clients: [4, 2]
[INFO][23:29:21]: [Server #39736] Selecting client #4 for training.
[INFO][23:29:21]: [Server #39736] Sending the current model to client #4 (simulated).
[INFO][23:29:21]: [Server #39736] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][23:29:21]: [Server #39736] Selecting client #2 for training.
[INFO][23:29:21]: [Server #39736] Sending the current model to client #2 (simulated).
[INFO][23:29:21]: [Server #39736] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][23:29:31]: [Server #39736] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][23:29:31]: [Server #39736] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][23:29:31]: [Server #39736] Adding client #5 to the list of clients for aggregation.
[INFO][23:29:31]: [Server #39736] Adding client #7 to the list of clients for aggregation.
[INFO][23:29:31]: [Server #39736] Aggregating 2 clients in total.
[INFO][23:29:31]: [Server #39736] Updated weights have been received.
[INFO][23:29:31]: [Server #39736] Aggregating model weight deltas.
[INFO][23:29:31]: [Server #39736] Finished aggregating updated weights.
[INFO][23:29:31]: [Server #39736] Started model testing.
[INFO][23:29:41]: [Trainer.test] 测试完成 - 准确率: 9.93% (993/10000)
[INFO][23:29:41]: [93m[1m[Server #39736] Global model accuracy: 9.93%
[0m
[INFO][23:29:41]: [Server #39736] All client reports have been processed.
[INFO][23:29:41]: [Server #39736] Saving the checkpoint to ./models/refedscafl/cifar10_resnet9/checkpoint_resnet_9_4.pth.
[INFO][23:29:41]: [Server #39736] Model saved to ./models/refedscafl/cifar10_resnet9/checkpoint_resnet_9_4.pth.
[INFO][23:29:41]: [93m[1m
[Server #39736] Starting round 5/500.[0m
[INFO][23:29:41]: [Server #39736] Selected clients: [1, 6]
[INFO][23:29:41]: [Server #39736] Selecting client #1 for training.
[INFO][23:29:41]: [Server #39736] Sending the current model to client #1 (simulated).
[INFO][23:29:41]: [Server #39736] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][23:29:41]: [Server #39736] Selecting client #6 for training.
[INFO][23:29:41]: [Server #39736] Sending the current model to client #6 (simulated).
[INFO][23:29:41]: [Server #39736] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][23:29:50]: [Server #39736] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][23:29:51]: [Server #39736] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][23:29:51]: [Server #39736] Requesting urgent model update from client #10.
[INFO][23:34:22]: [Server #39736] An existing client just disconnected.
[WARNING][23:34:22]: [Server #39736] Client process #39180 disconnected and removed from this server, 4 client processes are remaining.
[WARNING][23:34:22]: [93m[1m[Server #39736] Closing the server due to a failed client.[0m
[INFO][23:34:22]: [Server #39736] Training concluded.
[INFO][23:34:22]: [Server #39736] Model saved to ./models/refedscafl/cifar10_resnet9/resnet_9.pth.
[INFO][23:34:22]: [Server #39736] Closing the server.
[INFO][23:34:22]: Closing the connection to client #23852.
[INFO][23:34:22]: Closing the connection to client #12416.
[INFO][23:34:22]: Closing the connection to client #11036.
[INFO][23:34:22]: Closing the connection to client #28088.
