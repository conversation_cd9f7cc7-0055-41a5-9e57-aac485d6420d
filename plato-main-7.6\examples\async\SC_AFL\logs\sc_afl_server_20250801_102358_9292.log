2025-08-01 10:23:58,551 - INFO - 🚀 SC-AFL服务器启动 - 2025-08-01 10:23:58
2025-08-01 10:23:58,552 - INFO - ✅ 新日志文件已创建
2025-08-01 10:23:58,552 - INFO - 📁 日志目录: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs
2025-08-01 10:23:58,552 - INFO - 📄 日志文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs\sc_afl_server_20250801_102358_9292.log
2025-08-01 10:23:58,552 - INFO - 🔧 日志级别: DEBUG (文件), INFO (控制台)
2025-08-01 10:23:58,552 - INFO - 📊 文件模式: 新建模式 (每次启动创建新文件)
2025-08-01 10:23:58,553 - INFO - 🆔 进程ID: 9292
2025-08-01 10:23:58,553 - INFO - ✅ 日志文件创建成功，当前大小: 673 字节
2025-08-01 10:23:58,553 - INFO - 🚀 SC-AFL服务器初始化开始...
2025-08-01 10:23:58,553 - INFO - 📅 初始化时间: 2025-08-01 10:23:58
2025-08-01 10:23:58,565 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:23:58,565 - INFO - Server: 动态创建模型 lenet5，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 10:23:58,565 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:23:58,575 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:23:58,576 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:23:58,576 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:23:58,576 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:23:58,576 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:23:58,576 - INFO - [Trainer None] 初始化完成
2025-08-01 10:23:58,577 - INFO - Server: 创建了新的Trainer实例
2025-08-01 10:23:58,577 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:23:58,577 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:23:58,577 - INFO - [Algorithm] 初始化完成
2025-08-01 10:23:58,577 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:23:58,577 - INFO - Server: 创建了新的Algorithm实例
2025-08-01 10:23:58,578 - INFO - [93m[1m[9292] Logging runtime results to: ./results/cifar10_with_network/9292.csv.[0m
2025-08-01 10:23:58,578 - INFO - [Server #9292] Started training on 10 clients with 3 per round.
2025-08-01 10:23:58,578 - INFO - [DEBUG] 从配置文件读取 simulate_wall_time=True
2025-08-01 10:23:58,578 - WARNING - Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。
2025-08-01 10:23:58,578 - WARNING - Server: 训练器在初始化后为None，重新创建
2025-08-01 10:23:58,578 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:23:58,579 - WARNING - [Trainer None] 模型为None，尝试创建默认模型
2025-08-01 10:23:58,579 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:23:58,580 - INFO - [Trainer None] 动态创建模型 lenet5，输入通道: 3, 类别数: 10
2025-08-01 10:23:58,581 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:23:58,581 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:23:58,581 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:23:58,581 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:23:58,581 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:23:58,581 - INFO - [Trainer None] 初始化完成
2025-08-01 10:23:58,581 - INFO - Server: 重新创建了Trainer实例
2025-08-01 10:23:58,583 - INFO - [Algorithm] 已设置服务器引用 (客户端ID: None)
2025-08-01 10:23:58,583 - INFO - Server: 算法类已设置服务器引用
2025-08-01 10:23:58,583 - INFO - 动态加载数据集: CIFAR10
2025-08-01 10:23:59,208 - INFO - ✅ 成功加载数据集 CIFAR10: 10000 样本
2025-08-01 10:23:59,208 - INFO - ✅ 动态加载测试集成功: CIFAR10, 大小: 10000
2025-08-01 10:23:59,209 - INFO - ✅ 测试加载器已创建
2025-08-01 10:23:59,209 - INFO - 开始初始化全局模型权重
2025-08-01 10:23:59,209 - WARNING - 全局模型实例为None，正在尝试重新创建...
2025-08-01 10:23:59,210 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:23:59,210 - INFO - 成功重新创建了全局模型实例 lenet5，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 10:23:59,232 - INFO - [全局权重摘要] 参数数量: 10, 均值: -0.000145, 最大: 0.115070, 最小: -0.115316
2025-08-01 10:23:59,233 - INFO - [全局模型] 输入通道数: 3
2025-08-01 10:23:59,233 - INFO - 全局模型权重初始化成功
2025-08-01 10:23:59,234 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:23:59,250 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 10:23:59,250 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 10:23:59,251 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 10:23:59,251 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_102359.csv
2025-08-01 10:23:59,251 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 10:23:59,251 - WARNING - 网络模拟器初始化失败: 'Config' object has no attribute 'get'
2025-08-01 10:23:59,251 - INFO - SC-AFL算法参数: tau_max=5, V=1.0
2025-08-01 10:23:59,251 - INFO - 服务器初始化完成
2025-08-01 10:23:59,252 - INFO - 已创建并注册 0 个客户端（将在 Server.start() 中启动任务）
2025-08-01 10:23:59,252 - INFO - 客户端ID管理器初始化完成，总客户端数: 10, ID起始值: 1
2025-08-01 10:23:59,252 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 10:23:59,252 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 10:23:59,253 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 10:23:59,253 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_102359.csv
2025-08-01 10:23:59,253 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 10:23:59,254 - INFO - 服务器实例创建成功
2025-08-01 10:23:59,254 - INFO - 正在创建和注册 10 个客户端...
2025-08-01 10:23:59,254 - INFO - 客户端ID配置: 起始ID=1, 总数=10
2025-08-01 10:23:59,254 - INFO - 开始创建客户端 1...
2025-08-01 10:23:59,254 - INFO - 初始化客户端, ID: 1
2025-08-01 10:23:59,255 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:23:59,255 - INFO - [Client 1] 初始化阶段 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:23:59,255 - INFO - [Trainer] 初始化训练器, client_id: 1
2025-08-01 10:23:59,256 - INFO - [Trainer 1] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:23:59,257 - INFO - [Trainer 1] 模型的输入通道数: 3
2025-08-01 10:23:59,257 - INFO - [Trainer 1] 强制使用CPU
2025-08-01 10:23:59,257 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 10:23:59,257 - INFO - [Trainer 1] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:23:59,258 - INFO - [Trainer 1] 初始化完成
2025-08-01 10:23:59,258 - INFO - [Client 1] 创建新训练器
2025-08-01 10:23:59,258 - INFO - [Algorithm] 从训练器获取客户端ID: 1
2025-08-01 10:23:59,258 - INFO - [Algorithm] 初始化后修正client_id: 1
2025-08-01 10:23:59,258 - INFO - [Algorithm] 初始化完成
2025-08-01 10:23:59,258 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:23:59,258 - INFO - [Client 1] 创建新算法
2025-08-01 10:23:59,258 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 10:23:59,258 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 10:23:59,259 - INFO - [Client None] 父类初始化完成
2025-08-01 10:23:59,261 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:23:59,262 - INFO - [Client None] 父类初始化后 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:23:59,262 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:23:59,263 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:23:59,263 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:23:59,263 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:23:59,263 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:23:59,264 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:23:59,264 - INFO - [Trainer None] 初始化完成
2025-08-01 10:23:59,264 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:23:59,265 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:23:59,265 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:23:59,265 - INFO - [Algorithm] 初始化完成
2025-08-01 10:23:59,265 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:23:59,265 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:23:59,265 - INFO - [Client None] 开始加载数据
2025-08-01 10:23:59,265 - INFO - 顺序分配客户端ID: 1
2025-08-01 10:23:59,265 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 1
2025-08-01 10:23:59,266 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:23:59,267 - WARNING - [Client 1] 数据源为None，已创建新数据源
2025-08-01 10:23:59,267 - WARNING - [Client 1] 数据源trainset为None，已设置为空列表
2025-08-01 10:23:59,954 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:23:59,955 - INFO - [Client 1] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:23:59,955 - INFO - [Client 1] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:23:59,988 - INFO - [Client 1] 成功划分数据集，分配到 300 个样本
2025-08-01 10:23:59,988 - INFO - [Client 1] 初始化时成功加载数据
2025-08-01 10:23:59,988 - INFO - [客户端 1] 初始化验证通过
2025-08-01 10:23:59,988 - INFO - 客户端 1 实例创建成功
2025-08-01 10:23:59,989 - INFO - 客户端1已设置服务器引用
2025-08-01 10:23:59,989 - INFO - 客户端 1 已设置服务器引用
2025-08-01 10:23:59,989 - INFO - 客户端1已注册
2025-08-01 10:23:59,989 - INFO - 客户端 1 已成功注册到服务器
2025-08-01 10:23:59,989 - INFO - 开始创建客户端 2...
2025-08-01 10:23:59,989 - INFO - 初始化客户端, ID: 2
2025-08-01 10:23:59,990 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:23:59,990 - INFO - [Client 2] 初始化阶段 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:23:59,990 - INFO - [Trainer] 初始化训练器, client_id: 2
2025-08-01 10:23:59,991 - INFO - [Trainer 2] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:23:59,991 - INFO - [Trainer 2] 模型的输入通道数: 3
2025-08-01 10:23:59,993 - INFO - [Trainer 2] 强制使用CPU
2025-08-01 10:23:59,993 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 10:23:59,993 - INFO - [Trainer 2] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:23:59,993 - INFO - [Trainer 2] 初始化完成
2025-08-01 10:23:59,993 - INFO - [Client 2] 创建新训练器
2025-08-01 10:23:59,993 - INFO - [Algorithm] 从训练器获取客户端ID: 2
2025-08-01 10:23:59,993 - INFO - [Algorithm] 初始化后修正client_id: 2
2025-08-01 10:23:59,993 - INFO - [Algorithm] 初始化完成
2025-08-01 10:23:59,994 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:23:59,994 - INFO - [Client 2] 创建新算法
2025-08-01 10:23:59,995 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 10:23:59,995 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 10:23:59,995 - INFO - [Client None] 父类初始化完成
2025-08-01 10:23:59,996 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:23:59,996 - INFO - [Client None] 父类初始化后 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:23:59,996 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:23:59,997 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:23:59,997 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:23:59,997 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:23:59,997 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:23:59,997 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:23:59,997 - INFO - [Trainer None] 初始化完成
2025-08-01 10:23:59,998 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:23:59,998 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:23:59,998 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:23:59,998 - INFO - [Algorithm] 初始化完成
2025-08-01 10:23:59,998 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:23:59,998 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:23:59,998 - INFO - [Client None] 开始加载数据
2025-08-01 10:23:59,998 - INFO - 顺序分配客户端ID: 2
2025-08-01 10:23:59,998 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 2
2025-08-01 10:23:59,999 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:23:59,999 - WARNING - [Client 2] 数据源为None，已创建新数据源
2025-08-01 10:23:59,999 - WARNING - [Client 2] 数据源trainset为None，已设置为空列表
2025-08-01 10:24:00,665 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:24:00,665 - INFO - [Client 2] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:24:00,666 - INFO - [Client 2] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:24:00,670 - INFO - [Client 2] 成功划分数据集，分配到 300 个样本
2025-08-01 10:24:00,670 - INFO - [Client 2] 初始化时成功加载数据
2025-08-01 10:24:00,670 - INFO - [客户端 2] 初始化验证通过
2025-08-01 10:24:00,670 - INFO - 客户端 2 实例创建成功
2025-08-01 10:24:00,670 - INFO - 客户端2已设置服务器引用
2025-08-01 10:24:00,670 - INFO - 客户端 2 已设置服务器引用
2025-08-01 10:24:00,670 - INFO - 客户端2已注册
2025-08-01 10:24:00,670 - INFO - 客户端 2 已成功注册到服务器
2025-08-01 10:24:00,670 - INFO - 开始创建客户端 3...
2025-08-01 10:24:00,670 - INFO - 初始化客户端, ID: 3
2025-08-01 10:24:00,672 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:00,672 - INFO - [Client 3] 初始化阶段 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:00,672 - INFO - [Trainer] 初始化训练器, client_id: 3
2025-08-01 10:24:00,676 - INFO - [Trainer 3] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:00,676 - INFO - [Trainer 3] 模型的输入通道数: 3
2025-08-01 10:24:00,676 - INFO - [Trainer 3] 强制使用CPU
2025-08-01 10:24:00,677 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:24:00,677 - INFO - [Trainer 3] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:00,677 - INFO - [Trainer 3] 初始化完成
2025-08-01 10:24:00,677 - INFO - [Client 3] 创建新训练器
2025-08-01 10:24:00,677 - INFO - [Algorithm] 从训练器获取客户端ID: 3
2025-08-01 10:24:00,677 - INFO - [Algorithm] 初始化后修正client_id: 3
2025-08-01 10:24:00,677 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:00,677 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:00,677 - INFO - [Client 3] 创建新算法
2025-08-01 10:24:00,677 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 10:24:00,678 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 10:24:00,678 - INFO - [Client None] 父类初始化完成
2025-08-01 10:24:00,679 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:00,679 - INFO - [Client None] 父类初始化后 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:00,679 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:24:00,680 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:00,680 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:24:00,681 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:24:00,681 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:24:00,681 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:00,681 - INFO - [Trainer None] 初始化完成
2025-08-01 10:24:00,681 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:24:00,681 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:24:00,681 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:24:00,681 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:00,681 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:00,681 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:24:00,682 - INFO - [Client None] 开始加载数据
2025-08-01 10:24:00,682 - INFO - 顺序分配客户端ID: 3
2025-08-01 10:24:00,682 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 3
2025-08-01 10:24:00,682 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:24:00,683 - WARNING - [Client 3] 数据源为None，已创建新数据源
2025-08-01 10:24:00,683 - WARNING - [Client 3] 数据源trainset为None，已设置为空列表
2025-08-01 10:24:01,337 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:24:01,337 - INFO - [Client 3] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:24:01,337 - INFO - [Client 3] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:24:01,341 - INFO - [Client 3] 成功划分数据集，分配到 300 个样本
2025-08-01 10:24:01,341 - INFO - [Client 3] 初始化时成功加载数据
2025-08-01 10:24:01,341 - INFO - [客户端 3] 初始化验证通过
2025-08-01 10:24:01,341 - INFO - 客户端 3 实例创建成功
2025-08-01 10:24:01,342 - INFO - 客户端3已设置服务器引用
2025-08-01 10:24:01,342 - INFO - 客户端 3 已设置服务器引用
2025-08-01 10:24:01,342 - INFO - 客户端3已注册
2025-08-01 10:24:01,342 - INFO - 客户端 3 已成功注册到服务器
2025-08-01 10:24:01,342 - INFO - 开始创建客户端 4...
2025-08-01 10:24:01,342 - INFO - 初始化客户端, ID: 4
2025-08-01 10:24:01,343 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:01,343 - INFO - [Client 4] 初始化阶段 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:01,343 - INFO - [Trainer] 初始化训练器, client_id: 4
2025-08-01 10:24:01,344 - INFO - [Trainer 4] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:01,345 - INFO - [Trainer 4] 模型的输入通道数: 3
2025-08-01 10:24:01,345 - INFO - [Trainer 4] 强制使用CPU
2025-08-01 10:24:01,345 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:24:01,345 - INFO - [Trainer 4] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:01,345 - INFO - [Trainer 4] 初始化完成
2025-08-01 10:24:01,345 - INFO - [Client 4] 创建新训练器
2025-08-01 10:24:01,345 - INFO - [Algorithm] 从训练器获取客户端ID: 4
2025-08-01 10:24:01,345 - INFO - [Algorithm] 初始化后修正client_id: 4
2025-08-01 10:24:01,345 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:01,345 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:01,345 - INFO - [Client 4] 创建新算法
2025-08-01 10:24:01,346 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 10:24:01,346 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 10:24:01,346 - INFO - [Client None] 父类初始化完成
2025-08-01 10:24:01,346 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:01,346 - INFO - [Client None] 父类初始化后 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:01,346 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:24:01,347 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:01,348 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:24:01,348 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:24:01,348 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:24:01,348 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:01,349 - INFO - [Trainer None] 初始化完成
2025-08-01 10:24:01,349 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:24:01,349 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:24:01,349 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:24:01,349 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:01,349 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:01,349 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:24:01,349 - INFO - [Client None] 开始加载数据
2025-08-01 10:24:01,349 - INFO - 顺序分配客户端ID: 4
2025-08-01 10:24:01,349 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 4
2025-08-01 10:24:01,349 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:24:01,350 - WARNING - [Client 4] 数据源为None，已创建新数据源
2025-08-01 10:24:01,353 - WARNING - [Client 4] 数据源trainset为None，已设置为空列表
2025-08-01 10:24:02,014 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:24:02,014 - INFO - [Client 4] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:24:02,014 - INFO - [Client 4] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:24:02,018 - INFO - [Client 4] 成功划分数据集，分配到 300 个样本
2025-08-01 10:24:02,018 - INFO - [Client 4] 初始化时成功加载数据
2025-08-01 10:24:02,018 - INFO - [客户端 4] 初始化验证通过
2025-08-01 10:24:02,018 - INFO - 客户端 4 实例创建成功
2025-08-01 10:24:02,018 - INFO - 客户端4已设置服务器引用
2025-08-01 10:24:02,018 - INFO - 客户端 4 已设置服务器引用
2025-08-01 10:24:02,019 - INFO - 客户端4已注册
2025-08-01 10:24:02,019 - INFO - 客户端 4 已成功注册到服务器
2025-08-01 10:24:02,019 - INFO - 开始创建客户端 5...
2025-08-01 10:24:02,019 - INFO - 初始化客户端, ID: 5
2025-08-01 10:24:02,020 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:02,020 - INFO - [Client 5] 初始化阶段 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:02,020 - INFO - [Trainer] 初始化训练器, client_id: 5
2025-08-01 10:24:02,021 - INFO - [Trainer 5] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:02,021 - INFO - [Trainer 5] 模型的输入通道数: 3
2025-08-01 10:24:02,021 - INFO - [Trainer 5] 强制使用CPU
2025-08-01 10:24:02,022 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 10:24:02,022 - INFO - [Trainer 5] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:02,022 - INFO - [Trainer 5] 初始化完成
2025-08-01 10:24:02,022 - INFO - [Client 5] 创建新训练器
2025-08-01 10:24:02,022 - INFO - [Algorithm] 从训练器获取客户端ID: 5
2025-08-01 10:24:02,022 - INFO - [Algorithm] 初始化后修正client_id: 5
2025-08-01 10:24:02,022 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:02,022 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:02,022 - INFO - [Client 5] 创建新算法
2025-08-01 10:24:02,022 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 10:24:02,022 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 10:24:02,022 - INFO - [Client None] 父类初始化完成
2025-08-01 10:24:02,023 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:02,023 - INFO - [Client None] 父类初始化后 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:02,023 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:24:02,024 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:02,024 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:24:02,024 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:24:02,025 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:24:02,025 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:02,025 - INFO - [Trainer None] 初始化完成
2025-08-01 10:24:02,025 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:24:02,025 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:24:02,025 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:24:02,026 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:02,026 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:02,026 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:24:02,026 - INFO - [Client None] 开始加载数据
2025-08-01 10:24:02,026 - INFO - 顺序分配客户端ID: 5
2025-08-01 10:24:02,026 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 5
2025-08-01 10:24:02,026 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:24:02,026 - WARNING - [Client 5] 数据源为None，已创建新数据源
2025-08-01 10:24:02,027 - WARNING - [Client 5] 数据源trainset为None，已设置为空列表
2025-08-01 10:24:02,708 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:24:02,709 - INFO - [Client 5] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:24:02,709 - INFO - [Client 5] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:24:02,714 - INFO - [Client 5] 成功划分数据集，分配到 300 个样本
2025-08-01 10:24:02,715 - INFO - [Client 5] 初始化时成功加载数据
2025-08-01 10:24:02,715 - INFO - [客户端 5] 初始化验证通过
2025-08-01 10:24:02,715 - INFO - 客户端 5 实例创建成功
2025-08-01 10:24:02,715 - INFO - 客户端5已设置服务器引用
2025-08-01 10:24:02,715 - INFO - 客户端 5 已设置服务器引用
2025-08-01 10:24:02,715 - INFO - 客户端5已注册
2025-08-01 10:24:02,715 - INFO - 客户端 5 已成功注册到服务器
2025-08-01 10:24:02,716 - INFO - 开始创建客户端 6...
2025-08-01 10:24:02,716 - INFO - 初始化客户端, ID: 6
2025-08-01 10:24:02,716 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:02,717 - INFO - [Client 6] 初始化阶段 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:02,717 - INFO - [Trainer] 初始化训练器, client_id: 6
2025-08-01 10:24:02,718 - INFO - [Trainer 6] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:02,718 - INFO - [Trainer 6] 模型的输入通道数: 3
2025-08-01 10:24:02,718 - INFO - [Trainer 6] 强制使用CPU
2025-08-01 10:24:02,718 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 10:24:02,719 - INFO - [Trainer 6] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:02,719 - INFO - [Trainer 6] 初始化完成
2025-08-01 10:24:02,719 - INFO - [Client 6] 创建新训练器
2025-08-01 10:24:02,719 - INFO - [Algorithm] 从训练器获取客户端ID: 6
2025-08-01 10:24:02,719 - INFO - [Algorithm] 初始化后修正client_id: 6
2025-08-01 10:24:02,719 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:02,719 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:02,720 - INFO - [Client 6] 创建新算法
2025-08-01 10:24:02,720 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 10:24:02,720 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 10:24:02,720 - INFO - [Client None] 父类初始化完成
2025-08-01 10:24:02,721 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:02,721 - INFO - [Client None] 父类初始化后 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:02,721 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:24:02,723 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:02,724 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:24:02,724 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:24:02,724 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:24:02,724 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:02,724 - INFO - [Trainer None] 初始化完成
2025-08-01 10:24:02,724 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:24:02,725 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:24:02,725 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:24:02,725 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:02,725 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:02,725 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:24:02,725 - INFO - [Client None] 开始加载数据
2025-08-01 10:24:02,725 - INFO - 顺序分配客户端ID: 6
2025-08-01 10:24:02,725 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 6
2025-08-01 10:24:02,725 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:24:02,727 - WARNING - [Client 6] 数据源为None，已创建新数据源
2025-08-01 10:24:02,727 - WARNING - [Client 6] 数据源trainset为None，已设置为空列表
2025-08-01 10:24:03,437 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:24:03,438 - INFO - [Client 6] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:24:03,438 - INFO - [Client 6] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:24:03,441 - INFO - [Client 6] 成功划分数据集，分配到 300 个样本
2025-08-01 10:24:03,442 - INFO - [Client 6] 初始化时成功加载数据
2025-08-01 10:24:03,442 - INFO - [客户端 6] 初始化验证通过
2025-08-01 10:24:03,442 - INFO - 客户端 6 实例创建成功
2025-08-01 10:24:03,442 - INFO - 客户端6已设置服务器引用
2025-08-01 10:24:03,442 - INFO - 客户端 6 已设置服务器引用
2025-08-01 10:24:03,442 - INFO - 客户端6已注册
2025-08-01 10:24:03,442 - INFO - 客户端 6 已成功注册到服务器
2025-08-01 10:24:03,442 - INFO - 开始创建客户端 7...
2025-08-01 10:24:03,442 - INFO - 初始化客户端, ID: 7
2025-08-01 10:24:03,443 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:03,443 - INFO - [Client 7] 初始化阶段 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:03,443 - INFO - [Trainer] 初始化训练器, client_id: 7
2025-08-01 10:24:03,445 - INFO - [Trainer 7] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:03,445 - INFO - [Trainer 7] 模型的输入通道数: 3
2025-08-01 10:24:03,445 - INFO - [Trainer 7] 强制使用CPU
2025-08-01 10:24:03,445 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:24:03,446 - INFO - [Trainer 7] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:03,446 - INFO - [Trainer 7] 初始化完成
2025-08-01 10:24:03,446 - INFO - [Client 7] 创建新训练器
2025-08-01 10:24:03,446 - INFO - [Algorithm] 从训练器获取客户端ID: 7
2025-08-01 10:24:03,446 - INFO - [Algorithm] 初始化后修正client_id: 7
2025-08-01 10:24:03,446 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:03,446 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:03,447 - INFO - [Client 7] 创建新算法
2025-08-01 10:24:03,447 - INFO - [Algorithm] 设置客户端ID: 7
2025-08-01 10:24:03,447 - INFO - [Algorithm] 同步更新trainer的client_id: 7
2025-08-01 10:24:03,447 - INFO - [Client None] 父类初始化完成
2025-08-01 10:24:03,447 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:03,447 - INFO - [Client None] 父类初始化后 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:03,448 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:24:03,450 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:03,451 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:24:03,451 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:24:03,451 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:24:03,451 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:03,451 - INFO - [Trainer None] 初始化完成
2025-08-01 10:24:03,451 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:24:03,451 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:24:03,451 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:24:03,451 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:03,451 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:03,451 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:24:03,452 - INFO - [Client None] 开始加载数据
2025-08-01 10:24:03,452 - INFO - 顺序分配客户端ID: 7
2025-08-01 10:24:03,452 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 7
2025-08-01 10:24:03,452 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:24:03,453 - WARNING - [Client 7] 数据源为None，已创建新数据源
2025-08-01 10:24:03,453 - WARNING - [Client 7] 数据源trainset为None，已设置为空列表
2025-08-01 10:24:04,093 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:24:04,094 - INFO - [Client 7] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:24:04,094 - INFO - [Client 7] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:24:04,097 - INFO - [Client 7] 成功划分数据集，分配到 300 个样本
2025-08-01 10:24:04,097 - INFO - [Client 7] 初始化时成功加载数据
2025-08-01 10:24:04,097 - INFO - [客户端 7] 初始化验证通过
2025-08-01 10:24:04,097 - INFO - 客户端 7 实例创建成功
2025-08-01 10:24:04,097 - INFO - 客户端7已设置服务器引用
2025-08-01 10:24:04,098 - INFO - 客户端 7 已设置服务器引用
2025-08-01 10:24:04,098 - INFO - 客户端7已注册
2025-08-01 10:24:04,098 - INFO - 客户端 7 已成功注册到服务器
2025-08-01 10:24:04,098 - INFO - 开始创建客户端 8...
2025-08-01 10:24:04,098 - INFO - 初始化客户端, ID: 8
2025-08-01 10:24:04,098 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:04,099 - INFO - [Client 8] 初始化阶段 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:04,099 - INFO - [Trainer] 初始化训练器, client_id: 8
2025-08-01 10:24:04,100 - INFO - [Trainer 8] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:04,100 - INFO - [Trainer 8] 模型的输入通道数: 3
2025-08-01 10:24:04,100 - INFO - [Trainer 8] 强制使用CPU
2025-08-01 10:24:04,100 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:24:04,101 - INFO - [Trainer 8] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:04,101 - INFO - [Trainer 8] 初始化完成
2025-08-01 10:24:04,101 - INFO - [Client 8] 创建新训练器
2025-08-01 10:24:04,101 - INFO - [Algorithm] 从训练器获取客户端ID: 8
2025-08-01 10:24:04,101 - INFO - [Algorithm] 初始化后修正client_id: 8
2025-08-01 10:24:04,101 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:04,101 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:04,101 - INFO - [Client 8] 创建新算法
2025-08-01 10:24:04,101 - INFO - [Algorithm] 设置客户端ID: 8
2025-08-01 10:24:04,101 - INFO - [Algorithm] 同步更新trainer的client_id: 8
2025-08-01 10:24:04,101 - INFO - [Client None] 父类初始化完成
2025-08-01 10:24:04,102 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:04,102 - INFO - [Client None] 父类初始化后 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:04,102 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:24:04,103 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:04,103 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:24:04,103 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:24:04,103 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:24:04,103 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:04,103 - INFO - [Trainer None] 初始化完成
2025-08-01 10:24:04,103 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:24:04,104 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:24:04,104 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:24:04,104 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:04,104 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:04,104 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:24:04,104 - INFO - [Client None] 开始加载数据
2025-08-01 10:24:04,104 - INFO - 顺序分配客户端ID: 8
2025-08-01 10:24:04,104 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 8
2025-08-01 10:24:04,104 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:24:04,104 - WARNING - [Client 8] 数据源为None，已创建新数据源
2025-08-01 10:24:04,104 - WARNING - [Client 8] 数据源trainset为None，已设置为空列表
2025-08-01 10:24:04,837 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:24:04,837 - INFO - [Client 8] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:24:04,837 - INFO - [Client 8] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:24:04,842 - INFO - [Client 8] 成功划分数据集，分配到 300 个样本
2025-08-01 10:24:04,842 - INFO - [Client 8] 初始化时成功加载数据
2025-08-01 10:24:04,842 - INFO - [客户端 8] 初始化验证通过
2025-08-01 10:24:04,842 - INFO - 客户端 8 实例创建成功
2025-08-01 10:24:04,842 - INFO - 客户端8已设置服务器引用
2025-08-01 10:24:04,843 - INFO - 客户端 8 已设置服务器引用
2025-08-01 10:24:04,843 - INFO - 客户端8已注册
2025-08-01 10:24:04,843 - INFO - 客户端 8 已成功注册到服务器
2025-08-01 10:24:04,843 - INFO - 开始创建客户端 9...
2025-08-01 10:24:04,843 - INFO - 初始化客户端, ID: 9
2025-08-01 10:24:04,844 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:04,844 - INFO - [Client 9] 初始化阶段 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:04,844 - INFO - [Trainer] 初始化训练器, client_id: 9
2025-08-01 10:24:04,846 - INFO - [Trainer 9] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:04,846 - INFO - [Trainer 9] 模型的输入通道数: 3
2025-08-01 10:24:04,846 - INFO - [Trainer 9] 强制使用CPU
2025-08-01 10:24:04,846 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:24:04,847 - INFO - [Trainer 9] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:04,847 - INFO - [Trainer 9] 初始化完成
2025-08-01 10:24:04,847 - INFO - [Client 9] 创建新训练器
2025-08-01 10:24:04,847 - INFO - [Algorithm] 从训练器获取客户端ID: 9
2025-08-01 10:24:04,847 - INFO - [Algorithm] 初始化后修正client_id: 9
2025-08-01 10:24:04,848 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:04,848 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:04,848 - INFO - [Client 9] 创建新算法
2025-08-01 10:24:04,848 - INFO - [Algorithm] 设置客户端ID: 9
2025-08-01 10:24:04,848 - INFO - [Algorithm] 同步更新trainer的client_id: 9
2025-08-01 10:24:04,848 - INFO - [Client None] 父类初始化完成
2025-08-01 10:24:04,849 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:04,850 - INFO - [Client None] 父类初始化后 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:04,850 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:24:04,851 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:04,851 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:24:04,851 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:24:04,851 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:24:04,851 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:04,851 - INFO - [Trainer None] 初始化完成
2025-08-01 10:24:04,851 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:24:04,851 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:24:04,852 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:24:04,852 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:04,852 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:04,852 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:24:04,852 - INFO - [Client None] 开始加载数据
2025-08-01 10:24:04,852 - INFO - 顺序分配客户端ID: 9
2025-08-01 10:24:04,852 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 9
2025-08-01 10:24:04,853 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:24:04,853 - WARNING - [Client 9] 数据源为None，已创建新数据源
2025-08-01 10:24:04,853 - WARNING - [Client 9] 数据源trainset为None，已设置为空列表
2025-08-01 10:24:05,559 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:24:05,559 - INFO - [Client 9] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:24:05,560 - INFO - [Client 9] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:24:05,564 - INFO - [Client 9] 成功划分数据集，分配到 300 个样本
2025-08-01 10:24:05,564 - INFO - [Client 9] 初始化时成功加载数据
2025-08-01 10:24:05,565 - INFO - [客户端 9] 初始化验证通过
2025-08-01 10:24:05,565 - INFO - 客户端 9 实例创建成功
2025-08-01 10:24:05,565 - INFO - 客户端9已设置服务器引用
2025-08-01 10:24:05,565 - INFO - 客户端 9 已设置服务器引用
2025-08-01 10:24:05,565 - INFO - 客户端9已注册
2025-08-01 10:24:05,565 - INFO - 客户端 9 已成功注册到服务器
2025-08-01 10:24:05,565 - INFO - 开始创建客户端 10...
2025-08-01 10:24:05,565 - INFO - 初始化客户端, ID: 10
2025-08-01 10:24:05,566 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:05,566 - INFO - [Client 10] 初始化阶段 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:05,566 - INFO - [Trainer] 初始化训练器, client_id: 10
2025-08-01 10:24:05,567 - INFO - [Trainer 10] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:05,567 - INFO - [Trainer 10] 模型的输入通道数: 3
2025-08-01 10:24:05,567 - INFO - [Trainer 10] 强制使用CPU
2025-08-01 10:24:05,567 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:24:05,568 - INFO - [Trainer 10] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:05,568 - INFO - [Trainer 10] 初始化完成
2025-08-01 10:24:05,568 - INFO - [Client 10] 创建新训练器
2025-08-01 10:24:05,568 - INFO - [Algorithm] 从训练器获取客户端ID: 10
2025-08-01 10:24:05,568 - INFO - [Algorithm] 初始化后修正client_id: 10
2025-08-01 10:24:05,568 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:05,568 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:05,568 - INFO - [Client 10] 创建新算法
2025-08-01 10:24:05,568 - INFO - [Algorithm] 设置客户端ID: 10
2025-08-01 10:24:05,568 - INFO - [Algorithm] 同步更新trainer的client_id: 10
2025-08-01 10:24:05,568 - INFO - [Client None] 父类初始化完成
2025-08-01 10:24:05,569 - INFO - ✅ 成功创建模型 lenet5: Model
2025-08-01 10:24:05,569 - INFO - [Client None] 父类初始化后 动态创建模型 lenet5，输入通道数: 3, 类别数: 10
2025-08-01 10:24:05,569 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:24:05,570 - INFO - [Trainer None] 创建了完全独立的模型实例，禁用所有BatchNorm状态跟踪
2025-08-01 10:24:05,570 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:24:05,570 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:24:05,570 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:24:05,570 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:05,570 - INFO - [Trainer None] 初始化完成
2025-08-01 10:24:05,570 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:24:05,570 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:24:05,571 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:24:05,571 - INFO - [Algorithm] 初始化完成
2025-08-01 10:24:05,571 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:24:05,571 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:24:05,571 - INFO - [Client None] 开始加载数据
2025-08-01 10:24:05,571 - INFO - 顺序分配客户端ID: 10
2025-08-01 10:24:05,571 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 10
2025-08-01 10:24:05,571 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:24:05,572 - WARNING - [Client 10] 数据源为None，已创建新数据源
2025-08-01 10:24:05,572 - WARNING - [Client 10] 数据源trainset为None，已设置为空列表
2025-08-01 10:24:06,234 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:24:06,234 - INFO - [Client 10] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:24:06,235 - INFO - [Client 10] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:24:06,238 - INFO - [Client 10] 成功划分数据集，分配到 300 个样本
2025-08-01 10:24:06,238 - INFO - [Client 10] 初始化时成功加载数据
2025-08-01 10:24:06,238 - INFO - [客户端 10] 初始化验证通过
2025-08-01 10:24:06,238 - INFO - 客户端 10 实例创建成功
2025-08-01 10:24:06,238 - INFO - 客户端10已设置服务器引用
2025-08-01 10:24:06,239 - INFO - 客户端 10 已设置服务器引用
2025-08-01 10:24:06,239 - INFO - 客户端10已注册
2025-08-01 10:24:06,239 - INFO - 客户端 10 已成功注册到服务器
2025-08-01 10:24:06,239 - INFO - 已成功创建和注册 10 个客户端
2025-08-01 10:24:06,239 - INFO - 服务器属性检查:
2025-08-01 10:24:06,239 - INFO - - 客户端数量: 10
2025-08-01 10:24:06,239 - INFO - - 全局模型: 已初始化
2025-08-01 10:24:06,239 - INFO - - 算法: 已初始化
2025-08-01 10:24:06,239 - INFO - - 训练器: 已初始化
2025-08-01 10:24:06,239 - INFO - 准备启动服务器...
2025-08-01 10:24:06,239 - INFO - [Server #9292] 启动中...
2025-08-01 10:24:06,239 - INFO - 服务器将使用事件循环: <ProactorEventLoop running=False closed=False debug=False>
2025-08-01 10:24:06,240 - INFO - [Client 1] 模型已放置到设备: cpu
2025-08-01 10:24:06,241 - INFO - [Client 1] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,242 - INFO - [Client 1] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,243 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 10:24:06,243 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 10:24:06,243 - INFO - [Client 1] 已更新algorithm的client_id
2025-08-01 10:24:06,243 - INFO - [Client 1] 模型初始化完成
2025-08-01 10:24:06,243 - INFO - 客户端 1 模型初始化成功
2025-08-01 10:24:06,244 - INFO - 客户端 1 异步训练线程已启动
2025-08-01 10:24:06,244 - INFO - [Client 2] 模型已放置到设备: cpu
2025-08-01 10:24:06,245 - INFO - [Client 2] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,246 - INFO - [Client 2] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,246 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 10:24:06,246 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 10:24:06,246 - INFO - [Client 2] 已更新algorithm的client_id
2025-08-01 10:24:06,246 - INFO - [Client 2] 模型初始化完成
2025-08-01 10:24:06,246 - INFO - 客户端 2 模型初始化成功
2025-08-01 10:24:06,246 - INFO - 客户端 2 异步训练线程已启动
2025-08-01 10:24:06,246 - INFO - [Client 3] 模型已放置到设备: cpu
2025-08-01 10:24:06,247 - INFO - [Client 3] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,248 - INFO - [Client 3] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,248 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 10:24:06,248 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 10:24:06,248 - INFO - [Client 3] 已更新algorithm的client_id
2025-08-01 10:24:06,248 - INFO - [Client 3] 模型初始化完成
2025-08-01 10:24:06,248 - INFO - 客户端 3 模型初始化成功
2025-08-01 10:24:06,249 - INFO - 客户端 3 异步训练线程已启动
2025-08-01 10:24:06,249 - INFO - [Client 4] 模型已放置到设备: cpu
2025-08-01 10:24:06,250 - INFO - [Client 4] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,250 - INFO - [Client 4] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,250 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 10:24:06,250 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 10:24:06,250 - INFO - [Client 4] 已更新algorithm的client_id
2025-08-01 10:24:06,251 - INFO - [Client 4] 模型初始化完成
2025-08-01 10:24:06,251 - INFO - 客户端 4 模型初始化成功
2025-08-01 10:24:06,251 - INFO - 客户端 4 异步训练线程已启动
2025-08-01 10:24:06,251 - INFO - [Client 5] 模型已放置到设备: cpu
2025-08-01 10:24:06,253 - INFO - [Client 5] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,253 - INFO - [Client 5] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,253 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 10:24:06,253 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 10:24:06,253 - INFO - [Client 5] 已更新algorithm的client_id
2025-08-01 10:24:06,254 - INFO - [Client 5] 模型初始化完成
2025-08-01 10:24:06,254 - INFO - 客户端 5 模型初始化成功
2025-08-01 10:24:06,254 - INFO - 客户端 5 异步训练线程已启动
2025-08-01 10:24:06,254 - INFO - [Client 6] 模型已放置到设备: cpu
2025-08-01 10:24:06,255 - INFO - [Client 6] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,255 - INFO - [Client 6] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,255 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 10:24:06,257 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 10:24:06,257 - INFO - [Client 6] 已更新algorithm的client_id
2025-08-01 10:24:06,257 - INFO - [Client 6] 模型初始化完成
2025-08-01 10:24:06,257 - INFO - 客户端 6 模型初始化成功
2025-08-01 10:24:06,257 - INFO - 客户端 6 异步训练线程已启动
2025-08-01 10:24:06,258 - INFO - [Client 7] 模型已放置到设备: cpu
2025-08-01 10:24:06,259 - INFO - [Client 7] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,259 - INFO - [Client 7] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,259 - INFO - [Algorithm] 设置客户端ID: 7
2025-08-01 10:24:06,259 - INFO - [Algorithm] 同步更新trainer的client_id: 7
2025-08-01 10:24:06,260 - INFO - [Client 7] 已更新algorithm的client_id
2025-08-01 10:24:06,260 - INFO - [Client 7] 模型初始化完成
2025-08-01 10:24:06,260 - INFO - 客户端 7 模型初始化成功
2025-08-01 10:24:06,260 - INFO - 客户端 7 异步训练线程已启动
2025-08-01 10:24:06,260 - INFO - [Client 8] 模型已放置到设备: cpu
2025-08-01 10:24:06,261 - INFO - [Client 8] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,262 - INFO - [Client 8] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,262 - INFO - [Algorithm] 设置客户端ID: 8
2025-08-01 10:24:06,262 - INFO - [Algorithm] 同步更新trainer的client_id: 8
2025-08-01 10:24:06,262 - INFO - [Client 8] 已更新algorithm的client_id
2025-08-01 10:24:06,263 - INFO - [Client 8] 模型初始化完成
2025-08-01 10:24:06,263 - INFO - 客户端 8 模型初始化成功
2025-08-01 10:24:06,263 - INFO - 客户端 8 异步训练线程已启动
2025-08-01 10:24:06,263 - INFO - [Client 9] 模型已放置到设备: cpu
2025-08-01 10:24:06,265 - INFO - [Client 9] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,266 - INFO - [Client 9] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,266 - INFO - [Algorithm] 设置客户端ID: 9
2025-08-01 10:24:06,266 - INFO - [Algorithm] 同步更新trainer的client_id: 9
2025-08-01 10:24:06,266 - INFO - [Client 9] 已更新algorithm的client_id
2025-08-01 10:24:06,266 - INFO - [Client 9] 模型初始化完成
2025-08-01 10:24:06,266 - INFO - 客户端 9 模型初始化成功
2025-08-01 10:24:06,267 - INFO - 客户端 9 异步训练线程已启动
2025-08-01 10:24:06,267 - INFO - [Client 10] 模型已放置到设备: cpu
2025-08-01 10:24:06,268 - INFO - [Client 10] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,269 - INFO - [Client 10] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:24:06,269 - INFO - [Algorithm] 设置客户端ID: 10
2025-08-01 10:24:06,269 - INFO - [Algorithm] 同步更新trainer的client_id: 10
2025-08-01 10:24:06,269 - INFO - [Client 10] 已更新algorithm的client_id
2025-08-01 10:24:06,269 - INFO - [Client 10] 模型初始化完成
2025-08-01 10:24:06,269 - INFO - 客户端 10 模型初始化成功
2025-08-01 10:24:06,270 - INFO - 客户端 10 异步训练线程已启动
2025-08-01 10:24:06,271 - INFO - 服务器主循环任务已启动: <Task pending name='Task-1' coro=<Server.run() running at D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py:1302>>
2025-08-01 10:24:06,271 - INFO - Starting a server at address 127.0.0.1 and port 8000.
2025-08-01 10:24:06,274 - INFO - [Server #9292] 开始训练，共有 10 个客户端，每轮最多聚合 5 个客户端
2025-08-01 10:24:06,275 - INFO - 总训练轮次: 20
2025-08-01 10:24:06,275 - INFO - 🚀 开始第 1 轮训练（目标：20 轮）
2025-08-01 10:24:06,275 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:06,275 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:06,763 - INFO - 客户端 6 开始异步训练循环
2025-08-01 10:24:06,763 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:06,763 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 10:24:06,763 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 10:24:06,763 - INFO - [Client 6] 开始验证训练集
2025-08-01 10:24:06,787 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:06,787 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:06,787 - INFO - [Trainer 6] 开始训练
2025-08-01 10:24:06,787 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 10:24:06,787 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 10:24:06,788 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:06,788 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:06,791 - INFO - [Trainer 6] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:06,791 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 10:24:06,818 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2822, y=[0, 8, 8, 8, 0]
2025-08-01 10:24:06,819 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:06,823 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:06,842 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0586
2025-08-01 10:24:06,843 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:06,843 - INFO - [Trainer 6] 标签样本: [0, 0, 8, 8, 7]
2025-08-01 10:24:06,989 - ERROR - [Trainer 6] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:06,989 - ERROR - [Trainer 6] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:06,990 - ERROR - [Client 6] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:06,990 - ERROR - [Client 6] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:06,992 - INFO - 客户端 6 训练完成
2025-08-01 10:24:07,202 - INFO - 客户端 3 开始异步训练循环
2025-08-01 10:24:07,202 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:07,202 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 10:24:07,203 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 10:24:07,203 - INFO - [Client 3] 开始验证训练集
2025-08-01 10:24:07,203 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:07,203 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:07,203 - INFO - [Trainer 3] 开始训练
2025-08-01 10:24:07,203 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 10:24:07,203 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:24:07,203 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:07,204 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:07,206 - INFO - [Trainer 3] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:07,206 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 10:24:07,211 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1959, y=[6, 2, 2, 2, 2]
2025-08-01 10:24:07,211 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:07,215 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:07,233 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3133
2025-08-01 10:24:07,233 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:07,234 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:24:07,243 - ERROR - [Trainer 3] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:07,244 - ERROR - [Trainer 3] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:07,244 - ERROR - [Client 3] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:07,244 - ERROR - [Client 3] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:07,245 - INFO - 客户端 3 训练完成
2025-08-01 10:24:07,280 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:07,280 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:08,292 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:08,292 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:08,573 - INFO - 客户端 9 开始异步训练循环
2025-08-01 10:24:08,574 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:08,575 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 10:24:08,576 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 10:24:08,576 - INFO - [Client 9] 开始验证训练集
2025-08-01 10:24:08,577 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:08,578 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:08,578 - INFO - [Trainer 9] 开始训练
2025-08-01 10:24:08,578 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 10:24:08,578 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:24:08,579 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:08,579 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:08,583 - INFO - [Trainer 9] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:08,583 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 10:24:08,588 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4999, y=[6, 6, 6, 8, 6]
2025-08-01 10:24:08,589 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:08,593 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:08,605 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3886
2025-08-01 10:24:08,605 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:08,606 - INFO - [Trainer 9] 标签样本: [8, 6, 6, 6, 6]
2025-08-01 10:24:08,616 - ERROR - [Trainer 9] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:08,617 - ERROR - [Trainer 9] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:08,617 - ERROR - [Client 9] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:08,617 - ERROR - [Client 9] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:08,617 - INFO - 客户端 9 训练完成
2025-08-01 10:24:09,304 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:09,305 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:09,398 - INFO - 客户端 10 开始异步训练循环
2025-08-01 10:24:09,398 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:09,398 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:24:09,398 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:24:09,398 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:24:09,399 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:09,399 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:09,399 - INFO - [Trainer 10] 开始训练
2025-08-01 10:24:09,399 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:24:09,400 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:24:09,400 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:09,400 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:09,403 - INFO - [Trainer 10] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:09,403 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:24:09,408 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1673, y=[8, 8, 8, 8, 8]
2025-08-01 10:24:09,409 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:09,414 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:09,430 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2624
2025-08-01 10:24:09,431 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:09,431 - INFO - [Trainer 10] 标签样本: [1, 8, 8, 0, 8]
2025-08-01 10:24:09,453 - ERROR - [Trainer 10] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:09,454 - ERROR - [Trainer 10] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:09,454 - ERROR - [Client 10] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:09,455 - ERROR - [Client 10] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:09,456 - INFO - 客户端 10 训练完成
2025-08-01 10:24:10,319 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:10,319 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:11,334 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:11,334 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:11,474 - INFO - 客户端 1 开始异步训练循环
2025-08-01 10:24:11,474 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:11,475 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 10:24:11,475 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 10:24:11,476 - INFO - [Client 1] 开始验证训练集
2025-08-01 10:24:11,477 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:11,477 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:11,478 - INFO - [Trainer 1] 开始训练
2025-08-01 10:24:11,478 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 10:24:11,478 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 10:24:11,478 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:11,479 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:11,481 - INFO - [Trainer 1] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:11,481 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 10:24:11,488 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1567, y=[8, 5, 0, 0, 4]
2025-08-01 10:24:11,488 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:11,493 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:11,507 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1032
2025-08-01 10:24:11,507 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:11,508 - INFO - [Trainer 1] 标签样本: [0, 5, 5, 5, 5]
2025-08-01 10:24:11,520 - ERROR - [Trainer 1] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:11,520 - ERROR - [Trainer 1] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:11,521 - ERROR - [Client 1] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:11,521 - ERROR - [Client 1] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:11,521 - INFO - 客户端 1 训练完成
2025-08-01 10:24:12,071 - INFO - 客户端 7 开始异步训练循环
2025-08-01 10:24:12,071 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:12,071 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 10:24:12,071 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 10:24:12,072 - INFO - [Client 7] 开始验证训练集
2025-08-01 10:24:12,072 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:12,073 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:12,073 - INFO - [Trainer 7] 开始训练
2025-08-01 10:24:12,073 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 10:24:12,073 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:24:12,073 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:12,073 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:12,075 - INFO - [Trainer 7] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:12,075 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 10:24:12,080 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2632, y=[2, 2, 2, 2, 2]
2025-08-01 10:24:12,080 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:12,085 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:12,102 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2753
2025-08-01 10:24:12,102 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:12,102 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:24:12,114 - ERROR - [Trainer 7] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:12,114 - ERROR - [Trainer 7] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:12,114 - ERROR - [Client 7] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:12,115 - ERROR - [Client 7] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:12,115 - INFO - 客户端 7 训练完成
2025-08-01 10:24:12,337 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:12,340 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:12,777 - INFO - 客户端 4 开始异步训练循环
2025-08-01 10:24:12,777 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:12,778 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 10:24:12,778 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 10:24:12,778 - INFO - [Client 4] 开始验证训练集
2025-08-01 10:24:12,778 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:12,779 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:12,779 - INFO - [Trainer 4] 开始训练
2025-08-01 10:24:12,779 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 10:24:12,779 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:24:12,779 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:12,779 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:12,781 - INFO - [Trainer 4] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:12,781 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 10:24:12,786 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3918, y=[0, 1, 0, 1, 0]
2025-08-01 10:24:12,787 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:12,791 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:12,807 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2526
2025-08-01 10:24:12,807 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:12,808 - INFO - [Trainer 4] 标签样本: [0, 4, 0, 1, 1]
2025-08-01 10:24:12,817 - ERROR - [Trainer 4] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:12,818 - ERROR - [Trainer 4] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:12,818 - ERROR - [Client 4] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:12,819 - ERROR - [Client 4] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:12,819 - INFO - 客户端 4 训练完成
2025-08-01 10:24:13,196 - INFO - 客户端 8 开始异步训练循环
2025-08-01 10:24:13,196 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:13,197 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 10:24:13,197 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 10:24:13,197 - INFO - [Client 8] 开始验证训练集
2025-08-01 10:24:13,198 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:13,198 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:13,198 - INFO - [Trainer 8] 开始训练
2025-08-01 10:24:13,198 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 10:24:13,199 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:24:13,199 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:13,199 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:13,201 - INFO - [Trainer 8] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:13,202 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 10:24:13,207 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2253, y=[0, 0, 1, 6, 1]
2025-08-01 10:24:13,207 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:13,211 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:13,228 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4079
2025-08-01 10:24:13,228 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:13,228 - INFO - [Trainer 8] 标签样本: [1, 0, 1, 1, 1]
2025-08-01 10:24:13,238 - ERROR - [Trainer 8] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:13,238 - ERROR - [Trainer 8] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:13,239 - ERROR - [Client 8] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:13,239 - ERROR - [Client 8] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:13,240 - INFO - 客户端 8 训练完成
2025-08-01 10:24:13,353 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:13,353 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:13,930 - INFO - 客户端 5 开始异步训练循环
2025-08-01 10:24:13,931 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:13,931 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 10:24:13,932 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 10:24:13,932 - INFO - [Client 5] 开始验证训练集
2025-08-01 10:24:13,933 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:13,934 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:13,934 - INFO - [Trainer 5] 开始训练
2025-08-01 10:24:13,934 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 10:24:13,935 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 10:24:13,935 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:13,935 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:13,939 - INFO - [Trainer 5] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:13,939 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 10:24:13,944 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4180, y=[7, 7, 7, 7, 7]
2025-08-01 10:24:13,945 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:13,949 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:13,960 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4844
2025-08-01 10:24:13,960 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:13,960 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 7, 3]
2025-08-01 10:24:13,970 - ERROR - [Trainer 5] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:13,971 - ERROR - [Trainer 5] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:13,971 - ERROR - [Client 5] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:13,972 - ERROR - [Client 5] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:13,972 - INFO - 客户端 5 训练完成
2025-08-01 10:24:14,366 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:14,367 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:14,989 - INFO - 客户端 2 开始异步训练循环
2025-08-01 10:24:14,990 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:14,991 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 10:24:14,992 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 10:24:14,992 - INFO - [Client 2] 开始验证训练集
2025-08-01 10:24:14,993 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:14,993 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:14,993 - INFO - [Trainer 2] 开始训练
2025-08-01 10:24:14,993 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 10:24:14,994 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 10:24:14,994 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:14,994 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:14,996 - INFO - [Trainer 2] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:14,996 - INFO - [Trainer 2] 开始第 1/2 个epoch
2025-08-01 10:24:15,002 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2423, y=[8, 8, 8, 5, 8]
2025-08-01 10:24:15,002 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:15,007 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:15,020 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2364
2025-08-01 10:24:15,020 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:15,020 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:24:15,030 - ERROR - [Trainer 2] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:15,030 - ERROR - [Trainer 2] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:15,031 - ERROR - [Client 2] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:15,031 - ERROR - [Client 2] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:15,031 - INFO - 客户端 2 训练完成
2025-08-01 10:24:15,378 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:15,378 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:16,390 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:16,390 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:17,395 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:17,395 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:18,408 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:18,409 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:19,418 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:19,419 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:20,429 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:20,431 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:21,439 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:21,439 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:22,452 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:22,452 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:23,466 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:23,466 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:24,476 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:24,477 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:25,480 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:25,480 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:26,493 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:26,494 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:27,508 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:27,508 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:28,521 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:28,521 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:29,529 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:29,529 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:30,544 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:30,544 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:31,558 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:31,558 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:32,572 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:32,572 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:33,578 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:33,578 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:34,438 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:34,438 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 10:24:34,439 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 10:24:34,439 - INFO - [Client 1] 开始验证训练集
2025-08-01 10:24:34,440 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:34,440 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:34,441 - INFO - [Trainer 1] 开始训练
2025-08-01 10:24:34,441 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 10:24:34,441 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 10:24:34,442 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:34,442 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:34,442 - INFO - [Trainer 1] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:34,443 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 10:24:34,450 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1013, y=[4, 5, 8, 0, 8]
2025-08-01 10:24:34,450 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:34,455 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:34,470 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1416
2025-08-01 10:24:34,470 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:34,470 - INFO - [Trainer 1] 标签样本: [0, 4, 5, 8, 5]
2025-08-01 10:24:34,481 - ERROR - [Trainer 1] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:34,482 - ERROR - [Trainer 1] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:34,482 - ERROR - [Client 1] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:34,482 - ERROR - [Client 1] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:34,483 - INFO - 客户端 1 训练完成
2025-08-01 10:24:34,593 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:34,593 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:35,278 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:35,279 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 10:24:35,279 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 10:24:35,280 - INFO - [Client 8] 开始验证训练集
2025-08-01 10:24:35,282 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:35,282 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:35,283 - INFO - [Trainer 8] 开始训练
2025-08-01 10:24:35,283 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 10:24:35,284 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:24:35,284 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:35,285 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:35,286 - INFO - [Trainer 8] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:35,286 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 10:24:35,293 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4105, y=[6, 1, 1, 0, 1]
2025-08-01 10:24:35,293 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:35,299 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:35,309 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2446
2025-08-01 10:24:35,310 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:35,310 - INFO - [Trainer 8] 标签样本: [1, 6, 1, 1, 7]
2025-08-01 10:24:35,319 - ERROR - [Trainer 8] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:35,319 - ERROR - [Trainer 8] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:35,320 - ERROR - [Client 8] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:35,320 - ERROR - [Client 8] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:35,320 - INFO - 客户端 8 训练完成
2025-08-01 10:24:35,608 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:35,609 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:36,622 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:36,622 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:37,633 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:37,634 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:38,492 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:38,492 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 10:24:38,492 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 10:24:38,492 - INFO - [Client 3] 开始验证训练集
2025-08-01 10:24:38,493 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:38,493 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:38,493 - INFO - [Trainer 3] 开始训练
2025-08-01 10:24:38,493 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 10:24:38,493 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:24:38,494 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:38,494 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:38,495 - INFO - [Trainer 3] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:38,495 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 10:24:38,500 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2864, y=[2, 2, 2, 6, 2]
2025-08-01 10:24:38,501 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:38,507 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:38,523 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4016
2025-08-01 10:24:38,524 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:38,524 - INFO - [Trainer 3] 标签样本: [6, 2, 2, 6, 2]
2025-08-01 10:24:38,533 - ERROR - [Trainer 3] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:38,534 - ERROR - [Trainer 3] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:38,534 - ERROR - [Client 3] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:38,534 - ERROR - [Client 3] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:38,537 - INFO - 客户端 3 训练完成
2025-08-01 10:24:38,646 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:38,646 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:39,659 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:39,659 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:40,670 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:40,670 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:41,678 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:41,679 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:42,691 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:42,691 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:43,705 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:43,706 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:44,716 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:44,716 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:45,728 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:45,729 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:46,741 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:46,741 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:47,754 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:47,754 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:48,173 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:48,174 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:24:48,174 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:24:48,175 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:24:48,177 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:48,177 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:48,177 - INFO - [Trainer 10] 开始训练
2025-08-01 10:24:48,178 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:24:48,178 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:24:48,179 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:48,179 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:48,180 - INFO - [Trainer 10] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:48,180 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:24:48,190 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2240, y=[8, 8, 8, 8, 7]
2025-08-01 10:24:48,190 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:48,194 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:48,204 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0903
2025-08-01 10:24:48,204 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:48,205 - INFO - [Trainer 10] 标签样本: [8, 8, 6, 8, 8]
2025-08-01 10:24:48,214 - ERROR - [Trainer 10] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:48,214 - ERROR - [Trainer 10] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:48,214 - ERROR - [Client 10] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:48,215 - ERROR - [Client 10] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:48,215 - INFO - 客户端 10 训练完成
2025-08-01 10:24:48,765 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:48,765 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:48,983 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:48,984 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 10:24:48,985 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 10:24:48,985 - INFO - [Client 2] 开始验证训练集
2025-08-01 10:24:48,986 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:48,986 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:48,987 - INFO - [Trainer 2] 开始训练
2025-08-01 10:24:48,987 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 10:24:48,987 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 10:24:48,987 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:48,988 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:48,989 - INFO - [Trainer 2] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:48,989 - INFO - [Trainer 2] 开始第 1/2 个epoch
2025-08-01 10:24:48,995 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1553, y=[8, 8, 8, 8, 8]
2025-08-01 10:24:48,995 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:49,000 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:49,014 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:24:49,014 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 10:24:49,014 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1237
2025-08-01 10:24:49,014 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 10:24:49,014 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:49,015 - INFO - [Client 7] 开始验证训练集
2025-08-01 10:24:49,015 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:24:49,015 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:24:49,015 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 10:24:49,016 - INFO - [Trainer 7] 开始训练
2025-08-01 10:24:49,016 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 10:24:49,016 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:24:49,016 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:24:49,016 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:24:49,017 - INFO - [Trainer 7] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:24:49,017 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 10:24:49,023 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3941, y=[2, 2, 2, 2, 2]
2025-08-01 10:24:49,023 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 10:24:49,031 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 10:24:49,033 - ERROR - [Trainer 2] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:49,033 - ERROR - [Trainer 2] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:49,034 - ERROR - [Client 2] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:49,034 - ERROR - [Client 2] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:49,035 - INFO - 客户端 2 训练完成
2025-08-01 10:24:49,045 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2992
2025-08-01 10:24:49,045 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:24:49,045 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:24:49,048 - ERROR - [Trainer 7] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:49,048 - ERROR - [Trainer 7] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:49,049 - ERROR - [Client 7] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:24:49,049 - ERROR - [Client 7] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:24:49,049 - INFO - 客户端 7 训练完成
2025-08-01 10:24:49,777 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:49,777 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:50,791 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:50,792 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:51,803 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:51,803 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:52,803 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:52,803 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:53,807 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:53,808 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:54,821 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:54,821 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:55,834 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:55,834 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:56,837 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:56,837 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:57,848 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:57,848 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:58,864 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:58,864 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:59,877 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:24:59,877 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:00,879 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:00,879 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:01,890 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:01,890 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:02,902 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:02,902 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:03,914 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:03,914 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:03,977 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:03,977 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 10:25:03,977 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 10:25:03,978 - INFO - [Client 4] 开始验证训练集
2025-08-01 10:25:03,978 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:03,978 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:03,978 - INFO - [Trainer 4] 开始训练
2025-08-01 10:25:03,979 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 10:25:03,979 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:25:03,979 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:03,979 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:03,979 - INFO - [Trainer 4] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:03,980 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 10:25:03,985 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1654, y=[0, 0, 0, 0, 0]
2025-08-01 10:25:03,985 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:03,989 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:03,993 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1439
2025-08-01 10:25:03,993 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:03,993 - INFO - [Trainer 4] 标签样本: [0, 1, 4, 1, 1]
2025-08-01 10:25:04,003 - ERROR - [Trainer 4] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:04,004 - ERROR - [Trainer 4] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:04,004 - ERROR - [Client 4] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:04,004 - ERROR - [Client 4] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:04,005 - INFO - 客户端 4 训练完成
2025-08-01 10:25:04,133 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:04,134 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 10:25:04,135 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 10:25:04,135 - INFO - [Client 5] 开始验证训练集
2025-08-01 10:25:04,136 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:04,137 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:04,137 - INFO - [Trainer 5] 开始训练
2025-08-01 10:25:04,137 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 10:25:04,138 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 10:25:04,138 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:04,138 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:04,139 - INFO - [Trainer 5] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:04,139 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 10:25:04,145 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3966, y=[7, 7, 9, 7, 7]
2025-08-01 10:25:04,145 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:04,150 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:04,165 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4453
2025-08-01 10:25:04,166 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:04,166 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 7, 3]
2025-08-01 10:25:04,175 - ERROR - [Trainer 5] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:04,176 - ERROR - [Trainer 5] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:04,176 - ERROR - [Client 5] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:04,176 - ERROR - [Client 5] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:04,177 - INFO - 客户端 5 训练完成
2025-08-01 10:25:04,854 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:04,854 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 10:25:04,854 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 10:25:04,854 - INFO - [Client 6] 开始验证训练集
2025-08-01 10:25:04,855 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:04,855 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:04,855 - INFO - [Trainer 6] 开始训练
2025-08-01 10:25:04,855 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 10:25:04,856 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 10:25:04,856 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:04,856 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:04,856 - INFO - [Trainer 6] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:04,857 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 10:25:04,862 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=0.0033, y=[8, 0, 0, 8, 7]
2025-08-01 10:25:04,862 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:04,866 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:04,870 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1409
2025-08-01 10:25:04,870 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:04,870 - INFO - [Trainer 6] 标签样本: [8, 7, 0, 0, 0]
2025-08-01 10:25:04,879 - ERROR - [Trainer 6] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:04,879 - ERROR - [Trainer 6] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:04,880 - ERROR - [Client 6] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:04,880 - ERROR - [Client 6] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:04,880 - INFO - 客户端 6 训练完成
2025-08-01 10:25:04,917 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:04,917 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:05,930 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:05,930 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:05,992 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:05,993 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 10:25:05,994 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 10:25:05,994 - INFO - [Client 9] 开始验证训练集
2025-08-01 10:25:05,995 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:05,996 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:05,996 - INFO - [Trainer 9] 开始训练
2025-08-01 10:25:05,996 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 10:25:05,997 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:25:05,997 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:05,997 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:05,998 - INFO - [Trainer 9] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:05,998 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 10:25:06,006 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2659, y=[6, 6, 7, 8, 6]
2025-08-01 10:25:06,006 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:06,010 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:06,024 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5193
2025-08-01 10:25:06,024 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:06,025 - INFO - [Trainer 9] 标签样本: [8, 6, 8, 6, 6]
2025-08-01 10:25:06,033 - ERROR - [Trainer 9] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:06,034 - ERROR - [Trainer 9] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:06,034 - ERROR - [Client 9] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:06,034 - ERROR - [Client 9] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:06,035 - INFO - 客户端 9 训练完成
2025-08-01 10:25:06,930 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:06,930 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:07,945 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:07,945 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:08,959 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:08,960 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:09,974 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:09,974 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:10,990 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:10,990 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:11,996 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:11,996 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:13,010 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:13,011 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:14,025 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:14,025 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:15,034 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:15,034 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:16,043 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:16,044 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:17,057 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:17,057 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:17,104 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:17,104 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:25:17,104 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:25:17,105 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:25:17,106 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:17,106 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:17,106 - INFO - [Trainer 10] 开始训练
2025-08-01 10:25:17,106 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:25:17,106 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:25:17,107 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:17,107 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:17,107 - INFO - [Trainer 10] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:17,107 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:25:17,113 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2260, y=[7, 6, 8, 8, 8]
2025-08-01 10:25:17,113 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:17,118 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:17,136 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1561
2025-08-01 10:25:17,137 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:17,137 - INFO - [Trainer 10] 标签样本: [8, 8, 6, 8, 8]
2025-08-01 10:25:17,147 - ERROR - [Trainer 10] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:17,148 - ERROR - [Trainer 10] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:17,148 - ERROR - [Client 10] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:17,148 - ERROR - [Client 10] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:17,149 - INFO - 客户端 10 训练完成
2025-08-01 10:25:18,072 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:18,072 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:19,082 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:19,082 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:20,091 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:20,092 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:20,960 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:20,961 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 10:25:20,961 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 10:25:20,961 - INFO - [Client 8] 开始验证训练集
2025-08-01 10:25:20,963 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:20,963 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:20,963 - INFO - [Trainer 8] 开始训练
2025-08-01 10:25:20,964 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 10:25:20,964 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:25:20,964 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:20,965 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:20,965 - INFO - [Trainer 8] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:20,966 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 10:25:20,973 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2832, y=[1, 6, 0, 1, 4]
2025-08-01 10:25:20,974 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:20,978 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:20,991 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3560
2025-08-01 10:25:20,992 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:20,992 - INFO - [Trainer 8] 标签样本: [0, 0, 0, 1, 1]
2025-08-01 10:25:21,001 - ERROR - [Trainer 8] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:21,002 - ERROR - [Trainer 8] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:21,002 - ERROR - [Client 8] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:21,002 - ERROR - [Client 8] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:21,003 - INFO - 客户端 8 训练完成
2025-08-01 10:25:21,100 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:21,101 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:22,115 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:22,115 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:22,131 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:22,132 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 10:25:22,132 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 10:25:22,132 - INFO - [Client 3] 开始验证训练集
2025-08-01 10:25:22,134 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:22,134 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:22,134 - INFO - [Trainer 3] 开始训练
2025-08-01 10:25:22,134 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 10:25:22,134 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:25:22,135 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:22,135 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:22,135 - INFO - [Trainer 3] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:22,135 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 10:25:22,141 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4567, y=[2, 2, 2, 6, 2]
2025-08-01 10:25:22,141 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:22,146 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:22,161 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3516
2025-08-01 10:25:22,161 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:22,162 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:25:22,171 - ERROR - [Trainer 3] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:22,172 - ERROR - [Trainer 3] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:22,172 - ERROR - [Client 3] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:22,172 - ERROR - [Client 3] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:22,173 - INFO - 客户端 3 训练完成
2025-08-01 10:25:23,128 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:23,128 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:24,138 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:24,138 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:25,150 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:25,151 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:26,165 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:26,166 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:27,180 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:27,180 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:27,615 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:27,616 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 10:25:27,616 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 10:25:27,617 - INFO - [Client 7] 开始验证训练集
2025-08-01 10:25:27,618 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:27,618 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:27,618 - INFO - [Trainer 7] 开始训练
2025-08-01 10:25:27,618 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 10:25:27,619 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:25:27,619 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:27,620 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:27,621 - INFO - [Trainer 7] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:27,621 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 10:25:27,628 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3942, y=[2, 2, 2, 2, 5]
2025-08-01 10:25:27,629 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:27,633 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:27,647 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3809
2025-08-01 10:25:27,647 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:27,647 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:25:27,657 - ERROR - [Trainer 7] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:27,658 - ERROR - [Trainer 7] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:27,658 - ERROR - [Client 7] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:27,658 - ERROR - [Client 7] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:27,659 - INFO - 客户端 7 训练完成
2025-08-01 10:25:28,193 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:28,193 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:29,207 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:29,207 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:30,220 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:30,221 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:31,235 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:31,235 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:32,249 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:32,249 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:32,950 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:32,950 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 10:25:32,951 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 10:25:32,951 - INFO - [Client 1] 开始验证训练集
2025-08-01 10:25:32,952 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:32,952 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:32,953 - INFO - [Trainer 1] 开始训练
2025-08-01 10:25:32,953 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 10:25:32,954 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 10:25:32,954 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:32,954 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:32,955 - INFO - [Trainer 1] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:32,955 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 10:25:32,960 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1742, y=[5, 5, 5, 5, 5]
2025-08-01 10:25:32,960 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:32,964 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:32,980 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1470
2025-08-01 10:25:32,980 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:32,981 - INFO - [Trainer 1] 标签样本: [5, 0, 8, 0, 0]
2025-08-01 10:25:32,991 - ERROR - [Trainer 1] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:32,991 - ERROR - [Trainer 1] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:32,992 - ERROR - [Client 1] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:32,992 - ERROR - [Client 1] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:32,992 - INFO - 客户端 1 训练完成
2025-08-01 10:25:33,259 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:33,259 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:34,275 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:34,276 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:35,288 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:35,288 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:36,222 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:36,224 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 10:25:36,225 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 10:25:36,225 - INFO - [Client 5] 开始验证训练集
2025-08-01 10:25:36,226 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:36,227 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:36,227 - INFO - [Trainer 5] 开始训练
2025-08-01 10:25:36,228 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 10:25:36,228 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 10:25:36,229 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:36,229 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:36,230 - INFO - [Trainer 5] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:36,230 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 10:25:36,237 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4164, y=[7, 7, 7, 7, 7]
2025-08-01 10:25:36,237 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:36,242 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:36,254 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4211
2025-08-01 10:25:36,254 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:36,255 - INFO - [Trainer 5] 标签样本: [7, 3, 7, 7, 7]
2025-08-01 10:25:36,264 - ERROR - [Trainer 5] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:36,265 - ERROR - [Trainer 5] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:36,265 - ERROR - [Client 5] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:36,265 - ERROR - [Client 5] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:36,266 - INFO - 客户端 5 训练完成
2025-08-01 10:25:36,301 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:36,301 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:37,316 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:37,317 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:38,330 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:38,330 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:39,331 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:39,332 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:40,345 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:40,345 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:41,357 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:41,357 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:42,367 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:42,367 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:43,383 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:43,384 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:43,523 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:43,523 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 10:25:43,523 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 10:25:43,524 - INFO - [Client 4] 开始验证训练集
2025-08-01 10:25:43,524 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:43,524 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:43,524 - INFO - [Trainer 4] 开始训练
2025-08-01 10:25:43,525 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 10:25:43,525 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:25:43,525 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:43,525 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:43,525 - INFO - [Trainer 4] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:43,525 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 10:25:43,530 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1301, y=[0, 0, 0, 1, 1]
2025-08-01 10:25:43,530 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:43,535 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:43,538 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1856
2025-08-01 10:25:43,539 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:43,539 - INFO - [Trainer 4] 标签样本: [5, 1, 1, 0, 1]
2025-08-01 10:25:43,550 - ERROR - [Trainer 4] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:43,550 - ERROR - [Trainer 4] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:43,550 - ERROR - [Client 4] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:43,551 - ERROR - [Client 4] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:43,552 - INFO - 客户端 4 训练完成
2025-08-01 10:25:44,394 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:44,394 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:45,206 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:45,206 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 10:25:45,206 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 10:25:45,206 - INFO - [Client 2] 开始验证训练集
2025-08-01 10:25:45,207 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:45,207 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:45,207 - INFO - [Trainer 2] 开始训练
2025-08-01 10:25:45,207 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 10:25:45,207 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 10:25:45,207 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:45,207 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:45,208 - INFO - [Trainer 2] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:45,208 - INFO - [Trainer 2] 开始第 1/2 个epoch
2025-08-01 10:25:45,212 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1768, y=[8, 8, 8, 8, 8]
2025-08-01 10:25:45,213 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:45,217 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:45,222 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0712
2025-08-01 10:25:45,222 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:45,222 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:25:45,232 - ERROR - [Trainer 2] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:45,232 - ERROR - [Trainer 2] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:45,232 - ERROR - [Client 2] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:45,233 - ERROR - [Client 2] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:45,233 - INFO - 客户端 2 训练完成
2025-08-01 10:25:45,408 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:45,409 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:46,411 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:46,412 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:47,419 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:47,419 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:47,513 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:47,530 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 10:25:47,530 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 10:25:47,530 - INFO - [Client 9] 开始验证训练集
2025-08-01 10:25:47,531 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:47,531 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:47,531 - INFO - [Trainer 9] 开始训练
2025-08-01 10:25:47,531 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 10:25:47,531 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:25:47,532 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:47,532 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:47,532 - INFO - [Trainer 9] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:47,532 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 10:25:47,537 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4455, y=[7, 6, 6, 8, 8]
2025-08-01 10:25:47,537 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:47,541 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:47,545 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3211
2025-08-01 10:25:47,545 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:47,545 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 8, 6]
2025-08-01 10:25:47,554 - ERROR - [Trainer 9] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:47,555 - ERROR - [Trainer 9] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:47,555 - ERROR - [Client 9] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:47,556 - ERROR - [Client 9] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:47,556 - INFO - 客户端 9 训练完成
2025-08-01 10:25:48,420 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:48,421 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:48,748 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:48,749 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:25:48,750 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:25:48,750 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:25:48,752 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:48,752 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:48,752 - INFO - [Trainer 10] 开始训练
2025-08-01 10:25:48,752 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:25:48,752 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:25:48,754 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:48,754 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:48,754 - INFO - [Trainer 10] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:48,755 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:25:48,762 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2183, y=[0, 8, 8, 8, 8]
2025-08-01 10:25:48,762 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:48,766 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:48,779 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1814
2025-08-01 10:25:48,780 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:48,780 - INFO - [Trainer 10] 标签样本: [8, 8, 8, 8, 6]
2025-08-01 10:25:48,791 - ERROR - [Trainer 10] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:48,791 - ERROR - [Trainer 10] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:48,791 - ERROR - [Client 10] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:48,792 - ERROR - [Client 10] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:48,792 - INFO - 客户端 10 训练完成
2025-08-01 10:25:49,436 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:49,437 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:49,795 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:49,796 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 10:25:49,797 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 10:25:49,797 - INFO - [Client 3] 开始验证训练集
2025-08-01 10:25:49,798 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:49,798 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:49,798 - INFO - [Trainer 3] 开始训练
2025-08-01 10:25:49,798 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 10:25:49,798 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:25:49,799 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:49,799 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:49,799 - INFO - [Trainer 3] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:49,799 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 10:25:49,805 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2533, y=[6, 2, 2, 2, 2]
2025-08-01 10:25:49,806 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:49,810 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:49,826 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3503
2025-08-01 10:25:49,826 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:49,826 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 7, 2]
2025-08-01 10:25:49,835 - ERROR - [Trainer 3] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:49,836 - ERROR - [Trainer 3] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:49,836 - ERROR - [Client 3] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:49,836 - ERROR - [Client 3] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:49,836 - INFO - 客户端 3 训练完成
2025-08-01 10:25:50,451 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:50,452 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:51,464 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:51,465 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:52,057 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:52,058 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 10:25:52,058 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 10:25:52,059 - INFO - [Client 6] 开始验证训练集
2025-08-01 10:25:52,060 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:52,060 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:52,060 - INFO - [Trainer 6] 开始训练
2025-08-01 10:25:52,060 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 10:25:52,061 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 10:25:52,061 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:52,061 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:52,062 - INFO - [Trainer 6] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:52,062 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 10:25:52,068 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0583, y=[0, 7, 7, 0, 0]
2025-08-01 10:25:52,068 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:52,073 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:52,088 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0902
2025-08-01 10:25:52,088 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:52,088 - INFO - [Trainer 6] 标签样本: [7, 0, 7, 7, 8]
2025-08-01 10:25:52,098 - ERROR - [Trainer 6] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:52,098 - ERROR - [Trainer 6] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:52,098 - ERROR - [Client 6] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:52,099 - ERROR - [Client 6] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:52,099 - INFO - 客户端 6 训练完成
2025-08-01 10:25:52,477 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:52,478 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:53,489 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:53,490 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:54,503 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:54,504 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:55,376 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:25:55,376 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 10:25:55,377 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 10:25:55,377 - INFO - [Client 7] 开始验证训练集
2025-08-01 10:25:55,378 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:25:55,378 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 10:25:55,379 - INFO - [Trainer 7] 开始训练
2025-08-01 10:25:55,379 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 10:25:55,379 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:25:55,379 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:25:55,380 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:25:55,380 - INFO - [Trainer 7] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:25:55,380 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 10:25:55,388 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2667, y=[2, 2, 2, 2, 2]
2025-08-01 10:25:55,388 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 10:25:55,392 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 10:25:55,407 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3847
2025-08-01 10:25:55,407 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:25:55,407 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:25:55,416 - ERROR - [Trainer 7] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:55,417 - ERROR - [Trainer 7] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:55,417 - ERROR - [Client 7] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:25:55,417 - ERROR - [Client 7] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:25:55,418 - INFO - 客户端 7 训练完成
2025-08-01 10:25:55,515 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:55,515 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:56,530 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:56,530 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:57,541 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:57,541 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:58,557 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:58,557 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:59,568 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:25:59,568 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:00,565 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:00,566 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:01,576 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:01,576 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:02,578 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:02,580 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:03,593 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:03,593 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:04,605 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:04,606 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:05,447 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:05,448 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 10:26:05,449 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 10:26:05,449 - INFO - [Client 8] 开始验证训练集
2025-08-01 10:26:05,450 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:05,451 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:05,451 - INFO - [Trainer 8] 开始训练
2025-08-01 10:26:05,451 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 10:26:05,452 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:26:05,452 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:05,453 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:05,453 - INFO - [Trainer 8] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:05,453 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 10:26:05,460 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1576, y=[0, 1, 1, 0, 1]
2025-08-01 10:26:05,460 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:05,466 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:05,478 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2877
2025-08-01 10:26:05,478 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:05,478 - INFO - [Trainer 8] 标签样本: [6, 0, 1, 1, 0]
2025-08-01 10:26:05,489 - ERROR - [Trainer 8] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:05,489 - ERROR - [Trainer 8] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:05,489 - ERROR - [Client 8] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:05,490 - ERROR - [Client 8] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:05,490 - INFO - 客户端 8 训练完成
2025-08-01 10:26:05,619 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:05,620 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:06,633 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:06,633 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:07,646 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:07,646 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:08,658 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:08,658 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:09,668 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:09,668 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:10,683 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:10,683 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:11,670 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:11,671 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 10:26:11,671 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 10:26:11,672 - INFO - [Client 1] 开始验证训练集
2025-08-01 10:26:11,673 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:11,674 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:11,674 - INFO - [Trainer 1] 开始训练
2025-08-01 10:26:11,674 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 10:26:11,675 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 10:26:11,675 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:11,676 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:11,677 - INFO - [Trainer 1] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:11,677 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 10:26:11,682 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0847, y=[0, 8, 5, 4, 8]
2025-08-01 10:26:11,683 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:11,686 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:11,686 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:11,688 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:11,701 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1672
2025-08-01 10:26:11,701 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:11,701 - INFO - [Trainer 1] 标签样本: [5, 5, 8, 5, 8]
2025-08-01 10:26:11,711 - ERROR - [Trainer 1] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:11,711 - ERROR - [Trainer 1] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:11,712 - ERROR - [Client 1] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:11,712 - ERROR - [Client 1] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:11,712 - INFO - 客户端 1 训练完成
2025-08-01 10:26:12,698 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:12,699 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:13,710 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:13,710 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:13,803 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:13,804 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:26:13,804 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:26:13,804 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:26:13,805 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:13,805 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:13,805 - INFO - [Trainer 10] 开始训练
2025-08-01 10:26:13,805 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:26:13,806 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:26:13,806 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:13,806 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:13,807 - INFO - [Trainer 10] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:13,807 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:26:13,815 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2440, y=[8, 6, 8, 8, 8]
2025-08-01 10:26:13,815 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:13,820 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:13,835 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2436
2025-08-01 10:26:13,835 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:13,835 - INFO - [Trainer 10] 标签样本: [8, 8, 6, 8, 8]
2025-08-01 10:26:13,845 - ERROR - [Trainer 10] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:13,846 - ERROR - [Trainer 10] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:13,846 - ERROR - [Client 10] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:13,847 - ERROR - [Client 10] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:13,847 - INFO - 客户端 10 训练完成
2025-08-01 10:26:14,723 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:14,724 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:14,990 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:14,990 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 10:26:14,990 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 10:26:14,990 - INFO - [Client 3] 开始验证训练集
2025-08-01 10:26:14,991 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:14,991 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:14,991 - INFO - [Trainer 3] 开始训练
2025-08-01 10:26:14,991 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 10:26:14,991 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:26:14,992 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:14,992 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:14,992 - INFO - [Trainer 3] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:14,992 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 10:26:14,997 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4119, y=[6, 2, 2, 2, 2]
2025-08-01 10:26:14,997 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:15,002 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:15,006 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5119
2025-08-01 10:26:15,007 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:15,008 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:26:15,018 - ERROR - [Trainer 3] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:15,019 - ERROR - [Trainer 3] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:15,019 - ERROR - [Client 3] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:15,020 - ERROR - [Client 3] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:15,020 - INFO - 客户端 3 训练完成
2025-08-01 10:26:15,736 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:15,736 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:16,747 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:16,748 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:17,755 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:17,755 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:17,755 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:17,755 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 10:26:17,755 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 10:26:17,756 - INFO - [Client 9] 开始验证训练集
2025-08-01 10:26:17,756 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:17,757 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:17,757 - INFO - [Trainer 9] 开始训练
2025-08-01 10:26:17,757 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 10:26:17,757 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:26:17,757 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:17,758 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:17,758 - INFO - [Trainer 9] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:17,758 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 10:26:17,763 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4666, y=[8, 8, 8, 6, 6]
2025-08-01 10:26:17,763 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:17,768 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:17,771 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4137
2025-08-01 10:26:17,771 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:17,771 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 6, 6]
2025-08-01 10:26:17,781 - ERROR - [Trainer 9] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:17,782 - ERROR - [Trainer 9] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:17,782 - ERROR - [Client 9] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:17,782 - ERROR - [Client 9] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:17,783 - INFO - 客户端 9 训练完成
2025-08-01 10:26:18,761 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:18,762 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:18,978 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:18,979 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 10:26:18,979 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 10:26:18,980 - INFO - [Client 7] 开始验证训练集
2025-08-01 10:26:18,982 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:18,982 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:18,983 - INFO - [Trainer 7] 开始训练
2025-08-01 10:26:18,983 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 10:26:18,984 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:26:18,984 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:18,985 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:18,985 - INFO - [Trainer 7] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:18,985 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 10:26:18,990 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3308, y=[2, 2, 5, 2, 2]
2025-08-01 10:26:18,990 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:18,995 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:19,010 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2076
2025-08-01 10:26:19,010 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:19,010 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:26:19,019 - ERROR - [Trainer 7] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:19,020 - ERROR - [Trainer 7] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:19,020 - ERROR - [Client 7] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:19,020 - ERROR - [Client 7] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:19,021 - INFO - 客户端 7 训练完成
2025-08-01 10:26:19,768 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:19,768 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:20,781 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:20,783 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:21,795 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:21,796 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:22,811 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:22,811 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:23,823 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:23,824 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:24,835 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:24,835 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:25,852 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:25,853 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:26,861 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:26,862 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:27,875 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:27,876 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:28,217 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:28,218 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 10:26:28,218 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 10:26:28,219 - INFO - [Client 2] 开始验证训练集
2025-08-01 10:26:28,220 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:28,220 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:28,221 - INFO - [Trainer 2] 开始训练
2025-08-01 10:26:28,221 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 10:26:28,222 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 10:26:28,222 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:28,222 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:28,223 - INFO - [Trainer 2] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:28,223 - INFO - [Trainer 2] 开始第 1/2 个epoch
2025-08-01 10:26:28,231 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1565, y=[8, 8, 8, 8, 8]
2025-08-01 10:26:28,231 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:28,235 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:28,248 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2540
2025-08-01 10:26:28,248 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:28,248 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:26:28,257 - ERROR - [Trainer 2] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:28,258 - ERROR - [Trainer 2] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:28,258 - ERROR - [Client 2] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:28,258 - ERROR - [Client 2] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:28,259 - INFO - 客户端 2 训练完成
2025-08-01 10:26:28,884 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:28,885 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:29,895 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:29,896 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:30,332 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:30,332 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 10:26:30,332 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 10:26:30,332 - INFO - [Client 5] 开始验证训练集
2025-08-01 10:26:30,333 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:30,333 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:30,333 - INFO - [Trainer 5] 开始训练
2025-08-01 10:26:30,333 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 10:26:30,333 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 10:26:30,334 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:30,334 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:30,334 - INFO - [Trainer 5] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:30,334 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 10:26:30,339 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4266, y=[9, 7, 7, 7, 7]
2025-08-01 10:26:30,339 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:30,343 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:30,348 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4848
2025-08-01 10:26:30,348 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:30,348 - INFO - [Trainer 5] 标签样本: [7, 3, 7, 7, 7]
2025-08-01 10:26:30,358 - ERROR - [Trainer 5] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:30,358 - ERROR - [Trainer 5] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:30,359 - ERROR - [Client 5] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:30,359 - ERROR - [Client 5] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:30,359 - INFO - 客户端 5 训练完成
2025-08-01 10:26:30,644 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:30,645 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 10:26:30,646 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 10:26:30,647 - INFO - [Client 4] 开始验证训练集
2025-08-01 10:26:30,648 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:30,649 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:30,649 - INFO - [Trainer 4] 开始训练
2025-08-01 10:26:30,650 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 10:26:30,650 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:26:30,651 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:30,652 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:30,652 - INFO - [Trainer 4] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:30,653 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 10:26:30,660 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3085, y=[1, 0, 0, 1, 0]
2025-08-01 10:26:30,660 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:30,665 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:30,676 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1232
2025-08-01 10:26:30,676 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:30,677 - INFO - [Trainer 4] 标签样本: [0, 0, 0, 1, 0]
2025-08-01 10:26:30,687 - ERROR - [Trainer 4] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:30,688 - ERROR - [Trainer 4] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:30,688 - ERROR - [Client 4] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:30,688 - ERROR - [Client 4] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:30,689 - INFO - 客户端 4 训练完成
2025-08-01 10:26:30,908 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:30,909 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:31,924 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:31,925 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:31,954 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:31,954 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 10:26:31,954 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 10:26:31,955 - INFO - [Client 1] 开始验证训练集
2025-08-01 10:26:31,955 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:31,955 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:31,955 - INFO - [Trainer 1] 开始训练
2025-08-01 10:26:31,956 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 10:26:31,956 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 10:26:31,956 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:31,956 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:31,957 - INFO - [Trainer 1] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:31,957 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 10:26:31,962 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2789, y=[8, 8, 5, 8, 5]
2025-08-01 10:26:31,962 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:31,966 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:31,984 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3571
2025-08-01 10:26:31,984 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:31,984 - INFO - [Trainer 1] 标签样本: [8, 4, 5, 5, 5]
2025-08-01 10:26:31,994 - ERROR - [Trainer 1] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:31,995 - ERROR - [Trainer 1] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:31,995 - ERROR - [Client 1] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:31,995 - ERROR - [Client 1] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:31,996 - INFO - 客户端 1 训练完成
2025-08-01 10:26:32,933 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:32,934 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:33,944 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:33,945 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:34,953 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:34,954 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:35,959 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:35,960 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:36,974 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:36,975 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:37,977 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:37,978 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:38,993 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:38,994 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:40,003 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:40,003 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:40,004 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:40,005 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 10:26:40,005 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 10:26:40,006 - INFO - [Client 7] 开始验证训练集
2025-08-01 10:26:40,007 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:40,007 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:40,008 - INFO - [Trainer 7] 开始训练
2025-08-01 10:26:40,008 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 10:26:40,009 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:26:40,009 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:40,009 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:40,010 - INFO - [Trainer 7] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:40,010 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 10:26:40,017 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1978, y=[2, 2, 2, 2, 2]
2025-08-01 10:26:40,017 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:40,021 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:40,035 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3868
2025-08-01 10:26:40,035 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:40,035 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:26:40,045 - ERROR - [Trainer 7] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:40,045 - ERROR - [Trainer 7] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:40,045 - ERROR - [Client 7] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:40,045 - ERROR - [Client 7] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:40,046 - INFO - 客户端 7 训练完成
2025-08-01 10:26:41,015 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:41,015 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:42,016 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:42,017 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:42,375 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:42,376 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 10:26:42,376 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 10:26:42,376 - INFO - [Client 6] 开始验证训练集
2025-08-01 10:26:42,378 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:42,379 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:42,379 - INFO - [Trainer 6] 开始训练
2025-08-01 10:26:42,380 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 10:26:42,380 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 10:26:42,381 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:42,382 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:42,383 - INFO - [Trainer 6] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:42,383 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 10:26:42,389 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1986, y=[7, 8, 0, 6, 8]
2025-08-01 10:26:42,389 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:42,394 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:42,406 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2739
2025-08-01 10:26:42,407 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:42,407 - INFO - [Trainer 6] 标签样本: [0, 0, 8, 0, 8]
2025-08-01 10:26:42,415 - ERROR - [Trainer 6] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:42,416 - ERROR - [Trainer 6] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:42,416 - ERROR - [Client 6] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:42,416 - ERROR - [Client 6] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:42,417 - INFO - 客户端 6 训练完成
2025-08-01 10:26:43,029 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:43,030 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:44,046 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:44,046 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:45,062 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:45,062 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:45,439 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:45,440 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 10:26:45,441 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 10:26:45,441 - INFO - [Client 3] 开始验证训练集
2025-08-01 10:26:45,442 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:45,443 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:45,443 - INFO - [Trainer 3] 开始训练
2025-08-01 10:26:45,444 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 10:26:45,444 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:26:45,445 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:45,445 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:45,445 - INFO - [Trainer 3] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:45,446 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 10:26:45,453 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2552, y=[2, 2, 2, 2, 2]
2025-08-01 10:26:45,453 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:45,457 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:45,470 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3230
2025-08-01 10:26:45,470 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:45,470 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:26:45,479 - ERROR - [Trainer 3] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:45,480 - ERROR - [Trainer 3] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:45,480 - ERROR - [Client 3] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:45,480 - ERROR - [Client 3] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:45,481 - INFO - 客户端 3 训练完成
2025-08-01 10:26:46,062 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:46,062 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:47,076 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:47,076 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:47,801 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:26:47,802 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 10:26:47,802 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 10:26:47,802 - INFO - [Client 8] 开始验证训练集
2025-08-01 10:26:47,803 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:26:47,803 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 10:26:47,803 - INFO - [Trainer 8] 开始训练
2025-08-01 10:26:47,803 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 10:26:47,804 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:26:47,804 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:26:47,804 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:26:47,805 - INFO - [Trainer 8] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:26:47,805 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 10:26:47,812 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2855, y=[0, 0, 1, 1, 6]
2025-08-01 10:26:47,812 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 10:26:47,817 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 10:26:47,832 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0467
2025-08-01 10:26:47,832 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:26:47,832 - INFO - [Trainer 8] 标签样本: [0, 1, 0, 1, 0]
2025-08-01 10:26:47,842 - ERROR - [Trainer 8] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:47,842 - ERROR - [Trainer 8] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:47,842 - ERROR - [Client 8] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:26:47,842 - ERROR - [Client 8] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:26:47,842 - INFO - 客户端 8 训练完成
2025-08-01 10:26:48,082 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:48,082 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:49,092 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:49,092 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:50,100 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:50,100 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:51,111 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:51,111 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:52,123 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:52,123 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:53,134 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:53,135 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:54,148 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:54,148 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:55,157 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:55,157 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:56,171 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:56,171 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:57,180 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:57,180 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:58,193 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:58,193 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:59,207 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:26:59,207 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:00,220 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:00,220 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:01,235 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:01,235 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:02,246 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:02,246 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:03,249 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:03,249 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:04,263 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:04,263 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:04,371 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:04,371 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:27:04,371 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:27:04,371 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:27:04,372 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:04,372 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:04,372 - INFO - [Trainer 10] 开始训练
2025-08-01 10:27:04,372 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:27:04,373 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:27:04,373 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:04,373 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:04,374 - INFO - [Trainer 10] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:04,374 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:27:04,379 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2808, y=[8, 8, 6, 8, 7]
2025-08-01 10:27:04,379 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:04,385 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:04,389 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3893
2025-08-01 10:27:04,389 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:04,389 - INFO - [Trainer 10] 标签样本: [8, 6, 8, 7, 8]
2025-08-01 10:27:04,399 - ERROR - [Trainer 10] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:04,401 - ERROR - [Trainer 10] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:04,401 - ERROR - [Client 10] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:04,401 - ERROR - [Client 10] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:04,401 - INFO - 客户端 10 训练完成
2025-08-01 10:27:05,274 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:05,274 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:05,910 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:05,911 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 10:27:05,911 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 10:27:05,911 - INFO - [Client 7] 开始验证训练集
2025-08-01 10:27:05,912 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:05,912 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:05,912 - INFO - [Trainer 7] 开始训练
2025-08-01 10:27:05,912 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 10:27:05,913 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:27:05,913 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:05,913 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:05,913 - INFO - [Trainer 7] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:05,913 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 10:27:05,919 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2301, y=[2, 2, 2, 2, 2]
2025-08-01 10:27:05,920 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:05,924 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:05,941 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3121
2025-08-01 10:27:05,942 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:05,942 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:27:05,952 - ERROR - [Trainer 7] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:05,952 - ERROR - [Trainer 7] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:05,953 - ERROR - [Client 7] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:05,953 - ERROR - [Client 7] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:05,954 - INFO - 客户端 7 训练完成
2025-08-01 10:27:06,287 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:06,287 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:07,300 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:07,300 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:08,024 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:08,040 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 10:27:08,040 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 10:27:08,041 - INFO - [Client 5] 开始验证训练集
2025-08-01 10:27:08,041 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:08,042 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:08,042 - INFO - [Trainer 5] 开始训练
2025-08-01 10:27:08,042 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 10:27:08,042 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 10:27:08,042 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:08,043 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:08,043 - INFO - [Trainer 5] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:08,043 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 10:27:08,048 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4332, y=[3, 7, 3, 7, 7]
2025-08-01 10:27:08,048 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:08,052 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:08,055 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3712
2025-08-01 10:27:08,055 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:08,056 - INFO - [Trainer 5] 标签样本: [7, 7, 3, 7, 7]
2025-08-01 10:27:08,070 - ERROR - [Trainer 5] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:08,070 - ERROR - [Trainer 5] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:08,071 - ERROR - [Client 5] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:08,071 - ERROR - [Client 5] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:08,072 - INFO - 客户端 5 训练完成
2025-08-01 10:27:08,304 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:08,304 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:09,319 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:09,319 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:10,333 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:10,333 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:11,345 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:11,349 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:11,594 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:11,595 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 10:27:11,596 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 10:27:11,596 - INFO - [Client 2] 开始验证训练集
2025-08-01 10:27:11,597 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:11,598 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:11,598 - INFO - [Trainer 2] 开始训练
2025-08-01 10:27:11,598 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 10:27:11,598 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 10:27:11,598 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:11,599 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:11,599 - INFO - [Trainer 2] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:11,599 - INFO - [Trainer 2] 开始第 1/2 个epoch
2025-08-01 10:27:11,605 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1600, y=[8, 8, 8, 8, 8]
2025-08-01 10:27:11,606 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:11,611 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:11,624 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1661
2025-08-01 10:27:11,624 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:11,624 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:27:11,634 - ERROR - [Trainer 2] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:11,635 - ERROR - [Trainer 2] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:11,635 - ERROR - [Client 2] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:11,636 - ERROR - [Client 2] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:11,636 - INFO - 客户端 2 训练完成
2025-08-01 10:27:12,312 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:12,312 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 10:27:12,312 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 10:27:12,312 - INFO - [Client 9] 开始验证训练集
2025-08-01 10:27:12,313 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:12,313 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:12,313 - INFO - [Trainer 9] 开始训练
2025-08-01 10:27:12,313 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 10:27:12,314 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:27:12,314 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:12,314 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:12,314 - INFO - [Trainer 9] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:12,314 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 10:27:12,319 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3337, y=[6, 6, 8, 6, 6]
2025-08-01 10:27:12,319 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:12,323 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:12,328 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3165
2025-08-01 10:27:12,328 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:12,328 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 8, 8]
2025-08-01 10:27:12,339 - ERROR - [Trainer 9] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:12,340 - ERROR - [Trainer 9] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:12,340 - ERROR - [Client 9] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:12,340 - ERROR - [Client 9] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:12,341 - INFO - 客户端 9 训练完成
2025-08-01 10:27:12,360 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:12,360 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:13,374 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:13,374 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:14,387 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:14,387 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:15,391 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:15,391 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:15,886 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:15,886 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 10:27:15,886 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 10:27:15,886 - INFO - [Client 4] 开始验证训练集
2025-08-01 10:27:15,887 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:15,887 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:15,887 - INFO - [Trainer 4] 开始训练
2025-08-01 10:27:15,888 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 10:27:15,888 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:27:15,888 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:15,888 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:15,889 - INFO - [Trainer 4] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:15,889 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 10:27:15,894 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3145, y=[5, 1, 1, 5, 1]
2025-08-01 10:27:15,894 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:15,898 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:15,902 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2287
2025-08-01 10:27:15,902 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:15,902 - INFO - [Trainer 4] 标签样本: [5, 5, 1, 0, 1]
2025-08-01 10:27:15,912 - ERROR - [Trainer 4] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:15,912 - ERROR - [Trainer 4] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:15,912 - ERROR - [Client 4] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:15,912 - ERROR - [Client 4] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:15,913 - INFO - 客户端 4 训练完成
2025-08-01 10:27:16,402 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:16,402 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:16,666 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:16,666 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 10:27:16,666 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 10:27:16,666 - INFO - [Client 8] 开始验证训练集
2025-08-01 10:27:16,667 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:16,667 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:16,668 - INFO - [Trainer 8] 开始训练
2025-08-01 10:27:16,668 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 10:27:16,669 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:27:16,669 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:16,669 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:16,669 - INFO - [Trainer 8] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:16,669 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 10:27:16,675 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3686, y=[1, 6, 6, 1, 0]
2025-08-01 10:27:16,675 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:16,680 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:16,699 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3382
2025-08-01 10:27:16,699 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:16,700 - INFO - [Trainer 8] 标签样本: [0, 1, 1, 1, 7]
2025-08-01 10:27:16,712 - ERROR - [Trainer 8] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:16,712 - ERROR - [Trainer 8] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:16,713 - ERROR - [Client 8] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:16,713 - ERROR - [Client 8] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:16,713 - INFO - 客户端 8 训练完成
2025-08-01 10:27:16,888 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:16,888 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 10:27:16,888 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 10:27:16,888 - INFO - [Client 6] 开始验证训练集
2025-08-01 10:27:16,889 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:16,890 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:16,890 - INFO - [Trainer 6] 开始训练
2025-08-01 10:27:16,890 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 10:27:16,890 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 10:27:16,891 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:16,891 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:16,891 - INFO - [Trainer 6] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:16,891 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 10:27:16,897 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0900, y=[7, 0, 7, 8, 7]
2025-08-01 10:27:16,897 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:16,901 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:16,919 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0450
2025-08-01 10:27:16,919 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:16,919 - INFO - [Trainer 6] 标签样本: [0, 7, 8, 6, 0]
2025-08-01 10:27:16,929 - ERROR - [Trainer 6] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:16,930 - ERROR - [Trainer 6] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:16,930 - ERROR - [Client 6] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:16,930 - ERROR - [Client 6] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:16,931 - INFO - 客户端 6 训练完成
2025-08-01 10:27:17,404 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:17,405 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:18,417 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:18,417 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:19,430 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:19,430 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:20,434 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:20,434 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:21,436 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:21,436 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:21,715 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:21,731 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 10:27:21,732 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 10:27:21,732 - INFO - [Client 3] 开始验证训练集
2025-08-01 10:27:21,733 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:21,733 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:21,733 - INFO - [Trainer 3] 开始训练
2025-08-01 10:27:21,733 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 10:27:21,734 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:27:21,734 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:21,734 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:21,734 - INFO - [Trainer 3] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:21,734 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 10:27:21,741 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4009, y=[2, 2, 2, 2, 6]
2025-08-01 10:27:21,742 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:21,746 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:21,762 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2614
2025-08-01 10:27:21,763 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:21,763 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:27:21,773 - ERROR - [Trainer 3] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:21,773 - ERROR - [Trainer 3] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:21,773 - ERROR - [Client 3] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:21,774 - ERROR - [Client 3] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:21,774 - INFO - 客户端 3 训练完成
2025-08-01 10:27:22,449 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:22,449 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:23,461 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:23,461 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:24,474 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:24,474 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:25,485 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:25,485 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:26,498 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:26,498 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:27,513 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:27,513 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:28,525 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:28,525 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:29,530 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:29,530 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:29,998 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:29,999 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 10:27:30,000 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 10:27:30,000 - INFO - [Client 1] 开始验证训练集
2025-08-01 10:27:30,003 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:30,003 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:30,003 - INFO - [Trainer 1] 开始训练
2025-08-01 10:27:30,004 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 10:27:30,004 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 10:27:30,005 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:30,005 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:30,006 - INFO - [Trainer 1] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:30,007 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 10:27:30,014 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2029, y=[5, 4, 8, 5, 0]
2025-08-01 10:27:30,015 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:30,019 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:30,029 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2100
2025-08-01 10:27:30,029 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:30,030 - INFO - [Trainer 1] 标签样本: [5, 8, 0, 0, 0]
2025-08-01 10:27:30,039 - ERROR - [Trainer 1] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:30,040 - ERROR - [Trainer 1] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:30,040 - ERROR - [Client 1] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:30,040 - ERROR - [Client 1] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:30,041 - INFO - 客户端 1 训练完成
2025-08-01 10:27:30,533 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:30,534 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:31,535 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:31,535 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:32,547 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:32,547 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:33,503 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:33,503 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:27:33,503 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:27:33,504 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:27:33,504 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:33,504 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:33,504 - INFO - [Trainer 10] 开始训练
2025-08-01 10:27:33,504 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:27:33,505 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:27:33,505 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:33,505 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:33,505 - INFO - [Trainer 10] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:33,505 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:27:33,511 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1467, y=[6, 8, 8, 8, 8]
2025-08-01 10:27:33,512 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:33,517 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:33,519 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2616
2025-08-01 10:27:33,519 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:33,520 - INFO - [Trainer 10] 标签样本: [8, 8, 8, 8, 6]
2025-08-01 10:27:33,531 - ERROR - [Trainer 10] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:33,532 - ERROR - [Trainer 10] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:33,532 - ERROR - [Client 10] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:33,532 - ERROR - [Client 10] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:33,533 - INFO - 客户端 10 训练完成
2025-08-01 10:27:33,551 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:33,551 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:34,485 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:27:34,485 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 10:27:34,485 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 10:27:34,485 - INFO - [Client 9] 开始验证训练集
2025-08-01 10:27:34,486 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:27:34,486 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 10:27:34,486 - INFO - [Trainer 9] 开始训练
2025-08-01 10:27:34,486 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 10:27:34,486 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:27:34,486 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:27:34,487 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:27:34,487 - INFO - [Trainer 9] 开始训练 2 个epoch，已彻底禁用BatchNorm并重建梯度图
2025-08-01 10:27:34,487 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 10:27:34,492 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4721, y=[6, 6, 6, 6, 8]
2025-08-01 10:27:34,492 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 10:27:34,496 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 10:27:34,501 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4407
2025-08-01 10:27:34,501 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:27:34,501 - INFO - [Trainer 9] 标签样本: [6, 7, 8, 6, 8]
2025-08-01 10:27:34,512 - ERROR - [Trainer 9] 训练过程出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:34,512 - ERROR - [Trainer 9] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:34,512 - ERROR - [Client 9] 训练器训练过程中出错: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)
2025-08-01 10:27:34,513 - ERROR - [Client 9] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 416, in train
    output = self.model(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\plato\models\lenet5.py", line 101, in forward
    x = self.fc4(x)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x480 and 120x84)

2025-08-01 10:27:34,513 - INFO - 客户端 9 训练完成
2025-08-01 10:27:34,563 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:34,563 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:35,578 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:35,579 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:36,581 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:36,581 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:27:37,020 - INFO - 服务器启动完成
