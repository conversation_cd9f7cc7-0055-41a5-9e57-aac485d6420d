"""
A federated learning server using FedBuff.

Reference:

<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al., "Federated Learning with Buffered Asynchronous Aggregation,
" in Proc. International Conference on Artificial Intelligence and Statistics (AISTATS 2022).

https://proceedings.mlr.press/v151/nguyen22b/nguyen22b.pdf
"""
import asyncio
import copy
import os
import logging
import math
import time
import numpy as np
import sys
from datetime import datetime

import torch
import torch.nn.functional as F
from collections import OrderedDict
from plato.utils import fonts
from plato.utils import csv_processor

from plato.config import Config
from plato.servers import fedavg

# 导入复杂网络环境模拟器
try:
    from complex_network_environment import ComplexNetworkEnvironment
    NETWORK_SIMULATION_AVAILABLE = True
except ImportError:
    NETWORK_SIMULATION_AVAILABLE = False
    logging.warning("复杂网络环境模拟器不可用，将使用简单模拟")


class Server(fedavg.Server):
    """A federated learning server using the FedBuff algorithm with network simulation."""

    def __init__(
        self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None
    ):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )

        # 初始化准确率相关变量，避免KeyError
        self.global_accuracy = 0.0
        self.global_accuracy_std = 0.0
        self.local_accuracy = 0.0
        self.local_accuracy_std = 0.0

        # 初始化复杂网络环境模拟器
        if NETWORK_SIMULATION_AVAILABLE and hasattr(Config().clients, 'network_simulation') and Config().clients.network_simulation:
            self.network_env = ComplexNetworkEnvironment(
                num_clients=Config().clients.total_clients
            )
            self.network_simulation_enabled = True
            logging.info("🌐 FedBuff启用复杂网络环境模拟")
        else:
            self.network_env = None
            # 检查是否启用简单网络模拟
            self.network_simulation_enabled = hasattr(Config().clients, 'network_simulation') and Config().clients.network_simulation
            if self.network_simulation_enabled:
                logging.info("📡 FedBuff使用简单网络模拟")
            else:
                logging.info("🔗 FedBuff使用标准网络模式（无模拟）")

        # 网络统计
        self.network_stats = {
            'total_communications': 0,
            'successful_communications': 0,
            'failed_communications': 0,
            'avg_communication_time': 0.0,
            'max_communication_time': 0.0,
            'min_communication_time': float('inf')
        }

        # 陈旧度统计
        self.staleness_stats = {
            'total_staleness': 0.0,
            'total_updates': 0,
            'avg_staleness': 0.0,
            'max_staleness': 0,
            'min_staleness': float('inf'),
            'staleness_history': []  # 记录每轮的陈旧度
        }

        # 设置自定义结果文件命名
        self._setup_custom_result_file()

    async def aggregate_deltas(self, updates, deltas_received):
        """Aggregate weight updates from the clients using federated averaging with network simulation."""
        # Constructing the aggregation weights to be used
        aggregation_weights = []
        valid_deltas = []
        valid_updates = []

        for i, update in enumerate(deltas_received):
            staleness = updates[i].staleness
            client_id = updates[i].client_id

            # 模拟网络通信（如果启用）
            if self.network_simulation_enabled:
                # 估算模型大小（MB）
                model_size_mb = sum(delta.numel() * 4 for delta in update.values()) / (1024 * 1024)  # 假设float32
                success, comm_time, details = self.simulate_client_communication(client_id, model_size_mb)

                # 如果通信失败，跳过该客户端
                if not success:
                    logging.warning(f"[FedBuff] 客户端 {client_id} 通信失败，跳过本轮聚合")
                    continue

                # 记录通信时间到更新报告中（如果需要）
                if hasattr(updates[i], 'communication_time'):
                    updates[i].communication_time = comm_time

            # FedBuff使用简单的平均权重
            aggregation_weights.append(1.0)
            valid_deltas.append(update)
            valid_updates.append(updates[i])

            # 更新陈旧度统计
            self._update_staleness_stats(staleness)

        # 如果没有有效的更新，返回空的更新
        if not aggregation_weights:
            logging.warning("[FedBuff] 没有有效的客户端更新，跳过本轮聚合")
            return {name: self.trainer.zeros(delta.shape) for name, delta in deltas_received[0].items()}

        # 使用有效的deltas进行后续处理
        deltas_received = valid_deltas
        total_updates = len(deltas_received)

        # Perform weighted averaging
        avg_update = {
            name: self.trainer.zeros(delta.shape)
            for name, delta in deltas_received[0].items()
        }

        for update in deltas_received:
            for name, delta in update.items():
                # Use weighted average by the number of samples
                avg_update[name] += delta * (1 / total_updates)

            # Yield to other tasks in the server
            await asyncio.sleep(0)

        return avg_update

    def simulate_client_communication(self, client_id: int, data_size_mb: float = 1.0) -> tuple:
        """模拟客户端通信，返回(是否成功, 通信时间, 详细信息)"""
        if self.network_simulation_enabled and self.network_env:
            # 使用复杂网络环境模拟
            success, comm_time, details = self.network_env.simulate_client_communication(
                client_id, data_size_mb
            )

            # 更新网络统计
            self.network_stats['total_communications'] += 1
            if success:
                self.network_stats['successful_communications'] += 1
            else:
                self.network_stats['failed_communications'] += 1

            self.network_stats['avg_communication_time'] = (
                self.network_stats['avg_communication_time'] *
                (self.network_stats['total_communications'] - 1) + comm_time
            ) / self.network_stats['total_communications']

            self.network_stats['max_communication_time'] = max(
                self.network_stats['max_communication_time'], comm_time
            )
            self.network_stats['min_communication_time'] = min(
                self.network_stats['min_communication_time'], comm_time
            )

            # 记录详细日志
            if not success:
                logging.warning(
                    f"❌ FedBuff客户端 {client_id} 通信失败 | "
                    f"设备: {details.get('device_type', 'unknown')} | "
                    f"时间: {comm_time:.2f}s | "
                    f"延迟: {details.get('actual_latency', 0):.0f}ms"
                )
            else:
                logging.info(
                    f"✅ FedBuff客户端 {client_id} 通信成功 | "
                    f"设备: {details.get('device_type', 'unknown')} | "
                    f"时间: {comm_time:.2f}s"
                )

            return success, comm_time, details
        elif self.network_simulation_enabled:
            # 简单网络模拟
            return self._simple_network_simulation(client_id, data_size_mb)
        else:
            # 无网络模拟，直接返回成功
            return True, 0.1, {'no_simulation': True}

    def _simple_network_simulation(self, client_id: int, data_size_mb: float) -> tuple:
        """简单网络模拟实现"""
        config = Config().clients

        # 基础通信时间
        base_time = np.random.uniform(0.5, 2.0)

        # 网络延迟模拟
        if hasattr(config, 'network_delay'):
            min_delay = getattr(config.network_delay, 'min_delay', 50) / 1000  # 转换为秒
            max_delay = getattr(config.network_delay, 'max_delay', 2000) / 1000
            distribution = getattr(config.network_delay, 'distribution', 'uniform')

            if distribution == 'exponential':
                # 指数分布延迟
                delay = np.random.exponential((max_delay - min_delay) / 2) + min_delay
                delay = min(delay, max_delay)
            else:
                # 均匀分布延迟
                delay = np.random.uniform(min_delay, max_delay)
        else:
            delay = np.random.exponential(1.0)

        # 带宽限制影响
        if hasattr(config, 'bandwidth_limit'):
            upload_speed = getattr(config.bandwidth_limit, 'upload_speed', 1024)  # KB/s
            transmission_time = (data_size_mb * 1024) / upload_speed  # 秒
        else:
            transmission_time = data_size_mb * 0.1  # 简单估算

        total_time = base_time + delay + transmission_time

        # 丢包率模拟
        success_rate = 0.85  # 默认85%成功率
        if hasattr(config, 'packet_loss'):
            loss_rate = getattr(config.packet_loss, 'loss_rate', 0.15)
            success_rate = 1.0 - loss_rate

        success = np.random.random() < success_rate

        if not success:
            # 失败时的额外延迟
            total_time += np.random.uniform(5, 15)

        # 更新统计
        self.network_stats['total_communications'] += 1
        if success:
            self.network_stats['successful_communications'] += 1
        else:
            self.network_stats['failed_communications'] += 1

        return success, total_time, {
            'simple_simulation': True,
            'base_time': base_time,
            'delay': delay,
            'transmission_time': transmission_time
        }

    def _update_staleness_stats(self, staleness: int):
        """更新陈旧度统计信息"""
        self.staleness_stats['total_staleness'] += staleness
        self.staleness_stats['total_updates'] += 1

        # 计算平均陈旧度
        self.staleness_stats['avg_staleness'] = (
            self.staleness_stats['total_staleness'] /
            self.staleness_stats['total_updates']
        )

        # 更新最大最小陈旧度
        self.staleness_stats['max_staleness'] = max(
            self.staleness_stats['max_staleness'], staleness
        )
        self.staleness_stats['min_staleness'] = min(
            self.staleness_stats['min_staleness'], staleness
        )

    def get_staleness_statistics(self) -> dict:
        """获取陈旧度统计信息"""
        return {
            'avg_staleness': self.staleness_stats['avg_staleness'],
            'max_staleness': self.staleness_stats['max_staleness'],
            'min_staleness': self.staleness_stats['min_staleness'] if self.staleness_stats['min_staleness'] != float('inf') else 0,
            'total_updates': self.staleness_stats['total_updates'],
            'total_staleness': self.staleness_stats['total_staleness']
        }

    def get_network_statistics(self) -> dict:
        """获取网络统计信息"""
        if self.network_stats['total_communications'] > 0:
            success_rate = (
                self.network_stats['successful_communications'] /
                self.network_stats['total_communications']
            )
        else:
            success_rate = 0.0

        stats = {
            'success_rate': success_rate,
            'total_communications': self.network_stats['total_communications'],
            'avg_communication_time': self.network_stats['avg_communication_time'],
            'max_communication_time': self.network_stats['max_communication_time'],
            'min_communication_time': self.network_stats['min_communication_time']
        }

        if self.network_simulation_enabled and self.network_env:
            # 添加复杂网络环境的统计
            env_stats = self.network_env.get_comprehensive_stats()
            stats.update(env_stats)

        return stats

    def _setup_custom_result_file(self):
        """设置自定义的结果文件命名格式"""
        try:
            # 获取配置文件名（从命令行参数）
            config_name = "fedbuff_default"
            if len(sys.argv) >= 3 and sys.argv[1] == "-c":
                config_file = sys.argv[2]
                # 提取配置文件名（去掉路径和扩展名）
                config_name = os.path.splitext(os.path.basename(config_file))[0]

            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")

            # 生成自定义文件名
            custom_filename = f"{config_name}_{timestamp}.csv"

            # 更新Config中的文件路径
            result_path = Config().params["result_path"]

            # 确保目录存在
            os.makedirs(result_path, exist_ok=True)

            custom_result_file = os.path.join(result_path, custom_filename)

            # 保存自定义文件路径供后续使用
            self.custom_result_file = custom_result_file

            # 重新初始化CSV文件
            recorded_items = Config().params["result_types"]
            recorded_items_list = [x.strip() for x in recorded_items.split(",")]

            csv_processor.initialize_csv(
                custom_result_file, recorded_items_list, result_path
            )

            logging.info(f"📊 自定义结果文件: {custom_filename}")
            logging.info(f"📁 完整路径: {custom_result_file}")
            print(f"📊 自定义结果文件: {custom_filename}")
            print(f"📁 完整路径: {custom_result_file}")

        except Exception as e:
            logging.warning(f"设置自定义文件名失败，使用默认命名: {e}")
            print(f"❌ 设置自定义文件名失败: {e}")
            import traceback
            traceback.print_exc()
            self.custom_result_file = None

    def clients_processed(self):
        """重写clients_processed方法以使用自定义文件名"""
        print(f"🔄 clients_processed 被调用 - 第{self.current_round}轮")

        # 调用父类方法
        super().clients_processed()

        # 记录网络统计信息（在聚合完成后记录）
        if self.network_simulation_enabled:
            self._log_network_statistics()

        # 如果有自定义文件，写入结果
        if hasattr(self, 'custom_result_file') and self.custom_result_file:
            try:
                print(f"📝 写入自定义结果文件: {self.custom_result_file}")

                # 获取要记录的项目
                logged_items = self.get_logged_items()
                print(f"📊 记录项目: {logged_items}")

                # 按照配置文件中定义的顺序准备数据行
                recorded_items = Config().params["result_types"]
                recorded_items_list = [x.strip() for x in recorded_items.split(",")]

                new_row = []
                for item in recorded_items_list:
                    if item in logged_items:
                        new_row.append(logged_items[item])
                    else:
                        new_row.append(0.0)  # 默认值

                print(f"📋 写入数据行: {new_row}")

                # 写入自定义CSV文件
                csv_processor.write_csv(self.custom_result_file, new_row)
                print(f"✅ 第{self.current_round}轮结果写入成功")

            except Exception as e:
                logging.error(f"写入自定义结果文件失败: {e}")
                print(f"❌ 写入自定义结果文件失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"❌ 没有自定义结果文件设置")

        # 强制写入默认CSV文件作为备份
        try:
            self._write_backup_csv()
        except Exception as e:
            print(f"❌ 写入备份CSV失败: {e}")

    def _log_network_statistics(self):
        """记录网络统计信息"""
        stats = self.get_network_statistics()
        staleness_stats = self.get_staleness_statistics()

        logging.info(
            f"📊 [FedBuff Round {self.current_round}] 网络统计: "
            f"成功率={stats['success_rate']:.2%} | "
            f"总通信={stats['total_communications']} | "
            f"平均时间={stats['avg_communication_time']:.2f}s | "
            f"平均陈旧度={staleness_stats['avg_staleness']:.2f}"
        )

        # 每10轮输出详细统计
        if self.current_round % 10 == 0:
            logging.info(f"🌐 [FedBuff Round {self.current_round}] 详细统计:")
            logging.info(f"   网络统计:")
            for key, value in stats.items():
                if isinstance(value, float):
                    logging.info(f"     {key}: {value:.4f}")
                else:
                    logging.info(f"     {key}: {value}")

            logging.info(f"   陈旧度统计:")
            for key, value in staleness_stats.items():
                if isinstance(value, float):
                    logging.info(f"     {key}: {value:.4f}")
                else:
                    logging.info(f"     {key}: {value}")

    def get_logged_items(self) -> dict:
        """重写get_logged_items方法，添加网络统计信息"""
        logged_items = {
            "round": self.current_round,
            "accuracy": getattr(self, 'accuracy', 0.0),
            "elapsed_time": self.wall_time - self.initial_wall_time,
        }

        # 添加网络统计信息
        if self.network_simulation_enabled:
            network_stats = self.get_network_statistics()
            logged_items.update({
                "network_success_rate": network_stats.get('success_rate', 0.0),
                "avg_communication_time": network_stats.get('avg_communication_time', 0.0),
                "total_communications": network_stats.get('total_communications', 0)
            })

        # 添加陈旧度统计信息
        staleness_stats = self.get_staleness_statistics()
        logged_items.update({
            "avg_staleness": staleness_stats.get('avg_staleness', 0.0),
            "max_staleness": staleness_stats.get('max_staleness', 0),
            "min_staleness": staleness_stats.get('min_staleness', 0),
            "total_updates": staleness_stats.get('total_updates', 0)
        })

        # 只有在存在时才添加这些字段
        if hasattr(self, 'global_accuracy'):
            logged_items["global_accuracy"] = self.global_accuracy
        if hasattr(self, 'global_accuracy_std'):
            logged_items["global_accuracy_std"] = self.global_accuracy_std

        return logged_items

    def _write_backup_csv(self):
        """写入备份CSV文件"""
        try:
            # 生成备份文件名
            result_path = Config().params["result_path"]
            backup_filename = f"fedbuff_backup_{os.getpid()}.csv"
            backup_file = os.path.join(result_path, backup_filename)

            # 获取数据
            logged_items = self.get_logged_items()

            # 检查文件是否存在，如果不存在则创建表头
            if not os.path.exists(backup_file):
                recorded_items = Config().params["result_types"]
                recorded_items_list = [x.strip() for x in recorded_items.split(",")]
                csv_processor.initialize_csv(backup_file, recorded_items_list, result_path)
                print(f"📄 创建备份CSV文件: {backup_filename}")

            # 准备数据行
            recorded_items = Config().params["result_types"]
            recorded_items_list = [x.strip() for x in recorded_items.split(",")]

            new_row = []
            for item in recorded_items_list:
                if item in logged_items:
                    new_row.append(logged_items[item])
                else:
                    new_row.append(0.0)

            # 写入数据
            print(f"📋 写入数据行: {new_row}")
            csv_processor.write_csv(backup_file, new_row)
            print(f"✅ 备份CSV写入成功: {backup_filename}")

        except Exception as e:
            print(f"❌ 备份CSV写入失败: {e}")
            import traceback
            traceback.print_exc()
