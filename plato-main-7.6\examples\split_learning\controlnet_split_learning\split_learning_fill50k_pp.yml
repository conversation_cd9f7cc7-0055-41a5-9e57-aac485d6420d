clients:
    # Type
    type: split_learning

    # The total number of clients
    total_clients: 1

    # The number of clients selected in each round
    per_round: 1

    # Should the clients compute test accuracy locally?
    do_test: false

    # Split learning iterations for each client
    iteration: 12500

server:
    type: split_learning
    random_seed: 1
    address: 127.0.0.1
    port: 8001

    # Server doesn't have to do test for every round in split learning
    do_test: false

data:
    # The training and testing dataset
    datasource: ControlNet
    dataset_name: fill50k
    val_dataset_name: fill50k
    condition: canny

    # Number of samples in each partition
    partition_size: 50000
    testset_size: 1000

    # Fixed random seed
    random_seed: 1

    # IID, biased, or sharded?
    sampler: iid

trainer:
    # The type of the trainer
    type: split_learning_controlnet

    # The maximum number of training rounds
    rounds: 12500

    # The target accuracy
    target_accuracy: 0.95

    # The machine learning model
    model_name: controlnet

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 1
    optimizer: AdamW
    
algorithm:
    # Aggregation algorithm
    type: split_learning

parameters:
    model:
        model_structure_path: examples/split_learning/controlnet_split_learning/ControlNetSplitLearning/cldm_v15_
        privacy_preserving: true
    optimizer:
        lr: 0.00001

results: 
    types: round, accuracy, elapsed_time, comm_overhead