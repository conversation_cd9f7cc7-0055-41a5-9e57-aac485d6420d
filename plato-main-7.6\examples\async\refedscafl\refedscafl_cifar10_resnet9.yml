clients:
    # Type
    type: simple

    # The total number of clients (与FedAC和SC_AFL保持一致)
    total_clients: 100

    # The number of clients selected in each round (与FedAC和SC_AFL保持一致)
    per_round: 20

    # Should the clients compute test accuracy locally?
    do_test: true  # 与FedAC保持一致

    # Should the clients compute test accuracy with global model?
    do_global_test: true

    # Whether client heterogeneity should be simulated (与FedAC保持一致)
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch (与FedAC保持一致)
    max_sleep_time: 5

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false  # 与FedAC保持一致

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 5

    # Client sleep times for simulation (100 clients)
    client_sleep_times: [0.5, 0.8, 1.0, 0.6, 0.9, 1.2, 0.7, 1.1, 0.4, 0.3,
                        0.8, 1.0, 0.5, 0.9, 0.7, 1.1, 0.6, 0.4, 1.2, 0.3,
                        0.7, 0.9, 0.5, 1.0, 0.8, 0.6, 1.1, 0.4, 0.3, 1.2,
                        0.6, 0.8, 1.0, 0.5, 0.9, 0.7, 1.1, 0.4, 1.2, 0.3,
                        0.9, 0.7, 0.5, 0.8, 1.0, 0.6, 0.4, 1.1, 0.3, 1.2,
                        0.5, 0.8, 1.0, 0.6, 0.9, 1.2, 0.7, 1.1, 0.4, 0.3,
                        0.8, 1.0, 0.5, 0.9, 0.7, 1.1, 0.6, 0.4, 1.2, 0.3,
                        0.7, 0.9, 0.5, 1.0, 0.8, 0.6, 1.1, 0.4, 0.3, 1.2,
                        0.6, 0.8, 1.0, 0.5, 0.9, 0.7, 1.1, 0.4, 1.2, 0.3,
                        0.9, 0.7, 0.5, 0.8, 1.0, 0.6, 0.4, 1.1, 0.3, 1.2]

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8095  # 使用不同端口避免冲突
    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in synchronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server? (与FedAC保持一致)
    simulate_wall_time: true

    # What is the minimum number of clients that need to report before aggregation begins? (与FedAC保持一致)
    minimum_clients_aggregated: 10

    # What is the staleness bound, beyond which the server should wait for stale clients? (与FedAC和SC_AFL保持一致)
    staleness_bound: 5

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: true

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/refedscafl/cifar10_alpha01
    model_path: models/refedscafl/cifar10_alpha01

    random_seed: 1

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition (与FedAC保持一致)
    partition_size: 300

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution(alpha) (与FedAC保持一致)
    concentration: 0.1

    # The size of the testset on the server (与FedAC保持一致)
    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

    # Get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds (与FedAC保持一致)
    rounds: 400

    # The maximum number of clients running concurrently (与FedAC保持一致)
    max_concurrency: 10

    # The target accuracy
    target_accuracy: 1

    # Number of epochs for local training in each communication round (与FedAC保持一致)
    epochs: 5
    batch_size: 50  # 与FedAC保持一致
    optimizer: SGD
    lr_scheduler: LambdaLR  # 与FedAC保持一致

    # The machine learning model
    model_name: resnet_9

algorithm:
    # Aggregation algorithm
    type: fedavg  # 暂时保持fedavg以确保能启动
    lamda: 1.0

    # RefedSCAFL参数
    buffer_pool_size: 10
    greedy_selection_size: 5
    tau_max: 5
    V: 1.0  # 延迟权重参数

    # 基础权重
    success_weight: 0.8
    distill_weight: 0.2
    rho: 0.9
    communication_threshold: 2.0

    # 聚合算法选择（修正的核心配置）
    use_simple_average_aggregation: false
    use_sc_afl_style_aggregation: true  # 启用SC_AFL风格聚合

    # 增强自适应权重调整参数
    enable_adaptive_weights: true
    weight_adaptation_window: 5
    weight_adaptation_threshold: 0.02
    weight_adaptation_step: 0.05
    min_weight_ratio: 0.1

    # 模型一致性评估参数
    enable_consistency_factor: true
    consistency_gradient_weight: 0.4      # 梯度方向一致性权重
    consistency_magnitude_weight: 0.3     # 参数变化幅度权重
    consistency_distribution_weight: 0.3  # 权重分布相似性权重
    consistency_variance_penalty: 0.2     # 一致性方差惩罚系数

    # 知识蒸馏参数
    enable_knowledge_distillation: true
    distillation_temperature: 3.0
    distillation_alpha: 0.7

    # SCAFL参数
    use_pure_scafl_selection: true
    V: 1.0

    # 聚合算法选择 - 启用SC_AFL风格聚合（修正后的ReFedScaFL逻辑）
    use_simple_average_aggregation: false

parameters:
    model:
        num_classes: 10
        # ResNet-9 for CIFAR-10 (轻量级ResNet)
        in_channels: 3  # CIFAR-10是3通道彩色图像

    optimizer:
        lr: 0.01  # 与FedAC保持一致
        momentum: 0.9
        weight_decay: 0.0001  # 与FedAC保持一致

    learning_rate:
        # LambdaLR调度器 (与FedAC保持一致)
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    result_path: results/refedscafl/cifar10_alpha01

    # Write the following parameter(s) into a CSV (与FedAC保持一致，并添加staleness指标)
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness
