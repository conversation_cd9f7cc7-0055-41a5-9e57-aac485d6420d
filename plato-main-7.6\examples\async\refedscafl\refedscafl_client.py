
"""
ReFedScaFL客户端类，扩展自基础客户端
实现异步训练和通信功能
"""

import torch
import logging
import sys
from plato.clients import simple
# 导入你自己的 Algorithm 和 Trainer 类
from refedscafl_algorithm import Algorithm
from refedscafl_trainer import Trainer
from plato.datasources import base # 保留，以防万一Plato内部需要
from plato.models import registry as models # 保留，以防Plato内部注册机制需要
import asyncio
from plato.config import Config
import numpy as np
from torch.utils.data import Subset
import time
from types import SimpleNamespace
import copy
from torchvision import datasets, transforms
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，避免需要图形界面
import matplotlib.pyplot as plt
import os
import sys
from datetime import datetime
from PIL import Image # 用于数据验证中的PIL Image类型检查
import pickle # 用于序列化模型权重

# 配置日志 (确保整个文件共享这个 logger)
# 根日志记录器已在 refedscafl_algorithm.py 中配置，这里获取并使用即可
logger = logging.getLogger()

# 全局存储模型架构的模板，避免重复加载
_model_template = None

# 可视化数据分布函数 (保持不变，但确保其可以被调用)
def visualize_data_distribution(client_id, dataset, save_dir='data_distribution'):
    """
    可视化客户端数据分布
    
    参数:
        client_id: 客户端ID
        dataset: 客户端数据集
        save_dir: 保存目录
    """
    try:
        os.makedirs(save_dir, exist_ok=True)
        
        if hasattr(dataset, 'targets'):
            labels = dataset.targets
        else:
            if isinstance(dataset, Subset):
                if hasattr(dataset.dataset, 'targets'):
                    all_labels = dataset.dataset.targets
                    indices = dataset.indices
                    labels = [all_labels[i] for i in indices]
                else:
                    labels = []
                    for i in range(min(len(dataset), 1000)): # 限制样本数以避免内存问题
                        try:
                            _, label = dataset[i]
                            labels.append(label)
                        except Exception as e:
                            logger.warning(f"无法从样本 {i} 获取标签: {e}")
                            continue
            else:
                labels = []
                for i in range(min(len(dataset), 1000)): # 限制样本数
                    try:
                        _, label = dataset[i]
                        labels.append(label)
                    except Exception as e:
                        logger.warning(f"无法从样本 {i} 获取标签: {e}")
                        continue
        
        if not labels:
            logger.warning(f"客户端 {client_id} 没有可用于可视化的标签数据。")
            return False

        if not isinstance(labels, np.ndarray):
            labels = np.array(labels)
        
        unique_labels = np.unique(labels)
        class_counts = [np.sum(labels == label) for label in unique_labels]
        
        plt.figure(figsize=(10, 6))
        plt.bar(unique_labels, class_counts)
        plt.xlabel('类别')
        plt.ylabel('样本数')
        plt.title(f'客户端 {client_id} 的类别分布 (总样本数: {len(dataset)})')
        plt.xticks(unique_labels)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        
        save_path = os.path.join(save_dir, f'client_{client_id}_distribution.png')
        plt.savefig(save_path)
        plt.close()
        
        logger.info(f"[INFO] 客户端 {client_id} 的数据分布可视化已保存到 {save_path}")
        
        text_path = os.path.join(save_dir, f'client_{client_id}_distribution.txt')
        with open(text_path, 'w') as f:
            f.write(f"客户端 {client_id} 数据分布\n")
            f.write(f"总样本数: {len(dataset)}\n")
            f.write("类别分布:\n")
            for label, count in zip(unique_labels, class_counts):
                f.write(f"  类别 {label}: {count} 样本 ({count/len(dataset)*100:.2f}%)\n")
        
        return True
    except Exception as e:
        logger.error(f"[ERROR] 可视化客户端 {client_id} 的数据分布时出错: {str(e)}")
        return False

class Client(simple.Client):
    """
    ReFedScaFL客户端类，扩展自基础客户端
    实现异步训练和通信功能
    """
    def __init__(
        self,
        model=None,
        datasource=None,
        algorithm=None,
        trainer=None,
        callbacks=None,
    ):
        """
        初始化ReFedScaFL客户端
        """
        # 使用标准Plato客户端初始化方式
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )

        # ReFedScaFL特有的属性
        self.last_round = 0 # 客户端上次训练或蒸馏的轮次
        self.tau_k = 0      # 陈旧度
        self.D_k = 0        # 估计的训练+通信时间
        self.Q_k = 1.0      # 质量因子 (RefedSCAFL特有)
        self.need_distill = False # 是否需要进行蒸馏补偿
        self.current_model_weights = None       # 存储客户端本地模型的当前权重
        self.last_global_model_weights = None   # 存储最近一次从服务器接收到的全局模型权重
        self.last_model_receive_time = time.time() # 上次接收模型的时间
        
        # 从配置中获取通信阈值，提供默认值
        config = Config()
        if hasattr(config, 'algorithm') and hasattr(config.algorithm, 'communication_threshold'):
            self.comm_threshold = config.algorithm.communication_threshold
        else:
            self.comm_threshold = 1.5

        logger.info(f"[Client {self.client_id}] 基础初始化完成")

    def configure(self):
        """
        配置客户端。这个方法会在客户端启动时被调用，
        确保模型、训练器和数据已经加载。
        """
        # 使用标准Plato客户端配置方式
        super().configure()

        logger.info(f"[Client {self.client_id}] 配置验证完成。")

    def _load_data(self):
        """加载数据，使用标准Plato数据加载方式。"""
        logger.info(f"[Client {self.client_id}] 开始加载数据...")

        # 如果没有自定义datasource，则使用标准Plato数据源注册表
        if self.datasource is None:
            from plato.datasources import registry as datasources_registry
            self.datasource = datasources_registry.get(client_id=self.client_id)
            logger.info(f"[Client {self.client_id}] 创建数据源: {self.datasource.__class__.__name__}")

        # 调用父类的标准数据加载方式
        super()._load_data()
        logger.info(f"[Client {self.client_id}] ✅ 数据加载完成")

    def get_train_set(self):
        """获取客户端训练集。"""
        return self.trainset

    def get_test_set(self):
        """获取客户端测试集。"""
        return self.testset

    def validate_model(self):
        """验证模型参数是否包含 NaN 或 Inf 值。"""
        if self.model is None:
            logger.error(f"[Client {self.client_id}] 模型实例为None，无法进行验证。")
            return False
        try:
            for name, param in self.model.named_parameters():
                if torch.isnan(param).any() or torch.isinf(param).any():
                    logger.error(f"[Client {self.client_id}] 模型参数 '{name}' 包含NaN或Inf值，模型无效。")
                    return False
            logger.info(f"[Client {self.client_id}] 模型参数验证通过。")
            return True
        except Exception as e:
            logger.error(f"[Client {self.client_id}] 模型验证失败: {str(e)}")
            return False

    def update_client_state(self, current_server_round):
        """
        更新客户端状态，包括陈旧度 (tau_k)、估计训练+通信时间 (D_k) 和质量因子 (Q_k)。
        此方法应在客户端接收到服务器任务后，执行训练/蒸馏前更新。
        """
        # 更新陈旧度：服务器当前轮次 - 客户端上次更新全局模型时的轮次
        # 这里假设 self.last_round 记录的是客户端上次成功完成任务（训练或蒸馏）并提交更新时的轮次
        self.tau_k = max(0, current_server_round - self.last_round)

        # 更新估计的训练+通信时间 (D_k)
        # D_k 可以是上次训练的实际耗时 + 通信耗时，或者更复杂的历史平均
        # 这里使用一个简化的估算：如果 Trainer 报告了训练时间，则使用它；否则使用一个默认值。
        current_training_duration = getattr(self.trainer, 'last_training_time', 0.0) # 假设trainer会记录上次训练时间
        current_comm_duration = time.time() - self.last_model_receive_time # 计算从接收模型到当前的时间

        # D_k 可以是训练时间加上通信时间，或者一个更复杂的滑动平均
        self.D_k = current_training_duration + current_comm_duration

        # 更新质量因子 (Q_k)，参考 RefedSCAFL 论文中的公式
        # 例如：Q_k = 1 / (1 + tau_k) * (1 / (1 + D_k)) 或者其他结合陈旧度和延迟的函数
        # 为了避免除以0或过小的D_k导致Q_k过大，可以加1或使用log
        config = Config()
        V = getattr(config.algorithm, 'V', 1.0) # 延迟权重 V
        tau_max = getattr(config.algorithm, 'tau_max', 5) # 最大陈旧度 tau_max

        # 一个可能的质量因子公式，结合陈旧度和延迟：
        # Q_k = 1 / (1 + V * D_k + self.tau_k / tau_max)
        # 简化版：
        if self.D_k > 0:
            self.Q_k = 1.0 / (1.0 + self.tau_k) * (1.0 / self.D_k) # 兼顾陈旧度和速度
        else: # 如果 D_k 为0（例如首次），则只考虑陈旧度
            self.Q_k = 1.0 / (1.0 + self.tau_k)

        logger.info(f"[Client {self.client_id}] 状态更新: 当前轮次={current_server_round}, 陈旧度(tau_k)={self.tau_k}, 估计持续时间(D_k)={self.D_k:.2f}s, 质量因子(Q_k)={self.Q_k:.4f}。")

    async def _train(self):
        """
        实现客户端训练功能，确保训练完成后能够返回报告和模型权重。
        这是 base.Client 中的抽象方法，必须由子类实现。
        """
        try:
            # 记录模型接收时间，用于计算通信延迟
            self.last_model_receive_time = time.time()

            # 更新客户端状态
            self.update_client_state(self.current_round)

            # 检查训练集是否正确初始化
            if self.trainset is None or len(self.trainset) == 0:
                logger.error(f"[Client {self.client_id}] 训练集为空，无法进行训练")
                return None

            # 开始训练
            logger.info(f"[Client {self.client_id}] 开始训练，当前轮次: {self.current_round}")
            start_time = time.time()

            # 使用标准Plato训练流程
            try:
                report, weights = await super()._train()
            except AttributeError as attr_err:
                if "client_sleep_times" in str(attr_err):
                    # 处理client_sleep_times配置缺失的问题
                    logger.warning(f"[Client {self.client_id}] client_sleep_times配置缺失，跳过睡眠模拟")
                    # 直接调用trainer进行训练
                    training_time = self.trainer.train(self.trainset, self.sampler)

                    # 创建基本的训练报告
                    from types import SimpleNamespace

                    if self.sampler is None:
                        num_samples = self.datasource.num_train_examples()
                    else:
                        num_samples = self.sampler.num_samples()

                    report = SimpleNamespace(
                        client_id=self.client_id,
                        num_samples=num_samples,
                        accuracy=0,
                        global_accuracy=0,
                        training_time=training_time,
                        comm_time=0,
                        update_response=False,
                        deltas=None,
                    )

                    # 获取模型权重
                    weights = self.algorithm.extract_weights()

                else:
                    raise attr_err

            # 记录训练时间
            training_time = time.time() - start_time
            logger.info(f"[Client {self.client_id}] 训练完成，耗时: {training_time:.2f}秒")

            # 更新客户端的最后一次训练轮次
            self.last_round = self.current_round

            return report, weights

        except Exception as e:
            logger.error(f"[Client {self.client_id}] 训练过程中出错: {str(e)}")
            import traceback
            logger.error(f"详细错误堆栈: {traceback.format_exc()}")

            # 返回空的报告而不是None，避免后续处理错误
            from types import SimpleNamespace

            if self.sampler is None:
                num_samples = self.datasource.num_train_examples()
            else:
                num_samples = self.sampler.num_samples()

            empty_report = SimpleNamespace(
                client_id=self.client_id,
                num_samples=num_samples,
                accuracy=0,
                global_accuracy=0,
                training_time=0,
                comm_time=0,
                update_response=False,
                deltas=None,
            )

            return empty_report, None

    def distill_pseudo_gradient(self, global_model_weights):
        """
        优化的知识蒸馏方法，用于生成伪梯度。
        教师模型是服务器下发的全局模型 (global_model_weights)。
        学生模型是客户端本地的当前模型 (self.model)。
        """
        if self.model is None:
            logger.error(f"[Client {self.client_id}] 蒸馏补偿失败：本地模型 (self.model) 为 None。")
            return None
        if global_model_weights is None:
            logger.error(f"[Client {self.client_id}] 蒸馏补偿失败：全局模型权重 (global_model_weights) 为 None。")
            return None
        if len(self.trainset) == 0:
            logger.error(f"[Client {self.client_id}] 训练集为空，无法进行蒸馏。")
            return None

        try:
            # 确保本地模型处于可训练模式以计算梯度
            self.model.train()
            self.model.zero_grad() # 清除之前的梯度

            # 配置知识蒸馏参数
            config = Config()
            T = getattr(config.algorithm, 'distillation_temperature', 2.0)  # 温度参数
            alpha = getattr(config.algorithm, 'distillation_alpha', 0.5)    # KL散度权重
            # 可以添加 beta 权重给硬目标损失，如 (1-alpha-beta) * ce_loss
            # 这里简化为 (1-alpha) * ce_loss

            # 数据采样：从本地训练集中随机采样一个批次
            batch_size = min(Config().trainer.get('batch_size', 32), len(self.trainset))
            # 确保在数据集允许的范围内进行采样
            if batch_size == 0:
                logger.warning(f"[Client {self.client_id}] 蒸馏批量大小为0，无法采样数据。")
                return None

            indices = np.random.choice(len(self.trainset), batch_size, replace=False)
            batch_data = [self.trainset[i] for i in indices]
            
            # 将数据和标签堆叠成张量，并移动到正确设备
            inputs = torch.stack([item[0] for item in batch_data]).to(self.device)
            labels = torch.tensor([item[1] for item in batch_data]).to(self.device)

            # 学生模型前向传播
            student_logits = self.model(inputs)

            # 创建教师模型实例，加载全局权重，并设为评估模式
            # 注意：深拷贝是必要的，以确保教师模型不受本地训练影响
            teacher_model = copy.deepcopy(self.model)
            teacher_model.load_state_dict(global_model_weights)
            teacher_model.eval() # 教师模型只用于推理，不计算梯度

            # 教师模型前向传播 (不计算梯度)
            with torch.no_grad():
                teacher_logits = teacher_model(inputs)

            # 1. KL散度损失 (软目标)
            kl_loss_fn = torch.nn.KLDivLoss(reduction='batchmean')
            student_log_probs = torch.log_softmax(student_logits / T, dim=1)
            teacher_probs = torch.softmax(teacher_logits / T, dim=1)

            # 添加数值稳定性 (防止 log(0) 或除零)
            eps = 1e-8
            teacher_probs = torch.clamp(teacher_probs, min=eps, max=1.0-eps)
            student_log_probs = torch.clamp(student_log_probs, min=torch.log(torch.tensor(eps, device=self.device)))

            kl_loss = kl_loss_fn(student_log_probs, teacher_probs) * (T * T)

            if torch.isnan(kl_loss) or torch.isinf(kl_loss):
                logger.warning(f"[Client {self.client_id}] KL损失异常 ({kl_loss.item()})，使用备用值 0.0。")
                kl_loss = torch.tensor(0.0, device=self.device)

            # 2. 交叉熵损失 (硬目标)
            ce_loss_fn = torch.nn.CrossEntropyLoss()
            ce_loss = ce_loss_fn(student_logits, labels)

            # 组合损失：alpha * 软目标损失 + (1-alpha) * 硬目标损失
            total_loss = alpha * kl_loss + (1 - alpha) * ce_loss

            if torch.isnan(total_loss) or torch.isinf(total_loss):
                logger.error(f"[Client {self.client_id}] 组合损失异常 ({total_loss.item()})，蒸馏补偿失败。")
                return None

            # 反向传播计算梯度
            total_loss.backward()

            # 提取伪梯度
            pseudo_gradients = {}
            total_grad_norm = 0.0

            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    grad = param.grad.clone().cpu() # 将梯度移回CPU以减少GPU内存占用，方便传输

                    # 检查梯度是否包含NaN或Inf
                    if torch.isnan(grad).any() or torch.isinf(grad).any():
                        logger.warning(f"[Client {self.client_id}] 参数 '{name}' 的梯度包含NaN/Inf，跳过。")
                        continue

                    # 梯度裁剪
                    grad_norm = torch.norm(grad)
                    if grad_norm > 2.0: # 可以调整裁剪阈值
                        grad = grad / grad_norm * 2.0
                        logger.debug(f"[Client {self.client_id}] 参数 '{name}' 梯度被裁剪。")

                    # 再次检查处理后的梯度
                    if torch.isnan(grad).any() or torch.isinf(grad).any():
                        logger.warning(f"[Client {self.client_id}] 参数 '{name}' 处理后仍包含NaN/Inf，跳过。")
                        continue

                    pseudo_gradients[name] = grad
                    total_grad_norm += (grad_norm.item() ** 2) # 计算总梯度范数

            total_grad_norm = total_grad_norm ** 0.5

            logger.info(f"[Client {self.client_id}] 蒸馏补偿成功，生成伪梯度 "
                        f"(KL损失: {kl_loss.item():.4f}, CE损失: {ce_loss.item():.4f}, "
                        f"总损失: {total_loss.item():.4f}, 伪梯度范数: {total_grad_norm:.4f})")

            # 清理内存
            del teacher_model, teacher_logits, inputs, labels, student_logits, teacher_probs, student_log_probs
            torch.cuda.empty_cache() if torch.cuda.is_available() else None

            return pseudo_gradients
        except Exception as e:
            logger.error(f"[Client {self.client_id}] 蒸馏补偿失败，错误信息: {str(e)}")
            import traceback
            logger.error(f"详细错误堆栈: {traceback.format_exc()}")
            return None

    def __str__(self):
        """返回客户端的字符串表示，用于日志和调试。"""
        return f"Client #{self.client_id}"

    def __repr__(self):
        """返回客户端的字符串表示（用于调试）。"""
        return f"Client(client_id={self.client_id})"

# 确保客户端能被Plato框架识别和实例化
def client() -> Client:
    """Plato框架用于实例化客户端的入口函数。"""
    # client_id 和 server 引用将在 Plato 框架内部实例化 Client 时传入
    # 这里返回一个Client类的实例
    return Client()
