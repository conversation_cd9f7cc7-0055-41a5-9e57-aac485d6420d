# 📋 ReFedScaFL 专利权利要求书

## 🎯 **发明名称**
一种基于双缓冲池和知识蒸馏的异步联邦学习方法及系统

## 📝 **权利要求**

### **权利要求1（独立权利要求）**
一种基于双缓冲池的异步联邦学习方法，其特征在于，包括以下步骤：

1. **初始化步骤**：
   - 在服务器端建立成功缓冲池和蒸馏缓冲池；
   - 初始化全局模型权重；
   - 设置成功权重系数α和蒸馏权重系数β，满足α + β = 1；

2. **客户端选择步骤**：
   - 基于贪心策略从可用客户端中选择k个客户端参与训练；
   - 所述贪心策略综合考虑数据质量、通信稳定性和计算能力；

3. **异步训练步骤**：
   - 选中的客户端并行执行本地模型训练；
   - 训练完成后尝试向服务器上传模型权重；

4. **双路径处理步骤**：
   - 若客户端成功上传模型权重，则将其存入成功缓冲池；
   - 若客户端上传失败，则执行知识蒸馏生成补偿梯度，存入蒸馏缓冲池；

5. **聚合触发步骤**：
   - 检查聚合触发条件，包括缓冲池数量阈值和等待时间阈值；
   - 满足条件时触发模型聚合过程；

6. **自适应聚合步骤**：
   - 从成功缓冲池获取模型权重更新，计算加权贡献：W_success = Σ(w_i × n_i/N × α)；
   - 从蒸馏缓冲池获取梯度更新，计算加权贡献：G_distill = Σ(g_i × n_i/N × β)；
   - 生成新的全局模型：W_global = W_success + G_distill；
   - 其中w_i为第i个客户端的权重，g_i为第i个客户端的梯度，n_i为样本数，N为总样本数；

7. **权重调整步骤**：
   - 基于历史性能趋势计算权重调整量；
   - 动态更新成功权重系数α和蒸馏权重系数β；

8. **状态清理步骤**：
   - 清空双缓冲池；
   - 更新全局模型并广播给客户端。

### **权利要求2（从属权利要求）**
根据权利要求1所述的方法，其特征在于，所述知识蒸馏步骤包括：
- 使用全局模型作为教师模型，客户端本地模型作为学生模型；
- 计算蒸馏损失：L_KD = KL(softmax(z_s/T), softmax(z_t/T)) × T²；
- 其中z_s为学生模型输出，z_t为教师模型输出，T为温度参数；
- 通过反向传播计算梯度更新。

### **权利要求3（从属权利要求）**
根据权利要求1所述的方法，其特征在于，所述贪心客户端选择策略包括：
- 计算客户端综合评分：Score_i = w1×DataQuality_i + w2×CommStability_i + w3×CompCapability_i；
- 选择评分最高的k个客户端参与训练；
- 其中w1、w2、w3为权重系数，满足w1 + w2 + w3 = 1。

### **权利要求4（从属权利要求）**
根据权利要求1所述的方法，其特征在于，所述聚合触发条件包括：
- 条件1：成功缓冲池客户端数量 ≥ k；
- 条件2：成功缓冲池 + 蒸馏缓冲池客户端总数 ≥ k；
- 条件3：有更新且等待时间 > 预设阈值；
- 满足任一条件即触发聚合。

### **权利要求5（从属权利要求）**
根据权利要求1所述的方法，其特征在于，所述权重调整步骤包括：
- 计算性能趋势：slope = (n×Σ(xy) - Σ(x)×Σ(y)) / (n×Σ(x²) - (Σ(x))²)；
- 若slope < -threshold，则增加蒸馏权重系数β；
- 若slope > threshold，则向初始配置微调；
- 其中x为轮次序号，y为对应准确率。

### **权利要求6（独立权利要求）**
一种异步联邦学习系统，其特征在于，包括：

1. **服务器端**，包括：
   - 双缓冲池管理模块，用于管理成功缓冲池和蒸馏缓冲池；
   - 聚合引擎模块，用于执行自适应权重聚合算法；
   - 客户端选择模块，用于实现贪心选择策略；
   - 通信管理模块，用于处理异步通信和失败检测；
   - 权重调整模块，用于基于性能反馈调整聚合权重；

2. **客户端**，包括：
   - 本地训练模块，用于执行本地模型训练；
   - 蒸馏模块，用于生成知识蒸馏补偿梯度；
   - 通信模块，用于处理模型上传和下载；
   - 状态管理模块，用于跟踪训练状态和通信状态。

### **权利要求7（从属权利要求）**
根据权利要求6所述的系统，其特征在于，所述双缓冲池管理模块包括：
- 成功缓冲池，存储结构为{client_id, weights, samples, timestamp, is_distilled=False}；
- 蒸馏缓冲池，存储结构为{client_id, gradients, samples, timestamp, is_distilled=True}；
- 缓冲池操作接口，包括添加、查询、清空和状态检查功能。

### **权利要求8（从属权利要求）**
根据权利要求6所述的系统，其特征在于，所述聚合引擎模块包括：
- 权重提取器，用于从双缓冲池提取有效更新；
- 权重计算器，用于计算基于样本数和类型的加权系数；
- 聚合器，用于执行加权平均或梯度累加操作；
- 归一化器，用于对聚合结果进行归一化处理。

### **权利要求9（从属权利要求）**
根据权利要求6所述的系统，其特征在于，所述蒸馏模块包括：
- 教师模型接口，用于获取全局模型作为教师；
- 蒸馏损失计算器，用于计算KL散度损失；
- 梯度提取器，用于提取反向传播梯度；
- 质量评估器，用于评估蒸馏梯度的质量。

### **权利要求10（从属权利要求）**
根据权利要求6所述的系统，其特征在于，所述通信管理模块包括：
- 异步上传协议，支持非阻塞的模型权重传输；
- 蒸馏补偿协议，处理通信失败时的梯度传输；
- 失败检测机制，基于超时和重试策略检测通信失败；
- 状态同步协议，维护客户端和服务器的状态一致性。

### **权利要求11（独立权利要求）**
一种计算机可读存储介质，其上存储有计算机程序，其特征在于，所述计算机程序被处理器执行时实现权利要求1-5中任一项所述的方法。

### **权利要求12（独立权利要求）**
一种计算机设备，包括存储器、处理器及存储在存储器上并可在处理器上运行的计算机程序，其特征在于，所述处理器执行所述计算机程序时实现权利要求1-5中任一项所述的方法。

### **权利要求13（从属权利要求）**
根据权利要求1所述的方法，其特征在于，还包括客户端状态重置步骤：
- 当缓冲池客户端数量达到总客户端数的80%且长时间无聚合时；
- 或当定期重置时间间隔达到预设阈值时；
- 清空客户端状态标记，允许客户端重新参与训练。

### **权利要求14（从属权利要求）**
根据权利要求1所述的方法，其特征在于，所述自适应聚合步骤还包括：
- 梯度裁剪机制，防止梯度爆炸：clip_gradient = min(gradient, max_norm)；
- 自适应学习率计算：lr = min(1.0, max(0.1, weight_norm/grad_norm × 0.1))；
- 权重验证机制，确保聚合结果的有效性。

### **权利要求15（从属权利要求）**
根据权利要求6所述的系统，其特征在于，还包括监控模块，用于：
- 记录训练过程中的准确率历史；
- 跟踪权重调整历史；
- 统计通信成功率和失败率；
- 生成性能分析报告和可视化图表。

## 📋 **权利要求说明**

### **技术领域**
本发明涉及机器学习技术领域，特别是涉及一种基于双缓冲池和知识蒸馏的异步联邦学习方法及系统。

### **保护范围**
- 核心方法：双缓冲池异步联邦学习算法
- 关键技术：知识蒸馏补偿机制
- 系统架构：异步联邦学习系统设计
- 实现方式：计算机程序和存储介质

### **技术效果**
- 提高联邦学习在不稳定网络环境下的鲁棒性
- 充分利用通信失败客户端的训练信息
- 实现自适应的聚合权重优化
- 保证异步训练的收敛性和稳定性

### **应用价值**
- 适用于移动设备、边缘计算、物联网等场景
- 解决传统联邦学习的通信瓶颈问题
- 提供工业级的联邦学习解决方案
- 具有广泛的商业应用前景
