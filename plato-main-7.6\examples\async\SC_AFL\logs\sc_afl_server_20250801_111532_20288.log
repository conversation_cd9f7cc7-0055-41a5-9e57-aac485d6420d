2025-08-01 11:15:32,132 - INFO - 🚀 SC-AFL服务器启动 - 2025-08-01 11:15:32
2025-08-01 11:15:32,132 - INFO - ✅ 新日志文件已创建
2025-08-01 11:15:32,132 - INFO - 📁 日志目录: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs
2025-08-01 11:15:32,132 - INFO - 📄 日志文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs\sc_afl_server_20250801_111532_20288.log
2025-08-01 11:15:32,133 - INFO - 🔧 日志级别: DEBUG (文件), INFO (控制台)
2025-08-01 11:15:32,133 - INFO - 📊 文件模式: 新建模式 (每次启动创建新文件)
2025-08-01 11:15:32,133 - INFO - 🆔 进程ID: 20288
2025-08-01 11:15:32,133 - INFO - ✅ 日志文件创建成功，当前大小: 675 字节
2025-08-01 11:15:32,133 - INFO - 🚀 SC-AFL服务器初始化开始...
2025-08-01 11:15:32,133 - INFO - 📅 初始化时间: 2025-08-01 11:15:32
2025-08-01 11:15:32,134 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:32,161 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:32,161 - INFO - Server: 动态创建模型 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:15:32,162 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:15:32,179 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:32,180 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:15:32,180 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:15:32,181 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:15:32,182 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:32,182 - INFO - [Trainer None] 初始化完成
2025-08-01 11:15:32,182 - INFO - Server: 创建了新的Trainer实例
2025-08-01 11:15:32,182 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:15:32,182 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:15:32,182 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:32,182 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:32,182 - INFO - Server: 创建了新的Algorithm实例
2025-08-01 11:15:32,183 - INFO - [93m[1m[20288] Logging runtime results to: ./results/cifar10_with_network/20288.csv.[0m
2025-08-01 11:15:32,183 - INFO - [Server #20288] Started training on 6 clients with 3 per round.
2025-08-01 11:15:32,183 - INFO - [DEBUG] 从配置文件读取 simulate_wall_time=True
2025-08-01 11:15:32,183 - WARNING - Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。
2025-08-01 11:15:32,183 - WARNING - Server: 训练器在初始化后为None，重新创建
2025-08-01 11:15:32,183 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:15:32,183 - WARNING - [Trainer None] 模型为None，尝试创建默认模型
2025-08-01 11:15:32,184 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:32,198 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:32,199 - INFO - [Trainer None] 动态创建模型 resnet_9，输入通道: 3, 类别数: 10
2025-08-01 11:15:32,208 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:32,208 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:15:32,208 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:15:32,208 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:15:32,209 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:32,209 - INFO - [Trainer None] 初始化完成
2025-08-01 11:15:32,209 - INFO - Server: 重新创建了Trainer实例
2025-08-01 11:15:32,209 - INFO - [Algorithm] 已设置服务器引用 (客户端ID: None)
2025-08-01 11:15:32,209 - INFO - Server: 算法类已设置服务器引用
2025-08-01 11:15:32,209 - INFO - 动态加载数据集: CIFAR10
2025-08-01 11:15:32,696 - INFO - ✅ 成功加载数据集 CIFAR10: 10000 样本
2025-08-01 11:15:32,696 - INFO - ✅ 动态加载测试集成功: CIFAR10, 大小: 10000
2025-08-01 11:15:32,696 - INFO - ✅ 测试加载器已创建
2025-08-01 11:15:32,697 - INFO - 开始初始化全局模型权重
2025-08-01 11:15:32,697 - WARNING - 全局模型实例为None，正在尝试重新创建...
2025-08-01 11:15:32,697 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:32,709 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:32,710 - INFO - 成功重新创建了全局模型实例 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:15:32,726 - INFO - [全局权重摘要] 参数数量: 74, 均值: 0.001180, 最大: 1.000000, 最小: -0.191991
2025-08-01 11:15:32,727 - INFO - [全局模型] 输入通道数: 3
2025-08-01 11:15:32,727 - INFO - 全局模型权重初始化成功
2025-08-01 11:15:32,728 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:15:32,729 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:15:32,729 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:15:32,729 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:15:32,729 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_111532.csv
2025-08-01 11:15:32,729 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:15:32,730 - WARNING - 网络模拟器初始化失败: 'Config' object has no attribute 'get'
2025-08-01 11:15:32,730 - INFO - SC-AFL算法参数: tau_max=5, V=1.0
2025-08-01 11:15:32,730 - INFO - 服务器初始化完成
2025-08-01 11:15:32,730 - INFO - 已创建并注册 0 个客户端（将在 Server.start() 中启动任务）
2025-08-01 11:15:32,730 - INFO - 客户端ID管理器初始化完成，总客户端数: 6, ID起始值: 1
2025-08-01 11:15:32,730 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:15:32,730 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:15:32,731 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:15:32,731 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_111532.csv
2025-08-01 11:15:32,731 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:15:32,731 - INFO - 服务器实例创建成功
2025-08-01 11:15:32,732 - INFO - 正在创建和注册 6 个客户端...
2025-08-01 11:15:32,732 - INFO - 客户端ID配置: 起始ID=1, 总数=6
2025-08-01 11:15:32,732 - INFO - 开始创建客户端 1...
2025-08-01 11:15:32,732 - INFO - 初始化客户端, ID: 1
2025-08-01 11:15:32,732 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:32,749 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:32,749 - INFO - [Client 1] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:32,749 - INFO - [Trainer] 初始化训练器, client_id: 1
2025-08-01 11:15:32,758 - INFO - [Trainer 1] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:32,758 - INFO - [Trainer 1] 模型的输入通道数: 3
2025-08-01 11:15:32,758 - INFO - [Trainer 1] 强制使用CPU
2025-08-01 11:15:32,759 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:15:32,759 - INFO - [Trainer 1] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:32,759 - INFO - [Trainer 1] 初始化完成
2025-08-01 11:15:32,759 - INFO - [Client 1] 创建新训练器
2025-08-01 11:15:32,759 - INFO - [Algorithm] 从训练器获取客户端ID: 1
2025-08-01 11:15:32,759 - INFO - [Algorithm] 初始化后修正client_id: 1
2025-08-01 11:15:32,759 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:32,760 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:32,760 - INFO - [Client 1] 创建新算法
2025-08-01 11:15:32,760 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:15:32,760 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:15:32,760 - INFO - [Client None] 父类初始化完成
2025-08-01 11:15:32,760 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:32,775 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:32,775 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:32,775 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:15:32,784 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:32,784 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:15:32,785 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:15:32,785 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:15:32,785 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:32,785 - INFO - [Trainer None] 初始化完成
2025-08-01 11:15:32,785 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:15:32,785 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:15:32,786 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:15:32,786 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:32,786 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:32,786 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:15:32,786 - INFO - [Client None] 开始加载数据
2025-08-01 11:15:32,786 - INFO - 顺序分配客户端ID: 1
2025-08-01 11:15:32,786 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 1
2025-08-01 11:15:32,787 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:15:32,787 - WARNING - [Client 1] 数据源为None，已创建新数据源
2025-08-01 11:15:32,787 - WARNING - [Client 1] 数据源trainset为None，已设置为空列表
2025-08-01 11:15:33,376 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:15:33,377 - INFO - [Client 1] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:15:33,377 - INFO - [Client 1] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:15:33,396 - INFO - [Client 1] 成功划分数据集，分配到 300 个样本
2025-08-01 11:15:33,396 - INFO - [Client 1] 初始化时成功加载数据
2025-08-01 11:15:33,396 - INFO - [客户端 1] 初始化验证通过
2025-08-01 11:15:33,396 - INFO - 客户端 1 实例创建成功
2025-08-01 11:15:33,396 - INFO - 客户端1已设置服务器引用
2025-08-01 11:15:33,396 - INFO - 客户端 1 已设置服务器引用
2025-08-01 11:15:33,396 - INFO - 客户端1已注册
2025-08-01 11:15:33,397 - INFO - 客户端 1 已成功注册到服务器
2025-08-01 11:15:33,397 - INFO - 开始创建客户端 2...
2025-08-01 11:15:33,397 - INFO - 初始化客户端, ID: 2
2025-08-01 11:15:33,397 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:33,413 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:33,413 - INFO - [Client 2] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:33,413 - INFO - [Trainer] 初始化训练器, client_id: 2
2025-08-01 11:15:33,423 - INFO - [Trainer 2] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:33,423 - INFO - [Trainer 2] 模型的输入通道数: 3
2025-08-01 11:15:33,423 - INFO - [Trainer 2] 强制使用CPU
2025-08-01 11:15:33,424 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 11:15:33,424 - INFO - [Trainer 2] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:33,424 - INFO - [Trainer 2] 初始化完成
2025-08-01 11:15:33,424 - INFO - [Client 2] 创建新训练器
2025-08-01 11:15:33,424 - INFO - [Algorithm] 从训练器获取客户端ID: 2
2025-08-01 11:15:33,424 - INFO - [Algorithm] 初始化后修正client_id: 2
2025-08-01 11:15:33,424 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:33,424 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:33,424 - INFO - [Client 2] 创建新算法
2025-08-01 11:15:33,424 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:15:33,424 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:15:33,424 - INFO - [Client None] 父类初始化完成
2025-08-01 11:15:33,425 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:33,440 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:33,440 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:33,440 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:15:33,451 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:33,451 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:15:33,451 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:15:33,452 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:15:33,452 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:33,452 - INFO - [Trainer None] 初始化完成
2025-08-01 11:15:33,452 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:15:33,452 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:15:33,452 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:15:33,452 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:33,452 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:33,452 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:15:33,452 - INFO - [Client None] 开始加载数据
2025-08-01 11:15:33,452 - INFO - 顺序分配客户端ID: 2
2025-08-01 11:15:33,453 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 2
2025-08-01 11:15:33,453 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:15:33,453 - WARNING - [Client 2] 数据源为None，已创建新数据源
2025-08-01 11:15:33,453 - WARNING - [Client 2] 数据源trainset为None，已设置为空列表
2025-08-01 11:15:34,040 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:15:34,040 - INFO - [Client 2] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:15:34,041 - INFO - [Client 2] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:15:34,045 - INFO - [Client 2] 成功划分数据集，分配到 300 个样本
2025-08-01 11:15:34,045 - INFO - [Client 2] 初始化时成功加载数据
2025-08-01 11:15:34,045 - INFO - [客户端 2] 初始化验证通过
2025-08-01 11:15:34,045 - INFO - 客户端 2 实例创建成功
2025-08-01 11:15:34,045 - INFO - 客户端2已设置服务器引用
2025-08-01 11:15:34,045 - INFO - 客户端 2 已设置服务器引用
2025-08-01 11:15:34,045 - INFO - 客户端2已注册
2025-08-01 11:15:34,046 - INFO - 客户端 2 已成功注册到服务器
2025-08-01 11:15:34,046 - INFO - 开始创建客户端 3...
2025-08-01 11:15:34,046 - INFO - 初始化客户端, ID: 3
2025-08-01 11:15:34,046 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:34,061 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:34,061 - INFO - [Client 3] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:34,061 - INFO - [Trainer] 初始化训练器, client_id: 3
2025-08-01 11:15:34,071 - INFO - [Trainer 3] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:34,072 - INFO - [Trainer 3] 模型的输入通道数: 3
2025-08-01 11:15:34,072 - INFO - [Trainer 3] 强制使用CPU
2025-08-01 11:15:34,072 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:15:34,072 - INFO - [Trainer 3] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:34,072 - INFO - [Trainer 3] 初始化完成
2025-08-01 11:15:34,072 - INFO - [Client 3] 创建新训练器
2025-08-01 11:15:34,072 - INFO - [Algorithm] 从训练器获取客户端ID: 3
2025-08-01 11:15:34,073 - INFO - [Algorithm] 初始化后修正client_id: 3
2025-08-01 11:15:34,073 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:34,073 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:34,073 - INFO - [Client 3] 创建新算法
2025-08-01 11:15:34,073 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:15:34,073 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:15:34,073 - INFO - [Client None] 父类初始化完成
2025-08-01 11:15:34,073 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:34,087 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:34,087 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:34,087 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:15:34,096 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:34,096 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:15:34,096 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:15:34,096 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:15:34,097 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:34,097 - INFO - [Trainer None] 初始化完成
2025-08-01 11:15:34,097 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:15:34,097 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:15:34,097 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:15:34,097 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:34,097 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:34,097 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:15:34,097 - INFO - [Client None] 开始加载数据
2025-08-01 11:15:34,097 - INFO - 顺序分配客户端ID: 3
2025-08-01 11:15:34,097 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 3
2025-08-01 11:15:34,097 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:15:34,098 - WARNING - [Client 3] 数据源为None，已创建新数据源
2025-08-01 11:15:34,098 - WARNING - [Client 3] 数据源trainset为None，已设置为空列表
2025-08-01 11:15:34,693 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:15:34,693 - INFO - [Client 3] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:15:34,694 - INFO - [Client 3] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:15:34,698 - INFO - [Client 3] 成功划分数据集，分配到 300 个样本
2025-08-01 11:15:34,698 - INFO - [Client 3] 初始化时成功加载数据
2025-08-01 11:15:34,698 - INFO - [客户端 3] 初始化验证通过
2025-08-01 11:15:34,698 - INFO - 客户端 3 实例创建成功
2025-08-01 11:15:34,698 - INFO - 客户端3已设置服务器引用
2025-08-01 11:15:34,698 - INFO - 客户端 3 已设置服务器引用
2025-08-01 11:15:34,698 - INFO - 客户端3已注册
2025-08-01 11:15:34,698 - INFO - 客户端 3 已成功注册到服务器
2025-08-01 11:15:34,699 - INFO - 开始创建客户端 4...
2025-08-01 11:15:34,699 - INFO - 初始化客户端, ID: 4
2025-08-01 11:15:34,699 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:34,714 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:34,714 - INFO - [Client 4] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:34,715 - INFO - [Trainer] 初始化训练器, client_id: 4
2025-08-01 11:15:34,724 - INFO - [Trainer 4] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:34,725 - INFO - [Trainer 4] 模型的输入通道数: 3
2025-08-01 11:15:34,725 - INFO - [Trainer 4] 强制使用CPU
2025-08-01 11:15:34,725 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 11:15:34,725 - INFO - [Trainer 4] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:34,725 - INFO - [Trainer 4] 初始化完成
2025-08-01 11:15:34,725 - INFO - [Client 4] 创建新训练器
2025-08-01 11:15:34,726 - INFO - [Algorithm] 从训练器获取客户端ID: 4
2025-08-01 11:15:34,726 - INFO - [Algorithm] 初始化后修正client_id: 4
2025-08-01 11:15:34,726 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:34,726 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:34,726 - INFO - [Client 4] 创建新算法
2025-08-01 11:15:34,726 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:15:34,726 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:15:34,726 - INFO - [Client None] 父类初始化完成
2025-08-01 11:15:34,726 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:34,740 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:34,741 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:34,741 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:15:34,749 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:34,750 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:15:34,750 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:15:34,750 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:15:34,750 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:34,750 - INFO - [Trainer None] 初始化完成
2025-08-01 11:15:34,750 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:15:34,750 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:15:34,751 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:15:34,751 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:34,751 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:34,751 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:15:34,751 - INFO - [Client None] 开始加载数据
2025-08-01 11:15:34,751 - INFO - 顺序分配客户端ID: 4
2025-08-01 11:15:34,751 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 4
2025-08-01 11:15:34,751 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:15:34,751 - WARNING - [Client 4] 数据源为None，已创建新数据源
2025-08-01 11:15:34,751 - WARNING - [Client 4] 数据源trainset为None，已设置为空列表
2025-08-01 11:15:35,328 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:15:35,329 - INFO - [Client 4] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:15:35,329 - INFO - [Client 4] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:15:35,332 - INFO - [Client 4] 成功划分数据集，分配到 300 个样本
2025-08-01 11:15:35,332 - INFO - [Client 4] 初始化时成功加载数据
2025-08-01 11:15:35,332 - INFO - [客户端 4] 初始化验证通过
2025-08-01 11:15:35,334 - INFO - 客户端 4 实例创建成功
2025-08-01 11:15:35,334 - INFO - 客户端4已设置服务器引用
2025-08-01 11:15:35,334 - INFO - 客户端 4 已设置服务器引用
2025-08-01 11:15:35,334 - INFO - 客户端4已注册
2025-08-01 11:15:35,334 - INFO - 客户端 4 已成功注册到服务器
2025-08-01 11:15:35,334 - INFO - 开始创建客户端 5...
2025-08-01 11:15:35,334 - INFO - 初始化客户端, ID: 5
2025-08-01 11:15:35,334 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:35,349 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:35,349 - INFO - [Client 5] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:35,349 - INFO - [Trainer] 初始化训练器, client_id: 5
2025-08-01 11:15:35,358 - INFO - [Trainer 5] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:35,358 - INFO - [Trainer 5] 模型的输入通道数: 3
2025-08-01 11:15:35,358 - INFO - [Trainer 5] 强制使用CPU
2025-08-01 11:15:35,358 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 11:15:35,358 - INFO - [Trainer 5] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:35,358 - INFO - [Trainer 5] 初始化完成
2025-08-01 11:15:35,358 - INFO - [Client 5] 创建新训练器
2025-08-01 11:15:35,358 - INFO - [Algorithm] 从训练器获取客户端ID: 5
2025-08-01 11:15:35,359 - INFO - [Algorithm] 初始化后修正client_id: 5
2025-08-01 11:15:35,359 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:35,359 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:35,359 - INFO - [Client 5] 创建新算法
2025-08-01 11:15:35,359 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:15:35,359 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:15:35,359 - INFO - [Client None] 父类初始化完成
2025-08-01 11:15:35,359 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:35,374 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:35,374 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:35,374 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:15:35,382 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:35,382 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:15:35,383 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:15:35,383 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:15:35,383 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:35,383 - INFO - [Trainer None] 初始化完成
2025-08-01 11:15:35,383 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:15:35,383 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:15:35,383 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:15:35,383 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:35,383 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:35,384 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:15:35,384 - INFO - [Client None] 开始加载数据
2025-08-01 11:15:35,384 - INFO - 顺序分配客户端ID: 5
2025-08-01 11:15:35,384 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 5
2025-08-01 11:15:35,384 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:15:35,384 - WARNING - [Client 5] 数据源为None，已创建新数据源
2025-08-01 11:15:35,384 - WARNING - [Client 5] 数据源trainset为None，已设置为空列表
2025-08-01 11:15:35,963 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:15:35,963 - INFO - [Client 5] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:15:35,964 - INFO - [Client 5] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:15:35,967 - INFO - [Client 5] 成功划分数据集，分配到 300 个样本
2025-08-01 11:15:35,967 - INFO - [Client 5] 初始化时成功加载数据
2025-08-01 11:15:35,967 - INFO - [客户端 5] 初始化验证通过
2025-08-01 11:15:35,967 - INFO - 客户端 5 实例创建成功
2025-08-01 11:15:35,967 - INFO - 客户端5已设置服务器引用
2025-08-01 11:15:35,967 - INFO - 客户端 5 已设置服务器引用
2025-08-01 11:15:35,968 - INFO - 客户端5已注册
2025-08-01 11:15:35,968 - INFO - 客户端 5 已成功注册到服务器
2025-08-01 11:15:35,968 - INFO - 开始创建客户端 6...
2025-08-01 11:15:35,968 - INFO - 初始化客户端, ID: 6
2025-08-01 11:15:35,968 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:35,983 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:35,985 - INFO - [Client 6] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:35,985 - INFO - [Trainer] 初始化训练器, client_id: 6
2025-08-01 11:15:35,995 - INFO - [Trainer 6] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:35,995 - INFO - [Trainer 6] 模型的输入通道数: 3
2025-08-01 11:15:35,995 - INFO - [Trainer 6] 强制使用CPU
2025-08-01 11:15:35,996 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:15:35,996 - INFO - [Trainer 6] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:35,996 - INFO - [Trainer 6] 初始化完成
2025-08-01 11:15:35,996 - INFO - [Client 6] 创建新训练器
2025-08-01 11:15:35,996 - INFO - [Algorithm] 从训练器获取客户端ID: 6
2025-08-01 11:15:35,996 - INFO - [Algorithm] 初始化后修正client_id: 6
2025-08-01 11:15:35,996 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:35,997 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:35,997 - INFO - [Client 6] 创建新算法
2025-08-01 11:15:35,997 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:15:35,997 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:15:35,997 - INFO - [Client None] 父类初始化完成
2025-08-01 11:15:35,997 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:15:36,011 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:15:36,011 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:15:36,012 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:15:36,123 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:15:36,123 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:15:36,123 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:15:36,124 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:15:36,124 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:36,124 - INFO - [Trainer None] 初始化完成
2025-08-01 11:15:36,124 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:15:36,124 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:15:36,125 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:15:36,125 - INFO - [Algorithm] 初始化完成
2025-08-01 11:15:36,125 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:15:36,125 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:15:36,125 - INFO - [Client None] 开始加载数据
2025-08-01 11:15:36,125 - INFO - 顺序分配客户端ID: 6
2025-08-01 11:15:36,125 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 6
2025-08-01 11:15:36,125 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:15:36,125 - WARNING - [Client 6] 数据源为None，已创建新数据源
2025-08-01 11:15:36,125 - WARNING - [Client 6] 数据源trainset为None，已设置为空列表
2025-08-01 11:15:36,709 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:15:36,709 - INFO - [Client 6] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:15:36,709 - INFO - [Client 6] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:15:36,713 - INFO - [Client 6] 成功划分数据集，分配到 300 个样本
2025-08-01 11:15:36,713 - INFO - [Client 6] 初始化时成功加载数据
2025-08-01 11:15:36,713 - INFO - [客户端 6] 初始化验证通过
2025-08-01 11:15:36,713 - INFO - 客户端 6 实例创建成功
2025-08-01 11:15:36,713 - INFO - 客户端6已设置服务器引用
2025-08-01 11:15:36,713 - INFO - 客户端 6 已设置服务器引用
2025-08-01 11:15:36,714 - INFO - 客户端6已注册
2025-08-01 11:15:36,714 - INFO - 客户端 6 已成功注册到服务器
2025-08-01 11:15:36,714 - INFO - 已成功创建和注册 6 个客户端
2025-08-01 11:15:36,714 - INFO - 服务器属性检查:
2025-08-01 11:15:36,714 - INFO - - 客户端数量: 6
2025-08-01 11:15:36,714 - INFO - - 全局模型: 已初始化
2025-08-01 11:15:36,714 - INFO - - 算法: 已初始化
2025-08-01 11:15:36,714 - INFO - - 训练器: 已初始化
2025-08-01 11:15:36,714 - INFO - 准备启动服务器...
2025-08-01 11:15:36,714 - INFO - [Server #20288] 启动中...
2025-08-01 11:15:36,714 - INFO - 服务器将使用事件循环: <ProactorEventLoop running=False closed=False debug=False>
2025-08-01 11:15:36,715 - INFO - [Client 1] 模型已放置到设备: cpu
2025-08-01 11:15:36,721 - INFO - [Client 1] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,729 - INFO - [Client 1] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,729 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:15:36,729 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:15:36,729 - INFO - [Client 1] 已更新algorithm的client_id
2025-08-01 11:15:36,729 - INFO - [Client 1] 模型初始化完成
2025-08-01 11:15:36,729 - INFO - 客户端 1 模型初始化成功
2025-08-01 11:15:36,730 - INFO - 客户端 1 异步训练线程已启动
2025-08-01 11:15:36,730 - INFO - [Client 2] 模型已放置到设备: cpu
2025-08-01 11:15:36,736 - INFO - [Client 2] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,742 - INFO - [Client 2] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,743 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:15:36,744 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:15:36,744 - INFO - [Client 2] 已更新algorithm的client_id
2025-08-01 11:15:36,745 - INFO - [Client 2] 模型初始化完成
2025-08-01 11:15:36,745 - INFO - 客户端 2 模型初始化成功
2025-08-01 11:15:36,746 - INFO - 客户端 2 异步训练线程已启动
2025-08-01 11:15:36,747 - INFO - [Client 3] 模型已放置到设备: cpu
2025-08-01 11:15:36,755 - INFO - [Client 3] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,763 - INFO - [Client 3] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,763 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:15:36,763 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:15:36,764 - INFO - [Client 3] 已更新algorithm的client_id
2025-08-01 11:15:36,764 - INFO - [Client 3] 模型初始化完成
2025-08-01 11:15:36,764 - INFO - 客户端 3 模型初始化成功
2025-08-01 11:15:36,765 - INFO - 客户端 3 异步训练线程已启动
2025-08-01 11:15:36,765 - INFO - [Client 4] 模型已放置到设备: cpu
2025-08-01 11:15:36,771 - INFO - [Client 4] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,778 - INFO - [Client 4] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,779 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:15:36,779 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:15:36,779 - INFO - [Client 4] 已更新algorithm的client_id
2025-08-01 11:15:36,779 - INFO - [Client 4] 模型初始化完成
2025-08-01 11:15:36,779 - INFO - 客户端 4 模型初始化成功
2025-08-01 11:15:36,780 - INFO - 客户端 4 异步训练线程已启动
2025-08-01 11:15:36,780 - INFO - [Client 5] 模型已放置到设备: cpu
2025-08-01 11:15:36,785 - INFO - [Client 5] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,793 - INFO - [Client 5] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,793 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:15:36,793 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:15:36,793 - INFO - [Client 5] 已更新algorithm的client_id
2025-08-01 11:15:36,793 - INFO - [Client 5] 模型初始化完成
2025-08-01 11:15:36,794 - INFO - 客户端 5 模型初始化成功
2025-08-01 11:15:36,794 - INFO - 客户端 5 异步训练线程已启动
2025-08-01 11:15:36,794 - INFO - [Client 6] 模型已放置到设备: cpu
2025-08-01 11:15:36,799 - INFO - [Client 6] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,807 - INFO - [Client 6] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:15:36,807 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:15:36,807 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:15:36,808 - INFO - [Client 6] 已更新algorithm的client_id
2025-08-01 11:15:36,808 - INFO - [Client 6] 模型初始化完成
2025-08-01 11:15:36,808 - INFO - 客户端 6 模型初始化成功
2025-08-01 11:15:36,808 - INFO - 客户端 6 异步训练线程已启动
2025-08-01 11:15:36,808 - INFO - 服务器主循环任务已启动: <Task pending name='Task-1' coro=<Server.run() running at D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py:1302>>
2025-08-01 11:15:36,808 - INFO - Starting a server at address 127.0.0.1 and port 8000.
2025-08-01 11:15:36,812 - INFO - [Server #20288] 开始训练，共有 6 个客户端，每轮最多聚合 3 个客户端
2025-08-01 11:15:36,812 - INFO - 总训练轮次: 10
2025-08-01 11:15:36,813 - INFO - 🚀 开始第 1 轮训练（目标：10 轮）
2025-08-01 11:15:36,813 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:15:36,813 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:15:37,215 - INFO - 客户端 2 开始异步训练循环
2025-08-01 11:15:37,215 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:15:37,216 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 11:15:37,217 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 11:15:37,218 - INFO - [Client 2] 开始验证训练集
2025-08-01 11:15:37,239 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:15:37,240 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 11:15:37,240 - INFO - [Trainer 2] 开始训练
2025-08-01 11:15:37,240 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 11:15:37,240 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 11:15:37,241 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:37,241 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:15:37,242 - INFO - [Trainer 2] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:15:37,242 - INFO - [Trainer 2] 开始第 1/2 个epoch
2025-08-01 11:15:37,259 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0791, y=[8, 8, 9, 8, 8]
2025-08-01 11:15:37,259 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 11:15:37,263 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 11:15:37,276 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2406
2025-08-01 11:15:37,276 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:37,276 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 11:15:37,477 - INFO - [Trainer 2] Batch 0, Loss: 2.7338
2025-08-01 11:15:37,819 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:15:37,819 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:15:38,694 - INFO - [Trainer 2] Batch 5, Loss: 1.3230
2025-08-01 11:15:38,834 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:15:38,835 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:15:39,405 - INFO - [Trainer 2] Epoch 1 进度: 10/10 批次
2025-08-01 11:15:39,412 - INFO - [Trainer 2] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0562
2025-08-01 11:15:39,412 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:39,412 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 11:15:39,467 - INFO - [Trainer 2] Batch 9, Loss: 0.0177
2025-08-01 11:15:39,594 - INFO - [Trainer 2] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:15:39,594 - INFO - [Trainer 2] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.9521, Accuracy: 85.33%
2025-08-01 11:15:39,595 - INFO - [Trainer 2] 开始第 2/2 个epoch
2025-08-01 11:15:39,598 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1879, y=[8, 8, 8, 8, 8]
2025-08-01 11:15:39,599 - INFO - [Trainer 2] Epoch 2 开始处理 10 个批次
2025-08-01 11:15:39,602 - INFO - [Trainer 2] Epoch 2 进度: 1/10 批次
2025-08-01 11:15:39,619 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1598
2025-08-01 11:15:39,619 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:39,619 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 11:15:39,682 - INFO - [Trainer 2] Batch 0, Loss: 0.0040
2025-08-01 11:15:39,836 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:15:39,836 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:15:40,617 - INFO - [Trainer 2] Batch 5, Loss: 0.2115
2025-08-01 11:15:40,851 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:15:40,851 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:15:41,233 - INFO - [Trainer 2] Epoch 2 进度: 10/10 批次
2025-08-01 11:15:41,243 - INFO - [Trainer 2] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1496
2025-08-01 11:15:41,243 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:41,245 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 11:15:41,290 - INFO - [Trainer 2] Batch 9, Loss: 0.0159
2025-08-01 11:15:41,377 - INFO - [Trainer 2] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:15:41,377 - INFO - [Trainer 2] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.7111, Accuracy: 95.67%
2025-08-01 11:15:41,379 - INFO - [Trainer 2] 参数 conv1.weight: 平均值=0.005324, 标准差=0.121934
2025-08-01 11:15:41,379 - INFO - [Trainer 2] 参数 bn1.weight: 平均值=1.000079, 标准差=0.020186
2025-08-01 11:15:41,379 - INFO - [Trainer 2] 参数 bn1.bias: 平均值=-0.002864, 标准差=0.020325
2025-08-01 11:15:41,379 - INFO - [Trainer 2] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:15:41,379 - INFO - [Trainer.get_report] 客户端 2 训练报告 - Loss: 0.8316, Accuracy: 90.50%, 陈旧度: 0
2025-08-01 11:15:41,379 - INFO - [Trainer 2] 训练报告生成完成: Loss=0.8316, Accuracy=90.50%
2025-08-01 11:15:41,379 - INFO - [Client 2] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:15:41,380 - INFO - [Client 2] 使用训练准确率作为客户端准确率: 0.9050
2025-08-01 11:15:41,380 - INFO - [Client 2] 第 1 轮训练完成，耗时: 4.16秒, 准确率: 0.9050
2025-08-01 11:15:41,380 - INFO - [Client 2] 开始提取模型权重
2025-08-01 11:15:41,380 - INFO - [Client 2] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:15:41,380 - INFO - [Client 2] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:15:41,380 - INFO - [Client 2] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:15:41,380 - INFO - [Client 2] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:15:41,380 - INFO - [Client 2] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:15:41,380 - INFO - [服务器] 🔄 收到客户端 2 的模型更新，模型版本: unknown
2025-08-01 11:15:41,380 - INFO - [服务器] 客户端 2 训练信息 - 样本数: 300, 训练时间: 4.16秒
2025-08-01 11:15:41,380 - INFO - [服务器] 当前缓冲池大小: 0, 全局轮次: 0
2025-08-01 11:15:41,383 - INFO - [客户端权重摘要] 客户端2 | 参数数量: 74, 均值: 0.001626, 最大: 20.000000, 最小: -0.444423
2025-08-01 11:15:41,383 - INFO - 客户端 2 更新记录 - 陈旧度: 0, 提交轮次: 0
2025-08-01 11:15:41,383 - INFO - ✅ 客户端 2 的更新已加入成功缓冲池，当前池大小: 1
2025-08-01 11:15:41,383 - INFO - 📊 当前缓冲池中的客户端: [2]
2025-08-01 11:15:41,383 - INFO - 🔍 客户端 2 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:15:41,383 - INFO - 成功处理客户端 2 的更新
2025-08-01 11:15:41,384 - INFO - [Client 2] ✅ 成功上传训练结果到服务器
2025-08-01 11:15:41,384 - INFO - 客户端 2 训练完成
2025-08-01 11:15:41,493 - INFO - 客户端 4 开始异步训练循环
2025-08-01 11:15:41,493 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:15:41,494 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 11:15:41,494 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 11:15:41,494 - INFO - [Client 4] 开始验证训练集
2025-08-01 11:15:41,494 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:15:41,495 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 11:15:41,495 - INFO - [Trainer 4] 开始训练
2025-08-01 11:15:41,495 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 11:15:41,495 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 11:15:41,496 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:41,496 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:15:41,496 - INFO - [Trainer 4] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:15:41,496 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 11:15:41,502 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2488, y=[1, 1, 1, 1, 0]
2025-08-01 11:15:41,502 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 11:15:41,508 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 11:15:41,524 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2607
2025-08-01 11:15:41,525 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:41,526 - INFO - [Trainer 4] 标签样本: [1, 1, 1, 1, 0]
2025-08-01 11:15:41,602 - INFO - [Trainer 4] Batch 0, Loss: 2.1406
2025-08-01 11:15:41,698 - INFO - 客户端 5 开始异步训练循环
2025-08-01 11:15:41,698 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:15:41,700 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 11:15:41,700 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 11:15:41,700 - INFO - [Client 5] 开始验证训练集
2025-08-01 11:15:41,701 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:15:41,701 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 11:15:41,701 - INFO - [Trainer 5] 开始训练
2025-08-01 11:15:41,701 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 11:15:41,702 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 11:15:41,703 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:41,703 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:15:41,703 - INFO - [Trainer 5] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:15:41,703 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 11:15:41,715 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3604, y=[7, 7, 7, 7, 9]
2025-08-01 11:15:41,717 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 11:15:41,726 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 11:15:41,742 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4091
2025-08-01 11:15:41,744 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:41,744 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 3, 7]
2025-08-01 11:15:41,852 - INFO - [Trainer 5] Batch 0, Loss: 2.0281
2025-08-01 11:15:41,867 - WARNING - 配置文件设置min_clients_for_aggregation=1，可能导致每轮只聚合一个客户端
2025-08-01 11:15:41,868 - INFO - 异步模式下，当前有 1 个客户端更新，满足最小阈值 1，可以触发聚合
2025-08-01 11:15:41,868 - INFO - ✅ 触发聚合条件，开始第 0 轮聚合
2025-08-01 11:15:41,868 - INFO - 🔒 开始聚合过程，轮次: 0
2025-08-01 11:15:41,871 - DEBUG - 估计客户端2训练+通信时间: 2.5214855610722426秒 (计算: 1.8333476808119265秒, 通信: 0.6881378802603163秒, 距离: 2.51km, 带宽: 369109Hz, SNR: 1.99e+04)
2025-08-01 11:15:41,871 - INFO - 候选客户端按训练时间排序: [(2, np.float64(2.5214855610722426))]
2025-08-01 11:15:41,871 - INFO - 算法参数: V=1.0, max_clients=3, tau_max=5
2025-08-01 11:15:41,872 - DEBUG - 尝试聚合 1 客户端 (当前客户端: 2)
2025-08-01 11:15:41,872 - DEBUG -   模拟D_t: 2.5215, V*D_t: 2.5215
2025-08-01 11:15:41,872 - DEBUG -   惩罚项: 0.0000, 总目标值: 2.5215
2025-08-01 11:15:41,872 - DEBUG - 更新最佳集合：目标值 2.5215 < 之前最小 2.5215
2025-08-01 11:15:41,872 - INFO - 贪心策略选择 1 个客户端进行聚合: [2]
2025-08-01 11:15:41,872 - INFO - 最优目标函数值: 2.5215
2025-08-01 11:15:41,872 - INFO - 开始执行聚合 - 参与聚合的客户端数量: 1个
2025-08-01 11:15:41,873 - INFO - [Trainer.update_staleness] 客户端 2 陈旧度更新为: 0
2025-08-01 11:15:41,873 - DEBUG - 参与聚合的客户端 2 陈旧度: 0 (当前服务器轮次:0, 客户端拉取模型轮次:0)
2025-08-01 11:15:41,873 - DEBUG - 客户端 2 在服务器轮次 0 被聚合
2025-08-01 11:15:41,873 - INFO - 聚合执行 - 聚合客户端ID: [2]
2025-08-01 11:15:41,885 - INFO - [聚合前全局权重] 均值: 0.001180, 最大: 1.000000, 最小: -0.191991
2025-08-01 11:15:41,885 - INFO - [Algorithm] 客户端 2 - 样本数: 300, 陈旧度: 0, 陈旧度因子: 1.0000, 原始权重: 300.0000
2025-08-01 11:15:41,885 - INFO - [Algorithm] 客户端 2 最终权重: 1.0000
2025-08-01 11:15:41,915 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-08-01 11:15:41,915 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-08-01 11:15:41,916 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-08-01 11:15:41,923 - INFO - [Algorithm] 成功加载权重到模型
2025-08-01 11:15:41,931 - INFO - [聚合后全局权重] 均值: 0.001626, 最大: 20.000000, 最小: -0.444423
2025-08-01 11:15:41,941 - INFO - [权重变化] 平均绝对差异: 0.014404
2025-08-01 11:15:41,946 - INFO - ✅ 创建权重变化记录文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs/weights_change_20250801_111541.csv
2025-08-01 11:15:41,947 - INFO - 轮次 0 权重变化记录 - 平均变化: 0.014404, 均值: 0.001626, 最大: 20.000000, 最小: -0.444423
2025-08-01 11:15:41,947 - INFO - 服务器轮次 0 聚合完成: 更新了全局模型
2025-08-01 11:15:41,947 - INFO - [DEBUG] simulate_wall_time=True, asynchronous_mode=True
2025-08-01 11:15:41,948 - INFO - [DEBUG] wall_time更新: 1754018132.1838253 -> 1754018132.7593272 (增加了0.5755019187927246秒)
2025-08-01 11:15:41,948 - INFO - 记录轮次: 0
2025-08-01 11:15:41,948 - INFO - 聚合完成，进入新轮次，当前全局轮次: 1
2025-08-01 11:15:41,948 - INFO - 更新最后聚合时间: 1754018141.9486485
2025-08-01 11:15:41,969 - INFO - 为训练器创建了模型的深拷贝，重置BatchNorm统计信息，并移至设备: cpu
2025-08-01 11:15:41,970 - INFO - 已将全局权重加载到评估模型中
2025-08-01 11:15:41,970 - INFO - 开始评估全局模型，测试集大小: 10000
2025-08-01 11:15:42,617 - INFO - 客户端 6 开始异步训练循环
2025-08-01 11:15:42,617 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:15:42,619 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 11:15:42,620 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 11:15:42,620 - INFO - [Client 6] 开始验证训练集
2025-08-01 11:15:42,623 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:15:42,624 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 11:15:42,624 - INFO - [Trainer 6] 开始训练
2025-08-01 11:15:42,624 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 11:15:42,626 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:15:42,627 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:42,627 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:15:42,629 - INFO - [Trainer 6] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:15:42,629 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 11:15:42,673 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1970, y=[0, 7, 7, 7, 0]
2025-08-01 11:15:42,673 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 11:15:42,727 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 11:15:42,745 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2655
2025-08-01 11:15:42,746 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:42,747 - INFO - [Trainer 6] 标签样本: [7, 0, 0, 0, 0]
2025-08-01 11:15:43,013 - INFO - [Trainer 6] Batch 0, Loss: 2.1942
2025-08-01 11:15:43,720 - INFO - [Trainer 4] Batch 5, Loss: 1.0429
2025-08-01 11:15:44,025 - INFO - [Trainer 5] Batch 5, Loss: 0.5961
2025-08-01 11:15:44,362 - INFO - 客户端 1 开始异步训练循环
2025-08-01 11:15:44,363 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:15:44,365 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 11:15:44,366 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 11:15:44,368 - INFO - [Client 1] 开始验证训练集
2025-08-01 11:15:44,372 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:15:44,372 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 11:15:44,373 - INFO - [Trainer 1] 开始训练
2025-08-01 11:15:44,373 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 11:15:44,379 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:15:44,381 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:44,385 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:15:44,388 - INFO - [Trainer 1] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:15:44,388 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 11:15:44,440 - INFO - 客户端 3 开始异步训练循环
2025-08-01 11:15:44,440 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:15:44,441 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 11:15:44,441 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 11:15:44,443 - INFO - [Client 3] 开始验证训练集
2025-08-01 11:15:44,445 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:15:44,447 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 11:15:44,447 - INFO - [Trainer 3] 开始训练
2025-08-01 11:15:44,448 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 11:15:44,451 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:15:44,452 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:15:44,453 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:15:44,455 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1831, y=[5, 8, 5, 5, 5]
2025-08-01 11:15:44,456 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 11:15:44,456 - INFO - [Trainer 3] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:15:44,456 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 11:15:44,530 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 11:15:44,555 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0741
2025-08-01 11:15:44,561 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:44,566 - INFO - [Trainer 1] 标签样本: [0, 8, 5, 0, 0]
2025-08-01 11:15:44,587 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3983, y=[2, 2, 2, 2, 7]
2025-08-01 11:15:44,590 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 11:15:44,628 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 11:15:44,651 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4128
2025-08-01 11:15:44,655 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:44,656 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:15:45,066 - INFO - [Trainer 1] Batch 0, Loss: 2.3996
2025-08-01 11:15:45,083 - INFO - [Trainer 3] Batch 0, Loss: 2.4025
2025-08-01 11:15:45,876 - INFO - [Trainer 4] Epoch 1 进度: 10/10 批次
2025-08-01 11:15:45,898 - INFO - [Trainer 4] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0955
2025-08-01 11:15:45,901 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:45,902 - INFO - [Trainer 4] 标签样本: [1, 0, 1, 1, 0]
2025-08-01 11:15:46,141 - INFO - [Trainer 6] Batch 5, Loss: 1.5301
2025-08-01 11:15:46,172 - INFO - [Trainer 4] Batch 9, Loss: 0.6002
2025-08-01 11:15:46,321 - INFO - [Trainer 5] Epoch 1 进度: 10/10 批次
2025-08-01 11:15:46,335 - INFO - [Trainer 5] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4413
2025-08-01 11:15:46,338 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:46,341 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 7, 7]
2025-08-01 11:15:46,417 - INFO - [Trainer 4] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:15:46,420 - INFO - [Trainer 4] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.3152, Accuracy: 54.00%
2025-08-01 11:15:46,422 - INFO - [Trainer 4] 开始第 2/2 个epoch
2025-08-01 11:15:46,487 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4616, y=[0, 1, 1, 1, 0]
2025-08-01 11:15:46,488 - INFO - [Trainer 4] Epoch 2 开始处理 10 个批次
2025-08-01 11:15:46,542 - INFO - [Trainer 4] Epoch 2 进度: 1/10 批次
2025-08-01 11:15:46,551 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0283
2025-08-01 11:15:46,553 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:46,554 - INFO - [Trainer 4] 标签样本: [0, 0, 0, 1, 1]
2025-08-01 11:15:46,565 - INFO - [Trainer 5] Batch 9, Loss: 0.2871
2025-08-01 11:15:46,890 - INFO - [Trainer 4] Batch 0, Loss: 0.7875
2025-08-01 11:15:46,972 - INFO - [Trainer 5] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:15:46,975 - INFO - [Trainer 5] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.0489, Accuracy: 71.00%
2025-08-01 11:15:46,977 - INFO - [Trainer 5] 开始第 2/2 个epoch
2025-08-01 11:15:47,066 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3706, y=[7, 7, 3, 7, 3]
2025-08-01 11:15:47,070 - INFO - [Trainer 5] Epoch 2 开始处理 10 个批次
2025-08-01 11:15:47,152 - INFO - [Trainer 5] Epoch 2 进度: 1/10 批次
2025-08-01 11:15:47,179 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4092
2025-08-01 11:15:47,179 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:47,180 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 7, 7]
2025-08-01 11:15:47,590 - INFO - [Trainer 5] Batch 0, Loss: 0.4308
2025-08-01 11:15:49,187 - INFO - [Trainer 6] Epoch 1 进度: 10/10 批次
2025-08-01 11:15:49,194 - INFO - [Trainer 3] Batch 5, Loss: 0.9560
2025-08-01 11:15:49,198 - INFO - [Trainer 6] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3367
2025-08-01 11:15:49,198 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:49,199 - INFO - [Trainer 6] 标签样本: [8, 8, 0, 7, 0]
2025-08-01 11:15:49,241 - INFO - [Trainer 1] Batch 5, Loss: 1.7622
2025-08-01 11:15:49,443 - INFO - [Trainer 6] Batch 9, Loss: 1.4789
2025-08-01 11:15:49,758 - INFO - [Trainer 6] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:15:49,759 - INFO - [Trainer 6] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.5345, Accuracy: 43.00%
2025-08-01 11:15:49,759 - INFO - [Trainer 6] 开始第 2/2 个epoch
2025-08-01 11:15:49,846 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0487, y=[0, 8, 8, 0, 8]
2025-08-01 11:15:49,849 - INFO - [Trainer 6] Epoch 2 开始处理 10 个批次
2025-08-01 11:15:49,911 - INFO - [Trainer 6] Epoch 2 进度: 1/10 批次
2025-08-01 11:15:49,935 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0254
2025-08-01 11:15:49,936 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:49,937 - INFO - [Trainer 6] 标签样本: [8, 0, 0, 0, 0]
2025-08-01 11:15:50,216 - INFO - [Trainer 6] Batch 0, Loss: 2.2755
2025-08-01 11:15:51,060 - INFO - [Trainer 4] Batch 5, Loss: 1.0966
2025-08-01 11:15:51,754 - INFO - [Trainer 3] Epoch 1 进度: 10/10 批次
2025-08-01 11:15:51,758 - INFO - [Trainer 3] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1785
2025-08-01 11:15:51,761 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:51,762 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:15:51,786 - INFO - [Trainer 1] Epoch 1 进度: 10/10 批次
2025-08-01 11:15:51,792 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0197
2025-08-01 11:15:51,794 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:51,794 - INFO - [Trainer 1] 标签样本: [5, 5, 8, 0, 5]
2025-08-01 11:15:51,825 - INFO - [Trainer 5] Batch 5, Loss: 0.6721
2025-08-01 11:15:51,979 - INFO - [Trainer 3] Batch 9, Loss: 0.1364
2025-08-01 11:15:52,034 - INFO - [Trainer 1] Batch 9, Loss: 1.7556
2025-08-01 11:15:52,310 - INFO - [Trainer 1] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:15:52,310 - INFO - [Trainer 1] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.7000, Accuracy: 32.00%
2025-08-01 11:15:52,310 - INFO - [Trainer 1] 开始第 2/2 个epoch
2025-08-01 11:15:52,315 - INFO - [Trainer 3] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:15:52,316 - INFO - [Trainer 3] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.4419, Accuracy: 80.67%
2025-08-01 11:15:52,316 - INFO - [Trainer 3] 开始第 2/2 个epoch
2025-08-01 11:15:52,371 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2465, y=[4, 0, 5, 0, 5]
2025-08-01 11:15:52,371 - INFO - [Trainer 1] Epoch 2 开始处理 10 个批次
2025-08-01 11:15:52,380 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1582, y=[2, 2, 7, 2, 2]
2025-08-01 11:15:52,381 - INFO - [Trainer 3] Epoch 2 开始处理 10 个批次
2025-08-01 11:15:52,444 - INFO - [Trainer 1] Epoch 2 进度: 1/10 批次
2025-08-01 11:15:52,455 - INFO - [Trainer 3] Epoch 2 进度: 1/10 批次
2025-08-01 11:15:52,465 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2681
2025-08-01 11:15:52,466 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:52,466 - INFO - [Trainer 1] 标签样本: [5, 8, 8, 5, 4]
2025-08-01 11:15:52,475 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4894
2025-08-01 11:15:52,482 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:52,484 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:15:52,801 - INFO - [Trainer 3] Batch 0, Loss: 0.9221
2025-08-01 11:15:52,820 - INFO - [Trainer 1] Batch 0, Loss: 1.1203
2025-08-01 11:15:53,695 - INFO - [Trainer 4] Epoch 2 进度: 10/10 批次
2025-08-01 11:15:53,706 - INFO - [Trainer 4] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1269
2025-08-01 11:15:53,709 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:53,710 - INFO - [Trainer 4] 标签样本: [1, 0, 1, 0, 0]
2025-08-01 11:15:53,978 - INFO - [Trainer 4] Batch 9, Loss: 0.6290
2025-08-01 11:15:54,064 - INFO - [Trainer 6] Batch 5, Loss: 1.7978
2025-08-01 11:15:54,219 - INFO - [Trainer 5] Epoch 2 进度: 10/10 批次
2025-08-01 11:15:54,241 - INFO - [Trainer 5] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2521
2025-08-01 11:15:54,242 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:54,243 - INFO - [Trainer 5] 标签样本: [7, 7, 3, 7, 7]
2025-08-01 11:15:54,251 - INFO - [Trainer 4] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:15:54,252 - INFO - [Trainer 4] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.9878, Accuracy: 58.00%
2025-08-01 11:15:54,255 - INFO - [Trainer 4] 参数 conv1.weight: 平均值=-0.000219, 标准差=0.114614
2025-08-01 11:15:54,256 - INFO - [Trainer 4] 参数 bn1.weight: 平均值=0.999805, 标准差=0.012455
2025-08-01 11:15:54,257 - INFO - [Trainer 4] 参数 bn1.bias: 平均值=0.000033, 标准差=0.010220
2025-08-01 11:15:54,257 - INFO - [Trainer 4] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:15:54,259 - INFO - [Trainer.get_report] 客户端 4 训练报告 - Loss: 1.1515, Accuracy: 56.00%, 陈旧度: 0
2025-08-01 11:15:54,259 - INFO - [Trainer 4] 训练报告生成完成: Loss=1.1515, Accuracy=56.00%
2025-08-01 11:15:54,261 - INFO - [Client 4] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:15:54,262 - INFO - [Client 4] 使用训练准确率作为客户端准确率: 0.5600
2025-08-01 11:15:54,264 - INFO - [Client 4] 第 1 轮训练完成，耗时: 12.77秒, 准确率: 0.5600
2025-08-01 11:15:54,265 - INFO - [Client 4] 开始提取模型权重
2025-08-01 11:15:54,268 - INFO - [Client 4] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:15:54,273 - INFO - [Client 4] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:15:54,274 - INFO - [Client 4] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:15:54,275 - INFO - [Client 4] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:15:54,276 - INFO - [Client 4] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:15:54,276 - INFO - [服务器] 🔄 收到客户端 4 的模型更新，模型版本: unknown
2025-08-01 11:15:54,277 - INFO - [服务器] 客户端 4 训练信息 - 样本数: 300, 训练时间: 12.77秒
2025-08-01 11:15:54,278 - INFO - [服务器] 当前缓冲池大小: 1, 全局轮次: 1
2025-08-01 11:15:54,305 - INFO - [客户端权重摘要] 客户端4 | 参数数量: 74, 均值: 0.001314, 最大: 20.000000, 最小: -0.309636
2025-08-01 11:15:54,306 - INFO - 客户端 4 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:15:54,307 - INFO - ✅ 客户端 4 的更新已加入成功缓冲池，当前池大小: 2
2025-08-01 11:15:54,309 - INFO - 📊 当前缓冲池中的客户端: [2, 4]
2025-08-01 11:15:54,315 - INFO - 🔍 客户端 4 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:15:54,316 - INFO - 成功处理客户端 4 的更新
2025-08-01 11:15:54,316 - INFO - [Client 4] ✅ 成功上传训练结果到服务器
2025-08-01 11:15:54,316 - INFO - 客户端 4 训练完成
2025-08-01 11:15:54,365 - INFO - [Trainer 5] Batch 9, Loss: 0.9235
2025-08-01 11:15:54,563 - INFO - [Trainer 5] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:15:54,564 - INFO - [Trainer 5] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.8243, Accuracy: 74.33%
2025-08-01 11:15:54,565 - INFO - [Trainer 5] 参数 conv1.weight: 平均值=0.000930, 标准差=0.113067
2025-08-01 11:15:54,565 - INFO - [Trainer 5] 参数 bn1.weight: 平均值=0.999149, 标准差=0.007232
2025-08-01 11:15:54,566 - INFO - [Trainer 5] 参数 bn1.bias: 平均值=-0.000009, 标准差=0.006017
2025-08-01 11:15:54,567 - INFO - [Trainer 5] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:15:54,567 - INFO - [Trainer.get_report] 客户端 5 训练报告 - Loss: 0.9366, Accuracy: 72.67%, 陈旧度: 0
2025-08-01 11:15:54,567 - INFO - [Trainer 5] 训练报告生成完成: Loss=0.9366, Accuracy=72.67%
2025-08-01 11:15:54,568 - INFO - [Client 5] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:15:54,569 - INFO - [Client 5] 使用训练准确率作为客户端准确率: 0.7267
2025-08-01 11:15:54,570 - INFO - [Client 5] 第 1 轮训练完成，耗时: 12.87秒, 准确率: 0.7267
2025-08-01 11:15:54,570 - INFO - [Client 5] 开始提取模型权重
2025-08-01 11:15:54,578 - INFO - [Client 5] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:15:54,580 - INFO - [Client 5] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:15:54,580 - INFO - [Client 5] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:15:54,581 - INFO - [Client 5] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:15:54,582 - INFO - [Client 5] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:15:54,583 - INFO - [服务器] 🔄 收到客户端 5 的模型更新，模型版本: unknown
2025-08-01 11:15:54,583 - INFO - [服务器] 客户端 5 训练信息 - 样本数: 300, 训练时间: 12.87秒
2025-08-01 11:15:54,584 - INFO - [服务器] 当前缓冲池大小: 2, 全局轮次: 1
2025-08-01 11:15:54,596 - INFO - [客户端权重摘要] 客户端5 | 参数数量: 74, 均值: 0.001136, 最大: 20.000000, 最小: -0.276137
2025-08-01 11:15:54,599 - INFO - 客户端 5 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:15:54,600 - INFO - ✅ 客户端 5 的更新已加入成功缓冲池，当前池大小: 3
2025-08-01 11:15:54,602 - INFO - 📊 当前缓冲池中的客户端: [2, 4, 5]
2025-08-01 11:15:54,602 - INFO - 🔍 客户端 5 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:15:54,603 - INFO - 成功处理客户端 5 的更新
2025-08-01 11:15:54,603 - INFO - [Client 5] ✅ 成功上传训练结果到服务器
2025-08-01 11:15:54,604 - INFO - 客户端 5 训练完成
2025-08-01 11:15:55,623 - INFO - [Trainer 6] Epoch 2 进度: 10/10 批次
2025-08-01 11:15:55,648 - INFO - [Trainer 6] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.1269
2025-08-01 11:15:55,649 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:55,649 - INFO - [Trainer 6] 标签样本: [8, 0, 0, 8, 0]
2025-08-01 11:15:55,651 - INFO - [Trainer 1] Batch 5, Loss: 1.2505
2025-08-01 11:15:55,655 - INFO - [Trainer 3] Batch 5, Loss: 0.2988
2025-08-01 11:15:55,709 - INFO - [Trainer 6] Batch 9, Loss: 1.0297
2025-08-01 11:15:55,947 - INFO - [Trainer 6] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:15:55,947 - INFO - [Trainer 6] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.3828, Accuracy: 45.67%
2025-08-01 11:15:55,948 - INFO - [Trainer 6] 参数 conv1.weight: 平均值=0.002936, 标准差=0.115168
2025-08-01 11:15:55,948 - INFO - [Trainer 6] 参数 bn1.weight: 平均值=1.000822, 标准差=0.009624
2025-08-01 11:15:55,949 - INFO - [Trainer 6] 参数 bn1.bias: 平均值=-0.001646, 标准差=0.009379
2025-08-01 11:15:55,949 - INFO - [Trainer 6] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:15:55,950 - INFO - [Trainer.get_report] 客户端 6 训练报告 - Loss: 1.4587, Accuracy: 44.33%, 陈旧度: 0
2025-08-01 11:15:55,950 - INFO - [Trainer 6] 训练报告生成完成: Loss=1.4587, Accuracy=44.33%
2025-08-01 11:15:55,950 - INFO - [Client 6] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:15:55,951 - INFO - [Client 6] 使用训练准确率作为客户端准确率: 0.4433
2025-08-01 11:15:55,951 - INFO - [Client 6] 第 1 轮训练完成，耗时: 13.33秒, 准确率: 0.4433
2025-08-01 11:15:55,951 - INFO - [Client 6] 开始提取模型权重
2025-08-01 11:15:55,955 - INFO - [Client 6] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:15:55,955 - INFO - [Client 6] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:15:55,956 - INFO - [Client 6] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:15:55,956 - INFO - [Client 6] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:15:55,956 - INFO - [Client 6] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:15:55,956 - INFO - [服务器] 🔄 收到客户端 6 的模型更新，模型版本: unknown
2025-08-01 11:15:55,956 - INFO - [服务器] 客户端 6 训练信息 - 样本数: 300, 训练时间: 13.33秒
2025-08-01 11:15:55,957 - INFO - [服务器] 当前缓冲池大小: 3, 全局轮次: 1
2025-08-01 11:15:55,963 - INFO - [客户端权重摘要] 客户端6 | 参数数量: 74, 均值: 0.001009, 最大: 20.000000, 最小: -0.276188
2025-08-01 11:15:55,964 - INFO - 客户端 6 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:15:55,965 - INFO - ✅ 客户端 6 的更新已加入成功缓冲池，当前池大小: 4
2025-08-01 11:15:55,965 - INFO - 📊 当前缓冲池中的客户端: [2, 4, 5, 6]
2025-08-01 11:15:55,965 - INFO - 🔍 客户端 6 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:15:55,966 - INFO - 成功处理客户端 6 的更新
2025-08-01 11:15:55,966 - INFO - [Client 6] ✅ 成功上传训练结果到服务器
2025-08-01 11:15:55,967 - INFO - 客户端 6 训练完成
2025-08-01 11:15:57,002 - INFO - [Trainer 1] Epoch 2 进度: 10/10 批次
2025-08-01 11:15:57,010 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2577
2025-08-01 11:15:57,013 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:57,014 - INFO - [Trainer 1] 标签样本: [5, 8, 5, 8, 8]
2025-08-01 11:15:57,026 - INFO - [Trainer 3] Epoch 2 进度: 10/10 批次
2025-08-01 11:15:57,039 - INFO - [Trainer 3] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3846
2025-08-01 11:15:57,040 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:15:57,041 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 11:15:57,042 - INFO - [Trainer 1] Batch 9, Loss: 1.7564
2025-08-01 11:15:57,076 - INFO - [Trainer 3] Batch 9, Loss: 0.3798
2025-08-01 11:15:57,198 - INFO - [Trainer 1] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:15:57,198 - INFO - [Trainer 1] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.6761, Accuracy: 44.67%
2025-08-01 11:15:57,200 - INFO - [Trainer 1] 参数 conv1.weight: 平均值=0.003304, 标准差=0.115130
2025-08-01 11:15:57,200 - INFO - [Trainer 1] 参数 bn1.weight: 平均值=1.000311, 标准差=0.011154
2025-08-01 11:15:57,201 - INFO - [Trainer 1] 参数 bn1.bias: 平均值=-0.001211, 标准差=0.012155
2025-08-01 11:15:57,201 - INFO - [Trainer 1] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:15:57,201 - INFO - [Trainer.get_report] 客户端 1 训练报告 - Loss: 1.6881, Accuracy: 38.33%, 陈旧度: 0
2025-08-01 11:15:57,202 - INFO - [Trainer 1] 训练报告生成完成: Loss=1.6881, Accuracy=38.33%
2025-08-01 11:15:57,202 - INFO - [Client 1] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:15:57,202 - INFO - [Client 1] 使用训练准确率作为客户端准确率: 0.3833
2025-08-01 11:15:57,202 - INFO - [Client 1] 第 1 轮训练完成，耗时: 12.83秒, 准确率: 0.3833
2025-08-01 11:15:57,203 - INFO - [Client 1] 开始提取模型权重
2025-08-01 11:15:57,204 - INFO - [Client 1] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:15:57,204 - INFO - [Client 1] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:15:57,204 - INFO - [Client 1] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:15:57,204 - INFO - [Client 1] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:15:57,204 - INFO - [Client 1] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:15:57,205 - INFO - [服务器] 🔄 收到客户端 1 的模型更新，模型版本: unknown
2025-08-01 11:15:57,205 - INFO - [服务器] 客户端 1 训练信息 - 样本数: 300, 训练时间: 12.83秒
2025-08-01 11:15:57,205 - INFO - [服务器] 当前缓冲池大小: 4, 全局轮次: 1
2025-08-01 11:15:57,211 - INFO - [客户端权重摘要] 客户端1 | 参数数量: 74, 均值: 0.001242, 最大: 20.000000, 最小: -0.252761
2025-08-01 11:15:57,212 - INFO - 客户端 1 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:15:57,212 - INFO - ✅ 客户端 1 的更新已加入成功缓冲池，当前池大小: 5
2025-08-01 11:15:57,212 - INFO - 📊 当前缓冲池中的客户端: [2, 4, 5, 6, 1]
2025-08-01 11:15:57,213 - INFO - 🔍 客户端 1 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:15:57,213 - INFO - 成功处理客户端 1 的更新
2025-08-01 11:15:57,213 - INFO - [Client 1] ✅ 成功上传训练结果到服务器
2025-08-01 11:15:57,213 - INFO - 客户端 1 训练完成
2025-08-01 11:15:57,240 - INFO - [Trainer 3] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:15:57,241 - INFO - [Trainer 3] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.4768, Accuracy: 89.67%
2025-08-01 11:15:57,241 - INFO - [Trainer 3] 参数 conv1.weight: 平均值=-0.003999, 标准差=0.113586
2025-08-01 11:15:57,241 - INFO - [Trainer 3] 参数 bn1.weight: 平均值=1.000433, 标准差=0.012163
2025-08-01 11:15:57,242 - INFO - [Trainer 3] 参数 bn1.bias: 平均值=0.000482, 标准差=0.010451
2025-08-01 11:15:57,243 - INFO - [Trainer 3] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:15:57,243 - INFO - [Trainer.get_report] 客户端 3 训练报告 - Loss: 0.9594, Accuracy: 85.17%, 陈旧度: 0
2025-08-01 11:15:57,244 - INFO - [Trainer 3] 训练报告生成完成: Loss=0.9594, Accuracy=85.17%
2025-08-01 11:15:57,244 - INFO - [Client 3] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:15:57,244 - INFO - [Client 3] 使用训练准确率作为客户端准确率: 0.8517
2025-08-01 11:15:57,246 - INFO - [Client 3] 第 1 轮训练完成，耗时: 12.80秒, 准确率: 0.8517
2025-08-01 11:15:57,246 - INFO - [Client 3] 开始提取模型权重
2025-08-01 11:15:57,247 - INFO - [Client 3] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:15:57,247 - INFO - [Client 3] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:15:57,248 - INFO - [Client 3] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:15:57,248 - INFO - [Client 3] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:15:57,248 - INFO - [Client 3] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:15:57,248 - INFO - [服务器] 🔄 收到客户端 3 的模型更新，模型版本: unknown
2025-08-01 11:15:57,248 - INFO - [服务器] 客户端 3 训练信息 - 样本数: 300, 训练时间: 12.80秒
2025-08-01 11:15:57,249 - INFO - [服务器] 当前缓冲池大小: 5, 全局轮次: 1
2025-08-01 11:15:57,254 - INFO - [客户端权重摘要] 客户端3 | 参数数量: 74, 均值: 0.001019, 最大: 20.000000, 最小: -0.248070
2025-08-01 11:15:57,255 - INFO - 客户端 3 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 11:15:57,255 - INFO - ✅ 客户端 3 的更新已加入成功缓冲池，当前池大小: 6
2025-08-01 11:15:57,256 - INFO - 📊 当前缓冲池中的客户端: [2, 4, 5, 6, 1, 3]
2025-08-01 11:15:57,256 - INFO - 🔍 客户端 3 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:15:57,256 - INFO - 成功处理客户端 3 的更新
2025-08-01 11:15:57,257 - INFO - [Client 3] ✅ 成功上传训练结果到服务器
2025-08-01 11:15:57,257 - INFO - 客户端 3 训练完成
2025-08-01 11:16:02,285 - INFO - [Trainer None] 测试结果 - 准确率: 0.1000 (1000/10000), 损失: 84700.6117
2025-08-01 11:16:02,285 - INFO - 全局模型评估完成，准确率: 0.1000, 耗时: 20.32秒
2025-08-01 11:16:02,285 - WARNING - log_accuracy: 训练器的模型引用不一致，更新为当前全局模型
2025-08-01 11:16:02,286 - INFO - log_accuracy: 已同步训练器的模型引用
2025-08-01 11:16:02,286 - INFO - log_accuracy: 为训练器设置client_id=0
2025-08-01 11:16:02,286 - INFO - log_accuracy: 模型输入通道数: 3
2025-08-01 11:16:02,286 - ERROR - 准确率CSV文件未初始化，跳过记录
2025-08-01 11:16:02,287 - INFO - 创建准确率记录文件: ./results/scafl/mnist/accuracy_20250801_111602.csv
2025-08-01 11:16:02,288 - INFO - 轮次 1 准确率记录 - 当前: 10.00%, 平均(最近10轮): 10.00%, 最佳: 10.00%
2025-08-01 11:16:02,288 - INFO - 轮次: 1, 准确率: 0.1, 平均陈旧度: 0.0
2025-08-01 11:16:02,288 - INFO - 客户端 2 陈旧度: 0
2025-08-01 11:16:02,293 - INFO - [全局模型摘要] 轮次: 1 | 均值: 0.001626, 最大: 20.000000, 最小: -0.444423
2025-08-01 11:16:02,293 - INFO - [DEBUG] 结果记录时 - wall_time=1754018132.859327, initial_wall_time=1754018132.1838253, elapsed_time=0.675501823425293
2025-08-01 11:16:02,293 - INFO - 参与聚合的客户端平均准确率: 0.9050 (基于 1 个客户端)
2025-08-01 11:16:02,294 - INFO - 所有训练统计信息已记录到统一结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_111532.csv
2025-08-01 11:16:02,294 - INFO - 记录数据: {'round': 0, 'elapsed_time': 0.675501823425293, 'accuracy': 0.905, 'global_accuracy': 0.1, 'global_accuracy_std': 0.0, 'avg_staleness': 0.0, 'max_staleness': 0, 'min_staleness': 0, 'virtual_time': 0.0, 'aggregated_clients_count': 1}
2025-08-01 11:16:02,294 - DEBUG - 聚合后客户端 1 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:16:02,295 - DEBUG - 聚合后客户端 2 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:16:02,295 - DEBUG - 聚合后客户端 3 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:16:02,295 - DEBUG - 聚合后客户端 4 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:16:02,295 - DEBUG - 聚合后客户端 5 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:16:02,295 - DEBUG - 聚合后客户端 6 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 11:16:02,295 - INFO - 聚合完成，缓冲池中还剩 5 个更新，来自 5 个不同客户端
2025-08-01 11:16:02,295 - INFO - 🔓 聚合过程结束，轮次: 1
2025-08-01 11:16:02,295 - INFO - 🎉 第 0 轮聚合完成，当前轮次: 1
2025-08-01 11:16:02,295 - INFO - 🔄 聚合完成，重新启动客户端训练（第 1 轮）
2025-08-01 11:16:02,295 - INFO - 缓冲池已清空，准备新一轮训练
2025-08-01 11:16:02,295 - INFO - 重新启动客户端 1 的训练任务
2025-08-01 11:16:02,296 - INFO - 重新启动客户端 2 的训练任务
2025-08-01 11:16:02,296 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:16:02,296 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:16:02,297 - INFO - 重新启动客户端 3 的训练任务
2025-08-01 11:16:02,297 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 11:16:02,297 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 11:16:02,297 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 11:16:02,297 - INFO - [Client 3] 开始验证训练集
2025-08-01 11:16:02,297 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:16:02,297 - INFO - 重新启动客户端 4 的训练任务
2025-08-01 11:16:02,297 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 11:16:02,298 - INFO - [Client 3] 开始验证训练集
2025-08-01 11:16:02,298 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:16:02,298 - INFO - 重新启动客户端 5 的训练任务
2025-08-01 11:16:02,299 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 11:16:02,299 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:16:02,299 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 11:16:02,299 - INFO - 重新启动客户端 6 的训练任务
2025-08-01 11:16:02,300 - INFO - [Client 5] 开始验证训练集
2025-08-01 11:16:02,300 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 11:16:02,300 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 11:16:02,300 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:16:02,300 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:16:02,300 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:16:02,301 - INFO - ✅ 已重新启动 6 个客户端的训练任务
2025-08-01 11:16:02,301 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 11:16:02,301 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 11:16:02,301 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 11:16:02,301 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 11:16:02,301 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 11:16:02,302 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:16:02,302 - INFO - [Client 6] 开始验证训练集
2025-08-01 11:16:02,302 - INFO - [Client 6] 开始验证训练集
2025-08-01 11:16:02,302 - INFO - [Trainer 3] 开始训练
2025-08-01 11:16:02,302 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 11:16:02,302 - INFO - [Trainer 3] 开始训练
2025-08-01 11:16:02,302 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 11:16:02,303 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 11:16:02,303 - INFO - [Client 6] 开始验证训练集
2025-08-01 11:16:02,303 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 11:16:02,304 - INFO - [Trainer 5] 开始训练
2025-08-01 11:16:02,304 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:16:02,305 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 11:16:02,305 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:16:02,305 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 11:16:02,306 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 11:16:02,306 - INFO - [Trainer 6] 开始训练
2025-08-01 11:16:02,306 - INFO - [Trainer 6] 开始训练
2025-08-01 11:16:02,306 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 11:16:02,307 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 11:16:02,307 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:16:02,308 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 11:16:02,308 - INFO - [Trainer 6] 开始训练
2025-08-01 11:16:02,308 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 11:16:02,310 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:16:02,311 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:16:02,311 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:16:02,311 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:16:02,312 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:16:02,313 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:16:02,314 - INFO - [Trainer 3] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:16:02,314 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 11:16:02,314 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:16:02,314 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:16:02,315 - INFO - [Trainer 3] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:16:02,315 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:16:02,315 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 11:16:02,315 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 11:16:02,316 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:16:02,317 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:16:02,317 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:16:02,318 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:16:02,319 - INFO - [Trainer 6] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:16:02,319 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 11:16:02,319 - INFO - [Trainer 6] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:16:02,319 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:16:02,320 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 11:16:02,320 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:16:02,320 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:16:02,321 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:16:02,323 - INFO - [Trainer 6] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:16:02,323 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 11:16:02,323 - INFO - [Trainer 5] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:16:02,323 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 11:16:02,379 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2300, y=[2, 2, 2, 2, 2]
2025-08-01 11:16:02,380 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 11:16:02,381 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3309, y=[8, 7, 0, 7, 0]
2025-08-01 11:16:02,381 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 11:16:02,383 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3406, y=[0, 8, 0, 8, 0]
2025-08-01 11:16:02,383 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 11:16:02,385 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1012, y=[7, 8, 0, 8, 8]
2025-08-01 11:16:02,385 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3352, y=[2, 2, 2, 2, 2]
2025-08-01 11:16:02,386 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 11:16:02,386 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 11:16:02,387 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3239, y=[7, 7, 3, 7, 7]
2025-08-01 11:16:02,388 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 11:16:02,428 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 11:16:02,429 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 11:16:02,434 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 11:16:02,435 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 11:16:02,436 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 11:16:02,436 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 11:16:02,446 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1340
2025-08-01 11:16:02,446 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2142
2025-08-01 11:16:02,446 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:16:02,446 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0107
2025-08-01 11:16:02,446 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5329
2025-08-01 11:16:02,446 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2960
2025-08-01 11:16:02,446 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4968
2025-08-01 11:16:02,446 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:16:02,446 - INFO - [Trainer 6] 标签样本: [8, 7, 8, 0, 8]
2025-08-01 11:16:02,446 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:16:02,446 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:16:02,446 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:16:02,447 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:16:02,447 - INFO - [Trainer 6] 标签样本: [8, 0, 8, 8, 0]
2025-08-01 11:16:02,448 - INFO - [Trainer 6] 标签样本: [8, 7, 8, 0, 0]
2025-08-01 11:16:02,448 - INFO - [Trainer 3] 标签样本: [7, 6, 2, 2, 0]
2025-08-01 11:16:02,448 - INFO - [Trainer 5] 标签样本: [7, 3, 3, 7, 7]
2025-08-01 11:16:02,448 - INFO - [Trainer 3] 标签样本: [2, 7, 2, 2, 2]
2025-08-01 11:16:02,669 - INFO - [Trainer 6] Batch 0, Loss: 1.2575
2025-08-01 11:16:02,676 - INFO - [Trainer 5] Batch 0, Loss: 0.5840
2025-08-01 11:16:02,684 - INFO - [Trainer 6] Batch 0, Loss: 1.4622
2025-08-01 11:16:02,684 - INFO - [Trainer 6] Batch 0, Loss: 1.2472
2025-08-01 11:16:02,685 - INFO - [Trainer 3] Batch 0, Loss: 0.5929
2025-08-01 11:16:02,691 - INFO - [Trainer 3] Batch 0, Loss: 0.8260
2025-08-01 11:16:03,314 - INFO - 🚀 开始第 2 轮训练（目标：10 轮）
2025-08-01 11:16:03,314 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:16:03,314 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
