import pandas as pd
import matplotlib.pyplot as plt

# 配置部分（根据实际CSV文件列名调整）
CSV_PATH = r'E:\Experiments\orgin-plato-main\plato-main\examples\async\scafl\results\scafl_metrics\scafl_metrics_20250601_161810_669503.csv'
# 使用实际列名
TEST_ACC_COL = 'accuracy'  # 测试准确性列名
TIME_COL = 'training_time' # 训练时间列名
STALENESS_COL = 'client_staleness' # 客户端过时程度列名

def load_and_process_data():
    # 读取实验结果CSV文件（header=None表示没有列名）
    # 添加names参数指定列名，确保数据正确解析
    df = pd.read_csv(CSV_PATH, header=None, names=['round', 'training_time', 'accuracy', 'client_staleness'])
    
    # 确保数据列是数值类型
    df['training_time'] = pd.to_numeric(df['training_time'], errors='coerce')
    df['accuracy'] = pd.to_numeric(df['accuracy'], errors='coerce')
    df['client_staleness'] = pd.to_numeric(df['client_staleness'], errors='coerce')
    
    # 计算测试准确性指标（最后10轮平均+最高）
    last_10_acc = df['accuracy'].tail(10)
    avg_last_10_acc = last_10_acc.mean()
    max_acc = df['accuracy'].max()
    
    # 计算训练时间（示例：达到80%准确性所需时间）
    target_acc = 0.8
    time_to_target = df[df['accuracy'] >= target_acc]['training_time'].iloc[0] if not df[df['accuracy'] >= target_acc].empty else None
    
    # 计算平均过时程度（全周期平均值）
    avg_staleness = df['client_staleness'].mean()
    
    return df, avg_last_10_acc, max_acc, time_to_target, avg_staleness

def plot_results(df, avg_last_10_acc, max_acc, time_to_target, avg_staleness):
    # 设置matplotlib支持中文显示
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    plt.figure(figsize=(12, 8))
    
    # 定义目标准确性阈值
    target_acc = 0.8
    
    # 子图1：测试准确性变化
    plt.subplot(2, 2, 1)
    plt.plot(df[TEST_ACC_COL]*100, label='测试准确性(%)')
    plt.axhline(y=avg_last_10_acc*100, color='r', linestyle='--', label=f'最后10轮平均：{avg_last_10_acc*100:.2f}%')
    plt.axhline(y=max_acc*100, color='g', linestyle='--', label=f'最高准确性：{max_acc*100:.2f}%')
    plt.title('测试准确性变化趋势')
    plt.xlabel('训练轮次')
    plt.ylabel('准确性')
    plt.legend()
    
    # 子图2：训练时间与准确性关系
    plt.subplot(2, 2, 2)
    plt.scatter(df[TEST_ACC_COL]*100, df[TIME_COL], alpha=0.6)
    if time_to_target:
        plt.axvline(x=target_acc, color='r', linestyle='--', label=f'达到{target_acc*100}%时间：{time_to_target:.2f}秒')
    plt.title('训练时间与准确性关系')
    plt.xlabel('测试准确性')
    plt.ylabel('训练时间（秒）')
    plt.legend()
    
    # 子图3：客户端过时程度变化
    plt.subplot(2, 2, 3)
    plt.plot(df[STALENESS_COL], label='单轮过时程度')
    plt.axhline(y=avg_staleness, color='m', linestyle='--', label=f'平均过时程度：{avg_staleness:.2f}')
    plt.title('客户端过时程度变化')
    plt.xlabel('训练轮次')
    plt.ylabel('过时程度')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('实验结果分析图.png')
    plt.show()

if __name__ == '__main__':
    # 加载并处理数据
    data, avg_10_acc, max_accuracy, time_needed, avg_stale = load_and_process_data()
    
    # 打印关键指标
    print(f'最后10轮平均测试准确性：{avg_10_acc:.4f}')
    print(f'最高测试准确性：{max_accuracy:.4f}')
    print(f'达到80%准确性所需时间：{time_needed:.2f}秒' if time_needed else '未达到80%准确性')
    print(f'训练过程平均过时程度：{avg_stale:.2f}')
    
    # 绘制图表
    plot_results(data, avg_10_acc, max_accuracy, time_needed, avg_stale)