import logging
import sys
import os
import fedscafl_server
import fedscafl_algorithm
import torch
import torch.nn as nn
from plato.config import Config

# 设置基本日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fedscafl.log')
    ]
)

logger = logging.getLogger("FedSCAFL.Main")

# 一个简单的模型，用于算法初始化
class SimpleModel(nn.Module):
    def __init__(self):
        super().__init__()
        self.fc = nn.Linear(10, 2)
    
    def forward(self, x):
        return self.fc(x)

class SimpleMockTrainer:
    """简单的模拟训练器，仅用于初始化算法"""
    def __init__(self):
        self.model = SimpleModel()
    
    def __str__(self):
        return "SimpleMockTrainer"

def main():
    """FedSCAFL主程序入口"""
    logger.info("启动FedSCAFL联邦学习框架...")
    
    # 确保配置文件的功能被正确使用
    if hasattr(Config().server, 'tau_max'):
        logger.info(f"配置的tau_max为: {Config().server.tau_max}")
    else:
        logger.warning("配置中未找到tau_max，将使用默认值10")
        
    if hasattr(Config().server, 'V'):
        logger.info(f"配置的V参数为: {Config().server.V}")
    else:
        logger.warning("配置中未找到V参数，将使用默认值10")
    
    # 初始化算法
    try:
        logger.info("初始化算法...")
        # 创建一个简单的mock trainer，让算法能够正确初始化
        mock_trainer = SimpleMockTrainer()
        algorithm_cls = fedscafl_algorithm.Algorithm
        algorithm_instance = fedscafl_algorithm.Algorithm(trainer=mock_trainer)
        
        # 创建服务器实例
        logger.info("初始化服务器...")
        server = fedscafl_server.Server(algorithm=algorithm_cls)
        
        # 启动服务器
        logger.info("服务器初始化完成，开始运行...")
        
        # 打补丁：确保服务器和算法之间能共享staleness信息
        # 注意：真实运行中，服务器会创建自己的算法实例，这里只是为了确保有初始化的算法实例可用
        if hasattr(server, 'algorithm'):
            if server.algorithm is None:
                logger.warning("服务器的algorithm属性为None，尝试设置...")
                server.algorithm = algorithm_instance
                logger.info("已将算法实例设置到服务器")
            
            if not hasattr(server.algorithm, 'client_staleness'):
                server.algorithm.client_staleness = {}
                logger.info("已为算法实例配置client_staleness字典")
        else:
            logger.warning("服务器没有algorithm属性")
        
        # 启动服务
        server.run()
    except Exception as e:
        logger.error("运行过程中发生错误: %s", str(e), exc_info=True)
        raise

if __name__ == "__main__":
    main()