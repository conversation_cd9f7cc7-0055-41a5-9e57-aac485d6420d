"""
SCAFL服务器实现

这个文件实现了SCAFL的服务器端逻辑：
1. 基于Lyapunov优化的客户端选择
2. 异步聚合管理
3. 陈旧度和虚拟队列的维护
"""

import logging
import asyncio
import random
from plato.servers import fedavg
from plato.config import Config
from scafl_algorithm import Algorithm


class Server(fedavg.Server):
    """SCAFL服务器类，继承自FedAvg服务器"""
    
    def __init__(self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None):
        """初始化SCAFL服务器"""
        super().__init__(model, datasource, algorithm, trainer, callbacks)
        
        # 从配置读取SCAFL参数
        config = Config()
        self.tau_max = getattr(config.server, 'tau_max', 10)
        self.V = getattr(config.server, 'V', 10)
        self.max_aggregation_clients = getattr(config.server, 'max_aggregation_clients', 8)
        
        # SCAFL状态管理
        self.client_training_times = {}  # 客户端训练时间
        self.waiting_clients = {}        # 等待聚合的客户端
        self.current_round = 0
        
        logging.info(f"[SCAFL Server] 初始化完成")
        logging.info(f"[SCAFL Server] 参数: tau_max={self.tau_max}, V={self.V}, max_clients={self.max_aggregation_clients}")
    
    def configure(self):
        """配置服务器，使用SCAFL算法"""
        super().configure()
        
        # 确保使用SCAFL算法
        if not isinstance(self.algorithm, Algorithm):
            logging.info("[SCAFL Server] 创建SCAFL算法实例")
            self.algorithm = Algorithm(self.trainer)
    
    def estimate_client_training_time(self, client_id):
        """估计客户端训练时间
        
        在实际实现中，这可以基于：
        1. 历史训练时间
        2. 客户端硬件能力
        3. 网络状况
        
        现在我们使用简单的模拟
        """
        if client_id not in self.client_training_times:
            # 模拟不同客户端的训练时间（5-20秒）
            base_time = random.uniform(5.0, 20.0)
            self.client_training_times[client_id] = base_time
            
        return self.client_training_times[client_id]
    
    def compute_objective_function(self, candidate_clients):
        """计算SCAFL目标函数值
        
        根据论文公式(17): V*Dt + Σ Qk(t)*((τk(t)+1)(1-βt_k) - τmax)
        
        Args:
            candidate_clients: 候选聚合客户端列表
            
        Returns:
            float: 目标函数值
        """
        # 计算训练时间 Dt（所有聚合客户端中的最大训练时间）
        if candidate_clients:
            training_time = max(self.estimate_client_training_time(cid) for cid in candidate_clients)
        else:
            training_time = 0
        
        # 计算队列相关项
        queue_term = 0.0
        all_clients = list(range(1, self.total_clients + 1))  # 假设客户端ID从1开始
        
        for client_id in all_clients:
            queue_length = self.algorithm.virtual_queues.get(client_id, 0.0)
            staleness = self.algorithm.client_staleness.get(client_id, 0)
            
            if client_id in candidate_clients:
                # 参与聚合: βt_k = 1, (1-βt_k) = 0
                queue_contribution = queue_length * (0 - self.tau_max)
            else:
                # 未参与聚合: βt_k = 0, (1-βt_k) = 1
                queue_contribution = queue_length * ((staleness + 1) - self.tau_max)
            
            queue_term += queue_contribution
        
        objective_value = self.V * training_time + queue_term
        return objective_value
    
    def sc_afl_client_selection(self, available_clients):
        """SCAFL客户端选择算法
        
        实现论文Algorithm 1的逻辑
        
        Args:
            available_clients: 可用客户端列表
            
        Returns:
            list: 选择的客户端列表
        """
        if not available_clients:
            return []
        
        # Step 1: 按训练时间排序（升序）
        sorted_clients = sorted(available_clients, 
                              key=lambda cid: self.estimate_client_training_time(cid))
        
        # Step 2: 逐个添加客户端，寻找最优聚合集合
        best_clients = []
        min_objective = float('inf')
        
        max_clients = min(len(sorted_clients), self.max_aggregation_clients)
        
        for i in range(1, max_clients + 1):
            candidate_clients = sorted_clients[:i]
            objective_value = self.compute_objective_function(candidate_clients)
            
            logging.debug(f"[SCAFL] 候选集合{candidate_clients}: 目标函数值={objective_value:.4f}")
            
            if objective_value < min_objective:
                min_objective = objective_value
                best_clients = candidate_clients.copy()
        
        logging.info(f"[SCAFL] 选择客户端: {best_clients}, 目标函数值: {min_objective:.4f}")
        return best_clients
    
    async def select_clients(self):
        """选择参与本轮训练的客户端"""
        # 获取所有可用客户端
        available_clients = list(range(1, self.total_clients + 1))
        
        # 过滤掉陈旧度过高的客户端
        valid_clients = []
        for client_id in available_clients:
            staleness = self.algorithm.client_staleness.get(client_id, 0)
            if staleness <= self.tau_max:
                valid_clients.append(client_id)
            else:
                logging.warning(f"[SCAFL] 客户端{client_id}陈旧度过高({staleness} > {self.tau_max})，跳过")
        
        if not valid_clients:
            logging.warning("[SCAFL] 没有有效客户端，使用随机选择")
            selected = random.sample(available_clients, 
                                   min(self.clients_per_round, len(available_clients)))
        else:
            # 使用SCAFL算法选择客户端
            selected = self.sc_afl_client_selection(valid_clients)
            
            # 如果选择的客户端太少，补充一些随机客户端
            if len(selected) < self.clients_per_round:
                remaining = [c for c in valid_clients if c not in selected]
                additional = random.sample(remaining, 
                                         min(self.clients_per_round - len(selected), len(remaining)))
                selected.extend(additional)
        
        logging.info(f"[SCAFL] 第{self.current_round}轮选择了{len(selected)}个客户端: {selected}")
        return selected
    
    async def aggregate_weights(self, updates):
        """聚合客户端权重更新"""
        if not updates:
            logging.warning("[SCAFL Server] 没有收到客户端更新")
            return
        
        # 提取权重和客户端信息
        baseline_weights = self.algorithm.extract_weights()
        weights_received = []
        participating_clients = []
        
        for update in updates:
            weights_received.append(update.payload)
            participating_clients.append(update.client_id)
        
        # 使用SCAFL算法聚合权重
        aggregated_weights = await self.algorithm.aggregate_weights(
            baseline_weights, weights_received
        )
        
        # 更新全局模型
        self.algorithm.load_weights(aggregated_weights)
        
        # 更新所有客户端的陈旧度和虚拟队列
        all_clients = list(range(1, self.total_clients + 1))
        for client_id in all_clients:
            participated = client_id in participating_clients
            self.algorithm.update_client_staleness(client_id, participated)
            self.algorithm.update_virtual_queue(client_id, participated)
        
        self.current_round += 1
        logging.info(f"[SCAFL Server] 第{self.current_round}轮聚合完成")
        
        return aggregated_weights
