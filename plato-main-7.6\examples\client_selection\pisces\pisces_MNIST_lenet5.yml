clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 200

    # The number of clients selected in each round
    per_round: 20

    # Should the clients compute test accuracy locally?
    do_test:
        false

        # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto # zipf is used.
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    #max_sleep_time: 10

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: true

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 10

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8000
    do_test: true
    random_seed: 1
    #step_window: 2

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server? Useful if max_concurrency is specified
    simulate_wall_time: true

    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 10

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 10
    #penalty: 2
    exploration_factor: 0.9
    staleness_factor: 0.5
    exploration_decaying_factor: 0.98
    min_explore_factor: 0.3
    staleness_penalty_factor: 0.5
    checkpoint_path: results/test/checkpoint
    model_path: results/test/model

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 500

    # IID or non-IID?
    sampler: noniid

    #testset_size: 1000

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 5

    # The maximum number of clients running concurrently
    max_concurrency: 2

    # The target accuracy
    target_accuracy: 1

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD

    # The machine learning model
    model_name: lenet5

algorithm:
    # Aggregation algorithm
    type: fedavg

results:
    # Write the following parameter(s) into a CSV
    types: round, accuracy, elapsed_time, comm_time, round_time

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0
