#!/usr/bin/env python3
"""
联邦学习算法对比结果分析脚本
分析和可视化 ReFedScaFL、FedAC 和 SC_AFL 的性能对比

使用方法:
python analyze_comparison_results.py
"""

import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import glob
import argparse

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def find_latest_results():
    """查找最新的实验结果文件"""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    results = {}
    
    # ReFedScaFL 结果
    refedscafl_pattern = os.path.join(base_dir, "results/refedscafl/comparison_cifar10_alpha01/*.csv")
    refedscafl_files = glob.glob(refedscafl_pattern)
    if refedscafl_files:
        results['ReFedScaFL'] = max(refedscafl_files, key=os.path.getmtime)
    
    # FedAC 结果
    fedac_pattern = os.path.join(os.path.dirname(base_dir), "fedac/results/cifar10/alpha01/*.csv")
    fedac_files = glob.glob(fedac_pattern)
    if fedac_files:
        results['FedAC'] = max(fedac_files, key=os.path.getmtime)
    
    # SC_AFL 结果
    scafl_pattern = os.path.join(os.path.dirname(base_dir), "SC_AFL/results/*.csv")
    scafl_files = glob.glob(scafl_pattern)
    if scafl_files:
        results['SC_AFL'] = max(scafl_files, key=os.path.getmtime)
    
    return results

def load_and_clean_data(file_path, algorithm_name):
    """加载和清理CSV数据"""
    try:
        df = pd.read_csv(file_path)
        
        # 移除空行
        df = df.dropna(how='all')
        
        # 确保必要的列存在
        required_cols = ['round', 'elapsed_time', 'accuracy']
        for col in required_cols:
            if col not in df.columns:
                print(f"警告: {algorithm_name} 缺少列 '{col}'")
                return None
        
        # 数据类型转换
        df['round'] = pd.to_numeric(df['round'], errors='coerce')
        df['elapsed_time'] = pd.to_numeric(df['elapsed_time'], errors='coerce')
        df['accuracy'] = pd.to_numeric(df['accuracy'], errors='coerce')
        
        # 移除无效数据
        df = df.dropna(subset=['round', 'elapsed_time', 'accuracy'])
        
        # 添加算法标识
        df['algorithm'] = algorithm_name
        
        return df
        
    except Exception as e:
        print(f"加载 {algorithm_name} 数据时出错: {e}")
        return None

def plot_accuracy_comparison(data_dict, save_path=None):
    """绘制准确率对比图"""
    plt.figure(figsize=(12, 8))
    
    colors = {'ReFedScaFL': '#FF6B6B', 'FedAC': '#4ECDC4', 'SC_AFL': '#45B7D1'}
    markers = {'ReFedScaFL': 'o', 'FedAC': 's', 'SC_AFL': '^'}
    
    for alg_name, df in data_dict.items():
        if df is not None and not df.empty:
            plt.plot(df['round'], df['accuracy'] * 100, 
                    label=alg_name, 
                    color=colors.get(alg_name, 'gray'),
                    marker=markers.get(alg_name, 'o'),
                    markersize=6,
                    linewidth=2,
                    markevery=max(1, len(df) // 20))  # 每20个点显示一个标记
    
    plt.xlabel('训练轮次', fontsize=14)
    plt.ylabel('全局准确率 (%)', fontsize=14)
    plt.title('联邦学习算法准确率对比', fontsize=16, fontweight='bold')
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"准确率对比图已保存到: {save_path}")
    
    plt.show()

def plot_convergence_time(data_dict, save_path=None):
    """绘制收敛时间对比图"""
    plt.figure(figsize=(12, 8))
    
    colors = {'ReFedScaFL': '#FF6B6B', 'FedAC': '#4ECDC4', 'SC_AFL': '#45B7D1'}
    
    for alg_name, df in data_dict.items():
        if df is not None and not df.empty:
            # 计算累积时间
            df['cumulative_time'] = df['elapsed_time'].cumsum()
            plt.plot(df['cumulative_time'] / 60, df['accuracy'] * 100, 
                    label=alg_name, 
                    color=colors.get(alg_name, 'gray'),
                    linewidth=2)
    
    plt.xlabel('累积训练时间 (分钟)', fontsize=14)
    plt.ylabel('全局准确率 (%)', fontsize=14)
    plt.title('联邦学习算法收敛时间对比', fontsize=16, fontweight='bold')
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"收敛时间对比图已保存到: {save_path}")
    
    plt.show()

def generate_performance_report(data_dict, save_path=None):
    """生成性能对比报告"""
    report = []
    report.append("=" * 80)
    report.append("联邦学习算法性能对比报告")
    report.append("=" * 80)
    report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    for alg_name, df in data_dict.items():
        if df is not None and not df.empty:
            report.append(f"算法: {alg_name}")
            report.append("-" * 40)
            
            # 基本统计
            max_accuracy = df['accuracy'].max() * 100
            final_accuracy = df['accuracy'].iloc[-1] * 100
            total_rounds = len(df)
            total_time = df['elapsed_time'].sum() / 60  # 转换为分钟
            avg_time_per_round = df['elapsed_time'].mean()
            
            report.append(f"最高准确率: {max_accuracy:.2f}%")
            report.append(f"最终准确率: {final_accuracy:.2f}%")
            report.append(f"训练轮次: {total_rounds}")
            report.append(f"总训练时间: {total_time:.2f} 分钟")
            report.append(f"平均每轮时间: {avg_time_per_round:.2f} 秒")
            
            # 收敛分析
            target_accuracies = [0.5, 0.6, 0.7, 0.8]
            for target in target_accuracies:
                convergence_round = df[df['accuracy'] >= target]['round'].min()
                if not pd.isna(convergence_round):
                    convergence_time = df[df['round'] <= convergence_round]['elapsed_time'].sum() / 60
                    report.append(f"达到 {target*100:.0f}% 准确率: 第{convergence_round:.0f}轮 ({convergence_time:.2f}分钟)")
            
            # Staleness 分析 (如果有的话)
            if 'avg_staleness' in df.columns:
                avg_staleness = df['avg_staleness'].mean()
                max_staleness = df['max_staleness'].max()
                report.append(f"平均陈旧度: {avg_staleness:.2f}")
                report.append(f"最大陈旧度: {max_staleness:.2f}")
            
            report.append("")
    
    # 算法对比
    if len(data_dict) > 1:
        report.append("算法对比总结")
        report.append("-" * 40)
        
        # 准确率对比
        accuracies = {}
        times = {}
        for alg_name, df in data_dict.items():
            if df is not None and not df.empty:
                accuracies[alg_name] = df['accuracy'].max() * 100
                times[alg_name] = df['elapsed_time'].sum() / 60
        
        if accuracies:
            best_accuracy_alg = max(accuracies, key=accuracies.get)
            fastest_alg = min(times, key=times.get)
            
            report.append(f"最高准确率算法: {best_accuracy_alg} ({accuracies[best_accuracy_alg]:.2f}%)")
            report.append(f"最快收敛算法: {fastest_alg} ({times[fastest_alg]:.2f}分钟)")
            
            # 效率比较
            for alg_name in accuracies:
                efficiency = accuracies[alg_name] / times[alg_name]  # 准确率/时间
                report.append(f"{alg_name} 效率指标: {efficiency:.2f} (%/分钟)")
    
    report_text = "\n".join(report)
    print(report_text)
    
    if save_path:
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(report_text)
        print(f"\n性能报告已保存到: {save_path}")
    
    return report_text

def main():
    parser = argparse.ArgumentParser(description='分析联邦学习算法对比结果')
    parser.add_argument('--output-dir', default='comparison_results', 
                       help='输出目录')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = args.output_dir
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找结果文件
    print("查找最新的实验结果...")
    result_files = find_latest_results()
    
    if not result_files:
        print("未找到任何实验结果文件！")
        return
    
    print("找到以下结果文件:")
    for alg, file_path in result_files.items():
        print(f"  {alg}: {file_path}")
    
    # 加载数据
    print("\n加载和处理数据...")
    data_dict = {}
    for alg_name, file_path in result_files.items():
        df = load_and_clean_data(file_path, alg_name)
        if df is not None:
            data_dict[alg_name] = df
            print(f"  {alg_name}: {len(df)} 条记录")
        else:
            print(f"  {alg_name}: 数据加载失败")
    
    if not data_dict:
        print("没有成功加载任何数据！")
        return
    
    # 生成图表和报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print("\n生成对比图表...")
    plot_accuracy_comparison(data_dict, 
                           os.path.join(output_dir, f'accuracy_comparison_{timestamp}.png'))
    
    plot_convergence_time(data_dict, 
                         os.path.join(output_dir, f'convergence_time_{timestamp}.png'))
    
    print("\n生成性能报告...")
    generate_performance_report(data_dict, 
                              os.path.join(output_dir, f'performance_report_{timestamp}.txt'))
    
    print(f"\n所有结果已保存到目录: {output_dir}")

if __name__ == '__main__':
    main()
