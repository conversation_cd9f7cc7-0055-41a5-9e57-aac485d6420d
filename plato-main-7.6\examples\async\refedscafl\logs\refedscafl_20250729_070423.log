[INFO][07:04:23]: 日志系统已初始化
[INFO][07:04:23]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_070423.log
[INFO][07:04:23]: 日志级别: INFO
[WARNING][07:04:23]: 无法获取系统信息: No module named 'psutil'
[INFO][07:04:23]: 🚀 ReFedScaFL 训练开始
[INFO][07:04:23]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][07:04:23]: 开始时间: 2025-07-29 07:04:23
[INFO][07:04:23]: [Client None] 基础初始化完成
[INFO][07:04:23]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][07:04:23]: 创建并缓存共享模型
[INFO][07:04:23]: [93m[1m[35608] Logging runtime results to: ./results/refedscafl/comparison_cifar10_alpha01/35608.csv.[0m
[INFO][07:04:23]: [Server #35608] Started training on 100 clients with 20 per round.
[INFO][07:04:23]: 服务器参数配置完成：
[INFO][07:04:23]: - 客户端数量: total=100, per_round=20
[INFO][07:04:23]: - 权重参数: success=0.8, distill=0.2
[INFO][07:04:23]: - SCAFL参数: V=1.0, tau_max=5
[INFO][07:04:23]: 从共享资源模型提取并缓存全局权重
[INFO][07:04:23]: [Server #35608] Configuring the server...
[INFO][07:04:23]: Training: 400 rounds or accuracy above 100.0%

[INFO][07:04:23]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][07:04:23]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][07:04:23]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][07:04:23]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][07:04:23]: Algorithm: fedavg
[INFO][07:04:23]: Data source: CIFAR10
[INFO][07:04:24]: Starting client #1's process.
[INFO][07:04:24]: Starting client #2's process.
[INFO][07:04:24]: Starting client #3's process.
[INFO][07:04:24]: Starting client #4's process.
[INFO][07:04:24]: Starting client #5's process.
[INFO][07:04:24]: Starting client #6's process.
[INFO][07:04:24]: Starting client #7's process.
[INFO][07:04:24]: Starting client #8's process.
[INFO][07:04:24]: Starting client #9's process.
[INFO][07:04:24]: Starting client #10's process.
[INFO][07:04:24]: Setting the random seed for selecting clients: 1
[INFO][07:04:24]: Starting a server at address 127.0.0.1 and port 8092.
[INFO][07:04:38]: [Server #35608] A new client just connected.
[INFO][07:04:38]: [Server #35608] New client with id #9 arrived.
[INFO][07:04:38]: [Server #35608] Client process #25756 registered.
[INFO][07:04:38]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][07:04:39]: [Server #35608] A new client just connected.
[INFO][07:04:39]: [Server #35608] New client with id #10 arrived.
[INFO][07:04:39]: [Server #35608] Client process #32760 registered.
[INFO][07:04:39]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][07:04:40]: [Server #35608] A new client just connected.
[INFO][07:04:40]: [Server #35608] New client with id #2 arrived.
[INFO][07:04:40]: [Server #35608] Client process #12660 registered.
[INFO][07:04:40]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][07:04:40]: [Server #35608] A new client just connected.
[INFO][07:04:40]: [Server #35608] A new client just connected.
[INFO][07:04:40]: [Server #35608] New client with id #5 arrived.
[INFO][07:04:40]: [Server #35608] Client process #22844 registered.
[INFO][07:04:40]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][07:04:40]: [Server #35608] New client with id #8 arrived.
[INFO][07:04:40]: [Server #35608] Client process #35828 registered.
[INFO][07:04:40]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][07:04:40]: [Server #35608] A new client just connected.
[INFO][07:04:40]: [Server #35608] New client with id #4 arrived.
[INFO][07:04:40]: [Server #35608] Client process #13156 registered.
[INFO][07:04:40]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][07:04:40]: [Server #35608] A new client just connected.
[INFO][07:04:40]: [Server #35608] A new client just connected.
[INFO][07:04:40]: [Server #35608] New client with id #6 arrived.
[INFO][07:04:40]: [Server #35608] Client process #29900 registered.
[INFO][07:04:40]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][07:04:40]: [Server #35608] New client with id #7 arrived.
[INFO][07:04:40]: [Server #35608] Client process #27912 registered.
[INFO][07:04:40]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][07:04:40]: [Server #35608] A new client just connected.
[INFO][07:04:40]: [Server #35608] New client with id #3 arrived.
[INFO][07:04:40]: [Server #35608] Client process #26620 registered.
[INFO][07:04:40]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][07:04:40]: [Server #35608] A new client just connected.
[INFO][07:04:40]: [Server #35608] New client with id #1 arrived.
[INFO][07:04:40]: [Server #35608] Client process #31184 registered.
[INFO][07:04:40]: [Server #35608] Starting training.
[INFO][07:04:40]: [93m[1m
[Server #35608] Starting round 1/400.[0m
[INFO][07:04:40]: [Server #35608] Selected clients: [18, 73, 98, 9, 33, 16, 64, 58, 61, 84, 49, 27, 13, 63, 4, 50, 56, 78, 99, 1]
[INFO][07:04:40]: [Server #35608] Selecting client #18 for training.
[INFO][07:04:40]: [Server #35608] Sending the current model to client #18 (simulated).
[INFO][07:04:40]: [Server #35608] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][07:04:40]: [Server #35608] Selecting client #73 for training.
[INFO][07:04:40]: [Server #35608] Sending the current model to client #73 (simulated).
[INFO][07:04:40]: [Server #35608] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][07:04:40]: [Server #35608] Selecting client #98 for training.
[INFO][07:04:40]: [Server #35608] Sending the current model to client #98 (simulated).
[INFO][07:04:40]: [Server #35608] Sending 18.75 MB of payload data to client #98 (simulated).
[INFO][07:04:40]: [Server #35608] Selecting client #9 for training.
[INFO][07:04:40]: [Server #35608] Sending the current model to client #9 (simulated).
[INFO][07:04:40]: [Server #35608] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][07:04:40]: [Server #35608] Selecting client #33 for training.
[INFO][07:04:40]: [Server #35608] Sending the current model to client #33 (simulated).
[INFO][07:04:40]: [Server #35608] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][07:04:40]: [Server #35608] Selecting client #16 for training.
[INFO][07:04:40]: [Server #35608] Sending the current model to client #16 (simulated).
[INFO][07:04:40]: [Server #35608] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][07:04:40]: [Server #35608] Selecting client #64 for training.
[INFO][07:04:40]: [Server #35608] Sending the current model to client #64 (simulated).
[INFO][07:04:40]: [Server #35608] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][07:04:40]: [Server #35608] Selecting client #58 for training.
[INFO][07:04:40]: [Server #35608] Sending the current model to client #58 (simulated).
[INFO][07:04:40]: [Server #35608] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][07:04:40]: [Server #35608] Selecting client #61 for training.
[INFO][07:04:40]: [Server #35608] Sending the current model to client #61 (simulated).
[INFO][07:04:40]: [Server #35608] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][07:04:40]: [Server #35608] Selecting client #84 for training.
[INFO][07:04:40]: [Server #35608] Sending the current model to client #84 (simulated).
[INFO][07:04:40]: [Server #35608] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][07:04:40]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][07:07:26]: [Server #35608] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][07:07:29]: [Server #35608] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][07:07:31]: [Server #35608] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][07:07:33]: [Server #35608] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][07:07:33]: [Server #35608] Received 18.75 MB of payload data from client #98 (simulated).
[INFO][07:07:33]: [Server #35608] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][07:07:33]: [Server #35608] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][07:07:33]: [Server #35608] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][07:07:33]: [Server #35608] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][07:07:33]: [Server #35608] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][07:07:33]: [Server #35608] Selecting client #49 for training.
[INFO][07:07:33]: [Server #35608] Sending the current model to client #49 (simulated).
[INFO][07:07:33]: [Server #35608] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][07:07:33]: [Server #35608] Selecting client #27 for training.
[INFO][07:07:33]: [Server #35608] Sending the current model to client #27 (simulated).
[INFO][07:07:33]: [Server #35608] Sending 18.75 MB of payload data to client #27 (simulated).
[INFO][07:07:33]: [Server #35608] Selecting client #13 for training.
[INFO][07:07:33]: [Server #35608] Sending the current model to client #13 (simulated).
[INFO][07:07:33]: [Server #35608] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][07:07:33]: [Server #35608] Selecting client #63 for training.
[INFO][07:07:33]: [Server #35608] Sending the current model to client #63 (simulated).
[INFO][07:07:33]: [Server #35608] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][07:07:33]: [Server #35608] Selecting client #4 for training.
[INFO][07:07:33]: [Server #35608] Sending the current model to client #4 (simulated).
[INFO][07:07:34]: [Server #35608] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][07:07:34]: [Server #35608] Selecting client #50 for training.
[INFO][07:07:34]: [Server #35608] Sending the current model to client #50 (simulated).
[INFO][07:07:34]: [Server #35608] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][07:07:34]: [Server #35608] Selecting client #56 for training.
[INFO][07:07:34]: [Server #35608] Sending the current model to client #56 (simulated).
[INFO][07:07:34]: [Server #35608] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][07:07:34]: [Server #35608] Selecting client #78 for training.
[INFO][07:07:34]: [Server #35608] Sending the current model to client #78 (simulated).
[INFO][07:07:35]: [Server #35608] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][07:07:35]: [Server #35608] Selecting client #99 for training.
[INFO][07:07:35]: [Server #35608] Sending the current model to client #99 (simulated).
[INFO][07:07:35]: [Server #35608] Sending 18.75 MB of payload data to client #99 (simulated).
[INFO][07:07:35]: [Server #35608] Selecting client #1 for training.
[INFO][07:07:35]: [Server #35608] Sending the current model to client #1 (simulated).
[INFO][07:07:35]: [Server #35608] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][07:10:03]: [Server #35608] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][07:10:07]: [Server #35608] Received 18.75 MB of payload data from client #27 (simulated).
[INFO][07:10:07]: [Server #35608] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][07:10:07]: [Server #35608] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][07:10:07]: [Server #35608] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][07:10:08]: [Server #35608] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][07:10:08]: [Server #35608] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][07:10:08]: [Server #35608] Received 18.75 MB of payload data from client #99 (simulated).
[INFO][07:10:08]: [Server #35608] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][07:10:08]: [Server #35608] Received 18.75 MB of payload data from client #78 (simulated).
[INFO][07:10:08]: [Server #35608] Adding client #49 to the list of clients for aggregation.
[INFO][07:10:08]: [Server #35608] Adding client #27 to the list of clients for aggregation.
[INFO][07:10:08]: [Server #35608] Adding client #13 to the list of clients for aggregation.
[INFO][07:10:08]: [Server #35608] Adding client #63 to the list of clients for aggregation.
[INFO][07:10:08]: [Server #35608] Adding client #50 to the list of clients for aggregation.
[INFO][07:10:08]: [Server #35608] Adding client #4 to the list of clients for aggregation.
[INFO][07:10:08]: [Server #35608] Adding client #99 to the list of clients for aggregation.
[INFO][07:10:08]: [Server #35608] Adding client #56 to the list of clients for aggregation.
[INFO][07:10:08]: [Server #35608] Adding client #1 to the list of clients for aggregation.
[INFO][07:10:08]: [Server #35608] Adding client #78 to the list of clients for aggregation.
[INFO][07:10:08]: [Server #35608] Aggregating 10 clients in total.
[INFO][07:10:08]: [Server #35608] Updated weights have been received.
[INFO][07:10:08]: [Server #35608] Aggregating model weight deltas.
[INFO][07:10:08]: [Server #35608] Finished aggregating updated weights.
[INFO][07:10:08]: [Server #35608] Started model testing.
[INFO][07:10:18]: [Trainer.test] 测试完成 - 准确率: 14.41% (1441/10000)
[INFO][07:10:18]: [93m[1m[Server #35608] Global model accuracy: 14.41%
[0m
[INFO][07:10:18]: get_logged_items 被调用
[INFO][07:10:18]: 从updates获取参与客户端: [49, 27, 13, 63, 50, 4, 99, 56, 1, 78]
[INFO][07:10:18]: 客户端 49 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][07:10:18]: 客户端 27 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][07:10:18]: 客户端 13 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][07:10:18]: 客户端 63 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][07:10:18]: 客户端 50 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][07:10:18]: 客户端 4 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][07:10:18]: 客户端 99 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][07:10:18]: 客户端 56 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][07:10:18]: 客户端 1 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][07:10:18]: 客户端 78 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][07:10:18]: 陈旧度统计 - 参与客户端: [49, 27, 13, 63, 50, 4, 99, 56, 1, 78], 陈旧度: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
[INFO][07:10:18]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][07:10:18]: 最终logged_items: {'round': 1, 'accuracy': 0.1441, 'accuracy_std': 0, 'elapsed_time': 50.09989643096924, 'processing_time': 0.0029910999874118716, 'comm_time': 0, 'round_time': 50.09989621627028, 'comm_overhead': 749.9883651733398, 'global_accuracy': 0.1441, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][07:10:18]: [Server #35608] All client reports have been processed.
[INFO][07:10:18]: [Server #35608] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][07:10:18]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][07:10:18]: [93m[1m
[Server #35608] Starting round 2/400.[0m
[INFO][07:10:18]: [Server #35608] Selected clients: [100, 65, 39, 34, 85, 15, 45, 4, 3, 93]
[INFO][07:10:18]: [Server #35608] Selecting client #100 for training.
[INFO][07:10:18]: [Server #35608] Sending the current model to client #100 (simulated).
[INFO][07:10:18]: [Server #35608] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][07:10:18]: [Server #35608] Selecting client #65 for training.
[INFO][07:10:18]: [Server #35608] Sending the current model to client #65 (simulated).
[INFO][07:10:18]: [Server #35608] Sending 18.75 MB of payload data to client #65 (simulated).
[INFO][07:10:18]: [Server #35608] Selecting client #39 for training.
[INFO][07:10:18]: [Server #35608] Sending the current model to client #39 (simulated).
[INFO][07:10:18]: [Server #35608] Sending 18.75 MB of payload data to client #39 (simulated).
[INFO][07:10:18]: [Server #35608] Selecting client #34 for training.
[INFO][07:10:18]: [Server #35608] Sending the current model to client #34 (simulated).
[INFO][07:10:18]: [Server #35608] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][07:10:18]: [Server #35608] Selecting client #85 for training.
[INFO][07:10:18]: [Server #35608] Sending the current model to client #85 (simulated).
[INFO][07:10:19]: [Server #35608] Sending 18.75 MB of payload data to client #85 (simulated).
[INFO][07:10:19]: [Server #35608] Selecting client #15 for training.
[INFO][07:10:19]: [Server #35608] Sending the current model to client #15 (simulated).
[INFO][07:10:19]: [Server #35608] Sending 18.75 MB of payload data to client #15 (simulated).
[INFO][07:10:19]: [Server #35608] Selecting client #45 for training.
[INFO][07:10:19]: [Server #35608] Sending the current model to client #45 (simulated).
[INFO][07:10:19]: [Server #35608] Sending 18.75 MB of payload data to client #45 (simulated).
[INFO][07:10:19]: [Server #35608] Selecting client #4 for training.
[INFO][07:10:19]: [Server #35608] Sending the current model to client #4 (simulated).
[INFO][07:10:19]: [Server #35608] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][07:10:19]: [Server #35608] Selecting client #3 for training.
[INFO][07:10:19]: [Server #35608] Sending the current model to client #3 (simulated).
[INFO][07:10:20]: [Server #35608] Sending 18.75 MB of payload data to client #3 (simulated).
[INFO][07:10:20]: [Server #35608] Selecting client #93 for training.
[INFO][07:10:20]: [Server #35608] Sending the current model to client #93 (simulated).
[INFO][07:10:20]: [Server #35608] Sending 18.75 MB of payload data to client #93 (simulated).
[INFO][07:12:44]: [Server #35608] Received 18.75 MB of payload data from client #100 (simulated).
[INFO][07:12:46]: [Server #35608] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][07:12:46]: [Server #35608] Received 18.75 MB of payload data from client #39 (simulated).
[INFO][07:12:47]: [Server #35608] Received 18.75 MB of payload data from client #65 (simulated).
[INFO][07:12:49]: [Server #35608] Received 18.75 MB of payload data from client #85 (simulated).
[INFO][07:12:51]: [Server #35608] Received 18.75 MB of payload data from client #45 (simulated).
[INFO][07:12:52]: [Server #35608] Received 18.75 MB of payload data from client #15 (simulated).
[INFO][07:12:52]: [Server #35608] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][07:12:52]: [Server #35608] Received 18.75 MB of payload data from client #93 (simulated).
[INFO][07:12:52]: [Server #35608] Received 18.75 MB of payload data from client #3 (simulated).
[INFO][07:12:52]: [Server #35608] Adding client #18 to the list of clients for aggregation.
[INFO][07:12:52]: [Server #35608] Adding client #73 to the list of clients for aggregation.
[INFO][07:12:52]: [Server #35608] Adding client #9 to the list of clients for aggregation.
[INFO][07:12:52]: [Server #35608] Adding client #33 to the list of clients for aggregation.
[INFO][07:12:52]: [Server #35608] Adding client #98 to the list of clients for aggregation.
[INFO][07:12:52]: [Server #35608] Adding client #58 to the list of clients for aggregation.
[INFO][07:12:52]: [Server #35608] Adding client #64 to the list of clients for aggregation.
[INFO][07:12:52]: [Server #35608] Adding client #16 to the list of clients for aggregation.
[INFO][07:12:52]: [Server #35608] Adding client #84 to the list of clients for aggregation.
[INFO][07:12:52]: [Server #35608] Adding client #61 to the list of clients for aggregation.
[INFO][07:12:52]: [Server #35608] Aggregating 10 clients in total.
[INFO][07:12:52]: [Server #35608] Updated weights have been received.
[INFO][07:12:52]: [Server #35608] Aggregating model weight deltas.
[INFO][07:12:52]: [Server #35608] Finished aggregating updated weights.
[INFO][07:12:52]: [Server #35608] Started model testing.
[INFO][07:13:02]: [Trainer.test] 测试完成 - 准确率: 11.56% (1156/10000)
[INFO][07:13:02]: [93m[1m[Server #35608] Global model accuracy: 11.56%
[0m
[INFO][07:13:02]: get_logged_items 被调用
[INFO][07:13:02]: 从updates获取参与客户端: [18, 73, 9, 33, 98, 58, 64, 16, 84, 61]
[INFO][07:13:02]: 客户端 18 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][07:13:02]: 客户端 73 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][07:13:02]: 客户端 9 陈旧度: 2 (当前轮次:2, 上次参与:0)
[INFO][07:13:02]: 客户端 33 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][07:13:02]: 客户端 98 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][07:13:02]: 客户端 58 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][07:13:02]: 客户端 64 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][07:13:02]: 客户端 16 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][07:13:02]: 客户端 84 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][07:13:02]: 客户端 61 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][07:13:02]: 陈旧度统计 - 参与客户端: [18, 73, 9, 33, 98, 58, 64, 16, 84, 61], 陈旧度: [1, 1, 2, 1, 1, 1, 1, 1, 1, 1]
[INFO][07:13:02]: 平均陈旧度: 1.1, 最大: 2, 最小: 1
[INFO][07:13:02]: 最终logged_items: {'round': 2, 'accuracy': 0.1156, 'accuracy_std': 0, 'elapsed_time': 63.040645360946655, 'processing_time': 0.0038707000203430653, 'comm_time': 0, 'round_time': 63.04064515425125, 'comm_overhead': 1124.9825477600098, 'global_accuracy': 0.1156, 'avg_staleness': 1.1, 'max_staleness': 2, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][07:13:02]: [Server #35608] All client reports have been processed.
[INFO][07:13:02]: [Server #35608] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][07:13:02]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][07:13:02]: [93m[1m
[Server #35608] Starting round 3/400.[0m
[INFO][07:13:02]: [Server #35608] Selected clients: [77, 2, 55, 97, 31, 61, 6, 75, 32, 63]
[INFO][07:13:02]: [Server #35608] Selecting client #77 for training.
[INFO][07:13:02]: [Server #35608] Sending the current model to client #77 (simulated).
[INFO][07:13:02]: [Server #35608] Sending 18.75 MB of payload data to client #77 (simulated).
[INFO][07:13:02]: [Server #35608] Selecting client #2 for training.
[INFO][07:13:02]: [Server #35608] Sending the current model to client #2 (simulated).
[INFO][07:13:02]: [Server #35608] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][07:13:02]: [Server #35608] Selecting client #55 for training.
[INFO][07:13:02]: [Server #35608] Sending the current model to client #55 (simulated).
[INFO][07:13:02]: [Server #35608] Sending 18.75 MB of payload data to client #55 (simulated).
[INFO][07:13:02]: [Server #35608] Selecting client #97 for training.
[INFO][07:13:02]: [Server #35608] Sending the current model to client #97 (simulated).
[INFO][07:13:02]: [Server #35608] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][07:13:02]: [Server #35608] Selecting client #31 for training.
[INFO][07:13:02]: [Server #35608] Sending the current model to client #31 (simulated).
[INFO][07:13:02]: [Server #35608] Sending 18.75 MB of payload data to client #31 (simulated).
[INFO][07:13:02]: [Server #35608] Selecting client #61 for training.
[INFO][07:13:02]: [Server #35608] Sending the current model to client #61 (simulated).
[INFO][07:13:03]: [Server #35608] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][07:13:03]: [Server #35608] Selecting client #6 for training.
[INFO][07:13:03]: [Server #35608] Sending the current model to client #6 (simulated).
[INFO][07:13:03]: [Server #35608] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][07:13:03]: [Server #35608] Selecting client #75 for training.
[INFO][07:13:03]: [Server #35608] Sending the current model to client #75 (simulated).
[INFO][07:13:04]: [Server #35608] Sending 18.75 MB of payload data to client #75 (simulated).
[INFO][07:13:04]: [Server #35608] Selecting client #32 for training.
[INFO][07:13:04]: [Server #35608] Sending the current model to client #32 (simulated).
[INFO][07:13:04]: [Server #35608] Sending 18.75 MB of payload data to client #32 (simulated).
[INFO][07:13:04]: [Server #35608] Selecting client #63 for training.
[INFO][07:13:04]: [Server #35608] Sending the current model to client #63 (simulated).
[INFO][07:13:04]: [Server #35608] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][07:15:25]: [Server #35608] Received 18.75 MB of payload data from client #77 (simulated).
[INFO][07:15:26]: [Server #35608] Received 18.75 MB of payload data from client #55 (simulated).
[INFO][07:15:28]: [Server #35608] Received 18.75 MB of payload data from client #97 (simulated).
[INFO][07:15:28]: [Server #35608] Received 18.75 MB of payload data from client #31 (simulated).
[INFO][07:15:28]: [Server #35608] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][07:15:29]: [Server #35608] Received 18.75 MB of payload data from client #75 (simulated).
[INFO][07:15:29]: [Server #35608] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][07:15:29]: [Server #35608] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][07:15:29]: [Server #35608] Received 18.75 MB of payload data from client #32 (simulated).
[INFO][07:15:29]: [Server #35608] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][07:15:29]: [Server #35608] Adding client #100 to the list of clients for aggregation.
[INFO][07:15:29]: [Server #35608] Adding client #34 to the list of clients for aggregation.
[INFO][07:15:29]: [Server #35608] Adding client #39 to the list of clients for aggregation.
[INFO][07:15:29]: [Server #35608] Adding client #85 to the list of clients for aggregation.
[INFO][07:15:29]: [Server #35608] Adding client #65 to the list of clients for aggregation.
[INFO][07:15:29]: [Server #35608] Adding client #3 to the list of clients for aggregation.
[INFO][07:15:29]: [Server #35608] Adding client #15 to the list of clients for aggregation.
[INFO][07:15:29]: [Server #35608] Adding client #45 to the list of clients for aggregation.
[INFO][07:15:29]: [Server #35608] Adding client #93 to the list of clients for aggregation.
[INFO][07:15:29]: [Server #35608] Adding client #4 to the list of clients for aggregation.
[INFO][07:15:29]: [Server #35608] Aggregating 10 clients in total.
[INFO][07:15:29]: [Server #35608] Updated weights have been received.
[INFO][07:15:29]: [Server #35608] Aggregating model weight deltas.
[INFO][07:15:29]: [Server #35608] Finished aggregating updated weights.
[INFO][07:15:29]: [Server #35608] Started model testing.
[INFO][07:15:39]: [Trainer.test] 测试完成 - 准确率: 15.32% (1532/10000)
[INFO][07:15:39]: [93m[1m[Server #35608] Global model accuracy: 15.32%
[0m
[INFO][07:15:39]: get_logged_items 被调用
[INFO][07:15:39]: 从updates获取参与客户端: [100, 34, 39, 85, 65, 3, 15, 45, 93, 4]
[INFO][07:15:39]: 客户端 100 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][07:15:39]: 客户端 34 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][07:15:39]: 客户端 39 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][07:15:39]: 客户端 85 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][07:15:39]: 客户端 65 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][07:15:39]: 客户端 3 陈旧度: 3 (当前轮次:3, 上次参与:0)
[INFO][07:15:39]: 客户端 15 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][07:15:39]: 客户端 45 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][07:15:39]: 客户端 93 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][07:15:39]: 客户端 4 陈旧度: 2 (当前轮次:3, 上次参与:1)
[INFO][07:15:39]: 陈旧度统计 - 参与客户端: [100, 34, 39, 85, 65, 3, 15, 45, 93, 4], 陈旧度: [1, 1, 1, 1, 1, 3, 1, 1, 1, 2]
[INFO][07:15:39]: 平均陈旧度: 1.3, 最大: 3, 最小: 1
[INFO][07:15:39]: 最终logged_items: {'round': 3, 'accuracy': 0.1532, 'accuracy_std': 0, 'elapsed_time': 99.01292681694031, 'processing_time': 0.0006718000222463161, 'comm_time': 0, 'round_time': 48.91303028434049, 'comm_overhead': 1499.9767303466797, 'global_accuracy': 0.1532, 'avg_staleness': 1.3, 'max_staleness': 3, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][07:15:39]: [Server #35608] All client reports have been processed.
[INFO][07:15:39]: [Server #35608] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][07:15:39]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][07:15:39]: [93m[1m
[Server #35608] Starting round 4/400.[0m
[INFO][07:15:39]: [Server #35608] Selected clients: [71, 80, 34, 49, 96, 33, 66, 42, 4, 59]
[INFO][07:15:39]: [Server #35608] Selecting client #71 for training.
[INFO][07:15:39]: [Server #35608] Sending the current model to client #71 (simulated).
[INFO][07:15:39]: [Server #35608] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][07:15:39]: [Server #35608] Selecting client #80 for training.
[INFO][07:15:39]: [Server #35608] Sending the current model to client #80 (simulated).
[INFO][07:15:39]: [Server #35608] Sending 18.75 MB of payload data to client #80 (simulated).
[INFO][07:15:39]: [Server #35608] Selecting client #34 for training.
[INFO][07:15:39]: [Server #35608] Sending the current model to client #34 (simulated).
[INFO][07:15:39]: [Server #35608] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][07:15:39]: [Server #35608] Selecting client #49 for training.
[INFO][07:15:39]: [Server #35608] Sending the current model to client #49 (simulated).
[INFO][07:15:39]: [Server #35608] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][07:15:39]: [Server #35608] Selecting client #96 for training.
[INFO][07:15:39]: [Server #35608] Sending the current model to client #96 (simulated).
[INFO][07:15:39]: [Server #35608] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][07:15:39]: [Server #35608] Selecting client #33 for training.
[INFO][07:15:39]: [Server #35608] Sending the current model to client #33 (simulated).
[INFO][07:15:39]: [Server #35608] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][07:15:39]: [Server #35608] Selecting client #66 for training.
[INFO][07:15:39]: [Server #35608] Sending the current model to client #66 (simulated).
[INFO][07:15:40]: [Server #35608] Sending 18.75 MB of payload data to client #66 (simulated).
[INFO][07:15:40]: [Server #35608] Selecting client #42 for training.
[INFO][07:15:40]: [Server #35608] Sending the current model to client #42 (simulated).
[INFO][07:15:40]: [Server #35608] Sending 18.75 MB of payload data to client #42 (simulated).
[INFO][07:15:40]: [Server #35608] Selecting client #4 for training.
[INFO][07:15:40]: [Server #35608] Sending the current model to client #4 (simulated).
[INFO][07:15:40]: [Server #35608] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][07:15:40]: [Server #35608] Selecting client #59 for training.
[INFO][07:15:40]: [Server #35608] Sending the current model to client #59 (simulated).
[INFO][07:15:41]: [Server #35608] Sending 18.75 MB of payload data to client #59 (simulated).
[INFO][07:18:04]: [Server #35608] Received 18.75 MB of payload data from client #71 (simulated).
[INFO][07:18:05]: [Server #35608] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][07:18:06]: [Server #35608] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][07:18:07]: [Server #35608] Received 18.75 MB of payload data from client #80 (simulated).
[INFO][07:18:07]: [Server #35608] Received 18.75 MB of payload data from client #96 (simulated).
[INFO][07:18:07]: [Server #35608] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][07:18:07]: [Server #35608] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][07:18:07]: [Server #35608] Received 18.75 MB of payload data from client #59 (simulated).
[INFO][07:18:07]: [Server #35608] Received 18.75 MB of payload data from client #66 (simulated).
[INFO][07:18:07]: [Server #35608] Received 18.75 MB of payload data from client #42 (simulated).
[INFO][07:18:07]: [Server #35608] Adding client #55 to the list of clients for aggregation.
[INFO][07:18:07]: [Server #35608] Adding client #77 to the list of clients for aggregation.
[INFO][07:18:07]: [Server #35608] Adding client #31 to the list of clients for aggregation.
[INFO][07:18:07]: [Server #35608] Adding client #2 to the list of clients for aggregation.
[INFO][07:18:07]: [Server #35608] Adding client #97 to the list of clients for aggregation.
[INFO][07:18:07]: [Server #35608] Adding client #6 to the list of clients for aggregation.
[INFO][07:18:07]: [Server #35608] Adding client #32 to the list of clients for aggregation.
[INFO][07:18:07]: [Server #35608] Adding client #75 to the list of clients for aggregation.
[INFO][07:18:07]: [Server #35608] Adding client #63 to the list of clients for aggregation.
[INFO][07:18:07]: [Server #35608] Adding client #61 to the list of clients for aggregation.
[INFO][07:18:07]: [Server #35608] Aggregating 10 clients in total.
[INFO][07:18:07]: [Server #35608] Updated weights have been received.
[INFO][07:18:07]: [Server #35608] Aggregating model weight deltas.
[INFO][07:18:08]: [Server #35608] Finished aggregating updated weights.
[INFO][07:18:08]: [Server #35608] Started model testing.
[INFO][07:18:17]: [Trainer.test] 测试完成 - 准确率: 19.16% (1916/10000)
[INFO][07:18:17]: [93m[1m[Server #35608] Global model accuracy: 19.16%
[0m
[INFO][07:18:17]: get_logged_items 被调用
[INFO][07:18:17]: 从updates获取参与客户端: [55, 77, 31, 2, 97, 6, 32, 75, 63, 61]
[INFO][07:18:17]: 客户端 55 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][07:18:17]: 客户端 77 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][07:18:17]: 客户端 31 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][07:18:17]: 客户端 2 陈旧度: 4 (当前轮次:4, 上次参与:0)
[INFO][07:18:17]: 客户端 97 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][07:18:17]: 客户端 6 陈旧度: 4 (当前轮次:4, 上次参与:0)
[INFO][07:18:17]: 客户端 32 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][07:18:17]: 客户端 75 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][07:18:17]: 客户端 63 陈旧度: 3 (当前轮次:4, 上次参与:1)
[INFO][07:18:17]: 客户端 61 陈旧度: 2 (当前轮次:4, 上次参与:2)
[INFO][07:18:17]: 陈旧度统计 - 参与客户端: [55, 77, 31, 2, 97, 6, 32, 75, 63, 61], 陈旧度: [1, 1, 1, 4, 1, 4, 1, 1, 3, 2]
[INFO][07:18:17]: 平均陈旧度: 1.9, 最大: 4, 最小: 1
[INFO][07:18:17]: 最终logged_items: {'round': 4, 'accuracy': 0.1916, 'accuracy_std': 0, 'elapsed_time': 111.24959182739258, 'processing_time': 0.008414600015385076, 'comm_time': 0, 'round_time': 48.208946451864904, 'comm_overhead': 1874.9709129333496, 'global_accuracy': 0.1916, 'avg_staleness': 1.9, 'max_staleness': 4, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][07:18:17]: [Server #35608] All client reports have been processed.
[INFO][07:18:17]: [Server #35608] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][07:18:17]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][07:18:17]: [93m[1m
[Server #35608] Starting round 5/400.[0m
[INFO][07:18:17]: [Server #35608] Selected clients: [81, 92, 14, 25, 90, 41, 17, 47, 73, 61]
[INFO][07:18:17]: [Server #35608] Selecting client #81 for training.
[INFO][07:18:17]: [Server #35608] Sending the current model to client #81 (simulated).
[INFO][07:18:17]: [Server #35608] Sending 18.75 MB of payload data to client #81 (simulated).
[INFO][07:18:17]: [Server #35608] Selecting client #92 for training.
[INFO][07:18:17]: [Server #35608] Sending the current model to client #92 (simulated).
[INFO][07:18:18]: [Server #35608] Sending 18.75 MB of payload data to client #92 (simulated).
[INFO][07:18:18]: [Server #35608] Selecting client #14 for training.
[INFO][07:18:18]: [Server #35608] Sending the current model to client #14 (simulated).
[INFO][07:18:18]: [Server #35608] Sending 18.75 MB of payload data to client #14 (simulated).
[INFO][07:18:18]: [Server #35608] Selecting client #25 for training.
[INFO][07:18:18]: [Server #35608] Sending the current model to client #25 (simulated).
[INFO][07:18:18]: [Server #35608] Sending 18.75 MB of payload data to client #25 (simulated).
[INFO][07:18:18]: [Server #35608] Selecting client #90 for training.
[INFO][07:18:18]: [Server #35608] Sending the current model to client #90 (simulated).
[INFO][07:18:18]: [Server #35608] Sending 18.75 MB of payload data to client #90 (simulated).
[INFO][07:18:18]: [Server #35608] Selecting client #41 for training.
[INFO][07:18:18]: [Server #35608] Sending the current model to client #41 (simulated).
[INFO][07:18:18]: [Server #35608] Sending 18.75 MB of payload data to client #41 (simulated).
[INFO][07:18:18]: [Server #35608] Selecting client #17 for training.
[INFO][07:18:18]: [Server #35608] Sending the current model to client #17 (simulated).
[INFO][07:18:18]: [Server #35608] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][07:18:18]: [Server #35608] Selecting client #47 for training.
[INFO][07:18:18]: [Server #35608] Sending the current model to client #47 (simulated).
[INFO][07:18:19]: [Server #35608] Sending 18.75 MB of payload data to client #47 (simulated).
[INFO][07:18:19]: [Server #35608] Selecting client #73 for training.
[INFO][07:18:19]: [Server #35608] Sending the current model to client #73 (simulated).
[INFO][07:18:19]: [Server #35608] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][07:18:19]: [Server #35608] Selecting client #61 for training.
[INFO][07:18:19]: [Server #35608] Sending the current model to client #61 (simulated).
[INFO][07:18:19]: [Server #35608] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][07:20:45]: [Server #35608] Received 18.75 MB of payload data from client #81 (simulated).
[INFO][07:20:47]: [Server #35608] Received 18.75 MB of payload data from client #92 (simulated).
[INFO][07:20:48]: [Server #35608] Received 18.75 MB of payload data from client #25 (simulated).
[INFO][07:20:48]: [Server #35608] Received 18.75 MB of payload data from client #90 (simulated).
[INFO][07:20:49]: [Server #35608] Received 18.75 MB of payload data from client #14 (simulated).
[INFO][07:20:49]: [Server #35608] Received 18.75 MB of payload data from client #41 (simulated).
[INFO][07:20:49]: [Server #35608] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][07:20:49]: [Server #35608] Received 18.75 MB of payload data from client #47 (simulated).
[INFO][07:20:49]: [Server #35608] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][07:20:49]: [Server #35608] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][07:20:49]: [Server #35608] Adding client #71 to the list of clients for aggregation.
[INFO][07:20:49]: [Server #35608] Adding client #34 to the list of clients for aggregation.
[INFO][07:20:49]: [Server #35608] Adding client #49 to the list of clients for aggregation.
[INFO][07:20:49]: [Server #35608] Adding client #80 to the list of clients for aggregation.
[INFO][07:20:49]: [Server #35608] Adding client #96 to the list of clients for aggregation.
[INFO][07:20:49]: [Server #35608] Adding client #33 to the list of clients for aggregation.
[INFO][07:20:49]: [Server #35608] Adding client #59 to the list of clients for aggregation.
[INFO][07:20:49]: [Server #35608] Adding client #66 to the list of clients for aggregation.
[INFO][07:20:49]: [Server #35608] Adding client #42 to the list of clients for aggregation.
[INFO][07:20:49]: [Server #35608] Adding client #4 to the list of clients for aggregation.
[INFO][07:20:49]: [Server #35608] Aggregating 10 clients in total.
[INFO][07:20:49]: [Server #35608] Updated weights have been received.
[INFO][07:20:49]: [Server #35608] Aggregating model weight deltas.
[INFO][07:20:49]: [Server #35608] Finished aggregating updated weights.
[INFO][07:20:49]: [Server #35608] Started model testing.
[INFO][07:21:00]: [Trainer.test] 测试完成 - 准确率: 26.24% (2624/10000)
[INFO][07:21:00]: [93m[1m[Server #35608] Global model accuracy: 26.24%
[0m
[INFO][07:21:00]: get_logged_items 被调用
[INFO][07:21:00]: 从updates获取参与客户端: [71, 34, 49, 80, 96, 33, 59, 66, 42, 4]
[INFO][07:21:00]: 客户端 71 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][07:21:00]: 客户端 34 陈旧度: 2 (当前轮次:5, 上次参与:3)
[INFO][07:21:00]: 客户端 49 陈旧度: 4 (当前轮次:5, 上次参与:1)
[INFO][07:21:00]: 客户端 80 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][07:21:00]: 客户端 96 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][07:21:00]: 客户端 33 陈旧度: 3 (当前轮次:5, 上次参与:2)
[INFO][07:21:00]: 客户端 59 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][07:21:00]: 客户端 66 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][07:21:00]: 客户端 42 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][07:21:00]: 客户端 4 陈旧度: 2 (当前轮次:5, 上次参与:3)
[INFO][07:21:00]: 陈旧度统计 - 参与客户端: [71, 34, 49, 80, 96, 33, 59, 66, 42, 4], 陈旧度: [1, 2, 4, 1, 1, 3, 1, 1, 1, 2]
[INFO][07:21:00]: 平均陈旧度: 1.7, 最大: 4, 最小: 1
[INFO][07:21:00]: 最终logged_items: {'round': 5, 'accuracy': 0.2624, 'accuracy_std': 0, 'elapsed_time': 147.14617276191711, 'processing_time': 0.005425399984233081, 'comm_time': 0, 'round_time': 48.13324605236994, 'comm_overhead': 2249.9650955200195, 'global_accuracy': 0.2624, 'avg_staleness': 1.7, 'max_staleness': 4, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][07:21:00]: [Server #35608] All client reports have been processed.
[INFO][07:21:00]: [Server #35608] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][07:21:00]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][07:21:00]: [93m[1m
[Server #35608] Starting round 6/400.[0m
[INFO][07:21:00]: [Server #35608] Selected clients: [71, 96, 28, 43, 40, 84, 70, 56, 5, 68]
[INFO][07:21:00]: [Server #35608] Selecting client #71 for training.
[INFO][07:21:00]: [Server #35608] Sending the current model to client #71 (simulated).
[INFO][07:21:00]: [Server #35608] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][07:21:00]: [Server #35608] Selecting client #96 for training.
[INFO][07:21:00]: [Server #35608] Sending the current model to client #96 (simulated).
[INFO][07:21:00]: [Server #35608] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][07:21:00]: [Server #35608] An existing client just disconnected.
[WARNING][07:21:00]: [Server #35608] Client process #25756 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][07:21:00]: [93m[1m[Server #35608] Closing the server due to a failed client.[0m
[INFO][07:21:00]: [Server #35608] Training concluded.
[INFO][07:21:00]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][07:21:00]: [Server #35608] Closing the server.
[INFO][07:21:00]: Closing the connection to client #32760.
[INFO][07:21:00]: [Server #35608] An existing client just disconnected.
[WARNING][07:21:00]: [Server #35608] Client process #32760 disconnected and removed from this server, 8 client processes are remaining.
[WARNING][07:21:00]: [93m[1m[Server #35608] Closing the server due to a failed client.[0m
[INFO][07:21:00]: [Server #35608] Training concluded.
[INFO][07:21:00]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][07:21:00]: [Server #35608] Closing the server.
[INFO][07:21:00]: Closing the connection to client #12660.
[INFO][07:21:00]: [Server #35608] Selecting client #28 for training.
[INFO][07:21:00]: [Server #35608] Sending the current model to client #28 (simulated).
[INFO][07:21:00]: [Server #35608] Sending 18.75 MB of payload data to client #28 (simulated).
[INFO][07:21:00]: [Server #35608] An existing client just disconnected.
[WARNING][07:21:00]: [Server #35608] Client process #35828 disconnected and removed from this server, 7 client processes are remaining.
[WARNING][07:21:00]: [93m[1m[Server #35608] Closing the server due to a failed client.[0m
[INFO][07:21:00]: [Server #35608] Training concluded.
[INFO][07:21:00]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][07:21:00]: [Server #35608] Closing the server.
[INFO][07:21:00]: Closing the connection to client #12660.
[INFO][07:21:00]: [Server #35608] An existing client just disconnected.
[WARNING][07:21:00]: [Server #35608] Client process #27912 disconnected and removed from this server, 6 client processes are remaining.
[WARNING][07:21:00]: [93m[1m[Server #35608] Closing the server due to a failed client.[0m
[INFO][07:21:00]: [Server #35608] Training concluded.
[INFO][07:21:00]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][07:21:00]: [Server #35608] Closing the server.
[INFO][07:21:00]: Closing the connection to client #12660.
[INFO][07:21:00]: [Server #35608] An existing client just disconnected.
[WARNING][07:21:00]: [Server #35608] Client process #29900 disconnected and removed from this server, 5 client processes are remaining.
[WARNING][07:21:00]: [93m[1m[Server #35608] Closing the server due to a failed client.[0m
[INFO][07:21:00]: [Server #35608] Training concluded.
[INFO][07:21:00]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][07:21:00]: [Server #35608] Closing the server.
[INFO][07:21:00]: Closing the connection to client #12660.
[INFO][07:21:00]: [Server #35608] An existing client just disconnected.
[WARNING][07:21:00]: [Server #35608] Client process #22844 disconnected and removed from this server, 4 client processes are remaining.
[WARNING][07:21:00]: [93m[1m[Server #35608] Closing the server due to a failed client.[0m
[INFO][07:21:00]: [Server #35608] Training concluded.
[INFO][07:21:00]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][07:21:00]: [Server #35608] Closing the server.
[INFO][07:21:00]: Closing the connection to client #12660.
[INFO][07:21:00]: [Server #35608] An existing client just disconnected.
[WARNING][07:21:00]: [Server #35608] Client process #13156 disconnected and removed from this server, 3 client processes are remaining.
[WARNING][07:21:00]: [93m[1m[Server #35608] Closing the server due to a failed client.[0m
[INFO][07:21:00]: [Server #35608] Training concluded.
[INFO][07:21:00]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][07:21:00]: [Server #35608] Closing the server.
[INFO][07:21:00]: Closing the connection to client #12660.
[INFO][07:21:00]: [Server #35608] An existing client just disconnected.
[WARNING][07:21:00]: [Server #35608] Client process #26620 disconnected and removed from this server, 2 client processes are remaining.
[WARNING][07:21:00]: [93m[1m[Server #35608] Closing the server due to a failed client.[0m
[INFO][07:21:00]: [Server #35608] Training concluded.
[INFO][07:21:00]: [Server #35608] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][07:21:00]: [Server #35608] Closing the server.
[INFO][07:21:00]: Closing the connection to client #12660.
[INFO][07:21:00]: Closing the connection to client #12660.
[INFO][07:21:00]: Closing the connection to client #22844.
[INFO][07:21:00]: [Server #35608] Selecting client #43 for training.
[INFO][07:21:00]: [Server #35608] Sending the current model to client #43 (simulated).
[INFO][07:21:00]: [Server #35608] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][07:21:00]: Closing the connection to client #22844.
[INFO][07:21:00]: Closing the connection to client #22844.
[INFO][07:21:00]: Closing the connection to client #22844.
[INFO][07:21:00]: Closing the connection to client #13156.
[INFO][07:21:00]: Closing the connection to client #26620.
[INFO][07:21:00]: Closing the connection to client #31184.
[INFO][07:21:00]: Closing the connection to client #22844.
[INFO][07:21:00]: Closing the connection to client #35828.
[INFO][07:21:00]: [Server #35608] Selecting client #40 for training.
[INFO][07:21:00]: [Server #35608] Sending the current model to client #40 (simulated).
[INFO][07:21:00]: [Server #35608] Sending 18.75 MB of payload data to client #40 (simulated).
[INFO][07:21:00]: Closing the connection to client #13156.
[INFO][07:21:00]: Closing the connection to client #13156.
[INFO][07:21:00]: Closing the connection to client #13156.
[INFO][07:21:00]: Closing the connection to client #26620.
[INFO][07:21:00]: Closing the connection to client #31184.
