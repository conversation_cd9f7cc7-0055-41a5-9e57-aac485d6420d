"""
Obtaining the loss criterion for training workloads according to the configuration file.
"""
from typing import Union

from torch import nn
from lightly import loss

from plato.config import Config


def get(**kwargs: Union[str, dict]):
    """Get a loss function with its name from the configuration file."""
    registered_loss_criterion = {
        "L1Loss": nn.<PERSON>1<PERSON>oss,
        "MSELoss": nn.MSELoss,
        "BCELoss": nn.<PERSON><PERSON>oss,
        "BCEWithLogitsLoss": nn.BCEWithLogitsLoss,
        "NLLLoss": nn.NLLLoss,
        "PoissonNLLLoss": nn.<PERSON>isson<PERSON>oss,
        "CrossEntropyLoss": nn.CrossEntropyLoss,
        "HingeEmbeddingLoss": nn.<PERSON><PERSON><PERSON>mbedding<PERSON>oss,
        "MarginRankingLoss": nn.<PERSON>gin<PERSON>anking<PERSON>oss,
        "TripletMarginLoss": nn.TripletMarginLoss,
        "KLDivLoss": nn.KLDivLoss,
    }

    ssl_loss_criterion = {
        "NegativeCosineSimilarity": loss.NegativeCosineSimilarity,
        "NTXentLoss": loss.NTXentLoss,
        "BarlowTwinsLoss": loss.<PERSON><PERSON><PERSON>sLoss,
        "DCLLoss": loss.<PERSON>LLoss,
        "DCLWLoss": loss.DCLWLoss,
        "DINOLoss": loss.DINOLoss,
        "PMSNCustomLoss": loss.PMSNCustomLoss,
        "SwaVLoss": loss.SwaVLoss,
        "PMSNLoss": loss.PMSNLoss,
        "SymNegCosineSimilarityLoss": loss.SymNegCosineSimilarityLoss,
        "TiCoLoss": loss.TiCoLoss,
        "VICRegLoss": loss.VICRegLoss,
        "VICRegLLoss": loss.VICRegLLoss,
        "MSNLoss": loss.MSNLoss,
    }

    registered_loss_criterion.update(ssl_loss_criterion)

    loss_criterion_name = (
        kwargs["loss_criterion"]
        if "loss_criterion" in kwargs
        else (
            Config().trainer.loss_criterion
            if hasattr(Config.trainer, "loss_criterion")
            else "CrossEntropyLoss"
        )
    )

    loss_criterion_params = (
        kwargs["loss_criterion_params"]
        if "loss_criterion_params" in kwargs
        else (
            Config().parameters.loss_criterion._asdict()
            if hasattr(Config.parameters, "loss_criterion")
            else {}
        )
    )

    loss_criterion = registered_loss_criterion.get(loss_criterion_name)

    return loss_criterion(**loss_criterion_params)
