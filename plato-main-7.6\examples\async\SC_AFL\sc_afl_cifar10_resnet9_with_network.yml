clients:
    # Type
    type: simple

    # The total number of clients (进一步优化，减少客户端数量)
    total_clients: 6  # 进一步减少到6个客户端，降低内存压力

    # The starting ID for clients (客户端ID起始值)
    id_start: 1

    # The number of clients selected in each round (进一步优化，减少并发负载)
    per_round: 3  # 减少到3个客户端并发训练

    # The maximum number of clients for aggregation (将在server部分重新定义)
    # max_aggregation_clients: 20  # 注释掉，避免与server部分冲突

    # Should the clients compute test accuracy locally?
    do_test: false

    # Do we need to simulate the wall-clock time on the clients?
    do_we_simulate_wall_time: true

    # The simulation mode
    simulation: true

    # Should we operate in asynchronous mode?
    asynchronous: true

    # The staleness bound for asynchronous training
    staleness_bound: 5

    # The maximum amount of time for clients to sleep
    sleep_simulation: false

    # Should we simulate client availability?
    availability_simulation: false

    # Should we simulate stragglers?
    speed_simulation: false

    # Should we simulate system heterogeneity?
    system_heterogeneity_simulation: false

    # Should we simulate statistical heterogeneity?
    statistical_heterogeneity_simulation: false

    # The random seed for client sampling
    random_seed: 1

server:
    address: 127.0.0.1
    port: 8000
    simulate_wall_time: true
    do_test: false

    # 使用SCAFL自定义服务器
    type: scafl

    # 异步模式配置
    synchronous: false
    synchronous_mode: false
    staleness_bound: 5

    # 聚合策略配置 - SCAFL特有
    aggregation_strategy: "scafl"

    # 缓冲池配置 - SCAFL特有（进一步优化）
    buffer_size: 10  # 适配6个客户端的缓冲池大小
    min_clients_for_aggregation: 1  # 保持1个客户端就可以聚合
    max_aggregation_clients: 3  # 进一步减少最大聚合客户端数，降低内存压力

    # SCAFL参数（优化后）
    tau_max: 5  # 增加最大陈旧度阈值，允许更多客户端参与聚合
    V: 1  # 进一步降低延迟权重，更关注陈旧度

    # 性能优化配置（优化后）
    eval_frequency: 1  # 每轮都进行全局模型评估，便于观察收敛情况
    aggregation_timeout: 20  # 减少聚合超时时间，加快训练节奏

    # 网络模拟配置
    enable_network_simulation: true
    network_config:
        bandwidth_mean: 10.0  # Mbps
        bandwidth_std: 2.0
        latency_mean: 50.0    # ms
        latency_std: 10.0
        packet_loss_rate: 0.01

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition
    partition_size: 300

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution
    concentration: 0.1

    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds（进一步优化，减少训练轮数）
    rounds: 10  # 进一步减少到10轮，快速验证系统稳定性

    # The maximum number of clients running concurrently（进一步优化，减少并发数）
    max_concurrency: 1  # 进一步减少到1个并发，避免资源竞争

    # The target accuracy（优化后）
    target_accuracy: 0.9  # 降低目标准确率，便于观察收敛趋势

    # Number of epoches for local training in each communication round（优化配置，减少训练负载）
    epochs: 2  # 优化配置，减少本地训练轮数

    # Batch size（优化后）
    batch_size: 32  # 适中的批处理大小，平衡内存使用和训练效果

    # Optimizer
    optimizer: SGD

    # CPU优化设置
    use_cuda: false  # 明确禁用CUDA
    force_cpu: true  # 强制使用CPU

    # 减少模型评估频率以提高性能（优化后）
    eval_interval: 1  # 每轮都评估，便于观察收敛情况

    # Learning rate（优化后）
    learning_rate: 0.05  # 降低学习率，提高训练稳定性

    # Learning rate schedule（优化后）
    lr_schedule: StepLR  # 使用更简单的学习率调度

    # Learning rate schedule parameters（优化后）
    lr_schedule_params:
        step_size: 10  # 每10轮降低学习率
        gamma: 0.8     # 学习率衰减因子

    # The machine learning model
    model_name: resnet_9

# 模型参数配置
parameters:
    model:
        # 自动从数据集推断
        num_classes: 10
        in_channels: 3
        
    # 数据路径
    data_path: ./data

algorithm:
    # Aggregation algorithm
    type: sc_afl

    # Staleness threshold (陈旧度阈值)
    tau_max: 5

    # Delay weight parameter (延迟权重参数)
    V: 1.0

results:
    # Write the results to a CSV - 包含虚拟时间和聚合客户端数量字段
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count

    # The directory to save results
    result_path: results/cifar10_with_network

    # The names of the results
    result_prefix: scafl_cifar10_with_network
