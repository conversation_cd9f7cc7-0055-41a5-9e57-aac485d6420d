clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 30

    # Should the clients compute test accuracy locally?
    do_test: false

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 30

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 20

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8000
    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server? Useful if max_concurrency is specified
    simulate_wall_time: true

    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 15

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 10

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: true

    # Hyperparameters in the Port algorithm
    similarity_weight: 1
    staleness_weight: 3

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/cifar10
    model_path: models/cifar10

    random_seed: 1

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition（每个用户的数据）
    partition_size: 5000

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution
    concentration: 5

    # The size of the testset on the server
    testset_size: 10000

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 80

    # The maximum number of clients running concurrently
    max_concurrency: 5

    # The target accuracy
    target_accuracy: 0.75

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 128
    optimizer: SGD
    lr_scheduler: LambdaLR

    # The machine learning model
    model_name: resnet_18

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    optimizer:
        lr: 0.1
        momentum: 0.9
        weight_decay: 0.0001
    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    result_path: results/cifar10

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy
