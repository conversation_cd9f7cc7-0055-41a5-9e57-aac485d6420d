"""
ReFedScaFL训练器类，扩展自基础训练器
提供获取最后一个批次的输入数据和模型输出的功能
"""

import logging
import torch
import asyncio
from plato.trainers import basic
from plato.config import Config
import torch.nn as nn
import torch.optim as optim
import traceback

class Trainer(basic.Trainer):
    """SC-AFL训练器类"""
    
    def __init__(self, model=None, client_id=None):
        """初始化训练器
        
        Args:
            model: 要训练的模型
            client_id: 客户端ID
        """
        try:
            # 保存客户端ID
            self.client_id = client_id
            logging.info(f"[Trainer] 初始化训练器, client_id: {client_id}")
            
            # 获取配置参数
            config = Config()
            
            # 检查并设置模型
            if model is None:
                logging.warning(f"[Trainer {client_id}] 模型为None，尝试创建默认模型")
                try:
                    # 使用动态模型加载器
                    from dynamic_loader import DynamicModelLoader, ConfigurationManager
                    dataset_name, num_classes, in_channels = ConfigurationManager.get_dataset_info_from_config(config)
                    model_name = ConfigurationManager.get_model_info_from_config(config)
                    model = DynamicModelLoader.create_model(model_name, num_classes, in_channels)
                    logging.info(f"[Trainer {client_id}] 动态创建模型 {model_name}，输入通道: {in_channels}, 类别数: {num_classes}")
                except Exception as e:
                    logging.warning(f"[Trainer {client_id}] 动态模型创建失败: {e}，回退到LeNet5")
                    # 回退到LeNet5模型
                    from plato.models import lenet5
                    in_channels = getattr(config.parameters.model, 'in_channels', 1)
                    num_classes = getattr(config.parameters.model, 'num_classes', 10)
                    model = lenet5.Model(num_classes=num_classes)
                    logging.info(f"[Trainer {client_id}] 回退创建LeNet5模型，输入通道: {in_channels}, 类别数: {num_classes}")
            
            # 保存模型引用 - 创建独立的模型实例
            if model is not None:
                import copy

                # 深拷贝模型确保完全独立
                self.model = copy.deepcopy(model)

                # 确保所有参数都是独立的
                for param in self.model.parameters():
                    param.data = param.data.clone().detach()
                    param.requires_grad = True

                # 温和处理BatchNorm：保持功能但确保独立
                for module in self.model.modules():
                    if isinstance(module, torch.nn.BatchNorm2d):
                        # 重置统计信息确保独立
                        module.reset_running_stats()
                        # 使用较小的momentum减少in-place更新影响
                        module.momentum = 0.01  # 从默认0.1降低到0.01
                        # 确保统计信息跟踪开启但独立
                        module.track_running_stats = True

                logging.info(f"[Trainer {client_id}] 创建了独立的ResNet-9模型实例，优化BatchNorm参数")
            else:
                self.model = model
            
            # 检查模型的输入通道数
            if self.model is not None:
                if hasattr(self.model, 'conv1') and hasattr(self.model.conv1, 'weight'):
                    in_channels = self.model.conv1.weight.shape[1]
                    logging.info(f"[Trainer {client_id}] 模型的输入通道数: {in_channels}")
                elif hasattr(self.model, 'in_channels'):
                    logging.info(f"[Trainer {client_id}] 模型记录的输入通道数: {self.model.in_channels}")
            
            # 从配置获取设备选择策略
            use_cuda = getattr(config.trainer, 'use_cuda', True)  # 默认使用CUDA
            force_cpu = getattr(config.trainer, 'force_cpu', False)  # 默认不强制使用CPU
            
            if force_cpu:
                self.device = torch.device("cpu")
                logging.info(f"[Trainer {client_id}] 强制使用CPU")
            else:
                self.device = torch.device("cuda" if use_cuda and torch.cuda.is_available() else "cpu")
            
            # 将模型移到设备上
            if self.model is not None:
                self.model = self.model.to(self.device)
                logging.info(f"[Trainer {client_id}] 模型已移至设备: {self.device}")
            
            # 设置损失函数
            loss_function_name = getattr(config.trainer, 'loss_function', 'CrossEntropyLoss')
            if loss_function_name == 'CrossEntropyLoss':
                self.loss_criterion = nn.CrossEntropyLoss()
            elif loss_function_name == 'MSELoss':
                self.loss_criterion = nn.MSELoss()
            else:
                logging.warning(f"[Trainer {client_id}] 未知的损失函数: {loss_function_name}，使用默认的CrossEntropyLoss")
                self.loss_criterion = nn.CrossEntropyLoss()
            
            # 设置优化器（如果模型不为None）
            if self.model is not None:
                try:
                    # 从配置获取优化器参数
                    optimizer_type = getattr(config.trainer, 'optimizer', 'SGD')
                    learning_rate = getattr(config.trainer, 'learning_rate', 0.01)
                    momentum = getattr(config.trainer, 'momentum', 0.9)
                    weight_decay = getattr(config.trainer, 'weight_decay', 0.0001)
                    
                    if optimizer_type.lower() == 'sgd':
                        self.optimizer = optim.SGD(
                            self.model.parameters(),
                            lr=learning_rate,
                            momentum=momentum,
                            weight_decay=weight_decay
                        )
                    elif optimizer_type.lower() == 'adam':
                        self.optimizer = optim.Adam(
                            self.model.parameters(),
                            lr=learning_rate,
                            weight_decay=weight_decay
                        )
                    else:
                        logging.warning(f"[Trainer {client_id}] 未知的优化器类型: {optimizer_type}，使用默认的SGD")
                        self.optimizer = optim.SGD(
                            self.model.parameters(),
                            lr=learning_rate,
                            momentum=momentum,
                            weight_decay=weight_decay
                        )
                    
                    logging.info(f"[Trainer {client_id}] 优化器已创建: {optimizer_type}, lr={learning_rate}, momentum={momentum}, weight_decay={weight_decay}")
                except Exception as e:
                    logging.error(f"[Trainer {client_id}] 创建优化器时出错: {str(e)}")
                    self.optimizer = None
            else:
                self.optimizer = None
                logging.warning(f"[Trainer {client_id}] 模型为None，无法创建优化器")
            
            # 设置学习率调度器
            if self.optimizer is not None:
                try:
                    # 从配置获取学习率调度参数
                    use_lr_scheduler = getattr(config.trainer, 'use_lr_scheduler', False)
                    if use_lr_scheduler:
                        scheduler_type = getattr(config.trainer, 'lr_scheduler_type', 'StepLR')
                        if scheduler_type == 'StepLR':
                            step_size = getattr(config.trainer, 'lr_step_size', 50)
                            gamma = getattr(config.trainer, 'lr_gamma', 0.5)
                            self.lr_scheduler = optim.lr_scheduler.StepLR(
                                self.optimizer,
                                step_size=step_size,
                                gamma=gamma
                            )
                            logging.info(f"[Trainer {client_id}] 学习率调度器已创建: {scheduler_type}, step_size={step_size}, gamma={gamma}")
                        else:
                            logging.warning(f"[Trainer {client_id}] 未知的学习率调度器类型: {scheduler_type}，不使用学习率调度")
                            self.lr_scheduler = None
                    else:
                        self.lr_scheduler = None
                except Exception as e:
                    logging.error(f"[Trainer {client_id}] 创建学习率调度器时出错: {str(e)}")
                    self.lr_scheduler = None
            else:
                self.lr_scheduler = None
                logging.warning(f"[Trainer {client_id}] 优化器为None，无法创建学习率调度器")
                
            # 添加这些行以从配置中获取参数
            self.learning_rate = getattr(config.trainer, 'learning_rate', 0.01)
            self.weight_decay = getattr(config.trainer, 'weight_decay', 0.0001)
            self.batch_size = getattr(config.trainer, 'batch_size', 32)
            self.epochs = getattr(config.trainer, 'epochs', 1)
            
            # 初始化训练统计数据
            self.train_loss = 0
            self.train_correct = 0
            self.train_total = 0
            self.num_batches = 0
            self.x_batch = None
            self.local_logits = None
            
            # 初始化陈旧度记录
            self.staleness = 0
            self.last_update_round = 0
            
            logging.info(f"[Trainer {client_id}] 初始化完成")
            
        except Exception as e:
            logging.error(f"[Trainer] 初始化时出错: {str(e)}")
            import traceback
            logging.error(f"[Trainer] 异常堆栈: {traceback.format_exc()}")
            # 确保基本属性存在，即使初始化失败
            if not hasattr(self, 'client_id'):
                self.client_id = client_id
            if not hasattr(self, 'model'):
                self.model = model
            if not hasattr(self, 'device'):
                self.device = torch.device("cpu")
            if not hasattr(self, 'loss_criterion'):
                self.loss_criterion = nn.CrossEntropyLoss()
            if not hasattr(self, 'optimizer'):
                self.optimizer = None
            if not hasattr(self, 'lr_scheduler'):
                self.lr_scheduler = None
            if not hasattr(self, 'staleness'):
                self.staleness = 0
            if not hasattr(self, 'last_update_round'):
                self.last_update_round = 0
        
    async def train(self, trainset=None, sampler=None, **kwargs):
        """异步训练模型
        
        Args:
            trainset: 训练数据集
            sampler: 数据采样器
            **kwargs: 其他参数
            
        Returns:
            dict: 训练报告
        """
        # 确保客户端ID不为None，如果是None则设置为默认值
        if not hasattr(self, 'client_id') or self.client_id is None:
            self.client_id = kwargs.get('client_id', 0)
            logging.warning(f"训练器的client_id为None，设置为默认值: {self.client_id}")
            
        # 验证传入的client_id与训练器的client_id是否匹配
        kwargs_client_id = kwargs.get('client_id')
        if kwargs_client_id is not None and kwargs_client_id != self.client_id:
            logging.warning(f"传入的client_id ({kwargs_client_id}) 与训练器的client_id ({self.client_id}) 不匹配，将使用训练器的client_id")
            
        logging.info(f"[Trainer {self.client_id}] 开始训练")
        
        try:
            if trainset is None:
                raise ValueError("训练数据集不能为空")
                
            # 检查训练集大小
            if len(trainset) == 0:
                raise ValueError("训练数据集为空")
            
            logging.info(f"[Trainer {self.client_id}] 训练集大小: {len(trainset)}")
                
            # 确保模型已初始化
            if self.model is None:
                config = Config()
                try:
                    # 使用动态模型加载器
                    from dynamic_loader import DynamicModelLoader, ConfigurationManager
                    dataset_name, num_classes, in_channels = ConfigurationManager.get_dataset_info_from_config(config)
                    model_name = ConfigurationManager.get_model_info_from_config(config)
                    self.model = DynamicModelLoader.create_model(model_name, num_classes, in_channels)
                    logging.info(f"[Trainer {self.client_id}] 动态创建模型 {model_name}，输入通道: {in_channels}, 类别数: {num_classes}")
                except Exception as e:
                    logging.warning(f"[Trainer {self.client_id}] 动态模型创建失败: {e}，回退到LeNet5")
                    # 回退到LeNet5模型
                    from plato.models import lenet5
                    in_channels = getattr(config.parameters.model, 'in_channels', 1)
                    num_classes = getattr(config.parameters.model, 'num_classes', 10)
                    self.model = lenet5.Model(num_classes=num_classes)
                    logging.info(f"[Trainer {self.client_id}] 回退创建LeNet5模型，输入通道: {in_channels}, 类别数: {num_classes}")
            
            # 确保模型在训练模式
            self.model.train()
            
            # 检查模型是否在正确的设备上
            if not hasattr(self, 'device'):
                self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            
            self.model = self.model.to(self.device)
            logging.info(f"[Trainer {self.client_id}] 模型已移至设备: {self.device}")
                
            # 从配置获取训练参数
            config = Config()
            learning_rate = getattr(config.trainer, 'learning_rate', 0.01)
            momentum = getattr(config.trainer, 'momentum', 0.9)
            weight_decay = getattr(config.trainer, 'weight_decay', 0.0001)
                
            # 初始化优化器
            self.optimizer = torch.optim.SGD(
                self.model.parameters(),
                lr=learning_rate,
                momentum=momentum,
                weight_decay=weight_decay
            )
            
            logging.info(f"[Trainer {self.client_id}] 创建优化器，lr={learning_rate}, momentum={momentum}, weight_decay={weight_decay}")
            
            # 初始化损失函数
            self.loss_criterion = torch.nn.CrossEntropyLoss()
            
            # 从配置获取批处理大小
            batch_size = getattr(config.trainer, 'batch_size', 32)
            
            # 创建数据加载器
            train_loader = torch.utils.data.DataLoader(
                dataset=trainset,
                batch_size=batch_size,
                sampler=sampler,
                shuffle=True,  # 添加随机打乱
                num_workers=0,  # 不使用多进程加载数据
                pin_memory=False  # 不使用锁页内存
            )
            
            if len(train_loader) == 0:
                raise ValueError("训练数据加载器为空")
            
            logging.info(f"[Trainer {self.client_id}] 创建数据加载器，批次大小: {batch_size}, 批次数: {len(train_loader)}")
            
            # 获取训练轮数
            epochs = getattr(config.trainer, 'epochs', 1)
            
            # 启用梯度异常检测
            torch.autograd.set_detect_anomaly(True)

            # 训练模型
            self.model.train()

            # 确保模型参数需要梯度
            for param in self.model.parameters():
                param.requires_grad = True

            # 优化BatchNorm设置以避免梯度冲突
            for module in self.model.modules():
                if isinstance(module, torch.nn.BatchNorm2d):
                    # 重置统计信息确保每次训练独立
                    module.reset_running_stats()
                    # 使用更小的momentum减少in-place操作的影响
                    module.momentum = 0.01
                    # 确保在训练模式
                    module.training = True
                    module.track_running_stats = True

            logging.info(f"[Trainer {self.client_id}] 开始训练 {epochs} 个epoch，已优化BatchNorm设置")

            for epoch in range(epochs):
                self.current_epoch = epoch
                epoch_loss = 0.0
                epoch_correct = 0
                epoch_total = 0

                logging.info(f"[Trainer {self.client_id}] 开始第 {epoch+1}/{epochs} 个epoch")

                # 检查第一个批次的数据
                try:
                    first_batch = next(iter(train_loader))
                    x_sample, y_sample = first_batch
                    logging.info(f"[Trainer {self.client_id}] 第一个批次数据: x.shape={x_sample.shape}, y.shape={y_sample.shape}, "
                               f"x.min={x_sample.min().item():.4f}, x.max={x_sample.max().item():.4f}, "
                               f"x.mean={x_sample.mean().item():.4f}, y={y_sample[:5].tolist()}")
                except Exception as e:
                    logging.error(f"[Trainer {self.client_id}] 检查第一个批次数据时出错: {str(e)}")

                batch_count = 0
                total_batches = len(train_loader)
                logging.info(f"[Trainer {self.client_id}] Epoch {epoch+1} 开始处理 {total_batches} 个批次")

                for batch_idx, (x, y) in enumerate(train_loader):
                    batch_count += 1

                    # 每10个批次记录一次进度
                    if batch_idx % 10 == 0 or batch_idx == total_batches - 1:
                        logging.info(f"[Trainer {self.client_id}] Epoch {epoch+1} 进度: {batch_idx+1}/{total_batches} 批次")

                    # 获取异步睡眠时间，设置一个合理的默认值
                    async_sleep_time = getattr(config.trainer, 'async_sleep_time', 0.001)  # 1ms默认睡眠

                    # 允许其他协程运行，但不要睡眠太久
                    if async_sleep_time > 0:
                        await asyncio.sleep(min(async_sleep_time, 0.01))  # 最多睡眠10ms，减少睡眠时间
                    
                    # 将数据移到与模型相同的设备上
                    x = x.to(self.device)
                    y = y.to(self.device)

                    # 详细记录输入数据
                    if batch_idx == 0 or batch_idx == len(train_loader) - 1:
                        logging.info(f"[Trainer {self.client_id}] Batch {batch_idx}, x.shape: {x.shape}, y.shape: {y.shape}, "
                                   f"x.min: {x.min().item():.4f}, x.max: {x.max().item():.4f}, x.mean: {x.mean().item():.4f}")
                        logging.info(f"[Trainer {self.client_id}] 模型设备: {self.device}, 输入设备: {x.device}")
                        # 打印部分标签
                        logging.info(f"[Trainer {self.client_id}] 标签样本: {y[:5].tolist()}")

                    # 清除梯度
                    self.optimizer.zero_grad()
                    
                    # 前向传播
                    output = self.model(x)
                    
                    # 计算损失
                    loss = self.loss_criterion(output, y)

                    # 只记录部分批次的损失，避免日志过多
                    if batch_idx % 5 == 0 or batch_idx == total_batches - 1:
                        logging.info(f"[Trainer {self.client_id}] Batch {batch_idx}, Loss: {loss.item():.4f}")

                    # 执行反向传播 - 添加安全保护
                    try:
                        # 确保模型参数的梯度图是独立的
                        for param in self.model.parameters():
                            if param.grad is not None:
                                param.grad.detach_()

                        # 执行反向传播
                        loss.backward()

                    except RuntimeError as e:
                        if "inplace operation" in str(e):
                            logging.error(f"[Trainer {self.client_id}] 检测到in-place操作错误，尝试重新初始化模型参数")
                            # 重新分离所有参数
                            for param in self.model.parameters():
                                param.data = param.data.clone().detach()
                                param.requires_grad = True
                            # 重新计算前向传播和反向传播
                            optimizer.zero_grad()
                            output = self.model(x)
                            loss = criterion(output, y)
                            loss.backward()
                        else:
                            raise e
                    
                    # 检查梯度是否为0或NaN
                    has_zero_gradients = False
                    for name, param in self.model.named_parameters():
                        if param.grad is not None:
                            if torch.isnan(param.grad).any():
                                logging.error(f"[Trainer {self.client_id}] Parameter {name} has NaN gradients!")
                            if torch.isinf(param.grad).any():
                                logging.error(f"[Trainer {self.client_id}] Parameter {name} has Inf gradients!")
                            if param.grad.abs().sum() == 0:
                                has_zero_gradients = True
                                logging.warning(f"[Trainer {self.client_id}] Parameter {name} has zero gradients!")
                        else:
                            logging.warning(f"[Trainer {self.client_id}] Parameter {name} has no gradients (None)!")
                    
                    # 如果所有梯度都为零，尝试添加小扰动
                    if has_zero_gradients:
                        logging.warning(f"[Trainer {self.client_id}] 检测到零梯度，尝试添加小扰动")
                        for name, param in self.model.named_parameters():
                            if param.grad is not None and param.grad.abs().sum() == 0:
                                # 添加小的随机扰动
                                param.grad.add_(torch.randn_like(param.grad) * 1e-5)

                    # 更新参数
                    self.optimizer.step()
                    
                    # 保存最后一个批次的数据和logits
                    if batch_idx == len(train_loader) - 1:
                        self.x_batch = x
                        self.local_logits = output
                    
                    # 更新训练统计信息
                    self.train_loss += loss.item()
                    epoch_loss += loss.item()
                    _, predicted = output.max(1)
                    self.train_total += y.size(0)
                    epoch_total += y.size(0)
                    self.train_correct += predicted.eq(y).sum().item()
                    epoch_correct += predicted.eq(y).sum().item()
                    self.num_batches += 1

                logging.info(f"[Trainer {self.client_id}] Epoch {epoch+1} 批次循环完成，处理了 {batch_count} 个批次")

                # 记录每个epoch的训练结果
                if epoch_total > 0:
                    epoch_accuracy = 100. * epoch_correct / epoch_total
                    logging.info(f"[Trainer {self.client_id}] Epoch {epoch+1}/{epochs} 完成 - "
                               f"处理了 {batch_count} 个批次, "
                               f"Loss: {epoch_loss/max(1, len(train_loader)):.4f}, "
                               f"Accuracy: {epoch_accuracy:.2f}%")
                else:
                    logging.warning(f"[Trainer {self.client_id}] Epoch {epoch+1}/{epochs} - 没有训练数据")
            
            # 训练完成后，检查模型参数是否有变化
            param_count = 0
            for name, param in self.model.named_parameters():
                param_count += 1
                if param_count <= 3:  # 只记录前3个参数，避免日志过多
                    logging.info(f"[Trainer {self.client_id}] 参数 {name}: 平均值={param.data.mean().item():.6f}, 标准差={param.data.std().item():.6f}")

            logging.info(f"[Trainer {self.client_id}] 🎉 训练完成！总共 {epochs} 个epoch，{param_count} 个参数")

            # 禁用梯度异常检测
            torch.autograd.set_detect_anomaly(False)

            # 获取训练报告
            report = self.get_report()
            logging.info(f"[Trainer {self.client_id}] 训练报告生成完成: Loss={report.get('train_loss', 0):.4f}, Accuracy={report.get('train_accuracy', 0):.2%}")

            return report
            
        except Exception as e:
            # 禁用梯度异常检测
            torch.autograd.set_detect_anomaly(False)
            logging.error(f"[Trainer {self.client_id}] 训练过程出错: {str(e)}")
            import traceback
            logging.error(f"[Trainer {self.client_id}] 异常堆栈: {traceback.format_exc()}")
            raise
            
    def get_last_batch_logits(self):
        """获取最后一个批次的输入数据和logits"""
        try:
            if self.x_batch is None or self.local_logits is None:
                logging.warning(f"[Trainer.get_last_batch_logits] 客户端 {self.client_id} 未找到最后一个批次的logits")
                return None, None
            return self.x_batch, self.local_logits
        except Exception as e:
            logging.error(f"[Trainer.get_last_batch_logits] 获取最后一个批次logits时出错: {str(e)}")
            return None, None
    
    def update_staleness(self, current_round):
        """更新客户端模型的陈旧度
        
        Args:
            current_round: 当前的全局轮次
        """
        try:
            # 计算陈旧度为当前轮次与上次更新轮次的差值
            self.staleness = current_round - self.last_update_round
            logging.info(f"[Trainer.update_staleness] 客户端 {self.client_id} 陈旧度更新为: {self.staleness}")
        except Exception as e:
            logging.error(f"[Trainer.update_staleness] 更新陈旧度时出错: {str(e)}")
    
    def reset_staleness(self, current_round):
        """重置客户端的陈旧度，通常在客户端被选中进行训练后调用
        
        Args:
            current_round: 当前的全局轮次
        """
        try:
            self.last_update_round = current_round
            self.staleness = 0
            logging.info(f"[Trainer.reset_staleness] 客户端 {self.client_id} 陈旧度重置")
        except Exception as e:
            logging.error(f"[Trainer.reset_staleness] 重置陈旧度时出错: {str(e)}")
        
    def get_report(self):
        """获取训练报告"""
        try:
            # 计算平均损失和准确率
            avg_loss = self.train_loss / self.num_batches if self.num_batches > 0 else 0.0
            accuracy = self.train_correct / self.train_total if self.train_total > 0 else 0.0
            
            report = {
                'train_loss': avg_loss,
                'train_accuracy': accuracy,
                'train_total': self.train_total,
                'num_batches': self.num_batches,
                'current_epoch': self.current_epoch,
                'total_epochs': self.epochs,
                'client_id': self.client_id,
                'staleness': self.staleness
            }
            
            logging.info(f"[Trainer.get_report] 客户端 {self.client_id} 训练报告 - "
                        f"Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2%}, "
                        f"陈旧度: {self.staleness}")
            
            return report
            
        except Exception as e:
            logging.error(f"[Trainer.get_report] 生成训练报告时出错: {str(e)}")
            return {
                'train_loss': 0.0,
                'train_accuracy': 0.0,
                'train_total': 0,
                'num_batches': 0,
                'current_epoch': 0,
                'total_epochs': self.epochs,
                'client_id': self.client_id,
                'staleness': self.staleness if hasattr(self, 'staleness') else 0
            }
            
    @staticmethod
    def calculate_average_staleness(reports):
        """计算多个客户端的平均陈旧度
        
        Args:
            reports: 客户端训练报告列表
            
        Returns:
            float: 平均陈旧度
        """
        try:
            if not reports:
                return 0.0
                
            total_staleness = sum(report.get('staleness', 0) for report in reports)
            avg_staleness = total_staleness / len(reports)
            
            logging.info(f"[Trainer.calculate_average_staleness] 平均陈旧度: {avg_staleness:.2f}")
            return avg_staleness
            
        except Exception as e:
            logging.error(f"[Trainer.calculate_average_staleness] 计算平均陈旧度时出错: {str(e)}")
            return 0.0

    def test(self, testset):
        """
        使用测试集评估模型
        
        参数:
            testset: 测试数据集
            
        返回:
            float: 测试准确率 (0~1)
        """
        try:
            if self.model is None:
                logging.error(f"[Trainer {self.client_id}] 模型为None，无法进行测试")
                return 0.0
                
            # 确保模型在评估模式
            self.model.eval()
            
            # 创建测试数据加载器
            test_loader = torch.utils.data.DataLoader(
                dataset=testset,
                batch_size=128,  # 较大的批量加速评估
                shuffle=False,
                num_workers=0
            )
            
            correct = 0
            total = 0
            test_loss = 0.0
            
            # 检查测试集大小
            if not hasattr(test_loader, 'dataset') or not hasattr(test_loader.dataset, '__len__') or len(test_loader.dataset) == 0:
                logging.error(f"[Trainer {self.client_id}] 测试集为空或无效")
                return 0.0
            
            # 评估过程无需计算梯度
            with torch.no_grad():
                for inputs, targets in test_loader:
                    # 将数据移到正确的设备
                    inputs, targets = inputs.to(self.device), targets.to(self.device)
                    
                    # 检查输入通道数是否与模型匹配
                    model_in_channels = None
                    if hasattr(self.model, 'conv1') and hasattr(self.model.conv1, 'weight'):
                        model_in_channels = self.model.conv1.weight.shape[1]
                    
                    if model_in_channels is not None and inputs.shape[1] != model_in_channels:
                        logging.warning(f"[Trainer {self.client_id}] 测试数据通道数({inputs.shape[1]})与模型通道数({model_in_channels})不匹配，尝试调整...")
                        if inputs.shape[1] == 1 and model_in_channels == 3:
                            # 如果输入是1通道，模型期望3通道，则复制通道
                            inputs = inputs.repeat(1, 3, 1, 1)
                            logging.info(f"[Trainer {self.client_id}] 已将1通道输入扩展为3通道")
                        elif inputs.shape[1] == 3 and model_in_channels == 1:
                            # 如果输入是3通道，模型期望1通道，则取平均值
                            inputs = inputs.mean(dim=1, keepdim=True)
                            logging.info(f"[Trainer {self.client_id}] 已将3通道输入转换为1通道")
                        else:
                            logging.error(f"[Trainer {self.client_id}] 无法自动调整通道：数据{inputs.shape[1]}通道，模型{model_in_channels}通道")
                            try:
                                # 尝试重新创建一个与数据匹配的模型
                                from dynamic_loader import DynamicModelLoader, ConfigurationManager
                                config = Config()
                                model_name = ConfigurationManager.get_model_info_from_config(config)
                                new_model = DynamicModelLoader.create_model(model_name, num_classes=10, in_channels=inputs.shape[1])
                                new_model = new_model.to(self.device)
                                
                                # 复制可以复用的层权重
                                if hasattr(self.model, 'fc4') and hasattr(new_model, 'fc4'):
                                    new_model.fc4.load_state_dict(self.model.fc4.state_dict())
                                if hasattr(self.model, 'fc5') and hasattr(new_model, 'fc5'):
                                    new_model.fc5.load_state_dict(self.model.fc5.state_dict())
                                    
                                # 更新模型引用
                                self.model = new_model
                                logging.info(f"[Trainer {self.client_id}] 已创建与数据通道数匹配的新模型")
                            except Exception as e:
                                logging.error(f"[Trainer {self.client_id}] 创建新模型失败: {str(e)}")
                    
                    # 前向传播
                    outputs = self.model(inputs)
                    loss = self.loss_criterion(outputs, targets)
                    test_loss += loss.item()
                    
                    # 计算准确率
                    _, predicted = outputs.max(1)
                    total += targets.size(0)
                    correct += predicted.eq(targets).sum().item()
            
            # 计算平均损失和准确率
            avg_loss = test_loss / len(test_loader)
            accuracy = correct / total
            
            logging.info(f"[Trainer {self.client_id}] 测试结果 - 准确率: {accuracy:.4f} ({correct}/{total}), 损失: {avg_loss:.4f}")
            return accuracy
            
        except Exception as e:
            logging.error(f"[Trainer {self.client_id}] 测试过程出错: {str(e)}")
            import traceback
            logging.error(f"[Trainer {self.client_id}] 异常堆栈: {traceback.format_exc()}")
            return 0.0