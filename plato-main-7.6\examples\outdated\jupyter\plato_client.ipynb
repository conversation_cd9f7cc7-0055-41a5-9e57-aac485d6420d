{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "from torch import nn\n", "import nest_asyncio\n", "import asyncio\n", "sys.argv = [sys.argv[0], \"-i\", \"1\"]\n", "from plato.clients import simple\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[INFO][07:32:51]: Trainer: basic\n", "[INFO][07:32:51]: Algorithm: fedavg\n", "[INFO][07:32:56]: [Client #1] Contacting the central server.\n", "[INFO][07:32:56]: [Client #1] Connecting to the server at http://127.0.0.1:8000.\n", "[INFO][07:32:56]: [Client #1] Connected to the server.\n", "[INFO][07:32:56]: [Client #1] Waiting to be selected.\n", "[INFO][07:32:56]: [Client #1] Selected by the server.\n", "[INFO][07:32:56]: [Client #1] Loading its data source...\n", "[INFO][07:32:56]: Data source: MNIST\n", "[INFO][07:32:56]: [Client #1] Dataset size: 60000\n", "[INFO][07:32:56]: [Client #1] Sampler: iid\n", "[INFO][07:32:56]: [Client #1] Received 0.24 MB of payload data from the server.\n", "[INFO][07:32:56]: [Client #1] Started training.\n", "[INFO][07:32:56]: [Client #1] Loading the dataset.\n", "[INFO][07:32:56]: [Client #1] Epoch: [1/5][0/625]\tLoss: 2.331157\n", "[INFO][07:32:57]: [Client #1] Epoch: [1/5][10/625]\tLoss: 2.286354\n", "[INFO][07:32:57]: [Client #1] Epoch: [1/5][20/625]\tLoss: 2.269259\n", "[INFO][07:32:57]: [Client #1] Epoch: [1/5][30/625]\tLoss: 2.197079\n", "[INFO][07:32:57]: [Client #1] Epoch: [1/5][40/625]\tLoss: 2.082405\n", "[INFO][07:32:57]: [Client #1] Epoch: [1/5][50/625]\tLoss: 1.716630\n", "[INFO][07:32:57]: [Client #1] Epoch: [1/5][60/625]\tLoss: 0.982134\n", "[INFO][07:32:57]: [Client #1] Epoch: [1/5][70/625]\tLoss: 0.809223\n", "[INFO][07:32:57]: [Client #1] Epoch: [1/5][80/625]\tLoss: 0.920669\n", "[INFO][07:32:57]: [Client #1] Epoch: [1/5][90/625]\tLoss: 0.396763\n", "[INFO][07:32:58]: [Client #1] Epoch: [1/5][100/625]\tLoss: 0.592748\n", "[INFO][07:32:58]: [Client #1] Epoch: [1/5][110/625]\tLoss: 0.623444\n", "[INFO][07:32:58]: [Client #1] Epoch: [1/5][120/625]\tLoss: 0.692866\n", "[INFO][07:32:58]: [Client #1] Epoch: [1/5][130/625]\tLoss: 0.529191\n", "[INFO][07:32:58]: [Client #1] Epoch: [1/5][140/625]\tLoss: 0.337756\n", "[INFO][07:32:58]: [Client #1] Epoch: [1/5][150/625]\tLoss: 0.626956\n", "[INFO][07:32:58]: [Client #1] Epoch: [1/5][160/625]\tLoss: 0.090431\n", "[INFO][07:32:58]: [Client #1] Epoch: [1/5][170/625]\tLoss: 0.200666\n", "[INFO][07:32:58]: [Client #1] Epoch: [1/5][180/625]\tLoss: 0.037089\n", "[INFO][07:32:59]: [Client #1] Epoch: [1/5][190/625]\tLoss: 0.275781\n", "[INFO][07:32:59]: [Client #1] Epoch: [1/5][200/625]\tLoss: 0.272860\n", "[INFO][07:32:59]: [Client #1] Epoch: [1/5][210/625]\tLoss: 0.268901\n", "[INFO][07:32:59]: [Client #1] Epoch: [1/5][220/625]\tLoss: 0.499113\n", "[INFO][07:32:59]: [Client #1] Epoch: [1/5][230/625]\tLoss: 0.269573\n", "[INFO][07:32:59]: [Client #1] Epoch: [1/5][240/625]\tLoss: 0.390805\n", "[INFO][07:32:59]: [Client #1] Epoch: [1/5][250/625]\tLoss: 0.342597\n", "[INFO][07:32:59]: [Client #1] Epoch: [1/5][260/625]\tLoss: 0.187462\n", "[INFO][07:32:59]: [Client #1] Epoch: [1/5][270/625]\tLoss: 0.206512\n", "[INFO][07:33:00]: [Client #1] Epoch: [1/5][280/625]\tLoss: 0.143654\n", "[INFO][07:33:00]: [Client #1] Epoch: [1/5][290/625]\tLoss: 0.161942\n", "[INFO][07:33:00]: [Client #1] Epoch: [1/5][300/625]\tLoss: 0.511672\n", "[INFO][07:33:00]: [Client #1] Epoch: [1/5][310/625]\tLoss: 0.389299\n", "[INFO][07:33:00]: [Client #1] Epoch: [1/5][320/625]\tLoss: 0.150188\n", "[INFO][07:33:00]: [Client #1] Epoch: [1/5][330/625]\tLoss: 0.162340\n", "[INFO][07:33:00]: [Client #1] Epoch: [1/5][340/625]\tLoss: 0.193923\n", "[INFO][07:33:00]: [Client #1] Epoch: [1/5][350/625]\tLoss: 0.286891\n", "[INFO][07:33:00]: [Client #1] Epoch: [1/5][360/625]\tLoss: 0.014900\n", "[INFO][07:33:01]: [Client #1] Epoch: [1/5][370/625]\tLoss: 0.159801\n", "[INFO][07:33:01]: [Client #1] Epoch: [1/5][380/625]\tLoss: 0.638913\n", "[INFO][07:33:01]: [Client #1] Epoch: [1/5][390/625]\tLoss: 0.170257\n", "[INFO][07:33:01]: [Client #1] Epoch: [1/5][400/625]\tLoss: 0.073908\n", "[INFO][07:33:01]: [Client #1] Epoch: [1/5][410/625]\tLoss: 0.074610\n", "[INFO][07:33:01]: [Client #1] Epoch: [1/5][420/625]\tLoss: 0.495146\n", "[INFO][07:33:01]: [Client #1] Epoch: [1/5][430/625]\tLoss: 0.284808\n", "[INFO][07:33:01]: [Client #1] Epoch: [1/5][440/625]\tLoss: 0.343499\n", "[INFO][07:33:01]: [Client #1] Epoch: [1/5][450/625]\tLoss: 0.069057\n", "[INFO][07:33:02]: [Client #1] Epoch: [1/5][460/625]\tLoss: 0.166530\n", "[INFO][07:33:02]: [Client #1] Epoch: [1/5][470/625]\tLoss: 0.224146\n", "[INFO][07:33:02]: [Client #1] Epoch: [1/5][480/625]\tLoss: 0.503824\n", "[INFO][07:33:02]: [Client #1] Epoch: [1/5][490/625]\tLoss: 0.054568\n", "[INFO][07:33:02]: [Client #1] Epoch: [1/5][500/625]\tLoss: 0.299396\n", "[INFO][07:33:02]: [Client #1] Epoch: [1/5][510/625]\tLoss: 0.223458\n", "[INFO][07:33:02]: [Client #1] Epoch: [1/5][520/625]\tLoss: 0.156710\n", "[INFO][07:33:02]: [Client #1] Epoch: [1/5][530/625]\tLoss: 0.098944\n", "[INFO][07:33:02]: [Client #1] Epoch: [1/5][540/625]\tLoss: 0.052896\n", "[INFO][07:33:03]: [Client #1] Epoch: [1/5][550/625]\tLoss: 0.062727\n", "[INFO][07:33:03]: [Client #1] Epoch: [1/5][560/625]\tLoss: 0.076896\n", "[INFO][07:33:03]: [Client #1] Epoch: [1/5][570/625]\tLoss: 0.119420\n", "[INFO][07:33:03]: [Client #1] Epoch: [1/5][580/625]\tLoss: 0.233989\n", "[INFO][07:33:03]: [Client #1] Epoch: [1/5][590/625]\tLoss: 0.291756\n", "[INFO][07:33:03]: [Client #1] Epoch: [1/5][600/625]\tLoss: 0.071070\n", "[INFO][07:33:03]: [Client #1] Epoch: [1/5][610/625]\tLoss: 0.080491\n", "[INFO][07:33:03]: [Client #1] Epoch: [1/5][620/625]\tLoss: 0.045458\n", "[INFO][07:33:04]: [Client #1] Epoch: [2/5][0/625]\tLoss: 0.012542\n", "[INFO][07:33:04]: [Client #1] Epoch: [2/5][10/625]\tLoss: 0.070881\n", "[INFO][07:33:04]: [Client #1] Epoch: [2/5][20/625]\tLoss: 0.031030\n", "[INFO][07:33:04]: [Client #1] Epoch: [2/5][30/625]\tLoss: 0.064274\n", "[INFO][07:33:04]: [Client #1] Epoch: [2/5][40/625]\tLoss: 0.010197\n", "[INFO][07:33:04]: [Client #1] Epoch: [2/5][50/625]\tLoss: 0.059792\n", "[INFO][07:33:04]: [Client #1] Epoch: [2/5][60/625]\tLoss: 0.020701\n", "[INFO][07:33:04]: [Client #1] Epoch: [2/5][70/625]\tLoss: 0.066382\n", "[INFO][07:33:05]: [Client #1] Epoch: [2/5][80/625]\tLoss: 0.039451\n", "[INFO][07:33:05]: [Client #1] Epoch: [2/5][90/625]\tLoss: 0.025367\n", "[INFO][07:33:05]: [Client #1] Epoch: [2/5][100/625]\tLoss: 0.032388\n", "[INFO][07:33:05]: [Client #1] Epoch: [2/5][110/625]\tLoss: 0.104074\n", "[INFO][07:33:05]: [Client #1] Epoch: [2/5][120/625]\tLoss: 0.011901\n", "[INFO][07:33:05]: [Client #1] Epoch: [2/5][130/625]\tLoss: 0.167200\n", "[INFO][07:33:05]: [Client #1] Epoch: [2/5][140/625]\tLoss: 0.503492\n", "[INFO][07:33:05]: [Client #1] Epoch: [2/5][150/625]\tLoss: 0.340106\n", "[INFO][07:33:06]: [Client #1] Epoch: [2/5][160/625]\tLoss: 0.092187\n", "[INFO][07:33:06]: [Client #1] Epoch: [2/5][170/625]\tLoss: 0.055631\n", "[INFO][07:33:06]: [Client #1] Epoch: [2/5][180/625]\tLoss: 0.011337\n", "[INFO][07:33:06]: [Client #1] Epoch: [2/5][190/625]\tLoss: 0.071793\n", "[INFO][07:33:06]: [Client #1] Epoch: [2/5][200/625]\tLoss: 0.031692\n", "[INFO][07:33:06]: [Client #1] Epoch: [2/5][210/625]\tLoss: 0.050511\n", "[INFO][07:33:06]: [Client #1] Epoch: [2/5][220/625]\tLoss: 0.063282\n", "[INFO][07:33:07]: [Client #1] Epoch: [2/5][230/625]\tLoss: 0.018306\n", "[INFO][07:33:07]: [Client #1] Epoch: [2/5][240/625]\tLoss: 0.459021\n", "[INFO][07:33:07]: [Client #1] Epoch: [2/5][250/625]\tLoss: 0.059213\n", "[INFO][07:33:07]: [Client #1] Epoch: [2/5][260/625]\tLoss: 0.122833\n", "[INFO][07:33:07]: [Client #1] Epoch: [2/5][270/625]\tLoss: 0.085385\n", "[INFO][07:33:07]: [Client #1] Epoch: [2/5][280/625]\tLoss: 0.043795\n", "[INFO][07:33:07]: [Client #1] Epoch: [2/5][290/625]\tLoss: 0.071102\n", "[INFO][07:33:08]: [Client #1] Epoch: [2/5][300/625]\tLoss: 0.024249\n", "[INFO][07:33:08]: [Client #1] Epoch: [2/5][310/625]\tLoss: 0.121319\n", "[INFO][07:33:08]: [Client #1] Epoch: [2/5][320/625]\tLoss: 0.390995\n", "[INFO][07:33:08]: [Client #1] Epoch: [2/5][330/625]\tLoss: 0.031926\n", "[INFO][07:33:08]: [Client #1] Epoch: [2/5][340/625]\tLoss: 0.083730\n", "[INFO][07:33:08]: [Client #1] Epoch: [2/5][350/625]\tLoss: 0.004253\n", "[INFO][07:33:08]: [Client #1] Epoch: [2/5][360/625]\tLoss: 0.076553\n", "[INFO][07:33:08]: [Client #1] Epoch: [2/5][370/625]\tLoss: 0.079957\n", "[INFO][07:33:08]: [Client #1] Epoch: [2/5][380/625]\tLoss: 0.063505\n", "[INFO][07:33:09]: [Client #1] Epoch: [2/5][390/625]\tLoss: 0.138996\n", "[INFO][07:33:09]: [Client #1] Epoch: [2/5][400/625]\tLoss: 0.069826\n", "[INFO][07:33:09]: [Client #1] Epoch: [2/5][410/625]\tLoss: 0.324890\n", "[INFO][07:33:09]: [Client #1] Epoch: [2/5][420/625]\tLoss: 0.051450\n", "[INFO][07:33:09]: [Client #1] Epoch: [2/5][430/625]\tLoss: 0.123524\n", "[INFO][07:33:09]: [Client #1] Epoch: [2/5][440/625]\tLoss: 0.095217\n", "[INFO][07:33:09]: [Client #1] Epoch: [2/5][450/625]\tLoss: 0.018767\n", "[INFO][07:33:09]: [Client #1] Epoch: [2/5][460/625]\tLoss: 0.134987\n", "[INFO][07:33:10]: [Client #1] Epoch: [2/5][470/625]\tLoss: 0.016259\n", "[INFO][07:33:10]: [Client #1] Epoch: [2/5][480/625]\tLoss: 0.057368\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO][07:33:10]: [Client #1] Epoch: [2/5][490/625]\tLoss: 0.009492\n", "[INFO][07:33:10]: [Client #1] Epoch: [2/5][500/625]\tLoss: 0.393439\n", "[INFO][07:33:10]: [Client #1] Epoch: [2/5][510/625]\tLoss: 0.038963\n", "[INFO][07:33:10]: [Client #1] Epoch: [2/5][520/625]\tLoss: 0.074254\n", "[INFO][07:33:10]: [Client #1] Epoch: [2/5][530/625]\tLoss: 0.126864\n", "[INFO][07:33:10]: [Client #1] Epoch: [2/5][540/625]\tLoss: 0.217911\n", "[INFO][07:33:10]: [Client #1] Epoch: [2/5][550/625]\tLoss: 0.213304\n", "[INFO][07:33:11]: [Client #1] Epoch: [2/5][560/625]\tLoss: 0.030549\n", "[INFO][07:33:11]: [Client #1] Epoch: [2/5][570/625]\tLoss: 0.035900\n", "[INFO][07:33:11]: [Client #1] Epoch: [2/5][580/625]\tLoss: 0.073075\n", "[INFO][07:33:11]: [Client #1] Epoch: [2/5][590/625]\tLoss: 0.051711\n", "[INFO][07:33:11]: [Client #1] Epoch: [2/5][600/625]\tLoss: 0.092881\n", "[INFO][07:33:11]: [Client #1] Epoch: [2/5][610/625]\tLoss: 0.004620\n", "[INFO][07:33:11]: [Client #1] Epoch: [2/5][620/625]\tLoss: 0.121667\n", "[INFO][07:33:11]: [Client #1] Epoch: [3/5][0/625]\tLoss: 0.010603\n", "[INFO][07:33:12]: [Client #1] Epoch: [3/5][10/625]\tLoss: 0.036759\n", "[INFO][07:33:12]: [Client #1] Epoch: [3/5][20/625]\tLoss: 0.016824\n", "[INFO][07:33:12]: [Client #1] Epoch: [3/5][30/625]\tLoss: 0.028271\n", "[INFO][07:33:12]: [Client #1] Epoch: [3/5][40/625]\tLoss: 0.003855\n", "[INFO][07:33:12]: [Client #1] Epoch: [3/5][50/625]\tLoss: 0.002367\n", "[INFO][07:33:12]: [Client #1] Epoch: [3/5][60/625]\tLoss: 0.013779\n", "[INFO][07:33:12]: [Client #1] Epoch: [3/5][70/625]\tLoss: 0.190488\n", "[INFO][07:33:13]: [Client #1] Epoch: [3/5][80/625]\tLoss: 0.008509\n", "[INFO][07:33:13]: [Client #1] Epoch: [3/5][90/625]\tLoss: 0.070263\n", "[INFO][07:33:13]: [Client #1] Epoch: [3/5][100/625]\tLoss: 0.007114\n", "[INFO][07:33:13]: [Client #1] Epoch: [3/5][110/625]\tLoss: 0.022027\n", "[INFO][07:33:13]: [Client #1] Epoch: [3/5][120/625]\tLoss: 0.081912\n", "[INFO][07:33:13]: [Client #1] Epoch: [3/5][130/625]\tLoss: 0.015457\n", "[INFO][07:33:13]: [Client #1] Epoch: [3/5][140/625]\tLoss: 0.160845\n", "[INFO][07:33:13]: [Client #1] Epoch: [3/5][150/625]\tLoss: 0.022678\n", "[INFO][07:33:13]: [Client #1] Epoch: [3/5][160/625]\tLoss: 0.055910\n", "[INFO][07:33:14]: [Client #1] Epoch: [3/5][170/625]\tLoss: 0.174369\n", "[INFO][07:33:14]: [Client #1] Epoch: [3/5][180/625]\tLoss: 0.004930\n", "[INFO][07:33:14]: [Client #1] Epoch: [3/5][190/625]\tLoss: 0.070703\n", "[INFO][07:33:14]: [Client #1] Epoch: [3/5][200/625]\tLoss: 0.003180\n", "[INFO][07:33:14]: [Client #1] Epoch: [3/5][210/625]\tLoss: 0.113983\n", "[INFO][07:33:14]: [Client #1] Epoch: [3/5][220/625]\tLoss: 0.070396\n", "[INFO][07:33:14]: [Client #1] Epoch: [3/5][230/625]\tLoss: 0.075590\n", "[INFO][07:33:14]: [Client #1] Epoch: [3/5][240/625]\tLoss: 0.024757\n", "[INFO][07:33:14]: [Client #1] Epoch: [3/5][250/625]\tLoss: 0.061517\n", "[INFO][07:33:15]: [Client #1] Epoch: [3/5][260/625]\tLoss: 0.167029\n", "[INFO][07:33:15]: [Client #1] Epoch: [3/5][270/625]\tLoss: 0.114834\n", "[INFO][07:33:15]: [Client #1] Epoch: [3/5][280/625]\tLoss: 0.040528\n", "[INFO][07:33:15]: [Client #1] Epoch: [3/5][290/625]\tLoss: 0.188202\n", "[INFO][07:33:15]: [Client #1] Epoch: [3/5][300/625]\tLoss: 0.011327\n", "[INFO][07:33:15]: [Client #1] Epoch: [3/5][310/625]\tLoss: 0.079586\n", "[INFO][07:33:15]: [Client #1] Epoch: [3/5][320/625]\tLoss: 0.010110\n", "[INFO][07:33:15]: [Client #1] Epoch: [3/5][330/625]\tLoss: 0.155522\n", "[INFO][07:33:15]: [Client #1] Epoch: [3/5][340/625]\tLoss: 0.024463\n", "[INFO][07:33:16]: [Client #1] Epoch: [3/5][350/625]\tLoss: 0.159870\n", "[INFO][07:33:16]: [Client #1] Epoch: [3/5][360/625]\tLoss: 0.084890\n", "[INFO][07:33:16]: [Client #1] Epoch: [3/5][370/625]\tLoss: 0.064031\n", "[INFO][07:33:16]: [Client #1] Epoch: [3/5][380/625]\tLoss: 0.115388\n", "[INFO][07:33:16]: [Client #1] Epoch: [3/5][390/625]\tLoss: 0.026737\n", "[INFO][07:33:16]: [Client #1] Epoch: [3/5][400/625]\tLoss: 0.051554\n", "[INFO][07:33:16]: [Client #1] Epoch: [3/5][410/625]\tLoss: 0.106841\n", "[INFO][07:33:16]: [Client #1] Epoch: [3/5][420/625]\tLoss: 0.204638\n", "[INFO][07:33:16]: [Client #1] Epoch: [3/5][430/625]\tLoss: 0.005033\n", "[INFO][07:33:17]: [Client #1] Epoch: [3/5][440/625]\tLoss: 0.135289\n", "[INFO][07:33:17]: [Client #1] Epoch: [3/5][450/625]\tLoss: 0.070188\n", "[INFO][07:33:17]: [Client #1] Epoch: [3/5][460/625]\tLoss: 0.085420\n", "[INFO][07:33:17]: [Client #1] Epoch: [3/5][470/625]\tLoss: 0.152652\n", "[INFO][07:33:17]: [Client #1] Epoch: [3/5][480/625]\tLoss: 0.050290\n", "[INFO][07:33:17]: [Client #1] Epoch: [3/5][490/625]\tLoss: 0.044836\n", "[INFO][07:33:17]: [Client #1] Epoch: [3/5][500/625]\tLoss: 0.007281\n", "[INFO][07:33:17]: [Client #1] Epoch: [3/5][510/625]\tLoss: 0.001308\n", "[INFO][07:33:18]: [Client #1] Epoch: [3/5][520/625]\tLoss: 0.565261\n", "[INFO][07:33:18]: [Client #1] Epoch: [3/5][530/625]\tLoss: 0.021957\n", "[INFO][07:33:18]: [Client #1] Epoch: [3/5][540/625]\tLoss: 0.018363\n", "[INFO][07:33:18]: [Client #1] Epoch: [3/5][550/625]\tLoss: 0.073910\n", "[INFO][07:33:18]: [Client #1] Epoch: [3/5][560/625]\tLoss: 0.081173\n", "[INFO][07:33:18]: [Client #1] Epoch: [3/5][570/625]\tLoss: 0.048465\n", "[INFO][07:33:19]: [Client #1] Epoch: [3/5][580/625]\tLoss: 0.116553\n", "[INFO][07:33:19]: [Client #1] Epoch: [3/5][590/625]\tLoss: 0.122709\n", "[INFO][07:33:19]: [Client #1] Epoch: [3/5][600/625]\tLoss: 0.041422\n", "[INFO][07:33:19]: [Client #1] Epoch: [3/5][610/625]\tLoss: 0.101674\n", "[INFO][07:33:19]: [Client #1] Epoch: [3/5][620/625]\tLoss: 0.025650\n", "[INFO][07:33:19]: [Client #1] Epoch: [4/5][0/625]\tLoss: 0.014428\n", "[INFO][07:33:19]: [Client #1] Epoch: [4/5][10/625]\tLoss: 0.010470\n", "[INFO][07:33:20]: [Client #1] Epoch: [4/5][20/625]\tLoss: 0.003450\n", "[INFO][07:33:20]: [Client #1] Epoch: [4/5][30/625]\tLoss: 0.076539\n", "[INFO][07:33:20]: [Client #1] Epoch: [4/5][40/625]\tLoss: 0.073897\n", "[INFO][07:33:20]: [Client #1] Epoch: [4/5][50/625]\tLoss: 0.179886\n", "[INFO][07:33:20]: [Client #1] Epoch: [4/5][60/625]\tLoss: 0.014421\n", "[INFO][07:33:20]: [Client #1] Epoch: [4/5][70/625]\tLoss: 0.052684\n", "[INFO][07:33:20]: [Client #1] Epoch: [4/5][80/625]\tLoss: 0.046495\n", "[INFO][07:33:20]: [Client #1] Epoch: [4/5][90/625]\tLoss: 0.018793\n", "[INFO][07:33:21]: [Client #1] Epoch: [4/5][100/625]\tLoss: 0.008418\n", "[INFO][07:33:21]: [Client #1] Epoch: [4/5][110/625]\tLoss: 0.014784\n", "[INFO][07:33:21]: [Client #1] Epoch: [4/5][120/625]\tLoss: 0.005646\n", "[INFO][07:33:21]: [Client #1] Epoch: [4/5][130/625]\tLoss: 0.025147\n", "[INFO][07:33:21]: [Client #1] Epoch: [4/5][140/625]\tLoss: 0.010291\n", "[INFO][07:33:21]: [Client #1] Epoch: [4/5][150/625]\tLoss: 0.141883\n", "[INFO][07:33:21]: [Client #1] Epoch: [4/5][160/625]\tLoss: 0.007521\n", "[INFO][07:33:21]: [Client #1] Epoch: [4/5][170/625]\tLoss: 0.055170\n", "[INFO][07:33:21]: [Client #1] Epoch: [4/5][180/625]\tLoss: 0.011517\n", "[INFO][07:33:22]: [Client #1] Epoch: [4/5][190/625]\tLoss: 0.097038\n", "[INFO][07:33:22]: [Client #1] Epoch: [4/5][200/625]\tLoss: 0.033146\n", "[INFO][07:33:22]: [Client #1] Epoch: [4/5][210/625]\tLoss: 0.055189\n", "[INFO][07:33:22]: [Client #1] Epoch: [4/5][220/625]\tLoss: 0.002309\n", "[INFO][07:33:22]: [Client #1] Epoch: [4/5][230/625]\tLoss: 0.005009\n", "[INFO][07:33:22]: [Client #1] Epoch: [4/5][240/625]\tLoss: 0.007004\n", "[INFO][07:33:22]: [Client #1] Epoch: [4/5][250/625]\tLoss: 0.006556\n", "[INFO][07:33:23]: [Client #1] Epoch: [4/5][260/625]\tLoss: 0.006667\n", "[INFO][07:33:23]: [Client #1] Epoch: [4/5][270/625]\tLoss: 0.163918\n", "[INFO][07:33:23]: [Client #1] Epoch: [4/5][280/625]\tLoss: 0.000776\n", "[INFO][07:33:23]: [Client #1] Epoch: [4/5][290/625]\tLoss: 0.018028\n", "[INFO][07:33:23]: [Client #1] Epoch: [4/5][300/625]\tLoss: 0.040011\n", "[INFO][07:33:23]: [Client #1] Epoch: [4/5][310/625]\tLoss: 0.020127\n", "[INFO][07:33:23]: [Client #1] Epoch: [4/5][320/625]\tLoss: 0.179901\n", "[INFO][07:33:23]: [Client #1] Epoch: [4/5][330/625]\tLoss: 0.087224\n", "[INFO][07:33:23]: [Client #1] Epoch: [4/5][340/625]\tLoss: 0.001573\n", "[INFO][07:33:24]: [Client #1] Epoch: [4/5][350/625]\tLoss: 0.070616\n", "[INFO][07:33:24]: [Client #1] Epoch: [4/5][360/625]\tLoss: 0.068445\n", "[INFO][07:33:24]: [Client #1] Epoch: [4/5][370/625]\tLoss: 0.146984\n", "[INFO][07:33:24]: [Client #1] Epoch: [4/5][380/625]\tLoss: 0.143329\n", "[INFO][07:33:24]: [Client #1] Epoch: [4/5][390/625]\tLoss: 0.051149\n", "[INFO][07:33:24]: [Client #1] Epoch: [4/5][400/625]\tLoss: 0.001389\n", "[INFO][07:33:24]: [Client #1] Epoch: [4/5][410/625]\tLoss: 0.007951\n", "[INFO][07:33:24]: [Client #1] Epoch: [4/5][420/625]\tLoss: 0.026888\n", "[INFO][07:33:24]: [Client #1] Epoch: [4/5][430/625]\tLoss: 0.028278\n", "[INFO][07:33:25]: [Client #1] Epoch: [4/5][440/625]\tLoss: 0.002375\n", "[INFO][07:33:25]: [Client #1] Epoch: [4/5][450/625]\tLoss: 0.002251\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO][07:33:25]: [Client #1] Epoch: [4/5][460/625]\tLoss: 0.004273\n", "[INFO][07:33:25]: [Client #1] Epoch: [4/5][470/625]\tLoss: 0.081562\n", "[INFO][07:33:25]: [Client #1] Epoch: [4/5][480/625]\tLoss: 0.006120\n", "[INFO][07:33:25]: [Client #1] Epoch: [4/5][490/625]\tLoss: 0.044493\n", "[INFO][07:33:25]: [Client #1] Epoch: [4/5][500/625]\tLoss: 0.052862\n", "[INFO][07:33:25]: [Client #1] Epoch: [4/5][510/625]\tLoss: 0.036691\n", "[INFO][07:33:26]: [Client #1] Epoch: [4/5][520/625]\tLoss: 0.142635\n", "[INFO][07:33:26]: [Client #1] Epoch: [4/5][530/625]\tLoss: 0.116847\n", "[INFO][07:33:26]: [Client #1] Epoch: [4/5][540/625]\tLoss: 0.024555\n", "[INFO][07:33:26]: [Client #1] Epoch: [4/5][550/625]\tLoss: 0.007605\n", "[INFO][07:33:26]: [Client #1] Epoch: [4/5][560/625]\tLoss: 0.004742\n", "[INFO][07:33:26]: [Client #1] Epoch: [4/5][570/625]\tLoss: 0.079763\n", "[INFO][07:33:26]: [Client #1] Epoch: [4/5][580/625]\tLoss: 0.014608\n", "[INFO][07:33:26]: [Client #1] Epoch: [4/5][590/625]\tLoss: 0.291691\n", "[INFO][07:33:26]: [Client #1] Epoch: [4/5][600/625]\tLoss: 0.195737\n", "[INFO][07:33:27]: [Client #1] Epoch: [4/5][610/625]\tLoss: 0.013582\n", "[INFO][07:33:27]: [Client #1] Epoch: [4/5][620/625]\tLoss: 0.100700\n", "[INFO][07:33:27]: [Client #1] Epoch: [5/5][0/625]\tLoss: 0.181657\n", "[INFO][07:33:27]: [Client #1] Epoch: [5/5][10/625]\tLoss: 0.002866\n", "[INFO][07:33:27]: [Client #1] Epoch: [5/5][20/625]\tLoss: 0.007479\n", "[INFO][07:33:27]: [Client #1] Epoch: [5/5][30/625]\tLoss: 0.059038\n", "[INFO][07:33:27]: [Client #1] Epoch: [5/5][40/625]\tLoss: 0.049032\n", "[INFO][07:33:27]: [Client #1] Epoch: [5/5][50/625]\tLoss: 0.010833\n", "[INFO][07:33:28]: [Client #1] Epoch: [5/5][60/625]\tLoss: 0.024053\n", "[INFO][07:33:28]: [Client #1] Epoch: [5/5][70/625]\tLoss: 0.053290\n", "[INFO][07:33:28]: [Client #1] Epoch: [5/5][80/625]\tLoss: 0.007562\n", "[INFO][07:33:28]: [Client #1] Epoch: [5/5][90/625]\tLoss: 0.004333\n", "[INFO][07:33:28]: [Client #1] Epoch: [5/5][100/625]\tLoss: 0.004574\n", "[INFO][07:33:28]: [Client #1] Epoch: [5/5][110/625]\tLoss: 0.090346\n", "[INFO][07:33:28]: [Client #1] Epoch: [5/5][120/625]\tLoss: 0.070685\n", "[INFO][07:33:29]: [Client #1] Epoch: [5/5][130/625]\tLoss: 0.001034\n", "[INFO][07:33:29]: [Client #1] Epoch: [5/5][140/625]\tLoss: 0.006046\n", "[INFO][07:33:29]: [Client #1] Epoch: [5/5][150/625]\tLoss: 0.080132\n", "[INFO][07:33:29]: [Client #1] Epoch: [5/5][160/625]\tLoss: 0.015681\n", "[INFO][07:33:29]: [Client #1] Epoch: [5/5][170/625]\tLoss: 0.019403\n", "[INFO][07:33:29]: [Client #1] Epoch: [5/5][180/625]\tLoss: 0.023508\n", "[INFO][07:33:29]: [Client #1] Epoch: [5/5][190/625]\tLoss: 0.040061\n", "[INFO][07:33:30]: [Client #1] Epoch: [5/5][200/625]\tLoss: 0.010051\n", "[INFO][07:33:30]: [Client #1] Epoch: [5/5][210/625]\tLoss: 0.024317\n", "[INFO][07:33:30]: [Client #1] Epoch: [5/5][220/625]\tLoss: 0.173256\n", "[INFO][07:33:30]: [Client #1] Epoch: [5/5][230/625]\tLoss: 0.013675\n", "[INFO][07:33:30]: [Client #1] Epoch: [5/5][240/625]\tLoss: 0.000478\n", "[INFO][07:33:30]: [Client #1] Epoch: [5/5][250/625]\tLoss: 0.010641\n", "[INFO][07:33:31]: [Client #1] Epoch: [5/5][260/625]\tLoss: 0.001086\n", "[INFO][07:33:31]: [Client #1] Epoch: [5/5][270/625]\tLoss: 0.017098\n", "[INFO][07:33:31]: [Client #1] Epoch: [5/5][280/625]\tLoss: 0.118814\n", "[INFO][07:33:31]: [Client #1] Epoch: [5/5][290/625]\tLoss: 0.009646\n", "[INFO][07:33:31]: [Client #1] Epoch: [5/5][300/625]\tLoss: 0.067745\n", "[INFO][07:33:32]: [Client #1] Epoch: [5/5][310/625]\tLoss: 0.045326\n", "[INFO][07:33:32]: [Client #1] Epoch: [5/5][320/625]\tLoss: 0.043089\n", "[INFO][07:33:32]: [Client #1] Epoch: [5/5][330/625]\tLoss: 0.000797\n", "[INFO][07:33:32]: [Client #1] Epoch: [5/5][340/625]\tLoss: 0.090927\n", "[INFO][07:33:32]: [Client #1] Epoch: [5/5][350/625]\tLoss: 0.014958\n", "[INFO][07:33:33]: [Client #1] Epoch: [5/5][360/625]\tLoss: 0.018300\n", "[INFO][07:33:33]: [Client #1] Epoch: [5/5][370/625]\tLoss: 0.050818\n", "[INFO][07:33:33]: [Client #1] Epoch: [5/5][380/625]\tLoss: 0.145164\n", "[INFO][07:33:33]: [Client #1] Epoch: [5/5][390/625]\tLoss: 0.002854\n", "[INFO][07:33:33]: [Client #1] Epoch: [5/5][400/625]\tLoss: 0.015417\n", "[INFO][07:33:33]: [Client #1] Epoch: [5/5][410/625]\tLoss: 0.103734\n", "[INFO][07:33:33]: [Client #1] Epoch: [5/5][420/625]\tLoss: 0.082064\n", "[INFO][07:33:33]: [Client #1] Epoch: [5/5][430/625]\tLoss: 0.001056\n", "[INFO][07:33:34]: [Client #1] Epoch: [5/5][440/625]\tLoss: 0.174555\n", "[INFO][07:33:34]: [Client #1] Epoch: [5/5][450/625]\tLoss: 0.007573\n", "[INFO][07:33:34]: [Client #1] Epoch: [5/5][460/625]\tLoss: 0.007313\n", "[INFO][07:33:34]: [Client #1] Epoch: [5/5][470/625]\tLoss: 0.258451\n", "[INFO][07:33:34]: [Client #1] Epoch: [5/5][480/625]\tLoss: 0.003587\n", "[INFO][07:33:34]: [Client #1] Epoch: [5/5][490/625]\tLoss: 0.005162\n", "[INFO][07:33:34]: [Client #1] Epoch: [5/5][500/625]\tLoss: 0.016993\n", "[INFO][07:33:34]: [Client #1] Epoch: [5/5][510/625]\tLoss: 0.017624\n", "[INFO][07:33:35]: [Client #1] Epoch: [5/5][520/625]\tLoss: 0.064564\n", "[INFO][07:33:35]: [Client #1] Epoch: [5/5][530/625]\tLoss: 0.000444\n", "[INFO][07:33:35]: [Client #1] Epoch: [5/5][540/625]\tLoss: 0.044001\n", "[INFO][07:33:35]: [Client #1] Epoch: [5/5][550/625]\tLoss: 0.364723\n", "[INFO][07:33:36]: [Client #1] Epoch: [5/5][560/625]\tLoss: 0.615465\n", "[INFO][07:33:36]: [Client #1] Epoch: [5/5][570/625]\tLoss: 0.008052\n", "[INFO][07:33:36]: [Client #1] Epoch: [5/5][580/625]\tLoss: 0.044528\n", "[INFO][07:33:36]: [Client #1] Epoch: [5/5][590/625]\tLoss: 0.079976\n", "[INFO][07:33:36]: [Client #1] Epoch: [5/5][600/625]\tLoss: 0.014838\n", "[INFO][07:33:36]: [Client #1] Epoch: [5/5][610/625]\tLoss: 0.005854\n", "[INFO][07:33:36]: [Client #1] Epoch: [5/5][620/625]\tLoss: 0.026543\n", "[INFO][07:33:37]: [Client #1] Model trained.\n", "[INFO][07:33:37]: [Client #1] Sent 0.24 MB of payload data to the server.\n"]}], "source": ["client = simple.Client()\n", "client.configure()\n", "loop = asyncio.get_event_loop()\n", "loop.run_until_complete(client.start_client())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "abb1bb7cc74d8e0a69f854346f68e9c79602092e8f8792cdeea74894b6fb0780"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.5"}}, "nbformat": 4, "nbformat_minor": 4}