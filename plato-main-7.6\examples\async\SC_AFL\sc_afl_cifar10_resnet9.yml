clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 20

    # SC_AFL特有配置
    max_aggregation_clients: 20

    # Should the clients compute test accuracy locally?
    do_test: false

    # Do we need to simulate the wall-clock time on the clients?
    do_we_simulate_wall_time: false

    # The simulation mode
    simulation: true

    # Should we operate in asynchronous mode?
    asynchronous: true

    # The staleness bound for asynchronous training
    staleness_bound: 5

    # The maximum amount of time for clients to sleep
    sleep_simulation: false

    # Should we simulate client availability?
    availability_simulation: false

    # Should we simulate stragglers?
    speed_simulation: false

    # Should we simulate system heterogeneity?
    system_heterogeneity_simulation: false

    # Should we simulate statistical heterogeneity?
    statistical_heterogeneity_simulation: false

    # The random seed for client sampling
    random_seed: 1

server:
    address: 127.0.0.1
    port: 8000
    simulate_wall_time: false
    do_test: false

    # 使用SCAFL自定义服务器
    type: scafl

    # 异步模式配置
    synchronous: false
    synchronous_mode: false
    staleness_bound: 5

    # 训练间隔配置
    training_interval: 2

    # 聚合策略配置 - SCAFL特有
    aggregation_strategy: "scafl"

    # 缓冲池配置 - SCAFL特有
    buffer_size: 20
    min_clients_for_aggregation: 5
    max_aggregation_clients: 20

    # SCAFL参数
    tau_max: 5
    V: 1.0

data:
    # The training and testing dataset
    datasource: CIFAR10  # 明确指定CIFAR10

    # Number of samples in each partition
    partition_size: 500

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution
    concentration: 0.5

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 50

    # The maximum number of clients running concurrently
    max_concurrency: 20

    # The target accuracy
    target_accuracy: 0.80

    # Number of epoches for local training in each communication round
    epochs: 5

    # Batch size
    batch_size: 32

    # Optimizer
    optimizer: SGD

    # Learning rate
    learning_rate: 0.1

    # Learning rate schedule
    lr_schedule: CosineAnnealingLR

    # Learning rate schedule parameters
    lr_schedule_params:
        T_max: 50

    # The machine learning model
    model_name: resnet_9

# 模型参数配置
parameters:
    model:
        # 自动从数据集推断
        num_classes: 10
        in_channels: 3

    # 优化器配置
    optimizer:
        type: SGD
        lr: 0.1
        momentum: 0.9
        weight_decay: 0.0001

    # 数据路径
    data_path: ./data

algorithm:
    # Aggregation algorithm
    type: fedavg

results:
    # Write the results to a CSV
    types: csv

    # The directory to save results
    result_path: results/

    # The names of the results
    result_prefix: scafl_cifar10

# 日志配置
logging:
    # 日志级别
    level: INFO

    # 日志文件路径
    filename: logs/sc_afl_debug.log

    # 日志格式
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # 是否同时输出到控制台
    console: true
