clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 20

    # Should the clients compute test accuracy locally?
    do_test: true

    # *Should the clients compute test accuracy with global model?(test global model on local dataset)
    do_global_test: true

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 5

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 5

    random_seed: 1

    # (fedasync) the hyperparameter for regularizer
    proximal_term_penalty_constant: 0.005

server:
    # do_test: false
    address: 127.0.0.1
    port: 8002
    ping_timeout: 36000
    ping_interval: 36000

    # Should we simulate the wall-clock time on the server? Useful if max_concurrency is specified
    simulate_wall_time: true

    # Should we operate in sychronous mode?
    synchronous: false

    # (fedbuff)What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 10

    # (FedAsync)
    # staleness_bound: 1000 # FedAsync doesn't have any staleness bound
    # minimum_clients_aggregated: 1
    mixing_hyperparameter: 0.9
    adaptive_mixing: true
    staleness_weighting_function:
        type: Polynomial
        pa: 0.5
        ha: 10
        hb: 5

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 5

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: true

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/cifar100/01
    model_path: models/cifar100/01

    random_seed: 1

data:
    # EMNIST non-iid distribution
    # !include emnist_noniid.yml
    # The training and testing dataset
    datasource: CIFAR100

    # Number of samples in each partition
    partition_size: 300

    # IID or non-IID?
    sampler: noniid
    concentration: 0.1

    # The size of the testset on the server
    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

    # *get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # LeNet-5 model with the basic trainer
    # !include basic_lenet5.yml
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 400

    # The maximum number of clients running concurrently
    max_concurrency: 10

    # The target accuracy
    target_accuracy: 1

    # The machine learning model
    model_name: resnet_9

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD
    lr_scheduler: LambdaLR

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    # LeNet-5 training params
    # !include lenet5_params.yml
    model:
        num_classes: 100
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001
    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    result_path: results/cifar100/01

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std
