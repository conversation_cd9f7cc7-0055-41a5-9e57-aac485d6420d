clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 20

    # Should the clients compute test accuracy locally?
    do_test: true

    # Should the clients compute test accuracy with global model?
    do_global_test: true

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 5

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 5  # EMNIST训练时间较短

    # 网络波动模拟配置 - 启用网络模拟
    network_simulation: true

    # 网络延迟配置 (毫秒) - 模拟移动网络和边缘设备
    network_delay:
        min_delay: 50      # 最小延迟50ms
        max_delay: 2000    # 最大延迟2秒
        distribution: exponential  # 指数分布模拟真实网络

    # 网络丢包率配置
    packet_loss:
        loss_rate: 0.15    # 15%丢包率
        burst_loss: true   # 突发丢包

    # 网络带宽限制
    bandwidth_limit:
        upload_speed: 1024   # 1MB/s上传
        download_speed: 2048 # 2MB/s下载

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8097  # 不同端口避免冲突
    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in synchronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server?
    simulate_wall_time: true

    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 3

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 5

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: true

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/refedscafl/emnist_optimized
    model_path: models/refedscafl/emnist_optimized

    random_seed: 1

data:
    # The training and testing dataset
    datasource: EMNIST

    # Number of samples in each partition
    partition_size: 1128  # 每个客户端1128样本

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution(alpha)
    concentration: 0.1

    # The size of the testset on the server
    testset_size: 1880

    # The random seed for sampling data
    random_seed: 1

    # Get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 500  # 增加训练轮次以确保充分收敛

    # The maximum number of clients running concurrently
    max_concurrency: 3

    # The target accuracy
    target_accuracy: 1  # 目标95%准确度

    # Number of epochs for local training in each communication round
    epochs: 5
    batch_size: 32
    optimizer: SGD
    lr_scheduler: StepLR

    # The machine learning model
    model_name: lenet5

algorithm:
    # Aggregation algorithm
    type: fedavg
    lamda: 1.0

    # RefedSCAFL优化参数 - 学习FADAS的简洁性
    buffer_pool_size: 20
    greedy_selection_size: 5
    tau_max: 3  # 降低陈旧度阈值，学习FADAS

    # 简化权重配置 - 减少复杂性
    success_weight: 0.9  # 增加成功更新权重
    distill_weight: 0.1  # 减少知识蒸馏权重
    rho: 0.95  # 提高历史权重
    communication_threshold: 1.5  # 降低通信阈值

    # 简化自适应权重 - 学习FADAS的参数设置
    enable_adaptive_weights: false  # 暂时关闭复杂的自适应权重
    weight_adaptation_window: 3
    weight_adaptation_threshold: 0.01
    weight_adaptation_step: 0.02
    min_weight_ratio: 0.2

    # 保守的知识蒸馏设置
    enable_knowledge_distillation: true
    distillation_temperature: 2.0  # 降低温度
    distillation_alpha: 0.5  # 减少蒸馏影响

    # 保持SCAFL选择优势
    use_pure_scafl_selection: true
    V: 1.0

    # 新增：学习FADAS的AMSGrad参数
    enable_amsgrad_optimization: true  # 启用AMSGrad优化
    beta1: 0.9  # 一阶动量参数
    beta2: 0.99  # 二阶动量参数
    eps: 0.00000001  # 数值稳定性参数
    global_lr: 0.01  # 全局学习率

parameters:
    model:
        num_classes: 47
        in_channels: 1  # EMNIST是单通道灰度图像

    optimizer:
        lr: 0.01  # 学习FADAS的学习率设置
        momentum: 0.9
        weight_decay: 0.0001  # 适度的正则化

    learning_rate:
        # 学习FADAS的调度策略
        step_size: 30  # 每30轮降低学习率
        gamma: 0.1     # 学习率衰减因子

results:
    result_path: ./results/emnist_optimization

    # Write the following parameter(s) into a CSV (包含网络环境指标)
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, network_latency, network_bandwidth, network_reliability, network_success_rate
