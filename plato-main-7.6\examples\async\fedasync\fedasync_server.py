"""
A federated learning server using FedAsync.

Reference:

<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. "Asynchronous federated optimization,"
in Proc. 12th Annual Workshop on Optimization for Machine Learning (OPT 2020).

https://opt-ml.org/papers/2020/paper_28.pdf
"""
import logging
from collections import OrderedDict
import asyncio
import statistics

from plato.config import Config
from plato.servers import fedavg


class Server(fedavg.Server):
    """A federated learning server using the FedAsync algorithm."""

    def __init__(
        self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None
    ):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )

        # The hyperparameter of FedAsync with a range of (0, 1)
        self.mixing_hyperparam = 1
        # Whether adjust mixing hyperparameter after each round
        self.adaptive_mixing = False
        self.current_round_losses = []
        self.avg_loss = 0.0
        
    def get_logged_items(self) -> dict: #输出逻辑scafl可以复用
        """Get items to be logged by the LogProgressCallback class in a .csv file."""# 基本输出字段
        logged_items = super().get_logged_items()  # 调用父类的方法获取基础字段

        # 确保基本字段存在
        if "round" not in logged_items:
            logged_items["round"] = self.current_round
        if "elapsed_time" not in logged_items:
            logged_items["elapsed_time"] = self.wall_time - self.initial_wall_time
        if "accuracy" not in logged_items:
            logged_items["accuracy"] = getattr(self, 'accuracy', 0.0)

        # Add the average loss to the logged items
        logged_items["loss"] = self.avg_loss 

        # 添加陈旧度相关字段
        if hasattr(self, 'updates') and self.updates:
            staleness_values = []
            for update in self.updates:
                if hasattr(update, 'staleness'):
                    staleness_values.append(update.staleness)
                else:
                    staleness_values.append(0)

            if staleness_values:
                logged_items["avg_staleness"] = sum(staleness_values) / len(staleness_values)
                logged_items["max_staleness"] = max(staleness_values)
                logged_items["min_staleness"] = min(staleness_values)
            else:
                logged_items["avg_staleness"] = 0.0
                logged_items["max_staleness"] = 0.0
                logged_items["min_staleness"] = 0.0
        else:
            logged_items["avg_staleness"] = 0.0
            logged_items["max_staleness"] = 0.0
            logged_items["min_staleness"] = 0.0

        # 添加网络相关字段
        logged_items["network_latency"] = 0.0
        logged_items["network_bandwidth"] = 0.0
        logged_items["network_reliability"] = 1.0
        logged_items["network_success_rate"] = 1.0

        return logged_items
    
    def configure(self) -> None:
        """Configure the mixing hyperparameter for the server, as well as 配置混合参数和其他参数
        other parameters from the configuration file. 
        """
        # 调用父类的configure方法
        super().configure()

        # Configuring the mixing hyperparameter for FedAsync
        self.adaptive_mixing = (
            hasattr(Config().server, "adaptive_mixing") # 检查是否有自适应混合参数
            and Config().server.adaptive_mixing
        )

        if not hasattr(Config().server, "mixing_hyperparameter"):
            logging.warning(
                "FedAsync: Variable mixing hyperparameter is required for the FedAsync server."
            ) # 警告 缺少 mixing 超参数
        else:
            self.mixing_hyperparam = Config().server.mixing_hyperparameter

            if 0 < self.mixing_hyperparam < 1:
                logging.info( # 日志 mixing 超参数设置
                    "FedAsync: Mixing hyperparameter is set to %s.",
                    self.mixing_hyperparam,
                )
            else:
                logging.warning(
                    "FedAsync: Invalid mixing hyperparameter. " # 警告 mixing 超参数无效
                    "The hyperparameter needs to be between 0 and 1 (exclusive)." 
                ) # 警告 mixing 超参数无效

    async def aggregate_weights(self, updates, baseline_weights, weights_received):
        """Process the client reports by aggregating their weights.""" # 聚合客户端权重
        # Calculate the new mixing hyperparameter with client's staleness #利用客户端陈旧度计算新的混合超参
        
        # Reset the list of losses for this round 重置当前轮次的损失列表
        self.current_round_losses = []
        
        # Perform weighted averaging 加权平均
        for i, update in enumerate(updates):
            report = update.report  # 从更新中获取报告
            # Collect loss information if available 收集损失信息
            if hasattr(report, 'final_loss'): 
                client_id = updates[i].client_id  # 获取客户端ID
                loss = report.final_loss  # 获取损失值

                # Store loss for this client
                self.current_round_losses.append(loss)

                logging.info(f"[Server] Client {client_id} reported loss: {loss}")
                
            if self.adaptive_mixing:
                mixing_hyperparam = self.mixing_hyperparam * self._staleness_function(update.staleness)  # 在3种方式中选取一种，计算混合超参数
            else:
                mixing_hyperparam = self.mixing_hyperparam  # 保持不变

            logging.info("[client %s], staleness: %s, mixing hyperparameters: %s",
                         i, update.staleness, mixing_hyperparam)

            baseline_weights = await self.algorithm.aggregate_weights(  # 聚合权重
                baseline_weights, weights_received[i], mixing=mixing_hyperparam) 
            
        # Calculate average loss for this round if we have any losses 计算当前轮次的平均损失
        if self.current_round_losses: 
            self.avg_loss = statistics.mean(self.current_round_losses)  # 计算平均损失
            logging.info(f"[Server] Average loss for round {self.current_round}: {self.avg_loss}")
            
        return baseline_weights  # 返回聚合后的权重

    @staticmethod
    def _staleness_function(staleness) -> float: #3种混合超参的计算方式，看选哪种进行计算
        """Staleness function used to adjust the mixing hyperparameter"""  # 计算 stale 函数
        if hasattr(Config().server, "staleness_weighting_function"):  # 检查是否有 stale 函数
            staleness_func_param = Config().server.staleness_weighting_function  # 获取 stale 函数参数
            func_type = staleness_func_param.type.lower()  # 转换为小写
            if func_type == "constant":  # 常量 stale 函数
                return Server._constant_function()  # 返回常量 stale 函数
            elif func_type == "polynomial":  # 多项式 stale 函数
                a = staleness_func_param.pa  # 获取多项式 stale 函数参数
                return Server._polynomial_function(staleness, a)  # 返回多项式 stale 函数
            elif func_type == "hinge":  # hinge stale 函数
                a = staleness_func_param.ha  # 获取 hinge stale 函数参数
                b = staleness_func_param.hb  # 获取 hinge stale 函数参数
                return Server._hinge_function(staleness, a, b)  # 返回 hinge stale 函数
            else:
                logging.warning(  # 警告 未知 stale 函数类型
                    "FedAsync: Unknown staleness weighting function type. "
                    "Type needs to be constant, polynomial, or hinge."
                )
        else:
            return Server.constant_function()

    @staticmethod
    def _constant_function() -> float:
        """Constant staleness function as proposed in Sec. 5.2, Evaluation Setup."""
        return 1 

    @staticmethod
    def _polynomial_function(staleness, a) -> float:
        """Polynomial staleness function as proposed in Sec. 5.2, Evaluation Setup."""
        return (staleness + 1) ** -a  # 多项式 stale 函数

    @staticmethod
    def _hinge_function(staleness, a, b) -> float:
        """Hinge staleness function as proposed in Sec. 5.2, Evaluation Setup.""" 
        if staleness <= b:
            return 1  # 小于等于 b 时，返回 1
        else:
            return 1 / (a * (staleness - b) + 1)
