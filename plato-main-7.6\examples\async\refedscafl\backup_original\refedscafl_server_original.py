import logging
import os
import random
import sys
import time
import numpy as np
import torch
from datetime import datetime
import asyncio
from logging.handlers import RotatingFileHandler
import csv

from collections import OrderedDict

from plato.config import Config
from plato.servers import fedavg
from plato.models import registry as models
from refedscafl_client import Client
from refedscafl_algorithm import Algorithm
from refedscafl_trainer import Trainer
from refedscafl_result import ResultManager  # 导入结果管理器
from math import log2
from types import SimpleNamespace
from plato.models import lenet5
from plato.datasources import mnist

# 配置日志
def setup_logging():
    """配置日志系统"""
    # 获取根日志记录器
    root_logger = logging.getLogger()
    
    # 检查是否已经配置过日志处理器，避免重复添加
    if root_logger.handlers:
        # 如果已经有处理器，直接返回现有的logger
        return root_logger
    
    # 清除任何现有的处理器（以防万一）
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建日志目录
    log_dir = os.path.join(Config().result_path if hasattr(Config(), 'result_path') else './logs', 'server_logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 设置日志文件名
    log_file = os.path.join(log_dir, f'server_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    # 配置根日志记录器
    root_logger.setLevel(logging.DEBUG)
    
    # 创建文件处理器（带轮转）
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '[%(asctime)s][%(levelname)s][%(filename)s:%(lineno)d] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return root_logger

# 初始化日志
logger = setup_logging()

class Server(fedavg.Server):
    """
    自定义的SCAFL联邦学习服务器类，继承自FedAvg服务器。
    实现了基于SCAFL（Staleness-aware Client-Adaptive Federated Learning）的联邦学习算法。
    主要特点：
    1. 考虑客户端陈旧度（staleness）设置虚拟队列，估计客户端的通信时间，并计算延迟估计值
    2. 自适应客户端选择，根据贪心策略选择客户端，并设置缓冲池大小
    3. 支持知识蒸馏补偿
    4. 动态聚合策略，根据缓冲池大小和客户端的陈旧度设置权重
    """
    def __init__(self, model=None, algorithm=None, trainer=None):
        """初始化服务器"""
        super().__init__(model=model, trainer=trainer)
        logger.info("服务器初始化开始...")
        
        # 初始化基本属性
        self.current_round = 0
        self.selected_clients = []
        self.client_upload_success = {}
        self.distilled_clients = set()
        self.client_updates = {}  # 存储客户端更新
        self.global_weights = None  # 初始化全局权重为None
        
        # 触发客户端相关属性
        self.trigger_clients = []  # 本轮触发聚合的客户端
        self.non_trigger_clients = []  # 本轮非触发聚合的客户端
        
        # 初始化两个缓冲池
        self.success_buffer_pool = []  # 成功上传的客户端模型更新缓冲池
        self.distill_buffer_pool = []  # 蒸馏补偿的模型更新缓冲池
        self.success_buffered_clients = []  # 成功上传的客户端ID列表
        self.distill_buffered_clients = []  # 蒸馏补偿的客户端ID列表
        
        # 获取配置
        config = Config()
        self.total_clients = getattr(config.clients, 'total_clients', 10)
        self.clients_per_round = getattr(config.clients, 'per_round', 2)
        
        # 初始化权重参数
        self.initial_success_weight = getattr(config.algorithm, 'success_weight', 0.7)  # 初始成功上传权重
        self.initial_distill_weight = getattr(config.algorithm, 'distill_weight', 0.3)  # 初始蒸馏补偿权重
        self.success_weight = self.initial_success_weight  # 当前成功上传模型的权重
        self.distill_weight = self.initial_distill_weight  # 当前蒸馏补偿模型的权重

        # 自适应权重调整参数
        self.enable_adaptive_weights = getattr(config.algorithm, 'enable_adaptive_weights', True)  # 是否启用自适应权重
        self.weight_adaptation_window = getattr(config.algorithm, 'weight_adaptation_window', 5)  # 性能评估窗口大小
        self.weight_adaptation_threshold = getattr(config.algorithm, 'weight_adaptation_threshold', 0.02)  # 性能改进阈值
        self.weight_adaptation_step = getattr(config.algorithm, 'weight_adaptation_step', 0.05)  # 权重调整步长
        self.min_weight_ratio = getattr(config.algorithm, 'min_weight_ratio', 0.1)  # 最小权重比例

        # 历史性能跟踪
        self.accuracy_history = []  # 准确率历史
        self.weight_history = []    # 权重历史
        self.last_adaptation_round = 0  # 上次权重调整的轮次
        
        # 初始化贪心选择参数
        self.greedy_selection_size = getattr(config.algorithm, 'greedy_selection_size', 3)  # 贪心选择的客户端数量
        self.buffer_pool_size = getattr(config.algorithm, 'buffer_pool_size', 20)  # 缓冲池大小
        
        # 初始化通信阈值
        self.upload_threshold = getattr(config.algorithm, 'communication_threshold', 1.0)  # 从配置文件读取通信阈值
        self.comm_time_window = []   # 通信时间滑动窗口
        
        # 初始化模型
        if model is None:
            try:
                # 从配置中获取模型参数
                config = Config()
                num_classes = getattr(config.parameters.model, 'num_classes', 10)
                in_channels = getattr(config.parameters.model, 'in_channels', 1)

                self.model = lenet5.Model(num_classes=num_classes, in_channels=in_channels)
                logger.debug("模型创建成功: %s (num_classes=%d, in_channels=%d)",
                           self.model, num_classes, in_channels)
            except Exception as e:
                logger.error("模型初始化失败: %s", str(e))
                raise
        else:
            self.model = model
        
        # 初始化训练器
        if trainer is None:
            logger.debug("训练器初始化前的模型: %s", self.model)
            logger.debug("模型类型: %s", type(self.model))
            self.trainer = Trainer(model=self.model, client_id=None)
            logger.debug("训练器创建成功: %s", self.trainer)
            logger.debug("训练器类型: %s", type(self.trainer))
        else:
            self.trainer = trainer
        
        # 初始化算法
        if algorithm is None:
            try:
                logger.debug("算法初始化前的训练器: %s", self.trainer)
                if self.trainer is None:
                    logger.error("训练器为空，无法初始化算法")
                    raise ValueError("训练器为空")
                self.algorithm = Algorithm(trainer=self.trainer, server=self)
                logger.debug("算法初始化成功: %s", self.algorithm)
                logger.info("服务器算法初始化成功")
            except Exception as e:
                logger.error("服务器算法初始化失败: %s", str(e))
                raise
        else:
            self.algorithm = algorithm
            # 确保算法类有服务器引用
            if hasattr(self.algorithm, 'set_server_reference'):
                self.algorithm.set_server_reference(self)
        
        # 初始化全局模型权重
        self.initialize_global_weights()

        # 4. 初始化其他属性
        self.client_estimated_duration = {}

        # 从配置中获取参数
        config_obj = Config()
        self.K_max = getattr(config_obj.algorithm, 'K_max', 2)  # 最大客户端数
        self.lambda_distill = getattr(config_obj.algorithm, 'lambda_distill', 0.1)  # 蒸馏系数
        self.rho = getattr(config_obj.algorithm, 'rho', 1.0)  # 学习率
        self.eta = getattr(config_obj.algorithm, 'eta', 1.0)  # 动量系数

        logger.info("服务器参数配置：K_max=%d, lambda_distill=%.2f, rho=%.2f, eta=%.2f",
                   self.K_max, self.lambda_distill, self.rho, self.eta)

        # 初始化其他属性
        self.clients = {}
        self.client_states = {}
        self.buffer_pool = []
        self.client_estimated_duration = {}

        # 初始化客户端状态跟踪
        self.candidate_clients = set()
        self.buffer_pool = []

        # 初始化所有客户端
        for client_id in range(self.total_clients):
            client = Client()  # 不传递client_id，让Plato框架处理
            # 手动设置client_id（因为服务器端需要）
            client.client_id = client_id
            self.clients[client_id] = client
            client.set_server_reference(self)
            self.client_states[client_id] = {
                'last_round': 0,
                'is_upload_failed': False,
                'tau_k': 0,
                'beta_k': 0,
                'D_k': 0,
                'Q_k': 1.0
            }
            logger.info("初始化客户端 %d", client_id)

            # 启动客户端的异步训练循环
            asyncio.create_task(client.receive_model())
            
        # 初始化其他属性
        self.client_Hk_comp = {}
        self.client_Hk_comm = {}
        self.client_Hk = {}
        self.d_k_t_dict = {}
        self.aggregation_start_time = time.time()
        
        # SC-AFL 相关属性
        self.global_round = 0
        self.total_rounds = Config().trainer.rounds
        
        # 可配置参数
        self.tau_max = Config().algorithm.tau_max if hasattr(Config().algorithm, 'tau_max') else 6
        
        self.client_staleness = {}
        self.staleness_queue = {}
        self.historical_Ds = []
        self.staleness_history = []
        self.accuracy_log = []
        self.first_reach_time = {0.7: None, 0.8: None, 0.9: None}
        self.start_time = time.time()
        self.training_start_time = time.time()  # 记录训练开始时间
        self.communication_failure_prob = getattr(Config().algorithm, 'communication_failure_prob', 0.1)
        self.failed_clients = []
        self.global_model_cache = None
        self.client_start_time = {}

        # SCAFL相关参数
        self.V = getattr(config_obj.algorithm, 'V', 1.0)  # 延迟权重
        self.client_beta = {}  # 客户端通信成功率
        
        logger.info("服务器参数配置：success_weight=%.2f, distill_weight=%.2f, greedy_selection_size=%d, V=%.2f, tau_max=%d",
                   self.success_weight, self.distill_weight, self.greedy_selection_size, self.V, self.tau_max)

        # 初始化结果管理器 - 存储到refedscafl自己的results目录
        result_base_dir = "./results"  # 存储到refedscafl自己的results目录
        experiment_name = "refedscafl"
        self.result_manager = ResultManager(result_base_dir, experiment_name)

        # 固定分配服务器测试集
        self.testset = mnist.DataSource(client_id=0).get_test_set()

        logger.info("服务器初始化完成")

    def log_weight_shapes(self, context=""):
        """记录当前权重形状，用于调试"""
        if self.global_weights and 'conv1.weight' in self.global_weights:
            conv1_shape = self.global_weights['conv1.weight'].shape
            logger.debug(f"[权重监控]{context} 全局权重 conv1.weight 形状: {conv1_shape}")

        if self.global_model_cache and 'conv1.weight' in self.global_model_cache:
            cache_shape = self.global_model_cache['conv1.weight'].shape
            logger.debug(f"[权重监控]{context} 全局缓存 conv1.weight 形状: {cache_shape}")

        if self.model and hasattr(self.model, 'state_dict'):
            model_shape = self.model.state_dict()['conv1.weight'].shape
            logger.debug(f"[权重监控]{context} 服务器模型 conv1.weight 形状: {model_shape}")

    def register_client(self, client):
        """
        注册客户端到服务器
        参数:
            client: 要注册的客户端实例
        功能:
            1. 将客户端添加到服务器管理的客户端字典中
            2. 初始化客户端状态
            3. 记录客户端注册信息
        """
        if client.client_id is not None:
            self.clients[client.client_id] = client
            self.client_states[client.client_id] = {
                'last_round': 0,
                'is_upload_failed': False
            }
            logger.info("客户端%d已注册", client.client_id)
        else:
            logger.error("尝试注册的客户端ID为None")

    def get_cid(self, cid_obj):
        """从客户端ID对象中提取客户端ID"""
        try:
            if isinstance(cid_obj, SimpleNamespace):
                for key in ['id', 'client_id', 'sid', 'name']:
                    if hasattr(cid_obj, key):
                        cid = getattr(cid_obj, key)
                        if cid is not None:
                            return cid
                # 如果无法从SimpleNamespace获取有效ID，记录详细信息
                logger.error("无法从SimpleNamespace获取有效ID: %s", cid_obj)
                logger.error("SimpleNamespace属性: %s", dir(cid_obj))
                raise ValueError(f"无法从SimpleNamespace获取合法ID: {cid_obj}")
            elif cid_obj is None:
                logger.error("客户端ID对象为None")
                raise ValueError("客户端ID对象为None")
            else:
                return cid_obj
        except Exception as e:
            logger.error("get_cid方法出错，输入对象: %s, 类型: %s, 错误: %s", 
                        cid_obj, type(cid_obj), str(e))
            raise

    def estimate_client_times(self, cid):
        """
        估计客户端的计算和通信时间（更贴近现实无线边缘环境）
        """
        cid = self.get_cid(cid)
        a = 0.5  # 计算时间基线
        mu = 1.0
        param_count = 100000  # 模型参数量
        # 距离分布更广，模拟异构性
        distance = np.random.uniform(0.5, 3.0)  # 单位: km

        # 计算时间估计
        H_comp = a + np.random.exponential(1/mu)
        self.client_Hk_comp[cid] = H_comp

        # 通信参数
        B = np.random.uniform(50e3, 500e3)  # 带宽: 50~500kHz
        P = 10 ** ((10 - 30) / 10)  # 发射功率 (W)
        N0 = np.random.uniform(1e-15, 1e-13)  # 噪声功率谱密度 (W/Hz)
        L = 10 ** (3.5 * np.log10(distance))  # 路径损耗
        SNR = P / (N0 * B * L)  # 信噪比

        # 信道容量
        C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps

        # 模型大小（100K参数，32位）
        model_size = param_count * 32  # bits

        # 基础延迟（如协议、排队、接入等）
        latency = np.random.uniform(0.05, 0.3)  # 50~300ms

        # 通信时间 = 传输时延 + 基础延迟
        H_comm = model_size / C + latency
        # 控制在0.2~2s区间
        H_comm = np.clip(H_comm, 0.2, 2.0)
        self.client_Hk_comm[cid] = H_comm

        # 总时间估计
        H_k = H_comp + H_comm
        self.client_Hk[cid] = H_k
        self.client_estimated_duration[cid] = H_k
        logger.debug("估计客户端%d训练+通信时间: %f秒 (计算: %f秒, 通信: %f秒, 距离: %.2fkm, 带宽: %.0fHz, SNR: %.2e)",
                     cid, H_k, H_comp, H_comm, distance, B, SNR)

    def get_dkt(self, cid):
        """
        基于真实客户端状态的延迟估计函数
        
        计算公式：d_k^t = max{t_k_start + D_k - ∑D(i), 0}
        
        参数:
            cid: 客户端ID
            
        返回:
            float: 客户端当前的延迟估计值
        """
        cid = self.get_cid(cid)
        t = len(self.historical_Ds)

        # 获取客户端开始时间、估计持续时间和陈旧度
        t_k_start = self.client_start_time.get(cid, 0)
        D_k = self.client_estimated_duration.get(cid, 0)
        tau_k = self.client_staleness.get(cid, 0)

        # 计算历史聚合时间总和
        start_idx = max(0, t - tau_k)
        D_sum = sum(self.historical_Ds[start_idx: t]) if self.historical_Ds else 0

        # 计算延迟估计值 ♥
        d_k_t = max(t_k_start + D_k - D_sum, 0)
        
        logger.debug("客户端%d延迟估计: start=%f, duration=%f, tau=%f, D_sum=%f, d_k_t=%f", cid, t_k_start, D_k, tau_k, D_sum, d_k_t)
        
        return d_k_t
    
    def update_comm_threshold(self, H_comm):
        """
        更新通信时间阈值
        参数:
            H_comm: 客户端本次通信时间，如果为None则只更新阈值不添加新样本
        """
        window_size = 20
        # 只有H_comm不为None时才添加到窗口
        if H_comm is not None:
            self.comm_time_window.append(H_comm)
            if len(self.comm_time_window) > window_size:
                self.comm_time_window.pop(0)
                
        # 计算新的阈值，目标：90%成功率（10%失败率）
        if self.comm_time_window:
            # 排序通信时间
            sorted_times = sorted(self.comm_time_window)

            # 使用90%分位数作为阈值，确保90%的客户端能成功上传
            percentile_90 = np.percentile(sorted_times, 90)

            # 为了稳定性，结合均值进行调整
            mean_time = np.mean(sorted_times)

            # 如果90%分位数过于极端，使用均值+标准差的组合
            std_time = np.std(sorted_times)
            fallback_threshold = mean_time + 1.5 * std_time  # 约85-90%覆盖率

            # 选择更合理的阈值
            new_threshold = min(percentile_90, fallback_threshold)

            # 确保阈值不会过低（至少要覆盖80%的客户端）
            percentile_80 = np.percentile(sorted_times, 80)
            new_threshold = max(new_threshold, percentile_80)
            
            # 平滑更新，避免阈值剧烈变化
            if hasattr(self, 'upload_threshold') and self.upload_threshold is not None:
                # 使用指数平滑
                alpha = 0.3  # 平滑系数
                self.upload_threshold = alpha * new_threshold + (1 - alpha) * self.upload_threshold
            else:
                self.upload_threshold = new_threshold
        else:
            # 没有历史记录时使用配置文件中的值
            config = Config()
            self.upload_threshold = getattr(config.algorithm, 'communication_threshold', 2.0)
            
        # 确保阈值在合理范围内，上限提高以支持更高的配置阈值
        self.upload_threshold = np.clip(self.upload_threshold, 0.2, 5.0)
            
        if self.comm_time_window:
            sorted_times = sorted(self.comm_time_window)
            p90 = np.percentile(sorted_times, 90)
            p80 = np.percentile(sorted_times, 80)
            mean_time = np.mean(sorted_times)
            logger.info("[动态阈值] 当前upload_threshold=%.3f (目标90%%成功率) | 均值=%.3f, 90%%分位=%.3f, 80%%分位=%.3f, 窗口大小=%d",
                        self.upload_threshold, mean_time, p90, p80, len(self.comm_time_window))
        else:
            logger.info("[动态阈值] 当前upload_threshold=%.3f (初始配置值)", self.upload_threshold)

    async def clients_processed(self):
        """处理客户端更新"""
        try:
            super().clients_processed()

            start_time = getattr(self, "aggregation_start_time", time.time())
            D_j = time.time() - start_time
            self.historical_Ds.append(D_j)

            if not hasattr(self, 'buffer_pool'):
                self.buffer_pool = []
            if not hasattr(self, 'failed_clients'):
                self.failed_clients = []

            self.client_upload_success.clear()

            # 只保留聚合、状态更新等核心流程
            for update in self.updates:
                try:
                    cid = self.get_cid(update.client_id)
                    if cid not in self.client_start_time:
                        logger.warning("客户端%d的开始时间未记录，使用当前时间", cid)
                        self.client_start_time[cid] = time.time()
                    d_k_t = self.get_dkt(cid)
                    if not hasattr(self, 'd_k_t_dict'):
                        self.d_k_t_dict = {}
                    self.d_k_t_dict[cid] = d_k_t
                    prev_q = self.staleness_queue.get(cid, 0)
                    tau_k = self.client_staleness.get(cid, 0)
                    beta_k = 1
                    new_q = max(prev_q + (tau_k + 1) * (1 - beta_k) - self.tau_max, 0)
                    self.staleness_queue[cid] = new_q
                    logger.debug("客户端%d状态更新 - 延迟估计: %.4f, 陈旧度: %d, 队列长度: %.2f", 
                               cid, d_k_t, tau_k, new_q)
                except Exception as e:
                    logger.error("更新客户端状态时出错: %s", str(e))
                    import traceback
                    logger.error("详细错误信息: %s", traceback.format_exc())
                    continue

            current_staleness = sum(self.d_k_t_dict.values()) / len(self.d_k_t_dict) if self.d_k_t_dict else 0
            self.staleness_history.append(current_staleness)

            if self.should_trigger_aggregation():
                logger.info("满足聚合条件，开始聚合...")
                await self.aggregate_models()
            else:
                logger.debug("当前轮次不满足聚合条件")

            self.client_updates.clear()

        except Exception as e:
            logger.error("处理客户端更新时出错: %s", str(e))
            raise

    async def select_clients(self):
        """选择客户端参与训练"""
        try:
            # 更新所有客户端的陈旧度（在选择前预先计算）
            for client_id in range(self.total_clients):
                if client_id not in self.client_states:
                    self.client_states[client_id] = {'last_round': 0}
                current_round = self.current_round
                last_round = self.client_states[client_id].get('last_round', 0)
                self.client_staleness[client_id] = current_round - last_round
                
                # 同时更新队列长度
                prev_q = self.staleness_queue.get(client_id, 0)
                tau_k = self.client_staleness[client_id]
                beta_k = self.client_beta.get(client_id, 1)
                new_q = max(prev_q + (tau_k + 1) * (1 - beta_k) - self.tau_max, 0)
                self.staleness_queue[client_id] = new_q
                
                # 估计客户端的计算和通信时间
                self.estimate_client_times(client_id)
            
            # 使用贪心算法选择聚合触发的客户端
            available_clients = list(range(self.total_clients))
            client_scores = []
            
            for client_id in available_clients:
                # 获取客户端状态参数
                tau_k = self.client_staleness.get(client_id, 0)  # 陈旧度
                
                # 检查陈旧度是否超过阈值
                if tau_k > self.tau_max:
                    logger.debug(f"客户端{client_id}陈旧度({tau_k})超过阈值({self.tau_max})，降低其选择优先级")
                    # 陈旧度过高，大幅降低分数但仍可能被选中
                    score = -100
                else:
                    Q_k = self.staleness_queue.get(client_id, 1.0)  # 队列长度
                    beta_k = self.client_beta.get(client_id, 0.8)  # 通信成功率
                    D_k = self.client_estimated_duration.get(client_id, 1.0)  # 估计延迟
                    
                    # 优化的SCAFL得分计算
                    # 1. 基础延迟项
                    delay_term = beta_k * self.V * D_k

                    # 2. 队列项
                    queue_term = Q_k * ((tau_k + 1) * (1 - beta_k) - self.tau_max)

                    # 3. 数据质量加权
                    data_quality = self._calculate_data_quality_score(client_id)

                    # 4. 通信效率加权
                    comm_efficiency = self._calculate_communication_efficiency(client_id)

                    # 5. 参与多样性加权
                    participation_diversity = self._calculate_participation_diversity(client_id)

                    # 综合得分（负值，因为我们要最小化原公式）
                    base_score = -(delay_term + queue_term)
                    quality_bonus = 0.2 * data_quality + 0.1 * comm_efficiency + 0.1 * participation_diversity
                    score = base_score + quality_bonus
                
                client_scores.append((score, client_id))
                logger.debug("客户端%d预选得分: %.4f (陈旧度=%d)", 
                           client_id, score, tau_k)
            
            # 按得分降序排序（得分越高越好）
            client_scores.sort(key=lambda x: x[0], reverse=True)
            
            # 选择前clients_per_round个客户端参与训练
            self.selected_clients = [client_id for _, client_id in client_scores[:self.clients_per_round]]
            
            # 将选中的客户端分为触发客户端和非触发客户端
            # 触发客户端：选择得分最高的K个客户端
            self.trigger_clients = self.selected_clients[:self.greedy_selection_size]
            # 非触发客户端：其余选中的客户端
            self.non_trigger_clients = self.selected_clients[self.greedy_selection_size:]
            
            logger.info("已选择%d个客户端参与训练", len(self.selected_clients))
            logger.info("触发客户端(需要全部上传成功才聚合): %s", self.trigger_clients)
            logger.info("非触发客户端: %s", self.non_trigger_clients)
            
            return self.selected_clients
            
        except Exception as e:
            logger.error("选择客户端时出错: %s", str(e))
            self.selected_clients = []
            self.trigger_clients = []
            self.non_trigger_clients = []
            return []
            
    async def send_model_to_client(self, client_id):
        """向客户端发送模型"""
        try:
            # 确保全局权重已初始化
            if self.global_weights is None or len(self.global_weights) == 0:
                logger.error("全局权重为None或空，自动新建模型并初始化权重")
                # 从配置中获取模型参数
                config = Config()
                num_classes = getattr(config.parameters.model, 'num_classes', 10)
                in_channels = getattr(config.parameters.model, 'in_channels', 1)

                self.model = lenet5.Model(num_classes=num_classes, in_channels=in_channels)
                self.global_weights = self.model.state_dict()
                logger.info(f"紧急创建新模型: num_classes={num_classes}, in_channels={in_channels}")
            # 再次检查全局权重
            if self.global_weights is None or len(self.global_weights) == 0:
                logger.error("发送模型到客户端%d失败：全局权重为空", client_id)
                return None
            # 更新全局模型缓存前进行强制验证和修复
            if self.global_weights and 'conv1.weight' in self.global_weights:
                conv1_shape = self.global_weights['conv1.weight'].shape
                expected_channels = getattr(Config().parameters.model, 'in_channels', 1)

                if conv1_shape[1] != expected_channels:
                    logger.warning(f"⚠️ 检测到全局权重形状异常: {conv1_shape}")
                    logger.warning(f"⚠️ 期望通道数: {expected_channels}, 实际通道数: {conv1_shape[1]}")
                    logger.warning("⚠️ 权重形状异常，但保持当前权重以避免训练中断")

                    # 修复：不再强制重新初始化，而是记录警告并继续使用当前权重
                    # 这避免了训练好的权重被重置为随机值，防止准确率回退
                    logger.info("🔧 保持当前全局权重，避免训练进度丢失")
                else:
                    logger.debug(f"✅ 全局权重形状正确: {conv1_shape}")

            # 更新全局模型缓存
            self.log_weight_shapes("[缓存更新前]")
            self.global_model_cache = self.global_weights
            self.log_weight_shapes("[缓存更新后]")
            # 创建响应对象，包含通信阈值
            response = SimpleNamespace()
            response.weights = self.global_weights
            # 新增：下发前全局模型权重摘要日志
            import torch
            all_params = torch.cat([v.flatten() for v in self.global_weights.values()])
            logger.info(f"[全局模型摘要][下发前] 轮次: {getattr(self, 'current_round', 0)} -> 客户端{client_id} | 均值: {all_params.mean():.6f}, 最大: {all_params.max():.6f}, 最小: {all_params.min():.6f}")
            logger.info("成功发送模型到客户端%d，当前通信阈值: %.6f秒", client_id, self.upload_threshold)
            return response
        except Exception as e:
            logger.error("发送模型到客户端%d时出错: %s", client_id, str(e))
            return None
            
    async def process_client_update(self, client_id, report, weights):
        """处理客户端更新"""
        try:
            comm_time = self.client_Hk_comm.get(client_id, None)
            if comm_time is None:
                base_time = 0.5
                noise = np.random.normal(0, base_time * 0.5)
                comm_time = max(base_time + noise, 0.1)
            
            # 不管是否成功上传，都更新通信时间阈值
            self.update_comm_threshold(comm_time)
            
            should_upload = comm_time <= self.upload_threshold
            if should_upload:
                logger.info("客户端 %d 通信时间(H_comm)：%.2f <= %.2f，可以成功上传", client_id, comm_time, self.upload_threshold)
            else:
                logger.info("客户端 %d 通信时间(H_comm)：%.2f > %.2f，上传失败，需要进行蒸馏补偿", client_id, comm_time, self.upload_threshold)
            if should_upload:
                self.client_upload_success[client_id] = True
                update_record = {
                    'client_id': client_id,
                    'weights': weights,
                    'report': report,
                    'is_distilled': False
                }
                self.success_buffer_pool.append(update_record)
                if client_id not in self.success_buffered_clients:
                    self.success_buffered_clients.append(client_id)
                logger.info("客户端 %d 的更新已加入成功缓冲池", client_id)
                
                # 检查是否为触发客户端，并更新进度
                if client_id in self.trigger_clients:
                    trigger_success_count = len([cid for cid in self.trigger_clients if cid in self.success_buffered_clients])
                    logger.info("触发客户端 %d 已成功上传 (%d/%d)", 
                               client_id, trigger_success_count, len(self.trigger_clients))
                    if trigger_success_count == len(self.trigger_clients):
                        logger.info("所有触发客户端均已成功上传，可以触发聚合")
            else:
                self.client_upload_success[client_id] = False
                logger.warning("客户端 %d 上传失败 | 通信时间(H_comm): %f秒 > 阈值: %f秒", client_id, comm_time, self.upload_threshold)
                # 健壮性增强：检查 global_model_cache 和 client
                client = self.clients.get(client_id)
                if client is None:
                    logger.error("客户端 %d 蒸馏补偿失败：client为None", client_id)
                elif self.global_model_cache is None:
                    logger.error("客户端 %d 蒸馏补偿失败：global_model_cache为None", client_id)
                else:
                    try:
                        pseudo_gradient = client.distill_pseudo_gradient(self.global_model_cache)
                        if pseudo_gradient is not None:
                            logger.info("客户端 %d 生成伪梯度成功，类型: %s, shape: %s, 均值: %.6f", client_id, type(pseudo_gradient).__name__, str(getattr(pseudo_gradient, 'shape', '无')), float(getattr(pseudo_gradient, 'mean', lambda: float('nan'))() if hasattr(pseudo_gradient, 'mean') else float('nan')))
                            distill_record = {
                                'client_id': client_id,
                                'weights': pseudo_gradient,
                                'report': report,
                                'is_distilled': True
                            }
                            self.distill_buffer_pool.append(distill_record)
                            if client_id not in self.distill_buffered_clients:
                                self.distill_buffered_clients.append(client_id)
                            logger.info("客户端 %d 蒸馏伪梯度已加入蒸馏缓冲池", client_id)
                        else:
                            logger.warning("客户端 %d 生成伪梯度失败，返回值为None", client_id)
                    except Exception as e:
                        logger.error("客户端 %d 蒸馏补偿失败: %s (global_model_cache类型: %s, client类型: %s)", client_id, str(e), type(self.global_model_cache).__name__, type(client).__name__)
            
            logger.info("[异步统计] 当前已成功上传: %d个, 失败: %d个, 成功ID: %s", 
                      len(self.success_buffered_clients), 
                      list(self.client_upload_success.values()).count(False), 
                      self.success_buffered_clients)
                      
            # 检查是否可以触发聚合
            if self.should_trigger_aggregation():
                logger.info("满足聚合条件，开始聚合...")
                await self.aggregate_models()
                
            return True
        except Exception as e:
            logger.error("处理客户端 %d 更新时出错: %s", client_id, str(e))
            return False

    async def start(self):
        """启动服务器并开始第一轮训练"""
        try:
            logger.info("[Server #%d] 启动中...", os.getpid())
            
            # 确保全局权重已初始化
            if self.global_weights is None:
                self.initialize_global_weights()
                
            # 记录训练开始时间
            self.training_start_time = time.time()
            
            # 设置服务器状态为"选择客户端"，准备第一轮训练
            self.server_state = "选择客户端"
            
            # 启动事件循环
            asyncio.create_task(self.run())
            
            # 调用父类的start方法启动网络监听
            await super().start()
            
        except Exception as e:
            logger.error("服务器启动失败: %s", str(e))
            raise

    def all_clients_processed(self):
        """检查是否所有客户端都已经处理完毕（成功上传或蒸馏补偿）"""
        total_processed = len(self.success_buffered_clients) + len(self.distill_buffered_clients)
        return total_processed >= self.total_clients

    def should_reset_client_states(self):
        """
        判断是否应该重置客户端状态
        修复：更保守的重置策略，避免频繁清空训练状态导致准确率回退
        """
        total_buffered = len(self.success_buffered_clients) + len(self.distill_buffered_clients)

        # 如果没有缓冲的客户端，不需要重置
        if total_buffered == 0:
            return False

        # 初始化重置相关变量
        if not hasattr(self, 'last_reset_time'):
            self.last_reset_time = time.time()
        if not hasattr(self, 'last_aggregation_time'):
            self.last_aggregation_time = time.time()

        # 策略1：只有当缓冲池接近满载且长时间无聚合时才重置
        if total_buffered >= self.total_clients * 0.8:  # 80%的客户端都在缓冲池中
            time_since_agg = time.time() - self.last_aggregation_time
            if time_since_agg > 60:  # 60秒没有聚合才重置
                logger.info("长时间无聚合且缓冲池接近满载 (%.1f秒)，重置状态", time_since_agg)
                self.last_reset_time = time.time()
                return True

        # 策略2：定期重置，但间隔更长（每120秒）
        time_since_reset = time.time() - self.last_reset_time
        if time_since_reset > 120:  # 2分钟重置一次，避免过于频繁
            logger.info("定期重置客户端状态 (%.1f秒)，允许重新参与", time_since_reset)
            self.last_reset_time = time.time()
            return True

        # 大部分情况下不重置，保持训练连续性
        return False

    def update_accuracy_history(self, accuracy):
        """更新准确率历史记录"""
        self.accuracy_history.append({
            'round': self.current_round,
            'accuracy': accuracy,
            'success_weight': self.success_weight,
            'distill_weight': self.distill_weight
        })

        # 保持历史记录在合理范围内
        if len(self.accuracy_history) > 20:
            self.accuracy_history = self.accuracy_history[-20:]

    def calculate_performance_trend(self):
        """计算最近几轮的性能趋势"""
        if len(self.accuracy_history) < self.weight_adaptation_window:
            return 0.0  # 数据不足，无法计算趋势

        # 获取最近的性能数据
        recent_data = self.accuracy_history[-self.weight_adaptation_window:]

        # 计算线性趋势（简单的斜率计算）
        x = list(range(len(recent_data)))
        y = [data['accuracy'] for data in recent_data]

        # 计算斜率 (简单线性回归)
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] * x[i] for i in range(n))

        if n * sum_x2 - sum_x * sum_x == 0:
            return 0.0

        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        return slope

    def adapt_weights_based_on_performance(self):
        """基于历史性能自适应调整权重"""
        if not self.enable_adaptive_weights:
            return

        # 检查是否需要调整权重
        rounds_since_adaptation = self.current_round - self.last_adaptation_round
        if rounds_since_adaptation < self.weight_adaptation_window:
            return  # 还没到调整时间

        if len(self.accuracy_history) < self.weight_adaptation_window:
            return  # 历史数据不足

        # 计算性能趋势
        trend = self.calculate_performance_trend()

        logger.info("🔧 自适应权重调整分析:")
        logger.info("   - 当前权重: success=%.3f, distill=%.3f", self.success_weight, self.distill_weight)
        logger.info("   - 性能趋势: %.6f", trend)
        logger.info("   - 调整阈值: %.6f", self.weight_adaptation_threshold)

        # 记录当前权重配置
        self.weight_history.append({
            'round': self.current_round,
            'success_weight': self.success_weight,
            'distill_weight': self.distill_weight,
            'trend': trend
        })

        # 根据趋势调整权重
        if trend < -self.weight_adaptation_threshold:
            # 性能下降，尝试调整权重策略
            self._adjust_weights_for_poor_performance()
        elif trend > self.weight_adaptation_threshold:
            # 性能良好，可以微调优化
            self._adjust_weights_for_good_performance()
        else:
            # 性能稳定，保持当前权重
            logger.info("   - 决策: 性能稳定，保持当前权重")

        self.last_adaptation_round = self.current_round

    def _adjust_weights_for_poor_performance(self):
        """性能下降时的权重调整策略"""
        # 策略1: 如果当前偏向成功上传，尝试增加蒸馏补偿权重
        if self.success_weight > 0.6:
            new_distill_weight = min(0.5, self.distill_weight + self.weight_adaptation_step)
            new_success_weight = 1.0 - new_distill_weight

            logger.info("   - 决策: 性能下降，增加蒸馏补偿权重")
            logger.info("   - 调整: success %.3f→%.3f, distill %.3f→%.3f",
                       self.success_weight, new_success_weight,
                       self.distill_weight, new_distill_weight)

            self.success_weight = new_success_weight
            self.distill_weight = new_distill_weight

        # 策略2: 如果当前偏向蒸馏补偿，尝试增加成功上传权重
        elif self.distill_weight > 0.4:
            new_success_weight = min(0.8, self.success_weight + self.weight_adaptation_step)
            new_distill_weight = 1.0 - new_success_weight

            logger.info("   - 决策: 性能下降，增加成功上传权重")
            logger.info("   - 调整: success %.3f→%.3f, distill %.3f→%.3f",
                       self.success_weight, new_success_weight,
                       self.distill_weight, new_distill_weight)

            self.success_weight = new_success_weight
            self.distill_weight = new_distill_weight

        # 策略3: 如果权重已经比较平衡，尝试回到初始配置
        else:
            logger.info("   - 决策: 性能下降且权重平衡，回到初始配置")
            logger.info("   - 调整: success %.3f→%.3f, distill %.3f→%.3f",
                       self.success_weight, self.initial_success_weight,
                       self.distill_weight, self.initial_distill_weight)

            self.success_weight = self.initial_success_weight
            self.distill_weight = self.initial_distill_weight

    def _adjust_weights_for_good_performance(self):
        """性能良好时的权重调整策略"""
        # 策略1: 性能良好时，可以尝试微调优化
        # 检查当前配置是否接近最优

        # 如果当前权重偏离初始配置较远，可以尝试小幅调整
        success_diff = abs(self.success_weight - self.initial_success_weight)
        distill_diff = abs(self.distill_weight - self.initial_distill_weight)

        if success_diff > 0.1 or distill_diff > 0.1:
            # 当前配置偏离初始配置较远，尝试向初始配置靠拢
            adjustment_factor = 0.5  # 调整因子
            new_success_weight = self.success_weight + (self.initial_success_weight - self.success_weight) * adjustment_factor * self.weight_adaptation_step
            new_distill_weight = 1.0 - new_success_weight

            # 确保权重在合理范围内
            new_success_weight = max(self.min_weight_ratio, min(1.0 - self.min_weight_ratio, new_success_weight))
            new_distill_weight = 1.0 - new_success_weight

            logger.info("   - 决策: 性能良好，向初始配置微调")
            logger.info("   - 调整: success %.3f→%.3f, distill %.3f→%.3f",
                       self.success_weight, new_success_weight,
                       self.distill_weight, new_distill_weight)

            self.success_weight = new_success_weight
            self.distill_weight = new_distill_weight
        else:
            # 当前配置接近初始配置且性能良好，保持不变
            logger.info("   - 决策: 性能良好且配置合理，保持当前权重")

    def get_adaptive_weights_info(self):
        """获取自适应权重的详细信息"""
        info = {
            'current_weights': {
                'success_weight': self.success_weight,
                'distill_weight': self.distill_weight
            },
            'initial_weights': {
                'success_weight': self.initial_success_weight,
                'distill_weight': self.initial_distill_weight
            },
            'adaptation_enabled': self.enable_adaptive_weights,
            'adaptation_window': self.weight_adaptation_window,
            'last_adaptation_round': self.last_adaptation_round,
            'accuracy_history_length': len(self.accuracy_history),
            'weight_history_length': len(self.weight_history)
        }

        if len(self.accuracy_history) >= self.weight_adaptation_window:
            info['current_trend'] = self.calculate_performance_trend()
            info['recent_accuracy'] = [data['accuracy'] for data in self.accuracy_history[-5:]]

        return info

    async def run(self):
        """运行服务器训练循环"""
        try:
            logger.info("[Server #%d] 开始训练，共有 %d 个客户端，每轮选择 %d 个客户端参与训练，其中 %d 个作为触发客户端", 
                       os.getpid(), self.total_clients, self.clients_per_round, self.greedy_selection_size)
            logger.info("触发客户端机制：只有当所有触发客户端均成功上传后才进行聚合")
            
            # 设置训练间隔（秒）
            training_interval = 5
            
            while True:
                # 执行一轮训练
                await self.train_round()
                
                # 等待一段时间再进行下一轮训练
                logger.info(f"等待 {training_interval} 秒后尝试下一轮训练...")
                await asyncio.sleep(training_interval)
                
        except KeyboardInterrupt:
            logger.info("检测到键盘中断，停止训练...")
            # 输出训练结果摘要
            if hasattr(self, 'result_manager'):
                self.result_manager.close()
            return
        except Exception as e:
            logger.error("服务器运行出错: %s", str(e))
            raise

    async def train_round(self):
        """执行一轮训练"""
        try:
            # 确保全局权重已初始化
            if self.global_weights is None:
                self.initialize_global_weights()

            # 1. 清理本轮开始前的状态 - 修复：允许客户端重新参与
            currently_training_clients = set()

            # 修复：每轮开始时清理客户端状态，允许重新参与
            # 简化策略：如果缓冲池中有客户端但没有新的训练活动，就清理状态
            total_buffered = len(self.success_buffered_clients) + len(self.distill_buffered_clients)
            if total_buffered > 0:
                # 检查是否应该重置状态
                if self.should_reset_client_states():
                    logger.info("清理客户端状态，允许重新参与训练 (当前缓冲: 成功=%d, 蒸馏=%d)",
                               len(self.success_buffered_clients), len(self.distill_buffered_clients))
                    self.success_buffered_clients.clear()
                    self.distill_buffered_clients.clear()
                    self.success_buffer_pool.clear()
                    self.distill_buffer_pool.clear()

            # 2. 选择参与训练的客户端（包括触发客户端和非触发客户端）
            selected_clients = await self.select_clients()
            if not selected_clients:
                logger.warning("没有可用的客户端，等待下一轮")
                return
            
            # 3. 将全局模型下发给这些客户端
            for client_id in selected_clients:
                # 跳过已经在成功缓冲池中的客户端
                if client_id in self.success_buffered_clients:
                    logger.info("客户端%d已经成功上传，本轮不再训练", client_id)
                    continue
                    
                # 跳过已经在蒸馏缓冲池中的客户端
                if client_id in self.distill_buffered_clients:
                    logger.info("客户端%d已经进行过蒸馏补偿，本轮不再训练", client_id)
                    continue
                
                response = await self.send_model_to_client(client_id)
                if response is None:
                    logger.error("向客户端%d发送模型失败", client_id)
                    continue
                
                # 4. 记录每个客户端开始训练的时间
                self.client_start_time[client_id] = time.time()
                logger.info("客户端%d开始训练，开始时间: %f", client_id, self.client_start_time[client_id])
                
                # 5. 估算每个客户端的通信时间
                self.estimate_client_times(client_id)
                
                # 添加到当前训练客户端集合
                currently_training_clients.add(client_id)
            
            # 6. 记录本轮正在训练的客户端
            if currently_training_clients:
                logger.info("本轮发起训练的客户端: %s", list(currently_training_clients))
                
                # 7. 等待客户端上传更新，处理会在process_client_update中进行
                # 注意：处理会异步发生，不需要在这里等待
                # 一旦所有触发客户端上传成功，should_trigger_aggregation会返回True
                # 然后在process_client_update中调用aggregate_models
            else:
                logger.warning("本轮没有客户端发起训练，可能所有客户端都已成功上传或蒸馏补偿")
            
        except Exception as e:
            logger.error("训练轮次执行失败: %s", str(e))
            raise
    
    def log_accuracy(self):
        """
        记录训练准确率
        功能:
            1. 记录当前轮次的准确率
            2. 计算平均准确率
            3. 记录达到目标准确率的时间
        """
        if hasattr(self, 'accuracy'):
            self.accuracy_log.append(self.accuracy)
            for threshold in self.first_reach_time:
                if self.first_reach_time[threshold] is None and self.accuracy >= threshold:
                    self.first_reach_time[threshold] = time.time() - self.start_time
            
            avg_last_10 = sum(self.accuracy_log[-10:]) / min(10, len(self.accuracy_log))
            best_acc = max(self.accuracy_log)
            logger.info("\n【第%d轮结束】\n当前准确率: %f", self.global_round, self.accuracy)
            logger.info("过去10轮平均准确率: %f, 最佳准确率: %f", avg_last_10, best_acc)

    def should_trigger_aggregation(self):
        """
        检查是否应该触发聚合
        修复：更灵活的聚合触发条件，避免过度等待导致训练停滞
        """
        success_count = len(self.success_buffered_clients)
        distill_count = len(self.distill_buffered_clients)
        total_count = success_count + distill_count

        # 条件1：有足够的成功上传客户端（优先条件）
        if success_count >= self.clients_per_round:
            logger.info("条件1满足：成功上传客户端数量充足 (%d >= %d)", success_count, self.clients_per_round)
            return True

        # 条件2：成功上传 + 蒸馏补偿达到阈值
        if total_count >= self.clients_per_round:
            logger.info("条件2满足：总更新数量充足 (%d >= %d)", total_count, self.clients_per_round)
            return True

        # 条件3：有一定数量的更新且等待时间过长（避免无限等待）
        if total_count >= max(2, self.clients_per_round // 2):
            # 记录第一个更新的时间
            if not hasattr(self, 'first_update_time'):
                self.first_update_time = time.time()

            wait_time = time.time() - self.first_update_time
            if wait_time > 30:  # 等待30秒后强制聚合
                logger.info("条件3满足：等待时间过长 (%.1f秒)，强制触发聚合", wait_time)
                return True

        # 条件4：检查原始的触发客户端逻辑（作为备用）
        if self.trigger_clients:
            trigger_success_count = sum(1 for cid in self.trigger_clients if cid in self.success_buffered_clients)
            trigger_distill_count = sum(1 for cid in self.trigger_clients if cid in self.distill_buffered_clients)
            trigger_completed = trigger_success_count + trigger_distill_count

            if trigger_completed == len(self.trigger_clients):
                logger.info("条件4满足：所有触发客户端已完成 (%d/%d)", trigger_completed, len(self.trigger_clients))
                return True

        # 记录当前状态，便于调试
        logger.debug("聚合条件未满足 - 成功:%d, 蒸馏:%d, 总计:%d, 需要:%d",
                    success_count, distill_count, total_count, self.clients_per_round)
        return False

    async def process_client_reports(self, client_id, report, weights):
        """
        处理客户端报告和模型权重
        参数:
            client_id: 客户端ID
            report: 训练报告
            weights: 模型权重
        """
        try:
            cid = self.get_cid(client_id)
            
            if cid not in self.client_states:
                logger.error("客户端%d状态未初始化", cid)
                return
                
            # 更新客户端状态
            self.update_client_states(cid, report)
            
            # 检查weights有效性
            if weights is None or not isinstance(weights, dict) or len(weights) == 0:
                logger.error(f"客户端{cid}上传的weights无效，已丢弃")
                return
            # 创建更新记录
            update_record = {
                'client_id': cid,
                'weights': weights,
                'report': report,
                'is_distilled': cid in self.distilled_clients
            }
            
            # 根据是否是蒸馏更新，添加到相应的缓冲池
            if update_record['is_distilled']:
                self.distill_buffer_pool.append(update_record)
                if cid not in self.distill_buffered_clients:
                    self.distill_buffered_clients.append(cid)
                logger.info("客户端 %d 的蒸馏补偿更新已加入蒸馏缓冲池", cid)
            else:
                self.success_buffer_pool.append(update_record)
                if cid not in self.success_buffered_clients:
                    self.success_buffered_clients.append(cid)
                logger.info("客户端 %d 的更新已加入成功缓冲池", cid)
            
            # 输出简化的缓冲池状态
            logger.debug("缓冲池状态 - 成功: %d个, 蒸馏: %d个", 
                       len(self.success_buffered_clients), 
                       len(self.distill_buffered_clients))
            
            # 检查是否应该触发聚合
            if self.should_trigger_aggregation():
                logger.info("满足聚合条件，开始聚合...")
                await self.aggregate_models()
            else:
                logger.debug("当前轮次不满足聚合条件")
                
        except Exception as e:
            logger.error("处理客户端报告时出错: %s", str(e))
            # 记录更详细的错误信息
            import traceback
            logger.error("详细错误信息: %s", traceback.format_exc())
            # 确保cid变量存在
            if 'cid' in locals():
                if cid not in self.client_states:
                    self.client_states[cid] = {'is_upload_failed': True}
                else:
                    self.client_states[cid]['is_upload_failed'] = True

    def initialize_global_weights(self):
        """初始化全局模型权重"""
        try:
            if self.global_weights is None or len(self.global_weights) == 0:
                if self.trainer is not None and self.trainer.model is not None:
                    self.global_weights = self.trainer.model.state_dict()
                    logger.info("从训练器模型初始化全局权重")
                elif self.model is not None:
                    self.global_weights = self.model.state_dict()
                    logger.info("从服务器模型初始化全局权重")
                else:
                    logger.error("无法初始化全局权重：模型未定义，自动新建模型")
                    # 从配置中获取模型参数
                    config = Config()
                    num_classes = getattr(config.parameters.model, 'num_classes', 10)
                    in_channels = getattr(config.parameters.model, 'in_channels', 1)

                    self.model = lenet5.Model(num_classes=num_classes, in_channels=in_channels)
                    self.global_weights = self.model.state_dict()
                    logger.info(f"创建新模型: num_classes={num_classes}, in_channels={in_channels}")
            # 验证权重是否为空
            if not self.global_weights:
                logger.error("全局权重为空，自动新建模型")
                # 从配置中获取模型参数
                config = Config()
                num_classes = getattr(config.parameters.model, 'num_classes', 10)
                in_channels = getattr(config.parameters.model, 'in_channels', 1)

                self.model = lenet5.Model(num_classes=num_classes, in_channels=in_channels)
                self.global_weights = self.model.state_dict()
                logger.info(f"验证时创建新模型: num_classes={num_classes}, in_channels={in_channels}")
            logger.info("全局模型权重初始化成功")
        except Exception as e:
            logger.error("初始化全局权重失败: %s", str(e))
            # 不要在这里抛出异常，而是设置一个默认值
            self.global_weights = {}
            logger.warning("设置空的全局权重作为默认值")

    async def aggregate_models(self):
        """
        聚合模型更新
        功能:
            1. 使用贪心选择算法选择最佳客户端
            2. 调用算法类进行模型聚合
            3. 更新全局模型
            4. 清空缓冲池
        """
        try:
            # 先清理上一轮的记录
            client_staleness_records = []
            
            # 记录聚合信息
            logger.info("开始聚合 - 已有成功上传客户端: %d个, 蒸馏客户端: %d个", 
                       len(self.success_buffered_clients), len(self.distill_buffered_clients))
            logger.info("本轮触发聚合的客户端: %s", self.trigger_clients)
            
            # 1. 准备成功上传的客户端更新（不包括蒸馏补偿）
            success_updates = []
            for client_id in self.success_buffered_clients:
                update_record = next((update for update in self.success_buffer_pool 
                                    if update['client_id'] == client_id and not update['is_distilled']), None)
                if update_record:
                    success_updates.append(update_record)
                    
                    # 为成功上传的客户端计算陈旧度
                    client_id = update_record['client_id']
                    last_round = self.client_states[client_id].get('last_round', 0)
                    tau_k = self.current_round - last_round
                    
                    # 记录详细的陈旧度信息
                    client_staleness_records.append((client_id, tau_k))
                    logger.debug(f"成功上传的客户端 {client_id} 陈旧度: {tau_k} (当前轮次:{self.current_round}, 上次轮次:{last_round})")
                    
                    # 更新客户端最后成功参与的轮次（确保下次计算陈旧度时使用正确的值）
                    self.client_states[client_id]['last_round'] = self.current_round
            
            # 2. 准备蒸馏补偿的更新
            distill_updates = []
            for client_id in self.distill_buffered_clients:
                update_record = next((update for update in self.distill_buffer_pool 
                                    if update['client_id'] == client_id and update['is_distilled']), None)
                if update_record:
                    distill_updates.append(update_record)
            
            # 3. 记录聚合信息
            logger.info("聚合开始 - 成功上传: %d个客户端, 蒸馏补偿: %d个客户端", 
                       len(success_updates), len(distill_updates))
            
            # 4. 详细记录参与聚合的客户端ID列表
            success_client_ids = [update['client_id'] for update in success_updates]
            logger.info(f"本轮参与聚合的成功上传客户端ID列表: {success_client_ids}")
            
            # 5. 记录蒸馏补偿客户端详细信息
            if distill_updates:
                logger.info("本轮参与聚合的蒸馏补偿客户端ID: %s", [u['client_id'] for u in distill_updates])
                for u in distill_updates:
                    w = u['weights']
                    logger.info("蒸馏客户端 %d 伪梯度类型: %s, shape: %s", 
                               u['client_id'], type(w).__name__, str(getattr(w, 'shape', '无')))
            
            # 6. 执行模型聚合
            self.log_weight_shapes("[聚合前]")
            aggregated_weights = self.algorithm.aggregate_weights(
                success_updates,
                distill_updates,
                self.success_weight,
                self.distill_weight,
                self.rho,
                self.global_weights
            )
            self.log_weight_shapes("[聚合后]")
            if aggregated_weights is None:
                logger.error("模型聚合失败")
                return
                
            # 7. 验证聚合后的权重形状
            if aggregated_weights and 'conv1.weight' in aggregated_weights:
                conv1_shape = aggregated_weights['conv1.weight'].shape
                expected_channels = getattr(Config().parameters.model, 'in_channels', 1)

                if conv1_shape[1] != expected_channels:
                    logger.error(f"🚨 聚合后权重形状错误: {conv1_shape}，期望通道数: {expected_channels}")
                    logger.error("🚨 拒绝使用错误的聚合权重，保持当前全局权重")
                    return  # 直接返回，不更新全局权重
                else:
                    logger.debug(f"✅ 聚合后权重形状正确: {conv1_shape}")

            # 8. 更新全局模型
            if self.global_weights is None:
                self.global_weights = aggregated_weights
            else:
                # 直接使用聚合后的权重，因为新的aggregate_weights方法已经实现了
                # wt = wt-1 + a*正常梯度 + b*蒸馏梯度
                self.global_weights = aggregated_weights

            # 将新的全局权重加载到模型中
            try:
                self.model.load_state_dict(self.global_weights)
                logger.debug("全局权重成功加载到服务器模型")
            except Exception as e:
                logger.error(f"加载全局权重到服务器模型失败: {e}")
                # 如果加载失败，强制重新初始化
                self.initialize_global_weights()
            
            # ========== 更新：聚合后统计与记录 ==========
            # 8. 获取当前轮次并递增
            round_num = self.current_round
            self.current_round += 1  # 递增轮次
            
            # 9. 计算从训练开始到现在的时间（秒）
            elapsed_time = time.time() - self.training_start_time
            
            # 10. 评估全局模型准确率
            accuracy = None
            if hasattr(self, 'testset') and self.testset is not None:
                try:
                    accuracy = self.trainer.test(self.testset)

                    # 🔧 自适应权重调整：更新准确率历史并调整权重
                    if accuracy is not None:
                        self.update_accuracy_history(accuracy)
                        self.adapt_weights_based_on_performance()

                        # 记录自适应权重信息
                        if self.enable_adaptive_weights and self.current_round % 5 == 0:
                            weight_info = self.get_adaptive_weights_info()
                            logger.info("📊 自适应权重状态:")
                            logger.info("   - 当前权重: success=%.3f, distill=%.3f",
                                       weight_info['current_weights']['success_weight'],
                                       weight_info['current_weights']['distill_weight'])
                            if 'current_trend' in weight_info:
                                logger.info("   - 性能趋势: %.6f", weight_info['current_trend'])
                            if 'recent_accuracy' in weight_info:
                                logger.info("   - 最近准确率: %s",
                                           [f"{acc:.3f}" for acc in weight_info['recent_accuracy']])

                except Exception as e:
                    logger.error(f"全局模型评估出错: {str(e)}")
                    accuracy = None
            else:
                logger.warning("服务器未分配测试集，无法评估全局模型准确率")
                accuracy = None
                
            # 11. 计算平均陈旧度 - 只使用成功上传的客户端的陈旧度
            staleness_values = [tau for _, tau in client_staleness_records]
            avg_staleness = sum(staleness_values) / len(staleness_values) if staleness_values else 0
            
            # 12. 使用结果管理器记录本轮结果
            self.result_manager.record_training_stats(round_num, accuracy, client_staleness_records)
                
            # 13. 记录模型权重摘要
            self.result_manager.log_model_summary(self.global_weights)
            
            # ========== 结束 ==========
            # 14. 清空缓冲池
            self.success_buffer_pool.clear()
            self.distill_buffer_pool.clear()
            self.success_buffered_clients.clear()
            self.distill_buffered_clients.clear()

            # 更新聚合时间
            self.last_aggregation_time = time.time()

            # 重置触发客户端列表，为下一轮做准备
            self.trigger_clients = []
            self.non_trigger_clients = []
            
            self.update_comm_threshold(None)
            logger.info("聚合完成，缓冲池和触发客户端记录已重置")

        except Exception as e:
            logger.error("模型聚合失败: %s", str(e))
            raise

    def _calculate_data_quality_score(self, client_id):
        """
        计算客户端数据质量得分
        基于数据量、类别分布等因素
        """
        try:
            # 获取客户端数据信息（如果可用）
            if hasattr(self, 'client_data_info') and client_id in self.client_data_info:
                data_info = self.client_data_info[client_id]
                data_size = data_info.get('size', 600)  # 默认数据量
                class_diversity = data_info.get('class_diversity', 0.5)  # 类别多样性

                # 数据量得分（归一化到[0,1]）
                size_score = min(1.0, data_size / 1000.0)

                # 类别多样性得分
                diversity_score = class_diversity

                # 综合数据质量得分
                quality_score = 0.6 * size_score + 0.4 * diversity_score
            else:
                # 默认得分
                quality_score = 0.5

            return quality_score

        except Exception as e:
            logger.warning(f"计算客户端{client_id}数据质量得分失败: {e}")
            return 0.5

    def _calculate_communication_efficiency(self, client_id):
        """
        计算客户端通信效率得分
        基于历史通信时间和成功率
        """
        try:
            # 获取历史通信数据
            if hasattr(self, 'client_comm_history') and client_id in self.client_comm_history:
                comm_history = self.client_comm_history[client_id]
                avg_comm_time = sum(comm_history) / len(comm_history) if comm_history else 1.0

                # 通信效率得分（通信时间越短得分越高）
                efficiency_score = max(0.1, min(1.0, 2.0 / (1.0 + avg_comm_time)))
            else:
                # 使用当前通信成功率作为代理
                beta_k = self.client_beta.get(client_id, 0.8)
                efficiency_score = beta_k

            return efficiency_score

        except Exception as e:
            logger.warning(f"计算客户端{client_id}通信效率得分失败: {e}")
            return 0.5

    def _calculate_participation_diversity(self, client_id):
        """
        计算客户端参与多样性得分
        避免总是选择相同的客户端
        """
        try:
            # 获取最近参与历史
            if hasattr(self, 'client_participation_history'):
                if client_id not in self.client_participation_history:
                    self.client_participation_history[client_id] = []
            else:
                self.client_participation_history = {client_id: []}

            recent_participation = self.client_participation_history[client_id]

            # 计算最近参与频率（最近10轮）
            recent_rounds = recent_participation[-10:] if len(recent_participation) >= 10 else recent_participation
            participation_rate = len(recent_rounds) / 10.0 if recent_rounds else 0.0

            # 多样性得分（参与率越低得分越高，鼓励多样性）
            diversity_score = max(0.1, 1.0 - participation_rate)

            return diversity_score

        except Exception as e:
            logger.warning(f"计算客户端{client_id}参与多样性得分失败: {e}")
            return 0.5

    def update_client_states(self, client_id, report):
        """
        更新客户端状态
        参数:
            client_id: 客户端ID
            report: 训练报告
        """
        try:
            # 确保客户端状态字典已初始化
            if client_id not in self.client_states:
                self.client_states[client_id] = {
                    'last_round': 0,
                    'timestamp': time.time(),
                    'is_upload_failed': False
                }
            
            # 注意：不重复计算陈旧度，因为已在选择客户端时预先计算
            # 仅更新客户端最后一次成功参与的轮次记录
            
            # 更新通信成功率
            if hasattr(report, 'comm_success'):
                self.client_beta[client_id] = report.comm_success
            
            # 更新客户端状态
            self.client_states[client_id] = {
                'last_round': self.current_round,  # 更新最后一次成功的轮次
                'timestamp': time.time(),
                'is_upload_failed': False
            }
            
            logger.debug("客户端%d状态已更新：最后成功轮次=%d, 通信成功率=%.2f",
                        client_id, self.current_round,
                        self.client_beta.get(client_id, 0))
            
        except Exception as e:
            logger.error("更新客户端%d状态时出错: %s", client_id, str(e))

    async def receive_distill_update(self, client_id, pseudo_gradient):
        """
        接收客户端上传的伪梯度并加入蒸馏缓冲池
        
        参数：
            client_id: 客户端ID
            pseudo_gradient: 客户端通过蒸馏生成的梯度（不是模型权重）
        """
        try:
            # 创建伪训练报告
            distill_report = SimpleNamespace(
                client_id=client_id,
                num_samples=600,  # 默认值
                accuracy=0.0,
                training_time=0.0,
                upload_start_time=time.time(),
                is_distilled=True
            )
            
            # 确保伪梯度在添加到缓冲池前转移到正确的设备上
            if self.global_weights is not None and pseudo_gradient is not None:
                device = next(iter(self.global_weights.values())).device
                for key in pseudo_gradient:
                    if isinstance(pseudo_gradient[key], torch.Tensor):
                        pseudo_gradient[key] = pseudo_gradient[key].to(device)
            
            distill_record = {
                'client_id': client_id,
                'weights': pseudo_gradient,  # 注意：这里存储的是梯度，而不是权重，但变量名保持一致以兼容现有代码
                'report': distill_report,
                'is_distilled': True
            }
            self.distill_buffer_pool.append(distill_record)
            if client_id not in self.distill_buffered_clients:
                self.distill_buffered_clients.append(client_id)
            logger.info("收到客户端 %d 的蒸馏伪梯度，已加入蒸馏缓冲池", client_id)
        except Exception as e:
            logger.error("接收客户端 %d 蒸馏伪梯度时出错: %s", client_id, str(e))

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        try:
            if hasattr(self, 'result_manager'):
                self.result_manager.close()
                logger.info("结果文件已保存")
        except Exception as e:
            logger.error(f"关闭结果文件时出错: {str(e)}")