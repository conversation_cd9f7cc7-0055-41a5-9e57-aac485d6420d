clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 40

    # Should the clients compute test accuracy locally?
    do_test: true

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8000
    do_test: false
    random_seed: 1

    checkpoint_path: models/fedrep/cifar10
    model_path: models/fedrep/cifar10

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition
    partition_size: 600

    # IID or non-IID?
    sampler: noniid

    testset_sampler: noniid

    # The concentration parameter for the Dirichlet distribution
    concentration: 1

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 100

    # The maximum number of clients running concurrently
    max_concurrency: 7

    # The target accuracy
    target_accuracy: 0.98

    # Number of epochs for local training in each communication round
    epochs: 10
    batch_size: 10
    optimizer: SGD

    # Number of epoches for head optimization
    local_epochs: 9

    # The machine learning model
    model_name: vgg_16
    num_classes: 10

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    optimizer:
        lr: 0.03
        momentum: 0.0 # learning rate is fixed as in Appendix C.2
        weight_decay: 0.0
