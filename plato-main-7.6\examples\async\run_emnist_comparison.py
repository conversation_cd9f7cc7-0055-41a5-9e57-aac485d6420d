#!/usr/bin/env python3
"""
联邦学习算法对比实验脚本
对比算法：RefedSCAFL, SCAFL, FADAS, FedAC, FedBuff, FedAsync
数据集：EMNIST
模型：LeNet5
"""

import os
import sys
import subprocess
import time
import json
import pandas as pd
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../"))
sys.path.insert(0, project_root)

class FederatedLearningComparison:
    def __init__(self):
        self.algorithms = {
            'refedscafl': {
                'path': 'examples/async/refedscafl',
                'config': 'refedscafl_EMNIST_lenet5_optimized.yml',
                'script': 'refedscafl.py',
                'port': 8097
            },
            'scafl': {
                'path': 'examples/async/SC_AFL',
                'config': 'sc_afl_emnist_lenet5_with_network.yml',
                'script': 'sc_afl.py',
                'port': 8078
            },
            'fadas': {
                'path': 'examples/async/fadas',
                'config': 'fadas_EMNIST_lenet5_alpha0.1.yml',
                'script': 'fadas.py',
                'port': 8007
            },
            'fedac': {
                'path': 'examples/async/fedac',
                'config': 'fedac_EMNIST_lenet5_alpha0.1.yml',
                'script': 'fedac.py',
                'port': 8005
            },
            'fedbuff': {
                'path': 'examples/async/fedbuff',
                'config': 'fedbuff_EMNIST_lenet5_alpha0.1.yml',
                'script': 'fedbuff.py',
                'port': 8003
            },
            'fedasync': {
                'path': 'examples/async/fedasync',
                'config': 'fedasync_EMNIST_lenet5_alpha0.1.yml',
                'script': 'fedasync.py',
                'port': 8002
            }
        }
        
        self.results_dir = Path(current_dir) / "comparison_results"
        self.results_dir.mkdir(exist_ok=True)
        
        self.experiment_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def check_config_files(self):
        """检查所有配置文件是否存在"""
        print("检查配置文件...")
        missing_configs = []
        
        for alg_name, alg_info in self.algorithms.items():
            config_path = Path(project_root) / alg_info['path'] / alg_info['config']
            if not config_path.exists():
                missing_configs.append(f"{alg_name}: {config_path}")
                print(f"❌ 缺失: {config_path}")
            else:
                print(f"✅ 存在: {config_path}")
        
        if missing_configs:
            print(f"\n发现 {len(missing_configs)} 个缺失的配置文件:")
            for config in missing_configs:
                print(f"  - {config}")
            return False
        
        print("✅ 所有配置文件检查完成")
        return True
    
    def run_algorithm(self, alg_name, alg_info, timeout=3600):
        """运行单个算法"""
        print(f"\n🚀 开始运行 {alg_name.upper()}...")
        
        # 切换到算法目录
        alg_dir = Path(project_root) / alg_info['path']
        
        # 构建命令
        cmd = [
            sys.executable, alg_info['script'],
            '-c', alg_info['config']
        ]
        
        print(f"工作目录: {alg_dir}")
        print(f"执行命令: {' '.join(cmd)}")
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 运行算法
            result = subprocess.run(
                cmd,
                cwd=alg_dir,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 保存运行日志
            log_file = self.results_dir / f"{alg_name}_{self.experiment_timestamp}.log"
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"算法: {alg_name}\n")
                f.write(f"开始时间: {datetime.fromtimestamp(start_time)}\n")
                f.write(f"结束时间: {datetime.fromtimestamp(end_time)}\n")
                f.write(f"运行时长: {duration:.2f}秒\n")
                f.write(f"返回码: {result.returncode}\n")
                f.write(f"\n--- STDOUT ---\n{result.stdout}\n")
                f.write(f"\n--- STDERR ---\n{result.stderr}\n")
            
            if result.returncode == 0:
                print(f"✅ {alg_name.upper()} 运行成功 (耗时: {duration:.2f}秒)")
                return True, duration, None
            else:
                print(f"❌ {alg_name.upper()} 运行失败 (返回码: {result.returncode})")
                print(f"错误信息: {result.stderr[:500]}...")
                return False, duration, result.stderr
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {alg_name.upper()} 运行超时 ({timeout}秒)")
            return False, timeout, "运行超时"
        except Exception as e:
            print(f"💥 {alg_name.upper()} 运行异常: {str(e)}")
            return False, 0, str(e)
    
    def collect_results(self):
        """收集所有算法的结果"""
        print("\n📊 收集实验结果...")
        
        results_summary = []
        
        for alg_name, alg_info in self.algorithms.items():
            # 查找结果文件
            alg_dir = Path(project_root) / alg_info['path']
            
            # 可能的结果路径
            possible_result_paths = [
                alg_dir / "results",
                alg_dir / f"results/{alg_name}",
                alg_dir / "results/emnist",
                alg_dir / "results/emnist/01"
            ]
            
            result_file = None
            for result_path in possible_result_paths:
                if result_path.exists():
                    # 查找最新的CSV文件
                    csv_files = list(result_path.glob("*.csv"))
                    if csv_files:
                        result_file = max(csv_files, key=lambda x: x.stat().st_mtime)
                        break
            
            if result_file:
                print(f"✅ 找到 {alg_name} 结果文件: {result_file}")
                
                # 复制结果文件到对比目录
                dest_file = self.results_dir / f"{alg_name}_emnist_lenet5_{self.experiment_timestamp}.csv"
                try:
                    import shutil
                    shutil.copy2(result_file, dest_file)
                    
                    # 读取结果摘要
                    df = pd.read_csv(result_file)
                    if not df.empty:
                        final_accuracy = df['accuracy'].iloc[-1] if 'accuracy' in df.columns else 'N/A'
                        max_accuracy = df['accuracy'].max() if 'accuracy' in df.columns else 'N/A'
                        total_rounds = len(df)
                        
                        results_summary.append({
                            'algorithm': alg_name,
                            'final_accuracy': final_accuracy,
                            'max_accuracy': max_accuracy,
                            'total_rounds': total_rounds,
                            'result_file': str(dest_file)
                        })
                    
                except Exception as e:
                    print(f"❌ 处理 {alg_name} 结果文件时出错: {e}")
            else:
                print(f"❌ 未找到 {alg_name} 结果文件")
                results_summary.append({
                    'algorithm': alg_name,
                    'final_accuracy': 'N/A',
                    'max_accuracy': 'N/A',
                    'total_rounds': 'N/A',
                    'result_file': 'N/A'
                })
        
        # 保存结果摘要
        summary_df = pd.DataFrame(results_summary)
        summary_file = self.results_dir / f"comparison_summary_{self.experiment_timestamp}.csv"
        summary_df.to_csv(summary_file, index=False)
        
        print(f"\n📋 结果摘要保存到: {summary_file}")
        print("\n🏆 实验结果对比:")
        print(summary_df.to_string(index=False))
        
        return summary_df
    
    def run_comparison(self, algorithms_to_run=None, timeout=3600):
        """运行完整的对比实验"""
        print("🔬 开始联邦学习算法对比实验")
        print(f"实验时间戳: {self.experiment_timestamp}")
        print(f"结果保存目录: {self.results_dir}")
        
        # 检查配置文件
        if not self.check_config_files():
            print("❌ 配置文件检查失败，请先创建缺失的配置文件")
            return False
        
        # 确定要运行的算法
        if algorithms_to_run is None:
            algorithms_to_run = list(self.algorithms.keys())
        
        print(f"\n将运行以下算法: {', '.join(algorithms_to_run)}")
        
        # 运行实验记录
        experiment_log = {
            'timestamp': self.experiment_timestamp,
            'algorithms': algorithms_to_run,
            'results': {}
        }
        
        # 逐个运行算法
        for alg_name in algorithms_to_run:
            if alg_name not in self.algorithms:
                print(f"⚠️  未知算法: {alg_name}")
                continue
            
            alg_info = self.algorithms[alg_name]
            success, duration, error = self.run_algorithm(alg_name, alg_info, timeout)
            
            experiment_log['results'][alg_name] = {
                'success': success,
                'duration': duration,
                'error': error
            }
            
            # 算法间等待时间，避免端口冲突
            if success:
                print(f"⏳ 等待5秒后运行下一个算法...")
                time.sleep(5)
        
        # 保存实验日志
        log_file = self.results_dir / f"experiment_log_{self.experiment_timestamp}.json"
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(experiment_log, f, indent=2, ensure_ascii=False)
        
        # 收集结果
        summary_df = self.collect_results()
        
        print(f"\n🎉 实验完成！详细日志: {log_file}")
        return True

def main():
    """主函数"""
    comparison = FederatedLearningComparison()
    
    # 可以选择运行特定算法，或运行所有算法
    # algorithms_to_run = ['fadas', 'fedac']  # 只运行指定算法
    algorithms_to_run = None  # 运行所有算法
    
    success = comparison.run_comparison(
        algorithms_to_run=algorithms_to_run,
        timeout=1800  # 30分钟超时
    )
    
    if success:
        print("\n✅ 对比实验成功完成")
    else:
        print("\n❌ 对比实验失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
