[INFO][06:55:35]: 日志系统已初始化
[INFO][06:55:35]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_065535.log
[INFO][06:55:35]: 日志级别: INFO
[WARNING][06:55:35]: 无法获取系统信息: No module named 'psutil'
[INFO][06:55:35]: 🚀 ReFedScaFL 训练开始
[INFO][06:55:35]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][06:55:35]: 开始时间: 2025-07-29 06:55:35
[INFO][06:55:35]: [Client None] 基础初始化完成
[INFO][06:55:35]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][06:55:35]: 创建并缓存共享模型
[INFO][06:55:35]: [93m[1m[30488] Logging runtime results to: ./results/refedscafl/comparison_cifar10_alpha01/30488.csv.[0m
[INFO][06:55:35]: [Server #30488] Started training on 100 clients with 20 per round.
[INFO][06:55:35]: 服务器参数配置完成：
[INFO][06:55:35]: - 客户端数量: total=100, per_round=20
[INFO][06:55:35]: - 权重参数: success=0.8, distill=0.2
[INFO][06:55:35]: - SCAFL参数: V=1.0, tau_max=5
[INFO][06:55:35]: 从共享资源模型提取并缓存全局权重
[INFO][06:55:35]: [Server #30488] Configuring the server...
[INFO][06:55:35]: Training: 400 rounds or accuracy above 100.0%

[INFO][06:55:35]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][06:55:35]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][06:55:35]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][06:55:35]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][06:55:35]: Algorithm: fedavg
[INFO][06:55:35]: Data source: CIFAR10
[INFO][06:55:37]: Starting client #1's process.
[INFO][06:55:37]: Starting client #2's process.
[INFO][06:55:37]: Starting client #3's process.
[INFO][06:55:37]: Starting client #4's process.
[INFO][06:55:37]: Starting client #5's process.
[INFO][06:55:37]: Starting client #6's process.
[INFO][06:55:37]: Starting client #7's process.
[INFO][06:55:37]: Starting client #8's process.
[INFO][06:55:37]: Starting client #9's process.
[INFO][06:55:37]: Starting client #10's process.
[INFO][06:55:37]: Setting the random seed for selecting clients: 1
[INFO][06:55:37]: Starting a server at address 127.0.0.1 and port 8092.
[INFO][06:55:52]: [Server #30488] A new client just connected.
[INFO][06:55:52]: [Server #30488] New client with id #1 arrived.
[INFO][06:55:52]: [Server #30488] Client process #36112 registered.
[INFO][06:55:52]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][06:55:52]: [Server #30488] A new client just connected.
[INFO][06:55:52]: [Server #30488] New client with id #6 arrived.
[INFO][06:55:52]: [Server #30488] Client process #39680 registered.
[INFO][06:55:52]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][06:55:52]: [Server #30488] A new client just connected.
[INFO][06:55:52]: [Server #30488] New client with id #2 arrived.
[INFO][06:55:52]: [Server #30488] Client process #25272 registered.
[INFO][06:55:52]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][06:55:52]: [Server #30488] A new client just connected.
[INFO][06:55:52]: [Server #30488] New client with id #7 arrived.
[INFO][06:55:52]: [Server #30488] Client process #7436 registered.
[INFO][06:55:52]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][06:55:53]: [Server #30488] A new client just connected.
[INFO][06:55:53]: [Server #30488] New client with id #3 arrived.
[INFO][06:55:53]: [Server #30488] Client process #27384 registered.
[INFO][06:55:53]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][06:55:53]: [Server #30488] A new client just connected.
[INFO][06:55:53]: [Server #30488] New client with id #10 arrived.
[INFO][06:55:53]: [Server #30488] Client process #17828 registered.
[INFO][06:55:53]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][06:55:53]: [Server #30488] A new client just connected.
[INFO][06:55:53]: [Server #30488] A new client just connected.
[INFO][06:55:53]: [Server #30488] New client with id #4 arrived.
[INFO][06:55:53]: [Server #30488] Client process #33604 registered.
[INFO][06:55:53]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][06:55:53]: [Server #30488] New client with id #8 arrived.
[INFO][06:55:53]: [Server #30488] Client process #32016 registered.
[INFO][06:55:53]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][06:55:53]: [Server #30488] A new client just connected.
[INFO][06:55:53]: [Server #30488] A new client just connected.
[INFO][06:55:53]: [Server #30488] New client with id #9 arrived.
[INFO][06:55:53]: [Server #30488] Client process #8340 registered.
[INFO][06:55:53]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][06:55:53]: [Server #30488] New client with id #5 arrived.
[INFO][06:55:53]: [Server #30488] Client process #35616 registered.
[INFO][06:55:53]: [Server #30488] Starting training.
[INFO][06:55:53]: [93m[1m
[Server #30488] Starting round 1/400.[0m
[INFO][06:55:53]: [Server #30488] Selected clients: [18, 73, 98, 9, 33, 16, 64, 58, 61, 84, 49, 27, 13, 63, 4, 50, 56, 78, 99, 1]
[INFO][06:55:53]: [Server #30488] Selecting client #18 for training.
[INFO][06:55:53]: [Server #30488] Sending the current model to client #18 (simulated).
[INFO][06:55:53]: [Server #30488] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][06:55:53]: [Server #30488] Selecting client #73 for training.
[INFO][06:55:53]: [Server #30488] Sending the current model to client #73 (simulated).
[INFO][06:55:53]: [Server #30488] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][06:55:53]: [Server #30488] Selecting client #98 for training.
[INFO][06:55:53]: [Server #30488] Sending the current model to client #98 (simulated).
[INFO][06:55:53]: [Server #30488] Sending 18.75 MB of payload data to client #98 (simulated).
[INFO][06:55:53]: [Server #30488] Selecting client #9 for training.
[INFO][06:55:53]: [Server #30488] Sending the current model to client #9 (simulated).
[INFO][06:55:53]: [Server #30488] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][06:55:53]: [Server #30488] Selecting client #33 for training.
[INFO][06:55:53]: [Server #30488] Sending the current model to client #33 (simulated).
[INFO][06:55:53]: [Server #30488] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][06:55:53]: [Server #30488] Selecting client #16 for training.
[INFO][06:55:53]: [Server #30488] Sending the current model to client #16 (simulated).
[INFO][06:55:53]: [Server #30488] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][06:55:53]: [Server #30488] Selecting client #64 for training.
[INFO][06:55:53]: [Server #30488] Sending the current model to client #64 (simulated).
[INFO][06:55:53]: [Server #30488] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][06:55:53]: [Server #30488] Selecting client #58 for training.
[INFO][06:55:53]: [Server #30488] Sending the current model to client #58 (simulated).
[INFO][06:55:53]: [Server #30488] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][06:55:53]: [Server #30488] Selecting client #61 for training.
[INFO][06:55:53]: [Server #30488] Sending the current model to client #61 (simulated).
[INFO][06:55:53]: [Server #30488] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][06:55:53]: [Server #30488] Selecting client #84 for training.
[INFO][06:55:53]: [Server #30488] Sending the current model to client #84 (simulated).
[INFO][06:55:53]: [Server #30488] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][06:55:53]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][06:58:52]: [Server #30488] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][06:58:53]: [Server #30488] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][06:58:53]: [Server #30488] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][06:58:53]: [Server #30488] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][06:58:53]: [Server #30488] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][06:58:53]: [Server #30488] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][06:58:53]: [Server #30488] Received 18.75 MB of payload data from client #98 (simulated).
[INFO][06:58:53]: [Server #30488] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][06:58:53]: [Server #30488] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][06:58:53]: [Server #30488] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][06:58:53]: [Server #30488] Selecting client #49 for training.
[INFO][06:58:53]: [Server #30488] Sending the current model to client #49 (simulated).
[INFO][06:58:53]: [Server #30488] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][06:58:53]: [Server #30488] Selecting client #27 for training.
[INFO][06:58:53]: [Server #30488] Sending the current model to client #27 (simulated).
[INFO][06:58:53]: [Server #30488] Sending 18.75 MB of payload data to client #27 (simulated).
[INFO][06:58:53]: [Server #30488] Selecting client #13 for training.
[INFO][06:58:53]: [Server #30488] Sending the current model to client #13 (simulated).
[INFO][06:58:54]: [Server #30488] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][06:58:54]: [Server #30488] Selecting client #63 for training.
[INFO][06:58:54]: [Server #30488] Sending the current model to client #63 (simulated).
[INFO][06:58:54]: [Server #30488] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][06:58:54]: [Server #30488] Selecting client #4 for training.
[INFO][06:58:54]: [Server #30488] Sending the current model to client #4 (simulated).
[INFO][06:58:54]: [Server #30488] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][06:58:54]: [Server #30488] Selecting client #50 for training.
[INFO][06:58:54]: [Server #30488] Sending the current model to client #50 (simulated).
[INFO][06:58:54]: [Server #30488] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][06:58:54]: [Server #30488] Selecting client #56 for training.
[INFO][06:58:54]: [Server #30488] Sending the current model to client #56 (simulated).
[INFO][06:58:55]: [Server #30488] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][06:58:55]: [Server #30488] Selecting client #78 for training.
[INFO][06:58:55]: [Server #30488] Sending the current model to client #78 (simulated).
[INFO][06:58:55]: [Server #30488] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][06:58:55]: [Server #30488] Selecting client #99 for training.
[INFO][06:58:55]: [Server #30488] Sending the current model to client #99 (simulated).
[INFO][06:58:55]: [Server #30488] Sending 18.75 MB of payload data to client #99 (simulated).
[INFO][06:58:55]: [Server #30488] Selecting client #1 for training.
[INFO][06:58:55]: [Server #30488] Sending the current model to client #1 (simulated).
[INFO][06:58:56]: [Server #30488] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][07:01:46]: [Server #30488] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][07:01:52]: [Server #30488] Received 18.75 MB of payload data from client #27 (simulated).
[INFO][07:01:53]: [Server #30488] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][07:01:53]: [Server #30488] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][07:01:53]: [Server #30488] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][07:01:53]: [Server #30488] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][07:01:53]: [Server #30488] Received 18.75 MB of payload data from client #99 (simulated).
[INFO][07:01:53]: [Server #30488] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][07:01:53]: [Server #30488] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][07:01:53]: [Server #30488] Received 18.75 MB of payload data from client #78 (simulated).
[INFO][07:01:53]: [Server #30488] Adding client #49 to the list of clients for aggregation.
[INFO][07:01:53]: [Server #30488] Adding client #27 to the list of clients for aggregation.
[INFO][07:01:53]: [Server #30488] Adding client #50 to the list of clients for aggregation.
[INFO][07:01:53]: [Server #30488] Adding client #16 to the list of clients for aggregation.
[INFO][07:01:53]: [Server #30488] Adding client #4 to the list of clients for aggregation.
[INFO][07:01:53]: [Server #30488] Adding client #63 to the list of clients for aggregation.
[INFO][07:01:53]: [Server #30488] Adding client #56 to the list of clients for aggregation.
[INFO][07:01:53]: [Server #30488] Adding client #9 to the list of clients for aggregation.
[INFO][07:01:53]: [Server #30488] Adding client #33 to the list of clients for aggregation.
[INFO][07:01:53]: [Server #30488] Adding client #98 to the list of clients for aggregation.
[INFO][07:01:53]: [Server #30488] Aggregating 10 clients in total.
[INFO][07:01:53]: [Server #30488] Updated weights have been received.
[INFO][07:01:53]: [Server #30488] Aggregating model weight deltas.
[INFO][07:01:53]: [Server #30488] Finished aggregating updated weights.
[INFO][07:01:53]: [Server #30488] Started model testing.
[INFO][07:02:04]: [Trainer.test] 测试完成 - 准确率: 14.20% (1420/10000)
[INFO][07:02:04]: [93m[1m[Server #30488] Global model accuracy: 14.20%
[0m
[INFO][07:02:04]: get_logged_items 被调用
[INFO][07:02:04]: 从updates获取参与客户端: [49, 27, 50, 16, 4, 63, 56, 9, 33, 98]
[WARNING][07:02:04]: 没有找到参与客户端的陈旧度信息
[INFO][07:02:04]: 最终logged_items: {'round': 1, 'accuracy': 0.142, 'accuracy_std': 0, 'elapsed_time': 57.494556188583374, 'processing_time': 0.007859000033931807, 'comm_time': 0, 'round_time': 57.494554782198975, 'comm_overhead': 749.9883651733398, 'global_accuracy': 0.142, 'avg_staleness': 0.0, 'max_staleness': 0.0, 'min_staleness': 0.0, 'global_accuracy_std': 0.0}
[INFO][07:02:04]: [Server #30488] All client reports have been processed.
[INFO][07:02:04]: [Server #30488] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][07:02:04]: [Server #30488] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][07:02:04]: [93m[1m
[Server #30488] Starting round 2/400.[0m
[INFO][07:02:04]: [Server #30488] Selected clients: [100, 63, 38, 33, 85, 16, 44, 5, 4, 93]
[INFO][07:02:04]: [Server #30488] Selecting client #100 for training.
[INFO][07:02:04]: [Server #30488] Sending the current model to client #100 (simulated).
[INFO][07:02:04]: [Server #30488] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][07:02:04]: [Server #30488] Selecting client #63 for training.
[INFO][07:02:04]: [Server #30488] Sending the current model to client #63 (simulated).
[INFO][07:02:04]: [Server #30488] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][07:02:04]: [Server #30488] Selecting client #38 for training.
[INFO][07:02:04]: [Server #30488] Sending the current model to client #38 (simulated).
[INFO][07:02:04]: [Server #30488] Sending 18.75 MB of payload data to client #38 (simulated).
[INFO][07:02:04]: [Server #30488] Selecting client #33 for training.
[INFO][07:02:04]: [Server #30488] Sending the current model to client #33 (simulated).
[INFO][07:02:04]: [Server #30488] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][07:02:04]: [Server #30488] Selecting client #85 for training.
[INFO][07:02:04]: [Server #30488] Sending the current model to client #85 (simulated).
[INFO][07:02:04]: [Server #30488] Sending 18.75 MB of payload data to client #85 (simulated).
[INFO][07:02:04]: [Server #30488] Selecting client #16 for training.
[INFO][07:02:04]: [Server #30488] Sending the current model to client #16 (simulated).
[INFO][07:02:05]: [Server #30488] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][07:02:05]: [Server #30488] Selecting client #44 for training.
[INFO][07:02:05]: [Server #30488] Sending the current model to client #44 (simulated).
[INFO][07:02:05]: [Server #30488] Sending 18.75 MB of payload data to client #44 (simulated).
[INFO][07:02:05]: [Server #30488] Selecting client #5 for training.
[INFO][07:02:05]: [Server #30488] Sending the current model to client #5 (simulated).
[INFO][07:02:05]: [Server #30488] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][07:02:05]: [Server #30488] Selecting client #4 for training.
[INFO][07:02:05]: [Server #30488] Sending the current model to client #4 (simulated).
[INFO][07:02:06]: [Server #30488] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][07:02:06]: [Server #30488] Selecting client #93 for training.
[INFO][07:02:06]: [Server #30488] Sending the current model to client #93 (simulated).
[INFO][07:02:06]: [Server #30488] Sending 18.75 MB of payload data to client #93 (simulated).
[INFO][07:03:43]: [Server #30488] An existing client just disconnected.
[WARNING][07:03:43]: [Server #30488] Client process #17828 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][07:03:43]: [93m[1m[Server #30488] Closing the server due to a failed client.[0m
[INFO][07:03:43]: [Server #30488] Training concluded.
[INFO][07:03:43]: [Server #30488] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][07:03:43]: [Server #30488] Closing the server.
[INFO][07:03:43]: Closing the connection to client #36112.
[INFO][07:03:43]: Closing the connection to client #39680.
[INFO][07:03:43]: Closing the connection to client #25272.
[INFO][07:03:43]: Closing the connection to client #7436.
[INFO][07:03:43]: Closing the connection to client #27384.
[INFO][07:03:43]: Closing the connection to client #33604.
[INFO][07:03:43]: Closing the connection to client #32016.
[INFO][07:03:43]: Closing the connection to client #8340.
[INFO][07:03:43]: Closing the connection to client #35616.
