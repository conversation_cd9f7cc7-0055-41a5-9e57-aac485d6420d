general:
    # The prefix of directories of dataset, models, checkpoints, and results
    base_path: .

clients:
    # Type
    type: simple

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 80

    # Should clients really go to sleep, or should we just simulate the sleep times? The sleep_simulation is true only when speed_simulation is true
    sleep_simulation: true

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 20

    random_seed: 1
    #------------------- Need modification -------------------

    # The total number of clients
    total_clients: 15

    # The number of clients selected in each round
    per_round: 5

    # The round to remove the local data by clients
    # If do_optimized_clustering is true, it needs to add 2.
    # For example, if you want to do the data deletion after round 2,
    # You should set it as round 4.
    data_deletion_round: 4

    # The clients which need to delete their local data samples
    clients_requesting_deletion: [1, 2]

    # The percentage to delete the local data by clients
    deleted_data_ratio: 0.1

server:
    address: 127.0.0.1
    port: 8011
    random_seed: 1

    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server? Useful if max_concurrency is specified
    simulate_wall_time: true

    do_test: true

    # Should we do the cluster accuracy test during learning?
    do_clustered_test: true

    # Should be false. Specified in knot
    do_global_test: false

    staleness_bound: 1000

    #------------------- Need modification -------------------
    # The window size that accuracy of each cluster should reach
    window_size: 3

    # The minimum arrival clients that all clusters can aggregrate
    minimum_clients_aggregated: 5

    #optimization clustering method
    do_optimized_clustering: true

    # The total number of clusters
    clusters: 5

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 2000

    # IID or non-IID?
    sampler: noniid

    test_sampler: noniid

    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 32
    optimizer: SGD

    # The machine learning model
    model_name: lenet5

    #------------------- Need modification -------------------
    # The maximum number of training rounds
    rounds: 10

    # The maximum number of clients running concurrently
    # Always cacluate by 20G(GPU space on the sim)/size_per_client
    # (client size should always lower than 4G)
    max_concurrency: 2

    # The target accuracy
    target_accuracy: 0.80
    target_accuracy_std: 0.005

parameters:
    model:
        num_classes: 10

    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0

algorithm:
    # Aggregation algorithm
    type: fedavg

results:
    types: round, accuracy, clusters_accuracy, elapsed_time
