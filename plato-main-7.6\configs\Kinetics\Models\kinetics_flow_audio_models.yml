# optical flow model settings
flow_model:
  type: Recognizer3D

  backbone:
    type: ResNet2Plus1d
    depth: 50
    pretrained: null
    pretained2d: False
    norm_eval: False

    conv_cfg:
      type: Conv2plus1d

    norm_cfg:
      type: SyncBN
      requires_grad: True
      eps: 1e-3
    
    conv1_kernel:
      1. 3
      2. 7
      3. 7

    conv1_stride_t: 1
    pool1_stride_t: 1

    inflate:
      1. 1
      2. 1
      3. 1
      4. 1

    spatial_strides:
      1. 1
      2. 2
      3. 2
      4. 2

    temporal_strides:
      1. 1
      2. 2
      3. 2
      4. 2

    zero_init_residual: False

  cls_head:
    type: I3DHead
    num_classes: 400
    in_channels: &flow_in_channels 512
    spatial_type: avg
    dropout_ratio: 0.5
    init_std: 0.01
  # model training and testing settings
  train_cfg: null
  test_cfg:
    average_clips: prob

# audio model settings
audio_model:
  type: AudioRecognizer

  backbone:
    type: ResNet
    depth: 50
    in_channels: 1
    norm_eval: False

  cls_head:
    type: AudioTSNHead
    num_classes: 400
    in_channels: &audio_in_channels 512
    dropout_ratio: 0.5
    init_std: 0.01
  # model training and testing settings
  train_cfg: null
  test_cfg:
    average_clips: prob


fuse_model:
  type: FullyConnectedHead
  num_classes: 400
  in_channels: 
    1. *flow_in_channels
    2. *audio_in_channels

  hidden_layer_size:
    1. 512

  dropout_ratio: 0.5
