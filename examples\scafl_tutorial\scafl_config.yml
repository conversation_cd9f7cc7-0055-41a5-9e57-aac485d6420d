# SCAFL (Staleness-Controlled Asynchronous Federated Learning) 配置文件
# 师妹学习版本 - 详细注释

# 客户端配置
clients:
    type: simple                    # 使用简单客户端类型
    total_clients: 20              # 总客户端数量 (论文中的N)
    per_round: 10                  # 每轮选择参与训练的客户端数量
    do_test: true                  # 是否进行测试
    
    # 异步训练模拟参数
    speed_simulation: true         # 开启速度模拟（模拟客户端训练时间差异）
    simulation_distribution:
        distribution: pareto       # 使用帕累托分布模拟客户端异构性
        alpha: 1                   # 分布参数（越小异构性越强）
    max_sleep_time: 10            # 最大模拟延迟时间（秒）
    sleep_simulation: true        # 开启延迟模拟
    avg_training_time: 5          # 平均训练时间（秒）
    random_seed: 42               # 随机种子，保证实验可重复

# 服务器配置
server:
    address: 127.0.0.1            # 服务器地址
    port: 8000                    # 服务器端口
    synchronous: false            # 异步模式（SCAFL的关键！）
    simulate_wall_time: true     # 模拟真实时间
    
    # SCAFL 核心参数
    tau_max: 5                    # 最大陈旧度阈值 (论文中的τmax)
    V: 1.0                        # Lyapunov权衡参数 (论文中的V)
    max_aggregation_clients: 5    # 每轮最多聚合的客户端数量 (论文中的|Mmax|)
    
    # 模型保存路径
    checkpoint_path: models/scafl/tutorial
    model_path: models/scafl/tutorial

# 数据配置
data:
    datasource: MNIST             # 数据集（从简单的MNIST开始）
    partition_size: 300           # 每个客户端的数据量
    sampler: noniid              # 非独立同分布（更真实的场景）
    concentration: 0.5           # 非IID程度（0.1很极端，0.5适中）
    random_seed: 42              # 数据划分随机种子

# 训练器配置
trainer:
    type: basic                   # 基础训练器
    rounds: 20                    # 总训练轮数（先用少一点测试）
    max_concurrency: 3           # 最大并发客户端数
    target_accuracy: 0.90        # 目标准确率
    model_name: lenet5           # 模型名称
    epochs: 1                    # 每个客户端的本地训练轮数
    batch_size: 32               # 批大小
    optimizer: SGD               # 优化器
    learning_rate: 0.01          # 学习率

# 算法配置
algorithm:
    type: fedavg                 # 基础算法类型（我们会继承并扩展）

# 模型参数配置
parameters:
    model:
        num_classes: 10          # MNIST有10个类别
    optimizer:
        lr: 0.01                # 学习率
        momentum: 0.9            # 动量
        weight_decay: 0.0001     # 权重衰减
