"""
ReFedScaFL 日志配置模块
提供统一的日志配置，支持控制台和文件输出
"""

import os
import logging
import time
from datetime import datetime


def setup_logging(log_dir="logs", log_level=logging.INFO):
    """
    设置日志配置
    
    Args:
        log_dir: 日志文件目录
        log_level: 日志级别
    """
    # 创建日志目录
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 生成日志文件名（包含时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"refedscafl_{timestamp}.log"
    log_filepath = os.path.join(log_dir, log_filename)
    
    # 配置日志格式
    log_format = '[%(levelname)s][%(asctime)s]: %(message)s'
    date_format = '%H:%M:%S'
    
    # 清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # 配置根日志记录器
    logging.basicConfig(
        level=log_level,
        format=log_format,
        datefmt=date_format,
        handlers=[
            # 控制台处理器
            logging.StreamHandler(),
            # 文件处理器
            logging.FileHandler(log_filepath, mode='w', encoding='utf-8')
        ]
    )
    
    # 设置特定模块的日志级别
    logging.getLogger('socketio').setLevel(logging.WARNING)
    logging.getLogger('engineio').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)
    
    # 记录日志配置信息
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统已初始化")
    logger.info(f"日志文件路径: {log_filepath}")
    logger.info(f"日志级别: {logging.getLevelName(log_level)}")
    
    return log_filepath


def get_logger(name):
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        logging.Logger: 日志记录器实例
    """
    return logging.getLogger(name)


def log_system_info():
    """记录系统信息"""
    logger = get_logger(__name__)
    
    try:
        import torch
        import platform
        import psutil
        
        logger.info("=" * 60)
        logger.info("系统信息:")
        logger.info(f"  操作系统: {platform.system()} {platform.release()}")
        logger.info(f"  Python版本: {platform.python_version()}")
        logger.info(f"  PyTorch版本: {torch.__version__}")
        logger.info(f"  CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            logger.info(f"  CUDA版本: {torch.version.cuda}")
            logger.info(f"  GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                logger.info(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
        
        # 内存信息
        memory = psutil.virtual_memory()
        logger.info(f"  总内存: {memory.total / (1024**3):.1f} GB")
        logger.info(f"  可用内存: {memory.available / (1024**3):.1f} GB")
        
        # CPU信息
        logger.info(f"  CPU核心数: {psutil.cpu_count()}")
        logger.info("=" * 60)
        
    except ImportError as e:
        logger.warning(f"无法获取系统信息: {e}")


def log_training_start(config_file):
    """记录训练开始信息"""
    logger = get_logger(__name__)
    
    logger.info("🚀 ReFedScaFL 训练开始")
    logger.info(f"配置文件: {config_file}")
    logger.info(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def log_training_end():
    """记录训练结束信息"""
    logger = get_logger(__name__)
    
    logger.info("✅ ReFedScaFL 训练结束")
    logger.info(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def log_round_summary(round_num, accuracy, staleness_info, training_time):
    """
    记录轮次总结信息
    
    Args:
        round_num: 轮次编号
        accuracy: 准确率
        staleness_info: 陈旧度信息字典
        training_time: 训练时间
    """
    logger = get_logger(__name__)
    
    logger.info("=" * 50)
    logger.info(f"第 {round_num} 轮训练总结:")
    logger.info(f"  全局模型准确率: {accuracy:.4f}")
    logger.info(f"  平均陈旧度: {staleness_info.get('avg', 0):.2f}")
    logger.info(f"  最大陈旧度: {staleness_info.get('max', 0)}")
    logger.info(f"  最小陈旧度: {staleness_info.get('min', 0)}")
    logger.info(f"  轮次耗时: {training_time:.2f} 秒")
    logger.info("=" * 50)
