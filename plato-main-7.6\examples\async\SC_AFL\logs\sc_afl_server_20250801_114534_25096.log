2025-08-01 11:45:34,633 - INFO - 🚀 SC-AFL服务器启动 - 2025-08-01 11:45:34
2025-08-01 11:45:34,633 - INFO - ✅ 新日志文件已创建
2025-08-01 11:45:34,633 - INFO - 📁 日志目录: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs
2025-08-01 11:45:34,633 - INFO - 📄 日志文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs\sc_afl_server_20250801_114534_25096.log
2025-08-01 11:45:34,634 - INFO - 🔧 日志级别: DEBUG (文件), INFO (控制台)
2025-08-01 11:45:34,634 - INFO - 📊 文件模式: 新建模式 (每次启动创建新文件)
2025-08-01 11:45:34,634 - INFO - 🆔 进程ID: 25096
2025-08-01 11:45:34,634 - INFO - ✅ 日志文件创建成功，当前大小: 675 字节
2025-08-01 11:45:34,634 - INFO - 🚀 SC-AFL服务器初始化开始...
2025-08-01 11:45:34,634 - INFO - 📅 初始化时间: 2025-08-01 11:45:34
2025-08-01 11:45:34,635 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:34,651 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:34,652 - INFO - Server: 动态创建模型 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:45:34,652 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:45:34,663 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:34,663 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:45:34,663 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:45:34,664 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:45:34,664 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:34,664 - INFO - [Trainer None] 初始化完成
2025-08-01 11:45:34,664 - INFO - Server: 创建了新的Trainer实例
2025-08-01 11:45:34,664 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:45:34,664 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:45:34,664 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:34,664 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:34,664 - INFO - Server: 创建了新的Algorithm实例
2025-08-01 11:45:34,665 - INFO - [93m[1m[25096] Logging runtime results to: ./results/cifar10_with_network/25096.csv.[0m
2025-08-01 11:45:34,665 - INFO - [Server #25096] Started training on 6 clients with 3 per round.
2025-08-01 11:45:34,665 - INFO - [DEBUG] 从配置文件读取 simulate_wall_time=True
2025-08-01 11:45:34,665 - WARNING - Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。
2025-08-01 11:45:34,665 - WARNING - Server: 训练器在初始化后为None，重新创建
2025-08-01 11:45:34,665 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:45:34,665 - WARNING - [Trainer None] 模型为None，尝试创建默认模型
2025-08-01 11:45:34,665 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:34,679 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:34,679 - INFO - [Trainer None] 动态创建模型 resnet_9，输入通道: 3, 类别数: 10
2025-08-01 11:45:34,688 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:34,689 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:45:34,689 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:45:34,689 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:45:34,689 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:34,689 - INFO - [Trainer None] 初始化完成
2025-08-01 11:45:34,689 - INFO - Server: 重新创建了Trainer实例
2025-08-01 11:45:34,689 - INFO - [Algorithm] 已设置服务器引用 (客户端ID: None)
2025-08-01 11:45:34,689 - INFO - Server: 算法类已设置服务器引用
2025-08-01 11:45:34,689 - INFO - 动态加载数据集: CIFAR10
2025-08-01 11:45:35,181 - INFO - ✅ 成功加载数据集 CIFAR10: 10000 样本
2025-08-01 11:45:35,181 - INFO - ✅ 动态加载测试集成功: CIFAR10, 大小: 10000
2025-08-01 11:45:35,181 - INFO - ✅ 测试加载器已创建
2025-08-01 11:45:35,181 - INFO - 开始初始化全局模型权重
2025-08-01 11:45:35,181 - WARNING - 全局模型实例为None，正在尝试重新创建...
2025-08-01 11:45:35,181 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:35,194 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:35,194 - INFO - 成功重新创建了全局模型实例 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:45:35,199 - INFO - [全局权重摘要] 参数数量: 74, 均值: 0.001177, 最大: 1.000000, 最小: -0.192442
2025-08-01 11:45:35,199 - INFO - [全局模型] 输入通道数: 3
2025-08-01 11:45:35,199 - INFO - 全局模型权重初始化成功
2025-08-01 11:45:35,200 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:45:35,200 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:45:35,201 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:45:35,203 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_114535.csv
2025-08-01 11:45:35,203 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:45:35,203 - WARNING - 网络模拟器初始化失败: 'Config' object has no attribute 'get'
2025-08-01 11:45:35,203 - INFO - SC-AFL算法参数: tau_max=5, V=1.0
2025-08-01 11:45:35,203 - INFO - 服务器初始化完成
2025-08-01 11:45:35,203 - INFO - 已创建并注册 0 个客户端（将在 Server.start() 中启动任务）
2025-08-01 11:45:35,203 - INFO - 客户端ID管理器初始化完成，总客户端数: 6, ID起始值: 1
2025-08-01 11:45:35,203 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:45:35,203 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:45:35,204 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:45:35,204 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_114535.csv
2025-08-01 11:45:35,204 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:45:35,204 - INFO - 服务器实例创建成功
2025-08-01 11:45:35,204 - INFO - 正在创建和注册 6 个客户端...
2025-08-01 11:45:35,204 - INFO - 客户端ID配置: 起始ID=1, 总数=6
2025-08-01 11:45:35,204 - INFO - 开始创建客户端 1...
2025-08-01 11:45:35,204 - INFO - 初始化客户端, ID: 1
2025-08-01 11:45:35,204 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:35,220 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:35,220 - INFO - [Client 1] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:35,220 - INFO - [Trainer] 初始化训练器, client_id: 1
2025-08-01 11:45:35,231 - INFO - [Trainer 1] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:35,231 - INFO - [Trainer 1] 模型的输入通道数: 3
2025-08-01 11:45:35,231 - INFO - [Trainer 1] 强制使用CPU
2025-08-01 11:45:35,231 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:45:35,231 - INFO - [Trainer 1] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:35,232 - INFO - [Trainer 1] 初始化完成
2025-08-01 11:45:35,232 - INFO - [Client 1] 创建新训练器
2025-08-01 11:45:35,232 - INFO - [Algorithm] 从训练器获取客户端ID: 1
2025-08-01 11:45:35,232 - INFO - [Algorithm] 初始化后修正client_id: 1
2025-08-01 11:45:35,232 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:35,232 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:35,232 - INFO - [Client 1] 创建新算法
2025-08-01 11:45:35,232 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:45:35,232 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:45:35,232 - INFO - [Client None] 父类初始化完成
2025-08-01 11:45:35,232 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:35,247 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:35,247 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:35,247 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:45:35,258 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:35,258 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:45:35,258 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:45:35,258 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:45:35,258 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:35,258 - INFO - [Trainer None] 初始化完成
2025-08-01 11:45:35,258 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:45:35,259 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:45:35,259 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:45:35,259 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:35,259 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:35,259 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:45:35,259 - INFO - [Client None] 开始加载数据
2025-08-01 11:45:35,259 - INFO - 顺序分配客户端ID: 1
2025-08-01 11:45:35,259 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 1
2025-08-01 11:45:35,260 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:45:35,260 - WARNING - [Client 1] 数据源为None，已创建新数据源
2025-08-01 11:45:35,260 - WARNING - [Client 1] 数据源trainset为None，已设置为空列表
2025-08-01 11:45:35,859 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:45:35,860 - INFO - [Client 1] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:45:35,860 - INFO - [Client 1] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:45:35,864 - INFO - [Client 1] 成功划分数据集，分配到 300 个样本
2025-08-01 11:45:35,864 - INFO - [Client 1] 初始化时成功加载数据
2025-08-01 11:45:35,864 - INFO - [客户端 1] 初始化验证通过
2025-08-01 11:45:35,864 - INFO - 客户端 1 实例创建成功
2025-08-01 11:45:35,864 - INFO - 客户端1已设置服务器引用
2025-08-01 11:45:35,864 - INFO - 客户端 1 已设置服务器引用
2025-08-01 11:45:35,864 - INFO - 客户端1已注册
2025-08-01 11:45:35,864 - INFO - 客户端 1 已成功注册到服务器
2025-08-01 11:45:35,864 - INFO - 开始创建客户端 2...
2025-08-01 11:45:35,864 - INFO - 初始化客户端, ID: 2
2025-08-01 11:45:35,864 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:35,881 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:35,881 - INFO - [Client 2] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:35,881 - INFO - [Trainer] 初始化训练器, client_id: 2
2025-08-01 11:45:35,891 - INFO - [Trainer 2] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:35,891 - INFO - [Trainer 2] 模型的输入通道数: 3
2025-08-01 11:45:35,891 - INFO - [Trainer 2] 强制使用CPU
2025-08-01 11:45:35,891 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 11:45:35,891 - INFO - [Trainer 2] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:35,891 - INFO - [Trainer 2] 初始化完成
2025-08-01 11:45:35,891 - INFO - [Client 2] 创建新训练器
2025-08-01 11:45:35,891 - INFO - [Algorithm] 从训练器获取客户端ID: 2
2025-08-01 11:45:35,891 - INFO - [Algorithm] 初始化后修正client_id: 2
2025-08-01 11:45:35,892 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:35,892 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:35,892 - INFO - [Client 2] 创建新算法
2025-08-01 11:45:35,892 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:45:35,892 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:45:35,892 - INFO - [Client None] 父类初始化完成
2025-08-01 11:45:35,892 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:35,906 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:35,906 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:35,907 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:45:35,916 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:35,916 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:45:35,916 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:45:35,917 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:45:35,917 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:35,917 - INFO - [Trainer None] 初始化完成
2025-08-01 11:45:35,917 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:45:35,917 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:45:35,917 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:45:35,917 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:35,917 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:35,917 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:45:35,917 - INFO - [Client None] 开始加载数据
2025-08-01 11:45:35,917 - INFO - 顺序分配客户端ID: 2
2025-08-01 11:45:35,917 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 2
2025-08-01 11:45:35,918 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:45:35,918 - WARNING - [Client 2] 数据源为None，已创建新数据源
2025-08-01 11:45:35,918 - WARNING - [Client 2] 数据源trainset为None，已设置为空列表
2025-08-01 11:45:36,532 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:45:36,532 - INFO - [Client 2] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:45:36,532 - INFO - [Client 2] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:45:36,536 - INFO - [Client 2] 成功划分数据集，分配到 300 个样本
2025-08-01 11:45:36,536 - INFO - [Client 2] 初始化时成功加载数据
2025-08-01 11:45:36,536 - INFO - [客户端 2] 初始化验证通过
2025-08-01 11:45:36,536 - INFO - 客户端 2 实例创建成功
2025-08-01 11:45:36,536 - INFO - 客户端2已设置服务器引用
2025-08-01 11:45:36,536 - INFO - 客户端 2 已设置服务器引用
2025-08-01 11:45:36,536 - INFO - 客户端2已注册
2025-08-01 11:45:36,536 - INFO - 客户端 2 已成功注册到服务器
2025-08-01 11:45:36,536 - INFO - 开始创建客户端 3...
2025-08-01 11:45:36,536 - INFO - 初始化客户端, ID: 3
2025-08-01 11:45:36,536 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:36,551 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:36,551 - INFO - [Client 3] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:36,551 - INFO - [Trainer] 初始化训练器, client_id: 3
2025-08-01 11:45:36,562 - INFO - [Trainer 3] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:36,562 - INFO - [Trainer 3] 模型的输入通道数: 3
2025-08-01 11:45:36,562 - INFO - [Trainer 3] 强制使用CPU
2025-08-01 11:45:36,562 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:45:36,562 - INFO - [Trainer 3] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:36,562 - INFO - [Trainer 3] 初始化完成
2025-08-01 11:45:36,562 - INFO - [Client 3] 创建新训练器
2025-08-01 11:45:36,562 - INFO - [Algorithm] 从训练器获取客户端ID: 3
2025-08-01 11:45:36,562 - INFO - [Algorithm] 初始化后修正client_id: 3
2025-08-01 11:45:36,562 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:36,562 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:36,562 - INFO - [Client 3] 创建新算法
2025-08-01 11:45:36,562 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:45:36,562 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:45:36,562 - INFO - [Client None] 父类初始化完成
2025-08-01 11:45:36,562 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:36,576 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:36,576 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:36,576 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:45:36,585 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:36,585 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:45:36,585 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:45:36,585 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:45:36,585 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:36,585 - INFO - [Trainer None] 初始化完成
2025-08-01 11:45:36,585 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:45:36,585 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:45:36,585 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:45:36,585 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:36,585 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:36,585 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:45:36,585 - INFO - [Client None] 开始加载数据
2025-08-01 11:45:36,585 - INFO - 顺序分配客户端ID: 3
2025-08-01 11:45:36,585 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 3
2025-08-01 11:45:36,586 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:45:36,586 - WARNING - [Client 3] 数据源为None，已创建新数据源
2025-08-01 11:45:36,586 - WARNING - [Client 3] 数据源trainset为None，已设置为空列表
2025-08-01 11:45:37,185 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:45:37,185 - INFO - [Client 3] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:45:37,185 - INFO - [Client 3] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:45:37,188 - INFO - [Client 3] 成功划分数据集，分配到 300 个样本
2025-08-01 11:45:37,188 - INFO - [Client 3] 初始化时成功加载数据
2025-08-01 11:45:37,188 - INFO - [客户端 3] 初始化验证通过
2025-08-01 11:45:37,188 - INFO - 客户端 3 实例创建成功
2025-08-01 11:45:37,188 - INFO - 客户端3已设置服务器引用
2025-08-01 11:45:37,189 - INFO - 客户端 3 已设置服务器引用
2025-08-01 11:45:37,189 - INFO - 客户端3已注册
2025-08-01 11:45:37,189 - INFO - 客户端 3 已成功注册到服务器
2025-08-01 11:45:37,189 - INFO - 开始创建客户端 4...
2025-08-01 11:45:37,189 - INFO - 初始化客户端, ID: 4
2025-08-01 11:45:37,189 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:37,203 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:37,203 - INFO - [Client 4] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:37,203 - INFO - [Trainer] 初始化训练器, client_id: 4
2025-08-01 11:45:37,213 - INFO - [Trainer 4] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:37,213 - INFO - [Trainer 4] 模型的输入通道数: 3
2025-08-01 11:45:37,213 - INFO - [Trainer 4] 强制使用CPU
2025-08-01 11:45:37,214 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 11:45:37,214 - INFO - [Trainer 4] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:37,214 - INFO - [Trainer 4] 初始化完成
2025-08-01 11:45:37,214 - INFO - [Client 4] 创建新训练器
2025-08-01 11:45:37,214 - INFO - [Algorithm] 从训练器获取客户端ID: 4
2025-08-01 11:45:37,214 - INFO - [Algorithm] 初始化后修正client_id: 4
2025-08-01 11:45:37,214 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:37,214 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:37,214 - INFO - [Client 4] 创建新算法
2025-08-01 11:45:37,214 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:45:37,214 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:45:37,214 - INFO - [Client None] 父类初始化完成
2025-08-01 11:45:37,214 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:37,227 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:37,227 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:37,227 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:45:37,239 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:37,239 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:45:37,239 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:45:37,239 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:45:37,239 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:37,239 - INFO - [Trainer None] 初始化完成
2025-08-01 11:45:37,239 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:45:37,239 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:45:37,239 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:45:37,239 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:37,239 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:37,239 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:45:37,239 - INFO - [Client None] 开始加载数据
2025-08-01 11:45:37,240 - INFO - 顺序分配客户端ID: 4
2025-08-01 11:45:37,240 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 4
2025-08-01 11:45:37,240 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:45:37,240 - WARNING - [Client 4] 数据源为None，已创建新数据源
2025-08-01 11:45:37,240 - WARNING - [Client 4] 数据源trainset为None，已设置为空列表
2025-08-01 11:45:37,866 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:45:37,866 - INFO - [Client 4] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:45:37,866 - INFO - [Client 4] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:45:37,871 - INFO - [Client 4] 成功划分数据集，分配到 300 个样本
2025-08-01 11:45:37,871 - INFO - [Client 4] 初始化时成功加载数据
2025-08-01 11:45:37,871 - INFO - [客户端 4] 初始化验证通过
2025-08-01 11:45:37,871 - INFO - 客户端 4 实例创建成功
2025-08-01 11:45:37,871 - INFO - 客户端4已设置服务器引用
2025-08-01 11:45:37,871 - INFO - 客户端 4 已设置服务器引用
2025-08-01 11:45:37,871 - INFO - 客户端4已注册
2025-08-01 11:45:37,871 - INFO - 客户端 4 已成功注册到服务器
2025-08-01 11:45:37,871 - INFO - 开始创建客户端 5...
2025-08-01 11:45:37,871 - INFO - 初始化客户端, ID: 5
2025-08-01 11:45:37,871 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:37,886 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:37,886 - INFO - [Client 5] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:37,886 - INFO - [Trainer] 初始化训练器, client_id: 5
2025-08-01 11:45:37,897 - INFO - [Trainer 5] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:37,897 - INFO - [Trainer 5] 模型的输入通道数: 3
2025-08-01 11:45:37,897 - INFO - [Trainer 5] 强制使用CPU
2025-08-01 11:45:37,897 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 11:45:37,898 - INFO - [Trainer 5] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:37,898 - INFO - [Trainer 5] 初始化完成
2025-08-01 11:45:37,898 - INFO - [Client 5] 创建新训练器
2025-08-01 11:45:37,898 - INFO - [Algorithm] 从训练器获取客户端ID: 5
2025-08-01 11:45:37,898 - INFO - [Algorithm] 初始化后修正client_id: 5
2025-08-01 11:45:37,898 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:37,898 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:37,898 - INFO - [Client 5] 创建新算法
2025-08-01 11:45:37,898 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:45:37,898 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:45:37,898 - INFO - [Client None] 父类初始化完成
2025-08-01 11:45:37,898 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:37,912 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:37,912 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:37,912 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:45:37,921 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:37,921 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:45:37,921 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:45:37,922 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:45:37,922 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:37,922 - INFO - [Trainer None] 初始化完成
2025-08-01 11:45:37,922 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:45:37,922 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:45:37,922 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:45:37,922 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:37,922 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:37,922 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:45:37,922 - INFO - [Client None] 开始加载数据
2025-08-01 11:45:37,922 - INFO - 顺序分配客户端ID: 5
2025-08-01 11:45:37,922 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 5
2025-08-01 11:45:37,922 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:45:37,922 - WARNING - [Client 5] 数据源为None，已创建新数据源
2025-08-01 11:45:37,922 - WARNING - [Client 5] 数据源trainset为None，已设置为空列表
2025-08-01 11:45:38,572 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:45:38,572 - INFO - [Client 5] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:45:38,572 - INFO - [Client 5] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:45:38,576 - INFO - [Client 5] 成功划分数据集，分配到 300 个样本
2025-08-01 11:45:38,576 - INFO - [Client 5] 初始化时成功加载数据
2025-08-01 11:45:38,576 - INFO - [客户端 5] 初始化验证通过
2025-08-01 11:45:38,576 - INFO - 客户端 5 实例创建成功
2025-08-01 11:45:38,577 - INFO - 客户端5已设置服务器引用
2025-08-01 11:45:38,577 - INFO - 客户端 5 已设置服务器引用
2025-08-01 11:45:38,577 - INFO - 客户端5已注册
2025-08-01 11:45:38,577 - INFO - 客户端 5 已成功注册到服务器
2025-08-01 11:45:38,577 - INFO - 开始创建客户端 6...
2025-08-01 11:45:38,577 - INFO - 初始化客户端, ID: 6
2025-08-01 11:45:38,577 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:38,594 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:38,594 - INFO - [Client 6] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:38,594 - INFO - [Trainer] 初始化训练器, client_id: 6
2025-08-01 11:45:38,606 - INFO - [Trainer 6] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:38,606 - INFO - [Trainer 6] 模型的输入通道数: 3
2025-08-01 11:45:38,606 - INFO - [Trainer 6] 强制使用CPU
2025-08-01 11:45:38,606 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:45:38,606 - INFO - [Trainer 6] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:38,606 - INFO - [Trainer 6] 初始化完成
2025-08-01 11:45:38,606 - INFO - [Client 6] 创建新训练器
2025-08-01 11:45:38,606 - INFO - [Algorithm] 从训练器获取客户端ID: 6
2025-08-01 11:45:38,606 - INFO - [Algorithm] 初始化后修正client_id: 6
2025-08-01 11:45:38,606 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:38,606 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:38,606 - INFO - [Client 6] 创建新算法
2025-08-01 11:45:38,607 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:45:38,607 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:45:38,607 - INFO - [Client None] 父类初始化完成
2025-08-01 11:45:38,607 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:45:38,620 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:45:38,620 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:45:38,620 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:45:38,631 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:45:38,631 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:45:38,631 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:45:38,631 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:45:38,631 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:45:38,631 - INFO - [Trainer None] 初始化完成
2025-08-01 11:45:38,631 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:45:38,631 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:45:38,631 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:45:38,631 - INFO - [Algorithm] 初始化完成
2025-08-01 11:45:38,631 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:45:38,631 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:45:38,631 - INFO - [Client None] 开始加载数据
2025-08-01 11:45:38,631 - INFO - 顺序分配客户端ID: 6
2025-08-01 11:45:38,632 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 6
2025-08-01 11:45:38,632 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:45:38,632 - WARNING - [Client 6] 数据源为None，已创建新数据源
2025-08-01 11:45:38,632 - WARNING - [Client 6] 数据源trainset为None，已设置为空列表
2025-08-01 11:45:39,243 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:45:39,243 - INFO - [Client 6] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:45:39,243 - INFO - [Client 6] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:45:39,247 - INFO - [Client 6] 成功划分数据集，分配到 300 个样本
2025-08-01 11:45:39,247 - INFO - [Client 6] 初始化时成功加载数据
2025-08-01 11:45:39,247 - INFO - [客户端 6] 初始化验证通过
2025-08-01 11:45:39,247 - INFO - 客户端 6 实例创建成功
2025-08-01 11:45:39,247 - INFO - 客户端6已设置服务器引用
2025-08-01 11:45:39,247 - INFO - 客户端 6 已设置服务器引用
2025-08-01 11:45:39,247 - INFO - 客户端6已注册
2025-08-01 11:45:39,247 - INFO - 客户端 6 已成功注册到服务器
2025-08-01 11:45:39,247 - INFO - 已成功创建和注册 6 个客户端
2025-08-01 11:45:39,247 - INFO - 服务器属性检查:
2025-08-01 11:45:39,247 - INFO - - 客户端数量: 6
2025-08-01 11:45:39,247 - INFO - - 全局模型: 已初始化
2025-08-01 11:45:39,247 - INFO - - 算法: 已初始化
2025-08-01 11:45:39,247 - INFO - - 训练器: 已初始化
2025-08-01 11:45:39,247 - INFO - 准备启动服务器...
2025-08-01 11:45:39,247 - INFO - [Server #25096] 启动中...
2025-08-01 11:45:39,247 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:45:39,264 - INFO - 服务器将使用事件循环: <ProactorEventLoop running=False closed=False debug=False>
2025-08-01 11:45:39,264 - INFO - ✅ 服务器已有 6 个客户端，开始启动训练
2025-08-01 11:45:39,265 - INFO - [Client 1] 模型已放置到设备: cpu
2025-08-01 11:45:39,271 - INFO - [Client 1] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,279 - INFO - [Client 1] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,279 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:45:39,279 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:45:39,279 - INFO - [Client 1] 已更新algorithm的client_id
2025-08-01 11:45:39,279 - INFO - [Client 1] 模型初始化完成
2025-08-01 11:45:39,279 - INFO - 客户端 1 模型初始化成功
2025-08-01 11:45:39,279 - ERROR - 启动客户端 1 异步任务时出错: no running event loop
2025-08-01 11:45:39,293 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:45:39,297 - INFO - [Client 2] 模型已放置到设备: cpu
2025-08-01 11:45:39,302 - INFO - [Client 2] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,309 - INFO - [Client 2] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,309 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:45:39,309 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:45:39,309 - INFO - [Client 2] 已更新algorithm的client_id
2025-08-01 11:45:39,309 - INFO - [Client 2] 模型初始化完成
2025-08-01 11:45:39,309 - INFO - 客户端 2 模型初始化成功
2025-08-01 11:45:39,309 - ERROR - 启动客户端 2 异步任务时出错: no running event loop
2025-08-01 11:45:39,310 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:45:39,310 - INFO - [Client 3] 模型已放置到设备: cpu
2025-08-01 11:45:39,315 - INFO - [Client 3] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,321 - INFO - [Client 3] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,321 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:45:39,321 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:45:39,321 - INFO - [Client 3] 已更新algorithm的client_id
2025-08-01 11:45:39,321 - INFO - [Client 3] 模型初始化完成
2025-08-01 11:45:39,321 - INFO - 客户端 3 模型初始化成功
2025-08-01 11:45:39,321 - ERROR - 启动客户端 3 异步任务时出错: no running event loop
2025-08-01 11:45:39,321 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:45:39,322 - INFO - [Client 4] 模型已放置到设备: cpu
2025-08-01 11:45:39,326 - INFO - [Client 4] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,332 - INFO - [Client 4] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,332 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:45:39,332 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:45:39,332 - INFO - [Client 4] 已更新algorithm的client_id
2025-08-01 11:45:39,332 - INFO - [Client 4] 模型初始化完成
2025-08-01 11:45:39,332 - INFO - 客户端 4 模型初始化成功
2025-08-01 11:45:39,332 - ERROR - 启动客户端 4 异步任务时出错: no running event loop
2025-08-01 11:45:39,333 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:45:39,333 - INFO - [Client 5] 模型已放置到设备: cpu
2025-08-01 11:45:39,338 - INFO - [Client 5] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,344 - INFO - [Client 5] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,344 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:45:39,344 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:45:39,344 - INFO - [Client 5] 已更新algorithm的client_id
2025-08-01 11:45:39,344 - INFO - [Client 5] 模型初始化完成
2025-08-01 11:45:39,344 - INFO - 客户端 5 模型初始化成功
2025-08-01 11:45:39,344 - ERROR - 启动客户端 5 异步任务时出错: no running event loop
2025-08-01 11:45:39,345 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:45:39,345 - INFO - [Client 6] 模型已放置到设备: cpu
2025-08-01 11:45:39,350 - INFO - [Client 6] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,356 - INFO - [Client 6] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:45:39,356 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:45:39,356 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:45:39,356 - INFO - [Client 6] 已更新algorithm的client_id
2025-08-01 11:45:39,356 - INFO - [Client 6] 模型初始化完成
2025-08-01 11:45:39,356 - INFO - 客户端 6 模型初始化成功
2025-08-01 11:45:39,356 - ERROR - 启动客户端 6 异步任务时出错: no running event loop
2025-08-01 11:45:39,357 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:45:39,357 - INFO - 服务器主循环任务已启动: <Task pending name='Task-1' coro=<Server.run() running at D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py:1313>>
2025-08-01 11:45:39,357 - INFO - Starting a server at address 127.0.0.1 and port 8000.
2025-08-01 11:45:39,358 - INFO - [Server #25096] 开始训练，共有 6 个客户端，每轮最多聚合 3 个客户端
2025-08-01 11:45:39,358 - INFO - 总训练轮次: 10
2025-08-01 11:45:39,358 - INFO - 🚀 开始第 1 轮训练（目标：10 轮）
2025-08-01 11:45:39,358 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:39,358 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:40,374 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:40,374 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:41,384 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:41,384 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:42,399 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:42,399 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:43,410 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:43,410 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:44,423 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:45:44,423 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
