# FedBuff EMNIST配置文件
# 结合FedBuff和SCAFL的配置风格

# 客户端配置
clients:
    # 类型
    type: simple

    # 总客户端数量
    total_clients: 100

    # 每轮选择的客户端数量
    per_round: 20

    # 客户端是否在本地计算测试准确率
    do_test: true

    # 客户端是否使用全局模型在本地数据上计算测试准确率
    do_global_test: true

    # 是否模拟客户端异构性
    speed_simulation: true

    # 客户端速度分布
    simulation_distribution:
        distribution: pareto
        alpha: 1.0

    # 每个epoch后客户端最大睡眠时间
    max_sleep_time: 5

    # 是否实际进行睡眠，或只是模拟睡眠时间
    sleep_simulation: false

    # 如果模拟客户端训练时间，平均训练时间是多少
    avg_training_time: 5

    # 网络波动模拟配置 - 启用网络模拟
    network_simulation: true

    # 网络延迟配置 (毫秒) - 模拟移动网络和边缘设备
    network_delay:
        min_delay: 50      # 最小延迟50ms
        max_delay: 2000    # 最大延迟2秒
        distribution: exponential  # 指数分布模拟真实网络

    # 网络丢包率配置
    packet_loss:
        loss_rate: 0.15    # 15%丢包率
        burst_loss: true   # 突发丢包

    # 网络带宽限制
    bandwidth_limit:
        upload_speed: 1024   # 1MB/s上传
        download_speed: 2048 # 2MB/s下载

    random_seed: 1

# 服务器配置
server:
    address: 127.0.0.1
    port: 8033
    ping_timeout: 36000
    ping_interval: 36000

    # 是否模拟服务器端的墙上时钟时间
    simulate_wall_time: true

    # 是否在同步模式下运行
    synchronous: false

    # FedBuff参数：开始聚合前需要的最小客户端数量
    minimum_clients_aggregated: 10

    # 最大陈旧度界限，超过此界限服务器应等待陈旧客户端
    staleness_bound: 5

    # 是否向超过陈旧度界限的客户端发送紧急通知
    request_update: true

    # 存储临时检查点和模型的路径
    checkpoint_path: models/fedbuff/emnist
    model_path: models/fedbuff/emnist

    random_seed: 1

# 数据配置
data:
    # 使用的数据集
    datasource: EMNIST
    
    # 分区大小
    partition_size: 1128
    
    # 数据采样方式
    sampler: noniid
    
    # Dirichlet分布浓度参数（越小越不均衡）
    concentration: 0.1
    
    # 随机种子
    random_seed: 1

# 训练配置
trainer:
    # 训练器类型
    type: basic
    
    # 训练轮数
    rounds: 50
    
    # 最大并发数
    max_concurrency: 3
    
    # 目标准确率
    target_accuracy: 0.95
    
    # 使用的模型名称
    model_name: lenet5
    
    # 每轮本地训练的epoch数
    epochs: 1
    
    # 批次大小
    batch_size: 128
    
    # 优化器
    optimizer: SGD

# 算法配置
algorithm:
    # 算法类型
    type: fedavg

# 模型参数
parameters:
    model:
        # 类别数量
        num_classes: 47

    optimizer:
        # 学习率
        lr: 0.005
        
        # 动量
        momentum: 0.9
        
        # 权重衰减
        weight_decay: 0.0004

# 结果配置
results:
    # 结果保存路径
    result_path: results/fedbuff/emnist
    
    # 记录的结果类型
    types: round, elapsed_time, accuracy, avg_staleness, max_staleness, min_staleness, network_success_rate

# 可视化配置
visualization:
    # 是否启用可视化
    enabled: true
    
    # 可视化结果保存路径
    result_path: visualization_results/fedbuff
    
    # 中文字体支持
    chinese_font: Microsoft YaHei
    
    # 数据分布可视化
    data_distribution:
        enabled: true
        partition_types: ["iid", "noniid", "pathological"]
        
    # 客户端性能可视化
    client_performance:
        enabled: true
        metrics: ["quality", "staleness", "compute_power", "comm_speed"]
