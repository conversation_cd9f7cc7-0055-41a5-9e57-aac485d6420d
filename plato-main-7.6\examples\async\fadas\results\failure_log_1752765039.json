{"overall": {"total_attempts": 24, "total_failures": 2, "failure_rate": 0.08333333333333333, "current_threshold": 0.48967443295946705, "window_size": 20, "avg_comm_time": 0.42816955956558705}, "clients": {"1": {"attempts": 4, "failures": 0, "failure_rate": 0.0, "last_comm_time": 0.368372546717476, "last_success": "True"}, "2": {"attempts": 4, "failures": 0, "failure_rate": 0.0, "last_comm_time": 0.3396998685763958, "last_success": "True"}, "3": {"attempts": 4, "failures": 0, "failure_rate": 0.0, "last_comm_time": 0.3501499971918131, "last_success": "True"}, "4": {"attempts": 4, "failures": 0, "failure_rate": 0.0, "last_comm_time": 0.10795545664112577, "last_success": "True"}, "5": {"attempts": 4, "failures": 0, "failure_rate": 0.0, "last_comm_time": 0.37189180764517793, "last_success": "True"}, "6": {"attempts": 4, "failures": 2, "failure_rate": 0.5, "last_comm_time": 0.5132815400960021, "last_success": "False"}}, "config": {"base_communication_time": 0.5, "communication_noise_std": 0.5, "initial_threshold": 1.0, "threshold_range": [0.2, 3.0], "smoothing_alpha": 0.3, "window_size": 20, "enable_dynamic_threshold": true, "enable_logging": true}}