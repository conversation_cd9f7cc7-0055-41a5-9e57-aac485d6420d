from collections import OrderedDict
import logging
import torch
import torch.nn as nn
import numpy as np

from plato.algorithms import fedavg

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("FedSCAFL-Algorithm")

class Algorithm(fedavg.Algorithm):
    """FedSCAFL算法实现，处理客户端过时度并进行加权聚合。"""
    
    def __init__(self, trainer=None):
        self.trainer = trainer
        logger.info("FedSCAFL算法初始化，使用训练器: %s", trainer.__class__.__name__ if trainer else "None")
        if trainer:
            super().__init__(trainer)
            # model属性将在super().__init__中被设置
        else:
            # 如果没有trainer，我们需要手动设置一些必要的属性
            self.model = None
            
        self.client_staleness = {}
        logger.info("FedSCAFL算法初始化完成")
    
    def initialize_staleness(self):
        """确保过时度字典已初始化"""
        if not hasattr(self, 'client_staleness'):
            logger.info("初始化客户端过时度字典")
            self.client_staleness = {}
        return self.client_staleness
    
    def extract_weights(self):
        """
        提取模型权重的方法，如果没有model，则创建一个空模型并返回其权重
        """
        if self.model is None:
            logger.warning("尝试提取权重但model为None，正在创建临时模型")
            try:
                # 创建LeNet5模型
                class LeNet5(nn.Module):
                    """A simple LeNet-5 model."""
                    def __init__(self, num_classes=10):
                        super().__init__()
                        self.conv1 = nn.Conv2d(1, 6, 5)
                        self.conv2 = nn.Conv2d(6, 16, 5)
                        self.conv3 = nn.Conv2d(16, 120, 5)
                        self.fc4 = nn.Linear(120, 84)
                        self.fc5 = nn.Linear(84, num_classes)

                    def forward(self, x):
                        out = nn.functional.relu(self.conv1(x))
                        out = nn.functional.max_pool2d(out, 2)
                        out = nn.functional.relu(self.conv2(out))
                        out = nn.functional.max_pool2d(out, 2)
                        out = nn.functional.relu(self.conv3(out))
                        out = out.view(out.size(0), -1)
                        out = nn.functional.relu(self.fc4(out))
                        out = self.fc5(out)
                        return out
                
                # 创建临时模型实例
                temp_model = LeNet5(num_classes=10)
                self.model = temp_model
                logger.info("成功创建临时LeNet5模型")
                
                # 现在我们有模型了，使用父类的方法提取权重
                return super().extract_weights()
            except Exception as e:
                logger.error("创建临时模型失败: %s", str(e), exc_info=True)
                return {}
        
        return super().extract_weights()
        
    def load_weights(self, weights):
        """
        加载模型权重的方法，如果没有model，则先创建模型
        """
        if self.model is None:
            logger.warning("尝试加载权重但model为None，创建模型")
            try:
                # 创建LeNet5模型
                class LeNet5(nn.Module):
                    """A simple LeNet-5 model."""
                    def __init__(self, num_classes=10):
                        super().__init__()
                        self.conv1 = nn.Conv2d(1, 6, 5)
                        self.conv2 = nn.Conv2d(6, 16, 5)
                        self.conv3 = nn.Conv2d(16, 120, 5)
                        self.fc4 = nn.Linear(120, 84)
                        self.fc5 = nn.Linear(84, num_classes)

                    def forward(self, x):
                        out = nn.functional.relu(self.conv1(x))
                        out = nn.functional.max_pool2d(out, 2)
                        out = nn.functional.relu(self.conv2(out))
                        out = nn.functional.max_pool2d(out, 2)
                        out = nn.functional.relu(self.conv3(out))
                        out = out.view(out.size(0), -1)
                        out = nn.functional.relu(self.fc4(out))
                        out = self.fc5(out)
                        return out
                
                # 创建模型实例
                self.model = LeNet5(num_classes=10)
                logger.info("成功创建模型")
            except Exception as e:
                logger.error("创建模型失败: %s", str(e), exc_info=True)
                return
        
        # 检查权重字典是否为空
        if not weights:
            logger.warning("收到空的权重字典，跳过加载")
            return
            
        try:
            # 使用非严格模式加载权重，不要求完全匹配
            self.model.load_state_dict(weights, strict=False)
            logger.info("成功加载权重")
        except Exception as e:
            logger.error("加载权重失败: %s", str(e), exc_info=True)
            logger.warning("尝试使用备选方法加载权重")
            
            # 尝试只加载名称匹配的层
            try:
                model_state_dict = self.model.state_dict()
                filtered_weights = {k: v for k, v in weights.items() if k in model_state_dict and v.shape == model_state_dict[k].shape}
                if filtered_weights:
                    self.model.load_state_dict(filtered_weights, strict=False)
                    logger.info("使用过滤后的权重加载成功")
                else:
                    logger.warning("没有匹配的权重可以加载")
            except Exception as inner_e:
                logger.error("备选加载方法也失败: %s", str(inner_e), exc_info=True)

    def get_cid(self, client_id):
        """从客户端ID中提取简洁的标识符（例如去掉前缀）"""
        # 假设client_id格式为"client_123"或123，先转为字符串再分割
        cid = str(client_id).split('_')[-1]
        logger.debug("提取客户端ID: %s -> %s", client_id, cid)
        return cid

    async def aggregate_weights(self, updates, baseline_weights, weight_received):
        """根据客户端过时度进行加权聚合。"""
        # 确保过时度字典已初始化
        self.initialize_staleness()
        
        logger.info("开始聚合权重，根据客户端过时度进行加权")
        logger.info(f"收到 {len(updates) if updates else 0} 个客户端更新")
        
        # 输出各客户端的过时度信息
        if hasattr(self, 'client_staleness') and self.client_staleness:
            logger.info("当前客户端过时度信息:")
            for cid, staleness in self.client_staleness.items():
                logger.info(f"  客户端 {cid}: 过时度 = {staleness}")
        else:
            logger.warning("没有可用的客户端过时度信息")
        
        # 计算聚合权重
        weights_dict = {}
        staleness_weights = {}
        total_weight = 0
        
        try:
            # 为每个客户端计算权重
            for report in updates:
                cid = report.client_id
                # 获取样本数量
                sample_size = getattr(report, 'sample_size', 1)
                
                # 检索客户端的过时度
                if hasattr(self, 'client_staleness'):
                    staleness = self.client_staleness.get(cid, 0)
                else:
                    staleness = 0
                    logger.warning(f"客户端{cid}没有过时度信息，使用默认值0")
                
                # 计算权重 (样本量 * 过时度衰减因子)
                alpha = max(0, 1 - staleness * 0.1)  # 简单的线性衰减，可调整
                weight = sample_size * alpha
                
                weights_dict[cid] = weight
                staleness_weights[cid] = alpha
                total_weight += weight
                
                logger.info(f"客户端 {cid}: 样本数={sample_size}, 过时度={staleness}, " + 
                           f"衰减因子={alpha:.4f}, 最终权重={weight:.4f}")
            
            # 如果总权重为0，则使用均匀权重
            if total_weight == 0:
                logger.warning("总权重为0，使用均匀权重")
                for report in updates:
                    cid = report.client_id
                    weights_dict[cid] = 1.0 / len(updates) if updates else 0
            else:
                # 归一化权重
                for cid in weights_dict:
                    weights_dict[cid] /= total_weight
                    logger.info(f"客户端 {cid} 归一化权重: {weights_dict[cid]:.4f}")
            
            # 调用父类方法使用计算好的权重进行聚合
            return await super().aggregate_weights(updates, baseline_weights, weights_dict)
        except Exception as e:
            logger.error(f"权重聚合出错: {str(e)}", exc_info=True)
            # 失败时回退到均匀权重
            logger.warning("使用均匀权重作为回退方法")
            return await super().aggregate_weights(updates, baseline_weights, weight_received)