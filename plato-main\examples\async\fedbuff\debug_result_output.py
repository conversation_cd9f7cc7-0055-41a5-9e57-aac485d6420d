#!/usr/bin/env python3
"""
调试FedBuff结果输出逻辑
"""

import os
import sys
sys.path.append('../../../')

from plato.config import Config
from plato.utils import csv_processor

def debug_config():
    """调试配置加载"""
    print("🔍 调试配置加载")
    print("=" * 50)
    
    try:
        # 加载配置
        Config.load_config("fedbuff_MNIST_original.yml")
        
        print("✅ 配置加载成功")
        
        # 检查关键配置
        result_path = Config().params.get("result_path")
        print(f"📁 结果路径: {result_path}")
        
        result_types = Config().params.get("result_types")
        print(f"📊 结果类型: {result_types}")
        
        # 检查路径是否存在
        if result_path and os.path.exists(result_path):
            print(f"✅ 结果路径存在")
            files = os.listdir(result_path)
            print(f"📂 目录内容: {files}")
        else:
            print(f"❌ 结果路径不存在: {result_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_csv_creation():
    """测试CSV文件创建"""
    print(f"\n🧪 测试CSV文件创建")
    print("-" * 50)
    
    try:
        # 获取配置
        result_path = Config().params["result_path"]
        result_types = Config().params["result_types"]
        
        # 创建测试文件
        test_filename = "test_fedbuff_output.csv"
        test_file_path = os.path.join(result_path, test_filename)
        
        # 解析结果类型
        recorded_items_list = [x.strip() for x in result_types.split(",")]
        print(f"📋 记录字段: {recorded_items_list}")
        
        # 初始化CSV
        csv_processor.initialize_csv(test_file_path, recorded_items_list, result_path)
        print(f"✅ CSV文件初始化成功: {test_file_path}")
        
        # 写入测试数据
        test_data = [1, 10.5, 0.75, 0.72, 0.03]  # round, elapsed_time, accuracy, global_accuracy, global_accuracy_std
        csv_processor.write_csv(test_file_path, test_data)
        print(f"✅ 测试数据写入成功")
        
        # 检查文件是否存在
        if os.path.exists(test_file_path):
            print(f"✅ 文件确实存在: {test_file_path}")
            
            # 读取文件内容
            with open(test_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📄 文件内容:")
                print(content)
        else:
            print(f"❌ 文件不存在: {test_file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_default_csv_logic():
    """检查默认CSV逻辑"""
    print(f"\n🔍 检查默认CSV逻辑")
    print("-" * 50)
    
    try:
        # 模拟默认CSV文件名
        result_path = Config().params["result_path"]
        default_csv = f"{result_path}/{os.getpid()}.csv"
        
        print(f"📄 默认CSV文件路径: {default_csv}")
        print(f"🆔 当前进程ID: {os.getpid()}")
        
        # 检查是否存在
        if os.path.exists(default_csv):
            print(f"✅ 默认CSV文件存在")
            with open(default_csv, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📄 默认CSV内容:")
                print(content)
        else:
            print(f"❌ 默认CSV文件不存在")
        
        # 检查目录下所有CSV文件
        if os.path.exists(result_path):
            csv_files = [f for f in os.listdir(result_path) if f.endswith('.csv')]
            print(f"📂 目录下的CSV文件: {csv_files}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def simulate_fedbuff_output():
    """模拟FedBuff输出过程"""
    print(f"\n🎭 模拟FedBuff输出过程")
    print("-" * 50)
    
    try:
        # 模拟自定义文件名生成
        from datetime import datetime
        
        config_name = "fedbuff_original_MNIST"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
        custom_filename = f"{config_name}_{timestamp}.csv"
        
        result_path = Config().params["result_path"]
        custom_result_file = os.path.join(result_path, custom_filename)
        
        print(f"📄 自定义文件名: {custom_filename}")
        print(f"📁 完整路径: {custom_result_file}")
        
        # 初始化CSV
        recorded_items = Config().params["result_types"]
        recorded_items_list = [x.strip() for x in recorded_items.split(",")]
        
        csv_processor.initialize_csv(custom_result_file, recorded_items_list, result_path)
        print(f"✅ 自定义CSV初始化成功")
        
        # 模拟多轮数据写入
        for round_num in range(1, 4):
            test_data = [
                round_num,                    # round
                round_num * 15.5,            # elapsed_time
                0.2 + round_num * 0.1,       # accuracy
                0.18 + round_num * 0.09,     # global_accuracy
                0.03                         # global_accuracy_std
            ]
            
            csv_processor.write_csv(custom_result_file, test_data)
            print(f"✅ 第{round_num}轮数据写入成功")
        
        # 检查最终文件
        if os.path.exists(custom_result_file):
            print(f"✅ 最终文件存在: {custom_result_file}")
            with open(custom_result_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📄 最终文件内容:")
                print(content)
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主调试函数"""
    print("🚀 FedBuff结果输出逻辑调试")
    print("=" * 60)
    
    # 运行调试步骤
    results = []
    
    results.append(debug_config())
    results.append(test_csv_creation())
    results.append(check_default_csv_logic())
    results.append(simulate_fedbuff_output())
    
    # 总结
    print(f"\n🎯 调试结果总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有调试测试通过！")
        print("✅ CSV输出逻辑正常工作")
        print("✅ 文件路径配置正确")
        print("✅ 可以正常创建和写入CSV文件")
    else:
        print("❌ 部分调试测试失败")
        print("🔧 需要检查配置和路径设置")

if __name__ == "__main__":
    main()
