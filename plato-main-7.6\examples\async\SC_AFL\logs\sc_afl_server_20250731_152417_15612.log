2025-07-31 15:24:17,572 - INFO - 🚀 SC-AFL服务器启动 - 2025-07-31 15:24:17
2025-07-31 15:24:17,572 - INFO - ✅ 新日志文件已创建
2025-07-31 15:24:17,572 - INFO - 📁 日志目录: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs
2025-07-31 15:24:17,572 - INFO - 📄 日志文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs\sc_afl_server_20250731_152417_15612.log
2025-07-31 15:24:17,573 - INFO - 🔧 日志级别: DEBUG (文件), INFO (控制台)
2025-07-31 15:24:17,573 - INFO - 📊 文件模式: 新建模式 (每次启动创建新文件)
2025-07-31 15:24:17,574 - INFO - 🆔 进程ID: 15612
2025-07-31 15:24:17,575 - INFO - ✅ 日志文件创建成功，当前大小: 675 字节
2025-07-31 15:24:17,575 - INFO - 🚀 SC-AFL服务器初始化开始...
2025-07-31 15:24:17,575 - INFO - 📅 初始化时间: 2025-07-31 15:24:17
2025-07-31 15:24:17,669 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:17,669 - INFO - Server: 动态创建模型 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-07-31 15:24:17,670 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:17,715 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:17,716 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:17,716 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:17,718 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:17,719 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:17,720 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:17,720 - INFO - Server: 创建了新的Trainer实例
2025-07-31 15:24:17,720 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:17,721 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:17,721 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:17,722 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:17,723 - INFO - Server: 创建了新的Algorithm实例
2025-07-31 15:24:17,724 - INFO - [93m[1m[15612] Logging runtime results to: ./results/cifar10_with_network/15612.csv.[0m
2025-07-31 15:24:17,727 - INFO - [Server #15612] Started training on 100 clients with 20 per round.
2025-07-31 15:24:17,727 - INFO - [DEBUG] 从配置文件读取 simulate_wall_time=True
2025-07-31 15:24:17,728 - WARNING - Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。
2025-07-31 15:24:17,728 - WARNING - Server: 训练器在初始化后为None，重新创建
2025-07-31 15:24:17,729 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:17,729 - WARNING - [Trainer None] 模型为None，尝试创建默认模型
2025-07-31 15:24:17,807 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:17,808 - INFO - [Trainer None] 动态创建模型 resnet_9，输入通道: 3, 类别数: 10
2025-07-31 15:24:17,846 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:17,847 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:17,847 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:17,849 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:17,851 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:17,852 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:17,855 - INFO - Server: 重新创建了Trainer实例
2025-07-31 15:24:17,856 - INFO - [Algorithm] 已设置服务器引用 (客户端ID: None)
2025-07-31 15:24:17,857 - INFO - Server: 算法类已设置服务器引用
2025-07-31 15:24:17,857 - INFO - 动态加载数据集: CIFAR10
2025-07-31 15:24:19,808 - INFO - ✅ 成功加载数据集 CIFAR10: 10000 样本
2025-07-31 15:24:19,809 - INFO - ✅ 动态加载测试集成功: CIFAR10, 大小: 10000
2025-07-31 15:24:19,810 - INFO - ✅ 测试加载器已创建
2025-07-31 15:24:19,810 - INFO - 开始初始化全局模型权重
2025-07-31 15:24:19,811 - WARNING - 全局模型实例为None，正在尝试重新创建...
2025-07-31 15:24:19,866 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:19,866 - INFO - 成功重新创建了全局模型实例 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-07-31 15:24:19,887 - INFO - [全局权重摘要] 参数数量: 74, 均值: 0.001182, 最大: 1.000000, 最小: -0.191645
2025-07-31 15:24:19,888 - INFO - [全局模型] 输入通道数: 3
2025-07-31 15:24:19,888 - INFO - 全局模型权重初始化成功
2025-07-31 15:24:19,893 - DEBUG - Using proactor: IocpProactor
2025-07-31 15:24:19,898 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-07-31 15:24:19,898 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-07-31 15:24:19,900 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-07-31 15:24:19,901 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250731_152419.csv
2025-07-31 15:24:19,903 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-07-31 15:24:19,904 - WARNING - 网络模拟器初始化失败: 'Config' object has no attribute 'get'
2025-07-31 15:24:19,904 - INFO - SC-AFL算法参数: tau_max=5, V=1.0
2025-07-31 15:24:19,905 - INFO - 服务器初始化完成
2025-07-31 15:24:19,905 - INFO - 已创建并注册 0 个客户端（将在 Server.start() 中启动任务）
2025-07-31 15:24:19,906 - INFO - 客户端ID管理器初始化完成，总客户端数: 100, ID起始值: 1
2025-07-31 15:24:19,907 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-07-31 15:24:19,909 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-07-31 15:24:19,911 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-07-31 15:24:19,912 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250731_152419.csv
2025-07-31 15:24:19,914 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-07-31 15:24:19,914 - INFO - 服务器实例创建成功
2025-07-31 15:24:19,914 - INFO - 正在创建和注册 100 个客户端...
2025-07-31 15:24:19,915 - INFO - 客户端ID配置: 起始ID=1, 总数=100
2025-07-31 15:24:19,917 - INFO - 开始创建客户端 1...
2025-07-31 15:24:19,917 - INFO - 初始化客户端, ID: 1
2025-07-31 15:24:19,999 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:20,000 - INFO - [Client 1] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:20,001 - INFO - [Trainer] 初始化训练器, client_id: 1
2025-07-31 15:24:20,024 - INFO - [Trainer 1] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:20,025 - INFO - [Trainer 1] 模型的输入通道数: 3
2025-07-31 15:24:20,025 - INFO - [Trainer 1] 强制使用CPU
2025-07-31 15:24:20,027 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-07-31 15:24:20,028 - INFO - [Trainer 1] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:20,028 - INFO - [Trainer 1] 初始化完成
2025-07-31 15:24:20,029 - INFO - [Client 1] 创建新训练器
2025-07-31 15:24:20,029 - INFO - [Algorithm] 从训练器获取客户端ID: 1
2025-07-31 15:24:20,030 - INFO - [Algorithm] 初始化后修正client_id: 1
2025-07-31 15:24:20,030 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:20,030 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:20,031 - INFO - [Client 1] 创建新算法
2025-07-31 15:24:20,031 - INFO - [Algorithm] 设置客户端ID: 1
2025-07-31 15:24:20,032 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-07-31 15:24:20,032 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:20,104 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:20,105 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:20,105 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:20,131 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:20,144 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:20,144 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:20,148 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:20,149 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:20,158 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:20,158 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:20,159 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:20,160 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:20,161 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:20,161 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:20,161 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:20,161 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:20,161 - INFO - 顺序分配客户端ID: 1
2025-07-31 15:24:20,162 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 1
2025-07-31 15:24:20,166 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:20,168 - WARNING - [Client 1] 数据源为None，已创建新数据源
2025-07-31 15:24:20,168 - WARNING - [Client 1] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:22,627 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:22,627 - INFO - [Client 1] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:22,627 - INFO - [Client 1] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:22,661 - INFO - [Client 1] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:22,662 - INFO - [Client 1] 初始化时成功加载数据
2025-07-31 15:24:22,662 - INFO - [客户端 1] 初始化验证通过
2025-07-31 15:24:22,663 - INFO - 客户端 1 实例创建成功
2025-07-31 15:24:22,663 - INFO - 客户端1已设置服务器引用
2025-07-31 15:24:22,663 - INFO - 客户端 1 已设置服务器引用
2025-07-31 15:24:22,664 - INFO - 客户端1已注册
2025-07-31 15:24:22,665 - INFO - 客户端 1 已成功注册到服务器
2025-07-31 15:24:22,665 - INFO - 开始创建客户端 2...
2025-07-31 15:24:22,665 - INFO - 初始化客户端, ID: 2
2025-07-31 15:24:22,733 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:22,734 - INFO - [Client 2] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:22,735 - INFO - [Trainer] 初始化训练器, client_id: 2
2025-07-31 15:24:22,761 - INFO - [Trainer 2] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:22,762 - INFO - [Trainer 2] 模型的输入通道数: 3
2025-07-31 15:24:22,762 - INFO - [Trainer 2] 强制使用CPU
2025-07-31 15:24:22,764 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-07-31 15:24:22,765 - INFO - [Trainer 2] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:22,766 - INFO - [Trainer 2] 初始化完成
2025-07-31 15:24:22,766 - INFO - [Client 2] 创建新训练器
2025-07-31 15:24:22,767 - INFO - [Algorithm] 从训练器获取客户端ID: 2
2025-07-31 15:24:22,768 - INFO - [Algorithm] 初始化后修正client_id: 2
2025-07-31 15:24:22,768 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:22,768 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:22,769 - INFO - [Client 2] 创建新算法
2025-07-31 15:24:22,769 - INFO - [Algorithm] 设置客户端ID: 2
2025-07-31 15:24:22,769 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-07-31 15:24:22,770 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:22,853 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:22,854 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:22,854 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:22,879 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:22,880 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:22,881 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:22,883 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:22,885 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:22,886 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:22,886 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:22,887 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:22,888 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:22,889 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:22,889 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:22,890 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:22,890 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:22,890 - INFO - 顺序分配客户端ID: 2
2025-07-31 15:24:22,891 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 2
2025-07-31 15:24:22,892 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:22,893 - WARNING - [Client 2] 数据源为None，已创建新数据源
2025-07-31 15:24:22,893 - WARNING - [Client 2] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:25,300 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:25,301 - INFO - [Client 2] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:25,301 - INFO - [Client 2] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:25,313 - INFO - [Client 2] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:25,314 - INFO - [Client 2] 初始化时成功加载数据
2025-07-31 15:24:25,314 - INFO - [客户端 2] 初始化验证通过
2025-07-31 15:24:25,314 - INFO - 客户端 2 实例创建成功
2025-07-31 15:24:25,315 - INFO - 客户端2已设置服务器引用
2025-07-31 15:24:25,315 - INFO - 客户端 2 已设置服务器引用
2025-07-31 15:24:25,316 - INFO - 客户端2已注册
2025-07-31 15:24:25,316 - INFO - 客户端 2 已成功注册到服务器
2025-07-31 15:24:25,316 - INFO - 开始创建客户端 3...
2025-07-31 15:24:25,317 - INFO - 初始化客户端, ID: 3
2025-07-31 15:24:25,383 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:25,383 - INFO - [Client 3] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:25,384 - INFO - [Trainer] 初始化训练器, client_id: 3
2025-07-31 15:24:25,406 - INFO - [Trainer 3] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:25,407 - INFO - [Trainer 3] 模型的输入通道数: 3
2025-07-31 15:24:25,407 - INFO - [Trainer 3] 强制使用CPU
2025-07-31 15:24:25,409 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-07-31 15:24:25,409 - INFO - [Trainer 3] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:25,410 - INFO - [Trainer 3] 初始化完成
2025-07-31 15:24:25,410 - INFO - [Client 3] 创建新训练器
2025-07-31 15:24:25,411 - INFO - [Algorithm] 从训练器获取客户端ID: 3
2025-07-31 15:24:25,411 - INFO - [Algorithm] 初始化后修正client_id: 3
2025-07-31 15:24:25,412 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:25,412 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:25,412 - INFO - [Client 3] 创建新算法
2025-07-31 15:24:25,413 - INFO - [Algorithm] 设置客户端ID: 3
2025-07-31 15:24:25,413 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-07-31 15:24:25,414 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:25,481 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:25,481 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:25,482 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:25,507 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:25,508 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:25,508 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:25,510 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:25,510 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:25,511 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:25,511 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:25,512 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:25,513 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:25,514 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:25,514 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:25,515 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:25,516 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:25,516 - INFO - 顺序分配客户端ID: 3
2025-07-31 15:24:25,516 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 3
2025-07-31 15:24:25,518 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:25,521 - WARNING - [Client 3] 数据源为None，已创建新数据源
2025-07-31 15:24:25,521 - WARNING - [Client 3] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:27,872 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:27,872 - INFO - [Client 3] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:27,873 - INFO - [Client 3] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:27,886 - INFO - [Client 3] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:27,886 - INFO - [Client 3] 初始化时成功加载数据
2025-07-31 15:24:27,887 - INFO - [客户端 3] 初始化验证通过
2025-07-31 15:24:27,887 - INFO - 客户端 3 实例创建成功
2025-07-31 15:24:27,888 - INFO - 客户端3已设置服务器引用
2025-07-31 15:24:27,888 - INFO - 客户端 3 已设置服务器引用
2025-07-31 15:24:27,888 - INFO - 客户端3已注册
2025-07-31 15:24:27,889 - INFO - 客户端 3 已成功注册到服务器
2025-07-31 15:24:27,889 - INFO - 开始创建客户端 4...
2025-07-31 15:24:27,889 - INFO - 初始化客户端, ID: 4
2025-07-31 15:24:27,953 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:27,953 - INFO - [Client 4] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:27,954 - INFO - [Trainer] 初始化训练器, client_id: 4
2025-07-31 15:24:27,977 - INFO - [Trainer 4] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:27,977 - INFO - [Trainer 4] 模型的输入通道数: 3
2025-07-31 15:24:27,978 - INFO - [Trainer 4] 强制使用CPU
2025-07-31 15:24:27,979 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-07-31 15:24:27,980 - INFO - [Trainer 4] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:27,981 - INFO - [Trainer 4] 初始化完成
2025-07-31 15:24:27,981 - INFO - [Client 4] 创建新训练器
2025-07-31 15:24:27,981 - INFO - [Algorithm] 从训练器获取客户端ID: 4
2025-07-31 15:24:27,982 - INFO - [Algorithm] 初始化后修正client_id: 4
2025-07-31 15:24:27,982 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:27,983 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:27,983 - INFO - [Client 4] 创建新算法
2025-07-31 15:24:27,983 - INFO - [Algorithm] 设置客户端ID: 4
2025-07-31 15:24:27,984 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-07-31 15:24:27,984 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:28,092 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:28,093 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:28,094 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:28,123 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:28,124 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:28,125 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:28,127 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:28,129 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:28,130 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:28,130 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:28,131 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:28,132 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:28,133 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:28,133 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:28,134 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:28,135 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:28,136 - INFO - 顺序分配客户端ID: 4
2025-07-31 15:24:28,136 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 4
2025-07-31 15:24:28,138 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:28,139 - WARNING - [Client 4] 数据源为None，已创建新数据源
2025-07-31 15:24:28,140 - WARNING - [Client 4] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:30,325 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:30,326 - INFO - [Client 4] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:30,326 - INFO - [Client 4] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:30,339 - INFO - [Client 4] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:30,339 - INFO - [Client 4] 初始化时成功加载数据
2025-07-31 15:24:30,340 - INFO - [客户端 4] 初始化验证通过
2025-07-31 15:24:30,342 - INFO - 客户端 4 实例创建成功
2025-07-31 15:24:30,343 - INFO - 客户端4已设置服务器引用
2025-07-31 15:24:30,343 - INFO - 客户端 4 已设置服务器引用
2025-07-31 15:24:30,344 - INFO - 客户端4已注册
2025-07-31 15:24:30,344 - INFO - 客户端 4 已成功注册到服务器
2025-07-31 15:24:30,346 - INFO - 开始创建客户端 5...
2025-07-31 15:24:30,347 - INFO - 初始化客户端, ID: 5
2025-07-31 15:24:30,431 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:30,432 - INFO - [Client 5] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:30,433 - INFO - [Trainer] 初始化训练器, client_id: 5
2025-07-31 15:24:30,463 - INFO - [Trainer 5] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:30,463 - INFO - [Trainer 5] 模型的输入通道数: 3
2025-07-31 15:24:30,464 - INFO - [Trainer 5] 强制使用CPU
2025-07-31 15:24:30,465 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-07-31 15:24:30,467 - INFO - [Trainer 5] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:30,467 - INFO - [Trainer 5] 初始化完成
2025-07-31 15:24:30,468 - INFO - [Client 5] 创建新训练器
2025-07-31 15:24:30,468 - INFO - [Algorithm] 从训练器获取客户端ID: 5
2025-07-31 15:24:30,468 - INFO - [Algorithm] 初始化后修正client_id: 5
2025-07-31 15:24:30,469 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:30,470 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:30,471 - INFO - [Client 5] 创建新算法
2025-07-31 15:24:30,471 - INFO - [Algorithm] 设置客户端ID: 5
2025-07-31 15:24:30,472 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-07-31 15:24:30,472 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:30,541 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:30,542 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:30,542 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:30,581 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:30,583 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:30,584 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:30,585 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:30,587 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:30,588 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:30,589 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:30,589 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:30,590 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:30,591 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:30,592 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:30,592 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:30,593 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:30,593 - INFO - 顺序分配客户端ID: 5
2025-07-31 15:24:30,594 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 5
2025-07-31 15:24:30,596 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:30,601 - WARNING - [Client 5] 数据源为None，已创建新数据源
2025-07-31 15:24:30,602 - WARNING - [Client 5] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:32,986 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:32,987 - INFO - [Client 5] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:32,988 - INFO - [Client 5] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:33,000 - INFO - [Client 5] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:33,002 - INFO - [Client 5] 初始化时成功加载数据
2025-07-31 15:24:33,003 - INFO - [客户端 5] 初始化验证通过
2025-07-31 15:24:33,003 - INFO - 客户端 5 实例创建成功
2025-07-31 15:24:33,004 - INFO - 客户端5已设置服务器引用
2025-07-31 15:24:33,005 - INFO - 客户端 5 已设置服务器引用
2025-07-31 15:24:33,006 - INFO - 客户端5已注册
2025-07-31 15:24:33,007 - INFO - 客户端 5 已成功注册到服务器
2025-07-31 15:24:33,008 - INFO - 开始创建客户端 6...
2025-07-31 15:24:33,009 - INFO - 初始化客户端, ID: 6
2025-07-31 15:24:33,078 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:33,079 - INFO - [Client 6] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:33,079 - INFO - [Trainer] 初始化训练器, client_id: 6
2025-07-31 15:24:33,371 - INFO - [Trainer 6] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:33,371 - INFO - [Trainer 6] 模型的输入通道数: 3
2025-07-31 15:24:33,372 - INFO - [Trainer 6] 强制使用CPU
2025-07-31 15:24:33,373 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-07-31 15:24:33,374 - INFO - [Trainer 6] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:33,375 - INFO - [Trainer 6] 初始化完成
2025-07-31 15:24:33,375 - INFO - [Client 6] 创建新训练器
2025-07-31 15:24:33,375 - INFO - [Algorithm] 从训练器获取客户端ID: 6
2025-07-31 15:24:33,376 - INFO - [Algorithm] 初始化后修正client_id: 6
2025-07-31 15:24:33,376 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:33,377 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:33,377 - INFO - [Client 6] 创建新算法
2025-07-31 15:24:33,378 - INFO - [Algorithm] 设置客户端ID: 6
2025-07-31 15:24:33,378 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-07-31 15:24:33,379 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:33,444 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:33,444 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:33,445 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:33,466 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:33,467 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:33,467 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:33,469 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:33,469 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:33,470 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:33,470 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:33,470 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:33,471 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:33,471 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:33,472 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:33,472 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:33,472 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:33,473 - INFO - 顺序分配客户端ID: 6
2025-07-31 15:24:33,473 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 6
2025-07-31 15:24:33,474 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:33,474 - WARNING - [Client 6] 数据源为None，已创建新数据源
2025-07-31 15:24:33,475 - WARNING - [Client 6] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:35,779 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:35,780 - INFO - [Client 6] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:35,781 - INFO - [Client 6] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:35,796 - INFO - [Client 6] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:35,796 - INFO - [Client 6] 初始化时成功加载数据
2025-07-31 15:24:35,797 - INFO - [客户端 6] 初始化验证通过
2025-07-31 15:24:35,797 - INFO - 客户端 6 实例创建成功
2025-07-31 15:24:35,797 - INFO - 客户端6已设置服务器引用
2025-07-31 15:24:35,798 - INFO - 客户端 6 已设置服务器引用
2025-07-31 15:24:35,798 - INFO - 客户端6已注册
2025-07-31 15:24:35,798 - INFO - 客户端 6 已成功注册到服务器
2025-07-31 15:24:35,800 - INFO - 开始创建客户端 7...
2025-07-31 15:24:35,800 - INFO - 初始化客户端, ID: 7
2025-07-31 15:24:35,893 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:35,893 - INFO - [Client 7] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:35,894 - INFO - [Trainer] 初始化训练器, client_id: 7
2025-07-31 15:24:35,916 - INFO - [Trainer 7] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:35,917 - INFO - [Trainer 7] 模型的输入通道数: 3
2025-07-31 15:24:35,917 - INFO - [Trainer 7] 强制使用CPU
2025-07-31 15:24:35,919 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-07-31 15:24:35,920 - INFO - [Trainer 7] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:35,921 - INFO - [Trainer 7] 初始化完成
2025-07-31 15:24:35,921 - INFO - [Client 7] 创建新训练器
2025-07-31 15:24:35,921 - INFO - [Algorithm] 从训练器获取客户端ID: 7
2025-07-31 15:24:35,922 - INFO - [Algorithm] 初始化后修正client_id: 7
2025-07-31 15:24:35,922 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:35,923 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:35,923 - INFO - [Client 7] 创建新算法
2025-07-31 15:24:35,923 - INFO - [Algorithm] 设置客户端ID: 7
2025-07-31 15:24:35,923 - INFO - [Algorithm] 同步更新trainer的client_id: 7
2025-07-31 15:24:35,924 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:35,991 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:35,992 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:35,993 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:36,032 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:36,034 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:36,035 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:36,038 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:36,049 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:36,050 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:36,050 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:36,051 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:36,052 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:36,054 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:36,054 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:36,055 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:36,059 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:36,059 - INFO - 顺序分配客户端ID: 7
2025-07-31 15:24:36,060 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 7
2025-07-31 15:24:36,062 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:36,063 - WARNING - [Client 7] 数据源为None，已创建新数据源
2025-07-31 15:24:36,063 - WARNING - [Client 7] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:38,966 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:38,966 - INFO - [Client 7] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:38,967 - INFO - [Client 7] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:38,978 - INFO - [Client 7] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:38,978 - INFO - [Client 7] 初始化时成功加载数据
2025-07-31 15:24:38,979 - INFO - [客户端 7] 初始化验证通过
2025-07-31 15:24:38,980 - INFO - 客户端 7 实例创建成功
2025-07-31 15:24:38,980 - INFO - 客户端7已设置服务器引用
2025-07-31 15:24:38,980 - INFO - 客户端 7 已设置服务器引用
2025-07-31 15:24:38,981 - INFO - 客户端7已注册
2025-07-31 15:24:38,982 - INFO - 客户端 7 已成功注册到服务器
2025-07-31 15:24:38,982 - INFO - 开始创建客户端 8...
2025-07-31 15:24:38,983 - INFO - 初始化客户端, ID: 8
2025-07-31 15:24:39,042 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:39,043 - INFO - [Client 8] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:39,043 - INFO - [Trainer] 初始化训练器, client_id: 8
2025-07-31 15:24:39,065 - INFO - [Trainer 8] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:39,066 - INFO - [Trainer 8] 模型的输入通道数: 3
2025-07-31 15:24:39,066 - INFO - [Trainer 8] 强制使用CPU
2025-07-31 15:24:39,068 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-07-31 15:24:39,068 - INFO - [Trainer 8] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:39,069 - INFO - [Trainer 8] 初始化完成
2025-07-31 15:24:39,069 - INFO - [Client 8] 创建新训练器
2025-07-31 15:24:39,069 - INFO - [Algorithm] 从训练器获取客户端ID: 8
2025-07-31 15:24:39,070 - INFO - [Algorithm] 初始化后修正client_id: 8
2025-07-31 15:24:39,070 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:39,070 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:39,071 - INFO - [Client 8] 创建新算法
2025-07-31 15:24:39,071 - INFO - [Algorithm] 设置客户端ID: 8
2025-07-31 15:24:39,071 - INFO - [Algorithm] 同步更新trainer的client_id: 8
2025-07-31 15:24:39,072 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:39,134 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:39,135 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:39,135 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:39,160 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:39,160 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:39,161 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:39,162 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:39,163 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:39,163 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:39,163 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:39,164 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:39,164 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:39,164 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:39,165 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:39,165 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:39,165 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:39,166 - INFO - 顺序分配客户端ID: 8
2025-07-31 15:24:39,166 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 8
2025-07-31 15:24:39,167 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:39,168 - WARNING - [Client 8] 数据源为None，已创建新数据源
2025-07-31 15:24:39,168 - WARNING - [Client 8] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:41,080 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:41,081 - INFO - [Client 8] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:41,081 - INFO - [Client 8] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:41,097 - INFO - [Client 8] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:41,097 - INFO - [Client 8] 初始化时成功加载数据
2025-07-31 15:24:41,098 - INFO - [客户端 8] 初始化验证通过
2025-07-31 15:24:41,098 - INFO - 客户端 8 实例创建成功
2025-07-31 15:24:41,099 - INFO - 客户端8已设置服务器引用
2025-07-31 15:24:41,099 - INFO - 客户端 8 已设置服务器引用
2025-07-31 15:24:41,100 - INFO - 客户端8已注册
2025-07-31 15:24:41,100 - INFO - 客户端 8 已成功注册到服务器
2025-07-31 15:24:41,100 - INFO - 开始创建客户端 9...
2025-07-31 15:24:41,101 - INFO - 初始化客户端, ID: 9
2025-07-31 15:24:41,174 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:41,175 - INFO - [Client 9] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:41,175 - INFO - [Trainer] 初始化训练器, client_id: 9
2025-07-31 15:24:41,199 - INFO - [Trainer 9] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:41,199 - INFO - [Trainer 9] 模型的输入通道数: 3
2025-07-31 15:24:41,200 - INFO - [Trainer 9] 强制使用CPU
2025-07-31 15:24:41,201 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-07-31 15:24:41,201 - INFO - [Trainer 9] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:41,203 - INFO - [Trainer 9] 初始化完成
2025-07-31 15:24:41,203 - INFO - [Client 9] 创建新训练器
2025-07-31 15:24:41,203 - INFO - [Algorithm] 从训练器获取客户端ID: 9
2025-07-31 15:24:41,203 - INFO - [Algorithm] 初始化后修正client_id: 9
2025-07-31 15:24:41,203 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:41,205 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:41,205 - INFO - [Client 9] 创建新算法
2025-07-31 15:24:41,205 - INFO - [Algorithm] 设置客户端ID: 9
2025-07-31 15:24:41,205 - INFO - [Algorithm] 同步更新trainer的client_id: 9
2025-07-31 15:24:41,206 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:41,286 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:41,287 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:41,288 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:41,321 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:41,322 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:41,322 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:41,324 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:41,325 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:41,325 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:41,325 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:41,326 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:41,327 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:41,327 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:41,327 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:41,328 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:41,328 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:41,328 - INFO - 顺序分配客户端ID: 9
2025-07-31 15:24:41,329 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 9
2025-07-31 15:24:41,330 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:41,330 - WARNING - [Client 9] 数据源为None，已创建新数据源
2025-07-31 15:24:41,331 - WARNING - [Client 9] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:43,892 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:43,892 - INFO - [Client 9] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:43,893 - INFO - [Client 9] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:43,907 - INFO - [Client 9] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:43,907 - INFO - [Client 9] 初始化时成功加载数据
2025-07-31 15:24:43,908 - INFO - [客户端 9] 初始化验证通过
2025-07-31 15:24:43,908 - INFO - 客户端 9 实例创建成功
2025-07-31 15:24:43,908 - INFO - 客户端9已设置服务器引用
2025-07-31 15:24:43,909 - INFO - 客户端 9 已设置服务器引用
2025-07-31 15:24:43,909 - INFO - 客户端9已注册
2025-07-31 15:24:43,910 - INFO - 客户端 9 已成功注册到服务器
2025-07-31 15:24:43,910 - INFO - 开始创建客户端 10...
2025-07-31 15:24:43,910 - INFO - 初始化客户端, ID: 10
2025-07-31 15:24:43,991 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:43,992 - INFO - [Client 10] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:43,997 - INFO - [Trainer] 初始化训练器, client_id: 10
2025-07-31 15:24:44,034 - INFO - [Trainer 10] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:44,035 - INFO - [Trainer 10] 模型的输入通道数: 3
2025-07-31 15:24:44,035 - INFO - [Trainer 10] 强制使用CPU
2025-07-31 15:24:44,038 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-07-31 15:24:44,040 - INFO - [Trainer 10] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:44,041 - INFO - [Trainer 10] 初始化完成
2025-07-31 15:24:44,041 - INFO - [Client 10] 创建新训练器
2025-07-31 15:24:44,042 - INFO - [Algorithm] 从训练器获取客户端ID: 10
2025-07-31 15:24:44,043 - INFO - [Algorithm] 初始化后修正client_id: 10
2025-07-31 15:24:44,044 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:44,044 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:44,045 - INFO - [Client 10] 创建新算法
2025-07-31 15:24:44,045 - INFO - [Algorithm] 设置客户端ID: 10
2025-07-31 15:24:44,046 - INFO - [Algorithm] 同步更新trainer的client_id: 10
2025-07-31 15:24:44,046 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:44,158 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:44,159 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:44,160 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:44,188 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:44,189 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:44,189 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:44,191 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:44,192 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:44,192 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:44,192 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:44,193 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:44,193 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:44,194 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:44,194 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:44,195 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:44,195 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:44,196 - INFO - 顺序分配客户端ID: 10
2025-07-31 15:24:44,196 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 10
2025-07-31 15:24:44,197 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:44,198 - WARNING - [Client 10] 数据源为None，已创建新数据源
2025-07-31 15:24:44,199 - WARNING - [Client 10] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:46,374 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:46,376 - INFO - [Client 10] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:46,376 - INFO - [Client 10] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:46,390 - INFO - [Client 10] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:46,394 - INFO - [Client 10] 初始化时成功加载数据
2025-07-31 15:24:46,395 - INFO - [客户端 10] 初始化验证通过
2025-07-31 15:24:46,396 - INFO - 客户端 10 实例创建成功
2025-07-31 15:24:46,397 - INFO - 客户端10已设置服务器引用
2025-07-31 15:24:46,397 - INFO - 客户端 10 已设置服务器引用
2025-07-31 15:24:46,398 - INFO - 客户端10已注册
2025-07-31 15:24:46,398 - INFO - 客户端 10 已成功注册到服务器
2025-07-31 15:24:46,399 - INFO - 开始创建客户端 11...
2025-07-31 15:24:46,400 - INFO - 初始化客户端, ID: 11
2025-07-31 15:24:46,482 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:46,483 - INFO - [Client 11] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:46,483 - INFO - [Trainer] 初始化训练器, client_id: 11
2025-07-31 15:24:46,523 - INFO - [Trainer 11] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:46,523 - INFO - [Trainer 11] 模型的输入通道数: 3
2025-07-31 15:24:46,524 - INFO - [Trainer 11] 强制使用CPU
2025-07-31 15:24:46,526 - INFO - [Trainer 11] 模型已移至设备: cpu
2025-07-31 15:24:46,526 - INFO - [Trainer 11] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:46,529 - INFO - [Trainer 11] 初始化完成
2025-07-31 15:24:46,529 - INFO - [Client 11] 创建新训练器
2025-07-31 15:24:46,530 - INFO - [Algorithm] 从训练器获取客户端ID: 11
2025-07-31 15:24:46,531 - INFO - [Algorithm] 初始化后修正client_id: 11
2025-07-31 15:24:46,531 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:46,532 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:46,533 - INFO - [Client 11] 创建新算法
2025-07-31 15:24:46,534 - INFO - [Algorithm] 设置客户端ID: 11
2025-07-31 15:24:46,534 - INFO - [Algorithm] 同步更新trainer的client_id: 11
2025-07-31 15:24:46,536 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:46,609 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:46,610 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:46,610 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:46,632 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:46,632 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:46,633 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:46,634 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:46,635 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:46,636 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:46,636 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:46,636 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:46,637 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:46,637 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:46,638 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:46,638 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:46,639 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:46,639 - INFO - 顺序分配客户端ID: 11
2025-07-31 15:24:46,640 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 11
2025-07-31 15:24:46,642 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:46,642 - WARNING - [Client 11] 数据源为None，已创建新数据源
2025-07-31 15:24:46,643 - WARNING - [Client 11] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:48,782 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:48,785 - INFO - [Client 11] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:48,787 - INFO - [Client 11] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:48,805 - INFO - [Client 11] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:48,805 - INFO - [Client 11] 初始化时成功加载数据
2025-07-31 15:24:48,805 - INFO - [客户端 11] 初始化验证通过
2025-07-31 15:24:48,807 - INFO - 客户端 11 实例创建成功
2025-07-31 15:24:48,807 - INFO - 客户端11已设置服务器引用
2025-07-31 15:24:48,807 - INFO - 客户端 11 已设置服务器引用
2025-07-31 15:24:48,808 - INFO - 客户端11已注册
2025-07-31 15:24:48,809 - INFO - 客户端 11 已成功注册到服务器
2025-07-31 15:24:48,809 - INFO - 开始创建客户端 12...
2025-07-31 15:24:48,810 - INFO - 初始化客户端, ID: 12
2025-07-31 15:24:48,881 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:48,881 - INFO - [Client 12] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:48,881 - INFO - [Trainer] 初始化训练器, client_id: 12
2025-07-31 15:24:48,903 - INFO - [Trainer 12] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:48,904 - INFO - [Trainer 12] 模型的输入通道数: 3
2025-07-31 15:24:48,905 - INFO - [Trainer 12] 强制使用CPU
2025-07-31 15:24:48,908 - INFO - [Trainer 12] 模型已移至设备: cpu
2025-07-31 15:24:48,909 - INFO - [Trainer 12] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:48,910 - INFO - [Trainer 12] 初始化完成
2025-07-31 15:24:48,910 - INFO - [Client 12] 创建新训练器
2025-07-31 15:24:48,911 - INFO - [Algorithm] 从训练器获取客户端ID: 12
2025-07-31 15:24:48,911 - INFO - [Algorithm] 初始化后修正client_id: 12
2025-07-31 15:24:48,911 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:48,912 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:48,912 - INFO - [Client 12] 创建新算法
2025-07-31 15:24:48,912 - INFO - [Algorithm] 设置客户端ID: 12
2025-07-31 15:24:48,913 - INFO - [Algorithm] 同步更新trainer的client_id: 12
2025-07-31 15:24:48,913 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:49,001 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:49,002 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:49,002 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:49,030 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:49,030 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:49,031 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:49,033 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:49,034 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:49,035 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:49,035 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:49,035 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:49,036 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:49,036 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:49,037 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:49,037 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:49,037 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:49,037 - INFO - 顺序分配客户端ID: 12
2025-07-31 15:24:49,038 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 12
2025-07-31 15:24:49,039 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:49,040 - WARNING - [Client 12] 数据源为None，已创建新数据源
2025-07-31 15:24:49,040 - WARNING - [Client 12] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:51,375 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:51,377 - INFO - [Client 12] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:51,377 - INFO - [Client 12] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:51,399 - INFO - [Client 12] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:51,401 - INFO - [Client 12] 初始化时成功加载数据
2025-07-31 15:24:51,401 - INFO - [客户端 12] 初始化验证通过
2025-07-31 15:24:51,402 - INFO - 客户端 12 实例创建成功
2025-07-31 15:24:51,402 - INFO - 客户端12已设置服务器引用
2025-07-31 15:24:51,403 - INFO - 客户端 12 已设置服务器引用
2025-07-31 15:24:51,403 - INFO - 客户端12已注册
2025-07-31 15:24:51,404 - INFO - 客户端 12 已成功注册到服务器
2025-07-31 15:24:51,405 - INFO - 开始创建客户端 13...
2025-07-31 15:24:51,408 - INFO - 初始化客户端, ID: 13
2025-07-31 15:24:51,480 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:51,481 - INFO - [Client 13] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:51,482 - INFO - [Trainer] 初始化训练器, client_id: 13
2025-07-31 15:24:51,519 - INFO - [Trainer 13] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:51,520 - INFO - [Trainer 13] 模型的输入通道数: 3
2025-07-31 15:24:51,521 - INFO - [Trainer 13] 强制使用CPU
2025-07-31 15:24:51,522 - INFO - [Trainer 13] 模型已移至设备: cpu
2025-07-31 15:24:51,524 - INFO - [Trainer 13] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:51,524 - INFO - [Trainer 13] 初始化完成
2025-07-31 15:24:51,525 - INFO - [Client 13] 创建新训练器
2025-07-31 15:24:51,525 - INFO - [Algorithm] 从训练器获取客户端ID: 13
2025-07-31 15:24:51,525 - INFO - [Algorithm] 初始化后修正client_id: 13
2025-07-31 15:24:51,526 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:51,526 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:51,526 - INFO - [Client 13] 创建新算法
2025-07-31 15:24:51,526 - INFO - [Algorithm] 设置客户端ID: 13
2025-07-31 15:24:51,527 - INFO - [Algorithm] 同步更新trainer的client_id: 13
2025-07-31 15:24:51,539 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:51,645 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:51,646 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:51,646 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:51,704 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:51,705 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:51,705 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:51,706 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:51,707 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:51,708 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:51,708 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:51,709 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:51,709 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:51,709 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:51,710 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:51,710 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:51,711 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:51,711 - INFO - 顺序分配客户端ID: 13
2025-07-31 15:24:51,711 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 13
2025-07-31 15:24:51,714 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:51,714 - WARNING - [Client 13] 数据源为None，已创建新数据源
2025-07-31 15:24:51,715 - WARNING - [Client 13] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:53,737 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:53,737 - INFO - [Client 13] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:53,738 - INFO - [Client 13] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:53,755 - INFO - [Client 13] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:53,755 - INFO - [Client 13] 初始化时成功加载数据
2025-07-31 15:24:53,755 - INFO - [客户端 13] 初始化验证通过
2025-07-31 15:24:53,757 - INFO - 客户端 13 实例创建成功
2025-07-31 15:24:53,757 - INFO - 客户端13已设置服务器引用
2025-07-31 15:24:53,758 - INFO - 客户端 13 已设置服务器引用
2025-07-31 15:24:53,759 - INFO - 客户端13已注册
2025-07-31 15:24:53,760 - INFO - 客户端 13 已成功注册到服务器
2025-07-31 15:24:53,761 - INFO - 开始创建客户端 14...
2025-07-31 15:24:53,762 - INFO - 初始化客户端, ID: 14
2025-07-31 15:24:53,845 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:53,845 - INFO - [Client 14] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:53,847 - INFO - [Trainer] 初始化训练器, client_id: 14
2025-07-31 15:24:53,879 - INFO - [Trainer 14] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:53,881 - INFO - [Trainer 14] 模型的输入通道数: 3
2025-07-31 15:24:53,882 - INFO - [Trainer 14] 强制使用CPU
2025-07-31 15:24:53,884 - INFO - [Trainer 14] 模型已移至设备: cpu
2025-07-31 15:24:53,885 - INFO - [Trainer 14] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:53,886 - INFO - [Trainer 14] 初始化完成
2025-07-31 15:24:53,886 - INFO - [Client 14] 创建新训练器
2025-07-31 15:24:53,887 - INFO - [Algorithm] 从训练器获取客户端ID: 14
2025-07-31 15:24:53,889 - INFO - [Algorithm] 初始化后修正client_id: 14
2025-07-31 15:24:53,890 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:53,891 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:53,891 - INFO - [Client 14] 创建新算法
2025-07-31 15:24:53,892 - INFO - [Algorithm] 设置客户端ID: 14
2025-07-31 15:24:53,892 - INFO - [Algorithm] 同步更新trainer的client_id: 14
2025-07-31 15:24:53,893 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:53,985 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:53,986 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:53,986 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:54,024 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:54,025 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:54,025 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:54,028 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:54,030 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:54,030 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:54,031 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:54,032 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:54,032 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:54,033 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:54,033 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:54,034 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:54,035 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:54,035 - INFO - 顺序分配客户端ID: 14
2025-07-31 15:24:54,035 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 14
2025-07-31 15:24:54,039 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:54,041 - WARNING - [Client 14] 数据源为None，已创建新数据源
2025-07-31 15:24:54,041 - WARNING - [Client 14] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:56,305 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:56,307 - INFO - [Client 14] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:56,307 - INFO - [Client 14] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:56,324 - INFO - [Client 14] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:56,325 - INFO - [Client 14] 初始化时成功加载数据
2025-07-31 15:24:56,326 - INFO - [客户端 14] 初始化验证通过
2025-07-31 15:24:56,326 - INFO - 客户端 14 实例创建成功
2025-07-31 15:24:56,326 - INFO - 客户端14已设置服务器引用
2025-07-31 15:24:56,327 - INFO - 客户端 14 已设置服务器引用
2025-07-31 15:24:56,328 - INFO - 客户端14已注册
2025-07-31 15:24:56,328 - INFO - 客户端 14 已成功注册到服务器
2025-07-31 15:24:56,330 - INFO - 开始创建客户端 15...
2025-07-31 15:24:56,330 - INFO - 初始化客户端, ID: 15
2025-07-31 15:24:56,424 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:56,425 - INFO - [Client 15] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:56,425 - INFO - [Trainer] 初始化训练器, client_id: 15
2025-07-31 15:24:56,460 - INFO - [Trainer 15] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:56,461 - INFO - [Trainer 15] 模型的输入通道数: 3
2025-07-31 15:24:56,462 - INFO - [Trainer 15] 强制使用CPU
2025-07-31 15:24:56,463 - INFO - [Trainer 15] 模型已移至设备: cpu
2025-07-31 15:24:56,464 - INFO - [Trainer 15] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:56,465 - INFO - [Trainer 15] 初始化完成
2025-07-31 15:24:56,466 - INFO - [Client 15] 创建新训练器
2025-07-31 15:24:56,467 - INFO - [Algorithm] 从训练器获取客户端ID: 15
2025-07-31 15:24:56,468 - INFO - [Algorithm] 初始化后修正client_id: 15
2025-07-31 15:24:56,469 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:56,470 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:56,470 - INFO - [Client 15] 创建新算法
2025-07-31 15:24:56,471 - INFO - [Algorithm] 设置客户端ID: 15
2025-07-31 15:24:56,472 - INFO - [Algorithm] 同步更新trainer的client_id: 15
2025-07-31 15:24:56,473 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:56,579 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:56,580 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:56,581 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:56,616 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:56,618 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:56,618 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:56,622 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:56,624 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:56,625 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:56,625 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:56,625 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:56,626 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:56,626 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:56,627 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:56,627 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:56,627 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:56,627 - INFO - 顺序分配客户端ID: 15
2025-07-31 15:24:56,628 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 15
2025-07-31 15:24:56,630 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:56,631 - WARNING - [Client 15] 数据源为None，已创建新数据源
2025-07-31 15:24:56,632 - WARNING - [Client 15] 数据源trainset为None，已设置为空列表
2025-07-31 15:24:58,910 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:24:58,911 - INFO - [Client 15] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:24:58,912 - INFO - [Client 15] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:24:58,926 - INFO - [Client 15] 成功划分数据集，分配到 300 个样本
2025-07-31 15:24:58,927 - INFO - [Client 15] 初始化时成功加载数据
2025-07-31 15:24:58,927 - INFO - [客户端 15] 初始化验证通过
2025-07-31 15:24:58,927 - INFO - 客户端 15 实例创建成功
2025-07-31 15:24:58,928 - INFO - 客户端15已设置服务器引用
2025-07-31 15:24:58,928 - INFO - 客户端 15 已设置服务器引用
2025-07-31 15:24:58,929 - INFO - 客户端15已注册
2025-07-31 15:24:58,930 - INFO - 客户端 15 已成功注册到服务器
2025-07-31 15:24:58,930 - INFO - 开始创建客户端 16...
2025-07-31 15:24:58,931 - INFO - 初始化客户端, ID: 16
2025-07-31 15:24:59,013 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:59,014 - INFO - [Client 16] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:59,015 - INFO - [Trainer] 初始化训练器, client_id: 16
2025-07-31 15:24:59,041 - INFO - [Trainer 16] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:59,056 - INFO - [Trainer 16] 模型的输入通道数: 3
2025-07-31 15:24:59,056 - INFO - [Trainer 16] 强制使用CPU
2025-07-31 15:24:59,059 - INFO - [Trainer 16] 模型已移至设备: cpu
2025-07-31 15:24:59,060 - INFO - [Trainer 16] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:59,061 - INFO - [Trainer 16] 初始化完成
2025-07-31 15:24:59,061 - INFO - [Client 16] 创建新训练器
2025-07-31 15:24:59,063 - INFO - [Algorithm] 从训练器获取客户端ID: 16
2025-07-31 15:24:59,064 - INFO - [Algorithm] 初始化后修正client_id: 16
2025-07-31 15:24:59,065 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:59,067 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:59,068 - INFO - [Client 16] 创建新算法
2025-07-31 15:24:59,069 - INFO - [Algorithm] 设置客户端ID: 16
2025-07-31 15:24:59,069 - INFO - [Algorithm] 同步更新trainer的client_id: 16
2025-07-31 15:24:59,071 - INFO - [Client None] 父类初始化完成
2025-07-31 15:24:59,161 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:24:59,162 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:24:59,162 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:24:59,190 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:24:59,191 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:24:59,192 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:24:59,195 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:24:59,195 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:24:59,197 - INFO - [Trainer None] 初始化完成
2025-07-31 15:24:59,198 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:24:59,198 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:24:59,199 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:24:59,199 - INFO - [Algorithm] 初始化完成
2025-07-31 15:24:59,200 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:24:59,201 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:24:59,202 - INFO - [Client None] 开始加载数据
2025-07-31 15:24:59,206 - INFO - 顺序分配客户端ID: 16
2025-07-31 15:24:59,207 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 16
2025-07-31 15:24:59,209 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:24:59,209 - WARNING - [Client 16] 数据源为None，已创建新数据源
2025-07-31 15:24:59,210 - WARNING - [Client 16] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:01,617 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:01,618 - INFO - [Client 16] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:01,619 - INFO - [Client 16] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:01,634 - INFO - [Client 16] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:01,636 - INFO - [Client 16] 初始化时成功加载数据
2025-07-31 15:25:01,636 - INFO - [客户端 16] 初始化验证通过
2025-07-31 15:25:01,637 - INFO - 客户端 16 实例创建成功
2025-07-31 15:25:01,637 - INFO - 客户端16已设置服务器引用
2025-07-31 15:25:01,637 - INFO - 客户端 16 已设置服务器引用
2025-07-31 15:25:01,638 - INFO - 客户端16已注册
2025-07-31 15:25:01,639 - INFO - 客户端 16 已成功注册到服务器
2025-07-31 15:25:01,641 - INFO - 开始创建客户端 17...
2025-07-31 15:25:01,642 - INFO - 初始化客户端, ID: 17
2025-07-31 15:25:01,732 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:01,732 - INFO - [Client 17] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:01,733 - INFO - [Trainer] 初始化训练器, client_id: 17
2025-07-31 15:25:01,769 - INFO - [Trainer 17] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:01,771 - INFO - [Trainer 17] 模型的输入通道数: 3
2025-07-31 15:25:01,771 - INFO - [Trainer 17] 强制使用CPU
2025-07-31 15:25:01,774 - INFO - [Trainer 17] 模型已移至设备: cpu
2025-07-31 15:25:01,775 - INFO - [Trainer 17] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:01,775 - INFO - [Trainer 17] 初始化完成
2025-07-31 15:25:01,777 - INFO - [Client 17] 创建新训练器
2025-07-31 15:25:01,777 - INFO - [Algorithm] 从训练器获取客户端ID: 17
2025-07-31 15:25:01,777 - INFO - [Algorithm] 初始化后修正client_id: 17
2025-07-31 15:25:01,778 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:01,779 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:01,780 - INFO - [Client 17] 创建新算法
2025-07-31 15:25:01,780 - INFO - [Algorithm] 设置客户端ID: 17
2025-07-31 15:25:01,782 - INFO - [Algorithm] 同步更新trainer的client_id: 17
2025-07-31 15:25:01,783 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:01,885 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:01,886 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:01,887 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:01,924 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:01,925 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:01,926 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:01,929 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:01,931 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:01,932 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:01,932 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:01,933 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:01,933 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:01,933 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:01,934 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:01,935 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:01,935 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:01,935 - INFO - 顺序分配客户端ID: 17
2025-07-31 15:25:01,937 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 17
2025-07-31 15:25:01,941 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:01,942 - WARNING - [Client 17] 数据源为None，已创建新数据源
2025-07-31 15:25:01,942 - WARNING - [Client 17] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:04,489 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:04,490 - INFO - [Client 17] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:04,490 - INFO - [Client 17] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:04,508 - INFO - [Client 17] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:04,510 - INFO - [Client 17] 初始化时成功加载数据
2025-07-31 15:25:04,510 - INFO - [客户端 17] 初始化验证通过
2025-07-31 15:25:04,511 - INFO - 客户端 17 实例创建成功
2025-07-31 15:25:04,513 - INFO - 客户端17已设置服务器引用
2025-07-31 15:25:04,513 - INFO - 客户端 17 已设置服务器引用
2025-07-31 15:25:04,513 - INFO - 客户端17已注册
2025-07-31 15:25:04,514 - INFO - 客户端 17 已成功注册到服务器
2025-07-31 15:25:04,516 - INFO - 开始创建客户端 18...
2025-07-31 15:25:04,517 - INFO - 初始化客户端, ID: 18
2025-07-31 15:25:04,605 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:04,607 - INFO - [Client 18] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:04,609 - INFO - [Trainer] 初始化训练器, client_id: 18
2025-07-31 15:25:04,647 - INFO - [Trainer 18] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:04,648 - INFO - [Trainer 18] 模型的输入通道数: 3
2025-07-31 15:25:04,651 - INFO - [Trainer 18] 强制使用CPU
2025-07-31 15:25:04,655 - INFO - [Trainer 18] 模型已移至设备: cpu
2025-07-31 15:25:04,655 - INFO - [Trainer 18] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:04,657 - INFO - [Trainer 18] 初始化完成
2025-07-31 15:25:04,658 - INFO - [Client 18] 创建新训练器
2025-07-31 15:25:04,658 - INFO - [Algorithm] 从训练器获取客户端ID: 18
2025-07-31 15:25:04,658 - INFO - [Algorithm] 初始化后修正client_id: 18
2025-07-31 15:25:04,659 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:04,661 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:04,662 - INFO - [Client 18] 创建新算法
2025-07-31 15:25:04,662 - INFO - [Algorithm] 设置客户端ID: 18
2025-07-31 15:25:04,662 - INFO - [Algorithm] 同步更新trainer的client_id: 18
2025-07-31 15:25:04,663 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:04,769 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:04,771 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:04,772 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:04,816 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:04,817 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:04,817 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:04,820 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:04,824 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:04,824 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:04,825 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:04,825 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:04,826 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:04,826 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:04,826 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:04,827 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:04,827 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:04,827 - INFO - 顺序分配客户端ID: 18
2025-07-31 15:25:04,828 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 18
2025-07-31 15:25:04,829 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:04,831 - WARNING - [Client 18] 数据源为None，已创建新数据源
2025-07-31 15:25:04,831 - WARNING - [Client 18] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:07,340 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:07,341 - INFO - [Client 18] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:07,343 - INFO - [Client 18] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:07,361 - INFO - [Client 18] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:07,363 - INFO - [Client 18] 初始化时成功加载数据
2025-07-31 15:25:07,364 - INFO - [客户端 18] 初始化验证通过
2025-07-31 15:25:07,364 - INFO - 客户端 18 实例创建成功
2025-07-31 15:25:07,365 - INFO - 客户端18已设置服务器引用
2025-07-31 15:25:07,365 - INFO - 客户端 18 已设置服务器引用
2025-07-31 15:25:07,367 - INFO - 客户端18已注册
2025-07-31 15:25:07,369 - INFO - 客户端 18 已成功注册到服务器
2025-07-31 15:25:07,369 - INFO - 开始创建客户端 19...
2025-07-31 15:25:07,370 - INFO - 初始化客户端, ID: 19
2025-07-31 15:25:07,452 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:07,453 - INFO - [Client 19] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:07,453 - INFO - [Trainer] 初始化训练器, client_id: 19
2025-07-31 15:25:07,492 - INFO - [Trainer 19] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:07,493 - INFO - [Trainer 19] 模型的输入通道数: 3
2025-07-31 15:25:07,494 - INFO - [Trainer 19] 强制使用CPU
2025-07-31 15:25:07,497 - INFO - [Trainer 19] 模型已移至设备: cpu
2025-07-31 15:25:07,498 - INFO - [Trainer 19] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:07,499 - INFO - [Trainer 19] 初始化完成
2025-07-31 15:25:07,500 - INFO - [Client 19] 创建新训练器
2025-07-31 15:25:07,501 - INFO - [Algorithm] 从训练器获取客户端ID: 19
2025-07-31 15:25:07,501 - INFO - [Algorithm] 初始化后修正client_id: 19
2025-07-31 15:25:07,501 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:07,502 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:07,502 - INFO - [Client 19] 创建新算法
2025-07-31 15:25:07,505 - INFO - [Algorithm] 设置客户端ID: 19
2025-07-31 15:25:07,507 - INFO - [Algorithm] 同步更新trainer的client_id: 19
2025-07-31 15:25:07,508 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:07,633 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:07,634 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:07,635 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:07,693 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:07,694 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:07,695 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:07,698 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:07,699 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:07,700 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:07,701 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:07,701 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:07,702 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:07,702 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:07,703 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:07,703 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:07,703 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:07,704 - INFO - 顺序分配客户端ID: 19
2025-07-31 15:25:07,704 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 19
2025-07-31 15:25:07,705 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:07,706 - WARNING - [Client 19] 数据源为None，已创建新数据源
2025-07-31 15:25:07,706 - WARNING - [Client 19] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:10,115 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:10,116 - INFO - [Client 19] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:10,117 - INFO - [Client 19] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:10,131 - INFO - [Client 19] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:10,132 - INFO - [Client 19] 初始化时成功加载数据
2025-07-31 15:25:10,133 - INFO - [客户端 19] 初始化验证通过
2025-07-31 15:25:10,133 - INFO - 客户端 19 实例创建成功
2025-07-31 15:25:10,133 - INFO - 客户端19已设置服务器引用
2025-07-31 15:25:10,134 - INFO - 客户端 19 已设置服务器引用
2025-07-31 15:25:10,134 - INFO - 客户端19已注册
2025-07-31 15:25:10,135 - INFO - 客户端 19 已成功注册到服务器
2025-07-31 15:25:10,135 - INFO - 开始创建客户端 20...
2025-07-31 15:25:10,135 - INFO - 初始化客户端, ID: 20
2025-07-31 15:25:10,229 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:10,229 - INFO - [Client 20] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:10,230 - INFO - [Trainer] 初始化训练器, client_id: 20
2025-07-31 15:25:10,261 - INFO - [Trainer 20] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:10,262 - INFO - [Trainer 20] 模型的输入通道数: 3
2025-07-31 15:25:10,263 - INFO - [Trainer 20] 强制使用CPU
2025-07-31 15:25:10,266 - INFO - [Trainer 20] 模型已移至设备: cpu
2025-07-31 15:25:10,268 - INFO - [Trainer 20] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:10,269 - INFO - [Trainer 20] 初始化完成
2025-07-31 15:25:10,270 - INFO - [Client 20] 创建新训练器
2025-07-31 15:25:10,271 - INFO - [Algorithm] 从训练器获取客户端ID: 20
2025-07-31 15:25:10,271 - INFO - [Algorithm] 初始化后修正client_id: 20
2025-07-31 15:25:10,272 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:10,273 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:10,273 - INFO - [Client 20] 创建新算法
2025-07-31 15:25:10,274 - INFO - [Algorithm] 设置客户端ID: 20
2025-07-31 15:25:10,275 - INFO - [Algorithm] 同步更新trainer的client_id: 20
2025-07-31 15:25:10,275 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:10,352 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:10,353 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:10,353 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:10,378 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:10,379 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:10,380 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:10,382 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:10,383 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:10,383 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:10,384 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:10,384 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:10,385 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:10,385 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:10,385 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:10,386 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:10,386 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:10,386 - INFO - 顺序分配客户端ID: 20
2025-07-31 15:25:10,386 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 20
2025-07-31 15:25:10,387 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:10,389 - WARNING - [Client 20] 数据源为None，已创建新数据源
2025-07-31 15:25:10,389 - WARNING - [Client 20] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:12,759 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:12,759 - INFO - [Client 20] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:12,760 - INFO - [Client 20] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:12,785 - INFO - [Client 20] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:12,785 - INFO - [Client 20] 初始化时成功加载数据
2025-07-31 15:25:12,787 - INFO - [客户端 20] 初始化验证通过
2025-07-31 15:25:12,787 - INFO - 客户端 20 实例创建成功
2025-07-31 15:25:12,788 - INFO - 客户端20已设置服务器引用
2025-07-31 15:25:12,788 - INFO - 客户端 20 已设置服务器引用
2025-07-31 15:25:12,788 - INFO - 客户端20已注册
2025-07-31 15:25:12,789 - INFO - 客户端 20 已成功注册到服务器
2025-07-31 15:25:12,789 - INFO - 开始创建客户端 21...
2025-07-31 15:25:12,789 - INFO - 初始化客户端, ID: 21
2025-07-31 15:25:12,879 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:12,880 - INFO - [Client 21] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:12,880 - INFO - [Trainer] 初始化训练器, client_id: 21
2025-07-31 15:25:12,910 - INFO - [Trainer 21] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:12,910 - INFO - [Trainer 21] 模型的输入通道数: 3
2025-07-31 15:25:12,911 - INFO - [Trainer 21] 强制使用CPU
2025-07-31 15:25:12,912 - INFO - [Trainer 21] 模型已移至设备: cpu
2025-07-31 15:25:12,913 - INFO - [Trainer 21] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:12,913 - INFO - [Trainer 21] 初始化完成
2025-07-31 15:25:12,914 - INFO - [Client 21] 创建新训练器
2025-07-31 15:25:12,914 - INFO - [Algorithm] 从训练器获取客户端ID: 21
2025-07-31 15:25:12,915 - INFO - [Algorithm] 初始化后修正client_id: 21
2025-07-31 15:25:12,915 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:12,915 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:12,915 - INFO - [Client 21] 创建新算法
2025-07-31 15:25:12,917 - INFO - [Algorithm] 设置客户端ID: 21
2025-07-31 15:25:12,917 - INFO - [Algorithm] 同步更新trainer的client_id: 21
2025-07-31 15:25:12,921 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:13,013 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:13,013 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:13,013 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:13,050 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:13,050 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:13,051 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:13,052 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:13,053 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:13,054 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:13,054 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:13,054 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:13,056 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:13,056 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:13,057 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:13,058 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:13,058 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:13,058 - INFO - 顺序分配客户端ID: 21
2025-07-31 15:25:13,059 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 21
2025-07-31 15:25:13,060 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:13,062 - WARNING - [Client 21] 数据源为None，已创建新数据源
2025-07-31 15:25:13,062 - WARNING - [Client 21] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:15,325 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:15,327 - INFO - [Client 21] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:15,328 - INFO - [Client 21] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:15,346 - INFO - [Client 21] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:15,347 - INFO - [Client 21] 初始化时成功加载数据
2025-07-31 15:25:15,349 - INFO - [客户端 21] 初始化验证通过
2025-07-31 15:25:15,350 - INFO - 客户端 21 实例创建成功
2025-07-31 15:25:15,350 - INFO - 客户端21已设置服务器引用
2025-07-31 15:25:15,350 - INFO - 客户端 21 已设置服务器引用
2025-07-31 15:25:15,351 - INFO - 客户端21已注册
2025-07-31 15:25:15,351 - INFO - 客户端 21 已成功注册到服务器
2025-07-31 15:25:15,351 - INFO - 开始创建客户端 22...
2025-07-31 15:25:15,353 - INFO - 初始化客户端, ID: 22
2025-07-31 15:25:15,424 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:15,425 - INFO - [Client 22] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:15,427 - INFO - [Trainer] 初始化训练器, client_id: 22
2025-07-31 15:25:15,457 - INFO - [Trainer 22] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:15,458 - INFO - [Trainer 22] 模型的输入通道数: 3
2025-07-31 15:25:15,458 - INFO - [Trainer 22] 强制使用CPU
2025-07-31 15:25:15,461 - INFO - [Trainer 22] 模型已移至设备: cpu
2025-07-31 15:25:15,462 - INFO - [Trainer 22] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:15,463 - INFO - [Trainer 22] 初始化完成
2025-07-31 15:25:15,463 - INFO - [Client 22] 创建新训练器
2025-07-31 15:25:15,465 - INFO - [Algorithm] 从训练器获取客户端ID: 22
2025-07-31 15:25:15,465 - INFO - [Algorithm] 初始化后修正client_id: 22
2025-07-31 15:25:15,467 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:15,467 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:15,469 - INFO - [Client 22] 创建新算法
2025-07-31 15:25:15,469 - INFO - [Algorithm] 设置客户端ID: 22
2025-07-31 15:25:15,470 - INFO - [Algorithm] 同步更新trainer的client_id: 22
2025-07-31 15:25:15,470 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:15,553 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:15,554 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:15,554 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:15,586 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:15,591 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:15,592 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:15,593 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:15,595 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:15,595 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:15,595 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:15,595 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:15,597 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:15,598 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:15,598 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:15,599 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:15,600 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:15,600 - INFO - 顺序分配客户端ID: 22
2025-07-31 15:25:15,601 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 22
2025-07-31 15:25:15,603 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:15,604 - WARNING - [Client 22] 数据源为None，已创建新数据源
2025-07-31 15:25:15,604 - WARNING - [Client 22] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:17,914 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:17,915 - INFO - [Client 22] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:17,915 - INFO - [Client 22] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:17,932 - INFO - [Client 22] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:17,933 - INFO - [Client 22] 初始化时成功加载数据
2025-07-31 15:25:17,934 - INFO - [客户端 22] 初始化验证通过
2025-07-31 15:25:17,934 - INFO - 客户端 22 实例创建成功
2025-07-31 15:25:17,934 - INFO - 客户端22已设置服务器引用
2025-07-31 15:25:17,934 - INFO - 客户端 22 已设置服务器引用
2025-07-31 15:25:17,935 - INFO - 客户端22已注册
2025-07-31 15:25:17,935 - INFO - 客户端 22 已成功注册到服务器
2025-07-31 15:25:17,935 - INFO - 开始创建客户端 23...
2025-07-31 15:25:17,937 - INFO - 初始化客户端, ID: 23
2025-07-31 15:25:18,029 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:18,029 - INFO - [Client 23] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:18,030 - INFO - [Trainer] 初始化训练器, client_id: 23
2025-07-31 15:25:18,070 - INFO - [Trainer 23] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:18,072 - INFO - [Trainer 23] 模型的输入通道数: 3
2025-07-31 15:25:18,072 - INFO - [Trainer 23] 强制使用CPU
2025-07-31 15:25:18,074 - INFO - [Trainer 23] 模型已移至设备: cpu
2025-07-31 15:25:18,075 - INFO - [Trainer 23] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:18,075 - INFO - [Trainer 23] 初始化完成
2025-07-31 15:25:18,077 - INFO - [Client 23] 创建新训练器
2025-07-31 15:25:18,077 - INFO - [Algorithm] 从训练器获取客户端ID: 23
2025-07-31 15:25:18,077 - INFO - [Algorithm] 初始化后修正client_id: 23
2025-07-31 15:25:18,078 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:18,078 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:18,078 - INFO - [Client 23] 创建新算法
2025-07-31 15:25:18,079 - INFO - [Algorithm] 设置客户端ID: 23
2025-07-31 15:25:18,079 - INFO - [Algorithm] 同步更新trainer的client_id: 23
2025-07-31 15:25:18,079 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:18,173 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:18,174 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:18,176 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:18,217 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:18,218 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:18,218 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:18,221 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:18,222 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:18,222 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:18,223 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:18,223 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:18,224 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:18,224 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:18,224 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:18,225 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:18,226 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:18,226 - INFO - 顺序分配客户端ID: 23
2025-07-31 15:25:18,228 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 23
2025-07-31 15:25:18,230 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:18,230 - WARNING - [Client 23] 数据源为None，已创建新数据源
2025-07-31 15:25:18,231 - WARNING - [Client 23] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:20,861 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:20,864 - INFO - [Client 23] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:20,865 - INFO - [Client 23] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:21,030 - INFO - [Client 23] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:21,031 - INFO - [Client 23] 初始化时成功加载数据
2025-07-31 15:25:21,031 - INFO - [客户端 23] 初始化验证通过
2025-07-31 15:25:21,031 - INFO - 客户端 23 实例创建成功
2025-07-31 15:25:21,032 - INFO - 客户端23已设置服务器引用
2025-07-31 15:25:21,032 - INFO - 客户端 23 已设置服务器引用
2025-07-31 15:25:21,032 - INFO - 客户端23已注册
2025-07-31 15:25:21,033 - INFO - 客户端 23 已成功注册到服务器
2025-07-31 15:25:21,033 - INFO - 开始创建客户端 24...
2025-07-31 15:25:21,033 - INFO - 初始化客户端, ID: 24
2025-07-31 15:25:21,148 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:21,149 - INFO - [Client 24] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:21,150 - INFO - [Trainer] 初始化训练器, client_id: 24
2025-07-31 15:25:21,214 - INFO - [Trainer 24] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:21,215 - INFO - [Trainer 24] 模型的输入通道数: 3
2025-07-31 15:25:21,215 - INFO - [Trainer 24] 强制使用CPU
2025-07-31 15:25:21,221 - INFO - [Trainer 24] 模型已移至设备: cpu
2025-07-31 15:25:21,222 - INFO - [Trainer 24] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:21,223 - INFO - [Trainer 24] 初始化完成
2025-07-31 15:25:21,224 - INFO - [Client 24] 创建新训练器
2025-07-31 15:25:21,224 - INFO - [Algorithm] 从训练器获取客户端ID: 24
2025-07-31 15:25:21,224 - INFO - [Algorithm] 初始化后修正client_id: 24
2025-07-31 15:25:21,225 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:21,225 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:21,225 - INFO - [Client 24] 创建新算法
2025-07-31 15:25:21,227 - INFO - [Algorithm] 设置客户端ID: 24
2025-07-31 15:25:21,227 - INFO - [Algorithm] 同步更新trainer的client_id: 24
2025-07-31 15:25:21,227 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:21,326 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:21,326 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:21,326 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:21,365 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:21,365 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:21,366 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:21,367 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:21,367 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:21,369 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:21,369 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:21,370 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:21,370 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:21,370 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:21,371 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:21,371 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:21,371 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:21,371 - INFO - 顺序分配客户端ID: 24
2025-07-31 15:25:21,372 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 24
2025-07-31 15:25:21,372 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:21,373 - WARNING - [Client 24] 数据源为None，已创建新数据源
2025-07-31 15:25:21,374 - WARNING - [Client 24] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:23,552 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:23,552 - INFO - [Client 24] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:23,553 - INFO - [Client 24] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:23,570 - INFO - [Client 24] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:23,574 - INFO - [Client 24] 初始化时成功加载数据
2025-07-31 15:25:23,575 - INFO - [客户端 24] 初始化验证通过
2025-07-31 15:25:23,575 - INFO - 客户端 24 实例创建成功
2025-07-31 15:25:23,576 - INFO - 客户端24已设置服务器引用
2025-07-31 15:25:23,577 - INFO - 客户端 24 已设置服务器引用
2025-07-31 15:25:23,578 - INFO - 客户端24已注册
2025-07-31 15:25:23,578 - INFO - 客户端 24 已成功注册到服务器
2025-07-31 15:25:23,579 - INFO - 开始创建客户端 25...
2025-07-31 15:25:23,579 - INFO - 初始化客户端, ID: 25
2025-07-31 15:25:23,658 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:23,658 - INFO - [Client 25] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:23,658 - INFO - [Trainer] 初始化训练器, client_id: 25
2025-07-31 15:25:23,685 - INFO - [Trainer 25] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:23,685 - INFO - [Trainer 25] 模型的输入通道数: 3
2025-07-31 15:25:23,685 - INFO - [Trainer 25] 强制使用CPU
2025-07-31 15:25:23,688 - INFO - [Trainer 25] 模型已移至设备: cpu
2025-07-31 15:25:23,689 - INFO - [Trainer 25] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:23,689 - INFO - [Trainer 25] 初始化完成
2025-07-31 15:25:23,690 - INFO - [Client 25] 创建新训练器
2025-07-31 15:25:23,690 - INFO - [Algorithm] 从训练器获取客户端ID: 25
2025-07-31 15:25:23,690 - INFO - [Algorithm] 初始化后修正client_id: 25
2025-07-31 15:25:23,690 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:23,691 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:23,691 - INFO - [Client 25] 创建新算法
2025-07-31 15:25:23,692 - INFO - [Algorithm] 设置客户端ID: 25
2025-07-31 15:25:23,692 - INFO - [Algorithm] 同步更新trainer的client_id: 25
2025-07-31 15:25:23,693 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:23,770 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:23,770 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:23,771 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:23,798 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:23,799 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:23,799 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:23,801 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:23,802 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:23,802 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:23,802 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:23,803 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:23,803 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:23,803 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:23,803 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:23,803 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:23,803 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:23,805 - INFO - 顺序分配客户端ID: 25
2025-07-31 15:25:23,805 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 25
2025-07-31 15:25:23,806 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:23,807 - WARNING - [Client 25] 数据源为None，已创建新数据源
2025-07-31 15:25:23,807 - WARNING - [Client 25] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:25,597 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:25,598 - INFO - [Client 25] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:25,599 - INFO - [Client 25] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:25,614 - INFO - [Client 25] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:25,615 - INFO - [Client 25] 初始化时成功加载数据
2025-07-31 15:25:25,615 - INFO - [客户端 25] 初始化验证通过
2025-07-31 15:25:25,615 - INFO - 客户端 25 实例创建成功
2025-07-31 15:25:25,615 - INFO - 客户端25已设置服务器引用
2025-07-31 15:25:25,615 - INFO - 客户端 25 已设置服务器引用
2025-07-31 15:25:25,615 - INFO - 客户端25已注册
2025-07-31 15:25:25,617 - INFO - 客户端 25 已成功注册到服务器
2025-07-31 15:25:25,617 - INFO - 开始创建客户端 26...
2025-07-31 15:25:25,617 - INFO - 初始化客户端, ID: 26
2025-07-31 15:25:25,687 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:25,687 - INFO - [Client 26] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:25,688 - INFO - [Trainer] 初始化训练器, client_id: 26
2025-07-31 15:25:25,711 - INFO - [Trainer 26] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:25,712 - INFO - [Trainer 26] 模型的输入通道数: 3
2025-07-31 15:25:25,712 - INFO - [Trainer 26] 强制使用CPU
2025-07-31 15:25:25,714 - INFO - [Trainer 26] 模型已移至设备: cpu
2025-07-31 15:25:25,715 - INFO - [Trainer 26] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:25,715 - INFO - [Trainer 26] 初始化完成
2025-07-31 15:25:25,716 - INFO - [Client 26] 创建新训练器
2025-07-31 15:25:25,716 - INFO - [Algorithm] 从训练器获取客户端ID: 26
2025-07-31 15:25:25,725 - INFO - [Algorithm] 初始化后修正client_id: 26
2025-07-31 15:25:25,725 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:25,728 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:25,729 - INFO - [Client 26] 创建新算法
2025-07-31 15:25:25,729 - INFO - [Algorithm] 设置客户端ID: 26
2025-07-31 15:25:25,730 - INFO - [Algorithm] 同步更新trainer的client_id: 26
2025-07-31 15:25:25,730 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:25,795 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:25,797 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:25,797 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:25,821 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:25,822 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:25,823 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:25,824 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:25,825 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:25,825 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:25,825 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:25,827 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:25,827 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:25,828 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:25,828 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:25,829 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:25,829 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:25,830 - INFO - 顺序分配客户端ID: 26
2025-07-31 15:25:25,830 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 26
2025-07-31 15:25:25,831 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:25,832 - WARNING - [Client 26] 数据源为None，已创建新数据源
2025-07-31 15:25:25,832 - WARNING - [Client 26] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:28,113 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:28,113 - INFO - [Client 26] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:28,114 - INFO - [Client 26] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:28,131 - INFO - [Client 26] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:28,132 - INFO - [Client 26] 初始化时成功加载数据
2025-07-31 15:25:28,133 - INFO - [客户端 26] 初始化验证通过
2025-07-31 15:25:28,134 - INFO - 客户端 26 实例创建成功
2025-07-31 15:25:28,135 - INFO - 客户端26已设置服务器引用
2025-07-31 15:25:28,135 - INFO - 客户端 26 已设置服务器引用
2025-07-31 15:25:28,135 - INFO - 客户端26已注册
2025-07-31 15:25:28,137 - INFO - 客户端 26 已成功注册到服务器
2025-07-31 15:25:28,137 - INFO - 开始创建客户端 27...
2025-07-31 15:25:28,137 - INFO - 初始化客户端, ID: 27
2025-07-31 15:25:28,237 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:28,237 - INFO - [Client 27] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:28,238 - INFO - [Trainer] 初始化训练器, client_id: 27
2025-07-31 15:25:28,274 - INFO - [Trainer 27] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:28,275 - INFO - [Trainer 27] 模型的输入通道数: 3
2025-07-31 15:25:28,277 - INFO - [Trainer 27] 强制使用CPU
2025-07-31 15:25:28,278 - INFO - [Trainer 27] 模型已移至设备: cpu
2025-07-31 15:25:28,279 - INFO - [Trainer 27] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:28,280 - INFO - [Trainer 27] 初始化完成
2025-07-31 15:25:28,280 - INFO - [Client 27] 创建新训练器
2025-07-31 15:25:28,281 - INFO - [Algorithm] 从训练器获取客户端ID: 27
2025-07-31 15:25:28,282 - INFO - [Algorithm] 初始化后修正client_id: 27
2025-07-31 15:25:28,282 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:28,282 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:28,283 - INFO - [Client 27] 创建新算法
2025-07-31 15:25:28,283 - INFO - [Algorithm] 设置客户端ID: 27
2025-07-31 15:25:28,285 - INFO - [Algorithm] 同步更新trainer的client_id: 27
2025-07-31 15:25:28,285 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:28,382 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:28,382 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:28,383 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:28,417 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:28,418 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:28,418 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:28,420 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:28,421 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:28,425 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:28,426 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:28,427 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:28,427 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:28,427 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:28,429 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:28,430 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:28,431 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:28,431 - INFO - 顺序分配客户端ID: 27
2025-07-31 15:25:28,432 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 27
2025-07-31 15:25:28,433 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:28,433 - WARNING - [Client 27] 数据源为None，已创建新数据源
2025-07-31 15:25:28,434 - WARNING - [Client 27] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:30,764 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:30,765 - INFO - [Client 27] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:30,765 - INFO - [Client 27] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:30,783 - INFO - [Client 27] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:30,783 - INFO - [Client 27] 初始化时成功加载数据
2025-07-31 15:25:30,784 - INFO - [客户端 27] 初始化验证通过
2025-07-31 15:25:30,784 - INFO - 客户端 27 实例创建成功
2025-07-31 15:25:30,784 - INFO - 客户端27已设置服务器引用
2025-07-31 15:25:30,785 - INFO - 客户端 27 已设置服务器引用
2025-07-31 15:25:30,785 - INFO - 客户端27已注册
2025-07-31 15:25:30,785 - INFO - 客户端 27 已成功注册到服务器
2025-07-31 15:25:30,785 - INFO - 开始创建客户端 28...
2025-07-31 15:25:30,787 - INFO - 初始化客户端, ID: 28
2025-07-31 15:25:30,865 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:30,865 - INFO - [Client 28] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:30,867 - INFO - [Trainer] 初始化训练器, client_id: 28
2025-07-31 15:25:30,898 - INFO - [Trainer 28] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:30,899 - INFO - [Trainer 28] 模型的输入通道数: 3
2025-07-31 15:25:30,899 - INFO - [Trainer 28] 强制使用CPU
2025-07-31 15:25:30,900 - INFO - [Trainer 28] 模型已移至设备: cpu
2025-07-31 15:25:30,901 - INFO - [Trainer 28] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:30,902 - INFO - [Trainer 28] 初始化完成
2025-07-31 15:25:30,902 - INFO - [Client 28] 创建新训练器
2025-07-31 15:25:30,903 - INFO - [Algorithm] 从训练器获取客户端ID: 28
2025-07-31 15:25:30,903 - INFO - [Algorithm] 初始化后修正client_id: 28
2025-07-31 15:25:30,904 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:30,905 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:30,906 - INFO - [Client 28] 创建新算法
2025-07-31 15:25:30,907 - INFO - [Algorithm] 设置客户端ID: 28
2025-07-31 15:25:30,907 - INFO - [Algorithm] 同步更新trainer的client_id: 28
2025-07-31 15:25:30,907 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:30,979 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:30,979 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:30,979 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:31,005 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:31,005 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:31,007 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:31,010 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:31,011 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:31,012 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:31,013 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:31,013 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:31,014 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:31,015 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:31,015 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:31,015 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:31,017 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:31,017 - INFO - 顺序分配客户端ID: 28
2025-07-31 15:25:31,018 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 28
2025-07-31 15:25:31,019 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:31,020 - WARNING - [Client 28] 数据源为None，已创建新数据源
2025-07-31 15:25:31,020 - WARNING - [Client 28] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:33,355 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:33,355 - INFO - [Client 28] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:33,357 - INFO - [Client 28] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:33,372 - INFO - [Client 28] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:33,374 - INFO - [Client 28] 初始化时成功加载数据
2025-07-31 15:25:33,376 - INFO - [客户端 28] 初始化验证通过
2025-07-31 15:25:33,378 - INFO - 客户端 28 实例创建成功
2025-07-31 15:25:33,379 - INFO - 客户端28已设置服务器引用
2025-07-31 15:25:33,380 - INFO - 客户端 28 已设置服务器引用
2025-07-31 15:25:33,381 - INFO - 客户端28已注册
2025-07-31 15:25:33,381 - INFO - 客户端 28 已成功注册到服务器
2025-07-31 15:25:33,381 - INFO - 开始创建客户端 29...
2025-07-31 15:25:33,382 - INFO - 初始化客户端, ID: 29
2025-07-31 15:25:33,476 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:33,477 - INFO - [Client 29] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:33,479 - INFO - [Trainer] 初始化训练器, client_id: 29
2025-07-31 15:25:33,514 - INFO - [Trainer 29] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:33,520 - INFO - [Trainer 29] 模型的输入通道数: 3
2025-07-31 15:25:33,523 - INFO - [Trainer 29] 强制使用CPU
2025-07-31 15:25:33,528 - INFO - [Trainer 29] 模型已移至设备: cpu
2025-07-31 15:25:33,529 - INFO - [Trainer 29] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:33,529 - INFO - [Trainer 29] 初始化完成
2025-07-31 15:25:33,530 - INFO - [Client 29] 创建新训练器
2025-07-31 15:25:33,530 - INFO - [Algorithm] 从训练器获取客户端ID: 29
2025-07-31 15:25:33,531 - INFO - [Algorithm] 初始化后修正client_id: 29
2025-07-31 15:25:33,531 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:33,532 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:33,532 - INFO - [Client 29] 创建新算法
2025-07-31 15:25:33,533 - INFO - [Algorithm] 设置客户端ID: 29
2025-07-31 15:25:33,534 - INFO - [Algorithm] 同步更新trainer的client_id: 29
2025-07-31 15:25:33,536 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:33,619 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:33,620 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:33,620 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:33,654 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:33,655 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:33,655 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:33,657 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:33,659 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:33,659 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:33,659 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:33,660 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:33,661 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:33,661 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:33,662 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:33,662 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:33,663 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:33,664 - INFO - 顺序分配客户端ID: 29
2025-07-31 15:25:33,665 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 29
2025-07-31 15:25:33,668 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:33,669 - WARNING - [Client 29] 数据源为None，已创建新数据源
2025-07-31 15:25:33,669 - WARNING - [Client 29] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:36,081 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:36,081 - INFO - [Client 29] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:36,082 - INFO - [Client 29] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:36,100 - INFO - [Client 29] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:36,101 - INFO - [Client 29] 初始化时成功加载数据
2025-07-31 15:25:36,101 - INFO - [客户端 29] 初始化验证通过
2025-07-31 15:25:36,101 - INFO - 客户端 29 实例创建成功
2025-07-31 15:25:36,102 - INFO - 客户端29已设置服务器引用
2025-07-31 15:25:36,102 - INFO - 客户端 29 已设置服务器引用
2025-07-31 15:25:36,103 - INFO - 客户端29已注册
2025-07-31 15:25:36,103 - INFO - 客户端 29 已成功注册到服务器
2025-07-31 15:25:36,104 - INFO - 开始创建客户端 30...
2025-07-31 15:25:36,104 - INFO - 初始化客户端, ID: 30
2025-07-31 15:25:36,183 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:36,183 - INFO - [Client 30] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:36,183 - INFO - [Trainer] 初始化训练器, client_id: 30
2025-07-31 15:25:36,211 - INFO - [Trainer 30] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:36,212 - INFO - [Trainer 30] 模型的输入通道数: 3
2025-07-31 15:25:36,212 - INFO - [Trainer 30] 强制使用CPU
2025-07-31 15:25:36,214 - INFO - [Trainer 30] 模型已移至设备: cpu
2025-07-31 15:25:36,216 - INFO - [Trainer 30] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:36,217 - INFO - [Trainer 30] 初始化完成
2025-07-31 15:25:36,217 - INFO - [Client 30] 创建新训练器
2025-07-31 15:25:36,218 - INFO - [Algorithm] 从训练器获取客户端ID: 30
2025-07-31 15:25:36,218 - INFO - [Algorithm] 初始化后修正client_id: 30
2025-07-31 15:25:36,219 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:36,220 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:36,220 - INFO - [Client 30] 创建新算法
2025-07-31 15:25:36,220 - INFO - [Algorithm] 设置客户端ID: 30
2025-07-31 15:25:36,221 - INFO - [Algorithm] 同步更新trainer的client_id: 30
2025-07-31 15:25:36,222 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:36,304 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:36,305 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:36,305 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:36,332 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:36,333 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:36,333 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:36,335 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:36,335 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:36,335 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:36,335 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:36,337 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:36,337 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:36,338 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:36,338 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:36,339 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:36,339 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:36,340 - INFO - 顺序分配客户端ID: 30
2025-07-31 15:25:36,340 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 30
2025-07-31 15:25:36,346 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:36,347 - WARNING - [Client 30] 数据源为None，已创建新数据源
2025-07-31 15:25:36,348 - WARNING - [Client 30] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:38,681 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:38,683 - INFO - [Client 30] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:38,684 - INFO - [Client 30] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:38,707 - INFO - [Client 30] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:38,707 - INFO - [Client 30] 初始化时成功加载数据
2025-07-31 15:25:38,707 - INFO - [客户端 30] 初始化验证通过
2025-07-31 15:25:38,708 - INFO - 客户端 30 实例创建成功
2025-07-31 15:25:38,708 - INFO - 客户端30已设置服务器引用
2025-07-31 15:25:38,709 - INFO - 客户端 30 已设置服务器引用
2025-07-31 15:25:38,709 - INFO - 客户端30已注册
2025-07-31 15:25:38,710 - INFO - 客户端 30 已成功注册到服务器
2025-07-31 15:25:38,711 - INFO - 开始创建客户端 31...
2025-07-31 15:25:38,712 - INFO - 初始化客户端, ID: 31
2025-07-31 15:25:38,811 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:38,811 - INFO - [Client 31] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:38,812 - INFO - [Trainer] 初始化训练器, client_id: 31
2025-07-31 15:25:38,840 - INFO - [Trainer 31] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:38,841 - INFO - [Trainer 31] 模型的输入通道数: 3
2025-07-31 15:25:38,841 - INFO - [Trainer 31] 强制使用CPU
2025-07-31 15:25:38,846 - INFO - [Trainer 31] 模型已移至设备: cpu
2025-07-31 15:25:38,848 - INFO - [Trainer 31] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:38,848 - INFO - [Trainer 31] 初始化完成
2025-07-31 15:25:38,849 - INFO - [Client 31] 创建新训练器
2025-07-31 15:25:38,850 - INFO - [Algorithm] 从训练器获取客户端ID: 31
2025-07-31 15:25:38,851 - INFO - [Algorithm] 初始化后修正client_id: 31
2025-07-31 15:25:38,851 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:38,852 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:38,860 - INFO - [Client 31] 创建新算法
2025-07-31 15:25:38,867 - INFO - [Algorithm] 设置客户端ID: 31
2025-07-31 15:25:38,869 - INFO - [Algorithm] 同步更新trainer的client_id: 31
2025-07-31 15:25:38,870 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:38,942 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:38,942 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:38,943 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:38,969 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:38,969 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:38,970 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:38,972 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:38,972 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:38,973 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:38,973 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:38,974 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:38,974 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:38,974 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:38,975 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:38,975 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:38,975 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:38,975 - INFO - 顺序分配客户端ID: 31
2025-07-31 15:25:38,977 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 31
2025-07-31 15:25:38,978 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:38,978 - WARNING - [Client 31] 数据源为None，已创建新数据源
2025-07-31 15:25:38,979 - WARNING - [Client 31] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:41,553 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:41,554 - INFO - [Client 31] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:41,555 - INFO - [Client 31] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:41,570 - INFO - [Client 31] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:41,570 - INFO - [Client 31] 初始化时成功加载数据
2025-07-31 15:25:41,571 - INFO - [客户端 31] 初始化验证通过
2025-07-31 15:25:41,571 - INFO - 客户端 31 实例创建成功
2025-07-31 15:25:41,571 - INFO - 客户端31已设置服务器引用
2025-07-31 15:25:41,572 - INFO - 客户端 31 已设置服务器引用
2025-07-31 15:25:41,572 - INFO - 客户端31已注册
2025-07-31 15:25:41,573 - INFO - 客户端 31 已成功注册到服务器
2025-07-31 15:25:41,573 - INFO - 开始创建客户端 32...
2025-07-31 15:25:41,574 - INFO - 初始化客户端, ID: 32
2025-07-31 15:25:41,667 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:41,668 - INFO - [Client 32] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:41,668 - INFO - [Trainer] 初始化训练器, client_id: 32
2025-07-31 15:25:41,697 - INFO - [Trainer 32] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:41,700 - INFO - [Trainer 32] 模型的输入通道数: 3
2025-07-31 15:25:41,701 - INFO - [Trainer 32] 强制使用CPU
2025-07-31 15:25:41,705 - INFO - [Trainer 32] 模型已移至设备: cpu
2025-07-31 15:25:41,709 - INFO - [Trainer 32] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:41,711 - INFO - [Trainer 32] 初始化完成
2025-07-31 15:25:41,712 - INFO - [Client 32] 创建新训练器
2025-07-31 15:25:41,712 - INFO - [Algorithm] 从训练器获取客户端ID: 32
2025-07-31 15:25:41,714 - INFO - [Algorithm] 初始化后修正client_id: 32
2025-07-31 15:25:41,717 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:41,720 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:41,723 - INFO - [Client 32] 创建新算法
2025-07-31 15:25:41,724 - INFO - [Algorithm] 设置客户端ID: 32
2025-07-31 15:25:41,725 - INFO - [Algorithm] 同步更新trainer的client_id: 32
2025-07-31 15:25:41,726 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:41,813 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:41,814 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:41,814 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:41,844 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:41,845 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:41,846 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:41,847 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:41,848 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:41,848 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:41,849 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:41,849 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:41,850 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:41,850 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:41,851 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:41,851 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:41,851 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:41,852 - INFO - 顺序分配客户端ID: 32
2025-07-31 15:25:41,853 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 32
2025-07-31 15:25:41,854 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:41,854 - WARNING - [Client 32] 数据源为None，已创建新数据源
2025-07-31 15:25:41,855 - WARNING - [Client 32] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:44,354 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:44,355 - INFO - [Client 32] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:44,357 - INFO - [Client 32] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:44,385 - INFO - [Client 32] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:44,385 - INFO - [Client 32] 初始化时成功加载数据
2025-07-31 15:25:44,387 - INFO - [客户端 32] 初始化验证通过
2025-07-31 15:25:44,387 - INFO - 客户端 32 实例创建成功
2025-07-31 15:25:44,387 - INFO - 客户端32已设置服务器引用
2025-07-31 15:25:44,388 - INFO - 客户端 32 已设置服务器引用
2025-07-31 15:25:44,388 - INFO - 客户端32已注册
2025-07-31 15:25:44,389 - INFO - 客户端 32 已成功注册到服务器
2025-07-31 15:25:44,394 - INFO - 开始创建客户端 33...
2025-07-31 15:25:44,395 - INFO - 初始化客户端, ID: 33
2025-07-31 15:25:44,499 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:44,499 - INFO - [Client 33] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:44,500 - INFO - [Trainer] 初始化训练器, client_id: 33
2025-07-31 15:25:44,534 - INFO - [Trainer 33] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:44,535 - INFO - [Trainer 33] 模型的输入通道数: 3
2025-07-31 15:25:44,535 - INFO - [Trainer 33] 强制使用CPU
2025-07-31 15:25:44,537 - INFO - [Trainer 33] 模型已移至设备: cpu
2025-07-31 15:25:44,538 - INFO - [Trainer 33] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:44,539 - INFO - [Trainer 33] 初始化完成
2025-07-31 15:25:44,539 - INFO - [Client 33] 创建新训练器
2025-07-31 15:25:44,540 - INFO - [Algorithm] 从训练器获取客户端ID: 33
2025-07-31 15:25:44,540 - INFO - [Algorithm] 初始化后修正client_id: 33
2025-07-31 15:25:44,541 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:44,541 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:44,541 - INFO - [Client 33] 创建新算法
2025-07-31 15:25:44,542 - INFO - [Algorithm] 设置客户端ID: 33
2025-07-31 15:25:44,542 - INFO - [Algorithm] 同步更新trainer的client_id: 33
2025-07-31 15:25:44,543 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:44,620 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:44,620 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:44,621 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:44,647 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:44,649 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:44,649 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:44,651 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:44,652 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:44,653 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:44,653 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:44,663 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:44,664 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:44,665 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:44,665 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:44,665 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:44,667 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:44,667 - INFO - 顺序分配客户端ID: 33
2025-07-31 15:25:44,667 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 33
2025-07-31 15:25:44,669 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:44,672 - WARNING - [Client 33] 数据源为None，已创建新数据源
2025-07-31 15:25:44,683 - WARNING - [Client 33] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:46,948 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:46,949 - INFO - [Client 33] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:46,950 - INFO - [Client 33] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:46,966 - INFO - [Client 33] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:46,966 - INFO - [Client 33] 初始化时成功加载数据
2025-07-31 15:25:46,967 - INFO - [客户端 33] 初始化验证通过
2025-07-31 15:25:46,968 - INFO - 客户端 33 实例创建成功
2025-07-31 15:25:46,968 - INFO - 客户端33已设置服务器引用
2025-07-31 15:25:46,972 - INFO - 客户端 33 已设置服务器引用
2025-07-31 15:25:46,972 - INFO - 客户端33已注册
2025-07-31 15:25:46,973 - INFO - 客户端 33 已成功注册到服务器
2025-07-31 15:25:46,973 - INFO - 开始创建客户端 34...
2025-07-31 15:25:46,973 - INFO - 初始化客户端, ID: 34
2025-07-31 15:25:47,066 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:47,067 - INFO - [Client 34] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:47,067 - INFO - [Trainer] 初始化训练器, client_id: 34
2025-07-31 15:25:47,093 - INFO - [Trainer 34] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:47,094 - INFO - [Trainer 34] 模型的输入通道数: 3
2025-07-31 15:25:47,094 - INFO - [Trainer 34] 强制使用CPU
2025-07-31 15:25:47,097 - INFO - [Trainer 34] 模型已移至设备: cpu
2025-07-31 15:25:47,097 - INFO - [Trainer 34] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:47,098 - INFO - [Trainer 34] 初始化完成
2025-07-31 15:25:47,098 - INFO - [Client 34] 创建新训练器
2025-07-31 15:25:47,099 - INFO - [Algorithm] 从训练器获取客户端ID: 34
2025-07-31 15:25:47,099 - INFO - [Algorithm] 初始化后修正client_id: 34
2025-07-31 15:25:47,099 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:47,100 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:47,100 - INFO - [Client 34] 创建新算法
2025-07-31 15:25:47,100 - INFO - [Algorithm] 设置客户端ID: 34
2025-07-31 15:25:47,101 - INFO - [Algorithm] 同步更新trainer的client_id: 34
2025-07-31 15:25:47,101 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:47,202 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:47,202 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:47,203 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:47,235 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:47,236 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:47,237 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:47,238 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:47,239 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:47,240 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:47,240 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:47,241 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:47,241 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:47,242 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:47,242 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:47,243 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:47,244 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:47,244 - INFO - 顺序分配客户端ID: 34
2025-07-31 15:25:47,245 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 34
2025-07-31 15:25:47,247 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:47,248 - WARNING - [Client 34] 数据源为None，已创建新数据源
2025-07-31 15:25:47,248 - WARNING - [Client 34] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:49,824 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:49,825 - INFO - [Client 34] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:49,825 - INFO - [Client 34] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:49,840 - INFO - [Client 34] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:49,840 - INFO - [Client 34] 初始化时成功加载数据
2025-07-31 15:25:49,841 - INFO - [客户端 34] 初始化验证通过
2025-07-31 15:25:49,841 - INFO - 客户端 34 实例创建成功
2025-07-31 15:25:49,842 - INFO - 客户端34已设置服务器引用
2025-07-31 15:25:49,842 - INFO - 客户端 34 已设置服务器引用
2025-07-31 15:25:49,842 - INFO - 客户端34已注册
2025-07-31 15:25:49,844 - INFO - 客户端 34 已成功注册到服务器
2025-07-31 15:25:49,844 - INFO - 开始创建客户端 35...
2025-07-31 15:25:49,847 - INFO - 初始化客户端, ID: 35
2025-07-31 15:25:49,935 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:49,937 - INFO - [Client 35] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:49,937 - INFO - [Trainer] 初始化训练器, client_id: 35
2025-07-31 15:25:49,963 - INFO - [Trainer 35] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:49,963 - INFO - [Trainer 35] 模型的输入通道数: 3
2025-07-31 15:25:49,965 - INFO - [Trainer 35] 强制使用CPU
2025-07-31 15:25:49,966 - INFO - [Trainer 35] 模型已移至设备: cpu
2025-07-31 15:25:49,967 - INFO - [Trainer 35] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:49,967 - INFO - [Trainer 35] 初始化完成
2025-07-31 15:25:49,967 - INFO - [Client 35] 创建新训练器
2025-07-31 15:25:49,967 - INFO - [Algorithm] 从训练器获取客户端ID: 35
2025-07-31 15:25:49,967 - INFO - [Algorithm] 初始化后修正client_id: 35
2025-07-31 15:25:49,967 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:49,968 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:49,968 - INFO - [Client 35] 创建新算法
2025-07-31 15:25:49,968 - INFO - [Algorithm] 设置客户端ID: 35
2025-07-31 15:25:49,969 - INFO - [Algorithm] 同步更新trainer的client_id: 35
2025-07-31 15:25:49,969 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:50,052 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:50,054 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:50,054 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:50,085 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:50,087 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:50,088 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:50,089 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:50,090 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:50,091 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:50,091 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:50,092 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:50,093 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:50,093 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:50,094 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:50,094 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:50,095 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:50,095 - INFO - 顺序分配客户端ID: 35
2025-07-31 15:25:50,095 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 35
2025-07-31 15:25:50,097 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:50,098 - WARNING - [Client 35] 数据源为None，已创建新数据源
2025-07-31 15:25:50,099 - WARNING - [Client 35] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:52,658 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:52,658 - INFO - [Client 35] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:52,659 - INFO - [Client 35] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:52,681 - INFO - [Client 35] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:52,681 - INFO - [Client 35] 初始化时成功加载数据
2025-07-31 15:25:52,682 - INFO - [客户端 35] 初始化验证通过
2025-07-31 15:25:52,682 - INFO - 客户端 35 实例创建成功
2025-07-31 15:25:52,683 - INFO - 客户端35已设置服务器引用
2025-07-31 15:25:52,683 - INFO - 客户端 35 已设置服务器引用
2025-07-31 15:25:52,684 - INFO - 客户端35已注册
2025-07-31 15:25:52,684 - INFO - 客户端 35 已成功注册到服务器
2025-07-31 15:25:52,684 - INFO - 开始创建客户端 36...
2025-07-31 15:25:52,685 - INFO - 初始化客户端, ID: 36
2025-07-31 15:25:52,777 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:52,777 - INFO - [Client 36] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:52,779 - INFO - [Trainer] 初始化训练器, client_id: 36
2025-07-31 15:25:52,812 - INFO - [Trainer 36] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:52,813 - INFO - [Trainer 36] 模型的输入通道数: 3
2025-07-31 15:25:52,813 - INFO - [Trainer 36] 强制使用CPU
2025-07-31 15:25:52,815 - INFO - [Trainer 36] 模型已移至设备: cpu
2025-07-31 15:25:52,816 - INFO - [Trainer 36] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:52,817 - INFO - [Trainer 36] 初始化完成
2025-07-31 15:25:52,817 - INFO - [Client 36] 创建新训练器
2025-07-31 15:25:52,818 - INFO - [Algorithm] 从训练器获取客户端ID: 36
2025-07-31 15:25:52,818 - INFO - [Algorithm] 初始化后修正client_id: 36
2025-07-31 15:25:52,818 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:52,819 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:52,819 - INFO - [Client 36] 创建新算法
2025-07-31 15:25:52,820 - INFO - [Algorithm] 设置客户端ID: 36
2025-07-31 15:25:52,820 - INFO - [Algorithm] 同步更新trainer的client_id: 36
2025-07-31 15:25:52,820 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:52,912 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:52,913 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:52,913 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:52,946 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:52,946 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:52,947 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:52,948 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:52,949 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:52,950 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:52,950 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:52,951 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:52,951 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:52,952 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:52,953 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:52,953 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:52,953 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:52,955 - INFO - 顺序分配客户端ID: 36
2025-07-31 15:25:52,956 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 36
2025-07-31 15:25:52,957 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:52,958 - WARNING - [Client 36] 数据源为None，已创建新数据源
2025-07-31 15:25:52,959 - WARNING - [Client 36] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:55,623 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:55,625 - INFO - [Client 36] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:55,628 - INFO - [Client 36] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:55,647 - INFO - [Client 36] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:55,648 - INFO - [Client 36] 初始化时成功加载数据
2025-07-31 15:25:55,648 - INFO - [客户端 36] 初始化验证通过
2025-07-31 15:25:55,649 - INFO - 客户端 36 实例创建成功
2025-07-31 15:25:55,649 - INFO - 客户端36已设置服务器引用
2025-07-31 15:25:55,650 - INFO - 客户端 36 已设置服务器引用
2025-07-31 15:25:55,651 - INFO - 客户端36已注册
2025-07-31 15:25:55,652 - INFO - 客户端 36 已成功注册到服务器
2025-07-31 15:25:55,653 - INFO - 开始创建客户端 37...
2025-07-31 15:25:55,654 - INFO - 初始化客户端, ID: 37
2025-07-31 15:25:55,768 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:55,770 - INFO - [Client 37] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:55,772 - INFO - [Trainer] 初始化训练器, client_id: 37
2025-07-31 15:25:55,807 - INFO - [Trainer 37] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:55,808 - INFO - [Trainer 37] 模型的输入通道数: 3
2025-07-31 15:25:55,809 - INFO - [Trainer 37] 强制使用CPU
2025-07-31 15:25:55,811 - INFO - [Trainer 37] 模型已移至设备: cpu
2025-07-31 15:25:55,812 - INFO - [Trainer 37] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:55,815 - INFO - [Trainer 37] 初始化完成
2025-07-31 15:25:55,816 - INFO - [Client 37] 创建新训练器
2025-07-31 15:25:55,817 - INFO - [Algorithm] 从训练器获取客户端ID: 37
2025-07-31 15:25:55,817 - INFO - [Algorithm] 初始化后修正client_id: 37
2025-07-31 15:25:55,818 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:55,819 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:55,821 - INFO - [Client 37] 创建新算法
2025-07-31 15:25:55,823 - INFO - [Algorithm] 设置客户端ID: 37
2025-07-31 15:25:55,823 - INFO - [Algorithm] 同步更新trainer的client_id: 37
2025-07-31 15:25:55,824 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:55,928 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:55,929 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:55,930 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:55,967 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:55,968 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:55,969 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:55,971 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:55,972 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:55,972 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:55,973 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:55,973 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:55,973 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:55,974 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:55,974 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:55,975 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:55,975 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:55,976 - INFO - 顺序分配客户端ID: 37
2025-07-31 15:25:55,976 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 37
2025-07-31 15:25:55,977 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:55,978 - WARNING - [Client 37] 数据源为None，已创建新数据源
2025-07-31 15:25:55,978 - WARNING - [Client 37] 数据源trainset为None，已设置为空列表
2025-07-31 15:25:58,503 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:25:58,505 - INFO - [Client 37] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:25:58,505 - INFO - [Client 37] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:25:58,527 - INFO - [Client 37] 成功划分数据集，分配到 300 个样本
2025-07-31 15:25:58,528 - INFO - [Client 37] 初始化时成功加载数据
2025-07-31 15:25:58,531 - INFO - [客户端 37] 初始化验证通过
2025-07-31 15:25:58,532 - INFO - 客户端 37 实例创建成功
2025-07-31 15:25:58,533 - INFO - 客户端37已设置服务器引用
2025-07-31 15:25:58,536 - INFO - 客户端 37 已设置服务器引用
2025-07-31 15:25:58,537 - INFO - 客户端37已注册
2025-07-31 15:25:58,539 - INFO - 客户端 37 已成功注册到服务器
2025-07-31 15:25:58,540 - INFO - 开始创建客户端 38...
2025-07-31 15:25:58,541 - INFO - 初始化客户端, ID: 38
2025-07-31 15:25:58,652 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:58,654 - INFO - [Client 38] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:58,654 - INFO - [Trainer] 初始化训练器, client_id: 38
2025-07-31 15:25:58,699 - INFO - [Trainer 38] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:58,699 - INFO - [Trainer 38] 模型的输入通道数: 3
2025-07-31 15:25:58,700 - INFO - [Trainer 38] 强制使用CPU
2025-07-31 15:25:58,702 - INFO - [Trainer 38] 模型已移至设备: cpu
2025-07-31 15:25:58,703 - INFO - [Trainer 38] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:58,703 - INFO - [Trainer 38] 初始化完成
2025-07-31 15:25:58,703 - INFO - [Client 38] 创建新训练器
2025-07-31 15:25:58,704 - INFO - [Algorithm] 从训练器获取客户端ID: 38
2025-07-31 15:25:58,704 - INFO - [Algorithm] 初始化后修正client_id: 38
2025-07-31 15:25:58,705 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:58,705 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:58,705 - INFO - [Client 38] 创建新算法
2025-07-31 15:25:58,707 - INFO - [Algorithm] 设置客户端ID: 38
2025-07-31 15:25:58,707 - INFO - [Algorithm] 同步更新trainer的client_id: 38
2025-07-31 15:25:58,707 - INFO - [Client None] 父类初始化完成
2025-07-31 15:25:58,816 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:25:58,818 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:25:58,819 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:25:58,854 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:25:58,855 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:25:58,856 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:25:58,858 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:25:58,859 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:25:58,859 - INFO - [Trainer None] 初始化完成
2025-07-31 15:25:58,860 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:25:58,860 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:25:58,860 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:25:58,862 - INFO - [Algorithm] 初始化完成
2025-07-31 15:25:58,862 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:25:58,863 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:25:58,864 - INFO - [Client None] 开始加载数据
2025-07-31 15:25:58,864 - INFO - 顺序分配客户端ID: 38
2025-07-31 15:25:58,865 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 38
2025-07-31 15:25:58,867 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:25:58,869 - WARNING - [Client 38] 数据源为None，已创建新数据源
2025-07-31 15:25:58,870 - WARNING - [Client 38] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:01,453 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:01,455 - INFO - [Client 38] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:01,455 - INFO - [Client 38] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:01,477 - INFO - [Client 38] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:01,477 - INFO - [Client 38] 初始化时成功加载数据
2025-07-31 15:26:01,478 - INFO - [客户端 38] 初始化验证通过
2025-07-31 15:26:01,478 - INFO - 客户端 38 实例创建成功
2025-07-31 15:26:01,479 - INFO - 客户端38已设置服务器引用
2025-07-31 15:26:01,480 - INFO - 客户端 38 已设置服务器引用
2025-07-31 15:26:01,481 - INFO - 客户端38已注册
2025-07-31 15:26:01,481 - INFO - 客户端 38 已成功注册到服务器
2025-07-31 15:26:01,482 - INFO - 开始创建客户端 39...
2025-07-31 15:26:01,484 - INFO - 初始化客户端, ID: 39
2025-07-31 15:26:01,584 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:01,585 - INFO - [Client 39] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:01,586 - INFO - [Trainer] 初始化训练器, client_id: 39
2025-07-31 15:26:01,621 - INFO - [Trainer 39] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:01,624 - INFO - [Trainer 39] 模型的输入通道数: 3
2025-07-31 15:26:01,626 - INFO - [Trainer 39] 强制使用CPU
2025-07-31 15:26:01,628 - INFO - [Trainer 39] 模型已移至设备: cpu
2025-07-31 15:26:01,638 - INFO - [Trainer 39] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:01,639 - INFO - [Trainer 39] 初始化完成
2025-07-31 15:26:01,639 - INFO - [Client 39] 创建新训练器
2025-07-31 15:26:01,640 - INFO - [Algorithm] 从训练器获取客户端ID: 39
2025-07-31 15:26:01,640 - INFO - [Algorithm] 初始化后修正client_id: 39
2025-07-31 15:26:01,642 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:01,643 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:01,649 - INFO - [Client 39] 创建新算法
2025-07-31 15:26:01,651 - INFO - [Algorithm] 设置客户端ID: 39
2025-07-31 15:26:01,653 - INFO - [Algorithm] 同步更新trainer的client_id: 39
2025-07-31 15:26:01,655 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:01,729 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:01,730 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:01,730 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:01,755 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:01,757 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:01,757 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:01,759 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:01,760 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:01,761 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:01,761 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:01,761 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:01,762 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:01,762 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:01,762 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:01,764 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:01,764 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:01,764 - INFO - 顺序分配客户端ID: 39
2025-07-31 15:26:01,765 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 39
2025-07-31 15:26:01,765 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:01,767 - WARNING - [Client 39] 数据源为None，已创建新数据源
2025-07-31 15:26:01,768 - WARNING - [Client 39] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:04,601 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:04,602 - INFO - [Client 39] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:04,603 - INFO - [Client 39] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:04,623 - INFO - [Client 39] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:04,624 - INFO - [Client 39] 初始化时成功加载数据
2025-07-31 15:26:04,624 - INFO - [客户端 39] 初始化验证通过
2025-07-31 15:26:04,625 - INFO - 客户端 39 实例创建成功
2025-07-31 15:26:04,626 - INFO - 客户端39已设置服务器引用
2025-07-31 15:26:04,626 - INFO - 客户端 39 已设置服务器引用
2025-07-31 15:26:04,627 - INFO - 客户端39已注册
2025-07-31 15:26:04,627 - INFO - 客户端 39 已成功注册到服务器
2025-07-31 15:26:04,627 - INFO - 开始创建客户端 40...
2025-07-31 15:26:04,627 - INFO - 初始化客户端, ID: 40
2025-07-31 15:26:04,803 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:04,804 - INFO - [Client 40] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:04,804 - INFO - [Trainer] 初始化训练器, client_id: 40
2025-07-31 15:26:04,870 - INFO - [Trainer 40] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:04,870 - INFO - [Trainer 40] 模型的输入通道数: 3
2025-07-31 15:26:04,871 - INFO - [Trainer 40] 强制使用CPU
2025-07-31 15:26:04,872 - INFO - [Trainer 40] 模型已移至设备: cpu
2025-07-31 15:26:04,873 - INFO - [Trainer 40] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:04,874 - INFO - [Trainer 40] 初始化完成
2025-07-31 15:26:04,875 - INFO - [Client 40] 创建新训练器
2025-07-31 15:26:04,875 - INFO - [Algorithm] 从训练器获取客户端ID: 40
2025-07-31 15:26:04,877 - INFO - [Algorithm] 初始化后修正client_id: 40
2025-07-31 15:26:04,877 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:04,878 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:04,878 - INFO - [Client 40] 创建新算法
2025-07-31 15:26:04,880 - INFO - [Algorithm] 设置客户端ID: 40
2025-07-31 15:26:04,880 - INFO - [Algorithm] 同步更新trainer的client_id: 40
2025-07-31 15:26:04,881 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:05,145 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:05,146 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:05,146 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:05,188 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:05,189 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:05,190 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:05,191 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:05,192 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:05,192 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:05,193 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:05,193 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:05,193 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:05,194 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:05,194 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:05,194 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:05,195 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:05,195 - INFO - 顺序分配客户端ID: 40
2025-07-31 15:26:05,196 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 40
2025-07-31 15:26:05,197 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:05,197 - WARNING - [Client 40] 数据源为None，已创建新数据源
2025-07-31 15:26:05,198 - WARNING - [Client 40] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:07,535 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:07,536 - INFO - [Client 40] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:07,536 - INFO - [Client 40] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:07,550 - INFO - [Client 40] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:07,551 - INFO - [Client 40] 初始化时成功加载数据
2025-07-31 15:26:07,551 - INFO - [客户端 40] 初始化验证通过
2025-07-31 15:26:07,552 - INFO - 客户端 40 实例创建成功
2025-07-31 15:26:07,552 - INFO - 客户端40已设置服务器引用
2025-07-31 15:26:07,552 - INFO - 客户端 40 已设置服务器引用
2025-07-31 15:26:07,552 - INFO - 客户端40已注册
2025-07-31 15:26:07,553 - INFO - 客户端 40 已成功注册到服务器
2025-07-31 15:26:07,553 - INFO - 开始创建客户端 41...
2025-07-31 15:26:07,554 - INFO - 初始化客户端, ID: 41
2025-07-31 15:26:07,617 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:07,618 - INFO - [Client 41] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:07,618 - INFO - [Trainer] 初始化训练器, client_id: 41
2025-07-31 15:26:07,643 - INFO - [Trainer 41] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:07,644 - INFO - [Trainer 41] 模型的输入通道数: 3
2025-07-31 15:26:07,644 - INFO - [Trainer 41] 强制使用CPU
2025-07-31 15:26:07,645 - INFO - [Trainer 41] 模型已移至设备: cpu
2025-07-31 15:26:07,647 - INFO - [Trainer 41] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:07,648 - INFO - [Trainer 41] 初始化完成
2025-07-31 15:26:07,648 - INFO - [Client 41] 创建新训练器
2025-07-31 15:26:07,648 - INFO - [Algorithm] 从训练器获取客户端ID: 41
2025-07-31 15:26:07,649 - INFO - [Algorithm] 初始化后修正client_id: 41
2025-07-31 15:26:07,649 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:07,649 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:07,650 - INFO - [Client 41] 创建新算法
2025-07-31 15:26:07,650 - INFO - [Algorithm] 设置客户端ID: 41
2025-07-31 15:26:07,650 - INFO - [Algorithm] 同步更新trainer的client_id: 41
2025-07-31 15:26:07,651 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:07,720 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:07,721 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:07,721 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:07,749 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:07,749 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:07,750 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:07,752 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:07,753 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:07,753 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:07,753 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:07,753 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:07,754 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:07,754 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:07,754 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:07,754 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:07,755 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:07,755 - INFO - 顺序分配客户端ID: 41
2025-07-31 15:26:07,755 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 41
2025-07-31 15:26:07,757 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:07,758 - WARNING - [Client 41] 数据源为None，已创建新数据源
2025-07-31 15:26:07,758 - WARNING - [Client 41] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:09,877 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:09,878 - INFO - [Client 41] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:09,878 - INFO - [Client 41] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:09,895 - INFO - [Client 41] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:09,897 - INFO - [Client 41] 初始化时成功加载数据
2025-07-31 15:26:09,898 - INFO - [客户端 41] 初始化验证通过
2025-07-31 15:26:09,898 - INFO - 客户端 41 实例创建成功
2025-07-31 15:26:09,899 - INFO - 客户端41已设置服务器引用
2025-07-31 15:26:09,900 - INFO - 客户端 41 已设置服务器引用
2025-07-31 15:26:09,901 - INFO - 客户端41已注册
2025-07-31 15:26:09,902 - INFO - 客户端 41 已成功注册到服务器
2025-07-31 15:26:09,903 - INFO - 开始创建客户端 42...
2025-07-31 15:26:09,904 - INFO - 初始化客户端, ID: 42
2025-07-31 15:26:10,007 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:10,008 - INFO - [Client 42] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:10,009 - INFO - [Trainer] 初始化训练器, client_id: 42
2025-07-31 15:26:10,044 - INFO - [Trainer 42] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:10,045 - INFO - [Trainer 42] 模型的输入通道数: 3
2025-07-31 15:26:10,046 - INFO - [Trainer 42] 强制使用CPU
2025-07-31 15:26:10,047 - INFO - [Trainer 42] 模型已移至设备: cpu
2025-07-31 15:26:10,049 - INFO - [Trainer 42] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:10,049 - INFO - [Trainer 42] 初始化完成
2025-07-31 15:26:10,050 - INFO - [Client 42] 创建新训练器
2025-07-31 15:26:10,050 - INFO - [Algorithm] 从训练器获取客户端ID: 42
2025-07-31 15:26:10,050 - INFO - [Algorithm] 初始化后修正client_id: 42
2025-07-31 15:26:10,051 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:10,051 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:10,052 - INFO - [Client 42] 创建新算法
2025-07-31 15:26:10,052 - INFO - [Algorithm] 设置客户端ID: 42
2025-07-31 15:26:10,052 - INFO - [Algorithm] 同步更新trainer的client_id: 42
2025-07-31 15:26:10,053 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:10,146 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:10,147 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:10,147 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:10,188 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:10,188 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:10,189 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:10,192 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:10,194 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:10,195 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:10,195 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:10,195 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:10,197 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:10,198 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:10,198 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:10,199 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:10,199 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:10,200 - INFO - 顺序分配客户端ID: 42
2025-07-31 15:26:10,200 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 42
2025-07-31 15:26:10,201 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:10,202 - WARNING - [Client 42] 数据源为None，已创建新数据源
2025-07-31 15:26:10,203 - WARNING - [Client 42] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:12,787 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:12,787 - INFO - [Client 42] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:12,788 - INFO - [Client 42] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:12,809 - INFO - [Client 42] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:12,810 - INFO - [Client 42] 初始化时成功加载数据
2025-07-31 15:26:12,810 - INFO - [客户端 42] 初始化验证通过
2025-07-31 15:26:12,811 - INFO - 客户端 42 实例创建成功
2025-07-31 15:26:12,811 - INFO - 客户端42已设置服务器引用
2025-07-31 15:26:12,812 - INFO - 客户端 42 已设置服务器引用
2025-07-31 15:26:12,812 - INFO - 客户端42已注册
2025-07-31 15:26:12,812 - INFO - 客户端 42 已成功注册到服务器
2025-07-31 15:26:12,813 - INFO - 开始创建客户端 43...
2025-07-31 15:26:12,813 - INFO - 初始化客户端, ID: 43
2025-07-31 15:26:12,903 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:12,903 - INFO - [Client 43] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:12,904 - INFO - [Trainer] 初始化训练器, client_id: 43
2025-07-31 15:26:12,931 - INFO - [Trainer 43] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:12,932 - INFO - [Trainer 43] 模型的输入通道数: 3
2025-07-31 15:26:12,933 - INFO - [Trainer 43] 强制使用CPU
2025-07-31 15:26:12,937 - INFO - [Trainer 43] 模型已移至设备: cpu
2025-07-31 15:26:12,938 - INFO - [Trainer 43] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:12,938 - INFO - [Trainer 43] 初始化完成
2025-07-31 15:26:12,939 - INFO - [Client 43] 创建新训练器
2025-07-31 15:26:12,940 - INFO - [Algorithm] 从训练器获取客户端ID: 43
2025-07-31 15:26:12,941 - INFO - [Algorithm] 初始化后修正client_id: 43
2025-07-31 15:26:12,941 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:12,942 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:12,942 - INFO - [Client 43] 创建新算法
2025-07-31 15:26:12,943 - INFO - [Algorithm] 设置客户端ID: 43
2025-07-31 15:26:12,943 - INFO - [Algorithm] 同步更新trainer的client_id: 43
2025-07-31 15:26:12,944 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:13,025 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:13,025 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:13,026 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:13,054 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:13,055 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:13,055 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:13,058 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:13,059 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:13,060 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:13,061 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:13,061 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:13,061 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:13,062 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:13,062 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:13,063 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:13,063 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:13,064 - INFO - 顺序分配客户端ID: 43
2025-07-31 15:26:13,064 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 43
2025-07-31 15:26:13,066 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:13,067 - WARNING - [Client 43] 数据源为None，已创建新数据源
2025-07-31 15:26:13,067 - WARNING - [Client 43] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:15,253 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:15,254 - INFO - [Client 43] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:15,254 - INFO - [Client 43] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:15,267 - INFO - [Client 43] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:15,268 - INFO - [Client 43] 初始化时成功加载数据
2025-07-31 15:26:15,268 - INFO - [客户端 43] 初始化验证通过
2025-07-31 15:26:15,269 - INFO - 客户端 43 实例创建成功
2025-07-31 15:26:15,269 - INFO - 客户端43已设置服务器引用
2025-07-31 15:26:15,269 - INFO - 客户端 43 已设置服务器引用
2025-07-31 15:26:15,270 - INFO - 客户端43已注册
2025-07-31 15:26:15,271 - INFO - 客户端 43 已成功注册到服务器
2025-07-31 15:26:15,271 - INFO - 开始创建客户端 44...
2025-07-31 15:26:15,272 - INFO - 初始化客户端, ID: 44
2025-07-31 15:26:15,369 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:15,370 - INFO - [Client 44] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:15,371 - INFO - [Trainer] 初始化训练器, client_id: 44
2025-07-31 15:26:15,410 - INFO - [Trainer 44] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:15,411 - INFO - [Trainer 44] 模型的输入通道数: 3
2025-07-31 15:26:15,411 - INFO - [Trainer 44] 强制使用CPU
2025-07-31 15:26:15,413 - INFO - [Trainer 44] 模型已移至设备: cpu
2025-07-31 15:26:15,414 - INFO - [Trainer 44] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:15,415 - INFO - [Trainer 44] 初始化完成
2025-07-31 15:26:15,415 - INFO - [Client 44] 创建新训练器
2025-07-31 15:26:15,415 - INFO - [Algorithm] 从训练器获取客户端ID: 44
2025-07-31 15:26:15,417 - INFO - [Algorithm] 初始化后修正client_id: 44
2025-07-31 15:26:15,417 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:15,417 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:15,418 - INFO - [Client 44] 创建新算法
2025-07-31 15:26:15,419 - INFO - [Algorithm] 设置客户端ID: 44
2025-07-31 15:26:15,419 - INFO - [Algorithm] 同步更新trainer的client_id: 44
2025-07-31 15:26:15,420 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:15,505 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:15,505 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:15,506 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:15,540 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:15,540 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:15,542 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:15,542 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:15,543 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:15,544 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:15,544 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:15,546 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:15,546 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:15,548 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:15,548 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:15,548 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:15,549 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:15,549 - INFO - 顺序分配客户端ID: 44
2025-07-31 15:26:15,549 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 44
2025-07-31 15:26:15,550 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:15,551 - WARNING - [Client 44] 数据源为None，已创建新数据源
2025-07-31 15:26:15,553 - WARNING - [Client 44] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:18,021 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:18,022 - INFO - [Client 44] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:18,022 - INFO - [Client 44] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:18,037 - INFO - [Client 44] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:18,042 - INFO - [Client 44] 初始化时成功加载数据
2025-07-31 15:26:18,043 - INFO - [客户端 44] 初始化验证通过
2025-07-31 15:26:18,043 - INFO - 客户端 44 实例创建成功
2025-07-31 15:26:18,044 - INFO - 客户端44已设置服务器引用
2025-07-31 15:26:18,044 - INFO - 客户端 44 已设置服务器引用
2025-07-31 15:26:18,045 - INFO - 客户端44已注册
2025-07-31 15:26:18,045 - INFO - 客户端 44 已成功注册到服务器
2025-07-31 15:26:18,047 - INFO - 开始创建客户端 45...
2025-07-31 15:26:18,048 - INFO - 初始化客户端, ID: 45
2025-07-31 15:26:18,150 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:18,150 - INFO - [Client 45] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:18,151 - INFO - [Trainer] 初始化训练器, client_id: 45
2025-07-31 15:26:18,192 - INFO - [Trainer 45] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:18,193 - INFO - [Trainer 45] 模型的输入通道数: 3
2025-07-31 15:26:18,193 - INFO - [Trainer 45] 强制使用CPU
2025-07-31 15:26:18,195 - INFO - [Trainer 45] 模型已移至设备: cpu
2025-07-31 15:26:18,197 - INFO - [Trainer 45] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:18,198 - INFO - [Trainer 45] 初始化完成
2025-07-31 15:26:18,199 - INFO - [Client 45] 创建新训练器
2025-07-31 15:26:18,199 - INFO - [Algorithm] 从训练器获取客户端ID: 45
2025-07-31 15:26:18,200 - INFO - [Algorithm] 初始化后修正client_id: 45
2025-07-31 15:26:18,200 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:18,201 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:18,201 - INFO - [Client 45] 创建新算法
2025-07-31 15:26:18,202 - INFO - [Algorithm] 设置客户端ID: 45
2025-07-31 15:26:18,202 - INFO - [Algorithm] 同步更新trainer的client_id: 45
2025-07-31 15:26:18,203 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:18,309 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:18,311 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:18,311 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:18,352 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:18,355 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:18,356 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:18,360 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:18,361 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:18,361 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:18,362 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:18,362 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:18,363 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:18,363 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:18,363 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:18,364 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:18,364 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:18,365 - INFO - 顺序分配客户端ID: 45
2025-07-31 15:26:18,365 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 45
2025-07-31 15:26:18,367 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:18,369 - WARNING - [Client 45] 数据源为None，已创建新数据源
2025-07-31 15:26:18,370 - WARNING - [Client 45] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:20,984 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:20,985 - INFO - [Client 45] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:20,985 - INFO - [Client 45] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:21,007 - INFO - [Client 45] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:21,008 - INFO - [Client 45] 初始化时成功加载数据
2025-07-31 15:26:21,009 - INFO - [客户端 45] 初始化验证通过
2025-07-31 15:26:21,009 - INFO - 客户端 45 实例创建成功
2025-07-31 15:26:21,010 - INFO - 客户端45已设置服务器引用
2025-07-31 15:26:21,010 - INFO - 客户端 45 已设置服务器引用
2025-07-31 15:26:21,011 - INFO - 客户端45已注册
2025-07-31 15:26:21,011 - INFO - 客户端 45 已成功注册到服务器
2025-07-31 15:26:21,012 - INFO - 开始创建客户端 46...
2025-07-31 15:26:21,012 - INFO - 初始化客户端, ID: 46
2025-07-31 15:26:21,108 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:21,108 - INFO - [Client 46] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:21,109 - INFO - [Trainer] 初始化训练器, client_id: 46
2025-07-31 15:26:21,144 - INFO - [Trainer 46] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:21,145 - INFO - [Trainer 46] 模型的输入通道数: 3
2025-07-31 15:26:21,147 - INFO - [Trainer 46] 强制使用CPU
2025-07-31 15:26:21,150 - INFO - [Trainer 46] 模型已移至设备: cpu
2025-07-31 15:26:21,151 - INFO - [Trainer 46] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:21,152 - INFO - [Trainer 46] 初始化完成
2025-07-31 15:26:21,152 - INFO - [Client 46] 创建新训练器
2025-07-31 15:26:21,153 - INFO - [Algorithm] 从训练器获取客户端ID: 46
2025-07-31 15:26:21,153 - INFO - [Algorithm] 初始化后修正client_id: 46
2025-07-31 15:26:21,154 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:21,154 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:21,155 - INFO - [Client 46] 创建新算法
2025-07-31 15:26:21,159 - INFO - [Algorithm] 设置客户端ID: 46
2025-07-31 15:26:21,160 - INFO - [Algorithm] 同步更新trainer的client_id: 46
2025-07-31 15:26:21,162 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:21,255 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:21,256 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:21,257 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:21,284 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:21,288 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:21,289 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:21,291 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:21,292 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:21,292 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:21,294 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:21,294 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:21,294 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:21,295 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:21,295 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:21,296 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:21,297 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:21,300 - INFO - 顺序分配客户端ID: 46
2025-07-31 15:26:21,301 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 46
2025-07-31 15:26:21,303 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:21,304 - WARNING - [Client 46] 数据源为None，已创建新数据源
2025-07-31 15:26:21,305 - WARNING - [Client 46] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:23,759 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:23,760 - INFO - [Client 46] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:23,761 - INFO - [Client 46] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:23,785 - INFO - [Client 46] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:23,789 - INFO - [Client 46] 初始化时成功加载数据
2025-07-31 15:26:23,789 - INFO - [客户端 46] 初始化验证通过
2025-07-31 15:26:23,790 - INFO - 客户端 46 实例创建成功
2025-07-31 15:26:23,791 - INFO - 客户端46已设置服务器引用
2025-07-31 15:26:23,791 - INFO - 客户端 46 已设置服务器引用
2025-07-31 15:26:23,792 - INFO - 客户端46已注册
2025-07-31 15:26:23,793 - INFO - 客户端 46 已成功注册到服务器
2025-07-31 15:26:23,794 - INFO - 开始创建客户端 47...
2025-07-31 15:26:23,795 - INFO - 初始化客户端, ID: 47
2025-07-31 15:26:23,881 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:23,882 - INFO - [Client 47] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:23,882 - INFO - [Trainer] 初始化训练器, client_id: 47
2025-07-31 15:26:23,906 - INFO - [Trainer 47] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:23,907 - INFO - [Trainer 47] 模型的输入通道数: 3
2025-07-31 15:26:23,907 - INFO - [Trainer 47] 强制使用CPU
2025-07-31 15:26:23,908 - INFO - [Trainer 47] 模型已移至设备: cpu
2025-07-31 15:26:23,909 - INFO - [Trainer 47] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:23,910 - INFO - [Trainer 47] 初始化完成
2025-07-31 15:26:23,910 - INFO - [Client 47] 创建新训练器
2025-07-31 15:26:23,910 - INFO - [Algorithm] 从训练器获取客户端ID: 47
2025-07-31 15:26:23,911 - INFO - [Algorithm] 初始化后修正client_id: 47
2025-07-31 15:26:23,911 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:23,912 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:23,912 - INFO - [Client 47] 创建新算法
2025-07-31 15:26:23,912 - INFO - [Algorithm] 设置客户端ID: 47
2025-07-31 15:26:23,913 - INFO - [Algorithm] 同步更新trainer的client_id: 47
2025-07-31 15:26:23,914 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:23,986 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:23,987 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:23,988 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:24,013 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:24,014 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:24,014 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:24,015 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:24,018 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:24,019 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:24,019 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:24,020 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:24,020 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:24,021 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:24,022 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:24,022 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:24,023 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:24,023 - INFO - 顺序分配客户端ID: 47
2025-07-31 15:26:24,023 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 47
2025-07-31 15:26:24,025 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:24,028 - WARNING - [Client 47] 数据源为None，已创建新数据源
2025-07-31 15:26:24,029 - WARNING - [Client 47] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:26,755 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:26,756 - INFO - [Client 47] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:26,757 - INFO - [Client 47] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:26,781 - INFO - [Client 47] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:26,783 - INFO - [Client 47] 初始化时成功加载数据
2025-07-31 15:26:26,784 - INFO - [客户端 47] 初始化验证通过
2025-07-31 15:26:26,785 - INFO - 客户端 47 实例创建成功
2025-07-31 15:26:26,787 - INFO - 客户端47已设置服务器引用
2025-07-31 15:26:26,789 - INFO - 客户端 47 已设置服务器引用
2025-07-31 15:26:26,789 - INFO - 客户端47已注册
2025-07-31 15:26:26,792 - INFO - 客户端 47 已成功注册到服务器
2025-07-31 15:26:26,793 - INFO - 开始创建客户端 48...
2025-07-31 15:26:26,794 - INFO - 初始化客户端, ID: 48
2025-07-31 15:26:26,895 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:26,896 - INFO - [Client 48] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:26,897 - INFO - [Trainer] 初始化训练器, client_id: 48
2025-07-31 15:26:26,929 - INFO - [Trainer 48] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:26,930 - INFO - [Trainer 48] 模型的输入通道数: 3
2025-07-31 15:26:26,930 - INFO - [Trainer 48] 强制使用CPU
2025-07-31 15:26:26,932 - INFO - [Trainer 48] 模型已移至设备: cpu
2025-07-31 15:26:26,933 - INFO - [Trainer 48] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:26,935 - INFO - [Trainer 48] 初始化完成
2025-07-31 15:26:26,935 - INFO - [Client 48] 创建新训练器
2025-07-31 15:26:26,937 - INFO - [Algorithm] 从训练器获取客户端ID: 48
2025-07-31 15:26:26,937 - INFO - [Algorithm] 初始化后修正client_id: 48
2025-07-31 15:26:26,938 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:26,938 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:26,939 - INFO - [Client 48] 创建新算法
2025-07-31 15:26:26,939 - INFO - [Algorithm] 设置客户端ID: 48
2025-07-31 15:26:26,939 - INFO - [Algorithm] 同步更新trainer的client_id: 48
2025-07-31 15:26:26,940 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:27,035 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:27,035 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:27,036 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:27,064 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:27,065 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:27,066 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:27,068 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:27,069 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:27,069 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:27,070 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:27,070 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:27,071 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:27,072 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:27,073 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:27,074 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:27,074 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:27,075 - INFO - 顺序分配客户端ID: 48
2025-07-31 15:26:27,077 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 48
2025-07-31 15:26:27,080 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:27,081 - WARNING - [Client 48] 数据源为None，已创建新数据源
2025-07-31 15:26:27,082 - WARNING - [Client 48] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:29,619 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:29,620 - INFO - [Client 48] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:29,622 - INFO - [Client 48] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:29,634 - INFO - [Client 48] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:29,636 - INFO - [Client 48] 初始化时成功加载数据
2025-07-31 15:26:29,637 - INFO - [客户端 48] 初始化验证通过
2025-07-31 15:26:29,637 - INFO - 客户端 48 实例创建成功
2025-07-31 15:26:29,637 - INFO - 客户端48已设置服务器引用
2025-07-31 15:26:29,637 - INFO - 客户端 48 已设置服务器引用
2025-07-31 15:26:29,638 - INFO - 客户端48已注册
2025-07-31 15:26:29,638 - INFO - 客户端 48 已成功注册到服务器
2025-07-31 15:26:29,638 - INFO - 开始创建客户端 49...
2025-07-31 15:26:29,639 - INFO - 初始化客户端, ID: 49
2025-07-31 15:26:29,735 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:29,737 - INFO - [Client 49] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:29,738 - INFO - [Trainer] 初始化训练器, client_id: 49
2025-07-31 15:26:29,777 - INFO - [Trainer 49] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:29,778 - INFO - [Trainer 49] 模型的输入通道数: 3
2025-07-31 15:26:29,779 - INFO - [Trainer 49] 强制使用CPU
2025-07-31 15:26:29,781 - INFO - [Trainer 49] 模型已移至设备: cpu
2025-07-31 15:26:29,783 - INFO - [Trainer 49] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:29,784 - INFO - [Trainer 49] 初始化完成
2025-07-31 15:26:29,784 - INFO - [Client 49] 创建新训练器
2025-07-31 15:26:29,785 - INFO - [Algorithm] 从训练器获取客户端ID: 49
2025-07-31 15:26:29,785 - INFO - [Algorithm] 初始化后修正client_id: 49
2025-07-31 15:26:29,785 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:29,785 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:29,787 - INFO - [Client 49] 创建新算法
2025-07-31 15:26:29,787 - INFO - [Algorithm] 设置客户端ID: 49
2025-07-31 15:26:29,788 - INFO - [Algorithm] 同步更新trainer的client_id: 49
2025-07-31 15:26:29,789 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:29,879 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:29,880 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:29,882 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:29,923 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:29,927 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:29,928 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:29,930 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:29,932 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:29,932 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:29,933 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:29,934 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:29,935 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:29,935 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:29,938 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:29,938 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:29,939 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:29,940 - INFO - 顺序分配客户端ID: 49
2025-07-31 15:26:29,940 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 49
2025-07-31 15:26:29,942 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:29,944 - WARNING - [Client 49] 数据源为None，已创建新数据源
2025-07-31 15:26:29,945 - WARNING - [Client 49] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:32,437 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:32,438 - INFO - [Client 49] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:32,438 - INFO - [Client 49] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:32,456 - INFO - [Client 49] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:32,456 - INFO - [Client 49] 初始化时成功加载数据
2025-07-31 15:26:32,457 - INFO - [客户端 49] 初始化验证通过
2025-07-31 15:26:32,457 - INFO - 客户端 49 实例创建成功
2025-07-31 15:26:32,459 - INFO - 客户端49已设置服务器引用
2025-07-31 15:26:32,461 - INFO - 客户端 49 已设置服务器引用
2025-07-31 15:26:32,462 - INFO - 客户端49已注册
2025-07-31 15:26:32,463 - INFO - 客户端 49 已成功注册到服务器
2025-07-31 15:26:32,464 - INFO - 开始创建客户端 50...
2025-07-31 15:26:32,464 - INFO - 初始化客户端, ID: 50
2025-07-31 15:26:32,554 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:32,554 - INFO - [Client 50] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:32,555 - INFO - [Trainer] 初始化训练器, client_id: 50
2025-07-31 15:26:32,594 - INFO - [Trainer 50] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:32,596 - INFO - [Trainer 50] 模型的输入通道数: 3
2025-07-31 15:26:32,597 - INFO - [Trainer 50] 强制使用CPU
2025-07-31 15:26:32,600 - INFO - [Trainer 50] 模型已移至设备: cpu
2025-07-31 15:26:32,601 - INFO - [Trainer 50] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:32,601 - INFO - [Trainer 50] 初始化完成
2025-07-31 15:26:32,602 - INFO - [Client 50] 创建新训练器
2025-07-31 15:26:32,602 - INFO - [Algorithm] 从训练器获取客户端ID: 50
2025-07-31 15:26:32,603 - INFO - [Algorithm] 初始化后修正client_id: 50
2025-07-31 15:26:32,603 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:32,604 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:32,605 - INFO - [Client 50] 创建新算法
2025-07-31 15:26:32,605 - INFO - [Algorithm] 设置客户端ID: 50
2025-07-31 15:26:32,608 - INFO - [Algorithm] 同步更新trainer的client_id: 50
2025-07-31 15:26:32,609 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:32,699 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:32,700 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:32,701 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:32,745 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:32,747 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:32,747 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:32,751 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:32,753 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:32,753 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:32,754 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:32,754 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:32,754 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:32,755 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:32,755 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:32,755 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:32,757 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:32,757 - INFO - 顺序分配客户端ID: 50
2025-07-31 15:26:32,757 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 50
2025-07-31 15:26:32,758 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:32,760 - WARNING - [Client 50] 数据源为None，已创建新数据源
2025-07-31 15:26:32,760 - WARNING - [Client 50] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:35,451 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:35,452 - INFO - [Client 50] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:35,452 - INFO - [Client 50] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:35,465 - INFO - [Client 50] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:35,466 - INFO - [Client 50] 初始化时成功加载数据
2025-07-31 15:26:35,467 - INFO - [客户端 50] 初始化验证通过
2025-07-31 15:26:35,467 - INFO - 客户端 50 实例创建成功
2025-07-31 15:26:35,467 - INFO - 客户端50已设置服务器引用
2025-07-31 15:26:35,468 - INFO - 客户端 50 已设置服务器引用
2025-07-31 15:26:35,468 - INFO - 客户端50已注册
2025-07-31 15:26:35,468 - INFO - 客户端 50 已成功注册到服务器
2025-07-31 15:26:35,468 - INFO - 开始创建客户端 51...
2025-07-31 15:26:35,469 - INFO - 初始化客户端, ID: 51
2025-07-31 15:26:35,531 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:35,532 - INFO - [Client 51] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:35,532 - INFO - [Trainer] 初始化训练器, client_id: 51
2025-07-31 15:26:35,553 - INFO - [Trainer 51] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:35,554 - INFO - [Trainer 51] 模型的输入通道数: 3
2025-07-31 15:26:35,555 - INFO - [Trainer 51] 强制使用CPU
2025-07-31 15:26:35,555 - INFO - [Trainer 51] 模型已移至设备: cpu
2025-07-31 15:26:35,557 - INFO - [Trainer 51] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:35,558 - INFO - [Trainer 51] 初始化完成
2025-07-31 15:26:35,558 - INFO - [Client 51] 创建新训练器
2025-07-31 15:26:35,558 - INFO - [Algorithm] 从训练器获取客户端ID: 51
2025-07-31 15:26:35,558 - INFO - [Algorithm] 初始化后修正client_id: 51
2025-07-31 15:26:35,559 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:35,559 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:35,559 - INFO - [Client 51] 创建新算法
2025-07-31 15:26:35,559 - INFO - [Algorithm] 设置客户端ID: 51
2025-07-31 15:26:35,560 - INFO - [Algorithm] 同步更新trainer的client_id: 51
2025-07-31 15:26:35,560 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:35,631 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:35,631 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:35,632 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:35,655 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:35,657 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:35,657 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:35,659 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:35,660 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:35,660 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:35,661 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:35,661 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:35,661 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:35,662 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:35,662 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:35,662 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:35,664 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:35,664 - INFO - 顺序分配客户端ID: 51
2025-07-31 15:26:35,664 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 51
2025-07-31 15:26:35,665 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:35,666 - WARNING - [Client 51] 数据源为None，已创建新数据源
2025-07-31 15:26:35,666 - WARNING - [Client 51] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:37,755 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:37,755 - INFO - [Client 51] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:37,757 - INFO - [Client 51] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:37,771 - INFO - [Client 51] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:37,772 - INFO - [Client 51] 初始化时成功加载数据
2025-07-31 15:26:37,772 - INFO - [客户端 51] 初始化验证通过
2025-07-31 15:26:37,772 - INFO - 客户端 51 实例创建成功
2025-07-31 15:26:37,772 - INFO - 客户端51已设置服务器引用
2025-07-31 15:26:37,773 - INFO - 客户端 51 已设置服务器引用
2025-07-31 15:26:37,773 - INFO - 客户端51已注册
2025-07-31 15:26:37,774 - INFO - 客户端 51 已成功注册到服务器
2025-07-31 15:26:37,774 - INFO - 开始创建客户端 52...
2025-07-31 15:26:37,774 - INFO - 初始化客户端, ID: 52
2025-07-31 15:26:37,845 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:37,846 - INFO - [Client 52] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:37,846 - INFO - [Trainer] 初始化训练器, client_id: 52
2025-07-31 15:26:37,871 - INFO - [Trainer 52] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:37,872 - INFO - [Trainer 52] 模型的输入通道数: 3
2025-07-31 15:26:37,872 - INFO - [Trainer 52] 强制使用CPU
2025-07-31 15:26:37,874 - INFO - [Trainer 52] 模型已移至设备: cpu
2025-07-31 15:26:37,875 - INFO - [Trainer 52] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:37,875 - INFO - [Trainer 52] 初始化完成
2025-07-31 15:26:37,876 - INFO - [Client 52] 创建新训练器
2025-07-31 15:26:37,876 - INFO - [Algorithm] 从训练器获取客户端ID: 52
2025-07-31 15:26:37,876 - INFO - [Algorithm] 初始化后修正client_id: 52
2025-07-31 15:26:37,876 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:37,877 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:37,877 - INFO - [Client 52] 创建新算法
2025-07-31 15:26:37,877 - INFO - [Algorithm] 设置客户端ID: 52
2025-07-31 15:26:37,877 - INFO - [Algorithm] 同步更新trainer的client_id: 52
2025-07-31 15:26:37,878 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:37,954 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:37,955 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:37,955 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:37,985 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:37,986 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:37,987 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:37,988 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:37,990 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:37,990 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:37,991 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:37,991 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:37,992 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:37,992 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:37,993 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:37,993 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:37,995 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:37,995 - INFO - 顺序分配客户端ID: 52
2025-07-31 15:26:37,996 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 52
2025-07-31 15:26:37,997 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:37,998 - WARNING - [Client 52] 数据源为None，已创建新数据源
2025-07-31 15:26:37,998 - WARNING - [Client 52] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:40,544 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:40,545 - INFO - [Client 52] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:40,545 - INFO - [Client 52] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:40,566 - INFO - [Client 52] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:40,567 - INFO - [Client 52] 初始化时成功加载数据
2025-07-31 15:26:40,568 - INFO - [客户端 52] 初始化验证通过
2025-07-31 15:26:40,568 - INFO - 客户端 52 实例创建成功
2025-07-31 15:26:40,568 - INFO - 客户端52已设置服务器引用
2025-07-31 15:26:40,568 - INFO - 客户端 52 已设置服务器引用
2025-07-31 15:26:40,569 - INFO - 客户端52已注册
2025-07-31 15:26:40,569 - INFO - 客户端 52 已成功注册到服务器
2025-07-31 15:26:40,570 - INFO - 开始创建客户端 53...
2025-07-31 15:26:40,570 - INFO - 初始化客户端, ID: 53
2025-07-31 15:26:40,665 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:40,665 - INFO - [Client 53] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:40,666 - INFO - [Trainer] 初始化训练器, client_id: 53
2025-07-31 15:26:40,698 - INFO - [Trainer 53] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:40,699 - INFO - [Trainer 53] 模型的输入通道数: 3
2025-07-31 15:26:40,699 - INFO - [Trainer 53] 强制使用CPU
2025-07-31 15:26:40,702 - INFO - [Trainer 53] 模型已移至设备: cpu
2025-07-31 15:26:40,704 - INFO - [Trainer 53] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:40,704 - INFO - [Trainer 53] 初始化完成
2025-07-31 15:26:40,705 - INFO - [Client 53] 创建新训练器
2025-07-31 15:26:40,705 - INFO - [Algorithm] 从训练器获取客户端ID: 53
2025-07-31 15:26:40,706 - INFO - [Algorithm] 初始化后修正client_id: 53
2025-07-31 15:26:40,706 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:40,706 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:40,707 - INFO - [Client 53] 创建新算法
2025-07-31 15:26:40,708 - INFO - [Algorithm] 设置客户端ID: 53
2025-07-31 15:26:40,708 - INFO - [Algorithm] 同步更新trainer的client_id: 53
2025-07-31 15:26:40,709 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:40,805 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:40,806 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:40,806 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:40,838 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:40,839 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:40,839 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:40,843 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:40,845 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:40,845 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:40,845 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:40,847 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:40,847 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:40,847 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:40,847 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:40,848 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:40,848 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:40,848 - INFO - 顺序分配客户端ID: 53
2025-07-31 15:26:40,849 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 53
2025-07-31 15:26:40,850 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:40,851 - WARNING - [Client 53] 数据源为None，已创建新数据源
2025-07-31 15:26:40,851 - WARNING - [Client 53] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:43,394 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:43,395 - INFO - [Client 53] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:43,397 - INFO - [Client 53] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:43,422 - INFO - [Client 53] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:43,422 - INFO - [Client 53] 初始化时成功加载数据
2025-07-31 15:26:43,423 - INFO - [客户端 53] 初始化验证通过
2025-07-31 15:26:43,424 - INFO - 客户端 53 实例创建成功
2025-07-31 15:26:43,424 - INFO - 客户端53已设置服务器引用
2025-07-31 15:26:43,425 - INFO - 客户端 53 已设置服务器引用
2025-07-31 15:26:43,425 - INFO - 客户端53已注册
2025-07-31 15:26:43,425 - INFO - 客户端 53 已成功注册到服务器
2025-07-31 15:26:43,425 - INFO - 开始创建客户端 54...
2025-07-31 15:26:43,426 - INFO - 初始化客户端, ID: 54
2025-07-31 15:26:43,520 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:43,521 - INFO - [Client 54] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:43,521 - INFO - [Trainer] 初始化训练器, client_id: 54
2025-07-31 15:26:43,552 - INFO - [Trainer 54] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:43,553 - INFO - [Trainer 54] 模型的输入通道数: 3
2025-07-31 15:26:43,554 - INFO - [Trainer 54] 强制使用CPU
2025-07-31 15:26:43,555 - INFO - [Trainer 54] 模型已移至设备: cpu
2025-07-31 15:26:43,556 - INFO - [Trainer 54] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:43,557 - INFO - [Trainer 54] 初始化完成
2025-07-31 15:26:43,557 - INFO - [Client 54] 创建新训练器
2025-07-31 15:26:43,558 - INFO - [Algorithm] 从训练器获取客户端ID: 54
2025-07-31 15:26:43,558 - INFO - [Algorithm] 初始化后修正client_id: 54
2025-07-31 15:26:43,558 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:43,559 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:43,560 - INFO - [Client 54] 创建新算法
2025-07-31 15:26:43,562 - INFO - [Algorithm] 设置客户端ID: 54
2025-07-31 15:26:43,562 - INFO - [Algorithm] 同步更新trainer的client_id: 54
2025-07-31 15:26:43,562 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:43,651 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:43,652 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:43,653 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:43,689 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:43,689 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:43,690 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:43,692 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:43,692 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:43,693 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:43,693 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:43,693 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:43,695 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:43,695 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:43,697 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:43,697 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:43,697 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:43,698 - INFO - 顺序分配客户端ID: 54
2025-07-31 15:26:43,698 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 54
2025-07-31 15:26:43,700 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:43,704 - WARNING - [Client 54] 数据源为None，已创建新数据源
2025-07-31 15:26:43,705 - WARNING - [Client 54] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:46,320 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:46,321 - INFO - [Client 54] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:46,321 - INFO - [Client 54] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:46,347 - INFO - [Client 54] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:46,347 - INFO - [Client 54] 初始化时成功加载数据
2025-07-31 15:26:46,349 - INFO - [客户端 54] 初始化验证通过
2025-07-31 15:26:46,349 - INFO - 客户端 54 实例创建成功
2025-07-31 15:26:46,350 - INFO - 客户端54已设置服务器引用
2025-07-31 15:26:46,351 - INFO - 客户端 54 已设置服务器引用
2025-07-31 15:26:46,351 - INFO - 客户端54已注册
2025-07-31 15:26:46,351 - INFO - 客户端 54 已成功注册到服务器
2025-07-31 15:26:46,353 - INFO - 开始创建客户端 55...
2025-07-31 15:26:46,354 - INFO - 初始化客户端, ID: 55
2025-07-31 15:26:46,472 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:46,473 - INFO - [Client 55] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:46,473 - INFO - [Trainer] 初始化训练器, client_id: 55
2025-07-31 15:26:46,508 - INFO - [Trainer 55] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:46,509 - INFO - [Trainer 55] 模型的输入通道数: 3
2025-07-31 15:26:46,510 - INFO - [Trainer 55] 强制使用CPU
2025-07-31 15:26:46,513 - INFO - [Trainer 55] 模型已移至设备: cpu
2025-07-31 15:26:46,515 - INFO - [Trainer 55] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:46,515 - INFO - [Trainer 55] 初始化完成
2025-07-31 15:26:46,515 - INFO - [Client 55] 创建新训练器
2025-07-31 15:26:46,517 - INFO - [Algorithm] 从训练器获取客户端ID: 55
2025-07-31 15:26:46,517 - INFO - [Algorithm] 初始化后修正client_id: 55
2025-07-31 15:26:46,518 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:46,519 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:46,520 - INFO - [Client 55] 创建新算法
2025-07-31 15:26:46,524 - INFO - [Algorithm] 设置客户端ID: 55
2025-07-31 15:26:46,525 - INFO - [Algorithm] 同步更新trainer的client_id: 55
2025-07-31 15:26:46,525 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:46,620 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:46,620 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:46,621 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:46,654 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:46,655 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:46,656 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:46,659 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:46,667 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:46,668 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:46,669 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:46,670 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:46,671 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:46,671 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:46,672 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:46,672 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:46,674 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:46,675 - INFO - 顺序分配客户端ID: 55
2025-07-31 15:26:46,677 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 55
2025-07-31 15:26:46,682 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:46,688 - WARNING - [Client 55] 数据源为None，已创建新数据源
2025-07-31 15:26:46,688 - WARNING - [Client 55] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:49,525 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:49,525 - INFO - [Client 55] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:49,525 - INFO - [Client 55] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:49,544 - INFO - [Client 55] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:49,545 - INFO - [Client 55] 初始化时成功加载数据
2025-07-31 15:26:49,545 - INFO - [客户端 55] 初始化验证通过
2025-07-31 15:26:49,545 - INFO - 客户端 55 实例创建成功
2025-07-31 15:26:49,546 - INFO - 客户端55已设置服务器引用
2025-07-31 15:26:49,546 - INFO - 客户端 55 已设置服务器引用
2025-07-31 15:26:49,546 - INFO - 客户端55已注册
2025-07-31 15:26:49,547 - INFO - 客户端 55 已成功注册到服务器
2025-07-31 15:26:49,547 - INFO - 开始创建客户端 56...
2025-07-31 15:26:49,548 - INFO - 初始化客户端, ID: 56
2025-07-31 15:26:49,657 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:49,658 - INFO - [Client 56] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:49,659 - INFO - [Trainer] 初始化训练器, client_id: 56
2025-07-31 15:26:49,697 - INFO - [Trainer 56] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:49,698 - INFO - [Trainer 56] 模型的输入通道数: 3
2025-07-31 15:26:49,699 - INFO - [Trainer 56] 强制使用CPU
2025-07-31 15:26:49,701 - INFO - [Trainer 56] 模型已移至设备: cpu
2025-07-31 15:26:49,703 - INFO - [Trainer 56] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:49,703 - INFO - [Trainer 56] 初始化完成
2025-07-31 15:26:49,704 - INFO - [Client 56] 创建新训练器
2025-07-31 15:26:49,705 - INFO - [Algorithm] 从训练器获取客户端ID: 56
2025-07-31 15:26:49,705 - INFO - [Algorithm] 初始化后修正client_id: 56
2025-07-31 15:26:49,705 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:49,706 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:49,706 - INFO - [Client 56] 创建新算法
2025-07-31 15:26:49,707 - INFO - [Algorithm] 设置客户端ID: 56
2025-07-31 15:26:49,707 - INFO - [Algorithm] 同步更新trainer的client_id: 56
2025-07-31 15:26:49,707 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:49,799 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:49,800 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:49,800 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:49,828 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:49,828 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:49,830 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:49,831 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:49,832 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:49,833 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:49,834 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:49,834 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:49,835 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:49,835 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:49,836 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:49,837 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:49,837 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:49,837 - INFO - 顺序分配客户端ID: 56
2025-07-31 15:26:49,838 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 56
2025-07-31 15:26:49,839 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:49,839 - WARNING - [Client 56] 数据源为None，已创建新数据源
2025-07-31 15:26:49,840 - WARNING - [Client 56] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:52,462 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:52,463 - INFO - [Client 56] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:52,463 - INFO - [Client 56] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:52,479 - INFO - [Client 56] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:52,479 - INFO - [Client 56] 初始化时成功加载数据
2025-07-31 15:26:52,479 - INFO - [客户端 56] 初始化验证通过
2025-07-31 15:26:52,480 - INFO - 客户端 56 实例创建成功
2025-07-31 15:26:52,481 - INFO - 客户端56已设置服务器引用
2025-07-31 15:26:52,481 - INFO - 客户端 56 已设置服务器引用
2025-07-31 15:26:52,482 - INFO - 客户端56已注册
2025-07-31 15:26:52,482 - INFO - 客户端 56 已成功注册到服务器
2025-07-31 15:26:52,482 - INFO - 开始创建客户端 57...
2025-07-31 15:26:52,482 - INFO - 初始化客户端, ID: 57
2025-07-31 15:26:52,568 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:52,569 - INFO - [Client 57] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:52,569 - INFO - [Trainer] 初始化训练器, client_id: 57
2025-07-31 15:26:52,599 - INFO - [Trainer 57] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:52,600 - INFO - [Trainer 57] 模型的输入通道数: 3
2025-07-31 15:26:52,601 - INFO - [Trainer 57] 强制使用CPU
2025-07-31 15:26:52,603 - INFO - [Trainer 57] 模型已移至设备: cpu
2025-07-31 15:26:52,605 - INFO - [Trainer 57] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:52,606 - INFO - [Trainer 57] 初始化完成
2025-07-31 15:26:52,607 - INFO - [Client 57] 创建新训练器
2025-07-31 15:26:52,607 - INFO - [Algorithm] 从训练器获取客户端ID: 57
2025-07-31 15:26:52,607 - INFO - [Algorithm] 初始化后修正client_id: 57
2025-07-31 15:26:52,608 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:52,608 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:52,609 - INFO - [Client 57] 创建新算法
2025-07-31 15:26:52,609 - INFO - [Algorithm] 设置客户端ID: 57
2025-07-31 15:26:52,609 - INFO - [Algorithm] 同步更新trainer的client_id: 57
2025-07-31 15:26:52,610 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:52,703 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:52,704 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:52,704 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:52,745 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:52,746 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:52,747 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:52,749 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:52,750 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:52,750 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:52,751 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:52,751 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:52,752 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:52,753 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:52,753 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:52,754 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:52,755 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:52,757 - INFO - 顺序分配客户端ID: 57
2025-07-31 15:26:52,758 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 57
2025-07-31 15:26:52,760 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:52,762 - WARNING - [Client 57] 数据源为None，已创建新数据源
2025-07-31 15:26:52,762 - WARNING - [Client 57] 数据源trainset为None，已设置为空列表
2025-07-31 15:26:55,188 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:26:55,189 - INFO - [Client 57] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:26:55,189 - INFO - [Client 57] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:26:55,204 - INFO - [Client 57] 成功划分数据集，分配到 300 个样本
2025-07-31 15:26:55,206 - INFO - [Client 57] 初始化时成功加载数据
2025-07-31 15:26:55,206 - INFO - [客户端 57] 初始化验证通过
2025-07-31 15:26:55,207 - INFO - 客户端 57 实例创建成功
2025-07-31 15:26:55,207 - INFO - 客户端57已设置服务器引用
2025-07-31 15:26:55,207 - INFO - 客户端 57 已设置服务器引用
2025-07-31 15:26:55,209 - INFO - 客户端57已注册
2025-07-31 15:26:55,210 - INFO - 客户端 57 已成功注册到服务器
2025-07-31 15:26:55,210 - INFO - 开始创建客户端 58...
2025-07-31 15:26:55,211 - INFO - 初始化客户端, ID: 58
2025-07-31 15:26:55,290 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:55,290 - INFO - [Client 58] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:55,291 - INFO - [Trainer] 初始化训练器, client_id: 58
2025-07-31 15:26:59,025 - INFO - [Trainer 58] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:59,026 - INFO - [Trainer 58] 模型的输入通道数: 3
2025-07-31 15:26:59,026 - INFO - [Trainer 58] 强制使用CPU
2025-07-31 15:26:59,028 - INFO - [Trainer 58] 模型已移至设备: cpu
2025-07-31 15:26:59,029 - INFO - [Trainer 58] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:59,030 - INFO - [Trainer 58] 初始化完成
2025-07-31 15:26:59,032 - INFO - [Client 58] 创建新训练器
2025-07-31 15:26:59,032 - INFO - [Algorithm] 从训练器获取客户端ID: 58
2025-07-31 15:26:59,033 - INFO - [Algorithm] 初始化后修正client_id: 58
2025-07-31 15:26:59,034 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:59,034 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:59,037 - INFO - [Client 58] 创建新算法
2025-07-31 15:26:59,039 - INFO - [Algorithm] 设置客户端ID: 58
2025-07-31 15:26:59,042 - INFO - [Algorithm] 同步更新trainer的client_id: 58
2025-07-31 15:26:59,044 - INFO - [Client None] 父类初始化完成
2025-07-31 15:26:59,128 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:26:59,128 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:26:59,129 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:26:59,153 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:26:59,154 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:26:59,154 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:26:59,156 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:26:59,157 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:26:59,157 - INFO - [Trainer None] 初始化完成
2025-07-31 15:26:59,157 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:26:59,158 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:26:59,158 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:26:59,158 - INFO - [Algorithm] 初始化完成
2025-07-31 15:26:59,159 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:26:59,159 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:26:59,160 - INFO - [Client None] 开始加载数据
2025-07-31 15:26:59,160 - INFO - 顺序分配客户端ID: 58
2025-07-31 15:26:59,160 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 58
2025-07-31 15:26:59,162 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:26:59,163 - WARNING - [Client 58] 数据源为None，已创建新数据源
2025-07-31 15:26:59,163 - WARNING - [Client 58] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:01,820 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:01,821 - INFO - [Client 58] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:01,822 - INFO - [Client 58] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:01,840 - INFO - [Client 58] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:01,841 - INFO - [Client 58] 初始化时成功加载数据
2025-07-31 15:27:01,842 - INFO - [客户端 58] 初始化验证通过
2025-07-31 15:27:01,843 - INFO - 客户端 58 实例创建成功
2025-07-31 15:27:01,844 - INFO - 客户端58已设置服务器引用
2025-07-31 15:27:01,845 - INFO - 客户端 58 已设置服务器引用
2025-07-31 15:27:01,851 - INFO - 客户端58已注册
2025-07-31 15:27:01,852 - INFO - 客户端 58 已成功注册到服务器
2025-07-31 15:27:01,852 - INFO - 开始创建客户端 59...
2025-07-31 15:27:01,853 - INFO - 初始化客户端, ID: 59
2025-07-31 15:27:01,967 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:01,968 - INFO - [Client 59] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:01,969 - INFO - [Trainer] 初始化训练器, client_id: 59
2025-07-31 15:27:02,003 - INFO - [Trainer 59] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:02,004 - INFO - [Trainer 59] 模型的输入通道数: 3
2025-07-31 15:27:02,005 - INFO - [Trainer 59] 强制使用CPU
2025-07-31 15:27:02,006 - INFO - [Trainer 59] 模型已移至设备: cpu
2025-07-31 15:27:02,007 - INFO - [Trainer 59] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:02,007 - INFO - [Trainer 59] 初始化完成
2025-07-31 15:27:02,007 - INFO - [Client 59] 创建新训练器
2025-07-31 15:27:02,008 - INFO - [Algorithm] 从训练器获取客户端ID: 59
2025-07-31 15:27:02,011 - INFO - [Algorithm] 初始化后修正client_id: 59
2025-07-31 15:27:02,013 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:02,013 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:02,014 - INFO - [Client 59] 创建新算法
2025-07-31 15:27:02,015 - INFO - [Algorithm] 设置客户端ID: 59
2025-07-31 15:27:02,015 - INFO - [Algorithm] 同步更新trainer的client_id: 59
2025-07-31 15:27:02,017 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:02,091 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:02,092 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:02,092 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:02,117 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:02,118 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:02,118 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:02,120 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:02,121 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:02,121 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:02,122 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:02,122 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:02,124 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:02,124 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:02,125 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:02,125 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:02,125 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:02,126 - INFO - 顺序分配客户端ID: 59
2025-07-31 15:27:02,127 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 59
2025-07-31 15:27:02,128 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:02,129 - WARNING - [Client 59] 数据源为None，已创建新数据源
2025-07-31 15:27:02,130 - WARNING - [Client 59] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:04,796 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:04,797 - INFO - [Client 59] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:04,798 - INFO - [Client 59] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:04,813 - INFO - [Client 59] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:04,814 - INFO - [Client 59] 初始化时成功加载数据
2025-07-31 15:27:04,815 - INFO - [客户端 59] 初始化验证通过
2025-07-31 15:27:04,815 - INFO - 客户端 59 实例创建成功
2025-07-31 15:27:04,815 - INFO - 客户端59已设置服务器引用
2025-07-31 15:27:04,817 - INFO - 客户端 59 已设置服务器引用
2025-07-31 15:27:04,817 - INFO - 客户端59已注册
2025-07-31 15:27:04,818 - INFO - 客户端 59 已成功注册到服务器
2025-07-31 15:27:04,818 - INFO - 开始创建客户端 60...
2025-07-31 15:27:04,819 - INFO - 初始化客户端, ID: 60
2025-07-31 15:27:04,915 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:04,915 - INFO - [Client 60] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:04,917 - INFO - [Trainer] 初始化训练器, client_id: 60
2025-07-31 15:27:04,954 - INFO - [Trainer 60] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:04,955 - INFO - [Trainer 60] 模型的输入通道数: 3
2025-07-31 15:27:04,955 - INFO - [Trainer 60] 强制使用CPU
2025-07-31 15:27:04,957 - INFO - [Trainer 60] 模型已移至设备: cpu
2025-07-31 15:27:04,958 - INFO - [Trainer 60] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:04,959 - INFO - [Trainer 60] 初始化完成
2025-07-31 15:27:04,959 - INFO - [Client 60] 创建新训练器
2025-07-31 15:27:04,960 - INFO - [Algorithm] 从训练器获取客户端ID: 60
2025-07-31 15:27:04,960 - INFO - [Algorithm] 初始化后修正client_id: 60
2025-07-31 15:27:04,961 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:04,961 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:04,962 - INFO - [Client 60] 创建新算法
2025-07-31 15:27:04,962 - INFO - [Algorithm] 设置客户端ID: 60
2025-07-31 15:27:04,963 - INFO - [Algorithm] 同步更新trainer的client_id: 60
2025-07-31 15:27:04,963 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:05,031 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:05,032 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:05,032 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:05,054 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:05,055 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:05,056 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:05,057 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:05,058 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:05,059 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:05,060 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:05,060 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:05,060 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:05,061 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:05,061 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:05,062 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:05,062 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:05,063 - INFO - 顺序分配客户端ID: 60
2025-07-31 15:27:05,063 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 60
2025-07-31 15:27:05,065 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:05,065 - WARNING - [Client 60] 数据源为None，已创建新数据源
2025-07-31 15:27:05,066 - WARNING - [Client 60] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:07,320 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:07,320 - INFO - [Client 60] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:07,321 - INFO - [Client 60] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:07,337 - INFO - [Client 60] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:07,337 - INFO - [Client 60] 初始化时成功加载数据
2025-07-31 15:27:07,338 - INFO - [客户端 60] 初始化验证通过
2025-07-31 15:27:07,338 - INFO - 客户端 60 实例创建成功
2025-07-31 15:27:07,338 - INFO - 客户端60已设置服务器引用
2025-07-31 15:27:07,340 - INFO - 客户端 60 已设置服务器引用
2025-07-31 15:27:07,340 - INFO - 客户端60已注册
2025-07-31 15:27:07,341 - INFO - 客户端 60 已成功注册到服务器
2025-07-31 15:27:07,341 - INFO - 开始创建客户端 61...
2025-07-31 15:27:07,342 - INFO - 初始化客户端, ID: 61
2025-07-31 15:27:07,416 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:07,417 - INFO - [Client 61] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:07,417 - INFO - [Trainer] 初始化训练器, client_id: 61
2025-07-31 15:27:07,445 - INFO - [Trainer 61] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:07,447 - INFO - [Trainer 61] 模型的输入通道数: 3
2025-07-31 15:27:07,447 - INFO - [Trainer 61] 强制使用CPU
2025-07-31 15:27:07,449 - INFO - [Trainer 61] 模型已移至设备: cpu
2025-07-31 15:27:07,450 - INFO - [Trainer 61] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:07,450 - INFO - [Trainer 61] 初始化完成
2025-07-31 15:27:07,452 - INFO - [Client 61] 创建新训练器
2025-07-31 15:27:07,453 - INFO - [Algorithm] 从训练器获取客户端ID: 61
2025-07-31 15:27:07,454 - INFO - [Algorithm] 初始化后修正client_id: 61
2025-07-31 15:27:07,455 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:07,455 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:07,455 - INFO - [Client 61] 创建新算法
2025-07-31 15:27:07,459 - INFO - [Algorithm] 设置客户端ID: 61
2025-07-31 15:27:07,459 - INFO - [Algorithm] 同步更新trainer的client_id: 61
2025-07-31 15:27:07,460 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:07,558 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:07,560 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:07,561 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:07,602 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:07,603 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:07,604 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:07,607 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:07,610 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:07,613 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:07,614 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:07,615 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:07,618 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:07,619 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:07,621 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:07,624 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:07,628 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:07,629 - INFO - 顺序分配客户端ID: 61
2025-07-31 15:27:07,630 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 61
2025-07-31 15:27:07,637 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:07,638 - WARNING - [Client 61] 数据源为None，已创建新数据源
2025-07-31 15:27:07,639 - WARNING - [Client 61] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:10,101 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:10,102 - INFO - [Client 61] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:10,102 - INFO - [Client 61] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:10,115 - INFO - [Client 61] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:10,115 - INFO - [Client 61] 初始化时成功加载数据
2025-07-31 15:27:10,116 - INFO - [客户端 61] 初始化验证通过
2025-07-31 15:27:10,116 - INFO - 客户端 61 实例创建成功
2025-07-31 15:27:10,116 - INFO - 客户端61已设置服务器引用
2025-07-31 15:27:10,117 - INFO - 客户端 61 已设置服务器引用
2025-07-31 15:27:10,117 - INFO - 客户端61已注册
2025-07-31 15:27:10,117 - INFO - 客户端 61 已成功注册到服务器
2025-07-31 15:27:10,117 - INFO - 开始创建客户端 62...
2025-07-31 15:27:10,118 - INFO - 初始化客户端, ID: 62
2025-07-31 15:27:10,184 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:10,184 - INFO - [Client 62] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:10,185 - INFO - [Trainer] 初始化训练器, client_id: 62
2025-07-31 15:27:10,210 - INFO - [Trainer 62] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:10,211 - INFO - [Trainer 62] 模型的输入通道数: 3
2025-07-31 15:27:10,212 - INFO - [Trainer 62] 强制使用CPU
2025-07-31 15:27:10,213 - INFO - [Trainer 62] 模型已移至设备: cpu
2025-07-31 15:27:10,214 - INFO - [Trainer 62] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:10,214 - INFO - [Trainer 62] 初始化完成
2025-07-31 15:27:10,215 - INFO - [Client 62] 创建新训练器
2025-07-31 15:27:10,215 - INFO - [Algorithm] 从训练器获取客户端ID: 62
2025-07-31 15:27:10,215 - INFO - [Algorithm] 初始化后修正client_id: 62
2025-07-31 15:27:10,215 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:10,215 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:10,217 - INFO - [Client 62] 创建新算法
2025-07-31 15:27:10,217 - INFO - [Algorithm] 设置客户端ID: 62
2025-07-31 15:27:10,218 - INFO - [Algorithm] 同步更新trainer的client_id: 62
2025-07-31 15:27:10,218 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:10,289 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:10,289 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:10,290 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:10,315 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:10,315 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:10,317 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:10,319 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:10,320 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:10,320 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:10,320 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:10,321 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:10,321 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:10,321 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:10,322 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:10,322 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:10,322 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:10,323 - INFO - 顺序分配客户端ID: 62
2025-07-31 15:27:10,323 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 62
2025-07-31 15:27:10,324 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:10,325 - WARNING - [Client 62] 数据源为None，已创建新数据源
2025-07-31 15:27:10,325 - WARNING - [Client 62] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:12,820 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:12,821 - INFO - [Client 62] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:12,822 - INFO - [Client 62] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:12,840 - INFO - [Client 62] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:12,841 - INFO - [Client 62] 初始化时成功加载数据
2025-07-31 15:27:12,842 - INFO - [客户端 62] 初始化验证通过
2025-07-31 15:27:12,843 - INFO - 客户端 62 实例创建成功
2025-07-31 15:27:12,845 - INFO - 客户端62已设置服务器引用
2025-07-31 15:27:12,847 - INFO - 客户端 62 已设置服务器引用
2025-07-31 15:27:12,848 - INFO - 客户端62已注册
2025-07-31 15:27:12,849 - INFO - 客户端 62 已成功注册到服务器
2025-07-31 15:27:12,851 - INFO - 开始创建客户端 63...
2025-07-31 15:27:12,853 - INFO - 初始化客户端, ID: 63
2025-07-31 15:27:12,962 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:12,963 - INFO - [Client 63] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:12,964 - INFO - [Trainer] 初始化训练器, client_id: 63
2025-07-31 15:27:12,999 - INFO - [Trainer 63] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:13,000 - INFO - [Trainer 63] 模型的输入通道数: 3
2025-07-31 15:27:13,001 - INFO - [Trainer 63] 强制使用CPU
2025-07-31 15:27:13,004 - INFO - [Trainer 63] 模型已移至设备: cpu
2025-07-31 15:27:13,005 - INFO - [Trainer 63] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:13,008 - INFO - [Trainer 63] 初始化完成
2025-07-31 15:27:13,009 - INFO - [Client 63] 创建新训练器
2025-07-31 15:27:13,009 - INFO - [Algorithm] 从训练器获取客户端ID: 63
2025-07-31 15:27:13,010 - INFO - [Algorithm] 初始化后修正client_id: 63
2025-07-31 15:27:13,011 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:13,012 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:13,013 - INFO - [Client 63] 创建新算法
2025-07-31 15:27:13,013 - INFO - [Algorithm] 设置客户端ID: 63
2025-07-31 15:27:13,014 - INFO - [Algorithm] 同步更新trainer的client_id: 63
2025-07-31 15:27:13,014 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:13,100 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:13,101 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:13,103 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:13,150 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:13,151 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:13,151 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:13,155 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:13,157 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:13,158 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:13,159 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:13,159 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:13,160 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:13,160 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:13,161 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:13,161 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:13,162 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:13,163 - INFO - 顺序分配客户端ID: 63
2025-07-31 15:27:13,164 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 63
2025-07-31 15:27:13,165 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:13,169 - WARNING - [Client 63] 数据源为None，已创建新数据源
2025-07-31 15:27:13,170 - WARNING - [Client 63] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:15,730 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:15,731 - INFO - [Client 63] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:15,731 - INFO - [Client 63] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:15,750 - INFO - [Client 63] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:15,757 - INFO - [Client 63] 初始化时成功加载数据
2025-07-31 15:27:15,758 - INFO - [客户端 63] 初始化验证通过
2025-07-31 15:27:15,761 - INFO - 客户端 63 实例创建成功
2025-07-31 15:27:15,763 - INFO - 客户端63已设置服务器引用
2025-07-31 15:27:15,764 - INFO - 客户端 63 已设置服务器引用
2025-07-31 15:27:15,765 - INFO - 客户端63已注册
2025-07-31 15:27:15,766 - INFO - 客户端 63 已成功注册到服务器
2025-07-31 15:27:15,767 - INFO - 开始创建客户端 64...
2025-07-31 15:27:15,768 - INFO - 初始化客户端, ID: 64
2025-07-31 15:27:15,875 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:15,875 - INFO - [Client 64] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:15,876 - INFO - [Trainer] 初始化训练器, client_id: 64
2025-07-31 15:27:15,913 - INFO - [Trainer 64] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:15,915 - INFO - [Trainer 64] 模型的输入通道数: 3
2025-07-31 15:27:15,915 - INFO - [Trainer 64] 强制使用CPU
2025-07-31 15:27:15,918 - INFO - [Trainer 64] 模型已移至设备: cpu
2025-07-31 15:27:15,919 - INFO - [Trainer 64] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:15,920 - INFO - [Trainer 64] 初始化完成
2025-07-31 15:27:15,920 - INFO - [Client 64] 创建新训练器
2025-07-31 15:27:15,921 - INFO - [Algorithm] 从训练器获取客户端ID: 64
2025-07-31 15:27:15,922 - INFO - [Algorithm] 初始化后修正client_id: 64
2025-07-31 15:27:15,925 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:15,927 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:15,930 - INFO - [Client 64] 创建新算法
2025-07-31 15:27:15,931 - INFO - [Algorithm] 设置客户端ID: 64
2025-07-31 15:27:15,932 - INFO - [Algorithm] 同步更新trainer的client_id: 64
2025-07-31 15:27:15,934 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:16,031 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:16,031 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:16,032 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:16,064 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:16,065 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:16,065 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:16,068 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:16,069 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:16,070 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:16,071 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:16,072 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:16,072 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:16,073 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:16,073 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:16,074 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:16,074 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:16,074 - INFO - 顺序分配客户端ID: 64
2025-07-31 15:27:16,075 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 64
2025-07-31 15:27:16,076 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:16,078 - WARNING - [Client 64] 数据源为None，已创建新数据源
2025-07-31 15:27:16,078 - WARNING - [Client 64] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:18,709 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:18,710 - INFO - [Client 64] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:18,710 - INFO - [Client 64] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:18,726 - INFO - [Client 64] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:18,726 - INFO - [Client 64] 初始化时成功加载数据
2025-07-31 15:27:18,727 - INFO - [客户端 64] 初始化验证通过
2025-07-31 15:27:18,727 - INFO - 客户端 64 实例创建成功
2025-07-31 15:27:18,731 - INFO - 客户端64已设置服务器引用
2025-07-31 15:27:18,732 - INFO - 客户端 64 已设置服务器引用
2025-07-31 15:27:18,733 - INFO - 客户端64已注册
2025-07-31 15:27:18,734 - INFO - 客户端 64 已成功注册到服务器
2025-07-31 15:27:18,735 - INFO - 开始创建客户端 65...
2025-07-31 15:27:18,737 - INFO - 初始化客户端, ID: 65
2025-07-31 15:27:18,851 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:18,851 - INFO - [Client 65] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:18,852 - INFO - [Trainer] 初始化训练器, client_id: 65
2025-07-31 15:27:18,887 - INFO - [Trainer 65] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:18,890 - INFO - [Trainer 65] 模型的输入通道数: 3
2025-07-31 15:27:18,891 - INFO - [Trainer 65] 强制使用CPU
2025-07-31 15:27:18,894 - INFO - [Trainer 65] 模型已移至设备: cpu
2025-07-31 15:27:18,895 - INFO - [Trainer 65] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:18,897 - INFO - [Trainer 65] 初始化完成
2025-07-31 15:27:18,898 - INFO - [Client 65] 创建新训练器
2025-07-31 15:27:18,899 - INFO - [Algorithm] 从训练器获取客户端ID: 65
2025-07-31 15:27:18,899 - INFO - [Algorithm] 初始化后修正client_id: 65
2025-07-31 15:27:18,900 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:18,901 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:18,902 - INFO - [Client 65] 创建新算法
2025-07-31 15:27:18,903 - INFO - [Algorithm] 设置客户端ID: 65
2025-07-31 15:27:18,903 - INFO - [Algorithm] 同步更新trainer的client_id: 65
2025-07-31 15:27:18,904 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:19,019 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:19,019 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:19,020 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:19,056 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:19,057 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:19,057 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:19,060 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:19,061 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:19,062 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:19,063 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:19,063 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:19,064 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:19,064 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:19,064 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:19,065 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:19,065 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:19,065 - INFO - 顺序分配客户端ID: 65
2025-07-31 15:27:19,065 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 65
2025-07-31 15:27:19,068 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:19,068 - WARNING - [Client 65] 数据源为None，已创建新数据源
2025-07-31 15:27:19,069 - WARNING - [Client 65] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:21,825 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:21,827 - INFO - [Client 65] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:21,827 - INFO - [Client 65] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:21,840 - INFO - [Client 65] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:21,841 - INFO - [Client 65] 初始化时成功加载数据
2025-07-31 15:27:21,841 - INFO - [客户端 65] 初始化验证通过
2025-07-31 15:27:21,842 - INFO - 客户端 65 实例创建成功
2025-07-31 15:27:21,843 - INFO - 客户端65已设置服务器引用
2025-07-31 15:27:21,844 - INFO - 客户端 65 已设置服务器引用
2025-07-31 15:27:21,844 - INFO - 客户端65已注册
2025-07-31 15:27:21,845 - INFO - 客户端 65 已成功注册到服务器
2025-07-31 15:27:21,845 - INFO - 开始创建客户端 66...
2025-07-31 15:27:21,846 - INFO - 初始化客户端, ID: 66
2025-07-31 15:27:21,933 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:21,933 - INFO - [Client 66] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:21,934 - INFO - [Trainer] 初始化训练器, client_id: 66
2025-07-31 15:27:21,969 - INFO - [Trainer 66] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:21,970 - INFO - [Trainer 66] 模型的输入通道数: 3
2025-07-31 15:27:21,971 - INFO - [Trainer 66] 强制使用CPU
2025-07-31 15:27:21,973 - INFO - [Trainer 66] 模型已移至设备: cpu
2025-07-31 15:27:21,974 - INFO - [Trainer 66] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:21,975 - INFO - [Trainer 66] 初始化完成
2025-07-31 15:27:21,975 - INFO - [Client 66] 创建新训练器
2025-07-31 15:27:21,975 - INFO - [Algorithm] 从训练器获取客户端ID: 66
2025-07-31 15:27:21,977 - INFO - [Algorithm] 初始化后修正client_id: 66
2025-07-31 15:27:21,977 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:21,978 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:21,978 - INFO - [Client 66] 创建新算法
2025-07-31 15:27:21,979 - INFO - [Algorithm] 设置客户端ID: 66
2025-07-31 15:27:21,979 - INFO - [Algorithm] 同步更新trainer的client_id: 66
2025-07-31 15:27:21,979 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:22,078 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:22,079 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:22,079 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:22,109 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:22,109 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:22,110 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:22,111 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:22,112 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:22,113 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:22,114 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:22,115 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:22,116 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:22,116 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:22,117 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:22,117 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:22,119 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:22,119 - INFO - 顺序分配客户端ID: 66
2025-07-31 15:27:22,120 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 66
2025-07-31 15:27:22,121 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:22,122 - WARNING - [Client 66] 数据源为None，已创建新数据源
2025-07-31 15:27:22,122 - WARNING - [Client 66] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:24,961 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:24,962 - INFO - [Client 66] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:24,965 - INFO - [Client 66] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:24,988 - INFO - [Client 66] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:24,989 - INFO - [Client 66] 初始化时成功加载数据
2025-07-31 15:27:24,990 - INFO - [客户端 66] 初始化验证通过
2025-07-31 15:27:24,990 - INFO - 客户端 66 实例创建成功
2025-07-31 15:27:24,991 - INFO - 客户端66已设置服务器引用
2025-07-31 15:27:24,992 - INFO - 客户端 66 已设置服务器引用
2025-07-31 15:27:24,993 - INFO - 客户端66已注册
2025-07-31 15:27:24,994 - INFO - 客户端 66 已成功注册到服务器
2025-07-31 15:27:24,996 - INFO - 开始创建客户端 67...
2025-07-31 15:27:24,997 - INFO - 初始化客户端, ID: 67
2025-07-31 15:27:25,107 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:25,107 - INFO - [Client 67] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:25,108 - INFO - [Trainer] 初始化训练器, client_id: 67
2025-07-31 15:27:25,161 - INFO - [Trainer 67] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:25,162 - INFO - [Trainer 67] 模型的输入通道数: 3
2025-07-31 15:27:25,162 - INFO - [Trainer 67] 强制使用CPU
2025-07-31 15:27:25,164 - INFO - [Trainer 67] 模型已移至设备: cpu
2025-07-31 15:27:25,165 - INFO - [Trainer 67] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:25,166 - INFO - [Trainer 67] 初始化完成
2025-07-31 15:27:25,166 - INFO - [Client 67] 创建新训练器
2025-07-31 15:27:25,166 - INFO - [Algorithm] 从训练器获取客户端ID: 67
2025-07-31 15:27:25,167 - INFO - [Algorithm] 初始化后修正client_id: 67
2025-07-31 15:27:25,167 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:25,167 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:25,167 - INFO - [Client 67] 创建新算法
2025-07-31 15:27:25,168 - INFO - [Algorithm] 设置客户端ID: 67
2025-07-31 15:27:25,168 - INFO - [Algorithm] 同步更新trainer的client_id: 67
2025-07-31 15:27:25,169 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:25,275 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:25,275 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:25,276 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:25,321 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:25,322 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:25,322 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:25,324 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:25,325 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:25,325 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:25,325 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:25,325 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:25,326 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:25,326 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:25,328 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:25,328 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:25,328 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:25,329 - INFO - 顺序分配客户端ID: 67
2025-07-31 15:27:25,329 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 67
2025-07-31 15:27:25,330 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:25,331 - WARNING - [Client 67] 数据源为None，已创建新数据源
2025-07-31 15:27:25,331 - WARNING - [Client 67] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:28,334 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:28,334 - INFO - [Client 67] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:28,334 - INFO - [Client 67] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:28,349 - INFO - [Client 67] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:28,350 - INFO - [Client 67] 初始化时成功加载数据
2025-07-31 15:27:28,350 - INFO - [客户端 67] 初始化验证通过
2025-07-31 15:27:28,351 - INFO - 客户端 67 实例创建成功
2025-07-31 15:27:28,351 - INFO - 客户端67已设置服务器引用
2025-07-31 15:27:28,352 - INFO - 客户端 67 已设置服务器引用
2025-07-31 15:27:28,352 - INFO - 客户端67已注册
2025-07-31 15:27:28,352 - INFO - 客户端 67 已成功注册到服务器
2025-07-31 15:27:28,352 - INFO - 开始创建客户端 68...
2025-07-31 15:27:28,353 - INFO - 初始化客户端, ID: 68
2025-07-31 15:27:28,425 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:28,425 - INFO - [Client 68] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:28,426 - INFO - [Trainer] 初始化训练器, client_id: 68
2025-07-31 15:27:28,451 - INFO - [Trainer 68] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:28,451 - INFO - [Trainer 68] 模型的输入通道数: 3
2025-07-31 15:27:28,451 - INFO - [Trainer 68] 强制使用CPU
2025-07-31 15:27:28,453 - INFO - [Trainer 68] 模型已移至设备: cpu
2025-07-31 15:27:28,454 - INFO - [Trainer 68] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:28,454 - INFO - [Trainer 68] 初始化完成
2025-07-31 15:27:28,455 - INFO - [Client 68] 创建新训练器
2025-07-31 15:27:28,455 - INFO - [Algorithm] 从训练器获取客户端ID: 68
2025-07-31 15:27:28,455 - INFO - [Algorithm] 初始化后修正client_id: 68
2025-07-31 15:27:28,455 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:28,457 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:28,457 - INFO - [Client 68] 创建新算法
2025-07-31 15:27:28,458 - INFO - [Algorithm] 设置客户端ID: 68
2025-07-31 15:27:28,458 - INFO - [Algorithm] 同步更新trainer的client_id: 68
2025-07-31 15:27:28,459 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:28,529 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:28,529 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:28,530 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:28,582 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:28,583 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:28,583 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:28,584 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:28,585 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:28,585 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:28,585 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:28,586 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:28,586 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:28,587 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:28,587 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:28,587 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:28,587 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:28,588 - INFO - 顺序分配客户端ID: 68
2025-07-31 15:27:28,588 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 68
2025-07-31 15:27:28,589 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:28,590 - WARNING - [Client 68] 数据源为None，已创建新数据源
2025-07-31 15:27:28,590 - WARNING - [Client 68] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:30,736 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:30,737 - INFO - [Client 68] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:30,737 - INFO - [Client 68] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:30,750 - INFO - [Client 68] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:30,750 - INFO - [Client 68] 初始化时成功加载数据
2025-07-31 15:27:30,751 - INFO - [客户端 68] 初始化验证通过
2025-07-31 15:27:30,751 - INFO - 客户端 68 实例创建成功
2025-07-31 15:27:30,751 - INFO - 客户端68已设置服务器引用
2025-07-31 15:27:30,751 - INFO - 客户端 68 已设置服务器引用
2025-07-31 15:27:30,752 - INFO - 客户端68已注册
2025-07-31 15:27:30,752 - INFO - 客户端 68 已成功注册到服务器
2025-07-31 15:27:30,752 - INFO - 开始创建客户端 69...
2025-07-31 15:27:30,752 - INFO - 初始化客户端, ID: 69
2025-07-31 15:27:30,813 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:30,813 - INFO - [Client 69] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:30,814 - INFO - [Trainer] 初始化训练器, client_id: 69
2025-07-31 15:27:30,834 - INFO - [Trainer 69] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:30,835 - INFO - [Trainer 69] 模型的输入通道数: 3
2025-07-31 15:27:30,835 - INFO - [Trainer 69] 强制使用CPU
2025-07-31 15:27:30,837 - INFO - [Trainer 69] 模型已移至设备: cpu
2025-07-31 15:27:30,838 - INFO - [Trainer 69] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:30,838 - INFO - [Trainer 69] 初始化完成
2025-07-31 15:27:30,839 - INFO - [Client 69] 创建新训练器
2025-07-31 15:27:30,839 - INFO - [Algorithm] 从训练器获取客户端ID: 69
2025-07-31 15:27:30,839 - INFO - [Algorithm] 初始化后修正client_id: 69
2025-07-31 15:27:30,840 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:30,840 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:30,840 - INFO - [Client 69] 创建新算法
2025-07-31 15:27:30,842 - INFO - [Algorithm] 设置客户端ID: 69
2025-07-31 15:27:30,842 - INFO - [Algorithm] 同步更新trainer的client_id: 69
2025-07-31 15:27:30,842 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:30,919 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:30,920 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:30,920 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:30,949 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:30,951 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:30,952 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:30,953 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:30,955 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:30,955 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:30,955 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:30,957 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:30,957 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:30,958 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:30,958 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:30,958 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:30,959 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:30,959 - INFO - 顺序分配客户端ID: 69
2025-07-31 15:27:30,959 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 69
2025-07-31 15:27:30,960 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:30,963 - WARNING - [Client 69] 数据源为None，已创建新数据源
2025-07-31 15:27:30,963 - WARNING - [Client 69] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:33,218 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:33,219 - INFO - [Client 69] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:33,219 - INFO - [Client 69] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:33,232 - INFO - [Client 69] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:33,232 - INFO - [Client 69] 初始化时成功加载数据
2025-07-31 15:27:33,233 - INFO - [客户端 69] 初始化验证通过
2025-07-31 15:27:33,233 - INFO - 客户端 69 实例创建成功
2025-07-31 15:27:33,233 - INFO - 客户端69已设置服务器引用
2025-07-31 15:27:33,233 - INFO - 客户端 69 已设置服务器引用
2025-07-31 15:27:33,235 - INFO - 客户端69已注册
2025-07-31 15:27:33,235 - INFO - 客户端 69 已成功注册到服务器
2025-07-31 15:27:33,235 - INFO - 开始创建客户端 70...
2025-07-31 15:27:33,236 - INFO - 初始化客户端, ID: 70
2025-07-31 15:27:33,293 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:33,293 - INFO - [Client 70] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:33,294 - INFO - [Trainer] 初始化训练器, client_id: 70
2025-07-31 15:27:33,317 - INFO - [Trainer 70] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:33,318 - INFO - [Trainer 70] 模型的输入通道数: 3
2025-07-31 15:27:33,319 - INFO - [Trainer 70] 强制使用CPU
2025-07-31 15:27:33,320 - INFO - [Trainer 70] 模型已移至设备: cpu
2025-07-31 15:27:33,321 - INFO - [Trainer 70] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:33,322 - INFO - [Trainer 70] 初始化完成
2025-07-31 15:27:33,322 - INFO - [Client 70] 创建新训练器
2025-07-31 15:27:33,322 - INFO - [Algorithm] 从训练器获取客户端ID: 70
2025-07-31 15:27:33,323 - INFO - [Algorithm] 初始化后修正client_id: 70
2025-07-31 15:27:33,323 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:33,323 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:33,324 - INFO - [Client 70] 创建新算法
2025-07-31 15:27:33,324 - INFO - [Algorithm] 设置客户端ID: 70
2025-07-31 15:27:33,324 - INFO - [Algorithm] 同步更新trainer的client_id: 70
2025-07-31 15:27:33,325 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:33,385 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:33,386 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:33,386 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:33,413 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:33,414 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:33,414 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:33,416 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:33,418 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:33,418 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:33,419 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:33,419 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:33,420 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:33,420 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:33,420 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:33,420 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:33,421 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:33,421 - INFO - 顺序分配客户端ID: 70
2025-07-31 15:27:33,421 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 70
2025-07-31 15:27:33,422 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:33,424 - WARNING - [Client 70] 数据源为None，已创建新数据源
2025-07-31 15:27:33,424 - WARNING - [Client 70] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:35,458 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:35,458 - INFO - [Client 70] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:35,459 - INFO - [Client 70] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:35,470 - INFO - [Client 70] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:35,470 - INFO - [Client 70] 初始化时成功加载数据
2025-07-31 15:27:35,471 - INFO - [客户端 70] 初始化验证通过
2025-07-31 15:27:35,471 - INFO - 客户端 70 实例创建成功
2025-07-31 15:27:35,471 - INFO - 客户端70已设置服务器引用
2025-07-31 15:27:35,473 - INFO - 客户端 70 已设置服务器引用
2025-07-31 15:27:35,473 - INFO - 客户端70已注册
2025-07-31 15:27:35,473 - INFO - 客户端 70 已成功注册到服务器
2025-07-31 15:27:35,473 - INFO - 开始创建客户端 71...
2025-07-31 15:27:35,473 - INFO - 初始化客户端, ID: 71
2025-07-31 15:27:35,541 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:35,541 - INFO - [Client 71] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:35,542 - INFO - [Trainer] 初始化训练器, client_id: 71
2025-07-31 15:27:35,568 - INFO - [Trainer 71] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:35,568 - INFO - [Trainer 71] 模型的输入通道数: 3
2025-07-31 15:27:35,569 - INFO - [Trainer 71] 强制使用CPU
2025-07-31 15:27:35,572 - INFO - [Trainer 71] 模型已移至设备: cpu
2025-07-31 15:27:35,574 - INFO - [Trainer 71] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:35,574 - INFO - [Trainer 71] 初始化完成
2025-07-31 15:27:35,575 - INFO - [Client 71] 创建新训练器
2025-07-31 15:27:35,575 - INFO - [Algorithm] 从训练器获取客户端ID: 71
2025-07-31 15:27:35,576 - INFO - [Algorithm] 初始化后修正client_id: 71
2025-07-31 15:27:35,576 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:35,576 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:35,577 - INFO - [Client 71] 创建新算法
2025-07-31 15:27:35,577 - INFO - [Algorithm] 设置客户端ID: 71
2025-07-31 15:27:35,579 - INFO - [Algorithm] 同步更新trainer的client_id: 71
2025-07-31 15:27:35,579 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:35,625 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:35,625 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:35,625 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:35,644 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:35,644 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:35,645 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:35,647 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:35,648 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:35,648 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:35,649 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:35,649 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:35,650 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:35,650 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:35,651 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:35,651 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:35,652 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:35,653 - INFO - 顺序分配客户端ID: 71
2025-07-31 15:27:35,653 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 71
2025-07-31 15:27:35,655 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:35,657 - WARNING - [Client 71] 数据源为None，已创建新数据源
2025-07-31 15:27:35,658 - WARNING - [Client 71] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:38,033 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:38,034 - INFO - [Client 71] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:38,034 - INFO - [Client 71] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:38,049 - INFO - [Client 71] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:38,050 - INFO - [Client 71] 初始化时成功加载数据
2025-07-31 15:27:38,050 - INFO - [客户端 71] 初始化验证通过
2025-07-31 15:27:38,051 - INFO - 客户端 71 实例创建成功
2025-07-31 15:27:38,052 - INFO - 客户端71已设置服务器引用
2025-07-31 15:27:38,052 - INFO - 客户端 71 已设置服务器引用
2025-07-31 15:27:38,052 - INFO - 客户端71已注册
2025-07-31 15:27:38,053 - INFO - 客户端 71 已成功注册到服务器
2025-07-31 15:27:38,053 - INFO - 开始创建客户端 72...
2025-07-31 15:27:38,053 - INFO - 初始化客户端, ID: 72
2025-07-31 15:27:38,155 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:38,157 - INFO - [Client 72] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:38,157 - INFO - [Trainer] 初始化训练器, client_id: 72
2025-07-31 15:27:38,184 - INFO - [Trainer 72] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:38,185 - INFO - [Trainer 72] 模型的输入通道数: 3
2025-07-31 15:27:38,185 - INFO - [Trainer 72] 强制使用CPU
2025-07-31 15:27:38,187 - INFO - [Trainer 72] 模型已移至设备: cpu
2025-07-31 15:27:38,188 - INFO - [Trainer 72] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:38,188 - INFO - [Trainer 72] 初始化完成
2025-07-31 15:27:38,189 - INFO - [Client 72] 创建新训练器
2025-07-31 15:27:38,189 - INFO - [Algorithm] 从训练器获取客户端ID: 72
2025-07-31 15:27:38,190 - INFO - [Algorithm] 初始化后修正client_id: 72
2025-07-31 15:27:38,190 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:38,191 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:38,191 - INFO - [Client 72] 创建新算法
2025-07-31 15:27:38,191 - INFO - [Algorithm] 设置客户端ID: 72
2025-07-31 15:27:38,192 - INFO - [Algorithm] 同步更新trainer的client_id: 72
2025-07-31 15:27:38,192 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:38,270 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:38,271 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:38,271 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:38,299 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:38,300 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:38,301 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:38,304 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:38,305 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:38,305 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:38,307 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:38,308 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:38,308 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:38,309 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:38,310 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:38,310 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:38,311 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:38,311 - INFO - 顺序分配客户端ID: 72
2025-07-31 15:27:38,312 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 72
2025-07-31 15:27:38,313 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:38,314 - WARNING - [Client 72] 数据源为None，已创建新数据源
2025-07-31 15:27:38,314 - WARNING - [Client 72] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:40,822 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:40,823 - INFO - [Client 72] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:40,824 - INFO - [Client 72] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:40,840 - INFO - [Client 72] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:40,841 - INFO - [Client 72] 初始化时成功加载数据
2025-07-31 15:27:40,841 - INFO - [客户端 72] 初始化验证通过
2025-07-31 15:27:40,841 - INFO - 客户端 72 实例创建成功
2025-07-31 15:27:40,842 - INFO - 客户端72已设置服务器引用
2025-07-31 15:27:40,842 - INFO - 客户端 72 已设置服务器引用
2025-07-31 15:27:40,842 - INFO - 客户端72已注册
2025-07-31 15:27:40,843 - INFO - 客户端 72 已成功注册到服务器
2025-07-31 15:27:40,843 - INFO - 开始创建客户端 73...
2025-07-31 15:27:40,843 - INFO - 初始化客户端, ID: 73
2025-07-31 15:27:40,920 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:40,921 - INFO - [Client 73] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:40,921 - INFO - [Trainer] 初始化训练器, client_id: 73
2025-07-31 15:27:40,955 - INFO - [Trainer 73] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:40,956 - INFO - [Trainer 73] 模型的输入通道数: 3
2025-07-31 15:27:40,956 - INFO - [Trainer 73] 强制使用CPU
2025-07-31 15:27:40,958 - INFO - [Trainer 73] 模型已移至设备: cpu
2025-07-31 15:27:40,959 - INFO - [Trainer 73] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:40,960 - INFO - [Trainer 73] 初始化完成
2025-07-31 15:27:40,960 - INFO - [Client 73] 创建新训练器
2025-07-31 15:27:40,960 - INFO - [Algorithm] 从训练器获取客户端ID: 73
2025-07-31 15:27:40,962 - INFO - [Algorithm] 初始化后修正client_id: 73
2025-07-31 15:27:40,962 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:40,963 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:40,963 - INFO - [Client 73] 创建新算法
2025-07-31 15:27:40,964 - INFO - [Algorithm] 设置客户端ID: 73
2025-07-31 15:27:40,965 - INFO - [Algorithm] 同步更新trainer的client_id: 73
2025-07-31 15:27:40,967 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:41,049 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:41,049 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:41,050 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:41,085 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:41,087 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:41,088 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:41,089 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:41,090 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:41,091 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:41,091 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:41,091 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:41,092 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:41,092 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:41,092 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:41,093 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:41,093 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:41,093 - INFO - 顺序分配客户端ID: 73
2025-07-31 15:27:41,094 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 73
2025-07-31 15:27:41,095 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:41,095 - WARNING - [Client 73] 数据源为None，已创建新数据源
2025-07-31 15:27:41,096 - WARNING - [Client 73] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:43,462 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:43,463 - INFO - [Client 73] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:43,464 - INFO - [Client 73] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:43,476 - INFO - [Client 73] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:43,479 - INFO - [Client 73] 初始化时成功加载数据
2025-07-31 15:27:43,480 - INFO - [客户端 73] 初始化验证通过
2025-07-31 15:27:43,481 - INFO - 客户端 73 实例创建成功
2025-07-31 15:27:43,482 - INFO - 客户端73已设置服务器引用
2025-07-31 15:27:43,482 - INFO - 客户端 73 已设置服务器引用
2025-07-31 15:27:43,483 - INFO - 客户端73已注册
2025-07-31 15:27:43,484 - INFO - 客户端 73 已成功注册到服务器
2025-07-31 15:27:43,484 - INFO - 开始创建客户端 74...
2025-07-31 15:27:43,485 - INFO - 初始化客户端, ID: 74
2025-07-31 15:27:43,587 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:43,587 - INFO - [Client 74] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:43,588 - INFO - [Trainer] 初始化训练器, client_id: 74
2025-07-31 15:27:43,615 - INFO - [Trainer 74] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:43,616 - INFO - [Trainer 74] 模型的输入通道数: 3
2025-07-31 15:27:43,617 - INFO - [Trainer 74] 强制使用CPU
2025-07-31 15:27:43,619 - INFO - [Trainer 74] 模型已移至设备: cpu
2025-07-31 15:27:43,620 - INFO - [Trainer 74] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:43,620 - INFO - [Trainer 74] 初始化完成
2025-07-31 15:27:43,620 - INFO - [Client 74] 创建新训练器
2025-07-31 15:27:43,621 - INFO - [Algorithm] 从训练器获取客户端ID: 74
2025-07-31 15:27:43,621 - INFO - [Algorithm] 初始化后修正client_id: 74
2025-07-31 15:27:43,622 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:43,622 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:43,623 - INFO - [Client 74] 创建新算法
2025-07-31 15:27:43,623 - INFO - [Algorithm] 设置客户端ID: 74
2025-07-31 15:27:43,624 - INFO - [Algorithm] 同步更新trainer的client_id: 74
2025-07-31 15:27:43,624 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:43,702 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:43,702 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:43,703 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:43,734 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:43,735 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:43,735 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:43,737 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:43,738 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:43,738 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:43,738 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:43,739 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:43,739 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:43,740 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:43,740 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:43,741 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:43,741 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:43,742 - INFO - 顺序分配客户端ID: 74
2025-07-31 15:27:43,743 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 74
2025-07-31 15:27:43,744 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:43,745 - WARNING - [Client 74] 数据源为None，已创建新数据源
2025-07-31 15:27:43,745 - WARNING - [Client 74] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:45,974 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:45,975 - INFO - [Client 74] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:45,975 - INFO - [Client 74] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:45,993 - INFO - [Client 74] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:45,994 - INFO - [Client 74] 初始化时成功加载数据
2025-07-31 15:27:45,994 - INFO - [客户端 74] 初始化验证通过
2025-07-31 15:27:45,995 - INFO - 客户端 74 实例创建成功
2025-07-31 15:27:45,995 - INFO - 客户端74已设置服务器引用
2025-07-31 15:27:45,995 - INFO - 客户端 74 已设置服务器引用
2025-07-31 15:27:45,995 - INFO - 客户端74已注册
2025-07-31 15:27:45,997 - INFO - 客户端 74 已成功注册到服务器
2025-07-31 15:27:45,997 - INFO - 开始创建客户端 75...
2025-07-31 15:27:45,998 - INFO - 初始化客户端, ID: 75
2025-07-31 15:27:46,068 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:46,068 - INFO - [Client 75] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:46,069 - INFO - [Trainer] 初始化训练器, client_id: 75
2025-07-31 15:27:46,092 - INFO - [Trainer 75] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:46,093 - INFO - [Trainer 75] 模型的输入通道数: 3
2025-07-31 15:27:46,094 - INFO - [Trainer 75] 强制使用CPU
2025-07-31 15:27:46,095 - INFO - [Trainer 75] 模型已移至设备: cpu
2025-07-31 15:27:46,097 - INFO - [Trainer 75] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:46,097 - INFO - [Trainer 75] 初始化完成
2025-07-31 15:27:46,098 - INFO - [Client 75] 创建新训练器
2025-07-31 15:27:46,098 - INFO - [Algorithm] 从训练器获取客户端ID: 75
2025-07-31 15:27:46,099 - INFO - [Algorithm] 初始化后修正client_id: 75
2025-07-31 15:27:46,099 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:46,100 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:46,100 - INFO - [Client 75] 创建新算法
2025-07-31 15:27:46,101 - INFO - [Algorithm] 设置客户端ID: 75
2025-07-31 15:27:46,102 - INFO - [Algorithm] 同步更新trainer的client_id: 75
2025-07-31 15:27:46,103 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:46,161 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:46,162 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:46,162 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:46,185 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:46,185 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:46,185 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:46,187 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:46,188 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:46,188 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:46,189 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:46,189 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:46,189 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:46,189 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:46,191 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:46,191 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:46,191 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:46,191 - INFO - 顺序分配客户端ID: 75
2025-07-31 15:27:46,192 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 75
2025-07-31 15:27:46,193 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:46,194 - WARNING - [Client 75] 数据源为None，已创建新数据源
2025-07-31 15:27:46,194 - WARNING - [Client 75] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:48,403 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:48,404 - INFO - [Client 75] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:48,405 - INFO - [Client 75] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:48,422 - INFO - [Client 75] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:48,423 - INFO - [Client 75] 初始化时成功加载数据
2025-07-31 15:27:48,425 - INFO - [客户端 75] 初始化验证通过
2025-07-31 15:27:48,426 - INFO - 客户端 75 实例创建成功
2025-07-31 15:27:48,428 - INFO - 客户端75已设置服务器引用
2025-07-31 15:27:48,428 - INFO - 客户端 75 已设置服务器引用
2025-07-31 15:27:48,429 - INFO - 客户端75已注册
2025-07-31 15:27:48,429 - INFO - 客户端 75 已成功注册到服务器
2025-07-31 15:27:48,430 - INFO - 开始创建客户端 76...
2025-07-31 15:27:48,432 - INFO - 初始化客户端, ID: 76
2025-07-31 15:27:48,530 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:48,531 - INFO - [Client 76] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:48,532 - INFO - [Trainer] 初始化训练器, client_id: 76
2025-07-31 15:27:48,571 - INFO - [Trainer 76] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:48,572 - INFO - [Trainer 76] 模型的输入通道数: 3
2025-07-31 15:27:48,573 - INFO - [Trainer 76] 强制使用CPU
2025-07-31 15:27:48,575 - INFO - [Trainer 76] 模型已移至设备: cpu
2025-07-31 15:27:48,577 - INFO - [Trainer 76] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:48,578 - INFO - [Trainer 76] 初始化完成
2025-07-31 15:27:48,579 - INFO - [Client 76] 创建新训练器
2025-07-31 15:27:48,579 - INFO - [Algorithm] 从训练器获取客户端ID: 76
2025-07-31 15:27:48,580 - INFO - [Algorithm] 初始化后修正client_id: 76
2025-07-31 15:27:48,581 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:48,582 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:48,583 - INFO - [Client 76] 创建新算法
2025-07-31 15:27:48,583 - INFO - [Algorithm] 设置客户端ID: 76
2025-07-31 15:27:48,584 - INFO - [Algorithm] 同步更新trainer的client_id: 76
2025-07-31 15:27:48,585 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:48,683 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:48,684 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:48,684 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:48,715 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:48,715 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:48,717 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:48,719 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:48,721 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:48,721 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:48,723 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:48,725 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:48,725 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:48,727 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:48,728 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:48,729 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:48,729 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:48,730 - INFO - 顺序分配客户端ID: 76
2025-07-31 15:27:48,734 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 76
2025-07-31 15:27:48,735 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:48,737 - WARNING - [Client 76] 数据源为None，已创建新数据源
2025-07-31 15:27:48,737 - WARNING - [Client 76] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:51,385 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:51,385 - INFO - [Client 76] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:51,387 - INFO - [Client 76] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:51,399 - INFO - [Client 76] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:51,401 - INFO - [Client 76] 初始化时成功加载数据
2025-07-31 15:27:51,402 - INFO - [客户端 76] 初始化验证通过
2025-07-31 15:27:51,402 - INFO - 客户端 76 实例创建成功
2025-07-31 15:27:51,403 - INFO - 客户端76已设置服务器引用
2025-07-31 15:27:51,403 - INFO - 客户端 76 已设置服务器引用
2025-07-31 15:27:51,404 - INFO - 客户端76已注册
2025-07-31 15:27:51,404 - INFO - 客户端 76 已成功注册到服务器
2025-07-31 15:27:51,404 - INFO - 开始创建客户端 77...
2025-07-31 15:27:51,405 - INFO - 初始化客户端, ID: 77
2025-07-31 15:27:51,479 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:51,479 - INFO - [Client 77] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:51,480 - INFO - [Trainer] 初始化训练器, client_id: 77
2025-07-31 15:27:51,504 - INFO - [Trainer 77] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:51,505 - INFO - [Trainer 77] 模型的输入通道数: 3
2025-07-31 15:27:51,505 - INFO - [Trainer 77] 强制使用CPU
2025-07-31 15:27:51,507 - INFO - [Trainer 77] 模型已移至设备: cpu
2025-07-31 15:27:51,508 - INFO - [Trainer 77] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:51,508 - INFO - [Trainer 77] 初始化完成
2025-07-31 15:27:51,509 - INFO - [Client 77] 创建新训练器
2025-07-31 15:27:51,509 - INFO - [Algorithm] 从训练器获取客户端ID: 77
2025-07-31 15:27:51,509 - INFO - [Algorithm] 初始化后修正client_id: 77
2025-07-31 15:27:51,510 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:51,510 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:51,511 - INFO - [Client 77] 创建新算法
2025-07-31 15:27:51,511 - INFO - [Algorithm] 设置客户端ID: 77
2025-07-31 15:27:51,513 - INFO - [Algorithm] 同步更新trainer的client_id: 77
2025-07-31 15:27:51,513 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:51,595 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:51,597 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:51,598 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:51,631 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:51,631 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:51,632 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:51,634 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:51,635 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:51,637 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:51,637 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:51,638 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:51,638 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:51,639 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:51,639 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:51,639 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:51,640 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:51,640 - INFO - 顺序分配客户端ID: 77
2025-07-31 15:27:51,640 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 77
2025-07-31 15:27:51,642 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:51,645 - WARNING - [Client 77] 数据源为None，已创建新数据源
2025-07-31 15:27:51,646 - WARNING - [Client 77] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:54,137 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:54,138 - INFO - [Client 77] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:54,138 - INFO - [Client 77] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:54,151 - INFO - [Client 77] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:54,152 - INFO - [Client 77] 初始化时成功加载数据
2025-07-31 15:27:54,152 - INFO - [客户端 77] 初始化验证通过
2025-07-31 15:27:54,153 - INFO - 客户端 77 实例创建成功
2025-07-31 15:27:54,153 - INFO - 客户端77已设置服务器引用
2025-07-31 15:27:54,154 - INFO - 客户端 77 已设置服务器引用
2025-07-31 15:27:54,154 - INFO - 客户端77已注册
2025-07-31 15:27:54,155 - INFO - 客户端 77 已成功注册到服务器
2025-07-31 15:27:54,155 - INFO - 开始创建客户端 78...
2025-07-31 15:27:54,157 - INFO - 初始化客户端, ID: 78
2025-07-31 15:27:54,245 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:54,245 - INFO - [Client 78] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:54,247 - INFO - [Trainer] 初始化训练器, client_id: 78
2025-07-31 15:27:54,280 - INFO - [Trainer 78] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:54,283 - INFO - [Trainer 78] 模型的输入通道数: 3
2025-07-31 15:27:54,283 - INFO - [Trainer 78] 强制使用CPU
2025-07-31 15:27:54,286 - INFO - [Trainer 78] 模型已移至设备: cpu
2025-07-31 15:27:54,288 - INFO - [Trainer 78] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:54,288 - INFO - [Trainer 78] 初始化完成
2025-07-31 15:27:54,289 - INFO - [Client 78] 创建新训练器
2025-07-31 15:27:54,290 - INFO - [Algorithm] 从训练器获取客户端ID: 78
2025-07-31 15:27:54,291 - INFO - [Algorithm] 初始化后修正client_id: 78
2025-07-31 15:27:54,291 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:54,292 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:54,293 - INFO - [Client 78] 创建新算法
2025-07-31 15:27:54,294 - INFO - [Algorithm] 设置客户端ID: 78
2025-07-31 15:27:54,295 - INFO - [Algorithm] 同步更新trainer的client_id: 78
2025-07-31 15:27:54,295 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:54,403 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:54,404 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:54,405 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:54,439 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:54,439 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:54,440 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:54,442 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:54,442 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:54,443 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:54,444 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:54,444 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:54,445 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:54,445 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:54,447 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:54,447 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:54,448 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:54,449 - INFO - 顺序分配客户端ID: 78
2025-07-31 15:27:54,449 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 78
2025-07-31 15:27:54,452 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:54,453 - WARNING - [Client 78] 数据源为None，已创建新数据源
2025-07-31 15:27:54,454 - WARNING - [Client 78] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:57,054 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:57,056 - INFO - [Client 78] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:57,057 - INFO - [Client 78] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:57,080 - INFO - [Client 78] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:57,081 - INFO - [Client 78] 初始化时成功加载数据
2025-07-31 15:27:57,083 - INFO - [客户端 78] 初始化验证通过
2025-07-31 15:27:57,083 - INFO - 客户端 78 实例创建成功
2025-07-31 15:27:57,083 - INFO - 客户端78已设置服务器引用
2025-07-31 15:27:57,084 - INFO - 客户端 78 已设置服务器引用
2025-07-31 15:27:57,085 - INFO - 客户端78已注册
2025-07-31 15:27:57,086 - INFO - 客户端 78 已成功注册到服务器
2025-07-31 15:27:57,087 - INFO - 开始创建客户端 79...
2025-07-31 15:27:57,087 - INFO - 初始化客户端, ID: 79
2025-07-31 15:27:57,211 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:57,211 - INFO - [Client 79] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:57,212 - INFO - [Trainer] 初始化训练器, client_id: 79
2025-07-31 15:27:57,253 - INFO - [Trainer 79] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:57,255 - INFO - [Trainer 79] 模型的输入通道数: 3
2025-07-31 15:27:57,255 - INFO - [Trainer 79] 强制使用CPU
2025-07-31 15:27:57,258 - INFO - [Trainer 79] 模型已移至设备: cpu
2025-07-31 15:27:57,259 - INFO - [Trainer 79] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:57,259 - INFO - [Trainer 79] 初始化完成
2025-07-31 15:27:57,260 - INFO - [Client 79] 创建新训练器
2025-07-31 15:27:57,260 - INFO - [Algorithm] 从训练器获取客户端ID: 79
2025-07-31 15:27:57,260 - INFO - [Algorithm] 初始化后修正client_id: 79
2025-07-31 15:27:57,261 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:57,262 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:57,263 - INFO - [Client 79] 创建新算法
2025-07-31 15:27:57,263 - INFO - [Algorithm] 设置客户端ID: 79
2025-07-31 15:27:57,264 - INFO - [Algorithm] 同步更新trainer的client_id: 79
2025-07-31 15:27:57,265 - INFO - [Client None] 父类初始化完成
2025-07-31 15:27:57,371 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:57,371 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:57,372 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:27:57,411 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:57,412 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:27:57,413 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:27:57,415 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:27:57,415 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:57,417 - INFO - [Trainer None] 初始化完成
2025-07-31 15:27:57,417 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:27:57,418 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:27:57,418 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:27:57,419 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:57,419 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:57,420 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:27:57,420 - INFO - [Client None] 开始加载数据
2025-07-31 15:27:57,421 - INFO - 顺序分配客户端ID: 79
2025-07-31 15:27:57,422 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 79
2025-07-31 15:27:57,424 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:27:57,426 - WARNING - [Client 79] 数据源为None，已创建新数据源
2025-07-31 15:27:57,427 - WARNING - [Client 79] 数据源trainset为None，已设置为空列表
2025-07-31 15:27:59,830 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:27:59,832 - INFO - [Client 79] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:27:59,833 - INFO - [Client 79] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:27:59,847 - INFO - [Client 79] 成功划分数据集，分配到 300 个样本
2025-07-31 15:27:59,847 - INFO - [Client 79] 初始化时成功加载数据
2025-07-31 15:27:59,849 - INFO - [客户端 79] 初始化验证通过
2025-07-31 15:27:59,851 - INFO - 客户端 79 实例创建成功
2025-07-31 15:27:59,852 - INFO - 客户端79已设置服务器引用
2025-07-31 15:27:59,853 - INFO - 客户端 79 已设置服务器引用
2025-07-31 15:27:59,854 - INFO - 客户端79已注册
2025-07-31 15:27:59,855 - INFO - 客户端 79 已成功注册到服务器
2025-07-31 15:27:59,857 - INFO - 开始创建客户端 80...
2025-07-31 15:27:59,859 - INFO - 初始化客户端, ID: 80
2025-07-31 15:27:59,931 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:27:59,931 - INFO - [Client 80] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:27:59,931 - INFO - [Trainer] 初始化训练器, client_id: 80
2025-07-31 15:27:59,957 - INFO - [Trainer 80] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:27:59,958 - INFO - [Trainer 80] 模型的输入通道数: 3
2025-07-31 15:27:59,958 - INFO - [Trainer 80] 强制使用CPU
2025-07-31 15:27:59,960 - INFO - [Trainer 80] 模型已移至设备: cpu
2025-07-31 15:27:59,961 - INFO - [Trainer 80] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:27:59,961 - INFO - [Trainer 80] 初始化完成
2025-07-31 15:27:59,962 - INFO - [Client 80] 创建新训练器
2025-07-31 15:27:59,963 - INFO - [Algorithm] 从训练器获取客户端ID: 80
2025-07-31 15:27:59,963 - INFO - [Algorithm] 初始化后修正client_id: 80
2025-07-31 15:27:59,964 - INFO - [Algorithm] 初始化完成
2025-07-31 15:27:59,965 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:27:59,965 - INFO - [Client 80] 创建新算法
2025-07-31 15:27:59,965 - INFO - [Algorithm] 设置客户端ID: 80
2025-07-31 15:27:59,965 - INFO - [Algorithm] 同步更新trainer的client_id: 80
2025-07-31 15:27:59,967 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:00,035 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:00,035 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:00,037 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:00,064 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:00,067 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:00,068 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:00,072 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:00,072 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:00,073 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:00,074 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:00,074 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:00,075 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:00,078 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:00,082 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:00,083 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:00,085 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:00,087 - INFO - 顺序分配客户端ID: 80
2025-07-31 15:28:00,089 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 80
2025-07-31 15:28:00,092 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:00,093 - WARNING - [Client 80] 数据源为None，已创建新数据源
2025-07-31 15:28:00,093 - WARNING - [Client 80] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:02,478 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:02,479 - INFO - [Client 80] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:02,480 - INFO - [Client 80] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:02,495 - INFO - [Client 80] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:02,497 - INFO - [Client 80] 初始化时成功加载数据
2025-07-31 15:28:02,497 - INFO - [客户端 80] 初始化验证通过
2025-07-31 15:28:02,497 - INFO - 客户端 80 实例创建成功
2025-07-31 15:28:02,500 - INFO - 客户端80已设置服务器引用
2025-07-31 15:28:02,503 - INFO - 客户端 80 已设置服务器引用
2025-07-31 15:28:02,511 - INFO - 客户端80已注册
2025-07-31 15:28:02,512 - INFO - 客户端 80 已成功注册到服务器
2025-07-31 15:28:02,515 - INFO - 开始创建客户端 81...
2025-07-31 15:28:02,517 - INFO - 初始化客户端, ID: 81
2025-07-31 15:28:02,623 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:02,624 - INFO - [Client 81] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:02,626 - INFO - [Trainer] 初始化训练器, client_id: 81
2025-07-31 15:28:02,661 - INFO - [Trainer 81] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:02,662 - INFO - [Trainer 81] 模型的输入通道数: 3
2025-07-31 15:28:02,663 - INFO - [Trainer 81] 强制使用CPU
2025-07-31 15:28:02,665 - INFO - [Trainer 81] 模型已移至设备: cpu
2025-07-31 15:28:02,667 - INFO - [Trainer 81] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:02,667 - INFO - [Trainer 81] 初始化完成
2025-07-31 15:28:02,667 - INFO - [Client 81] 创建新训练器
2025-07-31 15:28:02,667 - INFO - [Algorithm] 从训练器获取客户端ID: 81
2025-07-31 15:28:02,669 - INFO - [Algorithm] 初始化后修正client_id: 81
2025-07-31 15:28:02,670 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:02,670 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:02,671 - INFO - [Client 81] 创建新算法
2025-07-31 15:28:02,671 - INFO - [Algorithm] 设置客户端ID: 81
2025-07-31 15:28:02,672 - INFO - [Algorithm] 同步更新trainer的client_id: 81
2025-07-31 15:28:02,672 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:02,751 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:02,752 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:02,752 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:02,780 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:02,781 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:02,783 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:02,785 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:02,787 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:02,789 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:02,790 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:02,792 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:02,793 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:02,795 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:02,798 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:02,799 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:02,802 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:02,802 - INFO - 顺序分配客户端ID: 81
2025-07-31 15:28:02,803 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 81
2025-07-31 15:28:02,808 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:02,809 - WARNING - [Client 81] 数据源为None，已创建新数据源
2025-07-31 15:28:02,810 - WARNING - [Client 81] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:05,323 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:05,324 - INFO - [Client 81] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:05,324 - INFO - [Client 81] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:05,341 - INFO - [Client 81] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:05,342 - INFO - [Client 81] 初始化时成功加载数据
2025-07-31 15:28:05,343 - INFO - [客户端 81] 初始化验证通过
2025-07-31 15:28:05,344 - INFO - 客户端 81 实例创建成功
2025-07-31 15:28:05,344 - INFO - 客户端81已设置服务器引用
2025-07-31 15:28:05,345 - INFO - 客户端 81 已设置服务器引用
2025-07-31 15:28:05,346 - INFO - 客户端81已注册
2025-07-31 15:28:05,346 - INFO - 客户端 81 已成功注册到服务器
2025-07-31 15:28:05,347 - INFO - 开始创建客户端 82...
2025-07-31 15:28:05,347 - INFO - 初始化客户端, ID: 82
2025-07-31 15:28:05,447 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:05,448 - INFO - [Client 82] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:05,449 - INFO - [Trainer] 初始化训练器, client_id: 82
2025-07-31 15:28:05,485 - INFO - [Trainer 82] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:05,487 - INFO - [Trainer 82] 模型的输入通道数: 3
2025-07-31 15:28:05,487 - INFO - [Trainer 82] 强制使用CPU
2025-07-31 15:28:05,488 - INFO - [Trainer 82] 模型已移至设备: cpu
2025-07-31 15:28:05,489 - INFO - [Trainer 82] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:05,493 - INFO - [Trainer 82] 初始化完成
2025-07-31 15:28:05,494 - INFO - [Client 82] 创建新训练器
2025-07-31 15:28:05,495 - INFO - [Algorithm] 从训练器获取客户端ID: 82
2025-07-31 15:28:05,498 - INFO - [Algorithm] 初始化后修正client_id: 82
2025-07-31 15:28:05,498 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:05,500 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:05,501 - INFO - [Client 82] 创建新算法
2025-07-31 15:28:05,502 - INFO - [Algorithm] 设置客户端ID: 82
2025-07-31 15:28:05,502 - INFO - [Algorithm] 同步更新trainer的client_id: 82
2025-07-31 15:28:05,509 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:05,599 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:05,600 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:05,601 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:05,640 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:05,642 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:05,647 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:05,650 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:05,652 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:05,654 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:05,655 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:05,660 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:05,661 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:05,662 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:05,671 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:05,671 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:05,673 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:05,674 - INFO - 顺序分配客户端ID: 82
2025-07-31 15:28:05,676 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 82
2025-07-31 15:28:05,678 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:05,681 - WARNING - [Client 82] 数据源为None，已创建新数据源
2025-07-31 15:28:05,682 - WARNING - [Client 82] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:08,176 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:08,177 - INFO - [Client 82] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:08,177 - INFO - [Client 82] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:08,193 - INFO - [Client 82] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:08,194 - INFO - [Client 82] 初始化时成功加载数据
2025-07-31 15:28:08,194 - INFO - [客户端 82] 初始化验证通过
2025-07-31 15:28:08,195 - INFO - 客户端 82 实例创建成功
2025-07-31 15:28:08,195 - INFO - 客户端82已设置服务器引用
2025-07-31 15:28:08,196 - INFO - 客户端 82 已设置服务器引用
2025-07-31 15:28:08,197 - INFO - 客户端82已注册
2025-07-31 15:28:08,197 - INFO - 客户端 82 已成功注册到服务器
2025-07-31 15:28:08,197 - INFO - 开始创建客户端 83...
2025-07-31 15:28:08,198 - INFO - 初始化客户端, ID: 83
2025-07-31 15:28:08,307 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:08,307 - INFO - [Client 83] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:08,308 - INFO - [Trainer] 初始化训练器, client_id: 83
2025-07-31 15:28:08,348 - INFO - [Trainer 83] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:08,349 - INFO - [Trainer 83] 模型的输入通道数: 3
2025-07-31 15:28:08,350 - INFO - [Trainer 83] 强制使用CPU
2025-07-31 15:28:08,353 - INFO - [Trainer 83] 模型已移至设备: cpu
2025-07-31 15:28:08,355 - INFO - [Trainer 83] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:08,357 - INFO - [Trainer 83] 初始化完成
2025-07-31 15:28:08,357 - INFO - [Client 83] 创建新训练器
2025-07-31 15:28:08,358 - INFO - [Algorithm] 从训练器获取客户端ID: 83
2025-07-31 15:28:08,358 - INFO - [Algorithm] 初始化后修正client_id: 83
2025-07-31 15:28:08,359 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:08,359 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:08,360 - INFO - [Client 83] 创建新算法
2025-07-31 15:28:08,361 - INFO - [Algorithm] 设置客户端ID: 83
2025-07-31 15:28:08,361 - INFO - [Algorithm] 同步更新trainer的client_id: 83
2025-07-31 15:28:08,363 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:08,451 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:08,451 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:08,453 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:08,485 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:08,485 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:08,487 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:08,490 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:08,491 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:08,492 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:08,493 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:08,493 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:08,494 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:08,494 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:08,495 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:08,495 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:08,498 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:08,498 - INFO - 顺序分配客户端ID: 83
2025-07-31 15:28:08,500 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 83
2025-07-31 15:28:08,501 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:08,502 - WARNING - [Client 83] 数据源为None，已创建新数据源
2025-07-31 15:28:08,503 - WARNING - [Client 83] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:10,975 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:10,975 - INFO - [Client 83] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:10,977 - INFO - [Client 83] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:10,989 - INFO - [Client 83] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:10,990 - INFO - [Client 83] 初始化时成功加载数据
2025-07-31 15:28:10,990 - INFO - [客户端 83] 初始化验证通过
2025-07-31 15:28:10,991 - INFO - 客户端 83 实例创建成功
2025-07-31 15:28:10,991 - INFO - 客户端83已设置服务器引用
2025-07-31 15:28:10,991 - INFO - 客户端 83 已设置服务器引用
2025-07-31 15:28:10,991 - INFO - 客户端83已注册
2025-07-31 15:28:10,992 - INFO - 客户端 83 已成功注册到服务器
2025-07-31 15:28:10,992 - INFO - 开始创建客户端 84...
2025-07-31 15:28:10,992 - INFO - 初始化客户端, ID: 84
2025-07-31 15:28:11,061 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:11,062 - INFO - [Client 84] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:11,062 - INFO - [Trainer] 初始化训练器, client_id: 84
2025-07-31 15:28:11,087 - INFO - [Trainer 84] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:11,087 - INFO - [Trainer 84] 模型的输入通道数: 3
2025-07-31 15:28:11,088 - INFO - [Trainer 84] 强制使用CPU
2025-07-31 15:28:11,089 - INFO - [Trainer 84] 模型已移至设备: cpu
2025-07-31 15:28:11,090 - INFO - [Trainer 84] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:11,090 - INFO - [Trainer 84] 初始化完成
2025-07-31 15:28:11,091 - INFO - [Client 84] 创建新训练器
2025-07-31 15:28:11,091 - INFO - [Algorithm] 从训练器获取客户端ID: 84
2025-07-31 15:28:11,091 - INFO - [Algorithm] 初始化后修正client_id: 84
2025-07-31 15:28:11,091 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:11,093 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:11,093 - INFO - [Client 84] 创建新算法
2025-07-31 15:28:11,093 - INFO - [Algorithm] 设置客户端ID: 84
2025-07-31 15:28:11,094 - INFO - [Algorithm] 同步更新trainer的client_id: 84
2025-07-31 15:28:11,094 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:11,209 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:11,210 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:11,210 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:11,237 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:11,238 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:11,238 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:11,240 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:11,241 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:11,242 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:11,242 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:11,243 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:11,243 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:11,244 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:11,244 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:11,244 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:11,245 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:11,245 - INFO - 顺序分配客户端ID: 84
2025-07-31 15:28:11,245 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 84
2025-07-31 15:28:11,248 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:11,248 - WARNING - [Client 84] 数据源为None，已创建新数据源
2025-07-31 15:28:11,249 - WARNING - [Client 84] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:13,820 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:13,821 - INFO - [Client 84] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:13,821 - INFO - [Client 84] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:13,839 - INFO - [Client 84] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:13,839 - INFO - [Client 84] 初始化时成功加载数据
2025-07-31 15:28:13,840 - INFO - [客户端 84] 初始化验证通过
2025-07-31 15:28:13,841 - INFO - 客户端 84 实例创建成功
2025-07-31 15:28:13,841 - INFO - 客户端84已设置服务器引用
2025-07-31 15:28:13,841 - INFO - 客户端 84 已设置服务器引用
2025-07-31 15:28:13,842 - INFO - 客户端84已注册
2025-07-31 15:28:13,842 - INFO - 客户端 84 已成功注册到服务器
2025-07-31 15:28:13,843 - INFO - 开始创建客户端 85...
2025-07-31 15:28:13,843 - INFO - 初始化客户端, ID: 85
2025-07-31 15:28:13,927 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:13,927 - INFO - [Client 85] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:13,927 - INFO - [Trainer] 初始化训练器, client_id: 85
2025-07-31 15:28:13,957 - INFO - [Trainer 85] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:13,958 - INFO - [Trainer 85] 模型的输入通道数: 3
2025-07-31 15:28:13,958 - INFO - [Trainer 85] 强制使用CPU
2025-07-31 15:28:13,960 - INFO - [Trainer 85] 模型已移至设备: cpu
2025-07-31 15:28:13,961 - INFO - [Trainer 85] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:13,962 - INFO - [Trainer 85] 初始化完成
2025-07-31 15:28:13,962 - INFO - [Client 85] 创建新训练器
2025-07-31 15:28:13,962 - INFO - [Algorithm] 从训练器获取客户端ID: 85
2025-07-31 15:28:13,962 - INFO - [Algorithm] 初始化后修正client_id: 85
2025-07-31 15:28:13,964 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:13,964 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:13,965 - INFO - [Client 85] 创建新算法
2025-07-31 15:28:13,965 - INFO - [Algorithm] 设置客户端ID: 85
2025-07-31 15:28:13,965 - INFO - [Algorithm] 同步更新trainer的client_id: 85
2025-07-31 15:28:13,965 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:14,055 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:14,055 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:14,057 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:14,085 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:14,086 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:14,087 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:14,088 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:14,089 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:14,090 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:14,090 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:14,091 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:14,091 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:14,092 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:14,094 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:14,095 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:14,095 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:14,096 - INFO - 顺序分配客户端ID: 85
2025-07-31 15:28:14,096 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 85
2025-07-31 15:28:14,097 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:14,098 - WARNING - [Client 85] 数据源为None，已创建新数据源
2025-07-31 15:28:14,099 - WARNING - [Client 85] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:16,534 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:16,535 - INFO - [Client 85] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:16,535 - INFO - [Client 85] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:16,552 - INFO - [Client 85] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:16,554 - INFO - [Client 85] 初始化时成功加载数据
2025-07-31 15:28:16,554 - INFO - [客户端 85] 初始化验证通过
2025-07-31 15:28:16,555 - INFO - 客户端 85 实例创建成功
2025-07-31 15:28:16,555 - INFO - 客户端85已设置服务器引用
2025-07-31 15:28:16,556 - INFO - 客户端 85 已设置服务器引用
2025-07-31 15:28:16,556 - INFO - 客户端85已注册
2025-07-31 15:28:16,557 - INFO - 客户端 85 已成功注册到服务器
2025-07-31 15:28:16,557 - INFO - 开始创建客户端 86...
2025-07-31 15:28:16,557 - INFO - 初始化客户端, ID: 86
2025-07-31 15:28:16,642 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:16,643 - INFO - [Client 86] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:16,643 - INFO - [Trainer] 初始化训练器, client_id: 86
2025-07-31 15:28:16,673 - INFO - [Trainer 86] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:16,674 - INFO - [Trainer 86] 模型的输入通道数: 3
2025-07-31 15:28:16,674 - INFO - [Trainer 86] 强制使用CPU
2025-07-31 15:28:16,677 - INFO - [Trainer 86] 模型已移至设备: cpu
2025-07-31 15:28:16,677 - INFO - [Trainer 86] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:16,677 - INFO - [Trainer 86] 初始化完成
2025-07-31 15:28:16,679 - INFO - [Client 86] 创建新训练器
2025-07-31 15:28:16,680 - INFO - [Algorithm] 从训练器获取客户端ID: 86
2025-07-31 15:28:16,680 - INFO - [Algorithm] 初始化后修正client_id: 86
2025-07-31 15:28:16,680 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:16,681 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:16,681 - INFO - [Client 86] 创建新算法
2025-07-31 15:28:16,682 - INFO - [Algorithm] 设置客户端ID: 86
2025-07-31 15:28:16,682 - INFO - [Algorithm] 同步更新trainer的client_id: 86
2025-07-31 15:28:16,683 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:16,779 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:16,780 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:16,781 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:16,812 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:16,815 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:16,815 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:16,818 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:16,820 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:16,821 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:16,822 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:16,822 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:16,823 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:16,823 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:16,824 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:16,825 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:16,825 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:16,825 - INFO - 顺序分配客户端ID: 86
2025-07-31 15:28:16,830 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 86
2025-07-31 15:28:16,832 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:16,833 - WARNING - [Client 86] 数据源为None，已创建新数据源
2025-07-31 15:28:16,835 - WARNING - [Client 86] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:19,410 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:19,410 - INFO - [Client 86] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:19,411 - INFO - [Client 86] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:19,429 - INFO - [Client 86] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:19,429 - INFO - [Client 86] 初始化时成功加载数据
2025-07-31 15:28:19,430 - INFO - [客户端 86] 初始化验证通过
2025-07-31 15:28:19,430 - INFO - 客户端 86 实例创建成功
2025-07-31 15:28:19,430 - INFO - 客户端86已设置服务器引用
2025-07-31 15:28:19,431 - INFO - 客户端 86 已设置服务器引用
2025-07-31 15:28:19,431 - INFO - 客户端86已注册
2025-07-31 15:28:19,431 - INFO - 客户端 86 已成功注册到服务器
2025-07-31 15:28:19,432 - INFO - 开始创建客户端 87...
2025-07-31 15:28:19,432 - INFO - 初始化客户端, ID: 87
2025-07-31 15:28:19,508 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:19,509 - INFO - [Client 87] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:19,510 - INFO - [Trainer] 初始化训练器, client_id: 87
2025-07-31 15:28:19,540 - INFO - [Trainer 87] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:19,547 - INFO - [Trainer 87] 模型的输入通道数: 3
2025-07-31 15:28:19,547 - INFO - [Trainer 87] 强制使用CPU
2025-07-31 15:28:19,549 - INFO - [Trainer 87] 模型已移至设备: cpu
2025-07-31 15:28:19,550 - INFO - [Trainer 87] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:19,550 - INFO - [Trainer 87] 初始化完成
2025-07-31 15:28:19,551 - INFO - [Client 87] 创建新训练器
2025-07-31 15:28:19,551 - INFO - [Algorithm] 从训练器获取客户端ID: 87
2025-07-31 15:28:19,552 - INFO - [Algorithm] 初始化后修正client_id: 87
2025-07-31 15:28:19,555 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:19,555 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:19,557 - INFO - [Client 87] 创建新算法
2025-07-31 15:28:19,557 - INFO - [Algorithm] 设置客户端ID: 87
2025-07-31 15:28:19,558 - INFO - [Algorithm] 同步更新trainer的client_id: 87
2025-07-31 15:28:19,558 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:19,640 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:19,640 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:19,640 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:19,674 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:19,675 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:19,675 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:19,679 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:19,680 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:19,681 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:19,682 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:19,683 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:19,684 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:19,684 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:19,687 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:19,687 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:19,689 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:19,689 - INFO - 顺序分配客户端ID: 87
2025-07-31 15:28:19,690 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 87
2025-07-31 15:28:19,694 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:19,695 - WARNING - [Client 87] 数据源为None，已创建新数据源
2025-07-31 15:28:19,697 - WARNING - [Client 87] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:22,282 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:22,283 - INFO - [Client 87] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:22,284 - INFO - [Client 87] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:22,301 - INFO - [Client 87] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:22,301 - INFO - [Client 87] 初始化时成功加载数据
2025-07-31 15:28:22,303 - INFO - [客户端 87] 初始化验证通过
2025-07-31 15:28:22,303 - INFO - 客户端 87 实例创建成功
2025-07-31 15:28:22,304 - INFO - 客户端87已设置服务器引用
2025-07-31 15:28:22,304 - INFO - 客户端 87 已设置服务器引用
2025-07-31 15:28:22,311 - INFO - 客户端87已注册
2025-07-31 15:28:22,312 - INFO - 客户端 87 已成功注册到服务器
2025-07-31 15:28:22,313 - INFO - 开始创建客户端 88...
2025-07-31 15:28:22,321 - INFO - 初始化客户端, ID: 88
2025-07-31 15:28:22,405 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:22,406 - INFO - [Client 88] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:22,406 - INFO - [Trainer] 初始化训练器, client_id: 88
2025-07-31 15:28:22,438 - INFO - [Trainer 88] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:22,440 - INFO - [Trainer 88] 模型的输入通道数: 3
2025-07-31 15:28:22,441 - INFO - [Trainer 88] 强制使用CPU
2025-07-31 15:28:22,442 - INFO - [Trainer 88] 模型已移至设备: cpu
2025-07-31 15:28:22,444 - INFO - [Trainer 88] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:22,449 - INFO - [Trainer 88] 初始化完成
2025-07-31 15:28:22,450 - INFO - [Client 88] 创建新训练器
2025-07-31 15:28:22,450 - INFO - [Algorithm] 从训练器获取客户端ID: 88
2025-07-31 15:28:22,451 - INFO - [Algorithm] 初始化后修正client_id: 88
2025-07-31 15:28:22,452 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:22,453 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:22,453 - INFO - [Client 88] 创建新算法
2025-07-31 15:28:22,454 - INFO - [Algorithm] 设置客户端ID: 88
2025-07-31 15:28:22,462 - INFO - [Algorithm] 同步更新trainer的client_id: 88
2025-07-31 15:28:22,463 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:22,562 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:22,563 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:22,564 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:22,601 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:22,602 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:22,602 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:22,604 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:22,604 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:22,605 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:22,605 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:22,606 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:22,606 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:22,606 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:22,606 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:22,607 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:22,607 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:22,607 - INFO - 顺序分配客户端ID: 88
2025-07-31 15:28:22,608 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 88
2025-07-31 15:28:22,609 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:22,610 - WARNING - [Client 88] 数据源为None，已创建新数据源
2025-07-31 15:28:22,610 - WARNING - [Client 88] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:24,923 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:24,924 - INFO - [Client 88] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:24,925 - INFO - [Client 88] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:24,940 - INFO - [Client 88] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:24,940 - INFO - [Client 88] 初始化时成功加载数据
2025-07-31 15:28:24,941 - INFO - [客户端 88] 初始化验证通过
2025-07-31 15:28:24,941 - INFO - 客户端 88 实例创建成功
2025-07-31 15:28:24,942 - INFO - 客户端88已设置服务器引用
2025-07-31 15:28:24,942 - INFO - 客户端 88 已设置服务器引用
2025-07-31 15:28:24,942 - INFO - 客户端88已注册
2025-07-31 15:28:24,943 - INFO - 客户端 88 已成功注册到服务器
2025-07-31 15:28:24,943 - INFO - 开始创建客户端 89...
2025-07-31 15:28:24,943 - INFO - 初始化客户端, ID: 89
2025-07-31 15:28:25,029 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:25,030 - INFO - [Client 89] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:25,030 - INFO - [Trainer] 初始化训练器, client_id: 89
2025-07-31 15:28:25,069 - INFO - [Trainer 89] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:25,070 - INFO - [Trainer 89] 模型的输入通道数: 3
2025-07-31 15:28:25,070 - INFO - [Trainer 89] 强制使用CPU
2025-07-31 15:28:25,072 - INFO - [Trainer 89] 模型已移至设备: cpu
2025-07-31 15:28:25,074 - INFO - [Trainer 89] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:25,074 - INFO - [Trainer 89] 初始化完成
2025-07-31 15:28:25,075 - INFO - [Client 89] 创建新训练器
2025-07-31 15:28:25,075 - INFO - [Algorithm] 从训练器获取客户端ID: 89
2025-07-31 15:28:25,076 - INFO - [Algorithm] 初始化后修正client_id: 89
2025-07-31 15:28:25,076 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:25,076 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:25,076 - INFO - [Client 89] 创建新算法
2025-07-31 15:28:25,077 - INFO - [Algorithm] 设置客户端ID: 89
2025-07-31 15:28:25,077 - INFO - [Algorithm] 同步更新trainer的client_id: 89
2025-07-31 15:28:25,078 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:25,172 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:25,173 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:25,174 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:25,210 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:25,211 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:25,212 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:25,213 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:25,214 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:25,215 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:25,216 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:25,216 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:25,217 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:25,218 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:25,218 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:25,220 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:25,220 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:25,221 - INFO - 顺序分配客户端ID: 89
2025-07-31 15:28:25,221 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 89
2025-07-31 15:28:25,223 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:25,223 - WARNING - [Client 89] 数据源为None，已创建新数据源
2025-07-31 15:28:25,224 - WARNING - [Client 89] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:27,609 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:27,610 - INFO - [Client 89] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:27,610 - INFO - [Client 89] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:27,627 - INFO - [Client 89] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:27,628 - INFO - [Client 89] 初始化时成功加载数据
2025-07-31 15:28:27,629 - INFO - [客户端 89] 初始化验证通过
2025-07-31 15:28:27,630 - INFO - 客户端 89 实例创建成功
2025-07-31 15:28:27,631 - INFO - 客户端89已设置服务器引用
2025-07-31 15:28:27,632 - INFO - 客户端 89 已设置服务器引用
2025-07-31 15:28:27,633 - INFO - 客户端89已注册
2025-07-31 15:28:27,633 - INFO - 客户端 89 已成功注册到服务器
2025-07-31 15:28:27,634 - INFO - 开始创建客户端 90...
2025-07-31 15:28:27,635 - INFO - 初始化客户端, ID: 90
2025-07-31 15:28:27,716 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:27,716 - INFO - [Client 90] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:27,717 - INFO - [Trainer] 初始化训练器, client_id: 90
2025-07-31 15:28:27,815 - INFO - [Trainer 90] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:27,816 - INFO - [Trainer 90] 模型的输入通道数: 3
2025-07-31 15:28:27,817 - INFO - [Trainer 90] 强制使用CPU
2025-07-31 15:28:27,819 - INFO - [Trainer 90] 模型已移至设备: cpu
2025-07-31 15:28:27,822 - INFO - [Trainer 90] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:27,823 - INFO - [Trainer 90] 初始化完成
2025-07-31 15:28:27,824 - INFO - [Client 90] 创建新训练器
2025-07-31 15:28:27,825 - INFO - [Algorithm] 从训练器获取客户端ID: 90
2025-07-31 15:28:27,826 - INFO - [Algorithm] 初始化后修正client_id: 90
2025-07-31 15:28:27,827 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:27,828 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:27,828 - INFO - [Client 90] 创建新算法
2025-07-31 15:28:27,829 - INFO - [Algorithm] 设置客户端ID: 90
2025-07-31 15:28:27,829 - INFO - [Algorithm] 同步更新trainer的client_id: 90
2025-07-31 15:28:27,830 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:27,940 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:27,942 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:27,942 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:27,979 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:27,980 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:27,980 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:27,983 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:27,985 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:27,985 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:27,986 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:27,986 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:27,986 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:27,987 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:27,987 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:27,987 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:27,988 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:27,988 - INFO - 顺序分配客户端ID: 90
2025-07-31 15:28:27,989 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 90
2025-07-31 15:28:27,991 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:27,995 - WARNING - [Client 90] 数据源为None，已创建新数据源
2025-07-31 15:28:27,997 - WARNING - [Client 90] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:30,576 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:30,578 - INFO - [Client 90] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:30,579 - INFO - [Client 90] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:30,599 - INFO - [Client 90] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:30,600 - INFO - [Client 90] 初始化时成功加载数据
2025-07-31 15:28:30,601 - INFO - [客户端 90] 初始化验证通过
2025-07-31 15:28:30,602 - INFO - 客户端 90 实例创建成功
2025-07-31 15:28:30,602 - INFO - 客户端90已设置服务器引用
2025-07-31 15:28:30,603 - INFO - 客户端 90 已设置服务器引用
2025-07-31 15:28:30,603 - INFO - 客户端90已注册
2025-07-31 15:28:30,604 - INFO - 客户端 90 已成功注册到服务器
2025-07-31 15:28:30,605 - INFO - 开始创建客户端 91...
2025-07-31 15:28:30,606 - INFO - 初始化客户端, ID: 91
2025-07-31 15:28:30,686 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:30,687 - INFO - [Client 91] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:30,691 - INFO - [Trainer] 初始化训练器, client_id: 91
2025-07-31 15:28:30,734 - INFO - [Trainer 91] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:30,736 - INFO - [Trainer 91] 模型的输入通道数: 3
2025-07-31 15:28:30,736 - INFO - [Trainer 91] 强制使用CPU
2025-07-31 15:28:30,739 - INFO - [Trainer 91] 模型已移至设备: cpu
2025-07-31 15:28:30,740 - INFO - [Trainer 91] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:30,741 - INFO - [Trainer 91] 初始化完成
2025-07-31 15:28:30,741 - INFO - [Client 91] 创建新训练器
2025-07-31 15:28:30,742 - INFO - [Algorithm] 从训练器获取客户端ID: 91
2025-07-31 15:28:30,743 - INFO - [Algorithm] 初始化后修正client_id: 91
2025-07-31 15:28:30,744 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:30,744 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:30,745 - INFO - [Client 91] 创建新算法
2025-07-31 15:28:30,747 - INFO - [Algorithm] 设置客户端ID: 91
2025-07-31 15:28:30,747 - INFO - [Algorithm] 同步更新trainer的client_id: 91
2025-07-31 15:28:30,748 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:30,848 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:30,849 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:30,852 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:30,905 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:30,905 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:30,905 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:30,907 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:30,909 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:30,909 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:30,910 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:30,912 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:30,912 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:30,913 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:30,919 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:30,920 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:30,920 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:30,922 - INFO - 顺序分配客户端ID: 91
2025-07-31 15:28:30,923 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 91
2025-07-31 15:28:30,925 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:30,926 - WARNING - [Client 91] 数据源为None，已创建新数据源
2025-07-31 15:28:30,926 - WARNING - [Client 91] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:33,349 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:33,350 - INFO - [Client 91] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:33,352 - INFO - [Client 91] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:33,370 - INFO - [Client 91] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:33,378 - INFO - [Client 91] 初始化时成功加载数据
2025-07-31 15:28:33,385 - INFO - [客户端 91] 初始化验证通过
2025-07-31 15:28:33,386 - INFO - 客户端 91 实例创建成功
2025-07-31 15:28:33,395 - INFO - 客户端91已设置服务器引用
2025-07-31 15:28:33,398 - INFO - 客户端 91 已设置服务器引用
2025-07-31 15:28:33,399 - INFO - 客户端91已注册
2025-07-31 15:28:33,400 - INFO - 客户端 91 已成功注册到服务器
2025-07-31 15:28:33,402 - INFO - 开始创建客户端 92...
2025-07-31 15:28:33,402 - INFO - 初始化客户端, ID: 92
2025-07-31 15:28:33,515 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:33,515 - INFO - [Client 92] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:33,516 - INFO - [Trainer] 初始化训练器, client_id: 92
2025-07-31 15:28:33,552 - INFO - [Trainer 92] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:33,553 - INFO - [Trainer 92] 模型的输入通道数: 3
2025-07-31 15:28:33,554 - INFO - [Trainer 92] 强制使用CPU
2025-07-31 15:28:33,555 - INFO - [Trainer 92] 模型已移至设备: cpu
2025-07-31 15:28:33,560 - INFO - [Trainer 92] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:33,561 - INFO - [Trainer 92] 初始化完成
2025-07-31 15:28:33,562 - INFO - [Client 92] 创建新训练器
2025-07-31 15:28:33,562 - INFO - [Algorithm] 从训练器获取客户端ID: 92
2025-07-31 15:28:33,563 - INFO - [Algorithm] 初始化后修正client_id: 92
2025-07-31 15:28:33,564 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:33,565 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:33,565 - INFO - [Client 92] 创建新算法
2025-07-31 15:28:33,566 - INFO - [Algorithm] 设置客户端ID: 92
2025-07-31 15:28:33,567 - INFO - [Algorithm] 同步更新trainer的client_id: 92
2025-07-31 15:28:33,568 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:33,673 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:33,675 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:33,676 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:33,712 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:33,713 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:33,714 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:33,716 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:33,717 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:33,717 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:33,718 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:33,719 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:33,719 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:33,719 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:33,720 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:33,720 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:33,721 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:33,722 - INFO - 顺序分配客户端ID: 92
2025-07-31 15:28:33,722 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 92
2025-07-31 15:28:33,723 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:33,724 - WARNING - [Client 92] 数据源为None，已创建新数据源
2025-07-31 15:28:33,724 - WARNING - [Client 92] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:36,317 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:36,319 - INFO - [Client 92] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:36,320 - INFO - [Client 92] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:36,337 - INFO - [Client 92] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:36,337 - INFO - [Client 92] 初始化时成功加载数据
2025-07-31 15:28:36,338 - INFO - [客户端 92] 初始化验证通过
2025-07-31 15:28:36,338 - INFO - 客户端 92 实例创建成功
2025-07-31 15:28:36,338 - INFO - 客户端92已设置服务器引用
2025-07-31 15:28:36,339 - INFO - 客户端 92 已设置服务器引用
2025-07-31 15:28:36,340 - INFO - 客户端92已注册
2025-07-31 15:28:36,340 - INFO - 客户端 92 已成功注册到服务器
2025-07-31 15:28:36,340 - INFO - 开始创建客户端 93...
2025-07-31 15:28:36,341 - INFO - 初始化客户端, ID: 93
2025-07-31 15:28:36,439 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:36,439 - INFO - [Client 93] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:36,440 - INFO - [Trainer] 初始化训练器, client_id: 93
2025-07-31 15:28:36,474 - INFO - [Trainer 93] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:36,475 - INFO - [Trainer 93] 模型的输入通道数: 3
2025-07-31 15:28:36,476 - INFO - [Trainer 93] 强制使用CPU
2025-07-31 15:28:36,477 - INFO - [Trainer 93] 模型已移至设备: cpu
2025-07-31 15:28:36,478 - INFO - [Trainer 93] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:36,479 - INFO - [Trainer 93] 初始化完成
2025-07-31 15:28:36,479 - INFO - [Client 93] 创建新训练器
2025-07-31 15:28:36,479 - INFO - [Algorithm] 从训练器获取客户端ID: 93
2025-07-31 15:28:36,480 - INFO - [Algorithm] 初始化后修正client_id: 93
2025-07-31 15:28:36,480 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:36,480 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:36,481 - INFO - [Client 93] 创建新算法
2025-07-31 15:28:36,481 - INFO - [Algorithm] 设置客户端ID: 93
2025-07-31 15:28:36,481 - INFO - [Algorithm] 同步更新trainer的client_id: 93
2025-07-31 15:28:36,482 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:36,549 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:36,550 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:36,551 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:36,581 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:36,582 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:36,583 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:36,585 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:36,586 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:36,587 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:36,587 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:36,588 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:36,588 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:36,589 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:36,589 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:36,590 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:36,590 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:36,590 - INFO - 顺序分配客户端ID: 93
2025-07-31 15:28:36,591 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 93
2025-07-31 15:28:36,592 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:36,593 - WARNING - [Client 93] 数据源为None，已创建新数据源
2025-07-31 15:28:36,593 - WARNING - [Client 93] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:39,112 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:39,112 - INFO - [Client 93] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:39,112 - INFO - [Client 93] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:39,130 - INFO - [Client 93] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:39,132 - INFO - [Client 93] 初始化时成功加载数据
2025-07-31 15:28:39,133 - INFO - [客户端 93] 初始化验证通过
2025-07-31 15:28:39,133 - INFO - 客户端 93 实例创建成功
2025-07-31 15:28:39,135 - INFO - 客户端93已设置服务器引用
2025-07-31 15:28:39,135 - INFO - 客户端 93 已设置服务器引用
2025-07-31 15:28:39,136 - INFO - 客户端93已注册
2025-07-31 15:28:39,136 - INFO - 客户端 93 已成功注册到服务器
2025-07-31 15:28:39,137 - INFO - 开始创建客户端 94...
2025-07-31 15:28:39,138 - INFO - 初始化客户端, ID: 94
2025-07-31 15:28:39,234 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:39,236 - INFO - [Client 94] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:39,237 - INFO - [Trainer] 初始化训练器, client_id: 94
2025-07-31 15:28:39,276 - INFO - [Trainer 94] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:39,278 - INFO - [Trainer 94] 模型的输入通道数: 3
2025-07-31 15:28:39,280 - INFO - [Trainer 94] 强制使用CPU
2025-07-31 15:28:39,283 - INFO - [Trainer 94] 模型已移至设备: cpu
2025-07-31 15:28:39,290 - INFO - [Trainer 94] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:39,292 - INFO - [Trainer 94] 初始化完成
2025-07-31 15:28:39,292 - INFO - [Client 94] 创建新训练器
2025-07-31 15:28:39,293 - INFO - [Algorithm] 从训练器获取客户端ID: 94
2025-07-31 15:28:39,293 - INFO - [Algorithm] 初始化后修正client_id: 94
2025-07-31 15:28:39,294 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:39,296 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:39,296 - INFO - [Client 94] 创建新算法
2025-07-31 15:28:39,297 - INFO - [Algorithm] 设置客户端ID: 94
2025-07-31 15:28:39,297 - INFO - [Algorithm] 同步更新trainer的client_id: 94
2025-07-31 15:28:39,297 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:39,437 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:39,438 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:39,439 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:39,477 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:39,480 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:39,480 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:39,484 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:39,485 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:39,493 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:39,494 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:39,495 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:39,495 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:39,496 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:39,497 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:39,500 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:39,500 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:39,501 - INFO - 顺序分配客户端ID: 94
2025-07-31 15:28:39,502 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 94
2025-07-31 15:28:39,502 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:39,506 - WARNING - [Client 94] 数据源为None，已创建新数据源
2025-07-31 15:28:39,507 - WARNING - [Client 94] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:42,108 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:42,109 - INFO - [Client 94] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:42,109 - INFO - [Client 94] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:42,123 - INFO - [Client 94] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:42,125 - INFO - [Client 94] 初始化时成功加载数据
2025-07-31 15:28:42,125 - INFO - [客户端 94] 初始化验证通过
2025-07-31 15:28:42,126 - INFO - 客户端 94 实例创建成功
2025-07-31 15:28:42,126 - INFO - 客户端94已设置服务器引用
2025-07-31 15:28:42,127 - INFO - 客户端 94 已设置服务器引用
2025-07-31 15:28:42,127 - INFO - 客户端94已注册
2025-07-31 15:28:42,129 - INFO - 客户端 94 已成功注册到服务器
2025-07-31 15:28:42,130 - INFO - 开始创建客户端 95...
2025-07-31 15:28:42,131 - INFO - 初始化客户端, ID: 95
2025-07-31 15:28:42,215 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:42,216 - INFO - [Client 95] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:42,216 - INFO - [Trainer] 初始化训练器, client_id: 95
2025-07-31 15:28:42,244 - INFO - [Trainer 95] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:42,244 - INFO - [Trainer 95] 模型的输入通道数: 3
2025-07-31 15:28:42,245 - INFO - [Trainer 95] 强制使用CPU
2025-07-31 15:28:42,246 - INFO - [Trainer 95] 模型已移至设备: cpu
2025-07-31 15:28:42,247 - INFO - [Trainer 95] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:42,248 - INFO - [Trainer 95] 初始化完成
2025-07-31 15:28:42,248 - INFO - [Client 95] 创建新训练器
2025-07-31 15:28:42,249 - INFO - [Algorithm] 从训练器获取客户端ID: 95
2025-07-31 15:28:42,249 - INFO - [Algorithm] 初始化后修正client_id: 95
2025-07-31 15:28:42,249 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:42,250 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:42,250 - INFO - [Client 95] 创建新算法
2025-07-31 15:28:42,250 - INFO - [Algorithm] 设置客户端ID: 95
2025-07-31 15:28:42,251 - INFO - [Algorithm] 同步更新trainer的client_id: 95
2025-07-31 15:28:42,251 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:42,325 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:42,326 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:42,326 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:42,353 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:42,354 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:42,355 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:42,357 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:42,358 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:42,359 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:42,359 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:42,359 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:42,360 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:42,360 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:42,361 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:42,361 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:42,362 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:42,362 - INFO - 顺序分配客户端ID: 95
2025-07-31 15:28:42,363 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 95
2025-07-31 15:28:42,364 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:42,365 - WARNING - [Client 95] 数据源为None，已创建新数据源
2025-07-31 15:28:42,365 - WARNING - [Client 95] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:44,776 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:44,777 - INFO - [Client 95] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:44,778 - INFO - [Client 95] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:44,792 - INFO - [Client 95] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:44,792 - INFO - [Client 95] 初始化时成功加载数据
2025-07-31 15:28:44,793 - INFO - [客户端 95] 初始化验证通过
2025-07-31 15:28:44,793 - INFO - 客户端 95 实例创建成功
2025-07-31 15:28:44,794 - INFO - 客户端95已设置服务器引用
2025-07-31 15:28:44,794 - INFO - 客户端 95 已设置服务器引用
2025-07-31 15:28:44,795 - INFO - 客户端95已注册
2025-07-31 15:28:44,795 - INFO - 客户端 95 已成功注册到服务器
2025-07-31 15:28:44,796 - INFO - 开始创建客户端 96...
2025-07-31 15:28:44,796 - INFO - 初始化客户端, ID: 96
2025-07-31 15:28:44,874 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:44,875 - INFO - [Client 96] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:44,875 - INFO - [Trainer] 初始化训练器, client_id: 96
2025-07-31 15:28:44,904 - INFO - [Trainer 96] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:44,904 - INFO - [Trainer 96] 模型的输入通道数: 3
2025-07-31 15:28:44,906 - INFO - [Trainer 96] 强制使用CPU
2025-07-31 15:28:44,908 - INFO - [Trainer 96] 模型已移至设备: cpu
2025-07-31 15:28:44,908 - INFO - [Trainer 96] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:44,909 - INFO - [Trainer 96] 初始化完成
2025-07-31 15:28:44,909 - INFO - [Client 96] 创建新训练器
2025-07-31 15:28:44,910 - INFO - [Algorithm] 从训练器获取客户端ID: 96
2025-07-31 15:28:44,910 - INFO - [Algorithm] 初始化后修正client_id: 96
2025-07-31 15:28:44,911 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:44,911 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:44,911 - INFO - [Client 96] 创建新算法
2025-07-31 15:28:44,913 - INFO - [Algorithm] 设置客户端ID: 96
2025-07-31 15:28:44,913 - INFO - [Algorithm] 同步更新trainer的client_id: 96
2025-07-31 15:28:44,913 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:44,986 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:44,986 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:44,987 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:45,022 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:45,023 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:45,023 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:45,025 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:45,026 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:45,026 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:45,027 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:45,028 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:45,029 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:45,030 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:45,032 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:45,033 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:45,034 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:45,035 - INFO - 顺序分配客户端ID: 96
2025-07-31 15:28:45,036 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 96
2025-07-31 15:28:45,040 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:45,043 - WARNING - [Client 96] 数据源为None，已创建新数据源
2025-07-31 15:28:45,043 - WARNING - [Client 96] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:47,432 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-07-31 15:28:47,432 - INFO - [Client 96] 成功动态加载CIFAR10数据集，大小: 50000
2025-07-31 15:28:47,434 - INFO - [Client 96] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 100, 浓度参数: 0.1
2025-07-31 15:28:47,452 - INFO - [Client 96] 成功划分数据集，分配到 300 个样本
2025-07-31 15:28:47,453 - INFO - [Client 96] 初始化时成功加载数据
2025-07-31 15:28:47,454 - INFO - [客户端 96] 初始化验证通过
2025-07-31 15:28:47,454 - INFO - 客户端 96 实例创建成功
2025-07-31 15:28:47,455 - INFO - 客户端96已设置服务器引用
2025-07-31 15:28:47,456 - INFO - 客户端 96 已设置服务器引用
2025-07-31 15:28:47,459 - INFO - 客户端96已注册
2025-07-31 15:28:47,461 - INFO - 客户端 96 已成功注册到服务器
2025-07-31 15:28:47,461 - INFO - 开始创建客户端 97...
2025-07-31 15:28:47,462 - INFO - 初始化客户端, ID: 97
2025-07-31 15:28:47,539 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:47,539 - INFO - [Client 97] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:47,540 - INFO - [Trainer] 初始化训练器, client_id: 97
2025-07-31 15:28:47,570 - INFO - [Trainer 97] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:47,572 - INFO - [Trainer 97] 模型的输入通道数: 3
2025-07-31 15:28:47,573 - INFO - [Trainer 97] 强制使用CPU
2025-07-31 15:28:47,574 - INFO - [Trainer 97] 模型已移至设备: cpu
2025-07-31 15:28:47,575 - INFO - [Trainer 97] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:47,575 - INFO - [Trainer 97] 初始化完成
2025-07-31 15:28:47,576 - INFO - [Client 97] 创建新训练器
2025-07-31 15:28:47,577 - INFO - [Algorithm] 从训练器获取客户端ID: 97
2025-07-31 15:28:47,577 - INFO - [Algorithm] 初始化后修正client_id: 97
2025-07-31 15:28:47,577 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:47,579 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:47,579 - INFO - [Client 97] 创建新算法
2025-07-31 15:28:47,579 - INFO - [Algorithm] 设置客户端ID: 97
2025-07-31 15:28:47,580 - INFO - [Algorithm] 同步更新trainer的client_id: 97
2025-07-31 15:28:47,580 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:47,658 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:47,659 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:47,660 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:47,687 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:47,688 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:47,689 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:47,691 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:47,692 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:47,692 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:47,692 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:47,692 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:47,693 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:47,693 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:47,693 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:47,694 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:47,694 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:47,695 - INFO - 顺序分配客户端ID: 97
2025-07-31 15:28:47,695 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 97
2025-07-31 15:28:47,696 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:47,698 - WARNING - [Client 97] 数据源为None，已创建新数据源
2025-07-31 15:28:47,699 - WARNING - [Client 97] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:50,062 - ERROR - ❌ 加载数据集 CIFAR10 失败: 
2025-07-31 15:28:50,063 - WARNING - [Client 97] 动态加载失败: ，回退到MNIST
2025-07-31 15:28:50,106 - ERROR - [Client 97] 加载或划分数据集时出错: 
2025-07-31 15:28:50,232 - ERROR - [Client 97] 数据加载异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 696, in load_data
    full_trainset = DynamicDataLoader.load_dataset(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\dynamic_loader.py", line 109, in load_dataset
    dataset = dataset_class(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torchvision\datasets\cifar.py", line 83, in __init__
    entry = pickle.load(f, encoding="latin1")
MemoryError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 707, in load_data
    full_trainset = datasets.MNIST(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torchvision\datasets\mnist.py", line 105, in __init__
    self.data, self.targets = self._load_data()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torchvision\datasets\mnist.py", line 124, in _load_data
    data = read_image_file(os.path.join(self.raw_folder, image_file))
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torchvision\datasets\mnist.py", line 555, in read_image_file
    x = read_sn3_pascalvincent_tensor(path, strict=False)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torchvision\datasets\mnist.py", line 514, in read_sn3_pascalvincent_tensor
    data = f.read()
MemoryError

2025-07-31 15:28:50,258 - ERROR - [Client 97] 初始化时数据加载失败
2025-07-31 15:28:50,258 - INFO - [客户端 97] 初始化验证通过
2025-07-31 15:28:50,259 - INFO - 客户端 97 实例创建成功
2025-07-31 15:28:50,261 - INFO - 客户端97已设置服务器引用
2025-07-31 15:28:50,262 - INFO - 客户端 97 已设置服务器引用
2025-07-31 15:28:50,267 - INFO - 客户端97已注册
2025-07-31 15:28:50,268 - INFO - 客户端 97 已成功注册到服务器
2025-07-31 15:28:50,269 - INFO - 开始创建客户端 98...
2025-07-31 15:28:50,270 - INFO - 初始化客户端, ID: 98
2025-07-31 15:28:50,366 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:50,367 - INFO - [Client 98] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:50,367 - INFO - [Trainer] 初始化训练器, client_id: 98
2025-07-31 15:28:50,397 - INFO - [Trainer 98] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:50,398 - INFO - [Trainer 98] 模型的输入通道数: 3
2025-07-31 15:28:50,399 - INFO - [Trainer 98] 强制使用CPU
2025-07-31 15:28:50,401 - INFO - [Trainer 98] 模型已移至设备: cpu
2025-07-31 15:28:50,401 - INFO - [Trainer 98] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:50,402 - INFO - [Trainer 98] 初始化完成
2025-07-31 15:28:50,402 - INFO - [Client 98] 创建新训练器
2025-07-31 15:28:50,403 - INFO - [Algorithm] 从训练器获取客户端ID: 98
2025-07-31 15:28:50,404 - INFO - [Algorithm] 初始化后修正client_id: 98
2025-07-31 15:28:50,405 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:50,405 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:50,406 - INFO - [Client 98] 创建新算法
2025-07-31 15:28:50,408 - INFO - [Algorithm] 设置客户端ID: 98
2025-07-31 15:28:50,409 - INFO - [Algorithm] 同步更新trainer的client_id: 98
2025-07-31 15:28:50,410 - INFO - [Client None] 父类初始化完成
2025-07-31 15:28:50,498 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:50,499 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:50,500 - INFO - [Trainer] 初始化训练器, client_id: None
2025-07-31 15:28:50,542 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-07-31 15:28:50,547 - INFO - [Trainer None] 模型的输入通道数: 3
2025-07-31 15:28:50,548 - INFO - [Trainer None] 强制使用CPU
2025-07-31 15:28:50,553 - INFO - [Trainer None] 模型已移至设备: cpu
2025-07-31 15:28:50,554 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-07-31 15:28:50,555 - INFO - [Trainer None] 初始化完成
2025-07-31 15:28:50,557 - INFO - [Client None] 父类初始化后创建新训练器
2025-07-31 15:28:50,558 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-07-31 15:28:50,564 - INFO - [Algorithm] 初始化后修正client_id: None
2025-07-31 15:28:50,567 - INFO - [Algorithm] 初始化完成
2025-07-31 15:28:50,569 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-07-31 15:28:50,571 - INFO - [Client None] 父类初始化后创建新算法
2025-07-31 15:28:50,571 - INFO - [Client None] 开始加载数据
2025-07-31 15:28:50,571 - INFO - 顺序分配客户端ID: 98
2025-07-31 15:28:50,572 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 98
2025-07-31 15:28:50,575 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-07-31 15:28:50,578 - WARNING - [Client 98] 数据源为None，已创建新数据源
2025-07-31 15:28:50,578 - WARNING - [Client 98] 数据源trainset为None，已设置为空列表
2025-07-31 15:28:52,572 - ERROR - ❌ 加载数据集 CIFAR10 失败: 
2025-07-31 15:28:52,573 - WARNING - [Client 98] 动态加载失败: ，回退到MNIST
2025-07-31 15:28:52,712 - ERROR - [Client 98] 加载或划分数据集时出错: 
2025-07-31 15:28:52,714 - ERROR - [Client 98] 数据加载异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 696, in load_data
    full_trainset = DynamicDataLoader.load_dataset(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\dynamic_loader.py", line 109, in load_dataset
    dataset = dataset_class(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torchvision\datasets\cifar.py", line 83, in __init__
    entry = pickle.load(f, encoding="latin1")
MemoryError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 707, in load_data
    full_trainset = datasets.MNIST(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torchvision\datasets\mnist.py", line 105, in __init__
    self.data, self.targets = self._load_data()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torchvision\datasets\mnist.py", line 124, in _load_data
    data = read_image_file(os.path.join(self.raw_folder, image_file))
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torchvision\datasets\mnist.py", line 555, in read_image_file
    x = read_sn3_pascalvincent_tensor(path, strict=False)
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torchvision\datasets\mnist.py", line 534, in read_sn3_pascalvincent_tensor
    parsed = torch.frombuffer(bytearray(data), dtype=torch_type, offset=(4 * (nd + 1)))
MemoryError

2025-07-31 15:28:52,733 - ERROR - [Client 98] 初始化时数据加载失败
2025-07-31 15:28:52,734 - INFO - [客户端 98] 初始化验证通过
2025-07-31 15:28:52,736 - INFO - 客户端 98 实例创建成功
2025-07-31 15:28:52,738 - INFO - 客户端98已设置服务器引用
2025-07-31 15:28:52,739 - INFO - 客户端 98 已设置服务器引用
2025-07-31 15:28:52,740 - INFO - 客户端98已注册
2025-07-31 15:28:52,742 - INFO - 客户端 98 已成功注册到服务器
2025-07-31 15:28:52,745 - INFO - 开始创建客户端 99...
2025-07-31 15:28:52,746 - INFO - 初始化客户端, ID: 99
2025-07-31 15:28:52,854 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-07-31 15:28:52,857 - INFO - [Client 99] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-07-31 15:28:52,858 - INFO - [Trainer] 初始化训练器, client_id: 99
