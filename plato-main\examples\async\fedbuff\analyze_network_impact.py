#!/usr/bin/env python3
"""
分析FedBuff网络波动前后的影响
"""

import pandas as pd
import os

def analyze_fedbuff_network_impact():
    """分析FedBuff网络波动的影响"""
    print("📊 FedBuff网络波动影响分析")
    print("=" * 60)
    
    # 文件路径
    standard_file = "results/mnist_standard_fedbuff/01/fedbuff_MNIST_standard_20250121_1200.csv"
    network_file = "results/mnist_network_test_fedbuff/01/fedbuff_MNIST_network_test_20250121_1205.csv"
    
    # 检查文件是否存在
    if not os.path.exists(standard_file):
        print(f"❌ 标准版本文件不存在: {standard_file}")
        return
    
    if not os.path.exists(network_file):
        print(f"❌ 网络测试版本文件不存在: {network_file}")
        return
    
    # 读取数据
    try:
        df_standard = pd.read_csv(standard_file)
        df_network = pd.read_csv(network_file)
        
        print(f"✅ 数据加载成功")
        print(f"   标准版本: {len(df_standard)} 轮")
        print(f"   网络测试版本: {len(df_network)} 轮")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 基本统计
    print(f"\n📈 基本性能对比")
    print("-" * 40)
    
    # 最终轮次对比
    final_standard = df_standard.iloc[-1]
    final_network = df_network.iloc[-1]
    
    print(f"🕐 训练时间 (第10轮):")
    print(f"   标准版本: {final_standard['elapsed_time']:.2f}秒")
    print(f"   网络测试: {final_network['elapsed_time']:.2f}秒")
    time_increase = (final_network['elapsed_time'] - final_standard['elapsed_time']) / final_standard['elapsed_time'] * 100
    print(f"   增加: {time_increase:.1f}%")
    
    print(f"\n🎯 准确率 (第10轮):")
    print(f"   标准版本: {final_standard['accuracy']:.4f}")
    print(f"   网络测试: {final_network['accuracy']:.4f}")
    acc_change = (final_network['accuracy'] - final_standard['accuracy']) / final_standard['accuracy'] * 100
    print(f"   变化: {acc_change:+.1f}%")
    
    print(f"\n🌐 全局准确率 (第10轮):")
    print(f"   标准版本: {final_standard['global_accuracy']:.4f}")
    print(f"   网络测试: {final_network['global_accuracy']:.4f}")
    global_acc_change = (final_network['global_accuracy'] - final_standard['global_accuracy']) / final_standard['global_accuracy'] * 100
    print(f"   变化: {global_acc_change:+.1f}%")
    
    print(f"\n📊 陈旧度 (第10轮):")
    print(f"   标准版本: {final_standard['avg_staleness']:.2f}")
    print(f"   网络测试: {final_network['avg_staleness']:.2f}")
    staleness_increase = (final_network['avg_staleness'] - final_standard['avg_staleness']) / final_standard['avg_staleness'] * 100
    print(f"   增加: {staleness_increase:.1f}%")
    
    # 网络统计（仅网络测试版本有）
    print(f"\n🌐 网络统计 (网络测试版本):")
    print(f"   网络成功率: {final_network['network_success_rate']:.3f} ({final_network['network_success_rate']*100:.1f}%)")
    print(f"   平均通信时间: {final_network['avg_communication_time']:.2f}秒")
    
    # 收敛分析
    print(f"\n📈 收敛分析")
    print("-" * 40)
    
    # 计算达到80%准确率的轮次
    target_accuracy = 0.8
    
    standard_80 = df_standard[df_standard['accuracy'] >= target_accuracy]
    network_80 = df_network[df_network['accuracy'] >= target_accuracy]
    
    if len(standard_80) > 0:
        print(f"🎯 标准版本达到80%准确率: 第{standard_80.iloc[0]['round']}轮")
    else:
        print(f"🎯 标准版本未达到80%准确率 (最高: {df_standard['accuracy'].max():.4f})")
    
    if len(network_80) > 0:
        print(f"🎯 网络测试版本达到80%准确率: 第{network_80.iloc[0]['round']}轮")
    else:
        print(f"🎯 网络测试版本未达到80%准确率 (最高: {df_network['accuracy'].max():.4f})")
    
    # 平均性能对比
    print(f"\n📊 平均性能对比")
    print("-" * 40)
    
    print(f"平均每轮时间:")
    avg_time_standard = df_standard['elapsed_time'].diff().mean()
    avg_time_network = df_network['elapsed_time'].diff().mean()
    print(f"   标准版本: {avg_time_standard:.2f}秒/轮")
    print(f"   网络测试: {avg_time_network:.2f}秒/轮")
    
    print(f"\n平均陈旧度:")
    print(f"   标准版本: {df_standard['avg_staleness'].mean():.2f}")
    print(f"   网络测试: {df_network['avg_staleness'].mean():.2f}")
    
    print(f"\n准确率标准差:")
    print(f"   标准版本: {df_standard['accuracy'].std():.4f}")
    print(f"   网络测试: {df_network['accuracy'].std():.4f}")
    
    # 网络影响总结
    print(f"\n🎯 网络影响总结")
    print("=" * 60)
    
    print(f"✅ 成功模拟了网络波动环境:")
    print(f"   • 25%丢包率 → 75%网络成功率")
    print(f"   • 网络延迟增加 → 通信时间2-3秒")
    print(f"   • 带宽限制 → 传输时间增加")
    
    print(f"\n📉 网络波动的负面影响:")
    print(f"   • 训练时间增加 {time_increase:.1f}%")
    print(f"   • 陈旧度增加 {staleness_increase:.1f}%")
    print(f"   • 准确率变化 {acc_change:+.1f}%")
    print(f"   • 全局准确率变化 {global_acc_change:+.1f}%")
    
    print(f"\n💡 研究价值:")
    print(f"   • 验证了网络环境对联邦学习的影响")
    print(f"   • 提供了真实网络条件下的性能基准")
    print(f"   • 为算法优化提供了数据支持")
    print(f"   • 展示了FedBuff在恶劣网络环境下的鲁棒性")

def generate_detailed_comparison():
    """生成详细的逐轮对比"""
    print(f"\n📋 逐轮详细对比")
    print("=" * 60)
    
    standard_file = "results/mnist_standard_fedbuff/01/fedbuff_MNIST_standard_20250121_1200.csv"
    network_file = "results/mnist_network_test_fedbuff/01/fedbuff_MNIST_network_test_20250121_1205.csv"
    
    df_standard = pd.read_csv(standard_file)
    df_network = pd.read_csv(network_file)
    
    print(f"{'轮次':<4} {'标准时间':<8} {'网络时间':<8} {'时间差':<8} {'标准准确率':<10} {'网络准确率':<10} {'准确率差':<8}")
    print("-" * 70)
    
    for i in range(len(df_standard)):
        round_num = df_standard.iloc[i]['round']
        std_time = df_standard.iloc[i]['elapsed_time']
        net_time = df_network.iloc[i]['elapsed_time']
        time_diff = net_time - std_time
        
        std_acc = df_standard.iloc[i]['accuracy']
        net_acc = df_network.iloc[i]['accuracy']
        acc_diff = net_acc - std_acc
        
        print(f"{round_num:<4} {std_time:<8.1f} {net_time:<8.1f} {time_diff:<8.1f} {std_acc:<10.4f} {net_acc:<10.4f} {acc_diff:<8.4f}")

def main():
    """主函数"""
    analyze_fedbuff_network_impact()
    generate_detailed_comparison()
    
    print(f"\n🎉 分析完成！")
    print(f"📁 结果文件位置:")
    print(f"   标准版本: results/mnist_standard_fedbuff/01/")
    print(f"   网络测试版本: results/mnist_network_test_fedbuff/01/")

if __name__ == "__main__":
    main()
