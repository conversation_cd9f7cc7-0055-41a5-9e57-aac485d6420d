#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SC_AFL CIFAR10+ResNet9 训练启动脚本
"""

import os
import sys
import logging
import time

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../../"))
sys.path.insert(0, project_root)

def setup_logging():
    """设置日志配置"""
    # 确保logs目录存在
    logs_dir = os.path.join(current_dir, "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # 设置日志文件路径
    log_file = os.path.join(logs_dir, f"sc_afl_run_{time.strftime('%Y%m%d_%H%M%S')}.log")

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    print(f"📝 日志文件: {log_file}")
    return log_file

def main():
    """主函数"""
    print("🚀 启动完整修复的SC_AFL训练 - CIFAR10 + ResNet9 (包含网络模拟)")
    print("=" * 70)
    print("🔧 主要修复内容:")
    print("  ✅ 修复单客户端聚合问题 - 设置min_clients_for_aggregation=2")
    print("  ✅ 添加虚拟时间记录功能 - 类似FADAS的时间记录方式")
    print("  ✅ 优化客户端选择算法 - 确保选择多个客户端")
    print("  ✅ 删除冗余函数 - 清理重复代码")
    print("  ✅ 增强结果记录 - 添加virtual_time和aggregated_clients_count字段")
    print("  ✅ 更新配置文件 - 确保CSV正确记录所有字段")
    print("=" * 70)

    # 设置日志
    log_file = setup_logging()

    # 设置配置文件 - 使用包含网络模拟的配置
    config_file = os.path.join(current_dir, "sc_afl_cifar10_resnet9_with_network.yml")

    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False

    print(f"📋 使用配置文件: {config_file}")
    print(f"📝 预期修复效果:")
    print(f"  - 每轮应聚合2个或更多客户端（而非1个）")
    print(f"  - 平均、最大、最小陈旧度应有差异（而非完全一致）")
    print(f"  - 结果文件应包含virtual_time和aggregated_clients_count字段")
    print(f"  - 虚拟时间应随训练轮次递增")
    print("=" * 70)

    # 设置命令行参数来指定配置文件
    import sys
    sys.argv = ['run_scafl_cifar10_resnet9.py', '--config', config_file]

    try:
        # 直接导入并运行SC_AFL组件
        from sc_afl_server import Server
        from sc_afl_client import Client
        from dynamic_loader import DynamicModelLoader, ConfigurationManager
        from plato.config import Config

        print("🎯 开始训练...")
        logging.info("SC_AFL训练开始")

        # 加载配置
        config = Config()

        # 创建动态组件
        dataset_name, num_classes, in_channels = ConfigurationManager.get_dataset_info_from_config(config)
        model_name = ConfigurationManager.get_model_info_from_config(config)
        model = DynamicModelLoader.create_model(model_name, num_classes, in_channels)

        logging.info(f"创建模型: {model_name}, 数据集: {dataset_name}")

        # 创建服务器
        server = Server(model=model)

        # 运行训练（异步）
        import asyncio
        asyncio.run(server.run())

        print("✅ 训练完成")
        logging.info("SC_AFL训练完成")
        return True

    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
        logging.warning("训练被用户中断")
        return False
    except Exception as e:
        print(f"❌ 训练过程中出错: {e}")
        logging.error(f"训练过程中出错: {e}")
        import traceback
        traceback.print_exc()
        logging.error(f"异常堆栈: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
