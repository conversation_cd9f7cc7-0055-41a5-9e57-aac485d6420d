clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 30  # 增加客户端数量但减少参与度

    # The number of clients selected in each round
    per_round: 2  # 很少的客户端参与

    # Should the clients compute test accuracy locally?
    do_test: true

    # Should the clients compute test accuracy with global model?
    do_global_test: true

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 0.3  # 极端不均匀

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 20

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 15

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8009
    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server?
    simulate_wall_time: true

    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 1

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 15  # 很高的陈旧度容忍

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: false  # 不发送紧急通知

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/mnist/poor_data
    model_path: models/mnist/poor_data

    random_seed: 1

    # (FADAS) - 极差的优化参数
    beta1: 0.1
    beta2: 0.5
    tauc: 10
    eps: 0.01
    global_lr: 0.0001  # 极低的全局学习率

algorithm:
    # Aggregation algorithm
    type: fedavg

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition - 极少的数据
    partition_size: 50

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution(alpha)
    concentration: 0.05  # 很强的非IID，但不至于导致错误

    # The size of the testset on the server
    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

    # get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 15  # 更多轮次但效果更差

    # The maximum number of clients running concurrently
    max_concurrency: 1

    # The target accuracy
    target_accuracy: 1

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 4  # 极小批次
    optimizer: SGD
    lr_scheduler: LambdaLR

    # The machine learning model
    model_name: lenet5

parameters:
    model:
        num_classes: 10
        in_channels: 1
    
    optimizer:
        lr: 0.0001  # 极低学习率
        momentum: 0.01  # 极低动量
        weight_decay: 0.1  # 极高权重衰减

    learning_rate:
        gamma: 0.1  # 激进衰减
        milestone_steps: 5ep,10ep

results:
    result_path: results/mnist/poor_data

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std
