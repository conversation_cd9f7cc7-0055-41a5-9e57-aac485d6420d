"""
FedSCAFL函数直接调试工具 - 独立测试各个函数
这个文件通过直接测试函数逻辑，而不依赖完整的框架初始化
"""
import logging
import time
import random
import numpy as np
from types import SimpleNamespace
import sys
import os

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('function_debug.log')
    ]
)
logger = logging.getLogger("FedSCAFL.Debug")

def test_get_dkt():
    """直接测试计算过时度的核心逻辑"""
    logger.info("测试get_dkt函数核心逻辑")
    
    # 模拟所需的变量
    H_k = 5.0  # 客户端总训练时间
    tau_k_t = 2  # 客户端过时轮数
    historical_Ds = [0.5, 0.6, 0.7]  # 历史聚合时间
    
    t = len(historical_Ds)
    start_index = max(0, t - tau_k_t)
    end_index = t - 1
    sum_Dj = sum(historical_Ds[start_index:end_index + 1]) if historical_Ds else 0
    d_k_t = max(H_k - sum_Dj, 0)
    
    logger.info("计算过时度: H_k=%.4f, tau_k_t=%d, sum_Dj=%.4f, d_k_t=%.4f", 
                H_k, tau_k_t, sum_Dj, d_k_t)
    return d_k_t

def test_estimate_client_times():
    """测试客户端时间估计逻辑"""
    logger.info("测试客户端时间估计逻辑")
    
    cid = 123
    a = 0.5  # 计算时间基线
    mu = 1.0
    param_count = 100000  # 模型参数量
    # 距离分布更广，模拟异构性
    distance = np.random.uniform(0.5, 3.0)  # 单位: km

    # 计算时间估计
    import math
    H_comp = a + np.random.exponential(1/mu)

    # 通信参数
    B = np.random.uniform(50e3, 500e3)  # 带宽: 50~500kHz
    P = 10 ** ((10 - 30) / 10)  # 发射功率 (W)
    N0 = np.random.uniform(1e-15, 1e-13)  # 噪声功率谱密度 (W/Hz)
    L = 10 ** (3.5 * math.log10(distance))  # 路径损耗
    SNR = P / (N0 * B * L)  # 信噪比

    # 信道容量
    from math import log2
    C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps

    # 模型大小（100K参数，32位）
    model_size = param_count * 32  # bits

    # 基础延迟（如协议、排队、接入等）
    latency = np.random.uniform(0.05, 0.3)  # 50~300ms

    # 通信时间 = 传输时延 + 基础延迟
    H_comm = model_size / C + latency
    # 控制在0.2~2s区间
    H_comm = np.clip(H_comm, 0.2, 2.0)

    # 总时间估计
    H_k = H_comp + H_comm
    logger.debug("估计客户端%s训练+通信时间: %f秒 (计算: %f秒, 通信: %f秒, 距离: %.2fkm, 带宽: %.0fHz, SNR: %.2e)",
                 cid, H_k, H_comp, H_comm, distance, B, SNR)
    
    return {
        "cid": cid,
        "H_comp": H_comp,
        "H_comm": H_comm,
        "H_k": H_k,
        "distance": distance,
        "bandwidth": B,
        "SNR": SNR
    }

def test_clients_selection():
    """测试客户端选择算法核心逻辑"""
    logger.info("测试客户端选择算法")
    
    # 模拟客户端数据
    N_t = [1, 2, 3, 4, 5]
    M_max = 3
    V = 10  # 权衡参数
    tau_max = 10  # 最大过时容忍度
    
    # 为客户端创建必要的属性
    client_Hk_comm = {}
    client_staleness = {}
    client_weights = {}
    client_completion_time = {}
    
    for cid in N_t:
        client_Hk_comm[cid] = random.uniform(0.2, 1.0)  # 通信时间
        client_staleness[cid] = random.randint(0, 5)  # 过时程度
        client_weights[cid] = 1.0  # 权重
        client_completion_time[cid] = random.uniform(1.0, 5.0)  # 完成时间
    
    # 按完成时间排序
    N_t_sorted = sorted(N_t, key=lambda cid: client_completion_time.get(cid, 0))
    logger.debug("客户端按完成时间排序: %s", N_t_sorted)
    
    # 应用SCAFL客户端选择算法
    M_t_star = []
    selected_ids = set()
    D_t = 0
    Q_k_sum = 0
    
    # 输出初始客户端状态
    for cid in N_t:
        logger.debug("客户端%s: 通信时间=%.2f, 过时程度=%d", 
                   cid, client_Hk_comm[cid], client_staleness[cid])
    
    # 选择客户端
    for i in range(M_max):
        best_delta_S = float('inf')
        best_client = None

        for cid in N_t_sorted:
            if cid in selected_ids:
                continue

            tau_k = client_staleness.get(cid, 0)
            alpha_k_t = client_weights.get(cid, 1.0)
            Q_k = 1
            Q_term = Q_k * ((tau_k + 1) * (1 - alpha_k_t) - tau_max)

            comm_time = client_Hk_comm.get(cid, 0)
            temp_D = max(D_t, comm_time)
            temp_S = V * temp_D + (Q_k_sum + Q_term)
            delta_S = temp_S - (V * D_t + Q_k_sum)
            
            logger.debug("评估客户端%s: tau_k=%d, Q_term=%.2f, temp_D=%.2f, delta_S=%.2f", 
                       cid, tau_k, Q_term, temp_D, delta_S)

            if delta_S < best_delta_S:
                best_delta_S = delta_S
                best_client = cid

        if best_client is not None:
            M_t_star.append(best_client)
            selected_ids.add(best_client)
            D_t = max(D_t, client_Hk_comm.get(best_client, 0))
            tau_k = client_staleness.get(best_client, 0)
            alpha_k_t = client_weights.get(best_client, 1.0)
            Q_k = 1
            Q_k_sum += Q_k * ((tau_k + 1) * (1 - alpha_k_t) - tau_max)
            min_S_M_t = V * D_t + Q_k_sum
            logger.debug("第%d轮选择: 选中客户端%s，当前D_t=%.4f，Q_k_sum=%.4f", 
                       i+1, best_client, D_t, Q_k_sum)

    logger.info("SCAFL算法选择了%d个客户端: %s", len(M_t_star), M_t_star)
    return M_t_star

def run_all_tests():
    """运行所有测试函数"""
    logger.info("========== 开始SCAFL函数核心逻辑测试 ==========")
    
    tests = [
        ("过时度计算函数", test_get_dkt),
        ("客户端时间估计函数", test_estimate_client_times),
        ("客户端选择算法", test_clients_selection)
    ]
    
    for name, func in tests:
        logger.info("\n----- 测试: %s -----", name)
        start_time = time.time()
        try:
            result = func()
            logger.info("函数执行结果: %s", result)
            success = True
        except Exception as e:
            logger.error("函数执行失败: %s", str(e), exc_info=True)
            success = False
        
        elapsed = time.time() - start_time
        status = "✓ 通过" if success else "✗ 失败"
        logger.info("%s: %s (耗时: %.2f秒)", name, status, elapsed)
    
    logger.info("\n========== 测试结束 ==========")

if __name__ == "__main__":
    run_all_tests() 