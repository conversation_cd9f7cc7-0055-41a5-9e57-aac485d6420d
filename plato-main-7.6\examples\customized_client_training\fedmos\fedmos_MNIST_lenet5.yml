clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 1000

    # The number of clients selected in each round
    per_round: 10

    # Should the clients compute test accuracy locally?
    do_test: false

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8000
    synchronous: true

    checkpoint_path: models/fedmos/mnist
    model_path: models/fedmos/mnist

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 600

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution
    concentration: 5

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 10

    # The maximum number of clients running concurrently
    max_concurrency: 3

    # The target accuracy
    target_accuracy: 0.94

    # Number of epochs for local training in each communication round
    epochs: 20
    batch_size: 10
    optimizer: SGD

    # The machine learning model
    model_name: lenet5

algorithm:
    # Aggregation algorithm
    type: fedavg
    a: 0.1
    mu: 0.001
    
parameters:
    optimizer:
        lr: 0.03
        momentum: 0.0 # learning rate is fixed as in Appendix C.2
        weight_decay: 0.0
