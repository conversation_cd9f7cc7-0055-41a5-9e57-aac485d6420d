2025-08-01 10:06:43,974 - INFO - 🚀 SC-AFL服务器启动 - 2025-08-01 10:06:43
2025-08-01 10:06:43,974 - INFO - ✅ 新日志文件已创建
2025-08-01 10:06:43,974 - INFO - 📁 日志目录: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs
2025-08-01 10:06:43,974 - INFO - 📄 日志文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs\sc_afl_server_20250801_100643_540.log
2025-08-01 10:06:43,974 - INFO - 🔧 日志级别: DEBUG (文件), INFO (控制台)
2025-08-01 10:06:43,974 - INFO - 📊 文件模式: 新建模式 (每次启动创建新文件)
2025-08-01 10:06:43,974 - INFO - 🆔 进程ID: 540
2025-08-01 10:06:43,974 - INFO - ✅ 日志文件创建成功，当前大小: 671 字节
2025-08-01 10:06:43,974 - INFO - 🚀 SC-AFL服务器初始化开始...
2025-08-01 10:06:43,974 - INFO - 📅 初始化时间: 2025-08-01 10:06:43
2025-08-01 10:06:43,991 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:43,991 - INFO - Server: 动态创建模型 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 10:06:43,991 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:43,998 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:44,000 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:44,000 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:44,000 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:44,001 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:44,001 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:44,001 - INFO - Server: 创建了新的Trainer实例
2025-08-01 10:06:44,001 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:44,001 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:44,001 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:44,001 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:44,001 - INFO - Server: 创建了新的Algorithm实例
2025-08-01 10:06:44,002 - INFO - [93m[1m[540] Logging runtime results to: ./results/cifar10_with_network/540.csv.[0m
2025-08-01 10:06:44,002 - INFO - [Server #540] Started training on 10 clients with 3 per round.
2025-08-01 10:06:44,002 - INFO - [DEBUG] 从配置文件读取 simulate_wall_time=True
2025-08-01 10:06:44,002 - WARNING - Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。
2025-08-01 10:06:44,002 - WARNING - Server: 训练器在初始化后为None，重新创建
2025-08-01 10:06:44,002 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:44,002 - WARNING - [Trainer None] 模型为None，尝试创建默认模型
2025-08-01 10:06:44,018 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:44,018 - INFO - [Trainer None] 动态创建模型 resnet_9，输入通道: 3, 类别数: 10
2025-08-01 10:06:44,023 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:44,024 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:44,024 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:44,024 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:44,024 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:44,025 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:44,025 - INFO - Server: 重新创建了Trainer实例
2025-08-01 10:06:44,025 - INFO - [Algorithm] 已设置服务器引用 (客户端ID: None)
2025-08-01 10:06:44,025 - INFO - Server: 算法类已设置服务器引用
2025-08-01 10:06:44,025 - INFO - 动态加载数据集: CIFAR10
2025-08-01 10:06:44,604 - INFO - ✅ 成功加载数据集 CIFAR10: 10000 样本
2025-08-01 10:06:44,604 - INFO - ✅ 动态加载测试集成功: CIFAR10, 大小: 10000
2025-08-01 10:06:44,605 - INFO - ✅ 测试加载器已创建
2025-08-01 10:06:44,605 - INFO - 开始初始化全局模型权重
2025-08-01 10:06:44,605 - WARNING - 全局模型实例为None，正在尝试重新创建...
2025-08-01 10:06:44,618 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:44,618 - INFO - 成功重新创建了全局模型实例 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 10:06:44,624 - INFO - [全局权重摘要] 参数数量: 74, 均值: 0.001175, 最大: 1.000000, 最小: -0.192305
2025-08-01 10:06:44,624 - INFO - [全局模型] 输入通道数: 3
2025-08-01 10:06:44,624 - INFO - 全局模型权重初始化成功
2025-08-01 10:06:44,625 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:06:44,625 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 10:06:44,627 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 10:06:44,627 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 10:06:44,627 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_100644.csv
2025-08-01 10:06:44,627 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 10:06:44,628 - WARNING - 网络模拟器初始化失败: 'Config' object has no attribute 'get'
2025-08-01 10:06:44,628 - INFO - SC-AFL算法参数: tau_max=5, V=1.0
2025-08-01 10:06:44,628 - INFO - 服务器初始化完成
2025-08-01 10:06:44,628 - INFO - 已创建并注册 0 个客户端（将在 Server.start() 中启动任务）
2025-08-01 10:06:44,628 - INFO - 客户端ID管理器初始化完成，总客户端数: 10, ID起始值: 1
2025-08-01 10:06:44,628 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 10:06:44,628 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 10:06:44,629 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 10:06:44,629 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_100644.csv
2025-08-01 10:06:44,629 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 10:06:44,629 - INFO - 服务器实例创建成功
2025-08-01 10:06:44,629 - INFO - 正在创建和注册 10 个客户端...
2025-08-01 10:06:44,629 - INFO - 客户端ID配置: 起始ID=1, 总数=10
2025-08-01 10:06:44,631 - INFO - 开始创建客户端 1...
2025-08-01 10:06:44,632 - INFO - 初始化客户端, ID: 1
2025-08-01 10:06:44,646 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:44,647 - INFO - [Client 1] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:44,647 - INFO - [Trainer] 初始化训练器, client_id: 1
2025-08-01 10:06:44,654 - INFO - [Trainer 1] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:44,655 - INFO - [Trainer 1] 模型的输入通道数: 3
2025-08-01 10:06:44,655 - INFO - [Trainer 1] 强制使用CPU
2025-08-01 10:06:44,655 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 10:06:44,655 - INFO - [Trainer 1] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:44,656 - INFO - [Trainer 1] 初始化完成
2025-08-01 10:06:44,656 - INFO - [Client 1] 创建新训练器
2025-08-01 10:06:44,656 - INFO - [Algorithm] 从训练器获取客户端ID: 1
2025-08-01 10:06:44,656 - INFO - [Algorithm] 初始化后修正client_id: 1
2025-08-01 10:06:44,656 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:44,656 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:44,656 - INFO - [Client 1] 创建新算法
2025-08-01 10:06:44,656 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 10:06:44,656 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 10:06:44,656 - INFO - [Client None] 父类初始化完成
2025-08-01 10:06:44,672 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:44,672 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:44,672 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:44,679 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:44,679 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:44,680 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:44,680 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:44,680 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:44,680 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:44,680 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:06:44,680 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:44,681 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:44,681 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:44,681 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:44,681 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:06:44,681 - INFO - [Client None] 开始加载数据
2025-08-01 10:06:44,681 - INFO - 顺序分配客户端ID: 1
2025-08-01 10:06:44,681 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 1
2025-08-01 10:06:44,682 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:06:44,682 - WARNING - [Client 1] 数据源为None，已创建新数据源
2025-08-01 10:06:44,682 - WARNING - [Client 1] 数据源trainset为None，已设置为空列表
2025-08-01 10:06:45,279 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:06:45,279 - INFO - [Client 1] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:06:45,279 - INFO - [Client 1] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:06:45,283 - INFO - [Client 1] 成功划分数据集，分配到 300 个样本
2025-08-01 10:06:45,284 - INFO - [Client 1] 初始化时成功加载数据
2025-08-01 10:06:45,284 - INFO - [客户端 1] 初始化验证通过
2025-08-01 10:06:45,284 - INFO - 客户端 1 实例创建成功
2025-08-01 10:06:45,284 - INFO - 客户端1已设置服务器引用
2025-08-01 10:06:45,284 - INFO - 客户端 1 已设置服务器引用
2025-08-01 10:06:45,284 - INFO - 客户端1已注册
2025-08-01 10:06:45,285 - INFO - 客户端 1 已成功注册到服务器
2025-08-01 10:06:45,285 - INFO - 开始创建客户端 2...
2025-08-01 10:06:45,285 - INFO - 初始化客户端, ID: 2
2025-08-01 10:06:45,302 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:45,302 - INFO - [Client 2] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:45,303 - INFO - [Trainer] 初始化训练器, client_id: 2
2025-08-01 10:06:45,310 - INFO - [Trainer 2] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:45,310 - INFO - [Trainer 2] 模型的输入通道数: 3
2025-08-01 10:06:45,310 - INFO - [Trainer 2] 强制使用CPU
2025-08-01 10:06:45,311 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 10:06:45,311 - INFO - [Trainer 2] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:45,311 - INFO - [Trainer 2] 初始化完成
2025-08-01 10:06:45,311 - INFO - [Client 2] 创建新训练器
2025-08-01 10:06:45,311 - INFO - [Algorithm] 从训练器获取客户端ID: 2
2025-08-01 10:06:45,311 - INFO - [Algorithm] 初始化后修正client_id: 2
2025-08-01 10:06:45,312 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:45,312 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:45,312 - INFO - [Client 2] 创建新算法
2025-08-01 10:06:45,312 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 10:06:45,312 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 10:06:45,312 - INFO - [Client None] 父类初始化完成
2025-08-01 10:06:45,329 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:45,330 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:45,330 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:45,337 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:45,338 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:45,338 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:45,338 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:45,339 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:45,339 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:45,339 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:06:45,339 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:45,339 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:45,340 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:45,340 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:45,340 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:06:45,340 - INFO - [Client None] 开始加载数据
2025-08-01 10:06:45,340 - INFO - 顺序分配客户端ID: 2
2025-08-01 10:06:45,340 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 2
2025-08-01 10:06:45,341 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:06:45,341 - WARNING - [Client 2] 数据源为None，已创建新数据源
2025-08-01 10:06:45,341 - WARNING - [Client 2] 数据源trainset为None，已设置为空列表
2025-08-01 10:06:45,943 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:06:45,944 - INFO - [Client 2] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:06:45,944 - INFO - [Client 2] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:06:45,949 - INFO - [Client 2] 成功划分数据集，分配到 300 个样本
2025-08-01 10:06:45,949 - INFO - [Client 2] 初始化时成功加载数据
2025-08-01 10:06:45,949 - INFO - [客户端 2] 初始化验证通过
2025-08-01 10:06:45,950 - INFO - 客户端 2 实例创建成功
2025-08-01 10:06:45,950 - INFO - 客户端2已设置服务器引用
2025-08-01 10:06:45,950 - INFO - 客户端 2 已设置服务器引用
2025-08-01 10:06:45,950 - INFO - 客户端2已注册
2025-08-01 10:06:45,950 - INFO - 客户端 2 已成功注册到服务器
2025-08-01 10:06:45,950 - INFO - 开始创建客户端 3...
2025-08-01 10:06:45,950 - INFO - 初始化客户端, ID: 3
2025-08-01 10:06:45,965 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:45,965 - INFO - [Client 3] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:45,965 - INFO - [Trainer] 初始化训练器, client_id: 3
2025-08-01 10:06:45,972 - INFO - [Trainer 3] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:45,972 - INFO - [Trainer 3] 模型的输入通道数: 3
2025-08-01 10:06:45,972 - INFO - [Trainer 3] 强制使用CPU
2025-08-01 10:06:45,972 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:06:45,972 - INFO - [Trainer 3] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:45,973 - INFO - [Trainer 3] 初始化完成
2025-08-01 10:06:45,973 - INFO - [Client 3] 创建新训练器
2025-08-01 10:06:45,973 - INFO - [Algorithm] 从训练器获取客户端ID: 3
2025-08-01 10:06:45,973 - INFO - [Algorithm] 初始化后修正client_id: 3
2025-08-01 10:06:45,973 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:45,973 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:45,973 - INFO - [Client 3] 创建新算法
2025-08-01 10:06:45,974 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 10:06:45,974 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 10:06:45,974 - INFO - [Client None] 父类初始化完成
2025-08-01 10:06:45,989 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:45,989 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:45,989 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:45,995 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:45,995 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:45,996 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:45,996 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:45,996 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:45,996 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:45,996 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:06:45,997 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:45,997 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:45,997 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:45,997 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:45,997 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:06:45,997 - INFO - [Client None] 开始加载数据
2025-08-01 10:06:45,998 - INFO - 顺序分配客户端ID: 3
2025-08-01 10:06:45,998 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 3
2025-08-01 10:06:45,998 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:06:45,998 - WARNING - [Client 3] 数据源为None，已创建新数据源
2025-08-01 10:06:45,998 - WARNING - [Client 3] 数据源trainset为None，已设置为空列表
2025-08-01 10:06:46,593 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:06:46,594 - INFO - [Client 3] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:06:46,594 - INFO - [Client 3] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:06:46,598 - INFO - [Client 3] 成功划分数据集，分配到 300 个样本
2025-08-01 10:06:46,598 - INFO - [Client 3] 初始化时成功加载数据
2025-08-01 10:06:46,598 - INFO - [客户端 3] 初始化验证通过
2025-08-01 10:06:46,599 - INFO - 客户端 3 实例创建成功
2025-08-01 10:06:46,599 - INFO - 客户端3已设置服务器引用
2025-08-01 10:06:46,599 - INFO - 客户端 3 已设置服务器引用
2025-08-01 10:06:46,599 - INFO - 客户端3已注册
2025-08-01 10:06:46,599 - INFO - 客户端 3 已成功注册到服务器
2025-08-01 10:06:46,599 - INFO - 开始创建客户端 4...
2025-08-01 10:06:46,599 - INFO - 初始化客户端, ID: 4
2025-08-01 10:06:46,615 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:46,615 - INFO - [Client 4] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:46,615 - INFO - [Trainer] 初始化训练器, client_id: 4
2025-08-01 10:06:46,621 - INFO - [Trainer 4] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:46,622 - INFO - [Trainer 4] 模型的输入通道数: 3
2025-08-01 10:06:46,622 - INFO - [Trainer 4] 强制使用CPU
2025-08-01 10:06:46,622 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:06:46,622 - INFO - [Trainer 4] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:46,622 - INFO - [Trainer 4] 初始化完成
2025-08-01 10:06:46,622 - INFO - [Client 4] 创建新训练器
2025-08-01 10:06:46,623 - INFO - [Algorithm] 从训练器获取客户端ID: 4
2025-08-01 10:06:46,623 - INFO - [Algorithm] 初始化后修正client_id: 4
2025-08-01 10:06:46,623 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:46,623 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:46,623 - INFO - [Client 4] 创建新算法
2025-08-01 10:06:46,623 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 10:06:46,623 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 10:06:46,623 - INFO - [Client None] 父类初始化完成
2025-08-01 10:06:46,638 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:46,638 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:46,639 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:46,644 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:46,644 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:46,644 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:46,644 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:46,645 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:46,645 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:46,645 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:06:46,645 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:46,645 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:46,645 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:46,645 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:46,645 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:06:46,645 - INFO - [Client None] 开始加载数据
2025-08-01 10:06:46,645 - INFO - 顺序分配客户端ID: 4
2025-08-01 10:06:46,646 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 4
2025-08-01 10:06:46,646 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:06:46,646 - WARNING - [Client 4] 数据源为None，已创建新数据源
2025-08-01 10:06:46,646 - WARNING - [Client 4] 数据源trainset为None，已设置为空列表
2025-08-01 10:06:47,243 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:06:47,243 - INFO - [Client 4] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:06:47,243 - INFO - [Client 4] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:06:47,247 - INFO - [Client 4] 成功划分数据集，分配到 300 个样本
2025-08-01 10:06:47,247 - INFO - [Client 4] 初始化时成功加载数据
2025-08-01 10:06:47,247 - INFO - [客户端 4] 初始化验证通过
2025-08-01 10:06:47,247 - INFO - 客户端 4 实例创建成功
2025-08-01 10:06:47,248 - INFO - 客户端4已设置服务器引用
2025-08-01 10:06:47,248 - INFO - 客户端 4 已设置服务器引用
2025-08-01 10:06:47,248 - INFO - 客户端4已注册
2025-08-01 10:06:47,248 - INFO - 客户端 4 已成功注册到服务器
2025-08-01 10:06:47,248 - INFO - 开始创建客户端 5...
2025-08-01 10:06:47,248 - INFO - 初始化客户端, ID: 5
2025-08-01 10:06:47,263 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:47,263 - INFO - [Client 5] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:47,263 - INFO - [Trainer] 初始化训练器, client_id: 5
2025-08-01 10:06:47,268 - INFO - [Trainer 5] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:47,268 - INFO - [Trainer 5] 模型的输入通道数: 3
2025-08-01 10:06:47,268 - INFO - [Trainer 5] 强制使用CPU
2025-08-01 10:06:47,270 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 10:06:47,270 - INFO - [Trainer 5] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:47,270 - INFO - [Trainer 5] 初始化完成
2025-08-01 10:06:47,270 - INFO - [Client 5] 创建新训练器
2025-08-01 10:06:47,270 - INFO - [Algorithm] 从训练器获取客户端ID: 5
2025-08-01 10:06:47,270 - INFO - [Algorithm] 初始化后修正client_id: 5
2025-08-01 10:06:47,270 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:47,271 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:47,271 - INFO - [Client 5] 创建新算法
2025-08-01 10:06:47,271 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 10:06:47,271 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 10:06:47,271 - INFO - [Client None] 父类初始化完成
2025-08-01 10:06:47,286 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:47,286 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:47,286 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:47,291 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:47,293 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:47,293 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:47,293 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:47,293 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:47,293 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:47,294 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:06:47,294 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:47,294 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:47,294 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:47,294 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:47,294 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:06:47,294 - INFO - [Client None] 开始加载数据
2025-08-01 10:06:47,294 - INFO - 顺序分配客户端ID: 5
2025-08-01 10:06:47,294 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 5
2025-08-01 10:06:47,295 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:06:47,295 - WARNING - [Client 5] 数据源为None，已创建新数据源
2025-08-01 10:06:47,295 - WARNING - [Client 5] 数据源trainset为None，已设置为空列表
2025-08-01 10:06:47,888 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:06:47,889 - INFO - [Client 5] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:06:47,889 - INFO - [Client 5] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:06:47,893 - INFO - [Client 5] 成功划分数据集，分配到 300 个样本
2025-08-01 10:06:47,893 - INFO - [Client 5] 初始化时成功加载数据
2025-08-01 10:06:47,893 - INFO - [客户端 5] 初始化验证通过
2025-08-01 10:06:47,893 - INFO - 客户端 5 实例创建成功
2025-08-01 10:06:47,893 - INFO - 客户端5已设置服务器引用
2025-08-01 10:06:47,893 - INFO - 客户端 5 已设置服务器引用
2025-08-01 10:06:47,894 - INFO - 客户端5已注册
2025-08-01 10:06:47,894 - INFO - 客户端 5 已成功注册到服务器
2025-08-01 10:06:47,894 - INFO - 开始创建客户端 6...
2025-08-01 10:06:47,894 - INFO - 初始化客户端, ID: 6
2025-08-01 10:06:47,910 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:47,910 - INFO - [Client 6] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:47,911 - INFO - [Trainer] 初始化训练器, client_id: 6
2025-08-01 10:06:48,024 - INFO - [Trainer 6] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:48,024 - INFO - [Trainer 6] 模型的输入通道数: 3
2025-08-01 10:06:48,024 - INFO - [Trainer 6] 强制使用CPU
2025-08-01 10:06:48,025 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 10:06:48,025 - INFO - [Trainer 6] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:48,025 - INFO - [Trainer 6] 初始化完成
2025-08-01 10:06:48,026 - INFO - [Client 6] 创建新训练器
2025-08-01 10:06:48,026 - INFO - [Algorithm] 从训练器获取客户端ID: 6
2025-08-01 10:06:48,026 - INFO - [Algorithm] 初始化后修正client_id: 6
2025-08-01 10:06:48,026 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:48,026 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:48,026 - INFO - [Client 6] 创建新算法
2025-08-01 10:06:48,026 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 10:06:48,026 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 10:06:48,027 - INFO - [Client None] 父类初始化完成
2025-08-01 10:06:48,041 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:48,041 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:48,041 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:48,048 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:48,049 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:48,049 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:48,049 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:48,050 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:48,050 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:48,050 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:06:48,050 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:48,050 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:48,050 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:48,050 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:48,050 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:06:48,050 - INFO - [Client None] 开始加载数据
2025-08-01 10:06:48,050 - INFO - 顺序分配客户端ID: 6
2025-08-01 10:06:48,050 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 6
2025-08-01 10:06:48,051 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:06:48,051 - WARNING - [Client 6] 数据源为None，已创建新数据源
2025-08-01 10:06:48,051 - WARNING - [Client 6] 数据源trainset为None，已设置为空列表
2025-08-01 10:06:48,643 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:06:48,643 - INFO - [Client 6] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:06:48,643 - INFO - [Client 6] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:06:48,647 - INFO - [Client 6] 成功划分数据集，分配到 300 个样本
2025-08-01 10:06:48,647 - INFO - [Client 6] 初始化时成功加载数据
2025-08-01 10:06:48,647 - INFO - [客户端 6] 初始化验证通过
2025-08-01 10:06:48,648 - INFO - 客户端 6 实例创建成功
2025-08-01 10:06:48,648 - INFO - 客户端6已设置服务器引用
2025-08-01 10:06:48,648 - INFO - 客户端 6 已设置服务器引用
2025-08-01 10:06:48,648 - INFO - 客户端6已注册
2025-08-01 10:06:48,648 - INFO - 客户端 6 已成功注册到服务器
2025-08-01 10:06:48,648 - INFO - 开始创建客户端 7...
2025-08-01 10:06:48,648 - INFO - 初始化客户端, ID: 7
2025-08-01 10:06:48,664 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:48,664 - INFO - [Client 7] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:48,664 - INFO - [Trainer] 初始化训练器, client_id: 7
2025-08-01 10:06:48,669 - INFO - [Trainer 7] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:48,670 - INFO - [Trainer 7] 模型的输入通道数: 3
2025-08-01 10:06:48,670 - INFO - [Trainer 7] 强制使用CPU
2025-08-01 10:06:48,670 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:06:48,670 - INFO - [Trainer 7] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:48,670 - INFO - [Trainer 7] 初始化完成
2025-08-01 10:06:48,670 - INFO - [Client 7] 创建新训练器
2025-08-01 10:06:48,671 - INFO - [Algorithm] 从训练器获取客户端ID: 7
2025-08-01 10:06:48,671 - INFO - [Algorithm] 初始化后修正client_id: 7
2025-08-01 10:06:48,671 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:48,671 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:48,671 - INFO - [Client 7] 创建新算法
2025-08-01 10:06:48,671 - INFO - [Algorithm] 设置客户端ID: 7
2025-08-01 10:06:48,671 - INFO - [Algorithm] 同步更新trainer的client_id: 7
2025-08-01 10:06:48,671 - INFO - [Client None] 父类初始化完成
2025-08-01 10:06:48,686 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:48,686 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:48,686 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:48,692 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:48,692 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:48,692 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:48,693 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:48,693 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:48,693 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:48,693 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:06:48,693 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:48,693 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:48,693 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:48,693 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:48,694 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:06:48,694 - INFO - [Client None] 开始加载数据
2025-08-01 10:06:48,694 - INFO - 顺序分配客户端ID: 7
2025-08-01 10:06:48,694 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 7
2025-08-01 10:06:48,694 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:06:48,694 - WARNING - [Client 7] 数据源为None，已创建新数据源
2025-08-01 10:06:48,694 - WARNING - [Client 7] 数据源trainset为None，已设置为空列表
2025-08-01 10:06:49,285 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:06:49,286 - INFO - [Client 7] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:06:49,286 - INFO - [Client 7] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:06:49,290 - INFO - [Client 7] 成功划分数据集，分配到 300 个样本
2025-08-01 10:06:49,290 - INFO - [Client 7] 初始化时成功加载数据
2025-08-01 10:06:49,290 - INFO - [客户端 7] 初始化验证通过
2025-08-01 10:06:49,290 - INFO - 客户端 7 实例创建成功
2025-08-01 10:06:49,290 - INFO - 客户端7已设置服务器引用
2025-08-01 10:06:49,290 - INFO - 客户端 7 已设置服务器引用
2025-08-01 10:06:49,291 - INFO - 客户端7已注册
2025-08-01 10:06:49,291 - INFO - 客户端 7 已成功注册到服务器
2025-08-01 10:06:49,291 - INFO - 开始创建客户端 8...
2025-08-01 10:06:49,291 - INFO - 初始化客户端, ID: 8
2025-08-01 10:06:49,308 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:49,308 - INFO - [Client 8] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:49,308 - INFO - [Trainer] 初始化训练器, client_id: 8
2025-08-01 10:06:49,314 - INFO - [Trainer 8] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:49,314 - INFO - [Trainer 8] 模型的输入通道数: 3
2025-08-01 10:06:49,314 - INFO - [Trainer 8] 强制使用CPU
2025-08-01 10:06:49,315 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:06:49,315 - INFO - [Trainer 8] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:49,315 - INFO - [Trainer 8] 初始化完成
2025-08-01 10:06:49,315 - INFO - [Client 8] 创建新训练器
2025-08-01 10:06:49,315 - INFO - [Algorithm] 从训练器获取客户端ID: 8
2025-08-01 10:06:49,315 - INFO - [Algorithm] 初始化后修正client_id: 8
2025-08-01 10:06:49,315 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:49,315 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:49,315 - INFO - [Client 8] 创建新算法
2025-08-01 10:06:49,315 - INFO - [Algorithm] 设置客户端ID: 8
2025-08-01 10:06:49,315 - INFO - [Algorithm] 同步更新trainer的client_id: 8
2025-08-01 10:06:49,315 - INFO - [Client None] 父类初始化完成
2025-08-01 10:06:49,331 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:49,331 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:49,332 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:49,338 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:49,338 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:49,339 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:49,339 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:49,339 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:49,339 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:49,339 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:06:49,340 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:49,340 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:49,340 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:49,340 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:49,341 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:06:49,341 - INFO - [Client None] 开始加载数据
2025-08-01 10:06:49,341 - INFO - 顺序分配客户端ID: 8
2025-08-01 10:06:49,341 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 8
2025-08-01 10:06:49,341 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:06:49,342 - WARNING - [Client 8] 数据源为None，已创建新数据源
2025-08-01 10:06:49,342 - WARNING - [Client 8] 数据源trainset为None，已设置为空列表
2025-08-01 10:06:49,938 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:06:49,939 - INFO - [Client 8] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:06:49,939 - INFO - [Client 8] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:06:49,942 - INFO - [Client 8] 成功划分数据集，分配到 300 个样本
2025-08-01 10:06:49,943 - INFO - [Client 8] 初始化时成功加载数据
2025-08-01 10:06:49,943 - INFO - [客户端 8] 初始化验证通过
2025-08-01 10:06:49,943 - INFO - 客户端 8 实例创建成功
2025-08-01 10:06:49,943 - INFO - 客户端8已设置服务器引用
2025-08-01 10:06:49,943 - INFO - 客户端 8 已设置服务器引用
2025-08-01 10:06:49,943 - INFO - 客户端8已注册
2025-08-01 10:06:49,943 - INFO - 客户端 8 已成功注册到服务器
2025-08-01 10:06:49,943 - INFO - 开始创建客户端 9...
2025-08-01 10:06:49,943 - INFO - 初始化客户端, ID: 9
2025-08-01 10:06:49,960 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:49,960 - INFO - [Client 9] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:49,960 - INFO - [Trainer] 初始化训练器, client_id: 9
2025-08-01 10:06:49,966 - INFO - [Trainer 9] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:49,967 - INFO - [Trainer 9] 模型的输入通道数: 3
2025-08-01 10:06:49,967 - INFO - [Trainer 9] 强制使用CPU
2025-08-01 10:06:49,967 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:06:49,968 - INFO - [Trainer 9] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:49,968 - INFO - [Trainer 9] 初始化完成
2025-08-01 10:06:49,968 - INFO - [Client 9] 创建新训练器
2025-08-01 10:06:49,968 - INFO - [Algorithm] 从训练器获取客户端ID: 9
2025-08-01 10:06:49,968 - INFO - [Algorithm] 初始化后修正client_id: 9
2025-08-01 10:06:49,968 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:49,969 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:49,969 - INFO - [Client 9] 创建新算法
2025-08-01 10:06:49,969 - INFO - [Algorithm] 设置客户端ID: 9
2025-08-01 10:06:49,969 - INFO - [Algorithm] 同步更新trainer的client_id: 9
2025-08-01 10:06:49,969 - INFO - [Client None] 父类初始化完成
2025-08-01 10:06:49,984 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:49,985 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:49,985 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:49,991 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:49,991 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:49,992 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:49,992 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:49,992 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:49,992 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:49,993 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:06:49,993 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:49,993 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:49,993 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:49,993 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:49,993 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:06:49,993 - INFO - [Client None] 开始加载数据
2025-08-01 10:06:49,993 - INFO - 顺序分配客户端ID: 9
2025-08-01 10:06:49,993 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 9
2025-08-01 10:06:49,994 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:06:49,994 - WARNING - [Client 9] 数据源为None，已创建新数据源
2025-08-01 10:06:49,994 - WARNING - [Client 9] 数据源trainset为None，已设置为空列表
2025-08-01 10:06:50,595 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:06:50,595 - INFO - [Client 9] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:06:50,596 - INFO - [Client 9] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:06:50,600 - INFO - [Client 9] 成功划分数据集，分配到 300 个样本
2025-08-01 10:06:50,600 - INFO - [Client 9] 初始化时成功加载数据
2025-08-01 10:06:50,600 - INFO - [客户端 9] 初始化验证通过
2025-08-01 10:06:50,600 - INFO - 客户端 9 实例创建成功
2025-08-01 10:06:50,600 - INFO - 客户端9已设置服务器引用
2025-08-01 10:06:50,600 - INFO - 客户端 9 已设置服务器引用
2025-08-01 10:06:50,600 - INFO - 客户端9已注册
2025-08-01 10:06:50,600 - INFO - 客户端 9 已成功注册到服务器
2025-08-01 10:06:50,600 - INFO - 开始创建客户端 10...
2025-08-01 10:06:50,601 - INFO - 初始化客户端, ID: 10
2025-08-01 10:06:50,617 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:50,617 - INFO - [Client 10] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:50,618 - INFO - [Trainer] 初始化训练器, client_id: 10
2025-08-01 10:06:50,624 - INFO - [Trainer 10] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:50,624 - INFO - [Trainer 10] 模型的输入通道数: 3
2025-08-01 10:06:50,624 - INFO - [Trainer 10] 强制使用CPU
2025-08-01 10:06:50,625 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:06:50,625 - INFO - [Trainer 10] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:50,625 - INFO - [Trainer 10] 初始化完成
2025-08-01 10:06:50,625 - INFO - [Client 10] 创建新训练器
2025-08-01 10:06:50,626 - INFO - [Algorithm] 从训练器获取客户端ID: 10
2025-08-01 10:06:50,626 - INFO - [Algorithm] 初始化后修正client_id: 10
2025-08-01 10:06:50,626 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:50,626 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:50,626 - INFO - [Client 10] 创建新算法
2025-08-01 10:06:50,626 - INFO - [Algorithm] 设置客户端ID: 10
2025-08-01 10:06:50,626 - INFO - [Algorithm] 同步更新trainer的client_id: 10
2025-08-01 10:06:50,626 - INFO - [Client None] 父类初始化完成
2025-08-01 10:06:50,641 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 10:06:50,641 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 10:06:50,641 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 10:06:50,648 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 10:06:50,648 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 10:06:50,648 - INFO - [Trainer None] 强制使用CPU
2025-08-01 10:06:50,648 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 10:06:50,649 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:50,649 - INFO - [Trainer None] 初始化完成
2025-08-01 10:06:50,649 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 10:06:50,649 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 10:06:50,649 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 10:06:50,650 - INFO - [Algorithm] 初始化完成
2025-08-01 10:06:50,650 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 10:06:50,650 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 10:06:50,650 - INFO - [Client None] 开始加载数据
2025-08-01 10:06:50,650 - INFO - 顺序分配客户端ID: 10
2025-08-01 10:06:50,650 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 10
2025-08-01 10:06:50,651 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 10:06:50,651 - WARNING - [Client 10] 数据源为None，已创建新数据源
2025-08-01 10:06:50,651 - WARNING - [Client 10] 数据源trainset为None，已设置为空列表
2025-08-01 10:06:51,286 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 10:06:51,287 - INFO - [Client 10] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 10:06:51,287 - INFO - [Client 10] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 10, 浓度参数: 0.1
2025-08-01 10:06:51,290 - INFO - [Client 10] 成功划分数据集，分配到 300 个样本
2025-08-01 10:06:51,290 - INFO - [Client 10] 初始化时成功加载数据
2025-08-01 10:06:51,290 - INFO - [客户端 10] 初始化验证通过
2025-08-01 10:06:51,290 - INFO - 客户端 10 实例创建成功
2025-08-01 10:06:51,290 - INFO - 客户端10已设置服务器引用
2025-08-01 10:06:51,291 - INFO - 客户端 10 已设置服务器引用
2025-08-01 10:06:51,291 - INFO - 客户端10已注册
2025-08-01 10:06:51,291 - INFO - 客户端 10 已成功注册到服务器
2025-08-01 10:06:51,291 - INFO - 已成功创建和注册 10 个客户端
2025-08-01 10:06:51,291 - INFO - 服务器属性检查:
2025-08-01 10:06:51,291 - INFO - - 客户端数量: 10
2025-08-01 10:06:51,291 - INFO - - 全局模型: 已初始化
2025-08-01 10:06:51,291 - INFO - - 算法: 已初始化
2025-08-01 10:06:51,291 - INFO - - 训练器: 已初始化
2025-08-01 10:06:51,291 - INFO - 准备启动服务器...
2025-08-01 10:06:51,292 - INFO - [Server #540] 启动中...
2025-08-01 10:06:51,292 - INFO - 服务器将使用事件循环: <ProactorEventLoop running=False closed=False debug=False>
2025-08-01 10:06:51,292 - INFO - [Client 1] 模型已放置到设备: cpu
2025-08-01 10:06:51,298 - INFO - [Client 1] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,303 - INFO - [Client 1] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,303 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 10:06:51,303 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 10:06:51,303 - INFO - [Client 1] 已更新algorithm的client_id
2025-08-01 10:06:51,303 - INFO - [Client 1] 模型初始化完成
2025-08-01 10:06:51,303 - INFO - 客户端 1 模型初始化成功
2025-08-01 10:06:51,304 - INFO - 客户端 1 异步训练线程已启动
2025-08-01 10:06:51,304 - INFO - [Client 2] 模型已放置到设备: cpu
2025-08-01 10:06:51,309 - INFO - [Client 2] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,314 - INFO - [Client 2] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,315 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 10:06:51,315 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 10:06:51,315 - INFO - [Client 2] 已更新algorithm的client_id
2025-08-01 10:06:51,315 - INFO - [Client 2] 模型初始化完成
2025-08-01 10:06:51,315 - INFO - 客户端 2 模型初始化成功
2025-08-01 10:06:51,316 - INFO - 客户端 2 异步训练线程已启动
2025-08-01 10:06:51,316 - INFO - [Client 3] 模型已放置到设备: cpu
2025-08-01 10:06:51,321 - INFO - [Client 3] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,328 - INFO - [Client 3] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,328 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 10:06:51,329 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 10:06:51,329 - INFO - [Client 3] 已更新algorithm的client_id
2025-08-01 10:06:51,329 - INFO - [Client 3] 模型初始化完成
2025-08-01 10:06:51,329 - INFO - 客户端 3 模型初始化成功
2025-08-01 10:06:51,329 - INFO - 客户端 3 异步训练线程已启动
2025-08-01 10:06:51,330 - INFO - [Client 4] 模型已放置到设备: cpu
2025-08-01 10:06:51,334 - INFO - [Client 4] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,339 - INFO - [Client 4] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,340 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 10:06:51,340 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 10:06:51,340 - INFO - [Client 4] 已更新algorithm的client_id
2025-08-01 10:06:51,340 - INFO - [Client 4] 模型初始化完成
2025-08-01 10:06:51,340 - INFO - 客户端 4 模型初始化成功
2025-08-01 10:06:51,341 - INFO - 客户端 4 异步训练线程已启动
2025-08-01 10:06:51,341 - INFO - [Client 5] 模型已放置到设备: cpu
2025-08-01 10:06:51,345 - INFO - [Client 5] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,353 - INFO - [Client 5] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,353 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 10:06:51,353 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 10:06:51,353 - INFO - [Client 5] 已更新algorithm的client_id
2025-08-01 10:06:51,354 - INFO - [Client 5] 模型初始化完成
2025-08-01 10:06:51,354 - INFO - 客户端 5 模型初始化成功
2025-08-01 10:06:51,354 - INFO - 客户端 5 异步训练线程已启动
2025-08-01 10:06:51,354 - INFO - [Client 6] 模型已放置到设备: cpu
2025-08-01 10:06:51,359 - INFO - [Client 6] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,365 - INFO - [Client 6] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,365 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 10:06:51,365 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 10:06:51,365 - INFO - [Client 6] 已更新algorithm的client_id
2025-08-01 10:06:51,365 - INFO - [Client 6] 模型初始化完成
2025-08-01 10:06:51,365 - INFO - 客户端 6 模型初始化成功
2025-08-01 10:06:51,366 - INFO - 客户端 6 异步训练线程已启动
2025-08-01 10:06:51,367 - INFO - [Client 7] 模型已放置到设备: cpu
2025-08-01 10:06:51,371 - INFO - [Client 7] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,377 - INFO - [Client 7] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,377 - INFO - [Algorithm] 设置客户端ID: 7
2025-08-01 10:06:51,377 - INFO - [Algorithm] 同步更新trainer的client_id: 7
2025-08-01 10:06:51,377 - INFO - [Client 7] 已更新algorithm的client_id
2025-08-01 10:06:51,377 - INFO - [Client 7] 模型初始化完成
2025-08-01 10:06:51,377 - INFO - 客户端 7 模型初始化成功
2025-08-01 10:06:51,377 - INFO - 客户端 7 异步训练线程已启动
2025-08-01 10:06:51,378 - INFO - [Client 8] 模型已放置到设备: cpu
2025-08-01 10:06:51,382 - INFO - [Client 8] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,388 - INFO - [Client 8] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,389 - INFO - [Algorithm] 设置客户端ID: 8
2025-08-01 10:06:51,389 - INFO - [Algorithm] 同步更新trainer的client_id: 8
2025-08-01 10:06:51,389 - INFO - [Client 8] 已更新algorithm的client_id
2025-08-01 10:06:51,389 - INFO - [Client 8] 模型初始化完成
2025-08-01 10:06:51,389 - INFO - 客户端 8 模型初始化成功
2025-08-01 10:06:51,389 - INFO - 客户端 8 异步训练线程已启动
2025-08-01 10:06:51,390 - INFO - [Client 9] 模型已放置到设备: cpu
2025-08-01 10:06:51,394 - INFO - [Client 9] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,400 - INFO - [Client 9] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,400 - INFO - [Algorithm] 设置客户端ID: 9
2025-08-01 10:06:51,400 - INFO - [Algorithm] 同步更新trainer的client_id: 9
2025-08-01 10:06:51,400 - INFO - [Client 9] 已更新algorithm的client_id
2025-08-01 10:06:51,400 - INFO - [Client 9] 模型初始化完成
2025-08-01 10:06:51,400 - INFO - 客户端 9 模型初始化成功
2025-08-01 10:06:51,401 - INFO - 客户端 9 异步训练线程已启动
2025-08-01 10:06:51,401 - INFO - [Client 10] 模型已放置到设备: cpu
2025-08-01 10:06:51,405 - INFO - [Client 10] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,411 - INFO - [Client 10] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 10:06:51,411 - INFO - [Algorithm] 设置客户端ID: 10
2025-08-01 10:06:51,411 - INFO - [Algorithm] 同步更新trainer的client_id: 10
2025-08-01 10:06:51,411 - INFO - [Client 10] 已更新algorithm的client_id
2025-08-01 10:06:51,411 - INFO - [Client 10] 模型初始化完成
2025-08-01 10:06:51,412 - INFO - 客户端 10 模型初始化成功
2025-08-01 10:06:51,412 - INFO - 客户端 10 异步训练线程已启动
2025-08-01 10:06:51,413 - INFO - 服务器主循环任务已启动: <Task pending name='Task-1' coro=<Server.run() running at D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py:1301>>
2025-08-01 10:06:51,414 - INFO - Starting a server at address 127.0.0.1 and port 8000.
2025-08-01 10:06:51,416 - INFO - [Server #540] 开始训练，共有 10 个客户端，每轮最多聚合 5 个客户端
2025-08-01 10:06:51,416 - INFO - 总训练轮次: 20
2025-08-01 10:06:51,416 - INFO - 🚀 开始第 1 轮训练（目标：20 轮）
2025-08-01 10:06:51,416 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:51,416 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:51,967 - INFO - 客户端 1 开始异步训练循环
2025-08-01 10:06:51,968 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:06:51,969 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 10:06:51,969 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 10:06:51,970 - INFO - [Client 1] 开始验证训练集
2025-08-01 10:06:51,994 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:06:51,994 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 10:06:51,995 - INFO - [Trainer 1] 开始训练
2025-08-01 10:06:51,995 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 10:06:51,995 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 10:06:51,996 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:51,996 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:06:51,996 - INFO - [Trainer 1] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:06:51,996 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 10:06:52,014 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3211, y=[4, 4, 4, 5, 0]
2025-08-01 10:06:52,014 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 10:06:52,018 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 10:06:52,031 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1178
2025-08-01 10:06:52,032 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:52,032 - INFO - [Trainer 1] 标签样本: [5, 0, 4, 5, 8]
2025-08-01 10:06:52,184 - INFO - [Trainer 1] Batch 0, Loss: 2.1744
2025-08-01 10:06:52,424 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:52,424 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:52,440 - INFO - 客户端 4 开始异步训练循环
2025-08-01 10:06:52,440 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:06:52,441 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 10:06:52,442 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 10:06:52,442 - INFO - [Client 4] 开始验证训练集
2025-08-01 10:06:52,443 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:06:52,443 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 10:06:52,443 - INFO - [Trainer 4] 开始训练
2025-08-01 10:06:52,443 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 10:06:52,444 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:06:52,444 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:52,445 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:06:52,445 - INFO - [Trainer 4] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:06:52,445 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 10:06:52,462 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3030, y=[0, 1, 0, 5, 0]
2025-08-01 10:06:52,462 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 10:06:52,470 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 10:06:52,486 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2634
2025-08-01 10:06:52,487 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:52,487 - INFO - [Trainer 4] 标签样本: [0, 1, 0, 1, 5]
2025-08-01 10:06:52,658 - INFO - [Trainer 4] Batch 0, Loss: 2.5242
2025-08-01 10:06:52,690 - INFO - 客户端 3 开始异步训练循环
2025-08-01 10:06:52,690 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:06:52,691 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 10:06:52,691 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 10:06:52,691 - INFO - [Client 3] 开始验证训练集
2025-08-01 10:06:52,693 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:06:52,693 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 10:06:52,694 - INFO - [Trainer 3] 开始训练
2025-08-01 10:06:52,694 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 10:06:52,695 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:06:52,695 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:52,696 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:06:52,696 - INFO - [Trainer 3] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:06:52,696 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 10:06:52,720 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4903, y=[2, 2, 2, 2, 2]
2025-08-01 10:06:52,720 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 10:06:52,742 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 10:06:52,751 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4851
2025-08-01 10:06:52,752 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:52,752 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 6, 2]
2025-08-01 10:06:52,851 - INFO - [Trainer 3] Batch 0, Loss: 2.4862
2025-08-01 10:06:52,953 - INFO - 客户端 2 开始异步训练循环
2025-08-01 10:06:52,955 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:06:52,955 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 10:06:52,958 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 10:06:52,959 - INFO - [Client 2] 开始验证训练集
2025-08-01 10:06:52,960 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:06:52,960 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 10:06:52,960 - INFO - [Trainer 2] 开始训练
2025-08-01 10:06:52,960 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 10:06:52,961 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 10:06:52,962 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:52,962 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:06:52,964 - INFO - [Trainer 2] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:06:52,964 - INFO - [Trainer 2] 开始第 1/2 个epoch
2025-08-01 10:06:53,584 - INFO - 客户端 7 开始异步训练循环
2025-08-01 10:06:53,585 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:53,587 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:06:53,591 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:53,593 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 10:06:53,595 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 10:06:53,597 - INFO - [Client 7] 开始验证训练集
2025-08-01 10:06:53,603 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:06:53,605 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 10:06:53,606 - INFO - [Trainer 7] 开始训练
2025-08-01 10:06:53,606 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 10:06:53,610 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:06:53,611 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:53,612 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:06:53,614 - INFO - [Trainer 7] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:06:53,615 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 10:06:53,628 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1786, y=[8, 8, 8, 8, 8]
2025-08-01 10:06:53,632 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 10:06:53,681 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 10:06:53,686 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2869, y=[2, 2, 2, 2, 2]
2025-08-01 10:06:53,687 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 10:06:53,700 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1346
2025-08-01 10:06:53,702 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:53,703 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:06:53,731 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 10:06:53,749 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3956
2025-08-01 10:06:53,749 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:53,750 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:06:53,798 - INFO - 客户端 9 开始异步训练循环
2025-08-01 10:06:53,801 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:06:53,804 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 10:06:53,806 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 10:06:53,807 - INFO - [Client 9] 开始验证训练集
2025-08-01 10:06:53,809 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:06:53,812 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 10:06:53,814 - INFO - [Trainer 9] 开始训练
2025-08-01 10:06:53,814 - INFO - [Trainer 2] Batch 0, Loss: 1.8035
2025-08-01 10:06:53,815 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 10:06:53,818 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:06:53,821 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:53,823 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:06:53,824 - INFO - [Trainer 9] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:06:53,826 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 10:06:53,884 - INFO - [Trainer 7] Batch 0, Loss: 2.4037
2025-08-01 10:06:53,930 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5513, y=[8, 6, 6, 6, 6]
2025-08-01 10:06:53,932 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 10:06:53,967 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 10:06:53,983 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5064
2025-08-01 10:06:53,987 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:53,991 - INFO - [Trainer 9] 标签样本: [6, 8, 6, 6, 6]
2025-08-01 10:06:53,997 - INFO - 客户端 10 开始异步训练循环
2025-08-01 10:06:54,003 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:06:54,009 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:06:54,014 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:06:54,015 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:06:54,018 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:06:54,021 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:06:54,026 - INFO - [Trainer 10] 开始训练
2025-08-01 10:06:54,032 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:06:54,039 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:06:54,042 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:54,047 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:06:54,055 - INFO - [Trainer 10] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:06:54,055 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:06:54,088 - INFO - 客户端 6 开始异步训练循环
2025-08-01 10:06:54,090 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:06:54,092 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 10:06:54,093 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 10:06:54,094 - INFO - [Client 6] 开始验证训练集
2025-08-01 10:06:54,101 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:06:54,105 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 10:06:54,105 - INFO - [Trainer 6] 开始训练
2025-08-01 10:06:54,106 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 10:06:54,113 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 10:06:54,118 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:54,124 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:06:54,130 - INFO - [Trainer 6] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:06:54,134 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 10:06:54,183 - INFO - [Trainer 9] Batch 0, Loss: 2.0219
2025-08-01 10:06:54,208 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3930, y=[8, 6, 8, 8, 6]
2025-08-01 10:06:54,210 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:06:54,256 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0558, y=[0, 8, 0, 0, 7]
2025-08-01 10:06:54,258 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 10:06:54,326 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:06:54,346 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2591
2025-08-01 10:06:54,349 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:54,351 - INFO - [Trainer 10] 标签样本: [8, 8, 6, 8, 8]
2025-08-01 10:06:54,378 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 10:06:54,389 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0894
2025-08-01 10:06:54,391 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:54,391 - INFO - [Trainer 6] 标签样本: [7, 7, 0, 8, 0]
2025-08-01 10:06:54,544 - INFO - [Trainer 10] Batch 0, Loss: 2.0151
2025-08-01 10:06:54,599 - INFO - [Trainer 6] Batch 0, Loss: 2.4948
2025-08-01 10:06:54,604 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:54,608 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:55,392 - INFO - [Trainer 1] Batch 5, Loss: 1.6615
2025-08-01 10:06:55,619 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:55,621 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:56,559 - INFO - [Trainer 4] Batch 5, Loss: 2.5153
2025-08-01 10:06:56,641 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:56,645 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:57,165 - INFO - 客户端 8 开始异步训练循环
2025-08-01 10:06:57,168 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:06:57,172 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 10:06:57,176 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 10:06:57,178 - INFO - [Client 8] 开始验证训练集
2025-08-01 10:06:57,184 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:06:57,190 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 10:06:57,192 - INFO - [Trainer 8] 开始训练
2025-08-01 10:06:57,200 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 10:06:57,203 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:06:57,209 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:06:57,215 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:06:57,218 - INFO - [Trainer 8] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:06:57,219 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 10:06:57,339 - INFO - [Trainer 3] Batch 5, Loss: 0.0000
2025-08-01 10:06:57,342 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2551, y=[6, 7, 1, 1, 1]
2025-08-01 10:06:57,344 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 10:06:57,419 - INFO - [Trainer 2] Batch 5, Loss: 0.2420
2025-08-01 10:06:57,427 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 10:06:57,454 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1391
2025-08-01 10:06:57,455 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:57,458 - INFO - [Trainer 8] 标签样本: [0, 0, 1, 0, 0]
2025-08-01 10:06:57,663 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:57,666 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:57,674 - INFO - [Trainer 8] Batch 0, Loss: 2.3631
2025-08-01 10:06:57,734 - INFO - [Trainer 7] Batch 5, Loss: 0.0000
2025-08-01 10:06:57,761 - INFO - [Trainer 9] Batch 5, Loss: 0.5893
2025-08-01 10:06:57,831 - INFO - [Trainer 1] Epoch 1 进度: 10/10 批次
2025-08-01 10:06:57,856 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3344
2025-08-01 10:06:57,857 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:57,861 - INFO - [Trainer 1] 标签样本: [5, 5, 8, 8, 8]
2025-08-01 10:06:58,070 - INFO - [Trainer 1] Batch 9, Loss: 1.6763
2025-08-01 10:06:58,393 - INFO - [Trainer 1] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:06:58,395 - INFO - [Trainer 1] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.9342, Accuracy: 41.33%
2025-08-01 10:06:58,395 - INFO - [Trainer 1] 开始第 2/2 个epoch
2025-08-01 10:06:58,421 - INFO - [Trainer 10] Batch 5, Loss: 1.6772
2025-08-01 10:06:58,444 - INFO - [Trainer 6] Batch 5, Loss: 1.1268
2025-08-01 10:06:58,485 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0417, y=[0, 8, 0, 0, 0]
2025-08-01 10:06:58,485 - INFO - [Trainer 1] Epoch 2 开始处理 10 个批次
2025-08-01 10:06:58,607 - INFO - [Trainer 1] Epoch 2 进度: 1/10 批次
2025-08-01 10:06:58,618 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1709
2025-08-01 10:06:58,619 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:58,620 - INFO - [Trainer 1] 标签样本: [0, 8, 4, 8, 8]
2025-08-01 10:06:58,677 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:58,678 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:58,811 - INFO - [Trainer 1] Batch 0, Loss: 3.3309
2025-08-01 10:06:59,354 - INFO - [Trainer 4] Epoch 1 进度: 10/10 批次
2025-08-01 10:06:59,370 - INFO - [Trainer 4] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1296
2025-08-01 10:06:59,374 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:06:59,376 - INFO - [Trainer 4] 标签样本: [1, 0, 1, 1, 0]
2025-08-01 10:06:59,545 - INFO - [Trainer 4] Batch 9, Loss: 0.9555
2025-08-01 10:06:59,694 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:59,698 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:06:59,963 - INFO - [Trainer 4] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:06:59,965 - INFO - [Trainer 4] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.7247, Accuracy: 47.33%
2025-08-01 10:06:59,967 - INFO - [Trainer 4] 开始第 2/2 个epoch
2025-08-01 10:07:00,012 - INFO - [Trainer 2] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:00,032 - INFO - [Trainer 2] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0790
2025-08-01 10:07:00,033 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:00,036 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:07:00,060 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3002, y=[0, 1, 1, 0, 1]
2025-08-01 10:07:00,061 - INFO - [Trainer 4] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:00,111 - INFO - [Trainer 4] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:00,120 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3415
2025-08-01 10:07:00,122 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:00,124 - INFO - [Trainer 4] 标签样本: [1, 1, 1, 1, 1]
2025-08-01 10:07:00,150 - INFO - [Trainer 3] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:00,166 - INFO - [Trainer 2] Batch 9, Loss: 0.0013
2025-08-01 10:07:00,167 - INFO - [Trainer 3] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4327
2025-08-01 10:07:00,169 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:00,171 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:07:00,274 - INFO - [Trainer 4] Batch 0, Loss: 1.1905
2025-08-01 10:07:00,289 - INFO - [Trainer 3] Batch 9, Loss: 1.4769
2025-08-01 10:07:00,509 - INFO - [Trainer 7] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:00,518 - INFO - [Trainer 2] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:00,524 - INFO - [Trainer 2] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.8087, Accuracy: 95.33%
2025-08-01 10:07:00,526 - INFO - [Trainer 2] 开始第 2/2 个epoch
2025-08-01 10:07:00,527 - INFO - [Trainer 7] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4271
2025-08-01 10:07:00,530 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:00,535 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:07:00,594 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0523, y=[8, 8, 8, 8, 8]
2025-08-01 10:07:00,597 - INFO - [Trainer 2] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:00,632 - INFO - [Trainer 7] Batch 9, Loss: 0.0000
2025-08-01 10:07:00,675 - INFO - [Trainer 2] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:00,682 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2505
2025-08-01 10:07:00,684 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:00,684 - INFO - [Trainer 2] 标签样本: [5, 8, 8, 8, 8]
2025-08-01 10:07:00,712 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:00,715 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:00,759 - INFO - [Trainer 3] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:00,762 - INFO - [Trainer 3] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.5545, Accuracy: 80.33%
2025-08-01 10:07:00,766 - INFO - [Trainer 3] 开始第 2/2 个epoch
2025-08-01 10:07:00,770 - INFO - [Trainer 9] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:00,796 - INFO - [Trainer 9] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7342, x.mean: -0.3595
2025-08-01 10:07:00,800 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:00,801 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 6, 6]
2025-08-01 10:07:00,843 - INFO - [Trainer 2] Batch 0, Loss: 0.2763
2025-08-01 10:07:00,849 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4199, y=[2, 2, 2, 2, 2]
2025-08-01 10:07:00,851 - INFO - [Trainer 3] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:00,871 - INFO - [Trainer 9] Batch 9, Loss: 0.1166
2025-08-01 10:07:00,932 - INFO - [Trainer 3] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:00,951 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4469
2025-08-01 10:07:00,955 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:00,957 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:07:00,976 - INFO - 客户端 5 开始异步训练循环
2025-08-01 10:07:00,979 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:00,983 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 10:07:00,986 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 10:07:00,989 - INFO - [Client 5] 开始验证训练集
2025-08-01 10:07:00,994 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:00,999 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:01,002 - INFO - [Trainer 5] 开始训练
2025-08-01 10:07:01,002 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 10:07:01,007 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 10:07:01,011 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:01,016 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:01,021 - INFO - [Trainer 5] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:01,023 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 10:07:01,035 - INFO - [Trainer 7] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:01,038 - INFO - [Trainer 7] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.4115, Accuracy: 89.00%
2025-08-01 10:07:01,038 - INFO - [Trainer 7] 开始第 2/2 个epoch
2025-08-01 10:07:01,041 - INFO - [Trainer 10] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:01,054 - INFO - [Trainer 10] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3009
2025-08-01 10:07:01,056 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:01,058 - INFO - [Trainer 10] 标签样本: [7, 8, 8, 8, 4]
2025-08-01 10:07:01,058 - INFO - [Trainer 6] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:01,072 - INFO - [Trainer 6] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0712
2025-08-01 10:07:01,074 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:01,077 - INFO - [Trainer 6] 标签样本: [0, 0, 8, 0, 8]
2025-08-01 10:07:01,083 - INFO - [Trainer 3] Batch 0, Loss: 0.6616
2025-08-01 10:07:01,138 - INFO - [Trainer 10] Batch 9, Loss: 0.8380
2025-08-01 10:07:01,160 - INFO - [Trainer 6] Batch 9, Loss: 1.6231
2025-08-01 10:07:01,236 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3932, y=[2, 2, 2, 2, 2]
2025-08-01 10:07:01,238 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3769, y=[7, 7, 7, 7, 3]
2025-08-01 10:07:01,239 - INFO - [Trainer 7] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:01,240 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:01,293 - INFO - [Trainer 8] Batch 5, Loss: 1.7747
2025-08-01 10:07:01,298 - INFO - [Trainer 9] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:01,299 - INFO - [Trainer 9] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.8997, Accuracy: 74.00%
2025-08-01 10:07:01,300 - INFO - [Trainer 9] 开始第 2/2 个epoch
2025-08-01 10:07:01,382 - INFO - [Trainer 7] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:01,402 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4750
2025-08-01 10:07:01,408 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:01,411 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:07:01,426 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:01,446 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3532
2025-08-01 10:07:01,448 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:01,450 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 3, 7]
2025-08-01 10:07:01,450 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5299, y=[6, 8, 6, 6, 8]
2025-08-01 10:07:01,454 - INFO - [Trainer 9] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:01,503 - INFO - [Trainer 6] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:01,505 - INFO - [Trainer 6] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.6156, Accuracy: 45.33%
2025-08-01 10:07:01,507 - INFO - [Trainer 6] 开始第 2/2 个epoch
2025-08-01 10:07:01,512 - INFO - [Trainer 10] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:01,514 - INFO - [Trainer 10] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.2843, Accuracy: 70.00%
2025-08-01 10:07:01,516 - INFO - [Trainer 10] 开始第 2/2 个epoch
2025-08-01 10:07:01,538 - INFO - [Trainer 7] Batch 0, Loss: 1.2967
2025-08-01 10:07:01,589 - INFO - [Trainer 5] Batch 0, Loss: 2.0216
2025-08-01 10:07:01,609 - INFO - [Trainer 9] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:01,621 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3627
2025-08-01 10:07:01,623 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:01,623 - INFO - [Trainer 9] 标签样本: [6, 7, 6, 6, 8]
2025-08-01 10:07:01,647 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1210, y=[8, 7, 8, 8, 8]
2025-08-01 10:07:01,650 - INFO - [Trainer 10] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:01,662 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1098, y=[0, 7, 8, 8, 7]
2025-08-01 10:07:01,664 - INFO - [Trainer 6] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:01,725 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:01,727 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:01,829 - INFO - [Trainer 10] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:01,830 - INFO - [Trainer 6] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:01,843 - INFO - [Trainer 9] Batch 0, Loss: 1.5751
2025-08-01 10:07:01,856 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3389
2025-08-01 10:07:01,857 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2403
2025-08-01 10:07:01,860 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:01,862 - INFO - [Trainer 10] 标签样本: [6, 6, 8, 8, 6]
2025-08-01 10:07:01,864 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:01,865 - INFO - [Trainer 6] 标签样本: [0, 7, 0, 8, 8]
2025-08-01 10:07:01,975 - INFO - [Trainer 1] Batch 5, Loss: 1.7470
2025-08-01 10:07:02,130 - INFO - [Trainer 6] Batch 0, Loss: 1.4340
2025-08-01 10:07:02,143 - INFO - [Trainer 10] Batch 0, Loss: 0.8344
2025-08-01 10:07:02,737 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:02,741 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:03,751 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:03,755 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:04,370 - INFO - [Trainer 4] Batch 5, Loss: 1.2466
2025-08-01 10:07:04,651 - INFO - [Trainer 8] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:04,660 - INFO - [Trainer 8] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3381
2025-08-01 10:07:04,661 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:04,661 - INFO - [Trainer 8] 标签样本: [0, 6, 1, 1, 0]
2025-08-01 10:07:04,665 - INFO - [Trainer 2] Batch 5, Loss: 1.3250
2025-08-01 10:07:04,730 - INFO - [Trainer 1] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:04,753 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0114
2025-08-01 10:07:04,757 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:04,759 - INFO - [Trainer 1] 标签样本: [0, 8, 8, 8, 5]
2025-08-01 10:07:04,768 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:04,772 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:04,899 - INFO - [Trainer 8] Batch 9, Loss: 1.0304
2025-08-01 10:07:04,987 - INFO - [Trainer 1] Batch 9, Loss: 1.2118
2025-08-01 10:07:05,376 - INFO - [Trainer 1] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:05,377 - INFO - [Trainer 1] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 2.0819, Accuracy: 38.33%
2025-08-01 10:07:05,384 - INFO - [Trainer 1] 参数 conv1.weight: 平均值=0.005973, 标准差=0.120997
2025-08-01 10:07:05,387 - INFO - [Trainer 1] 参数 bn1.weight: 平均值=1.000621, 标准差=0.017014
2025-08-01 10:07:05,395 - INFO - [Trainer 1] 参数 bn1.bias: 平均值=-0.002822, 标准差=0.014643
2025-08-01 10:07:05,398 - INFO - [Trainer 1] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:05,405 - INFO - [Trainer.get_report] 客户端 1 训练报告 - Loss: 2.0081, Accuracy: 39.83%, 陈旧度: 0
2025-08-01 10:07:05,406 - INFO - [Trainer 1] 训练报告生成完成: Loss=2.0081, Accuracy=39.83%
2025-08-01 10:07:05,406 - INFO - [Client 1] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:05,407 - INFO - [Client 1] 使用训练准确率作为客户端准确率: 0.3983
2025-08-01 10:07:05,408 - INFO - [Client 1] 第 1 轮训练完成，耗时: 13.44秒, 准确率: 0.3983
2025-08-01 10:07:05,411 - INFO - [Client 1] 开始提取模型权重
2025-08-01 10:07:05,419 - INFO - [Client 1] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:05,423 - INFO - [Client 1] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:05,423 - INFO - [Client 1] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:05,427 - INFO - [Client 1] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:05,428 - INFO - [Client 1] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:05,430 - INFO - [服务器] 🔄 收到客户端 1 的模型更新，模型版本: unknown
2025-08-01 10:07:05,432 - INFO - [服务器] 客户端 1 训练信息 - 样本数: 300, 训练时间: 13.44秒
2025-08-01 10:07:05,434 - INFO - [服务器] 当前缓冲池大小: 0, 全局轮次: 0
2025-08-01 10:07:05,438 - INFO - [Trainer 8] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:05,442 - INFO - [Trainer 8] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.4280, Accuracy: 50.33%
2025-08-01 10:07:05,446 - INFO - [Trainer 8] 开始第 2/2 个epoch
2025-08-01 10:07:05,456 - INFO - [客户端权重摘要] 客户端1 | 参数数量: 74, 均值: 0.001558, 最大: 25.884436, 最小: -1.477074
2025-08-01 10:07:05,457 - INFO - 客户端 1 更新记录 - 陈旧度: 0, 提交轮次: 0
2025-08-01 10:07:05,457 - INFO - ✅ 客户端 1 的更新已加入成功缓冲池，当前池大小: 1
2025-08-01 10:07:05,457 - INFO - 📊 当前缓冲池中的客户端: [1]
2025-08-01 10:07:05,458 - INFO - 🔍 客户端 1 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:05,459 - INFO - 成功处理客户端 1 的更新
2025-08-01 10:07:05,459 - INFO - [Client 1] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:05,460 - INFO - 客户端 1 训练完成
2025-08-01 10:07:05,527 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2997, y=[1, 0, 1, 0, 0]
2025-08-01 10:07:05,529 - INFO - [Trainer 8] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:05,579 - INFO - [Trainer 8] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:05,597 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2471
2025-08-01 10:07:05,598 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:05,601 - INFO - [Trainer 8] 标签样本: [0, 6, 0, 0, 0]
2025-08-01 10:07:05,689 - INFO - [Trainer 3] Batch 5, Loss: 0.2972
2025-08-01 10:07:05,769 - INFO - [Trainer 8] Batch 0, Loss: 2.7440
2025-08-01 10:07:05,791 - WARNING - 配置文件设置min_clients_for_aggregation=1，可能导致每轮只聚合一个客户端
2025-08-01 10:07:05,796 - INFO - 异步模式下，当前有 1 个客户端更新，满足最小阈值 1，可以触发聚合
2025-08-01 10:07:05,798 - INFO - ✅ 触发聚合条件，开始第 0 轮聚合
2025-08-01 10:07:05,804 - INFO - 🔒 开始聚合过程，轮次: 0
2025-08-01 10:07:05,817 - ERROR - 估计客户端1时间时出错: name 'log2' is not defined
2025-08-01 10:07:05,830 - ERROR - 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 773, in estimate_client_times
    C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps
NameError: name 'log2' is not defined

2025-08-01 10:07:05,833 - INFO - 候选客户端按训练时间排序: [(1, 5.0)]
2025-08-01 10:07:05,836 - INFO - 算法参数: V=1.0, max_clients=5, tau_max=5
2025-08-01 10:07:05,838 - DEBUG - 尝试聚合 1 客户端 (当前客户端: 1)
2025-08-01 10:07:05,839 - DEBUG -   模拟D_t: 5.0000, V*D_t: 5.0000
2025-08-01 10:07:05,839 - DEBUG -   惩罚项: 0.0000, 总目标值: 5.0000
2025-08-01 10:07:05,839 - DEBUG - 更新最佳集合：目标值 5.0000 < 之前最小 5.0000
2025-08-01 10:07:05,839 - INFO - 贪心策略选择 1 个客户端进行聚合: [1]
2025-08-01 10:07:05,840 - INFO - 最优目标函数值: 5.0000
2025-08-01 10:07:05,846 - INFO - 开始执行聚合 - 参与聚合的客户端数量: 1个
2025-08-01 10:07:05,852 - INFO - [Trainer.update_staleness] 客户端 1 陈旧度更新为: 0
2025-08-01 10:07:05,855 - DEBUG - 参与聚合的客户端 1 陈旧度: 0 (当前服务器轮次:0, 客户端拉取模型轮次:0)
2025-08-01 10:07:05,856 - DEBUG - 客户端 1 在服务器轮次 0 被聚合
2025-08-01 10:07:05,856 - INFO - 聚合执行 - 聚合客户端ID: [1]
2025-08-01 10:07:05,904 - INFO - [聚合前全局权重] 均值: 0.001175, 最大: 1.000000, 最小: -0.192305
2025-08-01 10:07:05,909 - INFO - [Algorithm] 客户端 1 - 样本数: 300, 陈旧度: 0, 陈旧度因子: 1.0000, 原始权重: 300.0000
2025-08-01 10:07:05,910 - INFO - [Algorithm] 客户端 1 最终权重: 1.0000
2025-08-01 10:07:06,023 - INFO - [Trainer 7] Batch 5, Loss: 1.0530
2025-08-01 10:07:06,065 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-08-01 10:07:06,068 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-08-01 10:07:06,070 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-08-01 10:07:06,097 - INFO - [Algorithm] 成功加载权重到模型
2025-08-01 10:07:06,119 - INFO - [聚合后全局权重] 均值: 0.001558, 最大: 25.884436, 最小: -1.477074
2025-08-01 10:07:06,139 - INFO - [权重变化] 平均绝对差异: 0.015228
2025-08-01 10:07:06,147 - ERROR - 权重变化CSV文件未初始化，跳过记录
2025-08-01 10:07:06,163 - INFO - 创建权重变化记录文件: ./results/scafl/mnist/weights_change_20250801_100706.csv
2025-08-01 10:07:06,168 - INFO - 轮次 0 权重变化记录 - 平均变化: 0.015228, 均值: 0.001558, 最大: 25.884436, 最小: -1.477074
2025-08-01 10:07:06,171 - INFO - 服务器轮次 0 聚合完成: 更新了全局模型
2025-08-01 10:07:06,172 - INFO - [DEBUG] simulate_wall_time=True, asynchronous_mode=True
2025-08-01 10:07:06,172 - INFO - [DEBUG] wall_time更新: 1754014004.0022285 -> 1754014004.8279245 (增加了0.8256959915161133秒)
2025-08-01 10:07:06,175 - INFO - 记录轮次: 0
2025-08-01 10:07:06,177 - INFO - 聚合完成，进入新轮次，当前全局轮次: 1
2025-08-01 10:07:06,179 - INFO - 更新最后聚合时间: 1754014026.179106
2025-08-01 10:07:06,297 - INFO - 为训练器创建了模型的深拷贝，重置BatchNorm统计信息，并移至设备: cpu
2025-08-01 10:07:06,298 - INFO - 已将全局权重加载到评估模型中
2025-08-01 10:07:06,299 - INFO - 开始评估全局模型，测试集大小: 10000
2025-08-01 10:07:06,342 - INFO - [Trainer 5] Batch 5, Loss: 1.2930
2025-08-01 10:07:06,411 - INFO - [Trainer 9] Batch 5, Loss: 0.4987
2025-08-01 10:07:06,476 - INFO - [Trainer 6] Batch 5, Loss: 1.0657
2025-08-01 10:07:06,933 - INFO - [Trainer 10] Batch 5, Loss: 0.7050
2025-08-01 10:07:07,473 - INFO - [Trainer 2] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:07,498 - INFO - [Trainer 2] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1983
2025-08-01 10:07:07,498 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:07,499 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:07:07,682 - INFO - [Trainer 2] Batch 9, Loss: 0.1859
2025-08-01 10:07:07,800 - INFO - [Trainer 4] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:07,808 - INFO - [Trainer 4] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0225
2025-08-01 10:07:07,809 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:07,810 - INFO - [Trainer 4] 标签样本: [4, 0, 0, 1, 0]
2025-08-01 10:07:07,968 - INFO - [Trainer 4] Batch 9, Loss: 1.0090
2025-08-01 10:07:08,021 - INFO - [Trainer 2] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:08,029 - INFO - [Trainer 2] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.3370, Accuracy: 95.67%
2025-08-01 10:07:08,029 - INFO - [Trainer 2] 参数 conv1.weight: 平均值=0.007879, 标准差=0.116520
2025-08-01 10:07:08,030 - INFO - [Trainer 2] 参数 bn1.weight: 平均值=1.000320, 标准差=0.022703
2025-08-01 10:07:08,033 - INFO - [Trainer 2] 参数 bn1.bias: 平均值=0.004681, 标准差=0.018313
2025-08-01 10:07:08,034 - INFO - [Trainer 2] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:08,036 - INFO - [Trainer.get_report] 客户端 2 训练报告 - Loss: 0.5728, Accuracy: 95.50%, 陈旧度: 0
2025-08-01 10:07:08,039 - INFO - [Trainer 2] 训练报告生成完成: Loss=0.5728, Accuracy=95.50%
2025-08-01 10:07:08,042 - INFO - [Client 2] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:08,045 - INFO - [Client 2] 使用训练准确率作为客户端准确率: 0.9550
2025-08-01 10:07:08,046 - INFO - [Client 2] 第 1 轮训练完成，耗时: 15.09秒, 准确率: 0.9550
2025-08-01 10:07:08,047 - INFO - [Client 2] 开始提取模型权重
2025-08-01 10:07:08,051 - INFO - [Client 2] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:08,054 - INFO - [Client 2] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:08,056 - INFO - [Client 2] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:08,059 - INFO - [Client 2] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:08,062 - INFO - [Client 2] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:08,062 - INFO - [服务器] 🔄 收到客户端 2 的模型更新，模型版本: unknown
2025-08-01 10:07:08,063 - INFO - [服务器] 客户端 2 训练信息 - 样本数: 300, 训练时间: 15.09秒
2025-08-01 10:07:08,063 - INFO - [服务器] 当前缓冲池大小: 1, 全局轮次: 1
2025-08-01 10:07:08,073 - INFO - [客户端权重摘要] 客户端2 | 参数数量: 74, 均值: 0.001501, 最大: 33.998192, 最小: -2.487158
2025-08-01 10:07:08,076 - INFO - 客户端 2 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:08,078 - INFO - ✅ 客户端 2 的更新已加入成功缓冲池，当前池大小: 2
2025-08-01 10:07:08,092 - INFO - 📊 当前缓冲池中的客户端: [1, 2]
2025-08-01 10:07:08,092 - INFO - 🔍 客户端 2 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:08,093 - INFO - 成功处理客户端 2 的更新
2025-08-01 10:07:08,093 - INFO - [Client 2] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:08,093 - INFO - 客户端 2 训练完成
2025-08-01 10:07:08,345 - INFO - [Trainer 4] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:08,350 - INFO - [Trainer 4] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.1820, Accuracy: 58.00%
2025-08-01 10:07:08,351 - INFO - [Trainer 4] 参数 conv1.weight: 平均值=-0.000284, 标准差=0.117680
2025-08-01 10:07:08,355 - INFO - [Trainer 4] 参数 bn1.weight: 平均值=0.999638, 标准差=0.014254
2025-08-01 10:07:08,356 - INFO - [Trainer 4] 参数 bn1.bias: 平均值=-0.000627, 标准差=0.011720
2025-08-01 10:07:08,358 - INFO - [Trainer 4] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:08,359 - INFO - [Trainer.get_report] 客户端 4 训练报告 - Loss: 1.4533, Accuracy: 52.67%, 陈旧度: 0
2025-08-01 10:07:08,362 - INFO - [Trainer 4] 训练报告生成完成: Loss=1.4533, Accuracy=52.67%
2025-08-01 10:07:08,364 - INFO - [Client 4] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:08,366 - INFO - [Client 4] 使用训练准确率作为客户端准确率: 0.5267
2025-08-01 10:07:08,372 - INFO - [Client 4] 第 1 轮训练完成，耗时: 15.92秒, 准确率: 0.5267
2025-08-01 10:07:08,381 - INFO - [Client 4] 开始提取模型权重
2025-08-01 10:07:08,382 - INFO - [Client 4] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:08,384 - INFO - [Client 4] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:08,384 - INFO - [Client 4] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:08,385 - INFO - [Client 4] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:08,385 - INFO - [Client 4] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:08,386 - INFO - [服务器] 🔄 收到客户端 4 的模型更新，模型版本: unknown
2025-08-01 10:07:08,386 - INFO - [服务器] 客户端 4 训练信息 - 样本数: 300, 训练时间: 15.92秒
2025-08-01 10:07:08,389 - INFO - [服务器] 当前缓冲池大小: 2, 全局轮次: 1
2025-08-01 10:07:08,428 - INFO - [客户端权重摘要] 客户端4 | 参数数量: 74, 均值: 0.001580, 最大: 24.940508, 最小: -1.959903
2025-08-01 10:07:08,431 - INFO - 客户端 4 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:08,433 - INFO - ✅ 客户端 4 的更新已加入成功缓冲池，当前池大小: 3
2025-08-01 10:07:08,438 - INFO - 📊 当前缓冲池中的客户端: [1, 2, 4]
2025-08-01 10:07:08,443 - INFO - 🔍 客户端 4 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:08,444 - INFO - 成功处理客户端 4 的更新
2025-08-01 10:07:08,444 - INFO - [Client 4] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:08,447 - INFO - 客户端 4 训练完成
2025-08-01 10:07:08,942 - INFO - [Trainer 3] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:08,954 - INFO - [Trainer 3] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1776
2025-08-01 10:07:08,955 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:08,955 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:07:09,076 - INFO - [Trainer 3] Batch 9, Loss: 0.0804
2025-08-01 10:07:09,113 - INFO - [Trainer 7] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:09,144 - INFO - [Trainer 7] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4658
2025-08-01 10:07:09,145 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:09,146 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:07:09,281 - INFO - [Trainer 7] Batch 9, Loss: 0.0002
2025-08-01 10:07:09,541 - INFO - [Trainer 3] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:09,543 - INFO - [Trainer 3] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.5090, Accuracy: 89.33%
2025-08-01 10:07:09,548 - INFO - [Trainer 3] 参数 conv1.weight: 平均值=-0.001434, 标准差=0.112882
2025-08-01 10:07:09,551 - INFO - [Trainer 3] 参数 bn1.weight: 平均值=0.999546, 标准差=0.015513
2025-08-01 10:07:09,554 - INFO - [Trainer 3] 参数 bn1.bias: 平均值=0.000148, 标准差=0.008404
2025-08-01 10:07:09,556 - INFO - [Trainer 3] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:09,561 - INFO - [Trainer.get_report] 客户端 3 训练报告 - Loss: 1.0318, Accuracy: 84.83%, 陈旧度: 0
2025-08-01 10:07:09,563 - INFO - [Trainer 3] 训练报告生成完成: Loss=1.0318, Accuracy=84.83%
2025-08-01 10:07:09,564 - INFO - [Client 3] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:09,571 - INFO - [Client 3] 使用训练准确率作为客户端准确率: 0.8483
2025-08-01 10:07:09,573 - INFO - [Client 3] 第 1 轮训练完成，耗时: 16.88秒, 准确率: 0.8483
2025-08-01 10:07:09,574 - INFO - [Client 3] 开始提取模型权重
2025-08-01 10:07:09,576 - INFO - [Client 3] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:09,577 - INFO - [Client 3] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:09,581 - INFO - [Client 3] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:09,584 - INFO - [Client 3] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:09,587 - INFO - [Client 3] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:09,588 - INFO - [服务器] 🔄 收到客户端 3 的模型更新，模型版本: unknown
2025-08-01 10:07:09,590 - INFO - [服务器] 客户端 3 训练信息 - 样本数: 300, 训练时间: 16.88秒
2025-08-01 10:07:09,591 - INFO - [服务器] 当前缓冲池大小: 3, 全局轮次: 1
2025-08-01 10:07:09,624 - INFO - [客户端权重摘要] 客户端3 | 参数数量: 74, 均值: 0.000936, 最大: 20.000000, 最小: -1.277732
2025-08-01 10:07:09,625 - INFO - 客户端 3 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:09,626 - INFO - ✅ 客户端 3 的更新已加入成功缓冲池，当前池大小: 4
2025-08-01 10:07:09,628 - INFO - 📊 当前缓冲池中的客户端: [1, 2, 4, 3]
2025-08-01 10:07:09,628 - INFO - 🔍 客户端 3 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:09,629 - INFO - 成功处理客户端 3 的更新
2025-08-01 10:07:09,631 - INFO - [Client 3] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:09,631 - INFO - 客户端 3 训练完成
2025-08-01 10:07:09,707 - INFO - [Trainer 5] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:09,720 - INFO - [Trainer 5] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2295
2025-08-01 10:07:09,722 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:09,722 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 7, 7]
2025-08-01 10:07:09,725 - INFO - [Trainer 7] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:09,726 - INFO - [Trainer 7] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.3471, Accuracy: 99.00%
2025-08-01 10:07:09,727 - INFO - [Trainer 7] 参数 conv1.weight: 平均值=-0.004468, 标准差=0.112571
2025-08-01 10:07:09,728 - INFO - [Trainer 7] 参数 bn1.weight: 平均值=0.998577, 标准差=0.007273
2025-08-01 10:07:09,728 - INFO - [Trainer 7] 参数 bn1.bias: 平均值=-0.001136, 标准差=0.006242
2025-08-01 10:07:09,729 - INFO - [Trainer 7] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:09,730 - INFO - [Trainer.get_report] 客户端 7 训练报告 - Loss: 0.3793, Accuracy: 94.00%, 陈旧度: 0
2025-08-01 10:07:09,730 - INFO - [Trainer 7] 训练报告生成完成: Loss=0.3793, Accuracy=94.00%
2025-08-01 10:07:09,730 - INFO - [Client 7] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:09,731 - INFO - [Client 7] 使用训练准确率作为客户端准确率: 0.9400
2025-08-01 10:07:09,731 - INFO - [Client 7] 第 1 轮训练完成，耗时: 16.13秒, 准确率: 0.9400
2025-08-01 10:07:09,732 - INFO - [Client 7] 开始提取模型权重
2025-08-01 10:07:09,739 - INFO - [Client 7] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:09,739 - INFO - [Client 7] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:09,740 - INFO - [Client 7] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:09,740 - INFO - [Client 7] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:09,742 - INFO - [Client 7] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:09,742 - INFO - [服务器] 🔄 收到客户端 7 的模型更新，模型版本: unknown
2025-08-01 10:07:09,742 - INFO - [服务器] 客户端 7 训练信息 - 样本数: 300, 训练时间: 16.13秒
2025-08-01 10:07:09,742 - INFO - [服务器] 当前缓冲池大小: 4, 全局轮次: 1
2025-08-01 10:07:09,748 - INFO - [Trainer 6] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:09,751 - INFO - [Trainer 5] Batch 9, Loss: 0.8890
2025-08-01 10:07:09,752 - INFO - [客户端权重摘要] 客户端7 | 参数数量: 74, 均值: 0.000906, 最大: 20.000000, 最小: -1.094184
2025-08-01 10:07:09,753 - INFO - 客户端 7 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:09,753 - INFO - ✅ 客户端 7 的更新已加入成功缓冲池，当前池大小: 5
2025-08-01 10:07:09,753 - INFO - 📊 当前缓冲池中的客户端: [1, 2, 4, 3, 7]
2025-08-01 10:07:09,753 - INFO - 🔍 客户端 7 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:09,754 - INFO - 成功处理客户端 7 的更新
2025-08-01 10:07:09,754 - INFO - [Client 7] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:09,754 - INFO - 客户端 7 训练完成
2025-08-01 10:07:09,759 - INFO - [Trainer 9] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:09,766 - INFO - [Trainer 9] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4695
2025-08-01 10:07:09,767 - INFO - [Trainer 6] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2950
2025-08-01 10:07:09,767 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:09,767 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:09,768 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 6, 6]
2025-08-01 10:07:09,768 - INFO - [Trainer 6] 标签样本: [8, 7, 0, 7, 0]
2025-08-01 10:07:09,801 - INFO - [Trainer 10] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:09,804 - INFO - [Trainer 6] Batch 9, Loss: 1.4958
2025-08-01 10:07:09,805 - INFO - [Trainer 9] Batch 9, Loss: 2.0189
2025-08-01 10:07:09,813 - INFO - [Trainer 10] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1543
2025-08-01 10:07:09,814 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:09,815 - INFO - [Trainer 10] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:07:09,940 - INFO - [Trainer 5] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:09,943 - INFO - [Trainer 5] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.4355, Accuracy: 72.00%
2025-08-01 10:07:09,944 - INFO - [Trainer 5] 开始第 2/2 个epoch
2025-08-01 10:07:09,955 - INFO - [Trainer 10] Batch 9, Loss: 0.3240
2025-08-01 10:07:09,960 - INFO - [Trainer 8] Batch 5, Loss: 1.6789
2025-08-01 10:07:09,986 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4458, y=[7, 7, 7, 7, 9]
2025-08-01 10:07:09,986 - INFO - [Trainer 5] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:10,026 - INFO - [Trainer 5] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:10,039 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4570
2025-08-01 10:07:10,039 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:10,040 - INFO - [Trainer 5] 标签样本: [7, 3, 3, 7, 7]
2025-08-01 10:07:10,084 - INFO - [Trainer 9] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:10,085 - INFO - [Trainer 9] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.8458, Accuracy: 80.33%
2025-08-01 10:07:10,086 - INFO - [Trainer 9] 参数 conv1.weight: 平均值=-0.004314, 标准差=0.115427
2025-08-01 10:07:10,088 - INFO - [Trainer 9] 参数 bn1.weight: 平均值=1.000224, 标准差=0.012713
2025-08-01 10:07:10,089 - INFO - [Trainer 9] 参数 bn1.bias: 平均值=0.000515, 标准差=0.009926
2025-08-01 10:07:10,090 - INFO - [Trainer 9] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:10,091 - INFO - [Trainer.get_report] 客户端 9 训练报告 - Loss: 0.8727, Accuracy: 77.17%, 陈旧度: 0
2025-08-01 10:07:10,092 - INFO - [Trainer 9] 训练报告生成完成: Loss=0.8727, Accuracy=77.17%
2025-08-01 10:07:10,094 - INFO - [Client 9] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:10,095 - INFO - [Client 9] 使用训练准确率作为客户端准确率: 0.7717
2025-08-01 10:07:10,096 - INFO - [Client 9] 第 1 轮训练完成，耗时: 16.29秒, 准确率: 0.7717
2025-08-01 10:07:10,098 - INFO - [Client 9] 开始提取模型权重
2025-08-01 10:07:10,103 - INFO - [Client 9] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:10,105 - INFO - [Client 9] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:10,107 - INFO - [Client 9] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:10,107 - INFO - [Client 9] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:10,107 - INFO - [Trainer 6] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:10,107 - INFO - [Client 9] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:10,107 - INFO - [Trainer 6] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.2795, Accuracy: 46.67%
2025-08-01 10:07:10,108 - INFO - [服务器] 🔄 收到客户端 9 的模型更新，模型版本: unknown
2025-08-01 10:07:10,109 - INFO - [Trainer 6] 参数 conv1.weight: 平均值=0.004134, 标准差=0.114595
2025-08-01 10:07:10,109 - INFO - [服务器] 客户端 9 训练信息 - 样本数: 300, 训练时间: 16.29秒
2025-08-01 10:07:10,110 - INFO - [服务器] 当前缓冲池大小: 5, 全局轮次: 1
2025-08-01 10:07:10,111 - INFO - [Trainer 6] 参数 bn1.weight: 平均值=0.999359, 标准差=0.012165
2025-08-01 10:07:10,112 - INFO - [Trainer 6] 参数 bn1.bias: 平均值=-0.000577, 标准差=0.007060
2025-08-01 10:07:10,112 - INFO - [Trainer 6] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:10,113 - INFO - [Trainer.get_report] 客户端 6 训练报告 - Loss: 1.4475, Accuracy: 46.00%, 陈旧度: 0
2025-08-01 10:07:10,114 - INFO - [Trainer 6] 训练报告生成完成: Loss=1.4475, Accuracy=46.00%
2025-08-01 10:07:10,116 - INFO - [Client 6] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:10,117 - INFO - [Client 6] 使用训练准确率作为客户端准确率: 0.4600
2025-08-01 10:07:10,117 - INFO - [Client 6] 第 1 轮训练完成，耗时: 16.02秒, 准确率: 0.4600
2025-08-01 10:07:10,117 - INFO - [Client 6] 开始提取模型权重
2025-08-01 10:07:10,121 - INFO - [Client 6] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:10,123 - INFO - [Client 6] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:10,123 - INFO - [Client 6] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:10,125 - INFO - [客户端权重摘要] 客户端9 | 参数数量: 74, 均值: 0.001390, 最大: 20.000000, 最小: -1.937029
2025-08-01 10:07:10,125 - INFO - [Client 6] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:10,125 - INFO - 客户端 9 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:10,126 - INFO - [Client 6] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:10,126 - INFO - ✅ 客户端 9 的更新已加入成功缓冲池，当前池大小: 6
2025-08-01 10:07:10,126 - INFO - [服务器] 🔄 收到客户端 6 的模型更新，模型版本: unknown
2025-08-01 10:07:10,127 - INFO - 📊 当前缓冲池中的客户端: [1, 2, 4, 3, 7, 9]
2025-08-01 10:07:10,127 - INFO - [服务器] 客户端 6 训练信息 - 样本数: 300, 训练时间: 16.02秒
2025-08-01 10:07:10,128 - INFO - 🔍 客户端 9 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:10,128 - INFO - [服务器] 当前缓冲池大小: 6, 全局轮次: 1
2025-08-01 10:07:10,129 - INFO - 成功处理客户端 9 的更新
2025-08-01 10:07:10,129 - INFO - [Trainer 5] Batch 0, Loss: 0.8980
2025-08-01 10:07:10,131 - INFO - [Client 9] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:10,131 - INFO - 客户端 9 训练完成
2025-08-01 10:07:10,139 - INFO - [客户端权重摘要] 客户端6 | 参数数量: 74, 均值: 0.001475, 最大: 20.000000, 最小: -1.204857
2025-08-01 10:07:10,140 - INFO - 客户端 6 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:10,140 - INFO - ✅ 客户端 6 的更新已加入成功缓冲池，当前池大小: 7
2025-08-01 10:07:10,140 - INFO - 📊 当前缓冲池中的客户端: [1, 2, 4, 3, 7, 9, 6]
2025-08-01 10:07:10,141 - INFO - 🔍 客户端 6 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:10,141 - INFO - 成功处理客户端 6 的更新
2025-08-01 10:07:10,142 - INFO - [Client 6] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:10,142 - INFO - 客户端 6 训练完成
2025-08-01 10:07:10,172 - INFO - [Trainer 10] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:10,172 - INFO - [Trainer 10] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.8087, Accuracy: 73.67%
2025-08-01 10:07:10,173 - INFO - [Trainer 10] 参数 conv1.weight: 平均值=0.006811, 标准差=0.113745
2025-08-01 10:07:10,174 - INFO - [Trainer 10] 参数 bn1.weight: 平均值=0.999794, 标准差=0.007987
2025-08-01 10:07:10,174 - INFO - [Trainer 10] 参数 bn1.bias: 平均值=0.000584, 标准差=0.007759
2025-08-01 10:07:10,176 - INFO - [Trainer 10] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:10,176 - INFO - [Trainer.get_report] 客户端 10 训练报告 - Loss: 1.0465, Accuracy: 71.83%, 陈旧度: 0
2025-08-01 10:07:10,176 - INFO - [Trainer 10] 训练报告生成完成: Loss=1.0465, Accuracy=71.83%
2025-08-01 10:07:10,177 - INFO - [Client 10] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:10,177 - INFO - [Client 10] 使用训练准确率作为客户端准确率: 0.7183
2025-08-01 10:07:10,177 - INFO - [Client 10] 第 1 轮训练完成，耗时: 16.16秒, 准确率: 0.7183
2025-08-01 10:07:10,178 - INFO - [Client 10] 开始提取模型权重
2025-08-01 10:07:10,181 - INFO - [Client 10] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:10,181 - INFO - [Client 10] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:10,182 - INFO - [Client 10] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:10,182 - INFO - [Client 10] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:10,182 - INFO - [Client 10] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:10,184 - INFO - [服务器] 🔄 收到客户端 10 的模型更新，模型版本: unknown
2025-08-01 10:07:10,184 - INFO - [服务器] 客户端 10 训练信息 - 样本数: 300, 训练时间: 16.16秒
2025-08-01 10:07:10,184 - INFO - [服务器] 当前缓冲池大小: 7, 全局轮次: 1
2025-08-01 10:07:10,203 - INFO - [客户端权重摘要] 客户端10 | 参数数量: 74, 均值: 0.001190, 最大: 20.000000, 最小: -1.852864
2025-08-01 10:07:10,204 - INFO - 客户端 10 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:10,205 - INFO - ✅ 客户端 10 的更新已加入成功缓冲池，当前池大小: 8
2025-08-01 10:07:10,207 - INFO - 📊 当前缓冲池中的客户端: [1, 2, 4, 3, 7, 9, 6, 10]
2025-08-01 10:07:10,209 - INFO - 🔍 客户端 10 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:10,209 - INFO - 成功处理客户端 10 的更新
2025-08-01 10:07:10,212 - INFO - [Client 10] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:10,213 - INFO - 客户端 10 训练完成
2025-08-01 10:07:11,136 - INFO - [Trainer 8] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:11,142 - INFO - [Trainer 8] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2325
2025-08-01 10:07:11,142 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:11,142 - INFO - [Trainer 8] 标签样本: [0, 0, 0, 6, 0]
2025-08-01 10:07:11,177 - INFO - [Trainer 8] Batch 9, Loss: 0.7506
2025-08-01 10:07:11,335 - INFO - [Trainer 8] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:11,335 - INFO - [Trainer 8] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.3701, Accuracy: 58.67%
2025-08-01 10:07:11,336 - INFO - [Trainer 8] 参数 conv1.weight: 平均值=0.001362, 标准差=0.121105
2025-08-01 10:07:11,338 - INFO - [Trainer 8] 参数 bn1.weight: 平均值=1.001016, 标准差=0.014536
2025-08-01 10:07:11,338 - INFO - [Trainer 8] 参数 bn1.bias: 平均值=0.001929, 标准差=0.010550
2025-08-01 10:07:11,338 - INFO - [Trainer 8] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:11,339 - INFO - [Trainer.get_report] 客户端 8 训练报告 - Loss: 1.3991, Accuracy: 54.50%, 陈旧度: 0
2025-08-01 10:07:11,339 - INFO - [Trainer 8] 训练报告生成完成: Loss=1.3991, Accuracy=54.50%
2025-08-01 10:07:11,339 - INFO - [Client 8] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:11,340 - INFO - [Client 8] 使用训练准确率作为客户端准确率: 0.5450
2025-08-01 10:07:11,340 - INFO - [Client 8] 第 1 轮训练完成，耗时: 14.16秒, 准确率: 0.5450
2025-08-01 10:07:11,341 - INFO - [Client 8] 开始提取模型权重
2025-08-01 10:07:11,342 - INFO - [Client 8] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:11,343 - INFO - [Client 8] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:11,343 - INFO - [Client 8] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:11,343 - INFO - [Client 8] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:11,343 - INFO - [Client 8] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:11,344 - INFO - [服务器] 🔄 收到客户端 8 的模型更新，模型版本: unknown
2025-08-01 10:07:11,344 - INFO - [服务器] 客户端 8 训练信息 - 样本数: 300, 训练时间: 14.16秒
2025-08-01 10:07:11,344 - INFO - [服务器] 当前缓冲池大小: 8, 全局轮次: 1
2025-08-01 10:07:11,351 - INFO - [客户端权重摘要] 客户端8 | 参数数量: 74, 均值: 0.001433, 最大: 20.000000, 最小: -1.700373
2025-08-01 10:07:11,351 - INFO - 客户端 8 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:11,353 - INFO - ✅ 客户端 8 的更新已加入成功缓冲池，当前池大小: 9
2025-08-01 10:07:11,353 - INFO - 📊 当前缓冲池中的客户端: [1, 2, 4, 3, 7, 9, 6, 10, 8]
2025-08-01 10:07:11,353 - INFO - 🔍 客户端 8 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:11,354 - INFO - 成功处理客户端 8 的更新
2025-08-01 10:07:11,354 - INFO - [Client 8] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:11,354 - INFO - 客户端 8 训练完成
2025-08-01 10:07:11,576 - INFO - [Trainer 5] Batch 5, Loss: 0.4999
2025-08-01 10:07:12,509 - INFO - [Trainer 5] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:12,516 - INFO - [Trainer 5] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4033
2025-08-01 10:07:12,516 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:12,516 - INFO - [Trainer 5] 标签样本: [7, 7, 3, 3, 3]
2025-08-01 10:07:12,542 - INFO - [Trainer 5] Batch 9, Loss: 1.8850
2025-08-01 10:07:12,658 - INFO - [Trainer 5] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:12,659 - INFO - [Trainer 5] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.8783, Accuracy: 71.67%
2025-08-01 10:07:12,659 - INFO - [Trainer 5] 参数 conv1.weight: 平均值=-0.004429, 标准差=0.114019
2025-08-01 10:07:12,659 - INFO - [Trainer 5] 参数 bn1.weight: 平均值=1.000024, 标准差=0.013568
2025-08-01 10:07:12,659 - INFO - [Trainer 5] 参数 bn1.bias: 平均值=0.001308, 标准差=0.008907
2025-08-01 10:07:12,660 - INFO - [Trainer 5] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:12,660 - INFO - [Trainer.get_report] 客户端 5 训练报告 - Loss: 1.1569, Accuracy: 71.83%, 陈旧度: 0
2025-08-01 10:07:12,660 - INFO - [Trainer 5] 训练报告生成完成: Loss=1.1569, Accuracy=71.83%
2025-08-01 10:07:12,660 - INFO - [Client 5] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:12,660 - INFO - [Client 5] 使用训练准确率作为客户端准确率: 0.7183
2025-08-01 10:07:12,660 - INFO - [Client 5] 第 1 轮训练完成，耗时: 11.67秒, 准确率: 0.7183
2025-08-01 10:07:12,660 - INFO - [Client 5] 开始提取模型权重
2025-08-01 10:07:12,662 - INFO - [Client 5] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:12,662 - INFO - [Client 5] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:12,662 - INFO - [Client 5] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:12,662 - INFO - [Client 5] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:12,663 - INFO - [Client 5] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:12,663 - INFO - [服务器] 🔄 收到客户端 5 的模型更新，模型版本: unknown
2025-08-01 10:07:12,663 - INFO - [服务器] 客户端 5 训练信息 - 样本数: 300, 训练时间: 11.67秒
2025-08-01 10:07:12,663 - INFO - [服务器] 当前缓冲池大小: 9, 全局轮次: 1
2025-08-01 10:07:12,666 - INFO - [客户端权重摘要] 客户端5 | 参数数量: 74, 均值: 0.001072, 最大: 20.000000, 最小: -1.532277
2025-08-01 10:07:12,669 - INFO - 客户端 5 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:12,669 - INFO - ✅ 客户端 5 的更新已加入成功缓冲池，当前池大小: 10
2025-08-01 10:07:12,669 - INFO - 📊 当前缓冲池中的客户端: [1, 2, 4, 3, 7, 9, 6, 10, 8, 5]
2025-08-01 10:07:12,669 - INFO - 🔍 客户端 5 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:12,670 - INFO - 成功处理客户端 5 的更新
2025-08-01 10:07:12,670 - INFO - [Client 5] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:12,670 - INFO - 客户端 5 训练完成
2025-08-01 10:07:20,550 - INFO - [Trainer None] 测试结果 - 准确率: 0.1000 (1000/10000), 损失: 2707.4599
2025-08-01 10:07:20,550 - INFO - 全局模型评估完成，准确率: 0.1000, 耗时: 14.25秒
2025-08-01 10:07:20,550 - WARNING - log_accuracy: 训练器的模型引用不一致，更新为当前全局模型
2025-08-01 10:07:20,550 - INFO - log_accuracy: 已同步训练器的模型引用
2025-08-01 10:07:20,550 - INFO - log_accuracy: 为训练器设置client_id=0
2025-08-01 10:07:20,551 - INFO - log_accuracy: 模型输入通道数: 3
2025-08-01 10:07:20,551 - ERROR - 准确率CSV文件未初始化，跳过记录
2025-08-01 10:07:20,553 - INFO - 创建准确率记录文件: ./results/scafl/mnist/accuracy_20250801_100720.csv
2025-08-01 10:07:20,553 - INFO - 轮次 1 准确率记录 - 当前: 10.00%, 平均(最近10轮): 10.00%, 最佳: 10.00%
2025-08-01 10:07:20,554 - INFO - 轮次: 1, 准确率: 0.1, 平均陈旧度: 0.0
2025-08-01 10:07:20,554 - INFO - 客户端 1 陈旧度: 0
2025-08-01 10:07:20,558 - INFO - [全局模型摘要] 轮次: 1 | 均值: 0.001558, 最大: 25.884436, 最小: -1.477074
2025-08-01 10:07:20,559 - INFO - [DEBUG] 结果记录时 - wall_time=1754014004.9279244, initial_wall_time=1754014004.0022285, elapsed_time=0.9256958961486816
2025-08-01 10:07:20,559 - INFO - 参与聚合的客户端平均准确率: 0.3983 (基于 1 个客户端)
2025-08-01 10:07:20,560 - INFO - 所有训练统计信息已记录到统一结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_100644.csv
2025-08-01 10:07:20,560 - INFO - 记录数据: {'round': 0, 'elapsed_time': 0.9256958961486816, 'accuracy': 0.3983333333333333, 'global_accuracy': 0.1, 'global_accuracy_std': 0.0, 'avg_staleness': 0.0, 'max_staleness': 0, 'min_staleness': 0, 'virtual_time': 0.0, 'aggregated_clients_count': 1}
2025-08-01 10:07:20,560 - DEBUG - 聚合后客户端 1 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 10:07:20,560 - DEBUG - 聚合后客户端 2 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 10:07:20,560 - DEBUG - 聚合后客户端 3 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 10:07:20,560 - DEBUG - 聚合后客户端 4 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 10:07:20,560 - DEBUG - 聚合后客户端 5 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 10:07:20,560 - DEBUG - 聚合后客户端 6 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 10:07:20,560 - DEBUG - 聚合后客户端 7 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 10:07:20,560 - DEBUG - 聚合后客户端 8 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 10:07:20,560 - DEBUG - 聚合后客户端 9 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 10:07:20,560 - DEBUG - 聚合后客户端 10 陈旧度: 1, Q_k 更新为: 0.0000
2025-08-01 10:07:20,560 - INFO - 聚合完成，缓冲池中还剩 9 个更新，来自 9 个不同客户端
2025-08-01 10:07:20,560 - INFO - 🔓 聚合过程结束，轮次: 1
2025-08-01 10:07:20,560 - INFO - 🎉 第 0 轮聚合完成，当前轮次: 1
2025-08-01 10:07:20,560 - INFO - 🔄 聚合完成，重新启动客户端训练（第 1 轮）
2025-08-01 10:07:20,560 - INFO - 缓冲池已清空，准备新一轮训练
2025-08-01 10:07:20,560 - INFO - 重新启动客户端 1 的训练任务
2025-08-01 10:07:20,561 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:20,561 - INFO - 重新启动客户端 2 的训练任务
2025-08-01 10:07:20,561 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 10:07:20,561 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:20,561 - INFO - 重新启动客户端 3 的训练任务
2025-08-01 10:07:20,561 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 10:07:20,562 - INFO - [Client 2] 开始验证训练集
2025-08-01 10:07:20,562 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:20,562 - INFO - 重新启动客户端 4 的训练任务
2025-08-01 10:07:20,562 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 10:07:20,563 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:20,563 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 10:07:20,563 - INFO - 重新启动客户端 5 的训练任务
2025-08-01 10:07:20,563 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 10:07:20,563 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 10:07:20,563 - INFO - [Client 4] 开始验证训练集
2025-08-01 10:07:20,563 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:20,564 - INFO - 重新启动客户端 6 的训练任务
2025-08-01 10:07:20,564 - INFO - [Client 4] 开始验证训练集
2025-08-01 10:07:20,564 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 10:07:20,564 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:20,565 - INFO - 重新启动客户端 7 的训练任务
2025-08-01 10:07:20,565 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 10:07:20,565 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:20,565 - INFO - [Client 6] 开始验证训练集
2025-08-01 10:07:20,566 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:20,566 - INFO - 重新启动客户端 8 的训练任务
2025-08-01 10:07:20,566 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 10:07:20,566 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:20,566 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 10:07:20,566 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:20,567 - INFO - 重新启动客户端 9 的训练任务
2025-08-01 10:07:20,567 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 10:07:20,567 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 10:07:20,567 - INFO - [Trainer 2] 开始训练
2025-08-01 10:07:20,567 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 10:07:20,567 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:20,568 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:20,568 - INFO - [Client 8] 开始验证训练集
2025-08-01 10:07:20,568 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:20,568 - INFO - 重新启动客户端 10 的训练任务
2025-08-01 10:07:20,568 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:07:20,568 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 10:07:20,569 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 10:07:20,569 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:20,569 - INFO - [Client 8] 开始验证训练集
2025-08-01 10:07:20,569 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:20,569 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:20,569 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:07:20,569 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:07:20,569 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:20,569 - INFO - ✅ 已重新启动 10 个客户端的训练任务
2025-08-01 10:07:20,569 - INFO - [Client 9] 开始验证训练集
2025-08-01 10:07:20,570 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:20,570 - INFO - [Trainer 4] 开始训练
2025-08-01 10:07:20,571 - INFO - [Trainer 4] 开始训练
2025-08-01 10:07:20,571 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:07:20,571 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:07:20,571 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:20,571 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:07:20,572 - INFO - [Trainer 6] 开始训练
2025-08-01 10:07:20,572 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 10:07:20,572 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 10:07:20,572 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:07:20,572 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:07:20,573 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:20,573 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:20,573 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 10:07:20,574 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 10:07:20,574 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:20,574 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:07:20,574 - INFO - [Trainer 8] 开始训练
2025-08-01 10:07:20,575 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:20,575 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:20,576 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 10:07:20,576 - INFO - [Trainer 8] 开始训练
2025-08-01 10:07:20,576 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:20,576 - INFO - [Trainer 9] 开始训练
2025-08-01 10:07:20,577 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 10:07:20,577 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:20,578 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 10:07:20,578 - INFO - [Trainer 10] 开始训练
2025-08-01 10:07:20,579 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:20,579 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:07:20,579 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:20,579 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:20,580 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:20,580 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:20,581 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:20,581 - INFO - [Trainer 10] 开始训练
2025-08-01 10:07:20,581 - INFO - [Trainer 10] 开始训练
2025-08-01 10:07:20,582 - INFO - [Trainer 2] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:20,582 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:07:20,582 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:07:20,582 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:07:20,583 - INFO - [Trainer 2] 开始第 1/2 个epoch
2025-08-01 10:07:20,584 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 10:07:20,585 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:07:20,585 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:07:20,586 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:20,586 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:20,588 - INFO - [Trainer 4] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:20,588 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:07:20,588 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 10:07:20,588 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:20,588 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:20,589 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:07:20,589 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:20,589 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:20,589 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:20,590 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:20,590 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:07:20,591 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:20,591 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:20,592 - INFO - [Trainer 8] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:20,592 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 10:07:20,592 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:07:20,592 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:20,593 - INFO - [Trainer 6] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:20,593 - INFO - [Trainer 4] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:20,593 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:20,593 - INFO - [Trainer 6] 开始第 1/2 个epoch
2025-08-01 10:07:20,593 - INFO - [Trainer 8] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:20,593 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:20,593 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 10:07:20,594 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 10:07:20,594 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:20,595 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:20,595 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:07:20,595 - INFO - [Trainer 10] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:20,596 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:20,596 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:20,597 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:07:20,598 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:20,598 - INFO - [Trainer 9] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:20,599 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 10:07:20,599 - INFO - [Trainer 10] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:20,600 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:07:20,600 - INFO - [Trainer 10] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:20,600 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:07:20,672 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1682, y=[6, 0, 0, 1, 1]
2025-08-01 10:07:20,672 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:20,674 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3380, y=[6, 0, 1, 0, 0]
2025-08-01 10:07:20,674 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2949, y=[8, 8, 8, 8, 8]
2025-08-01 10:07:20,674 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:20,674 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:20,675 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1851, y=[1, 0, 0, 0, 1]
2025-08-01 10:07:20,676 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2677, y=[8, 8, 8, 6, 4]
2025-08-01 10:07:20,676 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:20,676 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:20,677 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5259, y=[6, 6, 6, 6, 6]
2025-08-01 10:07:20,677 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:20,678 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0264, y=[0, 7, 0, 0, 0]
2025-08-01 10:07:20,678 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:20,679 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2665, y=[8, 6, 8, 8, 8]
2025-08-01 10:07:20,680 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:20,680 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2986, y=[6, 8, 8, 8, 8]
2025-08-01 10:07:20,681 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:20,683 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3600, y=[1, 1, 0, 5, 0]
2025-08-01 10:07:20,683 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:20,729 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:20,734 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:20,739 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:20,740 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:20,740 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:20,741 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:20,741 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:20,741 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:20,742 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:20,742 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:20,747 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1578
2025-08-01 10:07:20,748 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4361
2025-08-01 10:07:20,748 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2579
2025-08-01 10:07:20,748 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2081
2025-08-01 10:07:20,748 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:20,748 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0876
2025-08-01 10:07:20,748 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3479
2025-08-01 10:07:20,748 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2912
2025-08-01 10:07:20,748 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1130
2025-08-01 10:07:20,748 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2617
2025-08-01 10:07:20,748 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:20,748 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2142
2025-08-01 10:07:20,748 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:20,748 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:20,748 - INFO - [Trainer 10] 标签样本: [8, 8, 8, 6, 8]
2025-08-01 10:07:20,749 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:20,749 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:20,749 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:20,749 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:20,749 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:20,749 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 6, 8]
2025-08-01 10:07:20,749 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:20,749 - INFO - [Trainer 8] 标签样本: [6, 0, 0, 7, 0]
2025-08-01 10:07:20,749 - INFO - [Trainer 8] 标签样本: [1, 6, 0, 6, 0]
2025-08-01 10:07:20,750 - INFO - [Trainer 4] 标签样本: [1, 1, 0, 5, 4]
2025-08-01 10:07:20,750 - INFO - [Trainer 10] 标签样本: [8, 6, 8, 8, 8]
2025-08-01 10:07:20,751 - INFO - [Trainer 4] 标签样本: [1, 0, 1, 1, 1]
2025-08-01 10:07:20,751 - INFO - [Trainer 6] 标签样本: [7, 7, 0, 0, 8]
2025-08-01 10:07:20,751 - INFO - [Trainer 10] 标签样本: [8, 6, 8, 6, 8]
2025-08-01 10:07:20,751 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 5]
2025-08-01 10:07:21,034 - INFO - [Trainer 4] Batch 0, Loss: 0.7438
2025-08-01 10:07:21,047 - INFO - [Trainer 6] Batch 0, Loss: 1.1599
2025-08-01 10:07:21,057 - INFO - [Trainer 9] Batch 0, Loss: 0.2187
2025-08-01 10:07:21,059 - INFO - [Trainer 8] Batch 0, Loss: 0.7569
2025-08-01 10:07:21,063 - INFO - [Trainer 10] Batch 0, Loss: 0.5735
2025-08-01 10:07:21,068 - INFO - [Trainer 4] Batch 0, Loss: 0.7330
2025-08-01 10:07:21,082 - INFO - [Trainer 8] Batch 0, Loss: 1.0641
2025-08-01 10:07:21,099 - INFO - [Trainer 10] Batch 0, Loss: 0.6305
2025-08-01 10:07:21,101 - INFO - [Trainer 10] Batch 0, Loss: 0.1988
2025-08-01 10:07:21,118 - INFO - [Trainer 2] Batch 0, Loss: 0.1818
2025-08-01 10:07:21,585 - INFO - 🚀 开始第 2 轮训练（目标：20 轮）
2025-08-01 10:07:21,588 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:21,593 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:21,657 - ERROR - [Trainer 4] 训练过程出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 21; expected version 20 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:21,751 - ERROR - [Trainer 4] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 21; expected version 20 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:21,753 - ERROR - [Client 4] 训练器训练过程中出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 21; expected version 20 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:21,756 - ERROR - [Client 4] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 21; expected version 20 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:22,611 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:22,614 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:23,411 - ERROR - [Trainer 8] 训练过程出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 25; expected version 24 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:23,419 - ERROR - [Trainer 8] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 25; expected version 24 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:23,422 - ERROR - [Client 8] 训练器训练过程中出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 25; expected version 24 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:23,423 - ERROR - [Client 8] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 25; expected version 24 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:23,625 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:23,626 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:24,185 - ERROR - [Trainer 10] 训练过程出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 30; expected version 29 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:24,189 - ERROR - [Trainer 10] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 30; expected version 29 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:24,192 - ERROR - [Client 10] 训练器训练过程中出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 30; expected version 29 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:24,195 - ERROR - [Client 10] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 30; expected version 29 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:24,196 - ERROR - [Trainer 10] 训练过程出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 30; expected version 29 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:24,198 - ERROR - [Trainer 10] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 30; expected version 29 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:24,204 - ERROR - [Client 10] 训练器训练过程中出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 30; expected version 29 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:24,205 - ERROR - [Client 10] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 30; expected version 29 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:24,434 - INFO - [Trainer 4] Batch 5, Loss: 1.1542
2025-08-01 10:07:24,495 - INFO - [Trainer 9] Batch 5, Loss: 0.4115
2025-08-01 10:07:24,496 - INFO - [Trainer 6] Batch 5, Loss: 1.0121
2025-08-01 10:07:24,541 - INFO - [Trainer 8] Batch 5, Loss: 1.3460
2025-08-01 10:07:24,642 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:24,647 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:24,853 - INFO - [Trainer 10] Batch 5, Loss: 26.5213
2025-08-01 10:07:25,037 - INFO - [Trainer 2] Batch 5, Loss: 0.1214
2025-08-01 10:07:25,657 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:25,661 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:26,043 - INFO - [Trainer 4] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:26,050 - INFO - [Trainer 4] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.1865
2025-08-01 10:07:26,053 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,053 - INFO - [Trainer 4] 标签样本: [0, 0, 1, 0, 0]
2025-08-01 10:07:26,146 - INFO - [Trainer 4] Batch 9, Loss: 0.6825
2025-08-01 10:07:26,366 - INFO - [Trainer 6] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:26,377 - INFO - [Trainer 6] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2676
2025-08-01 10:07:26,381 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,384 - INFO - [Trainer 6] 标签样本: [8, 0, 0, 0, 8]
2025-08-01 10:07:26,387 - INFO - [Trainer 4] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:26,388 - INFO - [Trainer 4] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.9690, Accuracy: 64.00%
2025-08-01 10:07:26,388 - INFO - [Trainer 4] 开始第 2/2 个epoch
2025-08-01 10:07:26,411 - INFO - [Trainer 6] Batch 9, Loss: 1.5605
2025-08-01 10:07:26,416 - INFO - [Trainer 9] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:26,421 - INFO - [Trainer 9] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.6172, x.mean: -0.4642
2025-08-01 10:07:26,422 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,422 - INFO - [Trainer 9] 标签样本: [8, 6, 6, 6, 6]
2025-08-01 10:07:26,427 - INFO - [Trainer 10] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:26,428 - INFO - [Trainer 8] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:26,437 - INFO - [Trainer 10] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2888
2025-08-01 10:07:26,437 - INFO - [Trainer 8] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0585
2025-08-01 10:07:26,437 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,438 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,438 - INFO - [Trainer 10] 标签样本: [8, 6, 4, 8, 6]
2025-08-01 10:07:26,438 - INFO - [Trainer 8] 标签样本: [1, 1, 0, 0, 6]
2025-08-01 10:07:26,447 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2729, y=[5, 0, 0, 0, 0]
2025-08-01 10:07:26,448 - INFO - [Trainer 4] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:26,449 - INFO - [Trainer 9] Batch 9, Loss: 0.2802
2025-08-01 10:07:26,479 - INFO - [Trainer 4] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:26,484 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1050
2025-08-01 10:07:26,486 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,486 - INFO - [Trainer 4] 标签样本: [0, 1, 1, 4, 1]
2025-08-01 10:07:26,488 - INFO - [Trainer 10] Batch 9, Loss: 1.9859
2025-08-01 10:07:26,490 - INFO - [Trainer 8] Batch 9, Loss: 0.7525
2025-08-01 10:07:26,519 - INFO - [Trainer 6] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:26,519 - INFO - [Trainer 6] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.2216, Accuracy: 49.00%
2025-08-01 10:07:26,519 - INFO - [Trainer 6] 开始第 2/2 个epoch
2025-08-01 10:07:26,542 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0651, y=[7, 0, 0, 8, 7]
2025-08-01 10:07:26,543 - INFO - [Trainer 6] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:26,572 - INFO - [Trainer 6] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:26,578 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2012
2025-08-01 10:07:26,579 - INFO - [Trainer 4] Batch 0, Loss: 1.0558
2025-08-01 10:07:26,579 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,579 - INFO - [Trainer 6] 标签样本: [8, 8, 8, 0, 7]
2025-08-01 10:07:26,633 - INFO - [Trainer 9] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:26,635 - INFO - [Trainer 9] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.3933, Accuracy: 88.33%
2025-08-01 10:07:26,637 - INFO - [Trainer 9] 开始第 2/2 个epoch
2025-08-01 10:07:26,671 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:26,672 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:26,680 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3555, y=[6, 6, 6, 8, 8]
2025-08-01 10:07:26,682 - INFO - [Trainer 9] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:26,696 - INFO - [Trainer 10] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:26,697 - INFO - [Trainer 10] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 6.6541, Accuracy: 74.00%
2025-08-01 10:07:26,697 - INFO - [Trainer 6] Batch 0, Loss: 1.0517
2025-08-01 10:07:26,698 - INFO - [Trainer 10] 开始第 2/2 个epoch
2025-08-01 10:07:26,714 - INFO - [Trainer 8] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:26,715 - INFO - [Trainer 8] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.8481, Accuracy: 65.33%
2025-08-01 10:07:26,716 - INFO - [Trainer 8] 开始第 2/2 个epoch
2025-08-01 10:07:26,758 - INFO - [Trainer 9] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:26,766 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7147, x.mean: -0.2837
2025-08-01 10:07:26,767 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,767 - INFO - [Trainer 9] 标签样本: [8, 6, 6, 6, 6]
2025-08-01 10:07:26,776 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3209, y=[6, 8, 8, 7, 6]
2025-08-01 10:07:26,778 - INFO - [Trainer 10] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:26,783 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0328, y=[0, 1, 1, 0, 1]
2025-08-01 10:07:26,784 - INFO - [Trainer 8] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:26,825 - INFO - [Trainer 9] Batch 0, Loss: 0.4962
2025-08-01 10:07:26,837 - INFO - [Trainer 2] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:26,844 - INFO - [Trainer 2] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1772
2025-08-01 10:07:26,844 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,844 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 5]
2025-08-01 10:07:26,855 - INFO - [Trainer 10] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:26,860 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3062
2025-08-01 10:07:26,861 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,863 - INFO - [Trainer 10] 标签样本: [8, 8, 8, 6, 6]
2025-08-01 10:07:26,864 - INFO - [Trainer 8] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:26,875 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1925
2025-08-01 10:07:26,876 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:26,877 - INFO - [Trainer 8] 标签样本: [0, 0, 6, 1, 0]
2025-08-01 10:07:26,910 - INFO - [Trainer 2] Batch 9, Loss: 0.9288
2025-08-01 10:07:26,987 - INFO - [Trainer 10] Batch 0, Loss: 1.6382
2025-08-01 10:07:27,000 - INFO - [Trainer 8] Batch 0, Loss: 0.7337
2025-08-01 10:07:27,172 - INFO - [Trainer 2] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:27,173 - INFO - [Trainer 2] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.2954, Accuracy: 95.67%
2025-08-01 10:07:27,173 - INFO - [Trainer 2] 开始第 2/2 个epoch
2025-08-01 10:07:27,202 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0373, y=[8, 8, 8, 8, 8]
2025-08-01 10:07:27,207 - INFO - [Trainer 2] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:27,251 - INFO - [Trainer 2] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:27,268 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1276
2025-08-01 10:07:27,269 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:27,270 - INFO - [Trainer 2] 标签样本: [8, 5, 8, 8, 8]
2025-08-01 10:07:27,334 - INFO - [Trainer 2] Batch 0, Loss: 0.2953
2025-08-01 10:07:27,693 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:27,699 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:28,710 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:28,713 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:29,069 - INFO - [Trainer 4] Batch 5, Loss: 0.9685
2025-08-01 10:07:29,106 - INFO - [Trainer 9] Batch 5, Loss: 0.4754
2025-08-01 10:07:29,108 - INFO - [Trainer 6] Batch 5, Loss: 0.7853
2025-08-01 10:07:29,646 - INFO - [Trainer 10] Batch 5, Loss: 3.6019
2025-08-01 10:07:29,653 - INFO - [Trainer 2] Batch 5, Loss: 0.1144
2025-08-01 10:07:29,662 - INFO - [Trainer 8] Batch 5, Loss: 0.8496
2025-08-01 10:07:29,725 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:29,728 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:30,738 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:30,740 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:31,150 - INFO - [Trainer 4] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:31,157 - INFO - [Trainer 9] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:31,160 - INFO - [Trainer 9] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.7613
2025-08-01 10:07:31,160 - INFO - [Trainer 4] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3690
2025-08-01 10:07:31,161 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:31,161 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:31,161 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 6, 6]
2025-08-01 10:07:31,161 - INFO - [Trainer 4] 标签样本: [0, 0, 0, 1, 0]
2025-08-01 10:07:31,187 - INFO - [Trainer 4] Batch 9, Loss: 1.6812
2025-08-01 10:07:31,187 - INFO - [Trainer 9] Batch 9, Loss: 0.5702
2025-08-01 10:07:31,188 - INFO - [Trainer 6] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:31,193 - INFO - [Trainer 6] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3145
2025-08-01 10:07:31,195 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:31,195 - INFO - [Trainer 6] 标签样本: [7, 8, 0, 8, 0]
2025-08-01 10:07:31,271 - INFO - [Trainer 6] Batch 9, Loss: 1.1000
2025-08-01 10:07:31,363 - INFO - [Trainer 9] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:31,364 - INFO - [Trainer 4] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:31,365 - INFO - [Trainer 9] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.4300, Accuracy: 87.33%
2025-08-01 10:07:31,366 - INFO - [Trainer 4] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.0073, Accuracy: 62.00%
2025-08-01 10:07:31,367 - INFO - [Trainer 9] 参数 conv1.weight: 平均值=-0.003899, 标准差=0.116032
2025-08-01 10:07:31,367 - INFO - [Trainer 4] 参数 conv1.weight: 平均值=0.001076, 标准差=0.118880
2025-08-01 10:07:31,369 - INFO - [Trainer 9] 参数 bn1.weight: 平均值=0.999837, 标准差=0.014139
2025-08-01 10:07:31,371 - INFO - [Trainer 4] 参数 bn1.weight: 平均值=0.999309, 标准差=0.016866
2025-08-01 10:07:31,371 - INFO - [Trainer 9] 参数 bn1.bias: 平均值=0.001152, 标准差=0.013347
2025-08-01 10:07:31,372 - INFO - [Trainer 4] 参数 bn1.bias: 平均值=-0.001766, 标准差=0.014365
2025-08-01 10:07:31,372 - INFO - [Trainer 9] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:31,375 - INFO - [Trainer 4] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:31,375 - INFO - [Trainer.get_report] 客户端 9 训练报告 - Loss: 0.6422, Accuracy: 82.50%, 陈旧度: 0
2025-08-01 10:07:31,375 - INFO - [Trainer 9] 训练报告生成完成: Loss=0.6422, Accuracy=82.50%
2025-08-01 10:07:31,376 - INFO - [Client 9] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:31,377 - INFO - [Trainer.get_report] 客户端 4 训练报告 - Loss: 1.2207, Accuracy: 57.83%, 陈旧度: 0
2025-08-01 10:07:31,378 - INFO - [Client 9] 使用训练准确率作为客户端准确率: 0.8250
2025-08-01 10:07:31,379 - INFO - [Trainer 4] 训练报告生成完成: Loss=1.2207, Accuracy=57.83%
2025-08-01 10:07:31,380 - INFO - [Client 9] 第 2 轮训练完成，耗时: 10.81秒, 准确率: 0.8250
2025-08-01 10:07:31,380 - INFO - [Client 4] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:31,380 - INFO - [Client 9] 开始提取模型权重
2025-08-01 10:07:31,381 - INFO - [Client 4] 使用训练准确率作为客户端准确率: 0.5783
2025-08-01 10:07:31,381 - INFO - [Client 4] 第 2 轮训练完成，耗时: 10.82秒, 准确率: 0.5783
2025-08-01 10:07:31,383 - INFO - [Client 4] 开始提取模型权重
2025-08-01 10:07:31,383 - INFO - [Client 9] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:31,384 - INFO - [Client 9] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:31,384 - INFO - [Client 9] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:31,385 - INFO - [Client 9] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:31,385 - INFO - [Client 9] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:31,385 - INFO - [服务器] 🔄 收到客户端 9 的模型更新，模型版本: unknown
2025-08-01 10:07:31,386 - INFO - [Client 4] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:31,386 - INFO - [服务器] 客户端 9 训练信息 - 样本数: 300, 训练时间: 10.81秒
2025-08-01 10:07:31,386 - INFO - [服务器] 当前缓冲池大小: 0, 全局轮次: 1
2025-08-01 10:07:31,386 - INFO - [Client 4] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:31,387 - INFO - [Client 4] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:31,387 - INFO - [Client 4] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:31,388 - INFO - [Client 4] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:31,388 - INFO - [服务器] 🔄 收到客户端 4 的模型更新，模型版本: unknown
2025-08-01 10:07:31,388 - INFO - [服务器] 客户端 4 训练信息 - 样本数: 300, 训练时间: 10.82秒
2025-08-01 10:07:31,388 - INFO - [服务器] 当前缓冲池大小: 0, 全局轮次: 1
2025-08-01 10:07:31,400 - INFO - [客户端权重摘要] 客户端9 | 参数数量: 74, 均值: 0.001813, 最大: 26.938467, 最小: -2.711030
2025-08-01 10:07:31,401 - INFO - 客户端 9 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:31,401 - INFO - ✅ 客户端 9 的更新已加入成功缓冲池，当前池大小: 1
2025-08-01 10:07:31,401 - INFO - 📊 当前缓冲池中的客户端: [9]
2025-08-01 10:07:31,401 - INFO - 🔍 客户端 9 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:31,401 - INFO - 成功处理客户端 9 的更新
2025-08-01 10:07:31,401 - INFO - [Client 9] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:31,403 - INFO - [客户端权重摘要] 客户端4 | 参数数量: 74, 均值: 0.002195, 最大: 50.843697, 最小: -2.973051
2025-08-01 10:07:31,403 - INFO - 客户端 4 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:31,403 - INFO - ✅ 客户端 4 的更新已加入成功缓冲池，当前池大小: 2
2025-08-01 10:07:31,404 - INFO - 📊 当前缓冲池中的客户端: [9, 4]
2025-08-01 10:07:31,404 - INFO - 🔍 客户端 4 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:31,406 - INFO - 成功处理客户端 4 的更新
2025-08-01 10:07:31,406 - INFO - [Client 4] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:31,483 - INFO - [Trainer 6] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:31,486 - INFO - [Trainer 6] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.0293, Accuracy: 53.67%
2025-08-01 10:07:31,487 - INFO - [Trainer 6] 参数 conv1.weight: 平均值=0.004351, 标准差=0.115585
2025-08-01 10:07:31,488 - INFO - [Trainer 6] 参数 bn1.weight: 平均值=0.998563, 标准差=0.011521
2025-08-01 10:07:31,489 - INFO - [Trainer 6] 参数 bn1.bias: 平均值=-0.001881, 标准差=0.008995
2025-08-01 10:07:31,490 - INFO - [Trainer 6] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:31,490 - INFO - [Trainer.get_report] 客户端 6 训练报告 - Loss: 1.2865, Accuracy: 48.67%, 陈旧度: 0
2025-08-01 10:07:31,490 - INFO - [Trainer 6] 训练报告生成完成: Loss=1.2865, Accuracy=48.67%
2025-08-01 10:07:31,491 - INFO - [Client 6] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:31,491 - INFO - [Client 6] 使用训练准确率作为客户端准确率: 0.4867
2025-08-01 10:07:31,492 - INFO - [Client 6] 第 2 轮训练完成，耗时: 10.93秒, 准确率: 0.4867
2025-08-01 10:07:31,492 - INFO - [Client 6] 开始提取模型权重
2025-08-01 10:07:31,493 - INFO - [Client 6] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:31,494 - INFO - [Client 6] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:31,494 - INFO - [Client 6] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:31,495 - INFO - [Client 6] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:31,495 - INFO - [Client 6] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:31,495 - INFO - [服务器] 🔄 收到客户端 6 的模型更新，模型版本: unknown
2025-08-01 10:07:31,495 - INFO - [服务器] 客户端 6 训练信息 - 样本数: 300, 训练时间: 10.93秒
2025-08-01 10:07:31,495 - INFO - [服务器] 当前缓冲池大小: 2, 全局轮次: 1
2025-08-01 10:07:31,507 - INFO - [客户端权重摘要] 客户端6 | 参数数量: 74, 均值: 0.002187, 最大: 26.201868, 最小: -1.737565
2025-08-01 10:07:31,507 - INFO - 客户端 6 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:31,508 - INFO - ✅ 客户端 6 的更新已加入成功缓冲池，当前池大小: 3
2025-08-01 10:07:31,508 - INFO - 📊 当前缓冲池中的客户端: [9, 4, 6]
2025-08-01 10:07:31,508 - INFO - 🔍 客户端 6 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:31,508 - INFO - 成功处理客户端 6 的更新
2025-08-01 10:07:31,508 - INFO - [Client 6] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:31,540 - INFO - [Trainer 10] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:31,545 - INFO - [Trainer 8] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:31,547 - INFO - [Trainer 2] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:31,549 - INFO - [Trainer 8] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1653
2025-08-01 10:07:31,549 - INFO - [Trainer 10] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4579
2025-08-01 10:07:31,549 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:31,549 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:31,550 - INFO - [Trainer 8] 标签样本: [0, 6, 0, 1, 0]
2025-08-01 10:07:31,550 - INFO - [Trainer 10] 标签样本: [8, 8, 8, 8, 6]
2025-08-01 10:07:31,564 - INFO - [Trainer 2] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2301
2025-08-01 10:07:31,564 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:31,565 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:07:31,573 - INFO - [Trainer 8] Batch 9, Loss: 0.7121
2025-08-01 10:07:31,574 - INFO - [Trainer 10] Batch 9, Loss: 13.3174
2025-08-01 10:07:31,598 - INFO - [Trainer 2] Batch 9, Loss: 0.0062
2025-08-01 10:07:31,649 - INFO - [Trainer 10] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:31,651 - INFO - [Trainer 10] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 5.5039, Accuracy: 72.67%
2025-08-01 10:07:31,652 - INFO - [Trainer 10] 参数 conv1.weight: 平均值=0.043855, 标准差=0.238954
2025-08-01 10:07:31,652 - INFO - [Trainer 10] 参数 bn1.weight: 平均值=0.979501, 标准差=0.147288
2025-08-01 10:07:31,652 - INFO - [Trainer 10] 参数 bn1.bias: 平均值=-0.090464, 标准差=0.161712
2025-08-01 10:07:31,653 - INFO - [Trainer 10] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:31,653 - INFO - [Trainer.get_report] 客户端 10 训练报告 - Loss: 3.2453, Accuracy: 73.06%, 陈旧度: 0
2025-08-01 10:07:31,653 - INFO - [Trainer 8] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:31,653 - INFO - [Trainer 10] 训练报告生成完成: Loss=3.2453, Accuracy=73.06%
2025-08-01 10:07:31,653 - INFO - [Trainer 8] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.7675, Accuracy: 71.67%
2025-08-01 10:07:31,654 - INFO - [Client 10] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:31,654 - INFO - [Client 10] 使用训练准确率作为客户端准确率: 0.7306
2025-08-01 10:07:31,654 - INFO - [Trainer 8] 参数 conv1.weight: 平均值=0.000498, 标准差=0.122138
2025-08-01 10:07:31,654 - INFO - [Client 10] 第 4 轮训练完成，耗时: 11.08秒, 准确率: 0.7306
2025-08-01 10:07:31,654 - INFO - [Client 10] 开始提取模型权重
2025-08-01 10:07:31,654 - INFO - [Trainer 8] 参数 bn1.weight: 平均值=0.999763, 标准差=0.016857
2025-08-01 10:07:31,655 - INFO - [Trainer 8] 参数 bn1.bias: 平均值=0.002792, 标准差=0.012091
2025-08-01 10:07:31,655 - INFO - [Trainer 8] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:31,655 - INFO - [Trainer.get_report] 客户端 8 训练报告 - Loss: 1.0947, Accuracy: 61.55%, 陈旧度: 0
2025-08-01 10:07:31,655 - INFO - [Trainer 8] 训练报告生成完成: Loss=1.0947, Accuracy=61.55%
2025-08-01 10:07:31,655 - INFO - [Client 10] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:31,656 - INFO - [Client 8] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:31,656 - INFO - [Client 10] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:31,656 - INFO - [Client 8] 使用训练准确率作为客户端准确率: 0.6155
2025-08-01 10:07:31,656 - INFO - [Client 10] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:31,656 - INFO - [Client 8] 第 2 轮训练完成，耗时: 11.09秒, 准确率: 0.6155
2025-08-01 10:07:31,656 - INFO - [Client 10] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:31,656 - INFO - [Client 8] 开始提取模型权重
2025-08-01 10:07:31,657 - INFO - [Client 10] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:31,657 - INFO - [服务器] 🔄 收到客户端 10 的模型更新，模型版本: unknown
2025-08-01 10:07:31,657 - INFO - [服务器] 客户端 10 训练信息 - 样本数: 300, 训练时间: 11.08秒
2025-08-01 10:07:31,657 - INFO - [服务器] 当前缓冲池大小: 3, 全局轮次: 1
2025-08-01 10:07:31,658 - INFO - [Client 8] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:31,658 - INFO - [Client 8] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:31,658 - INFO - [Client 8] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:31,658 - INFO - [Client 8] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:31,658 - INFO - [Client 8] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:31,658 - INFO - [服务器] 🔄 收到客户端 8 的模型更新，模型版本: unknown
2025-08-01 10:07:31,659 - INFO - [服务器] 客户端 8 训练信息 - 样本数: 300, 训练时间: 11.09秒
2025-08-01 10:07:31,659 - INFO - [服务器] 当前缓冲池大小: 3, 全局轮次: 1
2025-08-01 10:07:31,661 - INFO - [客户端权重摘要] 客户端10 | 参数数量: 74, 均值: 0.013898, 最大: 1120.160156, 最小: -14.616385
2025-08-01 10:07:31,662 - INFO - 客户端 10 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:31,663 - INFO - ✅ 客户端 10 的更新已加入成功缓冲池，当前池大小: 4
2025-08-01 10:07:31,663 - INFO - 📊 当前缓冲池中的客户端: [9, 4, 6, 10]
2025-08-01 10:07:31,663 - INFO - [客户端权重摘要] 客户端8 | 参数数量: 74, 均值: 0.002284, 最大: 43.382408, 最小: -2.539524
2025-08-01 10:07:31,663 - INFO - 🔍 客户端 10 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:31,663 - INFO - 客户端 8 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:31,664 - INFO - 成功处理客户端 10 的更新
2025-08-01 10:07:31,664 - INFO - ✅ 客户端 8 的更新已加入成功缓冲池，当前池大小: 5
2025-08-01 10:07:31,664 - INFO - [Client 10] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:31,664 - INFO - 📊 当前缓冲池中的客户端: [9, 4, 6, 10, 8]
2025-08-01 10:07:31,664 - INFO - 🔍 客户端 8 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:31,664 - INFO - 成功处理客户端 8 的更新
2025-08-01 10:07:31,666 - INFO - [Client 8] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:31,672 - INFO - [Trainer 2] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:31,672 - INFO - [Trainer 2] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.2011, Accuracy: 95.67%
2025-08-01 10:07:31,673 - INFO - [Trainer 2] 参数 conv1.weight: 平均值=0.016162, 标准差=0.120920
2025-08-01 10:07:31,673 - INFO - [Trainer 2] 参数 bn1.weight: 平均值=0.999969, 标准差=0.022256
2025-08-01 10:07:31,673 - INFO - [Trainer 2] 参数 bn1.bias: 平均值=0.005473, 标准差=0.017029
2025-08-01 10:07:31,673 - INFO - [Trainer 2] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:31,673 - INFO - [Trainer.get_report] 客户端 2 训练报告 - Loss: 0.4105, Accuracy: 95.58%, 陈旧度: 0
2025-08-01 10:07:31,673 - INFO - [Trainer 2] 训练报告生成完成: Loss=0.4105, Accuracy=95.58%
2025-08-01 10:07:31,673 - INFO - [Client 2] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:31,673 - INFO - [Client 2] 使用训练准确率作为客户端准确率: 0.9558
2025-08-01 10:07:31,674 - INFO - [Client 2] 第 2 轮训练完成，耗时: 11.11秒, 准确率: 0.9558
2025-08-01 10:07:31,674 - INFO - [Client 2] 开始提取模型权重
2025-08-01 10:07:31,674 - INFO - [Client 2] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:31,674 - INFO - [Client 2] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:31,674 - INFO - [Client 2] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:31,674 - INFO - [Client 2] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:31,674 - INFO - [Client 2] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:31,674 - INFO - [服务器] 🔄 收到客户端 2 的模型更新，模型版本: unknown
2025-08-01 10:07:31,674 - INFO - [服务器] 客户端 2 训练信息 - 样本数: 300, 训练时间: 11.11秒
2025-08-01 10:07:31,674 - INFO - [服务器] 当前缓冲池大小: 5, 全局轮次: 1
2025-08-01 10:07:31,677 - INFO - [客户端权重摘要] 客户端2 | 参数数量: 74, 均值: 0.002111, 最大: 77.318054, 最小: -4.143336
2025-08-01 10:07:31,677 - INFO - 客户端 2 更新记录 - 陈旧度: 1, 提交轮次: 1
2025-08-01 10:07:31,677 - INFO - ✅ 客户端 2 的更新已加入成功缓冲池，当前池大小: 6
2025-08-01 10:07:31,677 - INFO - 📊 当前缓冲池中的客户端: [9, 4, 6, 10, 8, 2]
2025-08-01 10:07:31,677 - INFO - 🔍 客户端 2 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:31,677 - INFO - 成功处理客户端 2 的更新
2025-08-01 10:07:31,677 - INFO - [Client 2] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:31,752 - WARNING - 配置文件设置min_clients_for_aggregation=1，可能导致每轮只聚合一个客户端
2025-08-01 10:07:31,752 - INFO - 异步模式下，当前有 6 个客户端更新，满足最小阈值 1，可以触发聚合
2025-08-01 10:07:31,752 - INFO - ✅ 触发聚合条件，开始第 1 轮聚合
2025-08-01 10:07:31,753 - INFO - 🔒 开始聚合过程，轮次: 1
2025-08-01 10:07:31,753 - ERROR - 估计客户端9时间时出错: name 'log2' is not defined
2025-08-01 10:07:31,753 - ERROR - 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 773, in estimate_client_times
    C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps
NameError: name 'log2' is not defined

2025-08-01 10:07:31,754 - ERROR - 估计客户端4时间时出错: name 'log2' is not defined
2025-08-01 10:07:31,754 - ERROR - 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 773, in estimate_client_times
    C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps
NameError: name 'log2' is not defined

2025-08-01 10:07:31,754 - ERROR - 估计客户端6时间时出错: name 'log2' is not defined
2025-08-01 10:07:31,754 - ERROR - 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 773, in estimate_client_times
    C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps
NameError: name 'log2' is not defined

2025-08-01 10:07:31,754 - ERROR - 估计客户端10时间时出错: name 'log2' is not defined
2025-08-01 10:07:31,754 - ERROR - 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 773, in estimate_client_times
    C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps
NameError: name 'log2' is not defined

2025-08-01 10:07:31,755 - ERROR - 估计客户端8时间时出错: name 'log2' is not defined
2025-08-01 10:07:31,755 - ERROR - 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 773, in estimate_client_times
    C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps
NameError: name 'log2' is not defined

2025-08-01 10:07:31,755 - ERROR - 估计客户端2时间时出错: name 'log2' is not defined
2025-08-01 10:07:31,755 - ERROR - 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 773, in estimate_client_times
    C = max(B * log2(1 + SNR), 1e3)  # 下限1kbps
NameError: name 'log2' is not defined

2025-08-01 10:07:31,756 - INFO - 候选客户端按训练时间排序: [(9, 5.0), (4, 5.0), (6, 5.0), (10, 5.0), (8, 5.0), (2, 5.0)]
2025-08-01 10:07:31,756 - INFO - 算法参数: V=1.0, max_clients=5, tau_max=5
2025-08-01 10:07:31,756 - DEBUG - 尝试聚合 1 客户端 (当前客户端: 9)
2025-08-01 10:07:31,756 - DEBUG -   模拟D_t: 5.0000, V*D_t: 5.0000
2025-08-01 10:07:31,756 - DEBUG -   惩罚项: 0.0000, 总目标值: 5.0000
2025-08-01 10:07:31,756 - DEBUG - 更新最佳集合：目标值 5.0000 < 之前最小 5.0000
2025-08-01 10:07:31,756 - DEBUG - 尝试聚合 2 客户端 (当前客户端: 4)
2025-08-01 10:07:31,756 - DEBUG -   模拟D_t: 5.0000, V*D_t: 5.0000
2025-08-01 10:07:31,756 - DEBUG -   惩罚项: 0.0000, 总目标值: 5.0000
2025-08-01 10:07:31,756 - DEBUG - 尝试聚合 3 客户端 (当前客户端: 6)
2025-08-01 10:07:31,756 - DEBUG -   模拟D_t: 5.0000, V*D_t: 5.0000
2025-08-01 10:07:31,756 - DEBUG -   惩罚项: 0.0000, 总目标值: 5.0000
2025-08-01 10:07:31,756 - DEBUG - 尝试聚合 4 客户端 (当前客户端: 10)
2025-08-01 10:07:31,756 - DEBUG -   模拟D_t: 5.0000, V*D_t: 5.0000
2025-08-01 10:07:31,756 - DEBUG -   惩罚项: 0.0000, 总目标值: 5.0000
2025-08-01 10:07:31,756 - DEBUG - 尝试聚合 5 客户端 (当前客户端: 8)
2025-08-01 10:07:31,756 - DEBUG -   模拟D_t: 5.0000, V*D_t: 5.0000
2025-08-01 10:07:31,756 - DEBUG -   惩罚项: 0.0000, 总目标值: 5.0000
2025-08-01 10:07:31,756 - INFO - 贪心策略选择 1 个客户端进行聚合: [9]
2025-08-01 10:07:31,757 - INFO - 最优目标函数值: 5.0000
2025-08-01 10:07:31,757 - INFO - 开始执行聚合 - 参与聚合的客户端数量: 1个
2025-08-01 10:07:31,757 - INFO - [Trainer.update_staleness] 客户端 9 陈旧度更新为: 1
2025-08-01 10:07:31,757 - DEBUG - 参与聚合的客户端 9 陈旧度: 1 (当前服务器轮次:1, 客户端拉取模型轮次:0)
2025-08-01 10:07:31,757 - DEBUG - 客户端 9 在服务器轮次 1 被聚合
2025-08-01 10:07:31,757 - INFO - 聚合执行 - 聚合客户端ID: [9]
2025-08-01 10:07:31,762 - INFO - [聚合前全局权重] 均值: 0.001558, 最大: 25.884436, 最小: -1.477074
2025-08-01 10:07:31,762 - INFO - [Algorithm] 客户端 9 - 样本数: 300, 陈旧度: 1, 陈旧度因子: 0.9333, 原始权重: 280.0000
2025-08-01 10:07:31,762 - INFO - [Algorithm] 客户端 9 最终权重: 1.0000
2025-08-01 10:07:31,775 - INFO - [Algorithm] 从模型结构检测到输入通道: 3
2025-08-01 10:07:31,776 - INFO - [Algorithm] 从权重检测到输入通道: 3
2025-08-01 10:07:31,776 - INFO - [Algorithm] 模型输入通道: 3, 待加载权重输入通道: 3
2025-08-01 10:07:31,779 - INFO - [Algorithm] 成功加载权重到模型
2025-08-01 10:07:31,782 - INFO - [聚合后全局权重] 均值: 0.001813, 最大: 26.938467, 最小: -2.711030
2025-08-01 10:07:31,785 - INFO - [权重变化] 平均绝对差异: 0.015784
2025-08-01 10:07:31,788 - INFO - 轮次 1 权重变化记录 - 平均变化: 0.015784, 均值: 0.001813, 最大: 26.938467, 最小: -2.711030
2025-08-01 10:07:31,788 - INFO - 服务器轮次 1 聚合完成: 更新了全局模型
2025-08-01 10:07:31,788 - INFO - [DEBUG] simulate_wall_time=True, asynchronous_mode=True
2025-08-01 10:07:31,788 - INFO - [DEBUG] wall_time更新: 1754014004.9279244 -> 1754014005.4583275 (增加了0.5304031372070312秒)
2025-08-01 10:07:31,788 - INFO - 记录轮次: 1
2025-08-01 10:07:31,788 - INFO - 聚合完成，进入新轮次，当前全局轮次: 2
2025-08-01 10:07:31,788 - INFO - 更新最后聚合时间: 1754014051.788403
2025-08-01 10:07:31,791 - INFO - 已将全局权重加载到评估模型中
2025-08-01 10:07:31,791 - INFO - 开始评估全局模型，测试集大小: 10000
2025-08-01 10:07:33,139 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:33,141 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 10:07:33,142 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 10:07:33,143 - INFO - [Client 1] 开始验证训练集
2025-08-01 10:07:33,146 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:33,146 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:33,147 - INFO - [Trainer 1] 开始训练
2025-08-01 10:07:33,148 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 10:07:33,149 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 10:07:33,151 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:33,152 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:33,152 - INFO - [Trainer 1] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:33,153 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 10:07:33,167 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0101, y=[8, 0, 4, 0, 0]
2025-08-01 10:07:33,168 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:33,187 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:33,202 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1669
2025-08-01 10:07:33,203 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:33,203 - INFO - [Trainer 1] 标签样本: [5, 4, 0, 5, 0]
2025-08-01 10:07:33,252 - INFO - [Trainer 1] Batch 0, Loss: 1.1985
2025-08-01 10:07:34,382 - INFO - [Trainer 1] Batch 5, Loss: 1.2980
2025-08-01 10:07:35,147 - INFO - [Trainer 1] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:35,156 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2625
2025-08-01 10:07:35,156 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:35,156 - INFO - [Trainer 1] 标签样本: [5, 8, 5, 4, 8]
2025-08-01 10:07:35,191 - INFO - [Trainer 1] Batch 9, Loss: 1.1895
2025-08-01 10:07:35,218 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:35,220 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 10:07:35,221 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 10:07:35,221 - INFO - [Client 3] 开始验证训练集
2025-08-01 10:07:35,223 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:35,225 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:35,225 - INFO - [Trainer 3] 开始训练
2025-08-01 10:07:35,225 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 10:07:35,231 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 10:07:35,233 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:35,234 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:35,235 - INFO - [Trainer 3] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:35,237 - INFO - [Trainer 3] 开始第 1/2 个epoch
2025-08-01 10:07:35,258 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3852, y=[2, 2, 2, 2, 2]
2025-08-01 10:07:35,258 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:35,277 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:35,286 - INFO - [Trainer 1] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:35,287 - INFO - [Trainer 1] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.2443, Accuracy: 43.00%
2025-08-01 10:07:35,287 - INFO - [Trainer 1] 开始第 2/2 个epoch
2025-08-01 10:07:35,294 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3941
2025-08-01 10:07:35,295 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:35,295 - INFO - [Trainer 3] 标签样本: [2, 7, 2, 2, 2]
2025-08-01 10:07:35,310 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1507, y=[4, 4, 5, 0, 0]
2025-08-01 10:07:35,310 - INFO - [Trainer 1] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:35,331 - INFO - [Trainer 3] Batch 0, Loss: 0.4057
2025-08-01 10:07:35,334 - INFO - [Trainer 1] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:35,343 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3262
2025-08-01 10:07:35,344 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:35,345 - INFO - [Trainer 1] 标签样本: [5, 8, 8, 8, 4]
2025-08-01 10:07:35,460 - INFO - [Trainer 1] Batch 0, Loss: 1.1957
2025-08-01 10:07:36,852 - INFO - [Trainer 3] Batch 5, Loss: 1.0860
2025-08-01 10:07:36,887 - INFO - [Trainer 1] Batch 5, Loss: 1.3731
2025-08-01 10:07:36,969 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:36,971 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:07:36,973 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:07:36,974 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:07:36,980 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:36,983 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:36,983 - INFO - [Trainer 10] 开始训练
2025-08-01 10:07:36,983 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:07:36,986 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:07:36,991 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:36,994 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:36,996 - INFO - [Trainer 10] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:36,997 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:07:37,021 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0987, y=[8, 8, 8, 8, 8]
2025-08-01 10:07:37,021 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:37,048 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:37,070 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1742
2025-08-01 10:07:37,070 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:37,070 - INFO - [Trainer 10] 标签样本: [6, 8, 6, 7, 8]
2025-08-01 10:07:37,122 - INFO - [Trainer 10] Batch 0, Loss: 1.5570
2025-08-01 10:07:37,629 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:37,630 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 10:07:37,631 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 10:07:37,631 - INFO - [Client 5] 开始验证训练集
2025-08-01 10:07:37,633 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:37,634 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:37,634 - INFO - [Trainer 5] 开始训练
2025-08-01 10:07:37,634 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 10:07:37,643 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 10:07:37,644 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:37,645 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:37,647 - INFO - [Trainer 5] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:37,647 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 10:07:37,706 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3710, y=[7, 7, 7, 7, 3]
2025-08-01 10:07:37,707 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:37,763 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:37,769 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3501
2025-08-01 10:07:37,769 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:37,770 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 7, 7]
2025-08-01 10:07:37,877 - INFO - [Trainer 5] Batch 0, Loss: 0.5756
2025-08-01 10:07:38,339 - INFO - [Trainer 1] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:38,343 - INFO - [Trainer 3] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:38,344 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0983
2025-08-01 10:07:38,345 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:38,345 - INFO - [Trainer 1] 标签样本: [0, 5, 0, 5, 0]
2025-08-01 10:07:38,361 - INFO - [Trainer 3] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.6952, x.mean: -0.6058
2025-08-01 10:07:38,364 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:38,364 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:07:38,408 - INFO - [Trainer 1] Batch 9, Loss: 1.6528
2025-08-01 10:07:38,480 - INFO - [Trainer 3] Batch 9, Loss: 0.1491
2025-08-01 10:07:38,637 - INFO - [Trainer 1] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:38,637 - INFO - [Trainer 1] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.4107, Accuracy: 44.00%
2025-08-01 10:07:38,638 - INFO - [Trainer 1] 参数 conv1.weight: 平均值=0.005950, 标准差=0.121856
2025-08-01 10:07:38,639 - INFO - [Trainer 1] 参数 bn1.weight: 平均值=1.001002, 标准差=0.019432
2025-08-01 10:07:38,639 - INFO - [Trainer 1] 参数 bn1.bias: 平均值=-0.004277, 标准差=0.014213
2025-08-01 10:07:38,639 - INFO - [Trainer 1] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:38,640 - INFO - [Trainer.get_report] 客户端 1 训练报告 - Loss: 1.6678, Accuracy: 41.67%, 陈旧度: 0
2025-08-01 10:07:38,640 - INFO - [Trainer 1] 训练报告生成完成: Loss=1.6678, Accuracy=41.67%
2025-08-01 10:07:38,640 - INFO - [Client 1] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:38,640 - INFO - [Client 1] 使用训练准确率作为客户端准确率: 0.4167
2025-08-01 10:07:38,640 - INFO - [Client 1] 第 2 轮训练完成，耗时: 5.50秒, 准确率: 0.4167
2025-08-01 10:07:38,641 - INFO - [Client 1] 开始提取模型权重
2025-08-01 10:07:38,644 - INFO - [Client 1] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:38,645 - INFO - [Client 1] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:38,645 - INFO - [Client 1] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:38,646 - INFO - [Client 1] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:38,646 - INFO - [Client 1] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:38,646 - INFO - [服务器] 🔄 收到客户端 1 的模型更新，模型版本: unknown
2025-08-01 10:07:38,647 - INFO - [服务器] 客户端 1 训练信息 - 样本数: 300, 训练时间: 5.50秒
2025-08-01 10:07:38,647 - INFO - [服务器] 当前缓冲池大小: 6, 全局轮次: 2
2025-08-01 10:07:38,654 - INFO - [客户端权重摘要] 客户端1 | 参数数量: 74, 均值: 0.002717, 最大: 77.721146, 最小: -2.472999
2025-08-01 10:07:38,655 - INFO - 客户端 1 更新记录 - 陈旧度: 2, 提交轮次: 2
2025-08-01 10:07:38,655 - INFO - ✅ 客户端 1 的更新已加入成功缓冲池，当前池大小: 7
2025-08-01 10:07:38,656 - INFO - 📊 当前缓冲池中的客户端: [9, 4, 6, 10, 8, 2, 1]
2025-08-01 10:07:38,656 - INFO - 🔍 客户端 1 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:38,656 - INFO - 成功处理客户端 1 的更新
2025-08-01 10:07:38,657 - INFO - [Client 1] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:38,659 - INFO - 客户端 1 训练完成
2025-08-01 10:07:38,666 - INFO - [Trainer 3] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:38,666 - INFO - [Trainer 3] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.4661, Accuracy: 90.67%
2025-08-01 10:07:38,666 - INFO - [Trainer 3] 开始第 2/2 个epoch
2025-08-01 10:07:38,702 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3496, y=[2, 2, 2, 2, 2]
2025-08-01 10:07:38,703 - INFO - [Trainer 3] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:38,742 - INFO - [Trainer 3] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:38,750 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3048
2025-08-01 10:07:38,751 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:38,752 - INFO - [Trainer 3] 标签样本: [2, 2, 7, 2, 2]
2025-08-01 10:07:38,894 - INFO - [Trainer 3] Batch 0, Loss: 0.3974
2025-08-01 10:07:39,009 - INFO - [Trainer 10] Batch 5, Loss: 1.0724
2025-08-01 10:07:39,921 - INFO - [Trainer 5] Batch 5, Loss: 0.6207
2025-08-01 10:07:40,352 - INFO - [Trainer 10] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:40,361 - INFO - [Trainer 10] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0800
2025-08-01 10:07:40,362 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:40,362 - INFO - [Trainer 10] 标签样本: [6, 8, 8, 6, 6]
2025-08-01 10:07:40,484 - INFO - [Trainer 10] Batch 9, Loss: 1.0652
2025-08-01 10:07:40,635 - INFO - [Trainer 10] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:40,636 - INFO - [Trainer 10] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 2.4895, Accuracy: 73.67%
2025-08-01 10:07:40,636 - INFO - [Trainer 10] 开始第 2/2 个epoch
2025-08-01 10:07:40,677 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2395, y=[8, 6, 6, 8, 8]
2025-08-01 10:07:40,679 - INFO - [Trainer 10] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:40,698 - INFO - [Trainer 10] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:40,718 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1885
2025-08-01 10:07:40,718 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:40,718 - INFO - [Trainer 3] Batch 5, Loss: 0.0346
2025-08-01 10:07:40,719 - INFO - [Trainer 10] 标签样本: [8, 8, 7, 8, 8]
2025-08-01 10:07:40,882 - INFO - [Trainer 10] Batch 0, Loss: 0.4669
2025-08-01 10:07:41,396 - INFO - [Trainer 5] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:41,409 - INFO - [Trainer 5] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4205
2025-08-01 10:07:41,410 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:41,410 - INFO - [Trainer 5] 标签样本: [7, 7, 3, 7, 3]
2025-08-01 10:07:41,451 - INFO - [Trainer 5] Batch 9, Loss: 0.5519
2025-08-01 10:07:41,609 - INFO - [Trainer 5] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:41,610 - INFO - [Trainer 5] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.5887, Accuracy: 80.33%
2025-08-01 10:07:41,610 - INFO - [Trainer 5] 开始第 2/2 个epoch
2025-08-01 10:07:41,640 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4987, y=[7, 7, 7, 3, 7]
2025-08-01 10:07:41,641 - INFO - [Trainer 5] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:41,661 - INFO - [Trainer 5] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:41,672 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4973
2025-08-01 10:07:41,673 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:41,675 - INFO - [Trainer 5] 标签样本: [7, 7, 3, 7, 7]
2025-08-01 10:07:41,754 - INFO - [Trainer 5] Batch 0, Loss: 0.6993
2025-08-01 10:07:42,131 - INFO - [Trainer 3] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:42,144 - INFO - [Trainer 3] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7147, x.mean: -0.3721
2025-08-01 10:07:42,145 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:42,146 - INFO - [Trainer 3] 标签样本: [2, 6, 2, 2, 2]
2025-08-01 10:07:42,210 - INFO - [Trainer 3] Batch 9, Loss: 1.0638
2025-08-01 10:07:42,345 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:42,346 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 10:07:42,346 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 10:07:42,347 - INFO - [Client 9] 开始验证训练集
2025-08-01 10:07:42,351 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:42,352 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:42,353 - INFO - [Trainer 9] 开始训练
2025-08-01 10:07:42,353 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 10:07:42,357 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:07:42,366 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:42,367 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:42,368 - INFO - [Trainer 3] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:42,369 - INFO - [Trainer 3] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.5272, Accuracy: 90.67%
2025-08-01 10:07:42,370 - INFO - [Trainer 9] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:42,371 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 10:07:42,371 - INFO - [Trainer 3] 参数 conv1.weight: 平均值=-0.002585, 标准差=0.114041
2025-08-01 10:07:42,375 - INFO - [Trainer 3] 参数 bn1.weight: 平均值=0.999401, 标准差=0.016279
2025-08-01 10:07:42,376 - INFO - [Trainer 3] 参数 bn1.bias: 平均值=0.000423, 标准差=0.012868
2025-08-01 10:07:42,380 - INFO - [Trainer 3] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:42,381 - INFO - [Trainer.get_report] 客户端 3 训练报告 - Loss: 0.7642, Accuracy: 87.75%, 陈旧度: 0
2025-08-01 10:07:42,382 - INFO - [Trainer 3] 训练报告生成完成: Loss=0.7642, Accuracy=87.75%
2025-08-01 10:07:42,382 - INFO - [Client 3] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:42,382 - INFO - [Client 3] 使用训练准确率作为客户端准确率: 0.8775
2025-08-01 10:07:42,383 - INFO - [Client 3] 第 2 轮训练完成，耗时: 7.16秒, 准确率: 0.8775
2025-08-01 10:07:42,383 - INFO - [Client 3] 开始提取模型权重
2025-08-01 10:07:42,387 - INFO - [Client 3] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:42,389 - INFO - [Client 3] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:42,390 - INFO - [Client 3] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:42,391 - INFO - [Client 3] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:42,391 - INFO - [Client 3] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:42,394 - INFO - [服务器] 🔄 收到客户端 3 的模型更新，模型版本: unknown
2025-08-01 10:07:42,395 - INFO - [服务器] 客户端 3 训练信息 - 样本数: 300, 训练时间: 7.16秒
2025-08-01 10:07:42,396 - INFO - [服务器] 当前缓冲池大小: 7, 全局轮次: 2
2025-08-01 10:07:42,415 - INFO - [客户端权重摘要] 客户端3 | 参数数量: 74, 均值: 0.001381, 最大: 20.000000, 最小: -2.007341
2025-08-01 10:07:42,416 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1980, y=[6, 8, 6, 6, 8]
2025-08-01 10:07:42,416 - INFO - 客户端 3 更新记录 - 陈旧度: 2, 提交轮次: 2
2025-08-01 10:07:42,416 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:42,416 - INFO - ✅ 客户端 3 的更新已加入成功缓冲池，当前池大小: 8
2025-08-01 10:07:42,419 - INFO - 📊 当前缓冲池中的客户端: [9, 4, 6, 10, 8, 2, 1, 3]
2025-08-01 10:07:42,419 - INFO - 🔍 客户端 3 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:42,420 - INFO - 成功处理客户端 3 的更新
2025-08-01 10:07:42,425 - INFO - [Client 3] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:42,426 - INFO - 客户端 3 训练完成
2025-08-01 10:07:42,455 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:42,473 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3060
2025-08-01 10:07:42,474 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:42,474 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 6, 8]
2025-08-01 10:07:42,544 - INFO - [Trainer 9] Batch 0, Loss: 0.3133
2025-08-01 10:07:42,691 - INFO - [Trainer 10] Batch 5, Loss: 2.6956
2025-08-01 10:07:43,637 - INFO - [Trainer 5] Batch 5, Loss: 0.5137
2025-08-01 10:07:43,845 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:43,847 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 10:07:43,848 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 10:07:43,849 - INFO - [Client 4] 开始验证训练集
2025-08-01 10:07:43,851 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:43,851 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:43,854 - INFO - [Trainer 4] 开始训练
2025-08-01 10:07:43,854 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 10:07:43,856 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:07:43,863 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:43,864 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:43,865 - INFO - [Trainer 4] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:43,865 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 10:07:43,906 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1125, y=[1, 1, 1, 1, 0]
2025-08-01 10:07:43,908 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:43,951 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:43,970 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2528
2025-08-01 10:07:43,972 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:43,972 - INFO - [Trainer 4] 标签样本: [0, 0, 0, 0, 0]
2025-08-01 10:07:44,068 - INFO - [Trainer 4] Batch 0, Loss: 1.0478
2025-08-01 10:07:44,095 - INFO - [Trainer 10] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:44,113 - INFO - [Trainer 10] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2973
2025-08-01 10:07:44,114 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:44,115 - INFO - [Trainer 10] 标签样本: [8, 8, 7, 8, 8]
2025-08-01 10:07:44,226 - INFO - [Trainer 10] Batch 9, Loss: 2.2989
2025-08-01 10:07:44,401 - INFO - [Trainer 10] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:44,403 - INFO - [Trainer 10] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.9594, Accuracy: 76.00%
2025-08-01 10:07:44,405 - INFO - [Trainer 10] 参数 conv1.weight: 平均值=0.046225, 标准差=0.239805
2025-08-01 10:07:44,406 - INFO - [Trainer 10] 参数 bn1.weight: 平均值=0.979311, 标准差=0.156608
2025-08-01 10:07:44,407 - INFO - [Trainer 10] 参数 bn1.bias: 平均值=-0.084381, 标准差=0.168899
2025-08-01 10:07:44,407 - INFO - [Trainer 10] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:44,407 - INFO - [Trainer.get_report] 客户端 10 训练报告 - Loss: 2.9360, Accuracy: 73.59%, 陈旧度: 0
2025-08-01 10:07:44,409 - INFO - [Trainer 10] 训练报告生成完成: Loss=2.9360, Accuracy=73.59%
2025-08-01 10:07:44,410 - INFO - [Client 10] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:44,411 - INFO - [Client 10] 使用训练准确率作为客户端准确率: 0.7359
2025-08-01 10:07:44,411 - INFO - [Client 10] 第 5 轮训练完成，耗时: 7.44秒, 准确率: 0.7359
2025-08-01 10:07:44,412 - INFO - [Client 10] 开始提取模型权重
2025-08-01 10:07:44,414 - INFO - [Client 10] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:44,415 - INFO - [Client 10] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:44,416 - INFO - [Client 10] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:44,417 - INFO - [Client 10] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:44,425 - INFO - [Client 10] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:44,426 - INFO - [服务器] 🔄 收到客户端 10 的模型更新，模型版本: unknown
2025-08-01 10:07:44,426 - INFO - [服务器] 客户端 10 训练信息 - 样本数: 300, 训练时间: 7.44秒
2025-08-01 10:07:44,427 - INFO - [服务器] 当前缓冲池大小: 8, 全局轮次: 2
2025-08-01 10:07:44,440 - INFO - [客户端权重摘要] 客户端10 | 参数数量: 74, 均值: 0.021478, 最大: 1536.384644, 最小: -18.764971
2025-08-01 10:07:44,444 - INFO - 客户端 10 已有 1 个更新在缓冲池中，删除旧更新
2025-08-01 10:07:44,444 - INFO - 移除客户端 10 的旧更新，距今 12.8 秒
2025-08-01 10:07:44,444 - INFO - 客户端 10 更新记录 - 陈旧度: 2, 提交轮次: 2
2025-08-01 10:07:44,445 - INFO - ✅ 客户端 10 的更新已加入成功缓冲池，当前池大小: 8
2025-08-01 10:07:44,445 - INFO - 📊 当前缓冲池中的客户端: [9, 4, 6, 8, 2, 1, 3, 10]
2025-08-01 10:07:44,446 - INFO - 🔍 客户端 10 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:44,446 - INFO - 成功处理客户端 10 的更新
2025-08-01 10:07:44,449 - INFO - [Client 10] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:44,449 - INFO - 客户端 10 训练完成
2025-08-01 10:07:44,584 - INFO - [Trainer 9] Batch 5, Loss: 0.4212
2025-08-01 10:07:44,996 - INFO - [Trainer 5] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:45,015 - INFO - [Trainer 5] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5758
2025-08-01 10:07:45,018 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:45,018 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 7, 7]
2025-08-01 10:07:45,115 - INFO - [Trainer 5] Batch 9, Loss: 0.1939
2025-08-01 10:07:45,261 - INFO - [Trainer 5] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:45,262 - INFO - [Trainer 5] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.5005, Accuracy: 79.67%
2025-08-01 10:07:45,264 - INFO - [Trainer 5] 参数 conv1.weight: 平均值=-0.002702, 标准差=0.114973
2025-08-01 10:07:45,265 - INFO - [Trainer 5] 参数 bn1.weight: 平均值=0.999857, 标准差=0.013497
2025-08-01 10:07:45,266 - INFO - [Trainer 5] 参数 bn1.bias: 平均值=0.002837, 标准差=0.009199
2025-08-01 10:07:45,267 - INFO - [Trainer 5] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:45,268 - INFO - [Trainer.get_report] 客户端 5 训练报告 - Loss: 0.8507, Accuracy: 75.92%, 陈旧度: 0
2025-08-01 10:07:45,269 - INFO - [Trainer 5] 训练报告生成完成: Loss=0.8507, Accuracy=75.92%
2025-08-01 10:07:45,270 - INFO - [Client 5] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:45,270 - INFO - [Client 5] 使用训练准确率作为客户端准确率: 0.7592
2025-08-01 10:07:45,271 - INFO - [Client 5] 第 2 轮训练完成，耗时: 7.64秒, 准确率: 0.7592
2025-08-01 10:07:45,271 - INFO - [Client 5] 开始提取模型权重
2025-08-01 10:07:45,272 - INFO - [Client 5] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:45,272 - INFO - [Client 5] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:45,272 - INFO - [Client 5] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:45,274 - INFO - [Client 5] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:45,274 - INFO - [Client 5] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:45,274 - INFO - [服务器] 🔄 收到客户端 5 的模型更新，模型版本: unknown
2025-08-01 10:07:45,274 - INFO - [服务器] 客户端 5 训练信息 - 样本数: 300, 训练时间: 7.64秒
2025-08-01 10:07:45,274 - INFO - [服务器] 当前缓冲池大小: 8, 全局轮次: 2
2025-08-01 10:07:45,290 - INFO - [客户端权重摘要] 客户端5 | 参数数量: 74, 均值: 0.001280, 最大: 20.000000, 最小: -2.243681
2025-08-01 10:07:45,292 - INFO - 客户端 5 更新记录 - 陈旧度: 2, 提交轮次: 2
2025-08-01 10:07:45,295 - INFO - ✅ 客户端 5 的更新已加入成功缓冲池，当前池大小: 9
2025-08-01 10:07:45,296 - INFO - 📊 当前缓冲池中的客户端: [9, 4, 6, 8, 2, 1, 3, 10, 5]
2025-08-01 10:07:45,297 - INFO - 🔍 客户端 5 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:45,297 - INFO - 成功处理客户端 5 的更新
2025-08-01 10:07:45,297 - INFO - [Client 5] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:45,297 - INFO - 客户端 5 训练完成
2025-08-01 10:07:45,882 - INFO - [Trainer 9] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:45,888 - INFO - [Trainer 9] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.6172, x.mean: -0.2604
2025-08-01 10:07:45,888 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:45,890 - INFO - [Trainer 9] 标签样本: [8, 6, 8, 7, 6]
2025-08-01 10:07:45,963 - INFO - [Trainer 9] Batch 9, Loss: 0.4503
2025-08-01 10:07:45,978 - INFO - [Trainer 4] Batch 5, Loss: 1.0323
2025-08-01 10:07:46,077 - INFO - [Trainer 9] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:46,078 - INFO - [Trainer 9] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.4201, Accuracy: 86.33%
2025-08-01 10:07:46,078 - INFO - [Trainer 9] 开始第 2/2 个epoch
2025-08-01 10:07:46,105 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7342, x.mean=-0.5072, y=[6, 6, 6, 8, 6]
2025-08-01 10:07:46,106 - INFO - [Trainer 9] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:46,138 - INFO - [Trainer 9] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:46,152 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3340
2025-08-01 10:07:46,153 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:46,155 - INFO - [Trainer 9] 标签样本: [8, 6, 6, 6, 6]
2025-08-01 10:07:46,258 - INFO - [Trainer 9] Batch 0, Loss: 0.7954
2025-08-01 10:07:47,121 - INFO - [Trainer 4] Epoch 1 进度: 10/10 批次
2025-08-01 10:07:47,132 - INFO - [Trainer 4] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1758
2025-08-01 10:07:47,133 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:47,133 - INFO - [Trainer 4] 标签样本: [1, 1, 5, 0, 0]
2025-08-01 10:07:47,156 - INFO - [Trainer 4] Batch 9, Loss: 0.6077
2025-08-01 10:07:47,290 - INFO - [Trainer 4] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 10:07:47,290 - INFO - [Trainer 4] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.8796, Accuracy: 63.00%
2025-08-01 10:07:47,291 - INFO - [Trainer 4] 开始第 2/2 个epoch
2025-08-01 10:07:47,302 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2288, y=[1, 5, 1, 1, 1]
2025-08-01 10:07:47,302 - INFO - [Trainer 4] Epoch 2 开始处理 10 个批次
2025-08-01 10:07:47,323 - INFO - [Trainer 4] Epoch 2 进度: 1/10 批次
2025-08-01 10:07:47,335 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2102
2025-08-01 10:07:47,336 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:47,336 - INFO - [Trainer 4] 标签样本: [1, 0, 0, 1, 1]
2025-08-01 10:07:47,387 - INFO - [Trainer 4] Batch 0, Loss: 0.5391
2025-08-01 10:07:47,799 - INFO - [Trainer 9] Batch 5, Loss: 0.2206
2025-08-01 10:07:48,851 - INFO - [Trainer 4] Batch 5, Loss: 1.4994
2025-08-01 10:07:48,890 - INFO - [Trainer 9] Epoch 2 进度: 10/10 批次
2025-08-01 10:07:48,905 - INFO - [Trainer 9] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.6542
2025-08-01 10:07:48,906 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:48,907 - INFO - [Trainer 9] 标签样本: [6, 8, 8, 6, 6]
2025-08-01 10:07:48,957 - INFO - [Trainer 9] Batch 9, Loss: 0.5611
2025-08-01 10:07:49,055 - INFO - [Trainer 9] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 10:07:49,055 - INFO - [Trainer 9] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.5257, Accuracy: 85.00%
2025-08-01 10:07:49,056 - INFO - [Trainer 9] 参数 conv1.weight: 平均值=-0.002969, 标准差=0.118641
2025-08-01 10:07:49,058 - INFO - [Trainer 9] 参数 bn1.weight: 平均值=0.998970, 标准差=0.015197
2025-08-01 10:07:49,058 - INFO - [Trainer 9] 参数 bn1.bias: 平均值=-0.000755, 标准差=0.012899
2025-08-01 10:07:49,058 - INFO - [Trainer 9] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 10:07:49,059 - INFO - [Trainer.get_report] 客户端 9 训练报告 - Loss: 0.5858, Accuracy: 83.56%, 陈旧度: 1
2025-08-01 10:07:49,059 - INFO - [Trainer 9] 训练报告生成完成: Loss=0.5858, Accuracy=83.56%
2025-08-01 10:07:49,059 - INFO - [Client 9] ✅ 训练器训练完成，获得报告: True
2025-08-01 10:07:49,059 - INFO - [Client 9] 使用训练准确率作为客户端准确率: 0.8356
2025-08-01 10:07:49,060 - INFO - [Client 9] 第 3 轮训练完成，耗时: 6.71秒, 准确率: 0.8356
2025-08-01 10:07:49,060 - INFO - [Client 9] 开始提取模型权重
2025-08-01 10:07:49,062 - INFO - [Client 9] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 10:07:49,063 - INFO - [Client 9] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 10:07:49,063 - INFO - [Client 9] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 10:07:49,063 - INFO - [Client 9] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 10:07:49,063 - INFO - [Client 9] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 10:07:49,063 - INFO - [服务器] 🔄 收到客户端 9 的模型更新，模型版本: unknown
2025-08-01 10:07:49,065 - INFO - [服务器] 客户端 9 训练信息 - 样本数: 300, 训练时间: 6.71秒
2025-08-01 10:07:49,065 - INFO - [服务器] 当前缓冲池大小: 9, 全局轮次: 2
2025-08-01 10:07:49,075 - INFO - [客户端权重摘要] 客户端9 | 参数数量: 74, 均值: 0.001858, 最大: 28.892206, 最小: -2.717845
2025-08-01 10:07:49,075 - INFO - 客户端 9 已有 1 个更新在缓冲池中，删除旧更新
2025-08-01 10:07:49,076 - INFO - 移除客户端 9 的旧更新，距今 17.7 秒
2025-08-01 10:07:49,076 - INFO - 客户端 9 更新记录 - 陈旧度: 2, 提交轮次: 2
2025-08-01 10:07:49,077 - INFO - ✅ 客户端 9 的更新已加入成功缓冲池，当前池大小: 9
2025-08-01 10:07:49,077 - INFO - 📊 当前缓冲池中的客户端: [4, 6, 8, 2, 1, 3, 10, 5, 9]
2025-08-01 10:07:49,077 - INFO - 🔍 客户端 9 更新处理完成，等待主循环检查聚合条件...
2025-08-01 10:07:49,079 - INFO - 成功处理客户端 9 的更新
2025-08-01 10:07:49,079 - INFO - [Client 9] ✅ 成功上传训练结果到服务器
2025-08-01 10:07:49,080 - INFO - 客户端 9 训练完成
2025-08-01 10:07:49,242 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:49,243 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 10:07:49,243 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 10:07:49,243 - INFO - [Client 7] 开始验证训练集
2025-08-01 10:07:49,245 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:49,245 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:49,245 - INFO - [Trainer 7] 开始训练
2025-08-01 10:07:49,245 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 10:07:49,247 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:07:49,248 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:49,249 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:49,249 - INFO - [Trainer 7] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:49,249 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 10:07:49,285 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1918, y=[2, 2, 2, 2, 2]
2025-08-01 10:07:49,285 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:49,297 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:49,306 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2647
2025-08-01 10:07:49,306 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:49,307 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:07:49,362 - INFO - [Trainer 0] 测试结果 - 准确率: 0.1484 (1484/10000), 损失: 15.0180
2025-08-01 10:07:49,363 - INFO - 全局模型评估完成，准确率: 0.1484, 耗时: 17.57秒
2025-08-01 10:07:49,363 - INFO - log_accuracy: 模型输入通道数: 3
2025-08-01 10:07:49,364 - INFO - 轮次 2 准确率记录 - 当前: 14.84%, 平均(最近10轮): 12.42%, 最佳: 14.84%
2025-08-01 10:07:49,364 - INFO - 轮次: 2, 准确率: 0.1484, 平均陈旧度: 1.0
2025-08-01 10:07:49,365 - INFO - 客户端 9 陈旧度: 1
2025-08-01 10:07:49,367 - INFO - [Trainer 7] Batch 0, Loss: 0.0003
2025-08-01 10:07:49,378 - INFO - [权重变化] 轮次: 2 | 均值: 0.000255, 标准差: 0.076418
2025-08-01 10:07:49,385 - INFO - [全局模型摘要] 轮次: 2 | 均值: 0.001813, 最大: 26.938467, 最小: -2.711030
2025-08-01 10:07:49,385 - INFO - [DEBUG] 结果记录时 - wall_time=1754014005.5583274, initial_wall_time=1754014004.0022285, elapsed_time=1.5560989379882812
2025-08-01 10:07:49,386 - INFO - 参与聚合的客户端平均准确率: 0.8250 (基于 1 个客户端)
2025-08-01 10:07:49,387 - INFO - 所有训练统计信息已记录到统一结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_100644.csv
2025-08-01 10:07:49,387 - INFO - 记录数据: {'round': 1, 'elapsed_time': 1.5560989379882812, 'accuracy': 0.825, 'global_accuracy': 0.1484, 'global_accuracy_std': 0.0, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'virtual_time': 0.0, 'aggregated_clients_count': 1}
2025-08-01 10:07:49,387 - DEBUG - 聚合后客户端 1 陈旧度: 2, Q_k 更新为: 0.0000
2025-08-01 10:07:49,388 - DEBUG - 聚合后客户端 2 陈旧度: 2, Q_k 更新为: 0.0000
2025-08-01 10:07:49,388 - DEBUG - 聚合后客户端 3 陈旧度: 2, Q_k 更新为: 0.0000
2025-08-01 10:07:49,388 - DEBUG - 聚合后客户端 4 陈旧度: 2, Q_k 更新为: 0.0000
2025-08-01 10:07:49,388 - DEBUG - 聚合后客户端 5 陈旧度: 2, Q_k 更新为: 0.0000
2025-08-01 10:07:49,388 - DEBUG - 聚合后客户端 6 陈旧度: 2, Q_k 更新为: 0.0000
2025-08-01 10:07:49,388 - DEBUG - 聚合后客户端 7 陈旧度: 2, Q_k 更新为: 0.0000
2025-08-01 10:07:49,388 - DEBUG - 聚合后客户端 8 陈旧度: 2, Q_k 更新为: 0.0000
2025-08-01 10:07:49,388 - DEBUG - 聚合后客户端 9 陈旧度: 2, Q_k 更新为: 0.0000
2025-08-01 10:07:49,388 - DEBUG - 聚合后客户端 10 陈旧度: 2, Q_k 更新为: 0.0000
2025-08-01 10:07:49,388 - INFO - 聚合完成，缓冲池中还剩 8 个更新，来自 8 个不同客户端
2025-08-01 10:07:49,389 - INFO - 🔓 聚合过程结束，轮次: 2
2025-08-01 10:07:49,389 - INFO - 🎉 第 1 轮聚合完成，当前轮次: 2
2025-08-01 10:07:49,389 - INFO - 🔄 聚合完成，重新启动客户端训练（第 2 轮）
2025-08-01 10:07:49,390 - INFO - 缓冲池已清空，准备新一轮训练
2025-08-01 10:07:49,390 - INFO - 重新启动客户端 1 的训练任务
2025-08-01 10:07:49,391 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:49,391 - INFO - 重新启动客户端 2 的训练任务
2025-08-01 10:07:49,393 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 10:07:49,393 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:49,393 - INFO - 重新启动客户端 3 的训练任务
2025-08-01 10:07:49,394 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 10:07:49,394 - INFO - [Client 2] 开始验证训练集
2025-08-01 10:07:49,394 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:49,394 - INFO - 客户端 4 仍在训练中，跳过重启
2025-08-01 10:07:49,395 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 10:07:49,395 - INFO - 重新启动客户端 5 的训练任务
2025-08-01 10:07:49,396 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 10:07:49,396 - INFO - [Client 4] 开始验证训练集
2025-08-01 10:07:49,398 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 10:07:49,399 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:49,399 - INFO - 重新启动客户端 6 的训练任务
2025-08-01 10:07:49,399 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 10:07:49,399 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:49,400 - INFO - [Client 5] 开始验证训练集
2025-08-01 10:07:49,400 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:49,401 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:49,401 - INFO - 客户端 7 仍在训练中，跳过重启
2025-08-01 10:07:49,401 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:49,402 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 10:07:49,402 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:49,402 - INFO - 重新启动客户端 8 的训练任务
2025-08-01 10:07:49,403 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 10:07:49,403 - INFO - [Trainer 2] 开始训练
2025-08-01 10:07:49,404 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 10:07:49,404 - INFO - [Trainer 4] 开始训练
2025-08-01 10:07:49,404 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:49,404 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 10:07:49,405 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 10:07:49,405 - INFO - [Client 7] 开始验证训练集
2025-08-01 10:07:49,405 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:49,406 - INFO - 重新启动客户端 9 的训练任务
2025-08-01 10:07:49,406 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 10:07:49,406 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:49,407 - INFO - [Client 8] 开始验证训练集
2025-08-01 10:07:49,407 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 10:07:49,410 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:49,411 - INFO - 重新启动客户端 10 的训练任务
2025-08-01 10:07:49,411 - INFO - [Trainer 5] 开始训练
2025-08-01 10:07:49,411 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 10:07:49,413 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 10:07:49,413 - DEBUG - Using proactor: IocpProactor
2025-08-01 10:07:49,414 - INFO - ✅ 已重新启动 8 个客户端的训练任务
2025-08-01 10:07:49,414 - INFO - [Client 9] 开始验证训练集
2025-08-01 10:07:49,415 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:07:49,415 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:49,418 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:07:49,419 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:49,419 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:49,420 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:07:49,422 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 10:07:49,423 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 10:07:49,423 - INFO - [Trainer 7] 开始训练
2025-08-01 10:07:49,423 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:49,425 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 10:07:49,427 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:49,428 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 10:07:49,428 - INFO - [Trainer 8] 开始训练
2025-08-01 10:07:49,430 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 10:07:49,430 - INFO - [Client 10] 开始验证训练集
2025-08-01 10:07:49,430 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:49,431 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:49,431 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:49,432 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 10:07:49,433 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 10:07:49,435 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:49,436 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:49,438 - INFO - [Trainer 9] 开始训练
2025-08-01 10:07:49,441 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 10:07:49,442 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:49,443 - INFO - [Trainer 10] 开始训练
2025-08-01 10:07:49,443 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 10:07:49,444 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 10:07:49,446 - INFO - [Trainer 4] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:49,446 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:49,446 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:49,447 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:07:49,450 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:49,450 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 10:07:49,451 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 10:07:49,451 - INFO - [Trainer 4] 开始第 1/2 个epoch
2025-08-01 10:07:49,451 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 10:07:49,451 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:49,453 - INFO - [Trainer 5] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:49,454 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:49,455 - ERROR - [Trainer 4] 训练过程出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 6; expected version 5 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:49,455 - INFO - [Trainer 10] 开始训练
2025-08-01 10:07:49,457 - INFO - [Trainer 5] 开始第 1/2 个epoch
2025-08-01 10:07:49,459 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 10:07:49,459 - ERROR - [Trainer 4] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 6; expected version 5 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:49,461 - ERROR - [Client 4] 训练器训练过程中出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 6; expected version 5 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:49,461 - INFO - [Trainer 7] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:49,461 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:49,462 - INFO - [Trainer 7] 开始第 1/2 个epoch
2025-08-01 10:07:49,462 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:49,463 - INFO - [Trainer 2] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:49,463 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:49,465 - ERROR - [Client 4] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 6; expected version 5 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:49,465 - INFO - [Trainer 2] 开始第 1/2 个epoch
2025-08-01 10:07:49,466 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:49,467 - ERROR - [Trainer 7] 训练过程出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 4; expected version 3 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:49,468 - INFO - [Trainer 9] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:49,469 - INFO - [Trainer 9] 开始第 1/2 个epoch
2025-08-01 10:07:49,469 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:07:49,469 - INFO - [Trainer 8] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:49,469 - ERROR - [Trainer 7] 异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 4; expected version 3 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:49,470 - INFO - [Trainer 8] 开始第 1/2 个epoch
2025-08-01 10:07:49,471 - ERROR - [Client 7] 训练器训练过程中出错: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 4; expected version 3 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).
2025-08-01 10:07:49,473 - ERROR - [Client 7] 训练器异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_client.py", line 1171, in train
    trainer_report = await self.trainer.train(
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_trainer.py", line 385, in train
    loss.backward()
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\__init__.py", line 353, in backward
    _engine_run_backward(
  File "D:\Develop\Miniconda\envs\xhm-plato\lib\site-packages\torch\autograd\graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: one of the variables needed for gradient computation has been modified by an inplace operation: [torch.FloatTensor [64]] is at version 4; expected version 3 instead. Hint: enable anomaly detection to find the operation that failed to compute its gradient, with torch.autograd.set_detect_anomaly(True).

2025-08-01 10:07:49,474 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:49,475 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:49,475 - INFO - 客户端 4 训练完成
2025-08-01 10:07:49,477 - INFO - [Trainer 10] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:49,478 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:07:49,478 - INFO - 客户端 7 训练完成
2025-08-01 10:07:49,479 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 10:07:49,480 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 10:07:49,480 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 10:07:49,482 - INFO - [Trainer 10] 开始训练 2 个epoch，已重置BatchNorm统计信息
2025-08-01 10:07:49,482 - INFO - [Trainer 10] 开始第 1/2 个epoch
2025-08-01 10:07:49,548 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4248, y=[1, 5, 0, 0, 0]
2025-08-01 10:07:49,549 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:49,550 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2804, y=[2, 2, 2, 2, 2]
2025-08-01 10:07:49,550 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2628, y=[7, 7, 7, 3, 7]
2025-08-01 10:07:49,550 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:49,550 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3415, y=[0, 1, 1, 1, 1]
2025-08-01 10:07:49,550 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1436, y=[8, 8, 8, 8, 8]
2025-08-01 10:07:49,550 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:49,551 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:49,551 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:49,551 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3855, y=[8, 6, 6, 8, 6]
2025-08-01 10:07:49,552 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:49,554 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3110, y=[6, 8, 4, 6, 8]
2025-08-01 10:07:49,555 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:49,560 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2716, y=[0, 9, 6, 7, 8]
2025-08-01 10:07:49,560 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 10:07:49,605 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:49,606 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:49,606 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:49,607 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:49,607 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:49,608 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:49,609 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:49,609 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 10:07:49,621 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4128
2025-08-01 10:07:49,621 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2540
2025-08-01 10:07:49,621 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3556
2025-08-01 10:07:49,621 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2612
2025-08-01 10:07:49,621 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2652
2025-08-01 10:07:49,621 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2901
2025-08-01 10:07:49,621 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:49,621 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1145
2025-08-01 10:07:49,621 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:49,621 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1488
2025-08-01 10:07:49,621 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:49,621 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:49,621 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:49,621 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:49,621 - INFO - [Trainer 5] 标签样本: [3, 7, 7, 7, 7]
2025-08-01 10:07:49,621 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:49,621 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 8, 8]
2025-08-01 10:07:49,622 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 10:07:49,622 - INFO - [Trainer 10] 标签样本: [8, 8, 8, 8, 6]
2025-08-01 10:07:49,622 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 10:07:49,622 - INFO - [Trainer 8] 标签样本: [1, 0, 6, 6, 1]
2025-08-01 10:07:49,622 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 10:07:49,623 - INFO - [Trainer 10] 标签样本: [6, 8, 8, 6, 8]
2025-08-01 10:07:49,624 - INFO - [Trainer 4] 标签样本: [4, 1, 0, 0, 1]
2025-08-01 10:07:49,818 - INFO - [Trainer 5] Batch 0, Loss: 0.4804
2025-08-01 10:07:49,827 - INFO - [Trainer 7] Batch 0, Loss: 0.0001
2025-08-01 10:07:49,827 - INFO - [Trainer 10] Batch 0, Loss: 0.7096
2025-08-01 10:07:49,828 - INFO - [Trainer 10] Batch 0, Loss: 1.4267
2025-08-01 10:07:49,836 - INFO - [Trainer 8] Batch 0, Loss: 0.7264
2025-08-01 10:07:49,838 - INFO - [Trainer 9] Batch 0, Loss: 0.3988
2025-08-01 10:07:49,841 - INFO - [Trainer 4] Batch 0, Loss: 0.7492
2025-08-01 10:07:49,842 - INFO - [Trainer 2] Batch 0, Loss: 0.0139
2025-08-01 10:07:50,435 - INFO - 🚀 开始第 3 轮训练（目标：20 轮）
2025-08-01 10:07:50,440 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:50,444 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:51,457 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:51,460 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:52,470 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:52,475 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:53,311 - INFO - [Trainer 5] Batch 5, Loss: 0.5181
2025-08-01 10:07:53,422 - INFO - [Trainer 7] Batch 5, Loss: 0.0016
2025-08-01 10:07:53,491 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:53,494 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:53,604 - INFO - [Trainer 2] Batch 5, Loss: 0.2079
2025-08-01 10:07:53,623 - INFO - [Trainer 8] Batch 5, Loss: 0.8175
2025-08-01 10:07:53,666 - INFO - [Trainer 4] Batch 5, Loss: 0.8273
2025-08-01 10:07:53,668 - INFO - [Trainer 10] Batch 5, Loss: 3.3292
2025-08-01 10:07:53,670 - INFO - [Trainer 10] Batch 5, Loss: 2.9467
2025-08-01 10:07:53,671 - INFO - [Trainer 9] Batch 5, Loss: 0.5922
2025-08-01 10:07:54,504 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 10:07:54,506 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
