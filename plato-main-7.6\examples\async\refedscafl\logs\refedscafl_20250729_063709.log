[INFO][06:37:09]: 日志系统已初始化
[INFO][06:37:09]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_063709.log
[INFO][06:37:09]: 日志级别: INFO
[WARNING][06:37:09]: 无法获取系统信息: No module named 'psutil'
[INFO][06:37:09]: 🚀 ReFedScaFL 训练开始
[INFO][06:37:09]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][06:37:09]: 开始时间: 2025-07-29 06:37:09
[INFO][06:37:09]: [Client None] 基础初始化完成
[INFO][06:37:09]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][06:37:09]: 创建并缓存共享模型
[INFO][06:37:09]: [93m[1m[328] Logging runtime results to: ./results/refedscafl/comparison_cifar10_alpha01/328.csv.[0m
[INFO][06:37:09]: [Server #328] Started training on 20 clients with 20 per round.
[INFO][06:37:09]: 服务器参数配置完成：
[INFO][06:37:09]: - 客户端数量: total=20, per_round=20
[INFO][06:37:09]: - 权重参数: success=0.8, distill=0.2
[INFO][06:37:09]: - SCAFL参数: V=1.0, tau_max=5
[INFO][06:37:09]: 从共享资源模型提取并缓存全局权重
[INFO][06:37:09]: [Server #328] Configuring the server...
[INFO][06:37:09]: Training: 400 rounds or accuracy above 100.0%

[INFO][06:37:10]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][06:37:10]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][06:37:10]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][06:37:10]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][06:37:10]: Algorithm: fedavg
[INFO][06:37:10]: Data source: CIFAR10
[INFO][06:37:13]: Starting client #1's process.
[INFO][06:37:13]: Starting client #2's process.
[INFO][06:37:13]: Starting client #3's process.
[INFO][06:37:13]: Starting client #4's process.
[INFO][06:37:13]: Starting client #5's process.
[INFO][06:37:13]: Starting client #6's process.
[INFO][06:37:13]: Starting client #7's process.
[INFO][06:37:13]: Starting client #8's process.
[INFO][06:37:13]: Starting client #9's process.
[INFO][06:37:13]: Starting client #10's process.
[INFO][06:37:13]: Setting the random seed for selecting clients: 1
[INFO][06:37:13]: Starting a server at address 127.0.0.1 and port 8092.
[INFO][06:37:35]: [Server #328] A new client just connected.
[INFO][06:37:35]: [Server #328] New client with id #2 arrived.
[INFO][06:37:35]: [Server #328] Client process #7832 registered.
[INFO][06:37:35]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][06:37:36]: [Server #328] A new client just connected.
[INFO][06:37:36]: [Server #328] New client with id #1 arrived.
[INFO][06:37:36]: [Server #328] Client process #22664 registered.
[INFO][06:37:36]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][06:37:37]: [Server #328] A new client just connected.
[INFO][06:37:37]: [Server #328] New client with id #4 arrived.
[INFO][06:37:37]: [Server #328] Client process #37076 registered.
[INFO][06:37:37]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][06:37:37]: [Server #328] A new client just connected.
[INFO][06:37:37]: [Server #328] New client with id #9 arrived.
[INFO][06:37:37]: [Server #328] Client process #7344 registered.
[INFO][06:37:37]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][06:37:37]: [Server #328] A new client just connected.
[INFO][06:37:37]: [Server #328] New client with id #10 arrived.
[INFO][06:37:37]: [Server #328] Client process #32996 registered.
[INFO][06:37:37]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][06:37:37]: [Server #328] A new client just connected.
[INFO][06:37:37]: [Server #328] New client with id #3 arrived.
[INFO][06:37:37]: [Server #328] Client process #31828 registered.
[INFO][06:37:37]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][06:37:38]: [Server #328] A new client just connected.
[INFO][06:37:38]: [Server #328] New client with id #8 arrived.
[INFO][06:37:38]: [Server #328] Client process #38624 registered.
[INFO][06:37:38]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][06:37:38]: [Server #328] A new client just connected.
[INFO][06:37:38]: [Server #328] A new client just connected.
[INFO][06:37:38]: [Server #328] A new client just connected.
[INFO][06:37:38]: [Server #328] New client with id #5 arrived.
[INFO][06:37:38]: [Server #328] Client process #24340 registered.
[INFO][06:37:38]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][06:37:38]: [Server #328] New client with id #7 arrived.
[INFO][06:37:38]: [Server #328] Client process #24168 registered.
[INFO][06:37:38]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][06:37:38]: [Server #328] New client with id #6 arrived.
[INFO][06:37:38]: [Server #328] Client process #20872 registered.
[INFO][06:37:38]: [Server #328] Starting training.
[INFO][06:37:38]: [93m[1m
[Server #328] Starting round 1/400.[0m
[INFO][06:37:38]: [Server #328] Selected clients: [5, 19, 3, 9, 4, 8, 13, 15, 14, 11, 7, 16, 2, 17, 1, 10, 20, 18, 6, 12]
[INFO][06:37:38]: [Server #328] Selecting client #5 for training.
[INFO][06:37:38]: [Server #328] Sending the current model to client #5 (simulated).
[INFO][06:37:38]: [Server #328] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][06:37:38]: [Server #328] Selecting client #19 for training.
[INFO][06:37:38]: [Server #328] Sending the current model to client #19 (simulated).
[INFO][06:37:38]: [Server #328] Sending 18.75 MB of payload data to client #19 (simulated).
[INFO][06:37:38]: [Server #328] Selecting client #3 for training.
[INFO][06:37:38]: [Server #328] Sending the current model to client #3 (simulated).
[INFO][06:37:38]: [Server #328] Sending 18.75 MB of payload data to client #3 (simulated).
[INFO][06:37:38]: [Server #328] Selecting client #9 for training.
[INFO][06:37:38]: [Server #328] Sending the current model to client #9 (simulated).
[INFO][06:37:38]: [Server #328] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][06:37:38]: [Server #328] Selecting client #4 for training.
[INFO][06:37:38]: [Server #328] Sending the current model to client #4 (simulated).
[INFO][06:37:38]: [Server #328] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][06:37:38]: [Server #328] Selecting client #8 for training.
[INFO][06:37:38]: [Server #328] Sending the current model to client #8 (simulated).
[INFO][06:37:38]: [Server #328] Sending 18.75 MB of payload data to client #8 (simulated).
[INFO][06:37:38]: [Server #328] Selecting client #13 for training.
[INFO][06:37:38]: [Server #328] Sending the current model to client #13 (simulated).
[INFO][06:37:38]: [Server #328] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][06:37:38]: [Server #328] Selecting client #15 for training.
[INFO][06:37:38]: [Server #328] Sending the current model to client #15 (simulated).
[INFO][06:37:39]: [Server #328] Sending 18.75 MB of payload data to client #15 (simulated).
[INFO][06:37:39]: [Server #328] Selecting client #14 for training.
[INFO][06:37:39]: [Server #328] Sending the current model to client #14 (simulated).
[INFO][06:37:39]: [Server #328] Sending 18.75 MB of payload data to client #14 (simulated).
[INFO][06:37:39]: [Server #328] Selecting client #11 for training.
[INFO][06:37:39]: [Server #328] Sending the current model to client #11 (simulated).
[INFO][06:37:39]: [Server #328] Sending 18.75 MB of payload data to client #11 (simulated).
[INFO][06:37:39]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][06:43:55]: [Server #328] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][06:43:57]: [Server #328] Received 18.75 MB of payload data from client #8 (simulated).
[INFO][06:43:57]: [Server #328] Received 18.75 MB of payload data from client #3 (simulated).
[INFO][06:43:57]: [Server #328] Received 18.75 MB of payload data from client #14 (simulated).
[INFO][06:43:58]: [Server #328] Received 18.75 MB of payload data from client #15 (simulated).
[INFO][06:43:58]: [Server #328] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][06:43:58]: [Server #328] Received 18.75 MB of payload data from client #11 (simulated).
[INFO][06:43:58]: [Server #328] Received 18.75 MB of payload data from client #19 (simulated).
[INFO][06:43:58]: [Server #328] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][06:43:58]: [Server #328] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][06:43:58]: [Server #328] Selecting client #7 for training.
[INFO][06:43:58]: [Server #328] Sending the current model to client #7 (simulated).
[INFO][06:43:58]: [Server #328] Sending 18.75 MB of payload data to client #7 (simulated).
[INFO][06:43:58]: [Server #328] Selecting client #16 for training.
[INFO][06:43:58]: [Server #328] Sending the current model to client #16 (simulated).
[INFO][06:43:58]: [Server #328] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][06:43:58]: [Server #328] Selecting client #2 for training.
[INFO][06:43:58]: [Server #328] Sending the current model to client #2 (simulated).
[INFO][06:43:58]: [Server #328] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][06:43:58]: [Server #328] Selecting client #17 for training.
[INFO][06:43:58]: [Server #328] Sending the current model to client #17 (simulated).
[INFO][06:43:58]: [Server #328] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][06:43:58]: [Server #328] Selecting client #1 for training.
[INFO][06:43:58]: [Server #328] Sending the current model to client #1 (simulated).
[INFO][06:43:58]: [Server #328] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][06:43:58]: [Server #328] Selecting client #10 for training.
[INFO][06:43:58]: [Server #328] Sending the current model to client #10 (simulated).
[INFO][06:43:58]: [Server #328] Sending 18.75 MB of payload data to client #10 (simulated).
[INFO][06:43:58]: [Server #328] Selecting client #20 for training.
[INFO][06:43:58]: [Server #328] Sending the current model to client #20 (simulated).
[INFO][06:43:59]: [Server #328] Sending 18.75 MB of payload data to client #20 (simulated).
[INFO][06:43:59]: [Server #328] Selecting client #18 for training.
[INFO][06:43:59]: [Server #328] Sending the current model to client #18 (simulated).
[INFO][06:43:59]: [Server #328] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][06:43:59]: [Server #328] Selecting client #6 for training.
[INFO][06:43:59]: [Server #328] Sending the current model to client #6 (simulated).
[INFO][06:43:59]: [Server #328] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][06:43:59]: [Server #328] Selecting client #12 for training.
[INFO][06:43:59]: [Server #328] Sending the current model to client #12 (simulated).
[INFO][06:43:59]: [Server #328] Sending 18.75 MB of payload data to client #12 (simulated).
[INFO][06:46:23]: [Server #328] Received 18.75 MB of payload data from client #7 (simulated).
[INFO][06:46:27]: [Server #328] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][06:46:27]: [Server #328] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][06:46:27]: [Server #328] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][06:46:28]: [Server #328] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][06:46:28]: [Server #328] Received 18.75 MB of payload data from client #10 (simulated).
[INFO][06:46:28]: [Server #328] Received 18.75 MB of payload data from client #20 (simulated).
[INFO][06:46:28]: [Server #328] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][06:46:28]: [Server #328] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][06:46:28]: [Server #328] Received 18.75 MB of payload data from client #12 (simulated).
[INFO][06:46:28]: [Server #328] Adding client #7 to the list of clients for aggregation.
[INFO][06:46:28]: [Server #328] Adding client #2 to the list of clients for aggregation.
[INFO][06:46:28]: [Server #328] Adding client #16 to the list of clients for aggregation.
[INFO][06:46:28]: [Server #328] Adding client #1 to the list of clients for aggregation.
[INFO][06:46:28]: [Server #328] Adding client #6 to the list of clients for aggregation.
[INFO][06:46:28]: [Server #328] Adding client #10 to the list of clients for aggregation.
[INFO][06:46:28]: [Server #328] Adding client #17 to the list of clients for aggregation.
[INFO][06:46:28]: [Server #328] Adding client #20 to the list of clients for aggregation.
[INFO][06:46:28]: [Server #328] Adding client #18 to the list of clients for aggregation.
[INFO][06:46:28]: [Server #328] Adding client #12 to the list of clients for aggregation.
[INFO][06:46:28]: [Server #328] Aggregating 10 clients in total.
[INFO][06:46:28]: [Server #328] Updated weights have been received.
[INFO][06:46:28]: [Server #328] Aggregating model weight deltas.
[INFO][06:46:28]: [Server #328] Finished aggregating updated weights.
[INFO][06:46:28]: [Server #328] Started model testing.
[INFO][06:46:40]: [Trainer.test] 测试完成 - 准确率: 13.17% (1317/10000)
[INFO][06:46:40]: [93m[1m[Server #328] Global model accuracy: 13.17%
[0m
[INFO][06:46:40]: get_logged_items 被调用
[INFO][06:46:40]: 从updates获取参与客户端: [7, 2, 16, 1, 6, 10, 17, 20, 18, 12]
[WARNING][06:46:40]: 没有找到参与客户端的陈旧度信息
[INFO][06:46:40]: 最终logged_items: {'round': 1, 'accuracy': 0.1317, 'accuracy_std': 0, 'elapsed_time': 45.25733184814453, 'processing_time': 0.0038150000327732414, 'comm_time': 0, 'round_time': 45.257330300373724, 'comm_overhead': 749.9883651733398, 'global_accuracy': 0.1317, 'avg_staleness': 0.0, 'max_staleness': 0.0, 'min_staleness': 0.0, 'global_accuracy_std': 0.0}
[INFO][06:46:40]: [Server #328] All client reports have been processed.
[INFO][06:46:40]: [Server #328] Saving the checkpoint to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][06:46:40]: [Server #328] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][06:46:40]: [93m[1m
[Server #328] Starting round 2/400.[0m
[INFO][06:46:40]: [Server #328] Selected clients: [10, 7, 2, 6, 1, 12, 20, 16, 18, 17]
[INFO][06:46:40]: [Server #328] Selecting client #10 for training.
[INFO][06:46:40]: [Server #328] Sending the current model to client #10 (simulated).
[INFO][06:46:40]: [Server #328] Sending 18.75 MB of payload data to client #10 (simulated).
[INFO][06:46:40]: [Server #328] Selecting client #7 for training.
[INFO][06:46:40]: [Server #328] Sending the current model to client #7 (simulated).
[INFO][06:46:40]: [Server #328] Sending 18.75 MB of payload data to client #7 (simulated).
[INFO][06:46:40]: [Server #328] Selecting client #2 for training.
[INFO][06:46:40]: [Server #328] Sending the current model to client #2 (simulated).
[INFO][06:46:40]: [Server #328] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][06:46:40]: [Server #328] Selecting client #6 for training.
[INFO][06:46:40]: [Server #328] Sending the current model to client #6 (simulated).
[INFO][06:46:40]: [Server #328] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][06:46:40]: [Server #328] Selecting client #1 for training.
[INFO][06:46:40]: [Server #328] Sending the current model to client #1 (simulated).
[INFO][06:46:40]: [Server #328] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][06:46:40]: [Server #328] Selecting client #12 for training.
[INFO][06:46:40]: [Server #328] Sending the current model to client #12 (simulated).
[INFO][06:46:41]: [Server #328] Sending 18.75 MB of payload data to client #12 (simulated).
[INFO][06:46:41]: [Server #328] Selecting client #20 for training.
[INFO][06:46:41]: [Server #328] Sending the current model to client #20 (simulated).
[INFO][06:46:41]: [Server #328] Sending 18.75 MB of payload data to client #20 (simulated).
[INFO][06:46:41]: [Server #328] Selecting client #16 for training.
[INFO][06:46:41]: [Server #328] Sending the current model to client #16 (simulated).
[INFO][06:46:41]: [Server #328] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][06:46:41]: [Server #328] Selecting client #18 for training.
[INFO][06:46:42]: [Server #328] Sending the current model to client #18 (simulated).
[INFO][06:46:42]: [Server #328] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][06:46:42]: [Server #328] Selecting client #17 for training.
[INFO][06:46:42]: [Server #328] Sending the current model to client #17 (simulated).
[INFO][06:46:42]: [Server #328] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][06:47:14]: [Server #328] An existing client just disconnected.
[WARNING][06:47:14]: [Server #328] Client process #32996 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][06:47:14]: [93m[1m[Server #328] Closing the server due to a failed client.[0m
[INFO][06:47:14]: [Server #328] Training concluded.
[INFO][06:47:14]: [Server #328] Model saved to ./models/refedscafl/comparison_cifar10_alpha01/resnet_9.pth.
[INFO][06:47:14]: [Server #328] Closing the server.
[INFO][06:47:14]: Closing the connection to client #7832.
[INFO][06:47:14]: Closing the connection to client #22664.
[INFO][06:47:14]: Closing the connection to client #37076.
[INFO][06:47:14]: Closing the connection to client #7344.
[INFO][06:47:14]: Closing the connection to client #31828.
[INFO][06:47:14]: Closing the connection to client #38624.
[INFO][06:47:14]: Closing the connection to client #24340.
[INFO][06:47:14]: Closing the connection to client #24168.
[INFO][06:47:14]: Closing the connection to client #20872.
