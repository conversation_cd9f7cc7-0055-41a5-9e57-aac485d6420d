clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 3

    # The number of clients selected in each round
    per_round: 3

    # Should the clients compute test accuracy locally?
    do_test: true

server:
    address: 127.0.0.1
    port: 8000

data:
    # The training and testing dataset
    datasource: FashionMNIST

    # Number of samples in each partition
    partition_size: 20000

    # IID or non-IID?
    sampler: iid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 100

    # The maximum number of clients running concurrently
    max_concurrency: 3

    # The target accuracy
    target_accuracy: 0.88

    # The machine learning model
    model_name: lenet5
    
    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 32
    optimizer: SGD

algorithm:
    # Aggregation algorithm
    type: fedavg

parameters:
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0