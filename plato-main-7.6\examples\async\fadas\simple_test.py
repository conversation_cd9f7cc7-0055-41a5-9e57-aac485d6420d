#!/usr/bin/env python3
"""
简单的FedAS测试程序
直接测试失败模拟功能，不依赖复杂的Plato配置
"""

import os
import sys
import logging

# 添加Plato路径
current_dir = os.path.dirname(os.path.abspath(__file__))
plato_dir = os.path.join(current_dir, '../../..')
sys.path.insert(0, plato_dir)

# 导入失败模拟器
from communication_failure_simulator import CommunicationFailureSimulator


def test_with_fedas_simulation():
    """模拟FedAS训练过程，测试失败模拟效果"""
    print("🧪 FedAS通信失败模拟测试")
    print("=" * 50)
    
    # 创建失败模拟器
    config = {
        'base_communication_time': 0.5,
        'communication_noise_std': 0.5,
        'initial_threshold': 1.0,
        'enable_dynamic_threshold': True,
        'enable_logging': False  # 减少日志输出
    }
    
    simulator = CommunicationFailureSimulator(config)
    
    # 模拟FedAS训练过程
    total_clients = 6
    clients_per_round = 3
    rounds = 5
    
    print(f"配置: {total_clients}个客户端, 每轮选择{clients_per_round}个, 共{rounds}轮")
    print()
    
    # 统计信息
    round_stats = []
    
    for round_num in range(1, rounds + 1):
        print(f"🔄 第{round_num}轮训练:")
        
        # 选择客户端（简单轮询）
        selected_clients = [(round_num - 1) * clients_per_round + i for i in range(clients_per_round)]
        selected_clients = [c % total_clients + 1 for c in selected_clients]
        
        successful_updates = []
        failed_updates = []
        
        for client_id in selected_clients:
            # 模拟客户端训练完成，尝试上传
            success, comm_time, info = simulator.should_upload_succeed(client_id)
            
            if success:
                successful_updates.append({
                    'client_id': client_id,
                    'comm_time': comm_time,
                    'weights': f'weights_from_client_{client_id}'  # 模拟权重
                })
                print(f"   ✅ 客户端{client_id}: 上传成功 ({comm_time:.3f}s)")
            else:
                failed_updates.append({
                    'client_id': client_id,
                    'comm_time': comm_time
                })
                print(f"   ❌ 客户端{client_id}: 上传失败 ({comm_time:.3f}s)")
        
        # 聚合成功的更新
        if successful_updates:
            print(f"   📊 聚合{len(successful_updates)}个成功更新")
            # 这里可以添加实际的FedAS聚合逻辑
        else:
            print(f"   ⚠️  本轮没有成功更新，跳过聚合")
        
        # 记录本轮统计
        round_stat = {
            'round': round_num,
            'selected': len(selected_clients),
            'successful': len(successful_updates),
            'failed': len(failed_updates),
            'success_rate': len(successful_updates) / len(selected_clients) if selected_clients else 0
        }
        round_stats.append(round_stat)
        
        print(f"   📈 本轮成功率: {round_stat['success_rate']:.2%}")
        print()
    
    # 打印总体统计
    print("📊 训练完成统计:")
    print("-" * 50)
    
    total_selected = sum(stat['selected'] for stat in round_stats)
    total_successful = sum(stat['successful'] for stat in round_stats)
    total_failed = sum(stat['failed'] for stat in round_stats)
    overall_success_rate = total_successful / total_selected if total_selected > 0 else 0
    
    print(f"总选择次数: {total_selected}")
    print(f"总成功次数: {total_successful}")
    print(f"总失败次数: {total_failed}")
    print(f"总体成功率: {overall_success_rate:.2%}")
    
    # 获取模拟器统计
    sim_stats = simulator.get_statistics()
    overall_stats = sim_stats['overall']
    
    print(f"模拟器失败率: {overall_stats['failure_rate']:.2%}")
    print(f"当前通信阈值: {overall_stats['current_threshold']:.3f}s")
    print(f"平均通信时间: {overall_stats['avg_comm_time']:.3f}s")
    
    # 分析效果
    print("\n🎯 效果分析:")
    print("-" * 50)
    
    if overall_stats['failure_rate'] > 0:
        print("✅ 通信失败模拟正常工作")
        print(f"✅ 失败率 {overall_stats['failure_rate']:.1%} 会降低FedAS的训练效率")
        print("✅ 动态阈值调整机制正常运行")
        
        if overall_stats['failure_rate'] > 0.2:
            print("⚠️  失败率较高，可能显著影响收敛速度")
        elif overall_stats['failure_rate'] > 0.1:
            print("ℹ️  失败率适中，模拟真实网络环境")
        else:
            print("ℹ️  失败率较低，网络环境相对稳定")
    else:
        print("⚠️  没有发生失败，可能需要调整参数")
    
    print("\n💡 使用建议:")
    print("1. 将相同配置应用到其他算法（FedAC、ReFedScaFL等）")
    print("2. 对比不同算法在相同失败条件下的表现")
    print("3. 调整失败率参数测试不同网络环境")
    print("4. 分析ReFedScaFL的蒸馏补偿机制优势")
    
    return True


def main():
    """主函数"""
    logging.basicConfig(level=logging.WARNING)  # 减少日志输出
    
    try:
        success = test_with_fedas_simulation()
        
        if success:
            print(f"\n🎉 测试成功！失败模拟功能可以正常集成到FedAS中。")
            print(f"\n📋 下一步:")
            print(f"1. 确认这个失败模拟效果符合您的需求")
            print(f"2. 将相同的失败模拟应用到其他对比算法")
            print(f"3. 运行完整的对比实验")
        else:
            print(f"\n❌ 测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
