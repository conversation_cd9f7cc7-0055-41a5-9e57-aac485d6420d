#!/usr/bin/env python
"""
Test running a lot of selected configs.
"""

import subprocess
import time
import os

if __name__ == '__main__':
    for t in [
            'configs/MNIST/fedavg_async_lenet5',
            'configs/MNIST/fedavg_cross_silo_lenet5',
            # 'configs/MNIST/fedavg_lenet5_mindspore',
            'configs/MNIST/fedavg_lenet5_noniid',
            # 'configs/MNIST/fedavg_lenet5_tensorflow',
            'configs/MNIST/fedavg_lenet5',
            'configs/MNIST/fedprox_lenet5',
            # 'configs/MNIST/mistnet_pretrain_lenet5_mindspore',
            # 'configs/MNIST/mistnet_lenet5_mindspore',
            'configs/MNIST/mistnet_pretrain_lenet5',
            'configs/MNIST/mistnet_lenet5',
            '.github/workflows/configs/fedavg_yolov5',
            '.github/workflows/configs/mistnet_yolov5',
    ]:
        print(f"Running config: {t}.yml")
        log_root = '.log'
        log_folder = os.path.join(log_root,
                                  t.replace("/", "__").replace(".", ""))
        log_name = time.strftime("%Y_%m_%d__%H_%M_%S.txt", time.localtime())
        try:
            os.mkdir(log_root)
        except FileExistsError:
            pass
        try:
            os.mkdir(log_folder)
        except FileExistsError:
            pass
        with open(os.path.join(log_folder, log_name), 'a',
                  encoding='utf-8') as fp:
            fp.write(f'{t}\n\n')
            fp.flush()
            subprocess.call(['./run', f'--config={t}.yml'],
                            stdout=fp,
                            stderr=fp)
