# base dataset class for LEAF benchmark dataset
import json
import os
import numpy as np
import torch

from collections import defaultdict
from torch.utils.data import Dataset
from plato.config import Config
from plato.datasources import base


class CustomDictDataset(Dataset):
    """Custom dataset from a dictionary with support of transforms."""
    ALL_LETTERS = "\n !\"&'(),-.0123456789:;>?ABCDEFGHIJKLMNOPQRSTUVWXYZ[]abcdefghijklmnopqrstuvwxyz}"
    NUM_LETTERS = len(ALL_LETTERS)

    def letter_to_vec(self, letter):
        '''returns one-hot representation of given letter
        '''
        index = self.ALL_LETTERS.find(letter)
        return index

    def word_to_indices(self, word):
        '''returns a list of character indices
        Args:
            word: string

        Return:
            indices: int list with length len(word)
        '''
        indices = []
        for c in word:
            indices.append(self.ALL_LETTERS.find(c))
        return indices

    def __init__(self, loaded_data, transform=None):
        """Initializing the custom dataset."""
        super().__init__()
        self.loaded_data = loaded_data
        self.transform = transform

        # add
        # self.targets = self.loaded_data["y"]
        # self.classes = np.unique(self.targets)

    def __getitem__(self, index):
        sentence, target = self.loaded_data['x'][index], self.loaded_data['y'][index]
        indices = self.word_to_indices(sentence)
        target = self.letter_to_vec(target)
        # y = indices[1:].append(target)
        # target = indices[1:].append(target)
        indices = torch.LongTensor(np.array(indices))
        # y = torch.Tensor(np.array(y))
        # target = torch.LongTensor(np.array(target))
        return indices, target

    def __len__(self):
        return len(self.loaded_data["y"])

class ReadData(Dataset):
    # 加载所有用户的数据
    def __init__(self):
        super().__init__()

        self.trainset = None
        self.testset = None

        self.traindata = {}
        self.testdata = {}

        self.train_client_data = {}
        self.test_client_data = {}

        # use the official method to download and split the Shakespeare dataset
        # https://github.com/TalwalkarLab/leaf
        # the split .json file is perpared in direction data/shakespeare/train or test
        # read the data and load as dataset
        train_data_path = os.path.join(
            Config().params["data_path"], "shakespeare", "train"
        )
        test_data_path = os.path.join(
            Config().params["data_path"], "shakespeare", "test"
        )

        train_clients, train_groups, train_data_temp, test_data_temp = ReadData.read_data(
            train_data_path, test_data_path
        )

        train_data_x = []
        train_data_y = []

        test_data_x = []
        test_data_y = []

        for i in range(len(train_clients)):
            self.train_client_data[i] = set()
            l = len(train_data_x)
            cur_x = train_data_temp[train_clients[i]]['x']
            cur_y = train_data_temp[train_clients[i]]['y']
            for j in range(len(cur_x)):
                self.train_client_data[i].add(j + l)
                train_data_x.append(cur_x[j])
                train_data_y.append(cur_y[j])
        self.traindata['x'] = train_data_x
        self.traindata['y'] = train_data_y

        for i in range(len(train_clients)):
            self.test_client_data[i] = set()
            l = len(test_data_x)
            cur_x = test_data_temp[train_clients[i]]['x']
            cur_y = test_data_temp[train_clients[i]]['y']
            for j in range(len(cur_x)):
                self.test_client_data[i].add(j + l)
                test_data_x.append(cur_x[j])
                test_data_y.append(cur_y[j])
        self.testdata['x'] = test_data_x
        self.testdata['y'] = test_data_y

        # 完整的训练集和测试集
        self.trainset = CustomDictDataset(loaded_data=self.traindata)
        self.testset = CustomDictDataset(loaded_data=self.testdata)

    def get_client_dic(self, train: bool):
        if train:
            print(f"Number of training clients: {len(self.train_client_data)}")
            print(f"Training client IDs: {list(self.train_client_data.keys())}")
            return self.train_client_data
        else:
            print(f"Number of test clients: {len(self.test_client_data)}")
            print(f"Test client IDs: {list(self.test_client_data.keys())}")
            return self.test_client_data

    @staticmethod
    def read_data(train_data_dir, test_data_dir):
        '''parses data in given train and test data directories
        assumes:
        - the data in the input directories are .json files with
            keys 'users' and 'user_data'
        - the set of train set users is the same as the set of test set users

        Return:
            clients: list of client ids
            groups: list of group ids; empty list if none found
            train_data: dictionary of train data
            test_data: dictionary of test data
        '''
        train_clients, train_groups, train_data = ReadData.read_dir(train_data_dir)
        test_clients, test_groups, test_data = ReadData.read_dir(test_data_dir)

        assert train_clients == test_clients
        assert train_groups == test_groups

        return train_clients, train_groups, train_data, test_data

    @staticmethod
    def read_dir(data_dir):
        clients = []
        groups = []
        data = defaultdict(lambda: None)

        files = os.listdir(data_dir)
        files = [f for f in files if f.endswith('.json')]
        for f in files:
            file_path = os.path.join(data_dir, f)
            with open(file_path, 'r') as inf:
                cdata = json.load(inf)
            clients.extend(cdata['users'])
            if 'hierarchies' in cdata:
                groups.extend(cdata['hierarchies'])
            data.update(cdata['user_data'])

        clients = list(sorted(data.keys()))
        return clients, groups, data

class DatasetSplit(Dataset):
    def __init__(self, dataset, idxs):
        self.dataset = dataset
        self.idxs = list(idxs)

    def __len__(self):
        return len(self.idxs)

    def __getitem__(self, item):
        image, label = self.dataset[self.idxs[item]]
        return image, label

class DataSource(base.DataSource):
    # 只加载某个用户的数据集
    def __init__(self, client_id=0, **kwargs):
        super().__init__()

        # 获得完整的训练集和测试集
        read_data = ReadData()
        train_data = read_data.trainset
        test_data = read_data.testset

        # 获得完整的用户分类
        train_dict_users = read_data.get_client_dic(train=True)
        test_dict_users = read_data.get_client_dic(train=False)

        # 获得对应用户的训练和测试数据
        self.trainset = DatasetSplit(train_data, train_dict_users[client_id])
        self.testset = DatasetSplit(test_data, test_dict_users[client_id])

    def num_train_examples(self):
        return len(self.trainset)

    def num_test_examples(self):
        return len(self.testset)



