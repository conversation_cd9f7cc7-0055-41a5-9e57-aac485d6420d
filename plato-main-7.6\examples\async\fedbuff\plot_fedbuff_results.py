"""
FedBuff训练结果可视化工具

该脚本用于可视化FedBuff联邦学习的训练结果，包括准确率、陈旧度等指标。
支持从CSV文件中读取数据并生成图表。
"""

import os
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
from matplotlib.font_manager import FontProperties
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# 设置中文字体
def set_chinese_font():
    """设置支持中文的字体"""
    # 尝试多种中文字体，按优先级排序
    chinese_fonts = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'STSong', 'SimSun']
    for font in chinese_fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            return True
        except:
            continue
    return False

def search_result_files(directory=None):
    """搜索训练结果CSV文件"""
    result_files = []
    
    # 如果没有指定目录，尝试常见的结果目录
    if directory is None:
        potential_dirs = [
            "./results",
            "./results/fedbuff_metrics",
            "../results",
            "../results/fedbuff_metrics",
        ]
        
        for d in potential_dirs:
            if os.path.exists(d):
                for file in glob.glob(os.path.join(d, "*.csv")):
                    if "fedbuff_metrics" in file or "training_results" in file:
                        result_files.append(file)
    else:
        # 如果指定了目录，在该目录下搜索
        if os.path.exists(directory):
            for file in glob.glob(os.path.join(directory, "*.csv")):
                if "fedbuff_metrics" in file or "training_results" in file:
                    result_files.append(file)
    
    return result_files

def load_and_process_data(csv_path):
    """加载并处理CSV数据"""
    try:
        # 尝试读取带表头的CSV
        df = pd.read_csv(csv_path)
        
        # 检查是否有表头
        if df.shape[1] < 3:
            # 如果没有表头，重新读取并指定列名
            df = pd.read_csv(csv_path, header=None, names=['round', 'elapsed_time', 'accuracy', 'avg_staleness'])
        
        # 确保数据列是数值类型
        for col in ['elapsed_time', 'accuracy', 'avg_staleness']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 计算关键指标
        max_acc = df['accuracy'].max() if 'accuracy' in df.columns else 0
        last_10_acc = df['accuracy'].tail(10).mean() if 'accuracy' in df.columns and len(df) >= 10 else 0
        avg_staleness = df['avg_staleness'].mean() if 'avg_staleness' in df.columns else 0
        
        # 计算达到特定准确率所需的时间
        time_to_70 = df[df['accuracy'] >= 0.7]['elapsed_time'].min() if 'accuracy' in df.columns and (df['accuracy'] >= 0.7).any() else None
        time_to_80 = df[df['accuracy'] >= 0.8]['elapsed_time'].min() if 'accuracy' in df.columns and (df['accuracy'] >= 0.8).any() else None
        time_to_90 = df[df['accuracy'] >= 0.9]['elapsed_time'].min() if 'accuracy' in df.columns and (df['accuracy'] >= 0.9).any() else None
        
        return df, max_acc, last_10_acc, avg_staleness, time_to_70, time_to_80, time_to_90
    except Exception as e:
        logging.error(f"加载CSV文件失败: {e}")
        return None, 0, 0, 0, None, None, None

def plot_accuracy_curve(df, output_dir):
    """绘制准确率曲线"""
    plt.figure(figsize=(10, 6))
    
    # 绘制准确率曲线
    if 'round' in df.columns and 'accuracy' in df.columns:
        plt.plot(df['round'], df['accuracy'], 'b-', marker='o', markersize=3, label='准确率')
        
        # 添加平滑曲线（使用移动平均）
        window_size = min(5, len(df))
        if window_size > 1:
            smooth_acc = df['accuracy'].rolling(window=window_size).mean()
            plt.plot(df['round'], smooth_acc, 'r--', linewidth=2, label=f'平滑准确率 (窗口={window_size})')
    
    plt.xlabel('训练轮次')
    plt.ylabel('准确率')
    plt.title('FedBuff训练准确率曲线')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    # 保存图表
    output_path = os.path.join(output_dir, 'accuracy_curve.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    logging.info(f"准确率曲线已保存至: {output_path}")
    
    plt.close()

def plot_staleness_curve(df, output_dir):
    """绘制陈旧度曲线"""
    if 'avg_staleness' not in df.columns:
        logging.warning("CSV文件中没有陈旧度数据，跳过陈旧度曲线绘制")
        return
    
    plt.figure(figsize=(10, 6))
    
    # 绘制陈旧度曲线
    if 'round' in df.columns:
        plt.plot(df['round'], df['avg_staleness'], 'g-', marker='o', markersize=3, label='平均陈旧度')
        
        # 添加平滑曲线（使用移动平均）
        window_size = min(5, len(df))
        if window_size > 1:
            smooth_staleness = df['avg_staleness'].rolling(window=window_size).mean()
            plt.plot(df['round'], smooth_staleness, 'r--', linewidth=2, label=f'平滑陈旧度 (窗口={window_size})')
    
    plt.xlabel('训练轮次')
    plt.ylabel('平均陈旧度')
    plt.title('FedBuff客户端平均陈旧度曲线')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    # 保存图表
    output_path = os.path.join(output_dir, 'staleness_curve.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    logging.info(f"陈旧度曲线已保存至: {output_path}")
    
    plt.close()

def plot_time_accuracy(df, output_dir):
    """绘制时间-准确率曲线"""
    plt.figure(figsize=(10, 6))
    
    # 绘制时间-准确率曲线
    if 'elapsed_time' in df.columns and 'accuracy' in df.columns:
        plt.plot(df['elapsed_time'], df['accuracy'], 'b-', marker='o', markersize=3, label='准确率')
        
        # 添加平滑曲线（使用移动平均）
        window_size = min(5, len(df))
        if window_size > 1:
            df_sorted = df.sort_values('elapsed_time')
            smooth_acc = df_sorted['accuracy'].rolling(window=window_size).mean()
            plt.plot(df_sorted['elapsed_time'], smooth_acc, 'r--', linewidth=2, label=f'平滑准确率 (窗口={window_size})')
    
    plt.xlabel('训练时间 (秒)')
    plt.ylabel('准确率')
    plt.title('FedBuff训练时间-准确率曲线')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    # 保存图表
    output_path = os.path.join(output_dir, 'time_accuracy_curve.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    logging.info(f"时间-准确率曲线已保存至: {output_path}")
    
    plt.close()

def create_summary_report(df, max_acc, last_10_acc, avg_staleness, time_to_70, time_to_80, time_to_90, output_dir):
    """创建训练结果摘要报告"""
    plt.figure(figsize=(12, 8))
    
    # 创建一个没有坐标轴的图
    ax = plt.subplot(111)
    ax.axis('off')
    
    # 准备报告内容
    report_title = "FedBuff训练结果摘要报告"
    
    # 训练基本信息
    total_rounds = df['round'].max() if 'round' in df.columns else 0
    total_time = df['elapsed_time'].max() if 'elapsed_time' in df.columns else 0
    
    # 准备文本内容
    report_text = f"""
训练轮次: {total_rounds:.0f}
总训练时间: {total_time:.2f} 秒

性能指标:
  - 最高准确率: {max_acc:.4f}
  - 最后10轮平均准确率: {last_10_acc:.4f}
  - 平均陈旧度: {avg_staleness:.4f}

达到目标准确率所需时间:
  - 70% 准确率: {f"{time_to_70:.2f} 秒" if time_to_70 is not None else "未达到"}
  - 80% 准确率: {f"{time_to_80:.2f} 秒" if time_to_80 is not None else "未达到"}
  - 90% 准确率: {f"{time_to_90:.2f} 秒" if time_to_90 is not None else "未达到"}
"""
    
    # 添加标题
    plt.text(0.5, 0.95, report_title, fontsize=20, ha='center', va='top', weight='bold')
    
    # 添加报告内容
    plt.text(0.1, 0.85, report_text, fontsize=14, ha='left', va='top')
    
    # 保存报告
    output_path = os.path.join(output_dir, 'training_summary_report.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    logging.info(f"训练摘要报告已保存至: {output_path}")
    
    plt.close()

def main():
    # 设置命令行参数
    parser = argparse.ArgumentParser(description='FedBuff训练结果可视化工具')
    parser.add_argument('--csv', type=str, help='CSV结果文件路径')
    parser.add_argument('--dir', type=str, help='结果文件目录')
    parser.add_argument('--output', type=str, default='fedbuff_visualization_results', help='输出目录')
    args = parser.parse_args()
    
    # 设置中文字体
    set_chinese_font()
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 搜索结果文件
    csv_files = []
    if args.csv and os.path.exists(args.csv):
        csv_files = [args.csv]
    elif args.dir:
        csv_files = search_result_files(args.dir)
    else:
        csv_files = search_result_files()
    
    if not csv_files:
        logging.error("未找到任何结果文件")
        return
    
    # 处理找到的第一个CSV文件
    csv_path = csv_files[0]
    logging.info(f"使用结果文件: {csv_path}")
    
    # 加载并处理数据
    df, max_acc, last_10_acc, avg_staleness, time_to_70, time_to_80, time_to_90 = load_and_process_data(csv_path)
    if df is None:
        return
    
    # 生成可视化图表
    plot_accuracy_curve(df, args.output)
    plot_staleness_curve(df, args.output)
    plot_time_accuracy(df, args.output)
    create_summary_report(df, max_acc, last_10_acc, avg_staleness, time_to_70, time_to_80, time_to_90, args.output)
    
    logging.info(f"所有可视化结果已保存到 {args.output} 目录")

if __name__ == "__main__":
    main() 