"""
SCAFL主程序
师妹学习版本 - 详细注释

这是SCAFL的入口程序，负责：
1. 加载配置文件
2. 创建SCAFL组件（服务器、客户端、算法）
3. 启动训练过程
4. 处理异常和日志
"""

import os
import sys
import logging
import traceback

# 添加plato路径，确保能导入plato模块
# 这里假设我们在examples/scafl_tutorial目录下
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 先导入Config，其他模块稍后导入（避免配置问题）
from plato.config import Config


def setup_logging():
    """设置日志系统
    
    为SCAFL设置详细的日志，便于调试和分析
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),  # 控制台输出
            logging.FileHandler('scafl_training.log', mode='w', encoding='utf-8')  # 文件输出
        ]
    )
    
    # 设置特定模块的日志级别
    logging.getLogger('scafl_algorithm').setLevel(logging.DEBUG)
    logging.getLogger('scafl_server').setLevel(logging.DEBUG)
    logging.getLogger('scafl_client').setLevel(logging.INFO)


def load_config():
    """加载SCAFL配置文件
    
    Returns:
        bool: 配置加载是否成功
    """
    try:
        # 配置文件路径
        config_file = os.path.join(os.path.dirname(__file__), 'scafl_config.yml')
        
        if not os.path.exists(config_file):
            logging.error(f"❌ 配置文件不存在: {config_file}")
            return False
        
        # 设置配置文件路径到环境变量
        os.environ['config_file'] = config_file
        
        # 创建Config实例（会自动读取环境变量中的配置文件）
        config = Config()
        
        logging.info(f"✅ 成功加载配置文件: {config_file}")
        return True
        
    except Exception as e:
        logging.error(f"❌ 加载配置文件失败: {str(e)}")
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False


def create_scafl_components():
    """创建SCAFL组件
    
    Returns:
        tuple: (server, client) 或 (None, None) 如果创建失败
    """
    try:
        # 现在可以安全导入其他plato模块
        from scafl_server import Server
        from scafl_client import Client
        from scafl_algorithm import Algorithm
        
        logging.info("📦 开始创建SCAFL组件...")
        
        # 创建SCAFL服务器
        # 注意：我们不直接传入algorithm，让服务器自己创建SCAFL算法实例
        server = Server()
        logging.info("✅ SCAFL服务器创建成功")
        
        # 创建SCAFL客户端
        client = Client()
        logging.info("✅ SCAFL客户端创建成功")
        
        return server, client
        
    except Exception as e:
        logging.error(f"❌ 创建SCAFL组件失败: {str(e)}")
        logging.error(f"错误详情: {traceback.format_exc()}")
        return None, None


def print_experiment_info():
    """打印实验信息
    
    显示当前实验的配置信息，便于理解和调试
    """
    try:
        config = Config()
        
        logging.info("=" * 60)
        logging.info("🎯 SCAFL实验配置信息")
        logging.info("=" * 60)
        logging.info(f"📊 基本设置:")
        logging.info(f"   - 总客户端数: {config.clients.total_clients}")
        logging.info(f"   - 每轮训练客户端数: {config.clients.per_round}")
        logging.info(f"   - 训练轮数: {config.trainer.rounds}")
        logging.info(f"   - 目标准确率: {config.trainer.target_accuracy}")
        
        logging.info(f"🔧 SCAFL参数:")
        logging.info(f"   - 最大陈旧度 (τmax): {config.server.tau_max}")
        logging.info(f"   - Lyapunov参数 (V): {config.server.V}")
        logging.info(f"   - 最大聚合客户端数: {config.server.max_aggregation_clients}")
        
        logging.info(f"📚 数据和模型:")
        logging.info(f"   - 数据集: {config.data.datasource}")
        logging.info(f"   - 模型: {config.trainer.model_name}")
        logging.info(f"   - 每客户端数据量: {config.data.partition_size}")
        logging.info(f"   - 数据分布: {config.data.sampler}")
        
        logging.info(f"⚙️ 训练参数:")
        logging.info(f"   - 学习率: {config.trainer.learning_rate}")
        logging.info(f"   - 批大小: {config.trainer.batch_size}")
        logging.info(f"   - 本地训练轮数: {config.trainer.epochs}")
        logging.info("=" * 60)
        
    except Exception as e:
        logging.warning(f"⚠️ 无法显示实验信息: {str(e)}")


def main():
    """SCAFL主函数"""
    
    # 设置日志
    setup_logging()
    
    logging.info("🚀 启动SCAFL (Staleness-Controlled Asynchronous Federated Learning)")
    logging.info("👩‍🏫 师妹学习版本 - 详细注释")
    
    try:
        # 第一步：加载配置文件
        logging.info("📋 第一步：加载配置文件...")
        if not load_config():
            logging.error("❌ 配置文件加载失败，程序退出")
            return
        
        # 第二步：显示实验信息
        logging.info("📊 第二步：显示实验配置...")
        print_experiment_info()
        
        # 第三步：创建SCAFL组件
        logging.info("🔧 第三步：创建SCAFL组件...")
        server, client = create_scafl_components()
        
        if server is None or client is None:
            logging.error("❌ SCAFL组件创建失败，程序退出")
            return
        
        # 第四步：启动训练
        logging.info("🎯 第四步：开始SCAFL训练...")
        logging.info("💡 提示：训练过程中会显示详细的SCAFL算法执行信息")
        logging.info("📝 日志文件：scafl_training.log")
        
        # 启动训练（这会阻塞直到训练完成）
        server.run(client)
        
        logging.info("🎉 SCAFL训练完成！")
        logging.info("📈 请查看日志文件了解详细的训练过程")
        
    except KeyboardInterrupt:
        logging.info("⏹️ 用户中断训练")
    except Exception as e:
        logging.error(f"❌ SCAFL训练过程中出错: {str(e)}")
        logging.error(f"🔍 错误详情: {traceback.format_exc()}")
        logging.error("💡 请检查配置文件和依赖是否正确安装")
    finally:
        logging.info("🔚 SCAFL程序结束")


if __name__ == "__main__":
    main()
