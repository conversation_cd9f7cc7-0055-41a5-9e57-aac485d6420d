2025-08-01 11:23:35,304 - INFO - 🚀 SC-AFL服务器启动 - 2025-08-01 11:23:35
2025-08-01 11:23:35,304 - INFO - ✅ 新日志文件已创建
2025-08-01 11:23:35,304 - INFO - 📁 日志目录: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs
2025-08-01 11:23:35,304 - INFO - 📄 日志文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs\sc_afl_server_20250801_112335_13576.log
2025-08-01 11:23:35,305 - INFO - 🔧 日志级别: DEBUG (文件), INFO (控制台)
2025-08-01 11:23:35,305 - INFO - 📊 文件模式: 新建模式 (每次启动创建新文件)
2025-08-01 11:23:35,305 - INFO - 🆔 进程ID: 13576
2025-08-01 11:23:35,305 - INFO - ✅ 日志文件创建成功，当前大小: 675 字节
2025-08-01 11:23:35,305 - INFO - 🚀 SC-AFL服务器初始化开始...
2025-08-01 11:23:35,305 - INFO - 📅 初始化时间: 2025-08-01 11:23:35
2025-08-01 11:23:35,307 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:35,325 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:35,325 - INFO - Server: 动态创建模型 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:23:35,325 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:23:35,337 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:35,337 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:23:35,337 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:23:35,337 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:23:35,338 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:35,338 - INFO - [Trainer None] 初始化完成
2025-08-01 11:23:35,338 - INFO - Server: 创建了新的Trainer实例
2025-08-01 11:23:35,338 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:23:35,338 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:23:35,338 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:35,338 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:35,339 - INFO - Server: 创建了新的Algorithm实例
2025-08-01 11:23:35,339 - INFO - [93m[1m[13576] Logging runtime results to: ./results/cifar10_with_network/13576.csv.[0m
2025-08-01 11:23:35,339 - INFO - [Server #13576] Started training on 6 clients with 3 per round.
2025-08-01 11:23:35,339 - INFO - [DEBUG] 从配置文件读取 simulate_wall_time=True
2025-08-01 11:23:35,339 - WARNING - Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。
2025-08-01 11:23:35,340 - WARNING - Server: 训练器在初始化后为None，重新创建
2025-08-01 11:23:35,340 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:23:35,340 - WARNING - [Trainer None] 模型为None，尝试创建默认模型
2025-08-01 11:23:35,340 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:35,354 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:35,354 - INFO - [Trainer None] 动态创建模型 resnet_9，输入通道: 3, 类别数: 10
2025-08-01 11:23:35,363 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:35,363 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:23:35,363 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:23:35,364 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:23:35,364 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:35,364 - INFO - [Trainer None] 初始化完成
2025-08-01 11:23:35,364 - INFO - Server: 重新创建了Trainer实例
2025-08-01 11:23:35,364 - INFO - [Algorithm] 已设置服务器引用 (客户端ID: None)
2025-08-01 11:23:35,364 - INFO - Server: 算法类已设置服务器引用
2025-08-01 11:23:35,365 - INFO - 动态加载数据集: CIFAR10
2025-08-01 11:23:35,855 - INFO - ✅ 成功加载数据集 CIFAR10: 10000 样本
2025-08-01 11:23:35,856 - INFO - ✅ 动态加载测试集成功: CIFAR10, 大小: 10000
2025-08-01 11:23:35,856 - INFO - ✅ 测试加载器已创建
2025-08-01 11:23:35,856 - INFO - 开始初始化全局模型权重
2025-08-01 11:23:35,857 - WARNING - 全局模型实例为None，正在尝试重新创建...
2025-08-01 11:23:35,857 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:35,870 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:35,870 - INFO - 成功重新创建了全局模型实例 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:23:35,875 - INFO - [全局权重摘要] 参数数量: 74, 均值: 0.001180, 最大: 1.000000, 最小: -0.192368
2025-08-01 11:23:35,875 - INFO - [全局模型] 输入通道数: 3
2025-08-01 11:23:35,875 - INFO - 全局模型权重初始化成功
2025-08-01 11:23:35,876 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:23:35,877 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:23:35,877 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:23:35,878 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_112335.csv
2025-08-01 11:23:35,878 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:23:35,878 - WARNING - 网络模拟器初始化失败: 'Config' object has no attribute 'get'
2025-08-01 11:23:35,878 - INFO - SC-AFL算法参数: tau_max=5, V=1.0
2025-08-01 11:23:35,878 - INFO - 服务器初始化完成
2025-08-01 11:23:35,878 - INFO - 已创建并注册 0 个客户端（将在 Server.start() 中启动任务）
2025-08-01 11:23:35,879 - INFO - 客户端ID管理器初始化完成，总客户端数: 6, ID起始值: 1
2025-08-01 11:23:35,879 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:23:35,879 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:23:35,879 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:23:35,879 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_112335.csv
2025-08-01 11:23:35,879 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:23:35,880 - INFO - 服务器实例创建成功
2025-08-01 11:23:35,880 - INFO - 正在创建和注册 6 个客户端...
2025-08-01 11:23:35,880 - INFO - 客户端ID配置: 起始ID=1, 总数=6
2025-08-01 11:23:35,880 - INFO - 开始创建客户端 1...
2025-08-01 11:23:35,880 - INFO - 初始化客户端, ID: 1
2025-08-01 11:23:35,880 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:35,895 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:35,895 - INFO - [Client 1] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:35,895 - INFO - [Trainer] 初始化训练器, client_id: 1
2025-08-01 11:23:35,905 - INFO - [Trainer 1] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:35,905 - INFO - [Trainer 1] 模型的输入通道数: 3
2025-08-01 11:23:35,905 - INFO - [Trainer 1] 强制使用CPU
2025-08-01 11:23:35,905 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:23:35,906 - INFO - [Trainer 1] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:35,906 - INFO - [Trainer 1] 初始化完成
2025-08-01 11:23:35,906 - INFO - [Client 1] 创建新训练器
2025-08-01 11:23:35,906 - INFO - [Algorithm] 从训练器获取客户端ID: 1
2025-08-01 11:23:35,906 - INFO - [Algorithm] 初始化后修正client_id: 1
2025-08-01 11:23:35,906 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:35,907 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:35,907 - INFO - [Client 1] 创建新算法
2025-08-01 11:23:35,907 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:23:35,907 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:23:35,907 - INFO - [Client None] 父类初始化完成
2025-08-01 11:23:35,907 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:35,921 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:35,921 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:35,922 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:23:35,931 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:35,931 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:23:35,931 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:23:35,932 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:23:35,932 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:35,932 - INFO - [Trainer None] 初始化完成
2025-08-01 11:23:35,932 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:23:35,932 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:23:35,932 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:23:35,932 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:35,932 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:35,932 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:23:35,933 - INFO - [Client None] 开始加载数据
2025-08-01 11:23:35,933 - INFO - 顺序分配客户端ID: 1
2025-08-01 11:23:35,933 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 1
2025-08-01 11:23:35,934 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:23:35,934 - WARNING - [Client 1] 数据源为None，已创建新数据源
2025-08-01 11:23:35,934 - WARNING - [Client 1] 数据源trainset为None，已设置为空列表
2025-08-01 11:23:36,523 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:23:36,523 - INFO - [Client 1] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:23:36,524 - INFO - [Client 1] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:23:36,528 - INFO - [Client 1] 成功划分数据集，分配到 300 个样本
2025-08-01 11:23:36,528 - INFO - [Client 1] 初始化时成功加载数据
2025-08-01 11:23:36,529 - INFO - [客户端 1] 初始化验证通过
2025-08-01 11:23:36,529 - INFO - 客户端 1 实例创建成功
2025-08-01 11:23:36,529 - INFO - 客户端1已设置服务器引用
2025-08-01 11:23:36,529 - INFO - 客户端 1 已设置服务器引用
2025-08-01 11:23:36,529 - INFO - 客户端1已注册
2025-08-01 11:23:36,529 - INFO - 客户端 1 已成功注册到服务器
2025-08-01 11:23:36,529 - INFO - 开始创建客户端 2...
2025-08-01 11:23:36,530 - INFO - 初始化客户端, ID: 2
2025-08-01 11:23:36,530 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:36,545 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:36,545 - INFO - [Client 2] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:36,545 - INFO - [Trainer] 初始化训练器, client_id: 2
2025-08-01 11:23:36,555 - INFO - [Trainer 2] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:36,556 - INFO - [Trainer 2] 模型的输入通道数: 3
2025-08-01 11:23:36,556 - INFO - [Trainer 2] 强制使用CPU
2025-08-01 11:23:36,556 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 11:23:36,556 - INFO - [Trainer 2] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:36,556 - INFO - [Trainer 2] 初始化完成
2025-08-01 11:23:36,557 - INFO - [Client 2] 创建新训练器
2025-08-01 11:23:36,557 - INFO - [Algorithm] 从训练器获取客户端ID: 2
2025-08-01 11:23:36,557 - INFO - [Algorithm] 初始化后修正client_id: 2
2025-08-01 11:23:36,557 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:36,557 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:36,557 - INFO - [Client 2] 创建新算法
2025-08-01 11:23:36,557 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:23:36,557 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:23:36,557 - INFO - [Client None] 父类初始化完成
2025-08-01 11:23:36,557 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:36,571 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:36,572 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:36,572 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:23:36,581 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:36,582 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:23:36,582 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:23:36,582 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:23:36,582 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:36,583 - INFO - [Trainer None] 初始化完成
2025-08-01 11:23:36,583 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:23:36,583 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:23:36,583 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:23:36,583 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:36,583 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:36,583 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:23:36,583 - INFO - [Client None] 开始加载数据
2025-08-01 11:23:36,583 - INFO - 顺序分配客户端ID: 2
2025-08-01 11:23:36,583 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 2
2025-08-01 11:23:36,584 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:23:36,584 - WARNING - [Client 2] 数据源为None，已创建新数据源
2025-08-01 11:23:36,584 - WARNING - [Client 2] 数据源trainset为None，已设置为空列表
2025-08-01 11:23:37,182 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:23:37,183 - INFO - [Client 2] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:23:37,183 - INFO - [Client 2] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:23:37,186 - INFO - [Client 2] 成功划分数据集，分配到 300 个样本
2025-08-01 11:23:37,187 - INFO - [Client 2] 初始化时成功加载数据
2025-08-01 11:23:37,187 - INFO - [客户端 2] 初始化验证通过
2025-08-01 11:23:37,187 - INFO - 客户端 2 实例创建成功
2025-08-01 11:23:37,187 - INFO - 客户端2已设置服务器引用
2025-08-01 11:23:37,187 - INFO - 客户端 2 已设置服务器引用
2025-08-01 11:23:37,188 - INFO - 客户端2已注册
2025-08-01 11:23:37,188 - INFO - 客户端 2 已成功注册到服务器
2025-08-01 11:23:37,188 - INFO - 开始创建客户端 3...
2025-08-01 11:23:37,188 - INFO - 初始化客户端, ID: 3
2025-08-01 11:23:37,188 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:37,203 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:37,203 - INFO - [Client 3] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:37,203 - INFO - [Trainer] 初始化训练器, client_id: 3
2025-08-01 11:23:37,214 - INFO - [Trainer 3] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:37,214 - INFO - [Trainer 3] 模型的输入通道数: 3
2025-08-01 11:23:37,214 - INFO - [Trainer 3] 强制使用CPU
2025-08-01 11:23:37,215 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:23:37,215 - INFO - [Trainer 3] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:37,215 - INFO - [Trainer 3] 初始化完成
2025-08-01 11:23:37,215 - INFO - [Client 3] 创建新训练器
2025-08-01 11:23:37,215 - INFO - [Algorithm] 从训练器获取客户端ID: 3
2025-08-01 11:23:37,215 - INFO - [Algorithm] 初始化后修正client_id: 3
2025-08-01 11:23:37,215 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:37,215 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:37,215 - INFO - [Client 3] 创建新算法
2025-08-01 11:23:37,216 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:23:37,216 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:23:37,216 - INFO - [Client None] 父类初始化完成
2025-08-01 11:23:37,216 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:37,229 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:37,229 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:37,230 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:23:37,239 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:37,240 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:23:37,240 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:23:37,240 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:23:37,240 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:37,240 - INFO - [Trainer None] 初始化完成
2025-08-01 11:23:37,241 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:23:37,241 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:23:37,241 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:23:37,241 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:37,241 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:37,241 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:23:37,241 - INFO - [Client None] 开始加载数据
2025-08-01 11:23:37,241 - INFO - 顺序分配客户端ID: 3
2025-08-01 11:23:37,241 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 3
2025-08-01 11:23:37,241 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:23:37,242 - WARNING - [Client 3] 数据源为None，已创建新数据源
2025-08-01 11:23:37,242 - WARNING - [Client 3] 数据源trainset为None，已设置为空列表
2025-08-01 11:23:37,841 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:23:37,841 - INFO - [Client 3] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:23:37,842 - INFO - [Client 3] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:23:37,846 - INFO - [Client 3] 成功划分数据集，分配到 300 个样本
2025-08-01 11:23:37,846 - INFO - [Client 3] 初始化时成功加载数据
2025-08-01 11:23:37,847 - INFO - [客户端 3] 初始化验证通过
2025-08-01 11:23:37,847 - INFO - 客户端 3 实例创建成功
2025-08-01 11:23:37,847 - INFO - 客户端3已设置服务器引用
2025-08-01 11:23:37,847 - INFO - 客户端 3 已设置服务器引用
2025-08-01 11:23:37,847 - INFO - 客户端3已注册
2025-08-01 11:23:37,848 - INFO - 客户端 3 已成功注册到服务器
2025-08-01 11:23:37,848 - INFO - 开始创建客户端 4...
2025-08-01 11:23:37,848 - INFO - 初始化客户端, ID: 4
2025-08-01 11:23:37,848 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:37,863 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:37,863 - INFO - [Client 4] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:37,863 - INFO - [Trainer] 初始化训练器, client_id: 4
2025-08-01 11:23:37,874 - INFO - [Trainer 4] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:37,875 - INFO - [Trainer 4] 模型的输入通道数: 3
2025-08-01 11:23:37,875 - INFO - [Trainer 4] 强制使用CPU
2025-08-01 11:23:37,875 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 11:23:37,876 - INFO - [Trainer 4] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:37,876 - INFO - [Trainer 4] 初始化完成
2025-08-01 11:23:37,876 - INFO - [Client 4] 创建新训练器
2025-08-01 11:23:37,876 - INFO - [Algorithm] 从训练器获取客户端ID: 4
2025-08-01 11:23:37,876 - INFO - [Algorithm] 初始化后修正client_id: 4
2025-08-01 11:23:37,876 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:37,876 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:37,877 - INFO - [Client 4] 创建新算法
2025-08-01 11:23:37,877 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:23:37,877 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:23:37,877 - INFO - [Client None] 父类初始化完成
2025-08-01 11:23:37,877 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:37,891 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:37,891 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:37,891 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:23:37,904 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:37,905 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:23:37,905 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:23:37,905 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:23:37,906 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:37,906 - INFO - [Trainer None] 初始化完成
2025-08-01 11:23:37,906 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:23:37,906 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:23:37,906 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:23:37,906 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:37,906 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:37,907 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:23:37,907 - INFO - [Client None] 开始加载数据
2025-08-01 11:23:37,907 - INFO - 顺序分配客户端ID: 4
2025-08-01 11:23:37,907 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 4
2025-08-01 11:23:37,907 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:23:37,907 - WARNING - [Client 4] 数据源为None，已创建新数据源
2025-08-01 11:23:37,908 - WARNING - [Client 4] 数据源trainset为None，已设置为空列表
2025-08-01 11:23:38,511 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:23:38,512 - INFO - [Client 4] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:23:38,512 - INFO - [Client 4] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:23:38,516 - INFO - [Client 4] 成功划分数据集，分配到 300 个样本
2025-08-01 11:23:38,516 - INFO - [Client 4] 初始化时成功加载数据
2025-08-01 11:23:38,516 - INFO - [客户端 4] 初始化验证通过
2025-08-01 11:23:38,517 - INFO - 客户端 4 实例创建成功
2025-08-01 11:23:38,517 - INFO - 客户端4已设置服务器引用
2025-08-01 11:23:38,517 - INFO - 客户端 4 已设置服务器引用
2025-08-01 11:23:38,517 - INFO - 客户端4已注册
2025-08-01 11:23:38,517 - INFO - 客户端 4 已成功注册到服务器
2025-08-01 11:23:38,517 - INFO - 开始创建客户端 5...
2025-08-01 11:23:38,517 - INFO - 初始化客户端, ID: 5
2025-08-01 11:23:38,517 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:38,532 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:38,532 - INFO - [Client 5] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:38,532 - INFO - [Trainer] 初始化训练器, client_id: 5
2025-08-01 11:23:38,543 - INFO - [Trainer 5] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:38,544 - INFO - [Trainer 5] 模型的输入通道数: 3
2025-08-01 11:23:38,544 - INFO - [Trainer 5] 强制使用CPU
2025-08-01 11:23:38,544 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 11:23:38,544 - INFO - [Trainer 5] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:38,545 - INFO - [Trainer 5] 初始化完成
2025-08-01 11:23:38,545 - INFO - [Client 5] 创建新训练器
2025-08-01 11:23:38,545 - INFO - [Algorithm] 从训练器获取客户端ID: 5
2025-08-01 11:23:38,545 - INFO - [Algorithm] 初始化后修正client_id: 5
2025-08-01 11:23:38,545 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:38,545 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:38,545 - INFO - [Client 5] 创建新算法
2025-08-01 11:23:38,545 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:23:38,545 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:23:38,545 - INFO - [Client None] 父类初始化完成
2025-08-01 11:23:38,546 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:38,559 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:38,560 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:38,560 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:23:38,570 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:38,570 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:23:38,570 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:23:38,571 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:23:38,571 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:38,571 - INFO - [Trainer None] 初始化完成
2025-08-01 11:23:38,571 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:23:38,571 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:23:38,571 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:23:38,571 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:38,571 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:38,572 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:23:38,572 - INFO - [Client None] 开始加载数据
2025-08-01 11:23:38,572 - INFO - 顺序分配客户端ID: 5
2025-08-01 11:23:38,572 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 5
2025-08-01 11:23:38,572 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:23:38,572 - WARNING - [Client 5] 数据源为None，已创建新数据源
2025-08-01 11:23:38,572 - WARNING - [Client 5] 数据源trainset为None，已设置为空列表
2025-08-01 11:23:39,169 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:23:39,169 - INFO - [Client 5] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:23:39,169 - INFO - [Client 5] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:23:39,174 - INFO - [Client 5] 成功划分数据集，分配到 300 个样本
2025-08-01 11:23:39,174 - INFO - [Client 5] 初始化时成功加载数据
2025-08-01 11:23:39,174 - INFO - [客户端 5] 初始化验证通过
2025-08-01 11:23:39,174 - INFO - 客户端 5 实例创建成功
2025-08-01 11:23:39,174 - INFO - 客户端5已设置服务器引用
2025-08-01 11:23:39,174 - INFO - 客户端 5 已设置服务器引用
2025-08-01 11:23:39,174 - INFO - 客户端5已注册
2025-08-01 11:23:39,175 - INFO - 客户端 5 已成功注册到服务器
2025-08-01 11:23:39,175 - INFO - 开始创建客户端 6...
2025-08-01 11:23:39,175 - INFO - 初始化客户端, ID: 6
2025-08-01 11:23:39,175 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:39,189 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:39,189 - INFO - [Client 6] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:39,190 - INFO - [Trainer] 初始化训练器, client_id: 6
2025-08-01 11:23:39,198 - INFO - [Trainer 6] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:39,198 - INFO - [Trainer 6] 模型的输入通道数: 3
2025-08-01 11:23:39,199 - INFO - [Trainer 6] 强制使用CPU
2025-08-01 11:23:39,199 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:23:39,199 - INFO - [Trainer 6] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:39,199 - INFO - [Trainer 6] 初始化完成
2025-08-01 11:23:39,199 - INFO - [Client 6] 创建新训练器
2025-08-01 11:23:39,199 - INFO - [Algorithm] 从训练器获取客户端ID: 6
2025-08-01 11:23:39,199 - INFO - [Algorithm] 初始化后修正client_id: 6
2025-08-01 11:23:39,199 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:39,199 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:39,200 - INFO - [Client 6] 创建新算法
2025-08-01 11:23:39,200 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:23:39,200 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:23:39,200 - INFO - [Client None] 父类初始化完成
2025-08-01 11:23:39,200 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:23:39,213 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:23:39,214 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:23:39,214 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:23:39,222 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:23:39,222 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:23:39,222 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:23:39,223 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:23:39,223 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:39,223 - INFO - [Trainer None] 初始化完成
2025-08-01 11:23:39,223 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:23:39,223 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:23:39,223 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:23:39,223 - INFO - [Algorithm] 初始化完成
2025-08-01 11:23:39,223 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:23:39,223 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:23:39,224 - INFO - [Client None] 开始加载数据
2025-08-01 11:23:39,224 - INFO - 顺序分配客户端ID: 6
2025-08-01 11:23:39,224 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 6
2025-08-01 11:23:39,224 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:23:39,224 - WARNING - [Client 6] 数据源为None，已创建新数据源
2025-08-01 11:23:39,224 - WARNING - [Client 6] 数据源trainset为None，已设置为空列表
2025-08-01 11:23:39,854 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:23:39,855 - INFO - [Client 6] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:23:39,855 - INFO - [Client 6] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:23:39,860 - INFO - [Client 6] 成功划分数据集，分配到 300 个样本
2025-08-01 11:23:39,860 - INFO - [Client 6] 初始化时成功加载数据
2025-08-01 11:23:39,860 - INFO - [客户端 6] 初始化验证通过
2025-08-01 11:23:39,861 - INFO - 客户端 6 实例创建成功
2025-08-01 11:23:39,861 - INFO - 客户端6已设置服务器引用
2025-08-01 11:23:39,861 - INFO - 客户端 6 已设置服务器引用
2025-08-01 11:23:39,861 - INFO - 客户端6已注册
2025-08-01 11:23:39,861 - INFO - 客户端 6 已成功注册到服务器
2025-08-01 11:23:39,861 - INFO - 已成功创建和注册 6 个客户端
2025-08-01 11:23:39,862 - INFO - 服务器属性检查:
2025-08-01 11:23:39,862 - INFO - - 客户端数量: 6
2025-08-01 11:23:39,862 - INFO - - 全局模型: 已初始化
2025-08-01 11:23:39,862 - INFO - - 算法: 已初始化
2025-08-01 11:23:39,862 - INFO - - 训练器: 已初始化
2025-08-01 11:23:39,862 - INFO - 准备启动服务器...
2025-08-01 11:23:39,862 - INFO - [Server #13576] 启动中...
2025-08-01 11:23:39,862 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:23:39,875 - INFO - 服务器将使用事件循环: <ProactorEventLoop running=False closed=False debug=False>
2025-08-01 11:23:39,875 - INFO - ✅ 服务器已有 6 个客户端，开始启动训练
2025-08-01 11:23:39,877 - INFO - [Client 1] 模型已放置到设备: cpu
2025-08-01 11:23:39,891 - INFO - [Client 1] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,900 - INFO - [Client 1] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,901 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:23:39,901 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:23:39,901 - INFO - [Client 1] 已更新algorithm的client_id
2025-08-01 11:23:39,901 - INFO - [Client 1] 模型初始化完成
2025-08-01 11:23:39,902 - INFO - 客户端 1 模型初始化成功
2025-08-01 11:23:39,903 - INFO - 客户端 1 异步训练线程已启动
2025-08-01 11:23:39,903 - INFO - [Client 2] 模型已放置到设备: cpu
2025-08-01 11:23:39,909 - INFO - [Client 2] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,916 - INFO - [Client 2] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,916 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:23:39,917 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:23:39,917 - INFO - [Client 2] 已更新algorithm的client_id
2025-08-01 11:23:39,917 - INFO - [Client 2] 模型初始化完成
2025-08-01 11:23:39,917 - INFO - 客户端 2 模型初始化成功
2025-08-01 11:23:39,917 - INFO - 客户端 2 异步训练线程已启动
2025-08-01 11:23:39,918 - INFO - [Client 3] 模型已放置到设备: cpu
2025-08-01 11:23:39,924 - INFO - [Client 3] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,932 - INFO - [Client 3] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,932 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:23:39,932 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:23:39,933 - INFO - [Client 3] 已更新algorithm的client_id
2025-08-01 11:23:39,933 - INFO - [Client 3] 模型初始化完成
2025-08-01 11:23:39,933 - INFO - 客户端 3 模型初始化成功
2025-08-01 11:23:39,933 - INFO - 客户端 3 异步训练线程已启动
2025-08-01 11:23:39,934 - INFO - [Client 4] 模型已放置到设备: cpu
2025-08-01 11:23:39,939 - INFO - [Client 4] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,947 - INFO - [Client 4] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,947 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:23:39,947 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:23:39,947 - INFO - [Client 4] 已更新algorithm的client_id
2025-08-01 11:23:39,948 - INFO - [Client 4] 模型初始化完成
2025-08-01 11:23:39,948 - INFO - 客户端 4 模型初始化成功
2025-08-01 11:23:39,948 - INFO - 客户端 4 异步训练线程已启动
2025-08-01 11:23:39,949 - INFO - [Client 5] 模型已放置到设备: cpu
2025-08-01 11:23:39,954 - INFO - [Client 5] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,962 - INFO - [Client 5] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,962 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:23:39,962 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:23:39,962 - INFO - [Client 5] 已更新algorithm的client_id
2025-08-01 11:23:39,962 - INFO - [Client 5] 模型初始化完成
2025-08-01 11:23:39,963 - INFO - 客户端 5 模型初始化成功
2025-08-01 11:23:39,963 - INFO - 客户端 5 异步训练线程已启动
2025-08-01 11:23:39,964 - INFO - [Client 6] 模型已放置到设备: cpu
2025-08-01 11:23:39,970 - INFO - [Client 6] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,978 - INFO - [Client 6] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:23:39,978 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:23:39,978 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:23:39,978 - INFO - [Client 6] 已更新algorithm的client_id
2025-08-01 11:23:39,979 - INFO - [Client 6] 模型初始化完成
2025-08-01 11:23:39,979 - INFO - 客户端 6 模型初始化成功
2025-08-01 11:23:39,979 - INFO - 客户端 6 异步训练线程已启动
2025-08-01 11:23:39,980 - INFO - 服务器主循环任务已启动: <Task pending name='Task-1' coro=<Server.run() running at D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py:1278>>
2025-08-01 11:23:39,980 - INFO - Starting a server at address 127.0.0.1 and port 8000.
2025-08-01 11:23:39,982 - INFO - [Server #13576] 开始训练，共有 6 个客户端，每轮最多聚合 3 个客户端
2025-08-01 11:23:39,982 - INFO - 总训练轮次: 10
2025-08-01 11:23:39,983 - INFO - 🚀 开始第 1 轮训练（目标：10 轮）
2025-08-01 11:23:39,983 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:23:39,983 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:23:40,072 - INFO - 客户端 1 开始异步训练循环
2025-08-01 11:23:40,072 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:23:40,073 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 11:23:40,073 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 11:23:40,073 - INFO - [Client 1] 开始验证训练集
2025-08-01 11:23:40,074 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:23:40,075 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 11:23:40,075 - INFO - [Trainer 1] 开始训练
2025-08-01 11:23:40,075 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 11:23:40,076 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:23:40,076 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:23:40,076 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:23:40,077 - INFO - [Trainer 1] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:23:40,077 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 11:23:40,086 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2612, y=[0, 0, 5, 8, 5]
2025-08-01 11:23:40,087 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 11:23:40,091 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 11:23:40,096 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1448
2025-08-01 11:23:40,096 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:23:40,096 - INFO - [Trainer 1] 标签样本: [5, 4, 5, 0, 5]
2025-08-01 11:23:40,265 - INFO - [Trainer 1] Batch 0, Loss: 2.4466
2025-08-01 11:23:40,999 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:23:40,999 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:23:41,195 - INFO - [Trainer 1] Batch 5, Loss: 1.6957
