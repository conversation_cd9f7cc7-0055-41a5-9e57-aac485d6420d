# FedSCAFL - 联邦学习异步客户端选择算法

FedSCAFL是一种面向异步联邦学习的客户端选择和过时度处理算法。它能够根据客户端的过时程度和通信时间，动态选择最优的客户端子集进行训练。

## 主要功能

1. **过时度感知的客户端选择**: 根据客户端的过时程度、通信时间和计算能力，选择最优的客户端子集。
2. **过时度加权聚合**: 根据客户端的过时程度，对客户端上传的模型进行加权聚合，降低过时客户端的影响。
3. **自适应调度**: 考虑客户端的实时状态，动态调整客户端选择策略。

## 测试脚本

我们提供了一个测试脚本`test_client_selection.py`，用于验证客户端选择算法的正确性。

### 运行测试

```bash
cd plato-main/examples/async/fedscafl
python test_client_selection.py
```

测试脚本会创建一个模拟的服务器环境，生成随机的客户端过时度和通信时间，然后使用SCAFL算法选择最优的客户端子集。

## 主要参数

- `tau_max`: 最大允许的过时度，默认为10
- `V`: 权衡通信时间和过时度的参数，默认为10

## 日志记录

FedSCAFL会记录详细的日志，包括：
- 客户端过时度信息
- 客户端选择过程
- 模型聚合权重
- 训练准确率和过时度历史

## 注意事项

1. 确保在运行前已正确配置Plato环境
2. 如果遇到"NoneType has no attribute 'client_staleness'"错误，请检查算法实例是否正确初始化
3. 如果客户端选择函数没有被调用，请检查是否正确重写了`_select_clients`方法 