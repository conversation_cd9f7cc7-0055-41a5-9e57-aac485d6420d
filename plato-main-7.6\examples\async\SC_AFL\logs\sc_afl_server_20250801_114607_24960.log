2025-08-01 11:46:07,860 - INFO - 🚀 SC-AFL服务器启动 - 2025-08-01 11:46:07
2025-08-01 11:46:07,860 - INFO - ✅ 新日志文件已创建
2025-08-01 11:46:07,860 - INFO - 📁 日志目录: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs
2025-08-01 11:46:07,860 - INFO - 📄 日志文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs\sc_afl_server_20250801_114607_24960.log
2025-08-01 11:46:07,861 - INFO - 🔧 日志级别: DEBUG (文件), INFO (控制台)
2025-08-01 11:46:07,862 - INFO - 📊 文件模式: 新建模式 (每次启动创建新文件)
2025-08-01 11:46:07,862 - INFO - 🆔 进程ID: 24960
2025-08-01 11:46:07,862 - INFO - ✅ 日志文件创建成功，当前大小: 675 字节
2025-08-01 11:46:07,862 - INFO - 🚀 SC-AFL服务器初始化开始...
2025-08-01 11:46:07,862 - INFO - 📅 初始化时间: 2025-08-01 11:46:07
2025-08-01 11:46:07,863 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:07,881 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:07,881 - INFO - Server: 动态创建模型 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:46:07,881 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:46:07,894 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:07,895 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:46:07,895 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:46:07,895 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:46:07,896 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:07,896 - INFO - [Trainer None] 初始化完成
2025-08-01 11:46:07,896 - INFO - Server: 创建了新的Trainer实例
2025-08-01 11:46:07,896 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:46:07,897 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:46:07,897 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:07,897 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:07,897 - INFO - Server: 创建了新的Algorithm实例
2025-08-01 11:46:07,897 - INFO - [93m[1m[24960] Logging runtime results to: ./results/cifar10_with_network/24960.csv.[0m
2025-08-01 11:46:07,898 - INFO - [Server #24960] Started training on 6 clients with 3 per round.
2025-08-01 11:46:07,898 - INFO - [DEBUG] 从配置文件读取 simulate_wall_time=True
2025-08-01 11:46:07,898 - WARNING - Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。
2025-08-01 11:46:07,898 - WARNING - Server: 训练器在初始化后为None，重新创建
2025-08-01 11:46:07,898 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:46:07,898 - WARNING - [Trainer None] 模型为None，尝试创建默认模型
2025-08-01 11:46:07,898 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:07,913 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:07,913 - INFO - [Trainer None] 动态创建模型 resnet_9，输入通道: 3, 类别数: 10
2025-08-01 11:46:07,923 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:07,923 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:46:07,923 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:46:07,923 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:46:07,924 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:07,924 - INFO - [Trainer None] 初始化完成
2025-08-01 11:46:07,924 - INFO - Server: 重新创建了Trainer实例
2025-08-01 11:46:07,924 - INFO - [Algorithm] 已设置服务器引用 (客户端ID: None)
2025-08-01 11:46:07,924 - INFO - Server: 算法类已设置服务器引用
2025-08-01 11:46:07,924 - INFO - 动态加载数据集: CIFAR10
2025-08-01 11:46:08,427 - INFO - ✅ 成功加载数据集 CIFAR10: 10000 样本
2025-08-01 11:46:08,428 - INFO - ✅ 动态加载测试集成功: CIFAR10, 大小: 10000
2025-08-01 11:46:08,428 - INFO - ✅ 测试加载器已创建
2025-08-01 11:46:08,428 - INFO - 开始初始化全局模型权重
2025-08-01 11:46:08,428 - WARNING - 全局模型实例为None，正在尝试重新创建...
2025-08-01 11:46:08,429 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:08,442 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:08,443 - INFO - 成功重新创建了全局模型实例 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:46:08,450 - INFO - [全局权重摘要] 参数数量: 74, 均值: 0.001171, 最大: 1.000000, 最小: -0.191912
2025-08-01 11:46:08,450 - INFO - [全局模型] 输入通道数: 3
2025-08-01 11:46:08,451 - INFO - 全局模型权重初始化成功
2025-08-01 11:46:08,452 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:46:08,452 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:46:08,453 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:46:08,454 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_114608.csv
2025-08-01 11:46:08,456 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:46:08,456 - WARNING - 网络模拟器初始化失败: 'Config' object has no attribute 'get'
2025-08-01 11:46:08,456 - INFO - SC-AFL算法参数: tau_max=5, V=1.0
2025-08-01 11:46:08,456 - INFO - 服务器初始化完成
2025-08-01 11:46:08,456 - INFO - 已创建并注册 0 个客户端（将在 Server.start() 中启动任务）
2025-08-01 11:46:08,457 - INFO - 客户端ID管理器初始化完成，总客户端数: 6, ID起始值: 1
2025-08-01 11:46:08,457 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:46:08,457 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:46:08,457 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:46:08,458 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_114608.csv
2025-08-01 11:46:08,458 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:46:08,458 - INFO - 服务器实例创建成功
2025-08-01 11:46:08,458 - INFO - 正在创建和注册 6 个客户端...
2025-08-01 11:46:08,458 - INFO - 客户端ID配置: 起始ID=1, 总数=6
2025-08-01 11:46:08,458 - INFO - 开始创建客户端 1...
2025-08-01 11:46:08,458 - INFO - 初始化客户端, ID: 1
2025-08-01 11:46:08,458 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:08,475 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:08,476 - INFO - [Client 1] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:08,476 - INFO - [Trainer] 初始化训练器, client_id: 1
2025-08-01 11:46:08,487 - INFO - [Trainer 1] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:08,487 - INFO - [Trainer 1] 模型的输入通道数: 3
2025-08-01 11:46:08,487 - INFO - [Trainer 1] 强制使用CPU
2025-08-01 11:46:08,488 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:46:08,488 - INFO - [Trainer 1] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:08,488 - INFO - [Trainer 1] 初始化完成
2025-08-01 11:46:08,488 - INFO - [Client 1] 创建新训练器
2025-08-01 11:46:08,489 - INFO - [Algorithm] 从训练器获取客户端ID: 1
2025-08-01 11:46:08,489 - INFO - [Algorithm] 初始化后修正client_id: 1
2025-08-01 11:46:08,489 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:08,489 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:08,489 - INFO - [Client 1] 创建新算法
2025-08-01 11:46:08,489 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:46:08,489 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:46:08,489 - INFO - [Client None] 父类初始化完成
2025-08-01 11:46:08,489 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:08,504 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:08,505 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:08,505 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:46:08,514 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:08,514 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:46:08,515 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:46:08,515 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:46:08,515 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:08,515 - INFO - [Trainer None] 初始化完成
2025-08-01 11:46:08,516 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:46:08,516 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:46:08,516 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:46:08,516 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:08,516 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:08,516 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:46:08,516 - INFO - [Client None] 开始加载数据
2025-08-01 11:46:08,516 - INFO - 顺序分配客户端ID: 1
2025-08-01 11:46:08,517 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 1
2025-08-01 11:46:08,518 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:46:08,518 - WARNING - [Client 1] 数据源为None，已创建新数据源
2025-08-01 11:46:08,518 - WARNING - [Client 1] 数据源trainset为None，已设置为空列表
2025-08-01 11:46:09,138 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:46:09,138 - INFO - [Client 1] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:46:09,138 - INFO - [Client 1] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:46:09,142 - INFO - [Client 1] 成功划分数据集，分配到 300 个样本
2025-08-01 11:46:09,142 - INFO - [Client 1] 初始化时成功加载数据
2025-08-01 11:46:09,143 - INFO - [客户端 1] 初始化验证通过
2025-08-01 11:46:09,143 - INFO - 客户端 1 实例创建成功
2025-08-01 11:46:09,143 - INFO - 客户端1已设置服务器引用
2025-08-01 11:46:09,143 - INFO - 客户端 1 已设置服务器引用
2025-08-01 11:46:09,143 - INFO - 客户端1已注册
2025-08-01 11:46:09,143 - INFO - 客户端 1 已成功注册到服务器
2025-08-01 11:46:09,143 - INFO - 开始创建客户端 2...
2025-08-01 11:46:09,143 - INFO - 初始化客户端, ID: 2
2025-08-01 11:46:09,143 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:09,159 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:09,159 - INFO - [Client 2] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:09,159 - INFO - [Trainer] 初始化训练器, client_id: 2
2025-08-01 11:46:09,170 - INFO - [Trainer 2] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:09,171 - INFO - [Trainer 2] 模型的输入通道数: 3
2025-08-01 11:46:09,171 - INFO - [Trainer 2] 强制使用CPU
2025-08-01 11:46:09,171 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 11:46:09,171 - INFO - [Trainer 2] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:09,172 - INFO - [Trainer 2] 初始化完成
2025-08-01 11:46:09,172 - INFO - [Client 2] 创建新训练器
2025-08-01 11:46:09,172 - INFO - [Algorithm] 从训练器获取客户端ID: 2
2025-08-01 11:46:09,172 - INFO - [Algorithm] 初始化后修正client_id: 2
2025-08-01 11:46:09,172 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:09,172 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:09,172 - INFO - [Client 2] 创建新算法
2025-08-01 11:46:09,172 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:46:09,172 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:46:09,172 - INFO - [Client None] 父类初始化完成
2025-08-01 11:46:09,172 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:09,187 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:09,187 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:09,187 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:46:09,197 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:09,197 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:46:09,197 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:46:09,197 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:46:09,198 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:09,198 - INFO - [Trainer None] 初始化完成
2025-08-01 11:46:09,198 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:46:09,198 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:46:09,198 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:46:09,198 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:09,198 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:09,198 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:46:09,198 - INFO - [Client None] 开始加载数据
2025-08-01 11:46:09,198 - INFO - 顺序分配客户端ID: 2
2025-08-01 11:46:09,198 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 2
2025-08-01 11:46:09,199 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:46:09,199 - WARNING - [Client 2] 数据源为None，已创建新数据源
2025-08-01 11:46:09,199 - WARNING - [Client 2] 数据源trainset为None，已设置为空列表
2025-08-01 11:46:09,842 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:46:09,842 - INFO - [Client 2] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:46:09,843 - INFO - [Client 2] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:46:09,847 - INFO - [Client 2] 成功划分数据集，分配到 300 个样本
2025-08-01 11:46:09,847 - INFO - [Client 2] 初始化时成功加载数据
2025-08-01 11:46:09,848 - INFO - [客户端 2] 初始化验证通过
2025-08-01 11:46:09,848 - INFO - 客户端 2 实例创建成功
2025-08-01 11:46:09,848 - INFO - 客户端2已设置服务器引用
2025-08-01 11:46:09,848 - INFO - 客户端 2 已设置服务器引用
2025-08-01 11:46:09,849 - INFO - 客户端2已注册
2025-08-01 11:46:09,849 - INFO - 客户端 2 已成功注册到服务器
2025-08-01 11:46:09,849 - INFO - 开始创建客户端 3...
2025-08-01 11:46:09,849 - INFO - 初始化客户端, ID: 3
2025-08-01 11:46:09,849 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:09,864 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:09,864 - INFO - [Client 3] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:09,865 - INFO - [Trainer] 初始化训练器, client_id: 3
2025-08-01 11:46:09,877 - INFO - [Trainer 3] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:09,878 - INFO - [Trainer 3] 模型的输入通道数: 3
2025-08-01 11:46:09,878 - INFO - [Trainer 3] 强制使用CPU
2025-08-01 11:46:09,878 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:46:09,879 - INFO - [Trainer 3] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:09,879 - INFO - [Trainer 3] 初始化完成
2025-08-01 11:46:09,879 - INFO - [Client 3] 创建新训练器
2025-08-01 11:46:09,879 - INFO - [Algorithm] 从训练器获取客户端ID: 3
2025-08-01 11:46:09,879 - INFO - [Algorithm] 初始化后修正client_id: 3
2025-08-01 11:46:09,879 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:09,879 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:09,880 - INFO - [Client 3] 创建新算法
2025-08-01 11:46:09,880 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:46:09,880 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:46:09,880 - INFO - [Client None] 父类初始化完成
2025-08-01 11:46:09,880 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:09,894 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:09,894 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:09,894 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:46:09,905 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:09,905 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:46:09,905 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:46:09,906 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:46:09,906 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:09,906 - INFO - [Trainer None] 初始化完成
2025-08-01 11:46:09,906 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:46:09,906 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:46:09,906 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:46:09,906 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:09,907 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:09,907 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:46:09,907 - INFO - [Client None] 开始加载数据
2025-08-01 11:46:09,907 - INFO - 顺序分配客户端ID: 3
2025-08-01 11:46:09,907 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 3
2025-08-01 11:46:09,907 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:46:09,908 - WARNING - [Client 3] 数据源为None，已创建新数据源
2025-08-01 11:46:09,908 - WARNING - [Client 3] 数据源trainset为None，已设置为空列表
2025-08-01 11:46:10,526 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:46:10,526 - INFO - [Client 3] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:46:10,526 - INFO - [Client 3] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:46:10,530 - INFO - [Client 3] 成功划分数据集，分配到 300 个样本
2025-08-01 11:46:10,530 - INFO - [Client 3] 初始化时成功加载数据
2025-08-01 11:46:10,531 - INFO - [客户端 3] 初始化验证通过
2025-08-01 11:46:10,531 - INFO - 客户端 3 实例创建成功
2025-08-01 11:46:10,531 - INFO - 客户端3已设置服务器引用
2025-08-01 11:46:10,531 - INFO - 客户端 3 已设置服务器引用
2025-08-01 11:46:10,531 - INFO - 客户端3已注册
2025-08-01 11:46:10,531 - INFO - 客户端 3 已成功注册到服务器
2025-08-01 11:46:10,531 - INFO - 开始创建客户端 4...
2025-08-01 11:46:10,531 - INFO - 初始化客户端, ID: 4
2025-08-01 11:46:10,531 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:10,547 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:10,548 - INFO - [Client 4] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:10,548 - INFO - [Trainer] 初始化训练器, client_id: 4
2025-08-01 11:46:10,557 - INFO - [Trainer 4] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:10,557 - INFO - [Trainer 4] 模型的输入通道数: 3
2025-08-01 11:46:10,558 - INFO - [Trainer 4] 强制使用CPU
2025-08-01 11:46:10,558 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 11:46:10,558 - INFO - [Trainer 4] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:10,558 - INFO - [Trainer 4] 初始化完成
2025-08-01 11:46:10,558 - INFO - [Client 4] 创建新训练器
2025-08-01 11:46:10,558 - INFO - [Algorithm] 从训练器获取客户端ID: 4
2025-08-01 11:46:10,559 - INFO - [Algorithm] 初始化后修正client_id: 4
2025-08-01 11:46:10,559 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:10,559 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:10,559 - INFO - [Client 4] 创建新算法
2025-08-01 11:46:10,559 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:46:10,559 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:46:10,559 - INFO - [Client None] 父类初始化完成
2025-08-01 11:46:10,559 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:10,574 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:10,574 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:10,575 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:46:10,584 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:10,584 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:46:10,584 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:46:10,584 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:46:10,585 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:10,585 - INFO - [Trainer None] 初始化完成
2025-08-01 11:46:10,585 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:46:10,585 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:46:10,585 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:46:10,585 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:10,585 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:10,585 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:46:10,585 - INFO - [Client None] 开始加载数据
2025-08-01 11:46:10,585 - INFO - 顺序分配客户端ID: 4
2025-08-01 11:46:10,585 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 4
2025-08-01 11:46:10,586 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:46:10,586 - WARNING - [Client 4] 数据源为None，已创建新数据源
2025-08-01 11:46:10,586 - WARNING - [Client 4] 数据源trainset为None，已设置为空列表
2025-08-01 11:46:11,192 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:46:11,192 - INFO - [Client 4] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:46:11,193 - INFO - [Client 4] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:46:11,197 - INFO - [Client 4] 成功划分数据集，分配到 300 个样本
2025-08-01 11:46:11,197 - INFO - [Client 4] 初始化时成功加载数据
2025-08-01 11:46:11,197 - INFO - [客户端 4] 初始化验证通过
2025-08-01 11:46:11,197 - INFO - 客户端 4 实例创建成功
2025-08-01 11:46:11,197 - INFO - 客户端4已设置服务器引用
2025-08-01 11:46:11,198 - INFO - 客户端 4 已设置服务器引用
2025-08-01 11:46:11,198 - INFO - 客户端4已注册
2025-08-01 11:46:11,198 - INFO - 客户端 4 已成功注册到服务器
2025-08-01 11:46:11,198 - INFO - 开始创建客户端 5...
2025-08-01 11:46:11,198 - INFO - 初始化客户端, ID: 5
2025-08-01 11:46:11,198 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:11,212 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:11,212 - INFO - [Client 5] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:11,213 - INFO - [Trainer] 初始化训练器, client_id: 5
2025-08-01 11:46:11,222 - INFO - [Trainer 5] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:11,222 - INFO - [Trainer 5] 模型的输入通道数: 3
2025-08-01 11:46:11,223 - INFO - [Trainer 5] 强制使用CPU
2025-08-01 11:46:11,223 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 11:46:11,223 - INFO - [Trainer 5] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:11,223 - INFO - [Trainer 5] 初始化完成
2025-08-01 11:46:11,223 - INFO - [Client 5] 创建新训练器
2025-08-01 11:46:11,223 - INFO - [Algorithm] 从训练器获取客户端ID: 5
2025-08-01 11:46:11,224 - INFO - [Algorithm] 初始化后修正client_id: 5
2025-08-01 11:46:11,224 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:11,224 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:11,224 - INFO - [Client 5] 创建新算法
2025-08-01 11:46:11,224 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:46:11,224 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:46:11,224 - INFO - [Client None] 父类初始化完成
2025-08-01 11:46:11,224 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:11,238 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:11,239 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:11,239 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:46:11,249 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:11,249 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:46:11,249 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:46:11,250 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:46:11,250 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:11,250 - INFO - [Trainer None] 初始化完成
2025-08-01 11:46:11,250 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:46:11,250 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:46:11,251 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:46:11,251 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:11,251 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:11,251 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:46:11,251 - INFO - [Client None] 开始加载数据
2025-08-01 11:46:11,251 - INFO - 顺序分配客户端ID: 5
2025-08-01 11:46:11,251 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 5
2025-08-01 11:46:11,251 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:46:11,252 - WARNING - [Client 5] 数据源为None，已创建新数据源
2025-08-01 11:46:11,252 - WARNING - [Client 5] 数据源trainset为None，已设置为空列表
2025-08-01 11:46:11,858 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:46:11,859 - INFO - [Client 5] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:46:11,859 - INFO - [Client 5] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:46:11,863 - INFO - [Client 5] 成功划分数据集，分配到 300 个样本
2025-08-01 11:46:11,863 - INFO - [Client 5] 初始化时成功加载数据
2025-08-01 11:46:11,864 - INFO - [客户端 5] 初始化验证通过
2025-08-01 11:46:11,864 - INFO - 客户端 5 实例创建成功
2025-08-01 11:46:11,864 - INFO - 客户端5已设置服务器引用
2025-08-01 11:46:11,864 - INFO - 客户端 5 已设置服务器引用
2025-08-01 11:46:11,864 - INFO - 客户端5已注册
2025-08-01 11:46:11,865 - INFO - 客户端 5 已成功注册到服务器
2025-08-01 11:46:11,865 - INFO - 开始创建客户端 6...
2025-08-01 11:46:11,865 - INFO - 初始化客户端, ID: 6
2025-08-01 11:46:11,865 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:11,880 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:11,880 - INFO - [Client 6] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:11,880 - INFO - [Trainer] 初始化训练器, client_id: 6
2025-08-01 11:46:11,890 - INFO - [Trainer 6] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:11,890 - INFO - [Trainer 6] 模型的输入通道数: 3
2025-08-01 11:46:11,891 - INFO - [Trainer 6] 强制使用CPU
2025-08-01 11:46:11,891 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:46:11,891 - INFO - [Trainer 6] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:11,891 - INFO - [Trainer 6] 初始化完成
2025-08-01 11:46:11,891 - INFO - [Client 6] 创建新训练器
2025-08-01 11:46:11,892 - INFO - [Algorithm] 从训练器获取客户端ID: 6
2025-08-01 11:46:11,892 - INFO - [Algorithm] 初始化后修正client_id: 6
2025-08-01 11:46:11,892 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:11,892 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:11,892 - INFO - [Client 6] 创建新算法
2025-08-01 11:46:11,892 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:46:11,892 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:46:11,892 - INFO - [Client None] 父类初始化完成
2025-08-01 11:46:11,892 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:46:11,907 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:46:11,907 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:46:11,907 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:46:11,917 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:46:11,917 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:46:11,917 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:46:11,918 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:46:11,918 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:46:11,918 - INFO - [Trainer None] 初始化完成
2025-08-01 11:46:11,918 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:46:11,918 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:46:11,918 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:46:11,919 - INFO - [Algorithm] 初始化完成
2025-08-01 11:46:11,919 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:46:11,919 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:46:11,919 - INFO - [Client None] 开始加载数据
2025-08-01 11:46:11,919 - INFO - 顺序分配客户端ID: 6
2025-08-01 11:46:11,919 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 6
2025-08-01 11:46:11,919 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:46:11,920 - WARNING - [Client 6] 数据源为None，已创建新数据源
2025-08-01 11:46:11,920 - WARNING - [Client 6] 数据源trainset为None，已设置为空列表
2025-08-01 11:46:12,524 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:46:12,525 - INFO - [Client 6] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:46:12,525 - INFO - [Client 6] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:46:12,530 - INFO - [Client 6] 成功划分数据集，分配到 300 个样本
2025-08-01 11:46:12,530 - INFO - [Client 6] 初始化时成功加载数据
2025-08-01 11:46:12,530 - INFO - [客户端 6] 初始化验证通过
2025-08-01 11:46:12,530 - INFO - 客户端 6 实例创建成功
2025-08-01 11:46:12,530 - INFO - 客户端6已设置服务器引用
2025-08-01 11:46:12,531 - INFO - 客户端 6 已设置服务器引用
2025-08-01 11:46:12,531 - INFO - 客户端6已注册
2025-08-01 11:46:12,531 - INFO - 客户端 6 已成功注册到服务器
2025-08-01 11:46:12,531 - INFO - 已成功创建和注册 6 个客户端
2025-08-01 11:46:12,531 - INFO - 服务器属性检查:
2025-08-01 11:46:12,531 - INFO - - 客户端数量: 6
2025-08-01 11:46:12,532 - INFO - - 全局模型: 已初始化
2025-08-01 11:46:12,532 - INFO - - 算法: 已初始化
2025-08-01 11:46:12,532 - INFO - - 训练器: 已初始化
2025-08-01 11:46:12,532 - INFO - 准备启动服务器...
2025-08-01 11:46:12,532 - INFO - [Server #24960] 启动中...
2025-08-01 11:46:12,532 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:46:12,533 - INFO - 服务器将使用事件循环: <ProactorEventLoop running=False closed=False debug=False>
2025-08-01 11:46:12,533 - INFO - ✅ 服务器已有 6 个客户端，开始启动训练
2025-08-01 11:46:12,533 - INFO - [Client 1] 模型已放置到设备: cpu
2025-08-01 11:46:12,540 - INFO - [Client 1] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,547 - INFO - [Client 1] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,548 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:46:12,548 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:46:12,548 - INFO - [Client 1] 已更新algorithm的client_id
2025-08-01 11:46:12,548 - INFO - [Client 1] 模型初始化完成
2025-08-01 11:46:12,548 - INFO - 客户端 1 模型初始化成功
2025-08-01 11:46:12,550 - ERROR - 启动客户端 1 异步任务时出错: no running event loop
2025-08-01 11:46:12,552 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:46:12,554 - INFO - [Client 2] 模型已放置到设备: cpu
2025-08-01 11:46:12,559 - INFO - [Client 2] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,565 - INFO - [Client 2] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,566 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:46:12,566 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:46:12,566 - INFO - [Client 2] 已更新algorithm的client_id
2025-08-01 11:46:12,566 - INFO - [Client 2] 模型初始化完成
2025-08-01 11:46:12,566 - INFO - 客户端 2 模型初始化成功
2025-08-01 11:46:12,566 - ERROR - 启动客户端 2 异步任务时出错: no running event loop
2025-08-01 11:46:12,567 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:46:12,567 - INFO - [Client 3] 模型已放置到设备: cpu
2025-08-01 11:46:12,573 - INFO - [Client 3] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,580 - INFO - [Client 3] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,580 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:46:12,580 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:46:12,580 - INFO - [Client 3] 已更新algorithm的client_id
2025-08-01 11:46:12,580 - INFO - [Client 3] 模型初始化完成
2025-08-01 11:46:12,581 - INFO - 客户端 3 模型初始化成功
2025-08-01 11:46:12,581 - ERROR - 启动客户端 3 异步任务时出错: no running event loop
2025-08-01 11:46:12,581 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:46:12,582 - INFO - [Client 4] 模型已放置到设备: cpu
2025-08-01 11:46:12,587 - INFO - [Client 4] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,594 - INFO - [Client 4] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,594 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:46:12,594 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:46:12,594 - INFO - [Client 4] 已更新algorithm的client_id
2025-08-01 11:46:12,594 - INFO - [Client 4] 模型初始化完成
2025-08-01 11:46:12,594 - INFO - 客户端 4 模型初始化成功
2025-08-01 11:46:12,594 - ERROR - 启动客户端 4 异步任务时出错: no running event loop
2025-08-01 11:46:12,595 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:46:12,596 - INFO - [Client 5] 模型已放置到设备: cpu
2025-08-01 11:46:12,601 - INFO - [Client 5] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,608 - INFO - [Client 5] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,608 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:46:12,608 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:46:12,608 - INFO - [Client 5] 已更新algorithm的client_id
2025-08-01 11:46:12,608 - INFO - [Client 5] 模型初始化完成
2025-08-01 11:46:12,609 - INFO - 客户端 5 模型初始化成功
2025-08-01 11:46:12,609 - ERROR - 启动客户端 5 异步任务时出错: no running event loop
2025-08-01 11:46:12,609 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:46:12,610 - INFO - [Client 6] 模型已放置到设备: cpu
2025-08-01 11:46:12,615 - INFO - [Client 6] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,623 - INFO - [Client 6] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:46:12,623 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:46:12,623 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:46:12,623 - INFO - [Client 6] 已更新algorithm的client_id
2025-08-01 11:46:12,623 - INFO - [Client 6] 模型初始化完成
2025-08-01 11:46:12,623 - INFO - 客户端 6 模型初始化成功
2025-08-01 11:46:12,623 - ERROR - 启动客户端 6 异步任务时出错: no running event loop
2025-08-01 11:46:12,624 - ERROR - 启动客户端异常堆栈: Traceback (most recent call last):
  File "D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py", line 1289, in start
    task = asyncio.create_task(
        self._async_client_training(client_instance),
        name=f"client_{client_id}_training"
    )
  File "D:\Develop\Miniconda\Lib\asyncio\tasks.py", line 407, in create_task
    loop = events.get_running_loop()
RuntimeError: no running event loop

2025-08-01 11:46:12,624 - INFO - 服务器主循环任务已启动: <Task pending name='Task-1' coro=<Server.run() running at D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py:1313>>
2025-08-01 11:46:12,625 - INFO - Starting a server at address 127.0.0.1 and port 8000.
2025-08-01 11:46:12,626 - INFO - [Server #24960] 开始训练，共有 6 个客户端，每轮最多聚合 3 个客户端
2025-08-01 11:46:12,626 - INFO - 总训练轮次: 10
2025-08-01 11:46:12,626 - INFO - 🚀 开始第 1 轮训练（目标：10 轮）
2025-08-01 11:46:12,626 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:12,626 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:13,639 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:13,639 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:14,653 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:14,654 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:15,663 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:15,663 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:16,676 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:16,676 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:17,687 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:17,688 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:18,699 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:18,700 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:19,710 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:19,711 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:20,725 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:20,727 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:21,739 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:21,740 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:22,741 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:22,741 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:23,742 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:23,742 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:24,756 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:24,757 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:25,766 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:25,766 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:26,777 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:26,778 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:27,790 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:27,791 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:28,804 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:28,804 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:29,816 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:29,817 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:30,831 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:30,831 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:31,846 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:31,847 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:32,856 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:32,857 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:33,871 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:33,872 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:34,887 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:34,887 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:35,898 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:35,898 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:36,912 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:36,912 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:37,927 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:37,927 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:38,931 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:38,931 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:39,937 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:39,938 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:40,948 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:40,948 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:41,950 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:41,950 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:42,959 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:42,959 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:43,961 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:43,962 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:44,966 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:44,966 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:45,979 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:45,979 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:46,990 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:46,990 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:48,005 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:48,005 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:49,015 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:49,015 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:50,030 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:50,031 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:51,039 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:51,040 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:52,055 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:52,055 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:53,070 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:53,070 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:54,084 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:54,084 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:55,098 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:55,098 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:56,113 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:56,114 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:57,128 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:57,128 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:58,129 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:58,129 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:59,141 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:46:59,141 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:00,152 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:00,152 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:01,160 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:01,161 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:02,162 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:02,163 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:03,176 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:03,176 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:04,190 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:04,190 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:05,201 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:05,202 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:06,218 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:06,218 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:07,232 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:07,233 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:08,248 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:08,249 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:09,263 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:09,263 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:10,268 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:10,268 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:11,279 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:11,279 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:12,280 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:12,281 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:13,293 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:13,293 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:14,306 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:14,306 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:15,317 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:15,317 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:16,329 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:16,330 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:17,343 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:17,344 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:18,355 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:18,356 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:19,369 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:19,370 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:20,385 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:20,386 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:21,398 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:21,399 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:22,409 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:22,409 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:23,423 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:23,426 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:24,434 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:24,435 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:25,442 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:25,442 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:26,454 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:26,455 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:27,458 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:27,458 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:28,472 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:28,473 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:29,487 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:29,488 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:30,499 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:30,499 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:31,507 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:31,507 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:32,520 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:32,520 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:33,534 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:33,535 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:34,546 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:34,547 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:35,560 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:35,561 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:36,573 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:36,574 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:37,584 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:37,584 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:38,599 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:38,600 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:39,609 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:39,609 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:40,619 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:40,620 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:41,634 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:41,634 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:42,639 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:42,640 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:43,652 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:43,653 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:44,664 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:44,664 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:45,677 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:45,677 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:46,691 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:46,692 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:47,704 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:47,704 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:48,716 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:48,717 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:49,731 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:49,731 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:50,745 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:50,746 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:51,758 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:51,759 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:52,770 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:52,770 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:53,775 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:53,775 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:54,788 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:54,788 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:55,801 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:55,802 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:56,805 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:56,805 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:57,817 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:57,817 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:58,828 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:58,828 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:59,840 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:47:59,841 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:00,854 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:00,854 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:01,867 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:01,869 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:02,874 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:02,875 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:03,876 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:03,877 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:04,878 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:04,878 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:05,890 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:05,891 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:06,898 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:06,899 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:07,911 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:07,911 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:08,924 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:08,924 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:09,935 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:09,935 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:10,945 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:10,945 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:11,959 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:11,960 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:12,968 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:12,969 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:13,979 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:13,980 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:14,989 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:14,989 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:15,998 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:15,999 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:17,014 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:17,014 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:18,027 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:18,027 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:19,040 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:19,041 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:20,055 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:20,055 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:21,066 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:21,066 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:22,076 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:22,077 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:23,089 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:23,090 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:24,104 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:24,105 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:25,109 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:25,109 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:26,124 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:26,126 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:27,134 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:27,135 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:28,145 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:28,145 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:29,156 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:29,157 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:30,169 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:30,170 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:31,177 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:31,177 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:32,188 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:32,188 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:33,199 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:33,199 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:34,209 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:34,210 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:35,222 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:35,223 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:36,236 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:36,237 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:37,246 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:37,246 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:38,258 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:38,258 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:39,268 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:39,269 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:40,279 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:40,279 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:41,294 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:41,295 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:42,296 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:42,296 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:43,309 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:43,310 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:44,323 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:44,323 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:45,334 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:45,334 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:46,347 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:46,348 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:47,359 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:47,359 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:48,373 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:48,374 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:49,389 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:49,389 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:50,400 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:50,400 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:51,412 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:51,413 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:52,424 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:52,425 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:53,440 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:53,440 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:54,456 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:54,457 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:55,468 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:55,469 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:56,484 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:56,485 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:57,498 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:57,498 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:58,511 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:58,512 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:59,519 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:48:59,519 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:00,528 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:00,529 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:01,542 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:01,543 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:02,555 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:02,556 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:03,559 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:03,559 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:04,575 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:04,576 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:05,586 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:05,586 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:06,598 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:06,599 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:07,612 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:07,613 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:08,626 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:08,627 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:09,641 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:09,642 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:10,654 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:10,655 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:11,669 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:11,669 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:12,676 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:12,677 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:13,691 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:13,691 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:14,702 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:14,703 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:15,713 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:15,714 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:16,727 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:16,727 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:17,730 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:17,731 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:18,743 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:18,743 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:19,756 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:19,757 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:20,773 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:20,774 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:21,786 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:21,786 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:22,795 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:22,795 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:23,807 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:23,807 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:24,823 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:24,823 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:25,836 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:25,837 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:26,846 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:26,847 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:27,860 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:27,861 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:28,878 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:28,878 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:29,890 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:29,891 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:30,899 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:30,899 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:31,909 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:31,909 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:32,917 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:32,918 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:33,929 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:33,930 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:34,944 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:34,945 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:35,953 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:35,954 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:36,967 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:36,967 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:37,979 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:37,979 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:38,993 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:38,994 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:40,007 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:40,008 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:41,022 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:41,022 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:42,035 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:42,035 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:43,048 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:43,048 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:44,062 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:44,063 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:45,076 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:45,077 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:46,086 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:46,086 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:47,101 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:47,102 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:48,117 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:48,117 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:49,119 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:49,119 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:50,131 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:50,132 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:51,132 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:51,133 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:52,142 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:52,142 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:53,153 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:53,154 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:54,162 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:54,163 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:55,174 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:55,174 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:56,185 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:56,185 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:57,196 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:57,197 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:58,209 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:58,209 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:59,223 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:49:59,223 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:00,234 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:00,234 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:01,249 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:01,249 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:02,261 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:02,261 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:03,274 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:03,275 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:04,291 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:04,291 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:05,299 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:05,299 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:06,310 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:06,311 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:07,314 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:07,314 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:08,316 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:08,317 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:09,327 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:09,327 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:10,334 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:10,335 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:11,346 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:11,346 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:12,356 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:12,356 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:13,365 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:13,366 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:14,376 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:14,377 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:15,391 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:15,392 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:16,404 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:16,404 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:17,409 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:17,410 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:18,414 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:18,415 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:19,425 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:19,425 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:20,438 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:20,439 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:21,450 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:21,450 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:22,461 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:22,461 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:23,475 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:23,475 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:24,487 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:24,488 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:25,497 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:25,498 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:26,513 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:26,514 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:27,527 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:27,527 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:28,530 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:28,531 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:29,541 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:29,542 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:30,553 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:30,554 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:31,562 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:31,562 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:32,566 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:32,567 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:33,578 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:33,578 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:34,593 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:34,594 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:35,606 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:35,607 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:36,619 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:36,619 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:37,630 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:37,630 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:38,642 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:38,644 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:39,657 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:39,657 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:40,671 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:40,671 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:41,682 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:41,683 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:42,694 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:42,694 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:43,708 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:43,709 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:44,720 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:44,721 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:45,722 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:45,722 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:46,728 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:46,728 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:47,740 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:47,740 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:48,753 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:48,753 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:49,766 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:49,767 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:50,784 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:50,784 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:51,791 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:51,792 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:52,804 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:52,805 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:53,814 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:53,814 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:54,829 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:54,830 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:55,840 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:55,841 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:56,854 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:56,854 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:57,868 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:57,868 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:58,876 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:58,877 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:59,888 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:50:59,888 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:00,898 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:00,899 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:01,909 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:01,909 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:02,918 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:02,918 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:03,933 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:03,933 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:04,948 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:04,949 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:05,961 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:05,961 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:06,975 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:06,977 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:07,990 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:07,991 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:09,001 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:09,002 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:10,014 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:10,015 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:11,028 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:11,029 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:12,042 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:12,043 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:13,053 - WARNING - 第 0 轮等待超时，强制进行聚合检查
2025-08-01 11:51:13,054 - ERROR - 缓冲池为空，无法进行聚合，跳过第 0 轮
2025-08-01 11:51:13,054 - INFO - 🚀 开始第 1 轮训练（目标：10 轮）
2025-08-01 11:51:13,055 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:13,055 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:14,067 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:14,067 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:15,078 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:15,078 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:16,087 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:16,087 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:17,102 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:17,102 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:18,117 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:18,117 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:19,128 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:19,129 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:20,142 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:20,142 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:21,153 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:21,154 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:22,163 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:22,163 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:23,175 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:23,176 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:24,189 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:24,189 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:25,198 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:25,199 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:26,213 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:26,214 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:27,228 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:27,229 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:28,240 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:28,240 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:29,256 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:29,256 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:30,269 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:30,269 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:31,281 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:31,281 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:32,297 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:32,299 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:33,308 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:33,308 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:34,319 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:34,319 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:35,332 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:35,333 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:36,347 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:36,347 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:37,361 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:37,361 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:38,362 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:38,363 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:39,373 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:39,374 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:40,383 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:40,384 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:41,396 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:41,397 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:42,401 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:42,401 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:43,410 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:43,411 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:44,423 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:44,423 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:45,432 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:45,433 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:46,447 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:46,447 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:47,453 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:47,453 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:48,454 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:48,455 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:49,464 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:49,464 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:50,476 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:50,477 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:51,490 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:51,491 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:52,501 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:52,501 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:53,517 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:53,517 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:54,530 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:54,531 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:55,546 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:55,546 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:56,556 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:56,556 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:57,568 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:57,568 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:58,580 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:58,580 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:59,591 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:51:59,592 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:00,604 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:00,605 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:01,613 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:01,614 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:02,625 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:02,625 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:03,637 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:03,637 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:04,650 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:04,650 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:05,664 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:05,665 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:06,679 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:06,680 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:07,693 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:07,695 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:08,707 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:08,708 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:09,721 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:09,721 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:10,737 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:10,738 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:11,752 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:11,752 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:12,768 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:12,769 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:13,780 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:13,781 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:14,795 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:14,795 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:15,810 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:15,811 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:16,811 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:16,812 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:17,824 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:17,824 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:18,837 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:18,838 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:19,851 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:19,851 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:20,862 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:20,863 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:21,875 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:21,875 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:22,889 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:22,890 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:23,893 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:23,893 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:24,905 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:24,905 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:25,914 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:25,914 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:26,927 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:26,927 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:27,942 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:27,943 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:28,955 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:28,955 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:29,959 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:29,960 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:30,974 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:30,974 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:31,989 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:31,991 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:33,002 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:33,003 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:34,013 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:34,015 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:35,028 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:35,028 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:36,042 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:36,042 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:37,054 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:37,054 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:38,067 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:38,068 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:39,080 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:39,081 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:40,087 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:40,087 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:41,101 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:41,102 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:42,103 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:42,103 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:43,104 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:43,105 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:44,116 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:44,117 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:45,126 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:45,127 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:46,140 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:46,141 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:47,150 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:47,151 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:48,151 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:48,152 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:49,162 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:49,163 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:50,177 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:50,177 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:51,189 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:51,190 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:52,201 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:52,201 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:53,214 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:53,214 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:54,225 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:54,228 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:55,240 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:55,241 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:56,252 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:56,253 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:57,264 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:57,264 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:58,279 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:58,279 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:59,292 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:52:59,293 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:53:00,303 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:53:00,303 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:53:01,312 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:53:01,313 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:53:02,324 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 11:53:02,325 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
