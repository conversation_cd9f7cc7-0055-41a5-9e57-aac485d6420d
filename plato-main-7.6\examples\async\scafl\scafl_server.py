import logging
import os

from collections import OrderedDict

from plato.config import Config
from plato.servers import fedavg

import time
import numpy as np
from math import log2
from types import SimpleNamespace
import pandas as pd
from plato.utils import csv_processor

class Server(fedavg.Server):
    """
    自定义的SCAFL联邦学习服务器类，继承自FedAvg服务器。
    """

    def __init__(self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None):
        self.client_Hk_comp = {}
        self.client_Hk_comm = {}
        self.client_Hk = {}
        self.aggregation_start_time = time.time()

        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks
        )

        self.client_staleness = {}
        self.staleness_queue = {}
        self.tau_max = Config().tau_max if hasattr(Config(), 'tau_max') else 10
        self.V = Config().V if hasattr(Config(), 'V') else 10
        self.historical_Ds = []
        self.staleness_history = []
        self.accuracy_log = []
        self.first_reach_time = {0.7: None, 0.8: None, 0.9: None}
        self.start_time = time.time()
        self.run_id = "scafl_metrics"
        # 获取结果保存路径（使用配置中的路径或默认路径）
        result_path = getattr(Config(), 'result_path', 'E:\\Experiments\\orgin-plato-main\\plato-main\\examples\\async\\scafl\\results\\scafl_metrics')
        # 初始化CSV表头（包含avg_staleness）
        # 确保初始化时写入完整表头（包含avg_staleness）
        csv_processor.initialize_csv(f"{self.run_id}.csv", ['round', 'elapsed_time', 'accuracy', 'avg_staleness'], result_path)

    def get_cid(self, cid_obj):
        if isinstance(cid_obj, SimpleNamespace):
            for key in ['id', 'client_id', 'sid', 'name']:
                if hasattr(cid_obj, key):
                    return getattr(cid_obj, key)
            raise ValueError(f"[错误] 无法从 SimpleNamespace 获取合法 ID: {cid_obj}")
        return cid_obj

    def estimate_client_times(self, cid):
        cid = self.get_cid(cid)
        a = 0.5
        mu = 1.0
        param_count = 100000
        distance = np.random.uniform(0.2, 1.5)

        H_comp = a + np.random.exponential(1/mu)
        self.client_Hk_comp[cid] = H_comp

        B = 200e3
        P = 10 ** ((10 - 30) / 10)
        N0 = 10 ** ((-174 - 30) / 10)
        L = 10 ** (3.5 * np.log10(distance))
        SNR = P / (N0 * B * L)
        C = B * log2(1 + SNR)

        model_size = param_count * 32
        H_comm = model_size / C
        self.client_Hk_comm[cid] = H_comm

        H_k = H_comp + H_comm
        self.client_Hk[cid] = H_k

    def get_dkt(self, cid):
        cid = self.get_cid(cid)
        H_k = self.client_Hk.get(cid, 0)
        tau_k_t = self.client_staleness.get(cid, 0)
        t = len(self.historical_Ds)
        start_index = max(0, t - tau_k_t)
        end_index = t - 1
        sum_Dj = sum(self.historical_Ds[start_index:end_index + 1]) if self.historical_Ds else 0
        d_k_t = max(H_k - sum_Dj, 0)
        return d_k_t

    def clients_processed(self):
        super().clients_processed()

        start_time = getattr(self, "aggregation_start_time", time.time())
        D_j = time.time() - start_time
        self.historical_Ds.append(D_j)

        for cid in self.updates:
            cid = self.get_cid(cid)
            d_k_t = self.get_dkt(cid)
            if not hasattr(self, 'd_k_t_dict'):
                self.d_k_t_dict = {}
            self.d_k_t_dict[cid] = d_k_t

            prev_q = self.staleness_queue.get(cid, 0)
            tau_k = self.client_staleness.get(cid, 0)
            beta_k = 1
            new_q = max(prev_q + (tau_k + 1) * (1 - beta_k) - self.tau_max, 0)
            self.staleness_queue[cid] = new_q

        current_staleness = sum(self.d_k_t_dict.values()) / len(self.d_k_t_dict) if self.d_k_t_dict else 0
        self.staleness_history.append(current_staleness)

    def select_clients(self):
        clients = super().select_clients()
        for cid in clients:
            self.estimate_client_times(cid)
        return clients

    def select_clients_by_scafl(self, N_t, M_max):
        print(f"我我我，select_clients_by_scafl被调用了")
        min_S_M_t = float('inf')
        M_t_star = []

        N_t_sorted = sorted(N_t, key=lambda cid: self.client_completion_time.get(self.get_cid(cid), 0))

        M_t_star = []
        selected_ids = set()
        D_t = 0
        Q_k_sum = 0
        min_S_M_t = float('inf')

        for _ in range(M_max):
            best_delta_S = float('inf')
            best_client = None

            for cid in N_t_sorted:
                cid = self.get_cid(cid)
                if cid in selected_ids:
                    continue

                tau_k = self.client_staleness.get(cid, 0)
                alpha_k_t = self.client_weights.get(cid, 1.0) if hasattr(self, 'client_weights') else 1.0
                Q_k = 1
                Q_term = Q_k * ((tau_k + 1) * (1 - alpha_k_t) - self.tau_max)

                comm_time = self.client_Hk_comm.get(cid, 0)
                temp_D = max(D_t, comm_time)
                temp_S = self.V * temp_D + (Q_k_sum + Q_term)
                delta_S = temp_S - (self.V * D_t + Q_k_sum)

                if delta_S < best_delta_S:
                    best_delta_S = delta_S
                    best_client = cid

            if best_client is not None:
                M_t_star.append(best_client)
                selected_ids.add(best_client)
                D_t = max(D_t, self.client_Hk_comm.get(best_client, 0))
                tau_k = self.client_staleness.get(best_client, 0)
                alpha_k_t = self.client_weights.get(best_client, 1.0) if hasattr(self, 'client_weights') else 1.0
                Q_k = 1
                Q_k_sum += Q_k * ((tau_k + 1) * (1 - alpha_k_t) - self.tau_max)
                min_S_M_t = self.V * D_t + Q_k_sum

        return M_t_star

    async def wrap_up(self) -> None:
        self.save_to_checkpoint()

        if hasattr(self, 'accuracy'):
            self.accuracy_log.append(self.accuracy)
            for threshold in self.first_reach_time:
                if self.first_reach_time[threshold] is None and self.accuracy >= threshold:
                    self.first_reach_time[threshold] = time.time() - self.start_time

        current_round = self.current_round
        avg_staleness = self.staleness_history[-1] if self.staleness_history else 0.0
        logging.info(f"\n【第{current_round}轮结束】\n平均过时程度: {avg_staleness:.4f}\n当前准确率: {self.accuracy:.4f}")

        if self.accuracy_log:
            avg_last_10 = sum(self.accuracy_log[-10:]) / min(10, len(self.accuracy_log))
            best_acc = max(self.accuracy_log)
            logging.info(f"过去10轮平均准确率: {avg_last_10:.4f}, 最佳准确率: {best_acc:.4f}")

        # 确定CSV文件路径
        import time
        from datetime import datetime
        # 只在第一次运行时生成时间戳文件名，后续轮次使用同一个文件
        if not hasattr(self, 'csv_filename'):
            self.csv_filename = f"scafl_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}.csv"
            self.csv_path = os.path.join(Config().result_path if hasattr(Config(), 'result_path') else './results/scafl_metrics', self.csv_filename)
            os.makedirs(os.path.dirname(self.csv_path), exist_ok=True)
            logging.info(f"当前CSV文件路径：{self.csv_path}")  # 添加路径确认日志
        
        # 确保后续轮次也能访问csv_path
        csv_path = self.csv_path

        # 检查文件是否存在，不存在则初始化表头
        file_exists = os.path.exists(csv_path)
        logging.info(f"CSV文件是否存在：{file_exists}")  # 添加文件存在性检查日志
        if not file_exists:
            csv_processor.initialize_csv(
                csv_path,
                ['round', 'elapsed_time', 'accuracy', 'avg_staleness'],
                result_path=Config().result_path if hasattr(Config(), 'result_path') else './results/scafl_metrics'
            )
            logging.info("已初始化CSV表头")  # 添加表头初始化成功日志

        # 写入当前轮次数据
        csv_processor.write_csv(
            csv_path,
            [current_round, time.time() - self.start_time, self.accuracy, avg_staleness]
        )
        logging.info(f"已写入第{current_round}轮数据：[round={current_round}, elapsed_time={time.time() - self.start_time:.2f}, accuracy={self.accuracy:.4f}, avg_staleness={avg_staleness:.4f}]")  # 添加数据写入成功日志

        target_accuracy = getattr(Config().trainer, "target_accuracy", None)
        if target_accuracy and self.accuracy >= target_accuracy:
            logging.info(f"🎉 已达到目标准确率 {target_accuracy*100:.2f}%，总耗时 {time.time() - self.start_time:.2f}s，结束训练！")
            await self._close()
            return

        if self.current_round >= Config().trainer.rounds:
            logging.info(f"⏳ 已完成所有 {Config().trainer.rounds} 轮训练，结束训练！")
            await self._close()
            return

    async def aggregate_weights(self, updates, baseline_weights, weights_received):
        """
        根据客户端陈旧度τ_k(t)聚合模型权重，p_k^t ∝ 1/τ_k(t)

        Args:
            updates: 客户端上传的更新信息（包含report和payload）
            baseline_weights: 当前全局模型的基线权重
            weights_received: 从客户端接收的具体权重值
        """
        """
        基于客户端陈旧度τ_k(t)的权重聚合函数
        p_k^t ∝ 1 / τ_k(t)
        如果τ_k为0，则设置为一个较小的正数(0.1)避免除以零
        """
        print(f"我我我，aggregate_weights被调用了")
        # 计算每个客户端的权重
        weights = []
        for update in updates:
            cid = self.get_cid(update.client_id)
            tau_k = self.client_staleness.get(cid, 0)
            # 避免除以零
            if tau_k == 0:
                tau_k = 0.1
            weight = 1.0 / tau_k
            weights.append(weight)
        
        # 归一化权重
        total_weight = sum(weights)
        normalized_weights = [w / total_weight for w in weights]
        
        # 执行加权聚合
        averaged_weights = OrderedDict()
        for update, weight in zip(updates, normalized_weights):
            for name, param in update.payload.items():
                if name not in averaged_weights:
                    averaged_weights[name] = param * weight # 更新为param * weight
                else:
                    averaged_weights[name] += param * weight
        
        return averaged_weights
        
    def save_staleness_data(self):
        df = pd.DataFrame({
            'round': range(1, len(self.staleness_history)+1),
            'staleness': self.staleness_history,
            'accuracy': self.accuracy_log[:len(self.staleness_history)]
        })
        df.to_csv('training_metrics.csv', index=False)

    def training_complete(self):
        super().training_complete()
        self.save_staleness_data()

        if self.staleness_history:
            logging.info("已通过training_metrics.csv保存合并指标（过时程度+准确率）")
        else:
            df_acc = pd.DataFrame({
                'round': range(1, len(self.accuracy_log)+1),
                'accuracy': self.accuracy_log
            })
            df_acc.to_csv('accuracy_records.csv', index=False)

        logging.info("达到各测试精度阈值的时间:")
        for thres, ts in self.first_reach_time.items():
            logging.info(f"  {int(thres*100)}%: {'未达到' if ts is None else f'{ts:.2f}秒'}")
