[INFO][12:21:57]: 日志系统已初始化
[INFO][12:21:57]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_122157.log
[INFO][12:21:57]: 日志级别: INFO
[WARNING][12:21:57]: 无法获取系统信息: No module named 'psutil'
[INFO][12:21:57]: 🚀 ReFedScaFL 训练开始
[INFO][12:21:57]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][12:21:57]: 开始时间: 2025-07-29 12:21:57
[INFO][12:21:57]: [Client None] 基础初始化完成
[INFO][12:21:57]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][12:21:57]: 创建并缓存共享模型
[INFO][12:21:57]: [93m[1m[28000] Logging runtime results to: ./results/refedscafl/cifar10_alpha01/28000.csv.[0m
[INFO][12:21:57]: [Server #28000] Started training on 100 clients with 20 per round.
[INFO][12:21:57]: 服务器参数配置完成：
[INFO][12:21:57]: - 客户端数量: total=100, per_round=20
[INFO][12:21:57]: - 权重参数: success=0.8, distill=0.2
[INFO][12:21:57]: - SCAFL参数: V=1.0, tau_max=5
[INFO][12:21:57]: 从共享资源模型提取并缓存全局权重
[INFO][12:21:57]: [Server #28000] Configuring the server...
[INFO][12:21:57]: Training: 400 rounds or accuracy above 100.0%

[INFO][12:21:57]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][12:21:57]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][12:21:57]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][12:21:57]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][12:21:57]: Algorithm: fedavg
[INFO][12:21:57]: Data source: CIFAR10
[INFO][12:21:58]: Starting client #1's process.
[INFO][12:21:58]: Starting client #2's process.
[INFO][12:21:58]: Starting client #3's process.
[INFO][12:21:58]: Starting client #4's process.
[INFO][12:21:58]: Starting client #5's process.
[INFO][12:21:58]: Starting client #6's process.
[INFO][12:21:58]: Starting client #7's process.
[INFO][12:21:58]: Starting client #8's process.
[INFO][12:21:58]: Starting client #9's process.
[INFO][12:21:58]: Starting client #10's process.
[INFO][12:21:58]: Setting the random seed for selecting clients: 1
[INFO][12:21:58]: Starting a server at address 127.0.0.1 and port 8095.
[INFO][12:22:15]: [Server #28000] A new client just connected.
[INFO][12:22:15]: [Server #28000] New client with id #6 arrived.
[INFO][12:22:15]: [Server #28000] Client process #29972 registered.
[INFO][12:22:15]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][12:22:15]: [Server #28000] A new client just connected.
[INFO][12:22:15]: [Server #28000] New client with id #2 arrived.
[INFO][12:22:15]: [Server #28000] Client process #1352 registered.
[INFO][12:22:15]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][12:22:15]: [Server #28000] A new client just connected.
[INFO][12:22:15]: [Server #28000] New client with id #1 arrived.
[INFO][12:22:15]: [Server #28000] Client process #26544 registered.
[INFO][12:22:15]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][12:22:15]: [Server #28000] A new client just connected.
[INFO][12:22:15]: [Server #28000] A new client just connected.
[INFO][12:22:15]: [Server #28000] New client with id #7 arrived.
[INFO][12:22:15]: [Server #28000] Client process #36156 registered.
[INFO][12:22:15]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][12:22:15]: [Server #28000] New client with id #3 arrived.
[INFO][12:22:15]: [Server #28000] Client process #31076 registered.
[INFO][12:22:15]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][12:22:16]: [Server #28000] A new client just connected.
[INFO][12:22:16]: [Server #28000] New client with id #8 arrived.
[INFO][12:22:16]: [Server #28000] Client process #8428 registered.
[INFO][12:22:16]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][12:22:16]: [Server #28000] A new client just connected.
[INFO][12:22:16]: [Server #28000] A new client just connected.
[INFO][12:22:16]: [Server #28000] A new client just connected.
[INFO][12:22:16]: [Server #28000] New client with id #4 arrived.
[INFO][12:22:16]: [Server #28000] Client process #5772 registered.
[INFO][12:22:16]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][12:22:16]: [Server #28000] New client with id #9 arrived.
[INFO][12:22:16]: [Server #28000] Client process #40324 registered.
[INFO][12:22:16]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][12:22:16]: [Server #28000] A new client just connected.
[INFO][12:22:16]: [Server #28000] New client with id #10 arrived.
[INFO][12:22:16]: [Server #28000] Client process #19612 registered.
[INFO][12:22:16]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][12:22:16]: [Server #28000] New client with id #5 arrived.
[INFO][12:22:16]: [Server #28000] Client process #27764 registered.
[INFO][12:22:16]: [Server #28000] Starting training.
[INFO][12:22:16]: [93m[1m
[Server #28000] Starting round 1/400.[0m
[INFO][12:22:16]: [Server #28000] Selected clients: [18, 73, 98, 9, 33, 16, 64, 58, 61, 84, 49, 27, 13, 63, 4, 50, 56, 78, 99, 1]
[INFO][12:22:16]: [Server #28000] Selecting client #18 for training.
[INFO][12:22:16]: [Server #28000] Sending the current model to client #18 (simulated).
[INFO][12:22:16]: [Server #28000] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][12:22:16]: [Server #28000] Selecting client #73 for training.
[INFO][12:22:16]: [Server #28000] Sending the current model to client #73 (simulated).
[INFO][12:22:16]: [Server #28000] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][12:22:16]: [Server #28000] Selecting client #98 for training.
[INFO][12:22:16]: [Server #28000] Sending the current model to client #98 (simulated).
[INFO][12:22:16]: [Server #28000] Sending 18.75 MB of payload data to client #98 (simulated).
[INFO][12:22:16]: [Server #28000] Selecting client #9 for training.
[INFO][12:22:16]: [Server #28000] Sending the current model to client #9 (simulated).
[INFO][12:22:16]: [Server #28000] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][12:22:16]: [Server #28000] Selecting client #33 for training.
[INFO][12:22:16]: [Server #28000] Sending the current model to client #33 (simulated).
[INFO][12:22:16]: [Server #28000] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][12:22:16]: [Server #28000] Selecting client #16 for training.
[INFO][12:22:16]: [Server #28000] Sending the current model to client #16 (simulated).
[INFO][12:22:16]: [Server #28000] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][12:22:16]: [Server #28000] Selecting client #64 for training.
[INFO][12:22:16]: [Server #28000] Sending the current model to client #64 (simulated).
[INFO][12:22:16]: [Server #28000] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][12:22:16]: [Server #28000] Selecting client #58 for training.
[INFO][12:22:16]: [Server #28000] Sending the current model to client #58 (simulated).
[INFO][12:22:16]: [Server #28000] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][12:22:16]: [Server #28000] Selecting client #61 for training.
[INFO][12:22:16]: [Server #28000] Sending the current model to client #61 (simulated).
[INFO][12:22:16]: [Server #28000] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][12:22:16]: [Server #28000] Selecting client #84 for training.
[INFO][12:22:16]: [Server #28000] Sending the current model to client #84 (simulated).
[INFO][12:22:16]: [Server #28000] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][12:22:16]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][12:24:58]: [Server #28000] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][12:24:59]: [Server #28000] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][12:24:59]: [Server #28000] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][12:25:00]: [Server #28000] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][12:25:01]: [Server #28000] Received 18.75 MB of payload data from client #98 (simulated).
[INFO][12:25:02]: [Server #28000] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][12:25:02]: [Server #28000] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][12:25:02]: [Server #28000] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][12:25:02]: [Server #28000] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][12:25:02]: [Server #28000] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][12:25:02]: [Server #28000] Selecting client #49 for training.
[INFO][12:25:02]: [Server #28000] Sending the current model to client #49 (simulated).
[INFO][12:25:02]: [Server #28000] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][12:25:02]: [Server #28000] Selecting client #27 for training.
[INFO][12:25:02]: [Server #28000] Sending the current model to client #27 (simulated).
[INFO][12:25:02]: [Server #28000] Sending 18.75 MB of payload data to client #27 (simulated).
[INFO][12:25:02]: [Server #28000] Selecting client #13 for training.
[INFO][12:25:02]: [Server #28000] Sending the current model to client #13 (simulated).
[INFO][12:25:02]: [Server #28000] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][12:25:02]: [Server #28000] Selecting client #63 for training.
[INFO][12:25:02]: [Server #28000] Sending the current model to client #63 (simulated).
[INFO][12:25:02]: [Server #28000] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][12:25:02]: [Server #28000] Selecting client #4 for training.
[INFO][12:25:02]: [Server #28000] Sending the current model to client #4 (simulated).
[INFO][12:25:02]: [Server #28000] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][12:25:02]: [Server #28000] Selecting client #50 for training.
[INFO][12:25:02]: [Server #28000] Sending the current model to client #50 (simulated).
[INFO][12:25:02]: [Server #28000] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][12:25:02]: [Server #28000] Selecting client #56 for training.
[INFO][12:25:02]: [Server #28000] Sending the current model to client #56 (simulated).
[INFO][12:25:02]: [Server #28000] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][12:25:02]: [Server #28000] Selecting client #78 for training.
[INFO][12:25:02]: [Server #28000] Sending the current model to client #78 (simulated).
[INFO][12:25:03]: [Server #28000] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][12:25:03]: [Server #28000] Selecting client #99 for training.
[INFO][12:25:03]: [Server #28000] Sending the current model to client #99 (simulated).
[INFO][12:25:08]: [Server #28000] Sending 18.75 MB of payload data to client #99 (simulated).
[INFO][12:25:08]: [Server #28000] Selecting client #1 for training.
[INFO][12:25:08]: [Server #28000] Sending the current model to client #1 (simulated).
[INFO][12:25:09]: [Server #28000] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][12:27:41]: [Server #28000] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][12:27:42]: [Server #28000] Received 18.75 MB of payload data from client #27 (simulated).
[INFO][12:27:43]: [Server #28000] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][12:27:44]: [Server #28000] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][12:27:44]: [Server #28000] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][12:27:45]: [Server #28000] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][12:27:45]: [Server #28000] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][12:27:46]: [Server #28000] Received 18.75 MB of payload data from client #78 (simulated).
[INFO][12:27:47]: [Server #28000] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][12:27:47]: [Server #28000] Received 18.75 MB of payload data from client #99 (simulated).
[INFO][12:27:47]: [Server #28000] Adding client #49 to the list of clients for aggregation.
[INFO][12:27:47]: [Server #28000] Adding client #50 to the list of clients for aggregation.
[INFO][12:27:47]: [Server #28000] Adding client #27 to the list of clients for aggregation.
[INFO][12:27:47]: [Server #28000] Adding client #56 to the list of clients for aggregation.
[INFO][12:27:47]: [Server #28000] Adding client #4 to the list of clients for aggregation.
[INFO][12:27:47]: [Server #28000] Adding client #63 to the list of clients for aggregation.
[INFO][12:27:47]: [Server #28000] Adding client #13 to the list of clients for aggregation.
[INFO][12:27:47]: [Server #28000] Adding client #9 to the list of clients for aggregation.
[INFO][12:27:47]: [Server #28000] Adding client #18 to the list of clients for aggregation.
[INFO][12:27:47]: [Server #28000] Adding client #33 to the list of clients for aggregation.
[INFO][12:27:47]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:27:47]: [Server #28000] Updated weights have been received.
[INFO][12:27:47]: [Server #28000] Aggregating model weight deltas.
[INFO][12:27:47]: [Server #28000] Finished aggregating updated weights.
[INFO][12:27:47]: [Server #28000] Started model testing.
[INFO][12:27:59]: [Trainer.test] 测试完成 - 准确率: 16.70% (1670/10000)
[INFO][12:27:59]: [93m[1m[Server #28000] Global model accuracy: 16.70%
[0m
[INFO][12:27:59]: get_logged_items 被调用
[INFO][12:27:59]: 从updates获取参与客户端: [49, 50, 27, 56, 4, 63, 13, 9, 18, 33]
[INFO][12:27:59]: 客户端 49 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:27:59]: 客户端 50 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:27:59]: 客户端 27 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:27:59]: 客户端 56 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:27:59]: 客户端 4 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:27:59]: 客户端 63 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:27:59]: 客户端 13 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:27:59]: 客户端 9 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:27:59]: 客户端 18 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:27:59]: 客户端 33 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:27:59]: 陈旧度统计 - 参与客户端: [49, 50, 27, 56, 4, 63, 13, 9, 18, 33], 陈旧度: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
[INFO][12:27:59]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][12:27:59]: 最终logged_items: {'round': 1, 'accuracy': 0.167, 'accuracy_std': 0, 'elapsed_time': 51.48010230064392, 'processing_time': 0.014397499999972752, 'comm_time': 0, 'round_time': 51.480102401266464, 'comm_overhead': 749.9883651733398, 'global_accuracy': 0.167, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:27:59]: [Server #28000] All client reports have been processed.
[INFO][12:27:59]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][12:27:59]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][12:27:59]: [93m[1m
[Server #28000] Starting round 2/400.[0m
[INFO][12:27:59]: [Server #28000] Selected clients: [100, 62, 37, 32, 83, 15, 43, 5, 4, 92]
[INFO][12:27:59]: [Server #28000] Selecting client #100 for training.
[INFO][12:27:59]: [Server #28000] Sending the current model to client #100 (simulated).
[INFO][12:27:59]: [Server #28000] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][12:27:59]: [Server #28000] Selecting client #62 for training.
[INFO][12:27:59]: [Server #28000] Sending the current model to client #62 (simulated).
[INFO][12:27:59]: [Server #28000] Sending 18.75 MB of payload data to client #62 (simulated).
[INFO][12:27:59]: [Server #28000] Selecting client #37 for training.
[INFO][12:27:59]: [Server #28000] Sending the current model to client #37 (simulated).
[INFO][12:27:59]: [Server #28000] Sending 18.75 MB of payload data to client #37 (simulated).
[INFO][12:27:59]: [Server #28000] Selecting client #32 for training.
[INFO][12:27:59]: [Server #28000] Sending the current model to client #32 (simulated).
[INFO][12:27:59]: [Server #28000] Sending 18.75 MB of payload data to client #32 (simulated).
[INFO][12:27:59]: [Server #28000] Selecting client #83 for training.
[INFO][12:27:59]: [Server #28000] Sending the current model to client #83 (simulated).
[INFO][12:27:59]: [Server #28000] Sending 18.75 MB of payload data to client #83 (simulated).
[INFO][12:27:59]: [Server #28000] Selecting client #15 for training.
[INFO][12:27:59]: [Server #28000] Sending the current model to client #15 (simulated).
[INFO][12:27:59]: [Server #28000] Sending 18.75 MB of payload data to client #15 (simulated).
[INFO][12:27:59]: [Server #28000] Selecting client #43 for training.
[INFO][12:27:59]: [Server #28000] Sending the current model to client #43 (simulated).
[INFO][12:28:00]: [Server #28000] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][12:28:00]: [Server #28000] Selecting client #5 for training.
[INFO][12:28:00]: [Server #28000] Sending the current model to client #5 (simulated).
[INFO][12:28:00]: [Server #28000] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][12:28:00]: [Server #28000] Selecting client #4 for training.
[INFO][12:28:00]: [Server #28000] Sending the current model to client #4 (simulated).
[INFO][12:28:01]: [Server #28000] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][12:28:01]: [Server #28000] Selecting client #92 for training.
[INFO][12:28:01]: [Server #28000] Sending the current model to client #92 (simulated).
[INFO][12:28:01]: [Server #28000] Sending 18.75 MB of payload data to client #92 (simulated).
[INFO][12:30:38]: [Server #28000] Received 18.75 MB of payload data from client #32 (simulated).
[INFO][12:30:39]: [Server #28000] Received 18.75 MB of payload data from client #100 (simulated).
[INFO][12:30:39]: [Server #28000] Received 18.75 MB of payload data from client #62 (simulated).
[INFO][12:30:40]: [Server #28000] Received 18.75 MB of payload data from client #83 (simulated).
[INFO][12:30:40]: [Server #28000] Received 18.75 MB of payload data from client #37 (simulated).
[INFO][12:30:40]: [Server #28000] Received 18.75 MB of payload data from client #15 (simulated).
[INFO][12:30:40]: [Server #28000] Received 18.75 MB of payload data from client #43 (simulated).
[INFO][12:30:40]: [Server #28000] Received 18.75 MB of payload data from client #92 (simulated).
[INFO][12:30:40]: [Server #28000] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][12:30:41]: [Server #28000] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][12:30:41]: [Server #28000] Adding client #73 to the list of clients for aggregation.
[INFO][12:30:41]: [Server #28000] Adding client #98 to the list of clients for aggregation.
[INFO][12:30:41]: [Server #28000] Adding client #78 to the list of clients for aggregation.
[INFO][12:30:41]: [Server #28000] Adding client #1 to the list of clients for aggregation.
[INFO][12:30:41]: [Server #28000] Adding client #99 to the list of clients for aggregation.
[INFO][12:30:41]: [Server #28000] Adding client #16 to the list of clients for aggregation.
[INFO][12:30:41]: [Server #28000] Adding client #64 to the list of clients for aggregation.
[INFO][12:30:41]: [Server #28000] Adding client #84 to the list of clients for aggregation.
[INFO][12:30:41]: [Server #28000] Adding client #61 to the list of clients for aggregation.
[INFO][12:30:41]: [Server #28000] Adding client #58 to the list of clients for aggregation.
[INFO][12:30:41]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:30:41]: [Server #28000] Updated weights have been received.
[INFO][12:30:41]: [Server #28000] Aggregating model weight deltas.
[INFO][12:30:41]: [Server #28000] Finished aggregating updated weights.
[INFO][12:30:41]: [Server #28000] Started model testing.
[INFO][12:30:52]: [Trainer.test] 测试完成 - 准确率: 11.92% (1192/10000)
[INFO][12:30:52]: [93m[1m[Server #28000] Global model accuracy: 11.92%
[0m
[INFO][12:30:52]: get_logged_items 被调用
[INFO][12:30:52]: 从updates获取参与客户端: [73, 98, 78, 1, 99, 16, 64, 84, 61, 58]
[INFO][12:30:52]: 客户端 73 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:30:52]: 客户端 98 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:30:52]: 客户端 78 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:30:52]: 客户端 1 陈旧度: 2 (当前轮次:2, 上次参与:0)
[INFO][12:30:52]: 客户端 99 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:30:52]: 客户端 16 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:30:52]: 客户端 64 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:30:52]: 客户端 84 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:30:52]: 客户端 61 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:30:52]: 客户端 58 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:30:52]: 陈旧度统计 - 参与客户端: [73, 98, 78, 1, 99, 16, 64, 84, 61, 58], 陈旧度: [1, 1, 1, 2, 1, 1, 1, 1, 1, 1]
[INFO][12:30:52]: 平均陈旧度: 1.1, 最大: 2, 最小: 1
[INFO][12:30:52]: 最终logged_items: {'round': 2, 'accuracy': 0.1192, 'accuracy_std': 0, 'elapsed_time': 57.8702757358551, 'processing_time': 0.003718900000002634, 'comm_time': 0, 'round_time': 57.87027569617004, 'comm_overhead': 1124.9825477600098, 'global_accuracy': 0.1192, 'avg_staleness': 1.1, 'max_staleness': 2, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:30:52]: [Server #28000] All client reports have been processed.
[INFO][12:30:52]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][12:30:52]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][12:30:52]: [93m[1m
[Server #28000] Starting round 3/400.[0m
[INFO][12:30:52]: [Server #28000] Selected clients: [77, 2, 55, 97, 31, 61, 6, 75, 33, 64]
[INFO][12:30:52]: [Server #28000] Selecting client #77 for training.
[INFO][12:30:52]: [Server #28000] Sending the current model to client #77 (simulated).
[INFO][12:30:52]: [Server #28000] Sending 18.75 MB of payload data to client #77 (simulated).
[INFO][12:30:52]: [Server #28000] Selecting client #2 for training.
[INFO][12:30:52]: [Server #28000] Sending the current model to client #2 (simulated).
[INFO][12:30:52]: [Server #28000] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][12:30:52]: [Server #28000] Selecting client #55 for training.
[INFO][12:30:52]: [Server #28000] Sending the current model to client #55 (simulated).
[INFO][12:30:52]: [Server #28000] Sending 18.75 MB of payload data to client #55 (simulated).
[INFO][12:30:52]: [Server #28000] Selecting client #97 for training.
[INFO][12:30:52]: [Server #28000] Sending the current model to client #97 (simulated).
[INFO][12:30:52]: [Server #28000] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][12:30:52]: [Server #28000] Selecting client #31 for training.
[INFO][12:30:52]: [Server #28000] Sending the current model to client #31 (simulated).
[INFO][12:30:52]: [Server #28000] Sending 18.75 MB of payload data to client #31 (simulated).
[INFO][12:30:52]: [Server #28000] Selecting client #61 for training.
[INFO][12:30:52]: [Server #28000] Sending the current model to client #61 (simulated).
[INFO][12:30:53]: [Server #28000] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][12:30:53]: [Server #28000] Selecting client #6 for training.
[INFO][12:30:53]: [Server #28000] Sending the current model to client #6 (simulated).
[INFO][12:30:53]: [Server #28000] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][12:30:53]: [Server #28000] Selecting client #75 for training.
[INFO][12:30:53]: [Server #28000] Sending the current model to client #75 (simulated).
[INFO][12:30:53]: [Server #28000] Sending 18.75 MB of payload data to client #75 (simulated).
[INFO][12:30:53]: [Server #28000] Selecting client #33 for training.
[INFO][12:30:53]: [Server #28000] Sending the current model to client #33 (simulated).
[INFO][12:30:54]: [Server #28000] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][12:30:54]: [Server #28000] Selecting client #64 for training.
[INFO][12:30:54]: [Server #28000] Sending the current model to client #64 (simulated).
[INFO][12:30:54]: [Server #28000] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][12:33:30]: [Server #28000] Received 18.75 MB of payload data from client #77 (simulated).
[INFO][12:33:31]: [Server #28000] Received 18.75 MB of payload data from client #97 (simulated).
[INFO][12:33:32]: [Server #28000] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][12:33:32]: [Server #28000] Received 18.75 MB of payload data from client #31 (simulated).
[INFO][12:33:33]: [Server #28000] Received 18.75 MB of payload data from client #55 (simulated).
[INFO][12:33:33]: [Server #28000] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][12:33:33]: [Server #28000] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][12:33:33]: [Server #28000] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][12:33:33]: [Server #28000] Received 18.75 MB of payload data from client #75 (simulated).
[INFO][12:33:33]: [Server #28000] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][12:33:33]: [Server #28000] Adding client #62 to the list of clients for aggregation.
[INFO][12:33:33]: [Server #28000] Adding client #32 to the list of clients for aggregation.
[INFO][12:33:33]: [Server #28000] Adding client #100 to the list of clients for aggregation.
[INFO][12:33:33]: [Server #28000] Adding client #15 to the list of clients for aggregation.
[INFO][12:33:33]: [Server #28000] Adding client #37 to the list of clients for aggregation.
[INFO][12:33:33]: [Server #28000] Adding client #83 to the list of clients for aggregation.
[INFO][12:33:33]: [Server #28000] Adding client #43 to the list of clients for aggregation.
[INFO][12:33:33]: [Server #28000] Adding client #92 to the list of clients for aggregation.
[INFO][12:33:33]: [Server #28000] Adding client #5 to the list of clients for aggregation.
[INFO][12:33:33]: [Server #28000] Adding client #77 to the list of clients for aggregation.
[INFO][12:33:33]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:33:33]: [Server #28000] Updated weights have been received.
[INFO][12:33:33]: [Server #28000] Aggregating model weight deltas.
[INFO][12:33:33]: [Server #28000] Finished aggregating updated weights.
[INFO][12:33:33]: [Server #28000] Started model testing.
[INFO][12:33:46]: [Trainer.test] 测试完成 - 准确率: 13.83% (1383/10000)
[INFO][12:33:46]: [93m[1m[Server #28000] Global model accuracy: 13.83%
[0m
[INFO][12:33:46]: get_logged_items 被调用
[INFO][12:33:46]: 从updates获取参与客户端: [62, 32, 100, 15, 37, 83, 43, 92, 5, 77]
[INFO][12:33:46]: 客户端 62 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:33:46]: 客户端 32 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:33:46]: 客户端 100 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:33:46]: 客户端 15 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:33:46]: 客户端 37 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:33:46]: 客户端 83 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:33:46]: 客户端 43 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:33:46]: 客户端 92 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:33:46]: 客户端 5 陈旧度: 3 (当前轮次:3, 上次参与:0)
[INFO][12:33:46]: 客户端 77 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:33:46]: 陈旧度统计 - 参与客户端: [62, 32, 100, 15, 37, 83, 43, 92, 5, 77], 陈旧度: [1, 1, 1, 1, 1, 1, 1, 1, 3, 1]
[INFO][12:33:46]: 平均陈旧度: 1.2, 最大: 3, 最小: 1
[INFO][12:33:46]: 最终logged_items: {'round': 3, 'accuracy': 0.1383, 'accuracy_std': 0, 'elapsed_time': 106.63325476646423, 'processing_time': 0.007064100000036433, 'comm_time': 0, 'round_time': 54.389009610537755, 'comm_overhead': 1499.9767303466797, 'global_accuracy': 0.1383, 'avg_staleness': 1.2, 'max_staleness': 3, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:33:46]: [Server #28000] All client reports have been processed.
[INFO][12:33:46]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][12:33:46]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][12:33:46]: [93m[1m
[Server #28000] Starting round 4/400.[0m
[INFO][12:33:46]: [Server #28000] Selected clients: [72, 80, 35, 50, 96, 34, 67, 43, 5, 60]
[INFO][12:33:46]: [Server #28000] Selecting client #72 for training.
[INFO][12:33:46]: [Server #28000] Sending the current model to client #72 (simulated).
[INFO][12:33:46]: [Server #28000] Sending 18.75 MB of payload data to client #72 (simulated).
[INFO][12:33:46]: [Server #28000] Selecting client #80 for training.
[INFO][12:33:46]: [Server #28000] Sending the current model to client #80 (simulated).
[INFO][12:33:46]: [Server #28000] Sending 18.75 MB of payload data to client #80 (simulated).
[INFO][12:33:46]: [Server #28000] Selecting client #35 for training.
[INFO][12:33:46]: [Server #28000] Sending the current model to client #35 (simulated).
[INFO][12:33:46]: [Server #28000] Sending 18.75 MB of payload data to client #35 (simulated).
[INFO][12:33:46]: [Server #28000] Selecting client #50 for training.
[INFO][12:33:46]: [Server #28000] Sending the current model to client #50 (simulated).
[INFO][12:33:46]: [Server #28000] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][12:33:46]: [Server #28000] Selecting client #96 for training.
[INFO][12:33:46]: [Server #28000] Sending the current model to client #96 (simulated).
[INFO][12:33:46]: [Server #28000] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][12:33:46]: [Server #28000] Selecting client #34 for training.
[INFO][12:33:46]: [Server #28000] Sending the current model to client #34 (simulated).
[INFO][12:33:46]: [Server #28000] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][12:33:46]: [Server #28000] Selecting client #67 for training.
[INFO][12:33:46]: [Server #28000] Sending the current model to client #67 (simulated).
[INFO][12:33:47]: [Server #28000] Sending 18.75 MB of payload data to client #67 (simulated).
[INFO][12:33:47]: [Server #28000] Selecting client #43 for training.
[INFO][12:33:47]: [Server #28000] Sending the current model to client #43 (simulated).
[INFO][12:33:47]: [Server #28000] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][12:33:47]: [Server #28000] Selecting client #5 for training.
[INFO][12:33:47]: [Server #28000] Sending the current model to client #5 (simulated).
[INFO][12:33:48]: [Server #28000] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][12:33:48]: [Server #28000] Selecting client #60 for training.
[INFO][12:33:48]: [Server #28000] Sending the current model to client #60 (simulated).
[INFO][12:33:48]: [Server #28000] Sending 18.75 MB of payload data to client #60 (simulated).
[INFO][12:36:29]: [Server #28000] Received 18.75 MB of payload data from client #35 (simulated).
[INFO][12:36:30]: [Server #28000] Received 18.75 MB of payload data from client #80 (simulated).
[INFO][12:36:32]: [Server #28000] Received 18.75 MB of payload data from client #72 (simulated).
[INFO][12:36:32]: [Server #28000] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][12:36:33]: [Server #28000] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][12:36:34]: [Server #28000] Received 18.75 MB of payload data from client #96 (simulated).
[INFO][12:36:34]: [Server #28000] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][12:36:35]: [Server #28000] Received 18.75 MB of payload data from client #67 (simulated).
[INFO][12:36:36]: [Server #28000] Received 18.75 MB of payload data from client #60 (simulated).
[INFO][12:36:36]: [Server #28000] Received 18.75 MB of payload data from client #43 (simulated).
[INFO][12:36:36]: [Server #28000] Adding client #4 to the list of clients for aggregation.
[INFO][12:36:36]: [Server #28000] Adding client #2 to the list of clients for aggregation.
[INFO][12:36:36]: [Server #28000] Adding client #55 to the list of clients for aggregation.
[INFO][12:36:36]: [Server #28000] Adding client #97 to the list of clients for aggregation.
[INFO][12:36:36]: [Server #28000] Adding client #31 to the list of clients for aggregation.
[INFO][12:36:36]: [Server #28000] Adding client #61 to the list of clients for aggregation.
[INFO][12:36:36]: [Server #28000] Adding client #33 to the list of clients for aggregation.
[INFO][12:36:36]: [Server #28000] Adding client #64 to the list of clients for aggregation.
[INFO][12:36:36]: [Server #28000] Adding client #6 to the list of clients for aggregation.
[INFO][12:36:36]: [Server #28000] Adding client #75 to the list of clients for aggregation.
[INFO][12:36:36]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:36:36]: [Server #28000] Updated weights have been received.
[INFO][12:36:36]: [Server #28000] Aggregating model weight deltas.
[INFO][12:36:36]: [Server #28000] Finished aggregating updated weights.
[INFO][12:36:36]: [Server #28000] Started model testing.
[INFO][12:36:48]: [Trainer.test] 测试完成 - 准确率: 21.34% (2134/10000)
[INFO][12:36:48]: [93m[1m[Server #28000] Global model accuracy: 21.34%
[0m
[INFO][12:36:48]: get_logged_items 被调用
[INFO][12:36:48]: 从updates获取参与客户端: [4, 2, 55, 97, 31, 61, 33, 64, 6, 75]
[INFO][12:36:48]: 客户端 4 陈旧度: 3 (当前轮次:4, 上次参与:1)
[INFO][12:36:48]: 客户端 2 陈旧度: 4 (当前轮次:4, 上次参与:0)
[INFO][12:36:48]: 客户端 55 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][12:36:48]: 客户端 97 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][12:36:48]: 客户端 31 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][12:36:48]: 客户端 61 陈旧度: 2 (当前轮次:4, 上次参与:2)
[INFO][12:36:48]: 客户端 33 陈旧度: 3 (当前轮次:4, 上次参与:1)
[INFO][12:36:48]: 客户端 64 陈旧度: 2 (当前轮次:4, 上次参与:2)
[INFO][12:36:48]: 客户端 6 陈旧度: 4 (当前轮次:4, 上次参与:0)
[INFO][12:36:48]: 客户端 75 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][12:36:48]: 陈旧度统计 - 参与客户端: [4, 2, 55, 97, 31, 61, 33, 64, 6, 75], 陈旧度: [3, 4, 1, 1, 1, 2, 3, 2, 4, 1]
[INFO][12:36:48]: 平均陈旧度: 2.2, 最大: 4, 最小: 1
[INFO][12:36:48]: 最终logged_items: {'round': 4, 'accuracy': 0.2134, 'accuracy_std': 0, 'elapsed_time': 111.68809604644775, 'processing_time': 0.006715999999983069, 'comm_time': 0, 'round_time': 55.851192369924945, 'comm_overhead': 1874.9709129333496, 'global_accuracy': 0.2134, 'avg_staleness': 2.2, 'max_staleness': 4, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:36:48]: [Server #28000] All client reports have been processed.
[INFO][12:36:48]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][12:36:48]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][12:36:48]: [93m[1m
[Server #28000] Starting round 5/400.[0m
[INFO][12:36:48]: [Server #28000] Selected clients: [81, 92, 14, 25, 90, 41, 17, 47, 73, 61]
[INFO][12:36:48]: [Server #28000] Selecting client #81 for training.
[INFO][12:36:48]: [Server #28000] Sending the current model to client #81 (simulated).
[INFO][12:36:48]: [Server #28000] Sending 18.75 MB of payload data to client #81 (simulated).
[INFO][12:36:48]: [Server #28000] Selecting client #92 for training.
[INFO][12:36:48]: [Server #28000] Sending the current model to client #92 (simulated).
[INFO][12:36:48]: [Server #28000] Sending 18.75 MB of payload data to client #92 (simulated).
[INFO][12:36:48]: [Server #28000] Selecting client #14 for training.
[INFO][12:36:48]: [Server #28000] Sending the current model to client #14 (simulated).
[INFO][12:36:48]: [Server #28000] Sending 18.75 MB of payload data to client #14 (simulated).
[INFO][12:36:48]: [Server #28000] Selecting client #25 for training.
[INFO][12:36:48]: [Server #28000] Sending the current model to client #25 (simulated).
[INFO][12:36:48]: [Server #28000] Sending 18.75 MB of payload data to client #25 (simulated).
[INFO][12:36:48]: [Server #28000] Selecting client #90 for training.
[INFO][12:36:48]: [Server #28000] Sending the current model to client #90 (simulated).
[INFO][12:36:48]: [Server #28000] Sending 18.75 MB of payload data to client #90 (simulated).
[INFO][12:36:48]: [Server #28000] Selecting client #41 for training.
[INFO][12:36:48]: [Server #28000] Sending the current model to client #41 (simulated).
[INFO][12:36:49]: [Server #28000] Sending 18.75 MB of payload data to client #41 (simulated).
[INFO][12:36:49]: [Server #28000] Selecting client #17 for training.
[INFO][12:36:49]: [Server #28000] Sending the current model to client #17 (simulated).
[INFO][12:36:49]: [Server #28000] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][12:36:49]: [Server #28000] Selecting client #47 for training.
[INFO][12:36:49]: [Server #28000] Sending the current model to client #47 (simulated).
[INFO][12:36:50]: [Server #28000] Sending 18.75 MB of payload data to client #47 (simulated).
[INFO][12:36:50]: [Server #28000] Selecting client #73 for training.
[INFO][12:36:50]: [Server #28000] Sending the current model to client #73 (simulated).
[INFO][12:36:50]: [Server #28000] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][12:36:50]: [Server #28000] Selecting client #61 for training.
[INFO][12:36:50]: [Server #28000] Sending the current model to client #61 (simulated).
[INFO][12:36:51]: [Server #28000] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][12:39:35]: [Server #28000] Received 18.75 MB of payload data from client #81 (simulated).
[INFO][12:39:39]: [Server #28000] Received 18.75 MB of payload data from client #14 (simulated).
[INFO][12:39:40]: [Server #28000] Received 18.75 MB of payload data from client #25 (simulated).
[INFO][12:39:41]: [Server #28000] Received 18.75 MB of payload data from client #92 (simulated).
[INFO][12:39:41]: [Server #28000] Received 18.75 MB of payload data from client #90 (simulated).
[INFO][12:39:42]: [Server #28000] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][12:39:42]: [Server #28000] Received 18.75 MB of payload data from client #41 (simulated).
[INFO][12:39:42]: [Server #28000] Received 18.75 MB of payload data from client #47 (simulated).
[INFO][12:39:42]: [Server #28000] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][12:39:42]: [Server #28000] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][12:39:42]: [Server #28000] Adding client #80 to the list of clients for aggregation.
[INFO][12:39:42]: [Server #28000] Adding client #35 to the list of clients for aggregation.
[INFO][12:39:42]: [Server #28000] Adding client #34 to the list of clients for aggregation.
[INFO][12:39:42]: [Server #28000] Adding client #72 to the list of clients for aggregation.
[INFO][12:39:42]: [Server #28000] Adding client #5 to the list of clients for aggregation.
[INFO][12:39:42]: [Server #28000] Adding client #50 to the list of clients for aggregation.
[INFO][12:39:42]: [Server #28000] Adding client #96 to the list of clients for aggregation.
[INFO][12:39:42]: [Server #28000] Adding client #60 to the list of clients for aggregation.
[INFO][12:39:42]: [Server #28000] Adding client #67 to the list of clients for aggregation.
[INFO][12:39:42]: [Server #28000] Adding client #43 to the list of clients for aggregation.
[INFO][12:39:42]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:39:42]: [Server #28000] Updated weights have been received.
[INFO][12:39:42]: [Server #28000] Aggregating model weight deltas.
[INFO][12:39:42]: [Server #28000] Finished aggregating updated weights.
[INFO][12:39:42]: [Server #28000] Started model testing.
[INFO][12:39:54]: [Trainer.test] 测试完成 - 准确率: 11.08% (1108/10000)
[INFO][12:39:54]: [93m[1m[Server #28000] Global model accuracy: 11.08%
[0m
[INFO][12:39:54]: get_logged_items 被调用
[INFO][12:39:54]: 从updates获取参与客户端: [80, 35, 34, 72, 5, 50, 96, 60, 67, 43]
[INFO][12:39:54]: 客户端 80 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:39:54]: 客户端 35 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:39:54]: 客户端 34 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:39:54]: 客户端 72 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:39:54]: 客户端 5 陈旧度: 2 (当前轮次:5, 上次参与:3)
[INFO][12:39:54]: 客户端 50 陈旧度: 4 (当前轮次:5, 上次参与:1)
[INFO][12:39:54]: 客户端 96 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:39:54]: 客户端 60 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:39:54]: 客户端 67 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:39:54]: 客户端 43 陈旧度: 2 (当前轮次:5, 上次参与:3)
[INFO][12:39:54]: 陈旧度统计 - 参与客户端: [80, 35, 34, 72, 5, 50, 96, 60, 67, 43], 陈旧度: [1, 1, 1, 1, 2, 4, 1, 1, 1, 2]
[INFO][12:39:54]: 平均陈旧度: 1.5, 最大: 4, 最小: 1
[INFO][12:39:54]: 最终logged_items: {'round': 5, 'accuracy': 0.1108, 'accuracy_std': 0, 'elapsed_time': 162.28427004814148, 'processing_time': 0.008087600000067141, 'comm_time': 0, 'round_time': 55.65101526783144, 'comm_overhead': 2249.9650955200195, 'global_accuracy': 0.1108, 'avg_staleness': 1.5, 'max_staleness': 4, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:39:54]: [Server #28000] All client reports have been processed.
[INFO][12:39:54]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][12:39:54]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][12:39:54]: [93m[1m
[Server #28000] Starting round 6/400.[0m
[INFO][12:39:54]: [Server #28000] Selected clients: [71, 96, 28, 43, 40, 84, 70, 56, 5, 68]
[INFO][12:39:54]: [Server #28000] Selecting client #71 for training.
[INFO][12:39:54]: [Server #28000] Sending the current model to client #71 (simulated).
[INFO][12:39:54]: [Server #28000] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][12:39:54]: [Server #28000] Selecting client #96 for training.
[INFO][12:39:54]: [Server #28000] Sending the current model to client #96 (simulated).
[INFO][12:39:54]: [Server #28000] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][12:39:54]: [Server #28000] Selecting client #28 for training.
[INFO][12:39:54]: [Server #28000] Sending the current model to client #28 (simulated).
[INFO][12:39:54]: [Server #28000] Sending 18.75 MB of payload data to client #28 (simulated).
[INFO][12:39:54]: [Server #28000] Selecting client #43 for training.
[INFO][12:39:54]: [Server #28000] Sending the current model to client #43 (simulated).
[INFO][12:39:54]: [Server #28000] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][12:39:54]: [Server #28000] Selecting client #40 for training.
[INFO][12:39:54]: [Server #28000] Sending the current model to client #40 (simulated).
[INFO][12:39:55]: [Server #28000] Sending 18.75 MB of payload data to client #40 (simulated).
[INFO][12:39:55]: [Server #28000] Selecting client #84 for training.
[INFO][12:39:55]: [Server #28000] Sending the current model to client #84 (simulated).
[INFO][12:39:55]: [Server #28000] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][12:39:55]: [Server #28000] Selecting client #70 for training.
[INFO][12:39:55]: [Server #28000] Sending the current model to client #70 (simulated).
[INFO][12:39:55]: [Server #28000] Sending 18.75 MB of payload data to client #70 (simulated).
[INFO][12:39:55]: [Server #28000] Selecting client #56 for training.
[INFO][12:39:55]: [Server #28000] Sending the current model to client #56 (simulated).
[INFO][12:39:56]: [Server #28000] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][12:39:56]: [Server #28000] Selecting client #5 for training.
[INFO][12:39:56]: [Server #28000] Sending the current model to client #5 (simulated).
[INFO][12:39:56]: [Server #28000] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][12:39:56]: [Server #28000] Selecting client #68 for training.
[INFO][12:39:56]: [Server #28000] Sending the current model to client #68 (simulated).
[INFO][12:39:57]: [Server #28000] Sending 18.75 MB of payload data to client #68 (simulated).
[INFO][12:42:58]: [Server #28000] Received 18.75 MB of payload data from client #28 (simulated).
[INFO][12:42:58]: [Server #28000] Received 18.75 MB of payload data from client #96 (simulated).
[INFO][12:42:59]: [Server #28000] Received 18.75 MB of payload data from client #40 (simulated).
[INFO][12:42:59]: [Server #28000] Received 18.75 MB of payload data from client #43 (simulated).
[INFO][12:42:59]: [Server #28000] Received 18.75 MB of payload data from client #70 (simulated).
[INFO][12:42:59]: [Server #28000] Received 18.75 MB of payload data from client #71 (simulated).
[INFO][12:42:59]: [Server #28000] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][12:42:59]: [Server #28000] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][12:42:59]: [Server #28000] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][12:42:59]: [Server #28000] Received 18.75 MB of payload data from client #68 (simulated).
[INFO][12:42:59]: [Server #28000] Adding client #81 to the list of clients for aggregation.
[INFO][12:42:59]: [Server #28000] Adding client #25 to the list of clients for aggregation.
[INFO][12:42:59]: [Server #28000] Adding client #14 to the list of clients for aggregation.
[INFO][12:42:59]: [Server #28000] Adding client #92 to the list of clients for aggregation.
[INFO][12:42:59]: [Server #28000] Adding client #73 to the list of clients for aggregation.
[INFO][12:42:59]: [Server #28000] Adding client #47 to the list of clients for aggregation.
[INFO][12:42:59]: [Server #28000] Adding client #90 to the list of clients for aggregation.
[INFO][12:42:59]: [Server #28000] Adding client #17 to the list of clients for aggregation.
[INFO][12:42:59]: [Server #28000] Adding client #41 to the list of clients for aggregation.
[INFO][12:42:59]: [Server #28000] Adding client #61 to the list of clients for aggregation.
[INFO][12:42:59]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:42:59]: [Server #28000] Updated weights have been received.
[INFO][12:43:00]: [Server #28000] Aggregating model weight deltas.
[INFO][12:43:00]: [Server #28000] Finished aggregating updated weights.
[INFO][12:43:00]: [Server #28000] Started model testing.
[INFO][12:43:12]: [Trainer.test] 测试完成 - 准确率: 22.77% (2277/10000)
[INFO][12:43:12]: [93m[1m[Server #28000] Global model accuracy: 22.77%
[0m
[INFO][12:43:12]: get_logged_items 被调用
[INFO][12:43:12]: 从updates获取参与客户端: [81, 25, 14, 92, 73, 47, 90, 17, 41, 61]
[INFO][12:43:12]: 客户端 81 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][12:43:12]: 客户端 25 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][12:43:12]: 客户端 14 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][12:43:12]: 客户端 92 陈旧度: 3 (当前轮次:6, 上次参与:3)
[INFO][12:43:12]: 客户端 73 陈旧度: 4 (当前轮次:6, 上次参与:2)
[INFO][12:43:12]: 客户端 47 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][12:43:12]: 客户端 90 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][12:43:12]: 客户端 17 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][12:43:12]: 客户端 41 陈旧度: 1 (当前轮次:6, 上次参与:5)
[INFO][12:43:12]: 客户端 61 陈旧度: 2 (当前轮次:6, 上次参与:4)
[INFO][12:43:12]: 陈旧度统计 - 参与客户端: [81, 25, 14, 92, 73, 47, 90, 17, 41, 61], 陈旧度: [1, 1, 1, 3, 4, 1, 1, 1, 1, 2]
[INFO][12:43:12]: 平均陈旧度: 1.6, 最大: 4, 最小: 1
[INFO][12:43:12]: 最终logged_items: {'round': 6, 'accuracy': 0.2277, 'accuracy_std': 0, 'elapsed_time': 167.80406856536865, 'processing_time': 0.005308200000058605, 'comm_time': 0, 'round_time': 56.11597250880732, 'comm_overhead': 2624.9592781066895, 'global_accuracy': 0.2277, 'avg_staleness': 1.6, 'max_staleness': 4, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:43:12]: [Server #28000] All client reports have been processed.
[INFO][12:43:12]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_6.pth.
[INFO][12:43:12]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_6.pth.
[INFO][12:43:12]: [93m[1m
[Server #28000] Starting round 7/400.[0m
[INFO][12:43:12]: [Server #28000] Selected clients: [34, 57, 59, 95, 24, 51, 79, 100, 97, 52]
[INFO][12:43:12]: [Server #28000] Selecting client #34 for training.
[INFO][12:43:12]: [Server #28000] Sending the current model to client #34 (simulated).
[INFO][12:43:12]: [Server #28000] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][12:43:12]: [Server #28000] Selecting client #57 for training.
[INFO][12:43:12]: [Server #28000] Sending the current model to client #57 (simulated).
[INFO][12:43:12]: [Server #28000] Sending 18.75 MB of payload data to client #57 (simulated).
[INFO][12:43:12]: [Server #28000] Selecting client #59 for training.
[INFO][12:43:12]: [Server #28000] Sending the current model to client #59 (simulated).
[INFO][12:43:12]: [Server #28000] Sending 18.75 MB of payload data to client #59 (simulated).
[INFO][12:43:12]: [Server #28000] Selecting client #95 for training.
[INFO][12:43:12]: [Server #28000] Sending the current model to client #95 (simulated).
[INFO][12:43:12]: [Server #28000] Sending 18.75 MB of payload data to client #95 (simulated).
[INFO][12:43:12]: [Server #28000] Selecting client #24 for training.
[INFO][12:43:12]: [Server #28000] Sending the current model to client #24 (simulated).
[INFO][12:43:12]: [Server #28000] Sending 18.75 MB of payload data to client #24 (simulated).
[INFO][12:43:12]: [Server #28000] Selecting client #51 for training.
[INFO][12:43:12]: [Server #28000] Sending the current model to client #51 (simulated).
[INFO][12:43:12]: [Server #28000] Sending 18.75 MB of payload data to client #51 (simulated).
[INFO][12:43:12]: [Server #28000] Selecting client #79 for training.
[INFO][12:43:12]: [Server #28000] Sending the current model to client #79 (simulated).
[INFO][12:43:13]: [Server #28000] Sending 18.75 MB of payload data to client #79 (simulated).
[INFO][12:43:13]: [Server #28000] Selecting client #100 for training.
[INFO][12:43:13]: [Server #28000] Sending the current model to client #100 (simulated).
[INFO][12:43:13]: [Server #28000] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][12:43:13]: [Server #28000] Selecting client #97 for training.
[INFO][12:43:13]: [Server #28000] Sending the current model to client #97 (simulated).
[INFO][12:43:14]: [Server #28000] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][12:43:14]: [Server #28000] Selecting client #52 for training.
[INFO][12:43:14]: [Server #28000] Sending the current model to client #52 (simulated).
[INFO][12:43:14]: [Server #28000] Sending 18.75 MB of payload data to client #52 (simulated).
[INFO][12:46:14]: [Server #28000] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][12:46:16]: [Server #28000] Received 18.75 MB of payload data from client #57 (simulated).
[INFO][12:46:17]: [Server #28000] Received 18.75 MB of payload data from client #24 (simulated).
[INFO][12:46:17]: [Server #28000] Received 18.75 MB of payload data from client #59 (simulated).
[INFO][12:46:18]: [Server #28000] Received 18.75 MB of payload data from client #51 (simulated).
[INFO][12:46:18]: [Server #28000] Received 18.75 MB of payload data from client #95 (simulated).
[INFO][12:46:20]: [Server #28000] Received 18.75 MB of payload data from client #100 (simulated).
[INFO][12:46:21]: [Server #28000] Received 18.75 MB of payload data from client #97 (simulated).
[INFO][12:46:21]: [Server #28000] Received 18.75 MB of payload data from client #79 (simulated).
[INFO][12:46:21]: [Server #28000] Received 18.75 MB of payload data from client #52 (simulated).
[INFO][12:46:21]: [Server #28000] Adding client #96 to the list of clients for aggregation.
[INFO][12:46:21]: [Server #28000] Adding client #71 to the list of clients for aggregation.
[INFO][12:46:21]: [Server #28000] Adding client #70 to the list of clients for aggregation.
[INFO][12:46:21]: [Server #28000] Adding client #28 to the list of clients for aggregation.
[INFO][12:46:21]: [Server #28000] Adding client #40 to the list of clients for aggregation.
[INFO][12:46:21]: [Server #28000] Adding client #43 to the list of clients for aggregation.
[INFO][12:46:21]: [Server #28000] Adding client #5 to the list of clients for aggregation.
[INFO][12:46:21]: [Server #28000] Adding client #84 to the list of clients for aggregation.
[INFO][12:46:21]: [Server #28000] Adding client #68 to the list of clients for aggregation.
[INFO][12:46:21]: [Server #28000] Adding client #34 to the list of clients for aggregation.
[INFO][12:46:21]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:46:21]: [Server #28000] Updated weights have been received.
[INFO][12:46:21]: [Server #28000] Aggregating model weight deltas.
[INFO][12:46:21]: [Server #28000] Finished aggregating updated weights.
[INFO][12:46:21]: [Server #28000] Started model testing.
[INFO][12:46:33]: [Trainer.test] 测试完成 - 准确率: 13.22% (1322/10000)
[INFO][12:46:33]: [93m[1m[Server #28000] Global model accuracy: 13.22%
[0m
[INFO][12:46:33]: get_logged_items 被调用
[INFO][12:46:33]: 从updates获取参与客户端: [96, 71, 70, 28, 40, 43, 5, 84, 68, 34]
[INFO][12:46:33]: 客户端 96 陈旧度: 2 (当前轮次:7, 上次参与:5)
[INFO][12:46:33]: 客户端 71 陈旧度: 1 (当前轮次:7, 上次参与:6)
[INFO][12:46:33]: 客户端 70 陈旧度: 1 (当前轮次:7, 上次参与:6)
[INFO][12:46:33]: 客户端 28 陈旧度: 1 (当前轮次:7, 上次参与:6)
[INFO][12:46:33]: 客户端 40 陈旧度: 1 (当前轮次:7, 上次参与:6)
[INFO][12:46:33]: 客户端 43 陈旧度: 2 (当前轮次:7, 上次参与:5)
[INFO][12:46:33]: 客户端 5 陈旧度: 2 (当前轮次:7, 上次参与:5)
[INFO][12:46:33]: 客户端 84 陈旧度: 5 (当前轮次:7, 上次参与:2)
[INFO][12:46:33]: 客户端 68 陈旧度: 1 (当前轮次:7, 上次参与:6)
[INFO][12:46:33]: 客户端 34 陈旧度: 2 (当前轮次:7, 上次参与:5)
[INFO][12:46:33]: 陈旧度统计 - 参与客户端: [96, 71, 70, 28, 40, 43, 5, 84, 68, 34], 陈旧度: [2, 1, 1, 1, 1, 2, 2, 5, 1, 2]
[INFO][12:46:33]: 平均陈旧度: 1.8, 最大: 5, 最小: 1
[INFO][12:46:33]: 最终logged_items: {'round': 7, 'accuracy': 0.1322, 'accuracy_std': 0, 'elapsed_time': 224.92647457122803, 'processing_time': 0.023610899999994217, 'comm_time': 0, 'round_time': 62.543019013638286, 'comm_overhead': 2999.9534606933594, 'global_accuracy': 0.1322, 'avg_staleness': 1.8, 'max_staleness': 5, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:46:33]: [Server #28000] All client reports have been processed.
[INFO][12:46:33]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_7.pth.
[INFO][12:46:33]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_7.pth.
[INFO][12:46:33]: [93m[1m
[Server #28000] Starting round 8/400.[0m
[INFO][12:46:33]: [Server #28000] Selected clients: [12, 63, 92, 72, 14, 21, 73, 54, 49, 69]
[INFO][12:46:33]: [Server #28000] Selecting client #12 for training.
[INFO][12:46:33]: [Server #28000] Sending the current model to client #12 (simulated).
[INFO][12:46:33]: [Server #28000] Sending 18.75 MB of payload data to client #12 (simulated).
[INFO][12:46:33]: [Server #28000] Selecting client #63 for training.
[INFO][12:46:33]: [Server #28000] Sending the current model to client #63 (simulated).
[INFO][12:46:33]: [Server #28000] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][12:46:33]: [Server #28000] Selecting client #92 for training.
[INFO][12:46:33]: [Server #28000] Sending the current model to client #92 (simulated).
[INFO][12:46:33]: [Server #28000] Sending 18.75 MB of payload data to client #92 (simulated).
[INFO][12:46:33]: [Server #28000] Selecting client #72 for training.
[INFO][12:46:33]: [Server #28000] Sending the current model to client #72 (simulated).
[INFO][12:46:33]: [Server #28000] Sending 18.75 MB of payload data to client #72 (simulated).
[INFO][12:46:33]: [Server #28000] Selecting client #14 for training.
[INFO][12:46:33]: [Server #28000] Sending the current model to client #14 (simulated).
[INFO][12:46:34]: [Server #28000] Sending 18.75 MB of payload data to client #14 (simulated).
[INFO][12:46:34]: [Server #28000] Selecting client #21 for training.
[INFO][12:46:34]: [Server #28000] Sending the current model to client #21 (simulated).
[INFO][12:46:34]: [Server #28000] Sending 18.75 MB of payload data to client #21 (simulated).
[INFO][12:46:34]: [Server #28000] Selecting client #73 for training.
[INFO][12:46:34]: [Server #28000] Sending the current model to client #73 (simulated).
[INFO][12:46:34]: [Server #28000] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][12:46:34]: [Server #28000] Selecting client #54 for training.
[INFO][12:46:34]: [Server #28000] Sending the current model to client #54 (simulated).
[INFO][12:46:35]: [Server #28000] Sending 18.75 MB of payload data to client #54 (simulated).
[INFO][12:46:35]: [Server #28000] Selecting client #49 for training.
[INFO][12:46:35]: [Server #28000] Sending the current model to client #49 (simulated).
[INFO][12:46:35]: [Server #28000] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][12:46:35]: [Server #28000] Selecting client #69 for training.
[INFO][12:46:35]: [Server #28000] Sending the current model to client #69 (simulated).
[INFO][12:46:36]: [Server #28000] Sending 18.75 MB of payload data to client #69 (simulated).
[INFO][12:49:39]: [Server #28000] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][12:49:40]: [Server #28000] Received 18.75 MB of payload data from client #92 (simulated).
[INFO][12:49:41]: [Server #28000] Received 18.75 MB of payload data from client #12 (simulated).
[INFO][12:49:42]: [Server #28000] Received 18.75 MB of payload data from client #14 (simulated).
[INFO][12:49:42]: [Server #28000] Received 18.75 MB of payload data from client #72 (simulated).
[INFO][12:49:42]: [Server #28000] Received 18.75 MB of payload data from client #21 (simulated).
[INFO][12:49:42]: [Server #28000] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][12:49:42]: [Server #28000] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][12:49:42]: [Server #28000] Received 18.75 MB of payload data from client #69 (simulated).
[INFO][12:49:42]: [Server #28000] Received 18.75 MB of payload data from client #54 (simulated).
[INFO][12:49:42]: [Server #28000] Adding client #56 to the list of clients for aggregation.
[INFO][12:49:42]: [Server #28000] Adding client #59 to the list of clients for aggregation.
[INFO][12:49:42]: [Server #28000] Adding client #24 to the list of clients for aggregation.
[INFO][12:49:42]: [Server #28000] Adding client #57 to the list of clients for aggregation.
[INFO][12:49:42]: [Server #28000] Adding client #51 to the list of clients for aggregation.
[INFO][12:49:42]: [Server #28000] Adding client #95 to the list of clients for aggregation.
[INFO][12:49:42]: [Server #28000] Adding client #100 to the list of clients for aggregation.
[INFO][12:49:42]: [Server #28000] Adding client #79 to the list of clients for aggregation.
[INFO][12:49:42]: [Server #28000] Adding client #52 to the list of clients for aggregation.
[INFO][12:49:42]: [Server #28000] Adding client #97 to the list of clients for aggregation.
[INFO][12:49:42]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:49:42]: [Server #28000] Updated weights have been received.
[INFO][12:49:42]: [Server #28000] Aggregating model weight deltas.
[INFO][12:49:42]: [Server #28000] Finished aggregating updated weights.
[INFO][12:49:42]: [Server #28000] Started model testing.
[INFO][12:49:55]: [Trainer.test] 测试完成 - 准确率: 17.25% (1725/10000)
[INFO][12:49:55]: [93m[1m[Server #28000] Global model accuracy: 17.25%
[0m
[INFO][12:49:55]: get_logged_items 被调用
[INFO][12:49:55]: 从updates获取参与客户端: [56, 59, 24, 57, 51, 95, 100, 79, 52, 97]
[INFO][12:49:55]: 客户端 56 陈旧度: 7 (当前轮次:8, 上次参与:1)
[INFO][12:49:55]: 客户端 59 陈旧度: 1 (当前轮次:8, 上次参与:7)
[INFO][12:49:55]: 客户端 24 陈旧度: 1 (当前轮次:8, 上次参与:7)
[INFO][12:49:55]: 客户端 57 陈旧度: 1 (当前轮次:8, 上次参与:7)
[INFO][12:49:55]: 客户端 51 陈旧度: 1 (当前轮次:8, 上次参与:7)
[INFO][12:49:55]: 客户端 95 陈旧度: 1 (当前轮次:8, 上次参与:7)
[INFO][12:49:55]: 客户端 100 陈旧度: 5 (当前轮次:8, 上次参与:3)
[INFO][12:49:55]: 客户端 79 陈旧度: 1 (当前轮次:8, 上次参与:7)
[INFO][12:49:55]: 客户端 52 陈旧度: 1 (当前轮次:8, 上次参与:7)
[INFO][12:49:55]: 客户端 97 陈旧度: 4 (当前轮次:8, 上次参与:4)
[INFO][12:49:55]: 陈旧度统计 - 参与客户端: [56, 59, 24, 57, 51, 95, 100, 79, 52, 97], 陈旧度: [7, 1, 1, 1, 1, 1, 5, 1, 1, 4]
[INFO][12:49:55]: 平均陈旧度: 2.3, 最大: 7, 最小: 1
[INFO][12:49:55]: 最终logged_items: {'round': 8, 'accuracy': 0.1725, 'accuracy_std': 0, 'elapsed_time': 231.19071674346924, 'processing_time': 0.019565099999908853, 'comm_time': 0, 'round_time': 63.38664819793985, 'comm_overhead': 3374.9476432800293, 'global_accuracy': 0.1725, 'avg_staleness': 2.3, 'max_staleness': 7, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:49:55]: [Server #28000] All client reports have been processed.
[INFO][12:49:55]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_8.pth.
[INFO][12:49:55]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_8.pth.
[INFO][12:49:55]: [93m[1m
[Server #28000] Starting round 9/400.[0m
[INFO][12:49:55]: [Server #28000] Selected clients: [4, 67, 6, 43, 88, 85, 84, 56, 93, 25]
[INFO][12:49:55]: [Server #28000] Selecting client #4 for training.
[INFO][12:49:55]: [Server #28000] Sending the current model to client #4 (simulated).
[INFO][12:49:55]: [Server #28000] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][12:49:55]: [Server #28000] Selecting client #67 for training.
[INFO][12:49:55]: [Server #28000] Sending the current model to client #67 (simulated).
[INFO][12:49:55]: [Server #28000] Sending 18.75 MB of payload data to client #67 (simulated).
[INFO][12:49:55]: [Server #28000] Selecting client #6 for training.
[INFO][12:49:55]: [Server #28000] Sending the current model to client #6 (simulated).
[INFO][12:49:55]: [Server #28000] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][12:49:55]: [Server #28000] Selecting client #43 for training.
[INFO][12:49:55]: [Server #28000] Sending the current model to client #43 (simulated).
[INFO][12:49:55]: [Server #28000] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][12:49:55]: [Server #28000] Selecting client #88 for training.
[INFO][12:49:55]: [Server #28000] Sending the current model to client #88 (simulated).
[INFO][12:49:55]: [Server #28000] Sending 18.75 MB of payload data to client #88 (simulated).
[INFO][12:49:55]: [Server #28000] Selecting client #85 for training.
[INFO][12:49:55]: [Server #28000] Sending the current model to client #85 (simulated).
[INFO][12:49:55]: [Server #28000] Sending 18.75 MB of payload data to client #85 (simulated).
[INFO][12:49:55]: [Server #28000] Selecting client #84 for training.
[INFO][12:49:55]: [Server #28000] Sending the current model to client #84 (simulated).
[INFO][12:49:56]: [Server #28000] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][12:49:56]: [Server #28000] Selecting client #56 for training.
[INFO][12:49:56]: [Server #28000] Sending the current model to client #56 (simulated).
[INFO][12:49:56]: [Server #28000] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][12:49:56]: [Server #28000] Selecting client #93 for training.
[INFO][12:49:56]: [Server #28000] Sending the current model to client #93 (simulated).
[INFO][12:49:57]: [Server #28000] Sending 18.75 MB of payload data to client #93 (simulated).
[INFO][12:49:57]: [Server #28000] Selecting client #25 for training.
[INFO][12:49:57]: [Server #28000] Sending the current model to client #25 (simulated).
[INFO][12:49:58]: [Server #28000] Sending 18.75 MB of payload data to client #25 (simulated).
[INFO][12:53:00]: [Server #28000] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][12:53:00]: [Server #28000] Received 18.75 MB of payload data from client #43 (simulated).
[INFO][12:53:01]: [Server #28000] Received 18.75 MB of payload data from client #67 (simulated).
[INFO][12:53:01]: [Server #28000] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][12:53:02]: [Server #28000] Received 18.75 MB of payload data from client #85 (simulated).
[INFO][12:53:02]: [Server #28000] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][12:53:02]: [Server #28000] Received 18.75 MB of payload data from client #93 (simulated).
[INFO][12:53:02]: [Server #28000] Received 18.75 MB of payload data from client #88 (simulated).
[INFO][12:53:02]: [Server #28000] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][12:53:02]: [Server #28000] Received 18.75 MB of payload data from client #25 (simulated).
[INFO][12:53:02]: [Server #28000] Adding client #63 to the list of clients for aggregation.
[INFO][12:53:02]: [Server #28000] Adding client #12 to the list of clients for aggregation.
[INFO][12:53:02]: [Server #28000] Adding client #92 to the list of clients for aggregation.
[INFO][12:53:02]: [Server #28000] Adding client #69 to the list of clients for aggregation.
[INFO][12:53:02]: [Server #28000] Adding client #21 to the list of clients for aggregation.
[INFO][12:53:02]: [Server #28000] Adding client #49 to the list of clients for aggregation.
[INFO][12:53:02]: [Server #28000] Adding client #14 to the list of clients for aggregation.
[INFO][12:53:02]: [Server #28000] Adding client #54 to the list of clients for aggregation.
[INFO][12:53:02]: [Server #28000] Adding client #72 to the list of clients for aggregation.
[INFO][12:53:02]: [Server #28000] Adding client #73 to the list of clients for aggregation.
[INFO][12:53:02]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:53:02]: [Server #28000] Updated weights have been received.
[INFO][12:53:02]: [Server #28000] Aggregating model weight deltas.
[INFO][12:53:02]: [Server #28000] Finished aggregating updated weights.
[INFO][12:53:02]: [Server #28000] Started model testing.
[INFO][12:53:14]: [Trainer.test] 测试完成 - 准确率: 25.67% (2567/10000)
[INFO][12:53:14]: [93m[1m[Server #28000] Global model accuracy: 25.67%
[0m
[INFO][12:53:14]: get_logged_items 被调用
[INFO][12:53:14]: 从updates获取参与客户端: [63, 12, 92, 69, 21, 49, 14, 54, 72, 73]
[INFO][12:53:14]: 客户端 63 陈旧度: 8 (当前轮次:9, 上次参与:1)
[INFO][12:53:14]: 客户端 12 陈旧度: 1 (当前轮次:9, 上次参与:8)
[INFO][12:53:14]: 客户端 92 陈旧度: 3 (当前轮次:9, 上次参与:6)
[INFO][12:53:14]: 客户端 69 陈旧度: 1 (当前轮次:9, 上次参与:8)
[INFO][12:53:14]: 客户端 21 陈旧度: 1 (当前轮次:9, 上次参与:8)
[INFO][12:53:14]: 客户端 49 陈旧度: 8 (当前轮次:9, 上次参与:1)
[INFO][12:53:14]: 客户端 14 陈旧度: 3 (当前轮次:9, 上次参与:6)
[INFO][12:53:14]: 客户端 54 陈旧度: 1 (当前轮次:9, 上次参与:8)
[INFO][12:53:14]: 客户端 72 陈旧度: 4 (当前轮次:9, 上次参与:5)
[INFO][12:53:14]: 客户端 73 陈旧度: 3 (当前轮次:9, 上次参与:6)
[INFO][12:53:14]: 陈旧度统计 - 参与客户端: [63, 12, 92, 69, 21, 49, 14, 54, 72, 73], 陈旧度: [8, 1, 3, 1, 1, 8, 3, 1, 4, 3]
[INFO][12:53:14]: 平均陈旧度: 3.3, 最大: 8, 最小: 1
[INFO][12:53:14]: 最终logged_items: {'round': 9, 'accuracy': 0.2567, 'accuracy_std': 0, 'elapsed_time': 285.78084206581116, 'processing_time': 0.029891499999848747, 'comm_time': 0, 'round_time': 60.85436753006002, 'comm_overhead': 3749.941825866699, 'global_accuracy': 0.2567, 'avg_staleness': 3.3, 'max_staleness': 8, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:53:14]: [Server #28000] All client reports have been processed.
[INFO][12:53:14]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_9.pth.
[INFO][12:53:14]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_9.pth.
[INFO][12:53:14]: [93m[1m
[Server #28000] Starting round 10/400.[0m
[INFO][12:53:14]: [Server #28000] Selected clients: [24, 71, 33, 2, 29, 76, 77, 57, 72, 49]
[INFO][12:53:14]: [Server #28000] Selecting client #24 for training.
[INFO][12:53:14]: [Server #28000] Sending the current model to client #24 (simulated).
[INFO][12:53:14]: [Server #28000] Sending 18.75 MB of payload data to client #24 (simulated).
[INFO][12:53:14]: [Server #28000] Selecting client #71 for training.
[INFO][12:53:14]: [Server #28000] Sending the current model to client #71 (simulated).
[INFO][12:53:14]: [Server #28000] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][12:53:14]: [Server #28000] Selecting client #33 for training.
[INFO][12:53:14]: [Server #28000] Sending the current model to client #33 (simulated).
[INFO][12:53:14]: [Server #28000] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][12:53:14]: [Server #28000] Selecting client #2 for training.
[INFO][12:53:14]: [Server #28000] Sending the current model to client #2 (simulated).
[INFO][12:53:15]: [Server #28000] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][12:53:15]: [Server #28000] Selecting client #29 for training.
[INFO][12:53:15]: [Server #28000] Sending the current model to client #29 (simulated).
[INFO][12:53:15]: [Server #28000] Sending 18.75 MB of payload data to client #29 (simulated).
[INFO][12:53:15]: [Server #28000] Selecting client #76 for training.
[INFO][12:53:15]: [Server #28000] Sending the current model to client #76 (simulated).
[INFO][12:53:15]: [Server #28000] Sending 18.75 MB of payload data to client #76 (simulated).
[INFO][12:53:15]: [Server #28000] Selecting client #77 for training.
[INFO][12:53:15]: [Server #28000] Sending the current model to client #77 (simulated).
[INFO][12:53:15]: [Server #28000] Sending 18.75 MB of payload data to client #77 (simulated).
[INFO][12:53:15]: [Server #28000] Selecting client #57 for training.
[INFO][12:53:15]: [Server #28000] Sending the current model to client #57 (simulated).
[INFO][12:53:16]: [Server #28000] Sending 18.75 MB of payload data to client #57 (simulated).
[INFO][12:53:16]: [Server #28000] Selecting client #72 for training.
[INFO][12:53:16]: [Server #28000] Sending the current model to client #72 (simulated).
[INFO][12:53:17]: [Server #28000] Sending 18.75 MB of payload data to client #72 (simulated).
[INFO][12:53:17]: [Server #28000] Selecting client #49 for training.
[INFO][12:53:17]: [Server #28000] Sending the current model to client #49 (simulated).
[INFO][12:53:18]: [Server #28000] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][12:56:05]: [Server #28000] Received 18.75 MB of payload data from client #71 (simulated).
[INFO][12:56:07]: [Server #28000] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][12:56:08]: [Server #28000] Received 18.75 MB of payload data from client #24 (simulated).
[INFO][12:56:08]: [Server #28000] Received 18.75 MB of payload data from client #29 (simulated).
[INFO][12:56:09]: [Server #28000] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][12:56:09]: [Server #28000] Received 18.75 MB of payload data from client #76 (simulated).
[INFO][12:56:09]: [Server #28000] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][12:56:10]: [Server #28000] Received 18.75 MB of payload data from client #77 (simulated).
[INFO][12:56:10]: [Server #28000] Received 18.75 MB of payload data from client #57 (simulated).
[INFO][12:56:10]: [Server #28000] Received 18.75 MB of payload data from client #72 (simulated).
[INFO][12:56:10]: [Server #28000] Adding client #43 to the list of clients for aggregation.
[INFO][12:56:10]: [Server #28000] Adding client #6 to the list of clients for aggregation.
[INFO][12:56:10]: [Server #28000] Adding client #4 to the list of clients for aggregation.
[INFO][12:56:10]: [Server #28000] Adding client #84 to the list of clients for aggregation.
[INFO][12:56:10]: [Server #28000] Adding client #85 to the list of clients for aggregation.
[INFO][12:56:10]: [Server #28000] Adding client #67 to the list of clients for aggregation.
[INFO][12:56:10]: [Server #28000] Adding client #56 to the list of clients for aggregation.
[INFO][12:56:10]: [Server #28000] Adding client #93 to the list of clients for aggregation.
[INFO][12:56:10]: [Server #28000] Adding client #25 to the list of clients for aggregation.
[INFO][12:56:10]: [Server #28000] Adding client #88 to the list of clients for aggregation.
[INFO][12:56:10]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:56:10]: [Server #28000] Updated weights have been received.
[INFO][12:56:10]: [Server #28000] Aggregating model weight deltas.
[INFO][12:56:10]: [Server #28000] Finished aggregating updated weights.
[INFO][12:56:10]: [Server #28000] Started model testing.
[INFO][12:56:21]: [Trainer.test] 测试完成 - 准确率: 19.01% (1901/10000)
[INFO][12:56:21]: [93m[1m[Server #28000] Global model accuracy: 19.01%
[0m
[INFO][12:56:21]: get_logged_items 被调用
[INFO][12:56:21]: 从updates获取参与客户端: [43, 6, 4, 84, 85, 67, 56, 93, 25, 88]
[INFO][12:56:21]: 客户端 43 陈旧度: 3 (当前轮次:10, 上次参与:7)
[INFO][12:56:21]: 客户端 6 陈旧度: 6 (当前轮次:10, 上次参与:4)
[INFO][12:56:21]: 客户端 4 陈旧度: 6 (当前轮次:10, 上次参与:4)
[INFO][12:56:21]: 客户端 84 陈旧度: 3 (当前轮次:10, 上次参与:7)
[INFO][12:56:21]: 客户端 85 陈旧度: 1 (当前轮次:10, 上次参与:9)
[INFO][12:56:21]: 客户端 67 陈旧度: 5 (当前轮次:10, 上次参与:5)
[INFO][12:56:21]: 客户端 56 陈旧度: 2 (当前轮次:10, 上次参与:8)
[INFO][12:56:21]: 客户端 93 陈旧度: 1 (当前轮次:10, 上次参与:9)
[INFO][12:56:21]: 客户端 25 陈旧度: 4 (当前轮次:10, 上次参与:6)
[INFO][12:56:21]: 客户端 88 陈旧度: 1 (当前轮次:10, 上次参与:9)
[INFO][12:56:21]: 陈旧度统计 - 参与客户端: [43, 6, 4, 84, 85, 67, 56, 93, 25, 88], 陈旧度: [3, 6, 6, 3, 1, 5, 2, 1, 4, 1]
[INFO][12:56:21]: 平均陈旧度: 3.2, 最大: 6, 最小: 1
[INFO][12:56:21]: 最终logged_items: {'round': 10, 'accuracy': 0.1901, 'accuracy_std': 0, 'elapsed_time': 294.1328921318054, 'processing_time': 0.02005589999998847, 'comm_time': 0, 'round_time': 62.94217548753977, 'comm_overhead': 4124.936008453369, 'global_accuracy': 0.1901, 'avg_staleness': 3.2, 'max_staleness': 6, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:56:21]: [Server #28000] All client reports have been processed.
[INFO][12:56:21]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_10.pth.
[INFO][12:56:21]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_10.pth.
[INFO][12:56:21]: [93m[1m
[Server #28000] Starting round 11/400.[0m
[INFO][12:56:21]: [Server #28000] Selected clients: [84, 51, 65, 39, 95, 81, 88, 1, 55, 74]
[INFO][12:56:21]: [Server #28000] Selecting client #84 for training.
[INFO][12:56:21]: [Server #28000] Sending the current model to client #84 (simulated).
[INFO][12:56:21]: [Server #28000] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][12:56:21]: [Server #28000] Selecting client #51 for training.
[INFO][12:56:21]: [Server #28000] Sending the current model to client #51 (simulated).
[INFO][12:56:22]: [Server #28000] Sending 18.75 MB of payload data to client #51 (simulated).
[INFO][12:56:22]: [Server #28000] Selecting client #65 for training.
[INFO][12:56:22]: [Server #28000] Sending the current model to client #65 (simulated).
[INFO][12:56:22]: [Server #28000] Sending 18.75 MB of payload data to client #65 (simulated).
[INFO][12:56:22]: [Server #28000] Selecting client #39 for training.
[INFO][12:56:22]: [Server #28000] Sending the current model to client #39 (simulated).
[INFO][12:56:22]: [Server #28000] Sending 18.75 MB of payload data to client #39 (simulated).
[INFO][12:56:22]: [Server #28000] Selecting client #95 for training.
[INFO][12:56:22]: [Server #28000] Sending the current model to client #95 (simulated).
[INFO][12:56:22]: [Server #28000] Sending 18.75 MB of payload data to client #95 (simulated).
[INFO][12:56:22]: [Server #28000] Selecting client #81 for training.
[INFO][12:56:22]: [Server #28000] Sending the current model to client #81 (simulated).
[INFO][12:56:22]: [Server #28000] Sending 18.75 MB of payload data to client #81 (simulated).
[INFO][12:56:22]: [Server #28000] Selecting client #88 for training.
[INFO][12:56:22]: [Server #28000] Sending the current model to client #88 (simulated).
[INFO][12:56:22]: [Server #28000] Sending 18.75 MB of payload data to client #88 (simulated).
[INFO][12:56:22]: [Server #28000] Selecting client #1 for training.
[INFO][12:56:22]: [Server #28000] Sending the current model to client #1 (simulated).
[INFO][12:56:23]: [Server #28000] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][12:56:23]: [Server #28000] Selecting client #55 for training.
[INFO][12:56:23]: [Server #28000] Sending the current model to client #55 (simulated).
[INFO][12:56:23]: [Server #28000] Sending 18.75 MB of payload data to client #55 (simulated).
[INFO][12:56:23]: [Server #28000] Selecting client #74 for training.
[INFO][12:56:23]: [Server #28000] Sending the current model to client #74 (simulated).
[INFO][12:56:24]: [Server #28000] Sending 18.75 MB of payload data to client #74 (simulated).
[INFO][12:59:42]: [Server #28000] Received 18.75 MB of payload data from client #39 (simulated).
[INFO][12:59:43]: [Server #28000] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][12:59:44]: [Server #28000] Received 18.75 MB of payload data from client #65 (simulated).
[INFO][12:59:44]: [Server #28000] Received 18.75 MB of payload data from client #88 (simulated).
[INFO][12:59:45]: [Server #28000] Received 18.75 MB of payload data from client #51 (simulated).
[INFO][12:59:49]: [Server #28000] Received 18.75 MB of payload data from client #95 (simulated).
[INFO][12:59:49]: [Server #28000] Received 18.75 MB of payload data from client #74 (simulated).
[INFO][12:59:49]: [Server #28000] Received 18.75 MB of payload data from client #81 (simulated).
[INFO][12:59:50]: [Server #28000] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][12:59:50]: [Server #28000] Received 18.75 MB of payload data from client #55 (simulated).
[INFO][12:59:50]: [Server #28000] Adding client #65 to the list of clients for aggregation.
[INFO][12:59:50]: [Server #28000] Adding client #51 to the list of clients for aggregation.
[INFO][12:59:50]: [Server #28000] Adding client #84 to the list of clients for aggregation.
[INFO][12:59:50]: [Server #28000] Adding client #39 to the list of clients for aggregation.
[INFO][12:59:50]: [Server #28000] Adding client #88 to the list of clients for aggregation.
[INFO][12:59:50]: [Server #28000] Adding client #95 to the list of clients for aggregation.
[INFO][12:59:50]: [Server #28000] Adding client #71 to the list of clients for aggregation.
[INFO][12:59:50]: [Server #28000] Adding client #24 to the list of clients for aggregation.
[INFO][12:59:50]: [Server #28000] Adding client #74 to the list of clients for aggregation.
[INFO][12:59:50]: [Server #28000] Adding client #1 to the list of clients for aggregation.
[INFO][12:59:50]: [Server #28000] Aggregating 10 clients in total.
[INFO][12:59:50]: [Server #28000] Updated weights have been received.
[INFO][12:59:50]: [Server #28000] Aggregating model weight deltas.
[INFO][12:59:50]: [Server #28000] Finished aggregating updated weights.
[INFO][12:59:50]: [Server #28000] Started model testing.
[INFO][13:00:42]: [Trainer.test] 测试完成 - 准确率: 22.03% (2203/10000)
[INFO][13:00:42]: [93m[1m[Server #28000] Global model accuracy: 22.03%
[0m
[INFO][13:00:42]: get_logged_items 被调用
[INFO][13:00:42]: 从updates获取参与客户端: [65, 51, 84, 39, 88, 95, 71, 24, 74, 1]
[INFO][13:00:42]: 客户端 65 陈旧度: 1 (当前轮次:11, 上次参与:10)
[INFO][13:00:42]: 客户端 51 陈旧度: 3 (当前轮次:11, 上次参与:8)
[INFO][13:00:42]: 客户端 84 陈旧度: 1 (当前轮次:11, 上次参与:10)
[INFO][13:00:42]: 客户端 39 陈旧度: 1 (当前轮次:11, 上次参与:10)
[INFO][13:00:42]: 客户端 88 陈旧度: 1 (当前轮次:11, 上次参与:10)
[INFO][13:00:42]: 客户端 95 陈旧度: 3 (当前轮次:11, 上次参与:8)
[INFO][13:00:42]: 客户端 71 陈旧度: 4 (当前轮次:11, 上次参与:7)
[INFO][13:00:42]: 客户端 24 陈旧度: 3 (当前轮次:11, 上次参与:8)
[INFO][13:00:42]: 客户端 74 陈旧度: 1 (当前轮次:11, 上次参与:10)
[INFO][13:00:42]: 客户端 1 陈旧度: 9 (当前轮次:11, 上次参与:2)
[INFO][13:00:42]: 陈旧度统计 - 参与客户端: [65, 51, 84, 39, 88, 95, 71, 24, 74, 1], 陈旧度: [1, 3, 1, 1, 1, 3, 4, 3, 1, 9]
[INFO][13:00:42]: 平均陈旧度: 2.7, 最大: 9, 最小: 1
[INFO][13:00:42]: 最终logged_items: {'round': 11, 'accuracy': 0.2203, 'accuracy_std': 0, 'elapsed_time': 347.9216969013214, 'processing_time': 0.01208829999950467, 'comm_time': 0, 'round_time': 60.9481548727631, 'comm_overhead': 4499.930191040039, 'global_accuracy': 0.2203, 'avg_staleness': 2.7, 'max_staleness': 9, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:00:42]: [Server #28000] All client reports have been processed.
[INFO][13:00:42]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_11.pth.
[INFO][13:00:42]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_11.pth.
[INFO][13:00:42]: [93m[1m
[Server #28000] Starting round 12/400.[0m
[INFO][13:00:42]: [Server #28000] Selected clients: [18, 74, 82, 28, 61, 9, 68, 51, 83, 80]
[INFO][13:00:42]: [Server #28000] Selecting client #18 for training.
[INFO][13:00:42]: [Server #28000] Sending the current model to client #18 (simulated).
[INFO][13:00:42]: [Server #28000] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][13:00:42]: [Server #28000] Selecting client #74 for training.
[INFO][13:00:42]: [Server #28000] Sending the current model to client #74 (simulated).
[INFO][13:00:42]: [Server #28000] Sending 18.75 MB of payload data to client #74 (simulated).
[INFO][13:00:42]: [Server #28000] Selecting client #82 for training.
[INFO][13:00:42]: [Server #28000] Sending the current model to client #82 (simulated).
[INFO][13:00:42]: [Server #28000] Sending 18.75 MB of payload data to client #82 (simulated).
[INFO][13:00:42]: [Server #28000] Selecting client #28 for training.
[INFO][13:00:42]: [Server #28000] Sending the current model to client #28 (simulated).
[INFO][13:00:43]: [Server #28000] Sending 18.75 MB of payload data to client #28 (simulated).
[INFO][13:00:43]: [Server #28000] Selecting client #61 for training.
[INFO][13:00:43]: [Server #28000] Sending the current model to client #61 (simulated).
[INFO][13:00:43]: [Server #28000] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][13:00:43]: [Server #28000] Selecting client #9 for training.
[INFO][13:00:43]: [Server #28000] Sending the current model to client #9 (simulated).
[INFO][13:00:43]: [Server #28000] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][13:00:43]: [Server #28000] Selecting client #68 for training.
[INFO][13:00:43]: [Server #28000] Sending the current model to client #68 (simulated).
[INFO][13:00:43]: [Server #28000] Sending 18.75 MB of payload data to client #68 (simulated).
[INFO][13:00:43]: [Server #28000] Selecting client #51 for training.
[INFO][13:00:43]: [Server #28000] Sending the current model to client #51 (simulated).
[INFO][13:00:43]: [Server #28000] Sending 18.75 MB of payload data to client #51 (simulated).
[INFO][13:00:43]: [Server #28000] Selecting client #83 for training.
[INFO][13:00:43]: [Server #28000] Sending the current model to client #83 (simulated).
[INFO][13:00:43]: [Server #28000] Sending 18.75 MB of payload data to client #83 (simulated).
[INFO][13:00:43]: [Server #28000] Selecting client #80 for training.
[INFO][13:00:44]: [Server #28000] Sending the current model to client #80 (simulated).
[INFO][13:00:44]: [Server #28000] Sending 18.75 MB of payload data to client #80 (simulated).
[INFO][13:06:48]: [Server #28000] Received 18.75 MB of payload data from client #82 (simulated).
[INFO][13:06:51]: [Server #28000] Received 18.75 MB of payload data from client #68 (simulated).
[INFO][13:06:53]: [Server #28000] Received 18.75 MB of payload data from client #80 (simulated).
[INFO][13:06:54]: [Server #28000] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][13:06:55]: [Server #28000] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][13:06:55]: [Server #28000] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][13:06:55]: [Server #28000] Received 18.75 MB of payload data from client #74 (simulated).
[INFO][13:06:56]: [Server #28000] Received 18.75 MB of payload data from client #28 (simulated).
[INFO][13:06:56]: [Server #28000] Received 18.75 MB of payload data from client #83 (simulated).
[INFO][13:06:58]: [Server #28000] Received 18.75 MB of payload data from client #51 (simulated).
[INFO][13:06:58]: [Server #28000] Adding client #81 to the list of clients for aggregation.
[INFO][13:06:58]: [Server #28000] Adding client #33 to the list of clients for aggregation.
[INFO][13:06:58]: [Server #28000] Adding client #2 to the list of clients for aggregation.
[INFO][13:06:58]: [Server #28000] Adding client #29 to the list of clients for aggregation.
[INFO][13:06:58]: [Server #28000] Adding client #55 to the list of clients for aggregation.
[INFO][13:06:58]: [Server #28000] Adding client #76 to the list of clients for aggregation.
[INFO][13:06:58]: [Server #28000] Adding client #77 to the list of clients for aggregation.
[INFO][13:06:58]: [Server #28000] Adding client #57 to the list of clients for aggregation.
[INFO][13:06:58]: [Server #28000] Adding client #49 to the list of clients for aggregation.
[INFO][13:06:58]: [Server #28000] Adding client #72 to the list of clients for aggregation.
[INFO][13:06:58]: [Server #28000] Aggregating 10 clients in total.
[INFO][13:06:58]: [Server #28000] Updated weights have been received.
[INFO][13:06:58]: [Server #28000] Aggregating model weight deltas.
[INFO][13:06:58]: [Server #28000] Finished aggregating updated weights.
[INFO][13:06:58]: [Server #28000] Started model testing.
[INFO][13:07:47]: [Trainer.test] 测试完成 - 准确率: 25.72% (2572/10000)
[INFO][13:07:47]: [93m[1m[Server #28000] Global model accuracy: 25.72%
[0m
[INFO][13:07:47]: get_logged_items 被调用
[INFO][13:07:47]: 从updates获取参与客户端: [81, 33, 2, 29, 55, 76, 77, 57, 49, 72]
[INFO][13:07:47]: 客户端 81 陈旧度: 6 (当前轮次:12, 上次参与:6)
[INFO][13:07:47]: 客户端 33 陈旧度: 8 (当前轮次:12, 上次参与:4)
[INFO][13:07:47]: 客户端 2 陈旧度: 8 (当前轮次:12, 上次参与:4)
[INFO][13:07:47]: 客户端 29 陈旧度: 1 (当前轮次:12, 上次参与:11)
[INFO][13:07:47]: 客户端 55 陈旧度: 8 (当前轮次:12, 上次参与:4)
[INFO][13:07:47]: 客户端 76 陈旧度: 1 (当前轮次:12, 上次参与:11)
[INFO][13:07:47]: 客户端 77 陈旧度: 9 (当前轮次:12, 上次参与:3)
[INFO][13:07:47]: 客户端 57 陈旧度: 4 (当前轮次:12, 上次参与:8)
[INFO][13:07:47]: 客户端 49 陈旧度: 3 (当前轮次:12, 上次参与:9)
[INFO][13:07:47]: 客户端 72 陈旧度: 3 (当前轮次:12, 上次参与:9)
[INFO][13:07:47]: 陈旧度统计 - 参与客户端: [81, 33, 2, 29, 55, 76, 77, 57, 49, 72], 陈旧度: [6, 8, 8, 1, 8, 1, 9, 4, 3, 3]
[INFO][13:07:47]: 平均陈旧度: 5.1, 最大: 9, 最小: 1
[INFO][13:07:47]: 最终logged_items: {'round': 12, 'accuracy': 0.2572, 'accuracy_std': 0, 'elapsed_time': 351.98409700393677, 'processing_time': 0.005833700000039244, 'comm_time': 0, 'round_time': 66.20325498735951, 'comm_overhead': 4874.924373626709, 'global_accuracy': 0.2572, 'avg_staleness': 5.1, 'max_staleness': 9, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:07:47]: [Server #28000] All client reports have been processed.
[INFO][13:07:47]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_12.pth.
[INFO][13:07:47]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_12.pth.
[INFO][13:07:47]: [93m[1m
[Server #28000] Starting round 13/400.[0m
[INFO][13:07:47]: [Server #28000] Selected clients: [29, 71, 57, 69, 49, 58, 48, 1, 76, 77]
[INFO][13:07:47]: [Server #28000] Selecting client #29 for training.
[INFO][13:07:47]: [Server #28000] Sending the current model to client #29 (simulated).
[INFO][13:07:47]: [Server #28000] Sending 18.75 MB of payload data to client #29 (simulated).
[INFO][13:07:47]: [Server #28000] Selecting client #71 for training.
[INFO][13:07:47]: [Server #28000] Sending the current model to client #71 (simulated).
[INFO][13:07:47]: [Server #28000] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][13:07:47]: [Server #28000] Selecting client #57 for training.
[INFO][13:07:47]: [Server #28000] Sending the current model to client #57 (simulated).
[INFO][13:07:47]: [Server #28000] Sending 18.75 MB of payload data to client #57 (simulated).
[INFO][13:07:47]: [Server #28000] Selecting client #69 for training.
[INFO][13:07:47]: [Server #28000] Sending the current model to client #69 (simulated).
[INFO][13:07:47]: [Server #28000] Sending 18.75 MB of payload data to client #69 (simulated).
[INFO][13:07:47]: [Server #28000] Selecting client #49 for training.
[INFO][13:07:47]: [Server #28000] Sending the current model to client #49 (simulated).
[INFO][13:07:47]: [Server #28000] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][13:07:47]: [Server #28000] Selecting client #58 for training.
[INFO][13:07:47]: [Server #28000] Sending the current model to client #58 (simulated).
[INFO][13:07:47]: [Server #28000] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][13:07:47]: [Server #28000] Selecting client #48 for training.
[INFO][13:07:47]: [Server #28000] Sending the current model to client #48 (simulated).
[INFO][13:07:47]: [Server #28000] Sending 18.75 MB of payload data to client #48 (simulated).
[INFO][13:07:47]: [Server #28000] Selecting client #1 for training.
[INFO][13:07:47]: [Server #28000] Sending the current model to client #1 (simulated).
[INFO][13:07:48]: [Server #28000] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][13:07:48]: [Server #28000] Selecting client #76 for training.
[INFO][13:07:48]: [Server #28000] Sending the current model to client #76 (simulated).
[INFO][13:07:48]: [Server #28000] Sending 18.75 MB of payload data to client #76 (simulated).
[INFO][13:07:48]: [Server #28000] Selecting client #77 for training.
[INFO][13:07:48]: [Server #28000] Sending the current model to client #77 (simulated).
[INFO][13:07:48]: [Server #28000] Sending 18.75 MB of payload data to client #77 (simulated).
[INFO][13:12:49]: [Server #28000] Received 18.75 MB of payload data from client #48 (simulated).
[INFO][13:12:49]: [Server #28000] Received 18.75 MB of payload data from client #29 (simulated).
[INFO][13:12:51]: [Server #28000] Received 18.75 MB of payload data from client #76 (simulated).
[INFO][13:12:51]: [Server #28000] Received 18.75 MB of payload data from client #77 (simulated).
[INFO][13:12:51]: [Server #28000] Received 18.75 MB of payload data from client #69 (simulated).
[INFO][13:12:51]: [Server #28000] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][13:12:51]: [Server #28000] Received 18.75 MB of payload data from client #71 (simulated).
[INFO][13:12:51]: [Server #28000] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][13:12:51]: [Server #28000] Received 18.75 MB of payload data from client #57 (simulated).
[INFO][13:12:51]: [Server #28000] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][13:12:51]: [Server #28000] Adding client #61 to the list of clients for aggregation.
[INFO][13:12:51]: [Server #28000] Adding client #82 to the list of clients for aggregation.
[INFO][13:12:51]: [Server #28000] Adding client #74 to the list of clients for aggregation.
[INFO][13:12:51]: [Server #28000] Adding client #80 to the list of clients for aggregation.
[INFO][13:12:51]: [Server #28000] Adding client #83 to the list of clients for aggregation.
[INFO][13:12:51]: [Server #28000] Adding client #18 to the list of clients for aggregation.
[INFO][13:12:51]: [Server #28000] Adding client #29 to the list of clients for aggregation.
[INFO][13:12:51]: [Server #28000] Adding client #49 to the list of clients for aggregation.
[INFO][13:12:51]: [Server #28000] Adding client #68 to the list of clients for aggregation.
[INFO][13:12:51]: [Server #28000] Adding client #69 to the list of clients for aggregation.
[INFO][13:12:51]: [Server #28000] Aggregating 10 clients in total.
[INFO][13:12:51]: [Server #28000] Updated weights have been received.
[INFO][13:12:51]: [Server #28000] Aggregating model weight deltas.
[INFO][13:12:51]: [Server #28000] Finished aggregating updated weights.
[INFO][13:12:51]: [Server #28000] Started model testing.
[INFO][13:13:03]: [Trainer.test] 测试完成 - 准确率: 27.94% (2794/10000)
[INFO][13:13:03]: [93m[1m[Server #28000] Global model accuracy: 27.94%
[0m
[INFO][13:13:03]: get_logged_items 被调用
[INFO][13:13:03]: 从updates获取参与客户端: [61, 82, 74, 80, 83, 18, 29, 49, 68, 69]
[INFO][13:13:03]: 客户端 61 陈旧度: 7 (当前轮次:13, 上次参与:6)
[INFO][13:13:03]: 客户端 82 陈旧度: 1 (当前轮次:13, 上次参与:12)
[INFO][13:13:03]: 客户端 74 陈旧度: 2 (当前轮次:13, 上次参与:11)
[INFO][13:13:03]: 客户端 80 陈旧度: 8 (当前轮次:13, 上次参与:5)
[INFO][13:13:03]: 客户端 83 陈旧度: 10 (当前轮次:13, 上次参与:3)
[INFO][13:13:03]: 客户端 18 陈旧度: 12 (当前轮次:13, 上次参与:1)
[INFO][13:13:03]: 客户端 29 陈旧度: 1 (当前轮次:13, 上次参与:12)
[INFO][13:13:03]: 客户端 49 陈旧度: 1 (当前轮次:13, 上次参与:12)
[INFO][13:13:03]: 客户端 68 陈旧度: 6 (当前轮次:13, 上次参与:7)
[INFO][13:13:03]: 客户端 69 陈旧度: 4 (当前轮次:13, 上次参与:9)
[INFO][13:13:03]: 陈旧度统计 - 参与客户端: [61, 82, 74, 80, 83, 18, 29, 49, 68, 69], 陈旧度: [7, 1, 2, 8, 10, 12, 1, 1, 6, 4]
[INFO][13:13:03]: 平均陈旧度: 5.2, 最大: 12, 最小: 1
[INFO][13:13:03]: 最终logged_items: {'round': 13, 'accuracy': 0.2794, 'accuracy_std': 0, 'elapsed_time': 486.63320565223694, 'processing_time': 0.0013964999998279382, 'comm_time': 0, 'round_time': 137.87863111092838, 'comm_overhead': 5249.918556213379, 'global_accuracy': 0.2794, 'avg_staleness': 5.2, 'max_staleness': 12, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:13:03]: [Server #28000] All client reports have been processed.
[INFO][13:13:03]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_13.pth.
[INFO][13:13:03]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_13.pth.
[INFO][13:13:03]: [93m[1m
[Server #28000] Starting round 14/400.[0m
[INFO][13:13:03]: [Server #28000] Selected clients: [90, 89, 46, 66, 87, 5, 33, 92, 25, 81]
[INFO][13:13:03]: [Server #28000] Selecting client #90 for training.
[INFO][13:13:03]: [Server #28000] Sending the current model to client #90 (simulated).
[INFO][13:13:03]: [Server #28000] Sending 18.75 MB of payload data to client #90 (simulated).
[INFO][13:13:03]: [Server #28000] Selecting client #89 for training.
[INFO][13:13:03]: [Server #28000] Sending the current model to client #89 (simulated).
[INFO][13:13:03]: [Server #28000] Sending 18.75 MB of payload data to client #89 (simulated).
[INFO][13:13:03]: [Server #28000] Selecting client #46 for training.
[INFO][13:13:03]: [Server #28000] Sending the current model to client #46 (simulated).
[INFO][13:13:03]: [Server #28000] Sending 18.75 MB of payload data to client #46 (simulated).
[INFO][13:13:03]: [Server #28000] Selecting client #66 for training.
[INFO][13:13:03]: [Server #28000] Sending the current model to client #66 (simulated).
[INFO][13:13:03]: [Server #28000] Sending 18.75 MB of payload data to client #66 (simulated).
[INFO][13:13:03]: [Server #28000] Selecting client #87 for training.
[INFO][13:13:03]: [Server #28000] Sending the current model to client #87 (simulated).
[INFO][13:13:03]: [Server #28000] Sending 18.75 MB of payload data to client #87 (simulated).
[INFO][13:13:03]: [Server #28000] Selecting client #5 for training.
[INFO][13:13:03]: [Server #28000] Sending the current model to client #5 (simulated).
[INFO][13:13:03]: [Server #28000] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][13:13:03]: [Server #28000] Selecting client #33 for training.
[INFO][13:13:03]: [Server #28000] Sending the current model to client #33 (simulated).
[INFO][13:13:04]: [Server #28000] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][13:13:04]: [Server #28000] Selecting client #92 for training.
[INFO][13:13:04]: [Server #28000] Sending the current model to client #92 (simulated).
[INFO][13:13:04]: [Server #28000] Sending 18.75 MB of payload data to client #92 (simulated).
[INFO][13:13:04]: [Server #28000] Selecting client #25 for training.
[INFO][13:13:04]: [Server #28000] Sending the current model to client #25 (simulated).
[INFO][13:13:04]: [Server #28000] Sending 18.75 MB of payload data to client #25 (simulated).
[INFO][13:13:04]: [Server #28000] Selecting client #81 for training.
[INFO][13:13:04]: [Server #28000] Sending the current model to client #81 (simulated).
[INFO][13:13:05]: [Server #28000] Sending 18.75 MB of payload data to client #81 (simulated).
[INFO][13:15:27]: [Server #28000] Received 18.75 MB of payload data from client #89 (simulated).
[INFO][13:15:29]: [Server #28000] Received 18.75 MB of payload data from client #90 (simulated).
[INFO][13:15:29]: [Server #28000] Received 18.75 MB of payload data from client #66 (simulated).
[INFO][13:15:30]: [Server #28000] Received 18.75 MB of payload data from client #87 (simulated).
[INFO][13:15:30]: [Server #28000] Received 18.75 MB of payload data from client #46 (simulated).
[INFO][13:15:30]: [Server #28000] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][13:15:30]: [Server #28000] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][13:15:30]: [Server #28000] Received 18.75 MB of payload data from client #81 (simulated).
[INFO][13:15:31]: [Server #28000] Received 18.75 MB of payload data from client #92 (simulated).
[INFO][13:15:31]: [Server #28000] Received 18.75 MB of payload data from client #25 (simulated).
[INFO][13:15:31]: [Server #28000] Adding client #28 to the list of clients for aggregation.
[INFO][13:15:31]: [Server #28000] Adding client #9 to the list of clients for aggregation.
[INFO][13:15:31]: [Server #28000] Adding client #51 to the list of clients for aggregation.
[INFO][13:15:31]: [Server #28000] Adding client #1 to the list of clients for aggregation.
[INFO][13:15:31]: [Server #28000] Adding client #76 to the list of clients for aggregation.
[INFO][13:15:31]: [Server #28000] Adding client #48 to the list of clients for aggregation.
[INFO][13:15:31]: [Server #28000] Adding client #71 to the list of clients for aggregation.
[INFO][13:15:31]: [Server #28000] Adding client #57 to the list of clients for aggregation.
[INFO][13:15:31]: [Server #28000] Adding client #77 to the list of clients for aggregation.
[INFO][13:15:31]: [Server #28000] Adding client #58 to the list of clients for aggregation.
[INFO][13:15:31]: [Server #28000] Aggregating 10 clients in total.
[INFO][13:15:31]: [Server #28000] Updated weights have been received.
[INFO][13:15:31]: [Server #28000] Aggregating model weight deltas.
[INFO][13:15:31]: [Server #28000] Finished aggregating updated weights.
[INFO][13:15:31]: [Server #28000] Started model testing.
[INFO][13:15:42]: [Trainer.test] 测试完成 - 准确率: 23.55% (2355/10000)
[INFO][13:15:42]: [93m[1m[Server #28000] Global model accuracy: 23.55%
[0m
[INFO][13:15:42]: get_logged_items 被调用
[INFO][13:15:42]: 从updates获取参与客户端: [28, 9, 51, 1, 76, 48, 71, 57, 77, 58]
[INFO][13:15:42]: 客户端 28 陈旧度: 7 (当前轮次:14, 上次参与:7)
[INFO][13:15:42]: 客户端 9 陈旧度: 13 (当前轮次:14, 上次参与:1)
[INFO][13:15:42]: 客户端 51 陈旧度: 3 (当前轮次:14, 上次参与:11)
[INFO][13:15:42]: 客户端 1 陈旧度: 3 (当前轮次:14, 上次参与:11)
[INFO][13:15:42]: 客户端 76 陈旧度: 2 (当前轮次:14, 上次参与:12)
[INFO][13:15:42]: 客户端 48 陈旧度: 1 (当前轮次:14, 上次参与:13)
[INFO][13:15:42]: 客户端 71 陈旧度: 3 (当前轮次:14, 上次参与:11)
[INFO][13:15:42]: 客户端 57 陈旧度: 2 (当前轮次:14, 上次参与:12)
[INFO][13:15:42]: 客户端 77 陈旧度: 2 (当前轮次:14, 上次参与:12)
[INFO][13:15:42]: 客户端 58 陈旧度: 12 (当前轮次:14, 上次参与:2)
[INFO][13:15:42]: 陈旧度统计 - 参与客户端: [28, 9, 51, 1, 76, 48, 71, 57, 77, 58], 陈旧度: [7, 13, 3, 3, 2, 1, 3, 2, 2, 12]
[INFO][13:15:42]: 平均陈旧度: 4.8, 最大: 13, 最小: 1
[INFO][13:15:42]: 最终logged_items: {'round': 14, 'accuracy': 0.2355, 'accuracy_std': 0, 'elapsed_time': 496.36383271217346, 'processing_time': 0.0015918999997666106, 'comm_time': 0, 'round_time': 144.37973564927051, 'comm_overhead': 5624.912738800049, 'global_accuracy': 0.2355, 'avg_staleness': 4.8, 'max_staleness': 13, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:15:42]: [Server #28000] All client reports have been processed.
[INFO][13:15:42]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_14.pth.
[INFO][13:15:42]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_14.pth.
[INFO][13:15:42]: [93m[1m
[Server #28000] Starting round 15/400.[0m
[INFO][13:15:42]: [Server #28000] Selected clients: [80, 26, 13, 76, 36, 6, 97, 11, 12, 3]
[INFO][13:15:42]: [Server #28000] Selecting client #80 for training.
[INFO][13:15:42]: [Server #28000] Sending the current model to client #80 (simulated).
[INFO][13:15:42]: [Server #28000] Sending 18.75 MB of payload data to client #80 (simulated).
[INFO][13:15:42]: [Server #28000] Selecting client #26 for training.
[INFO][13:15:42]: [Server #28000] Sending the current model to client #26 (simulated).
[INFO][13:15:42]: [Server #28000] Sending 18.75 MB of payload data to client #26 (simulated).
[INFO][13:15:42]: [Server #28000] Selecting client #13 for training.
[INFO][13:15:42]: [Server #28000] Sending the current model to client #13 (simulated).
[INFO][13:15:42]: [Server #28000] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][13:15:42]: [Server #28000] Selecting client #76 for training.
[INFO][13:15:42]: [Server #28000] Sending the current model to client #76 (simulated).
[INFO][13:15:42]: [Server #28000] Sending 18.75 MB of payload data to client #76 (simulated).
[INFO][13:15:42]: [Server #28000] Selecting client #36 for training.
[INFO][13:15:42]: [Server #28000] Sending the current model to client #36 (simulated).
[INFO][13:15:42]: [Server #28000] Sending 18.75 MB of payload data to client #36 (simulated).
[INFO][13:15:42]: [Server #28000] Selecting client #6 for training.
[INFO][13:15:42]: [Server #28000] Sending the current model to client #6 (simulated).
[INFO][13:15:43]: [Server #28000] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][13:15:43]: [Server #28000] Selecting client #97 for training.
[INFO][13:15:43]: [Server #28000] Sending the current model to client #97 (simulated).
[INFO][13:15:43]: [Server #28000] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][13:15:43]: [Server #28000] Selecting client #11 for training.
[INFO][13:15:43]: [Server #28000] Sending the current model to client #11 (simulated).
[INFO][13:15:43]: [Server #28000] Sending 18.75 MB of payload data to client #11 (simulated).
[INFO][13:15:43]: [Server #28000] Selecting client #12 for training.
[INFO][13:15:43]: [Server #28000] Sending the current model to client #12 (simulated).
[INFO][13:15:44]: [Server #28000] Sending 18.75 MB of payload data to client #12 (simulated).
[INFO][13:15:44]: [Server #28000] Selecting client #3 for training.
[INFO][13:15:44]: [Server #28000] Sending the current model to client #3 (simulated).
[INFO][13:15:44]: [Server #28000] Sending 18.75 MB of payload data to client #3 (simulated).
[INFO][13:18:15]: [Server #28000] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][13:18:16]: [Server #28000] Received 18.75 MB of payload data from client #80 (simulated).
[INFO][13:18:17]: [Server #28000] Received 18.75 MB of payload data from client #26 (simulated).
[INFO][13:18:19]: [Server #28000] Received 18.75 MB of payload data from client #36 (simulated).
[INFO][13:18:19]: [Server #28000] Received 18.75 MB of payload data from client #97 (simulated).
[INFO][13:18:19]: [Server #28000] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][13:18:19]: [Server #28000] Received 18.75 MB of payload data from client #76 (simulated).
[INFO][13:18:19]: [Server #28000] Received 18.75 MB of payload data from client #11 (simulated).
[INFO][13:18:19]: [Server #28000] Received 18.75 MB of payload data from client #3 (simulated).
[INFO][13:18:19]: [Server #28000] Received 18.75 MB of payload data from client #12 (simulated).
[INFO][13:18:19]: [Server #28000] Adding client #89 to the list of clients for aggregation.
[INFO][13:18:19]: [Server #28000] Adding client #90 to the list of clients for aggregation.
[INFO][13:18:19]: [Server #28000] Adding client #66 to the list of clients for aggregation.
[INFO][13:18:19]: [Server #28000] Adding client #46 to the list of clients for aggregation.
[INFO][13:18:19]: [Server #28000] Adding client #87 to the list of clients for aggregation.
[INFO][13:18:19]: [Server #28000] Adding client #33 to the list of clients for aggregation.
[INFO][13:18:19]: [Server #28000] Adding client #5 to the list of clients for aggregation.
[INFO][13:18:19]: [Server #28000] Adding client #25 to the list of clients for aggregation.
[INFO][13:18:19]: [Server #28000] Adding client #92 to the list of clients for aggregation.
[INFO][13:18:19]: [Server #28000] Adding client #81 to the list of clients for aggregation.
[INFO][13:18:19]: [Server #28000] Aggregating 10 clients in total.
[INFO][13:18:19]: [Server #28000] Updated weights have been received.
[INFO][13:18:19]: [Server #28000] Aggregating model weight deltas.
[INFO][13:18:19]: [Server #28000] Finished aggregating updated weights.
[INFO][13:18:19]: [Server #28000] Started model testing.
[INFO][13:18:31]: [Trainer.test] 测试完成 - 准确率: 28.32% (2832/10000)
[INFO][13:18:31]: [93m[1m[Server #28000] Global model accuracy: 28.32%
[0m
[INFO][13:18:31]: get_logged_items 被调用
[INFO][13:18:31]: 从updates获取参与客户端: [89, 90, 66, 46, 87, 33, 5, 25, 92, 81]
[INFO][13:18:31]: 客户端 89 陈旧度: 1 (当前轮次:15, 上次参与:14)
[INFO][13:18:31]: 客户端 90 陈旧度: 9 (当前轮次:15, 上次参与:6)
[INFO][13:18:31]: 客户端 66 陈旧度: 1 (当前轮次:15, 上次参与:14)
[INFO][13:18:31]: 客户端 46 陈旧度: 1 (当前轮次:15, 上次参与:14)
[INFO][13:18:31]: 客户端 87 陈旧度: 1 (当前轮次:15, 上次参与:14)
[INFO][13:18:31]: 客户端 33 陈旧度: 3 (当前轮次:15, 上次参与:12)
[INFO][13:18:31]: 客户端 5 陈旧度: 8 (当前轮次:15, 上次参与:7)
[INFO][13:18:31]: 客户端 25 陈旧度: 5 (当前轮次:15, 上次参与:10)
[INFO][13:18:31]: 客户端 92 陈旧度: 6 (当前轮次:15, 上次参与:9)
[INFO][13:18:31]: 客户端 81 陈旧度: 3 (当前轮次:15, 上次参与:12)
[INFO][13:18:31]: 陈旧度统计 - 参与客户端: [89, 90, 66, 46, 87, 33, 5, 25, 92, 81], 陈旧度: [1, 9, 1, 1, 1, 3, 8, 5, 6, 3]
[INFO][13:18:31]: 平均陈旧度: 3.8, 最大: 9, 最小: 1
[INFO][13:18:31]: 最终logged_items: {'round': 15, 'accuracy': 0.2832, 'accuracy_std': 0, 'elapsed_time': 532.1704218387604, 'processing_time': 0.0068257000002631685, 'comm_time': 0, 'round_time': 45.53721630248128, 'comm_overhead': 5999.906921386719, 'global_accuracy': 0.2832, 'avg_staleness': 3.8, 'max_staleness': 9, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][13:18:31]: [Server #28000] All client reports have been processed.
[INFO][13:18:31]: [Server #28000] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_15.pth.
[INFO][13:18:31]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_15.pth.
[INFO][13:18:31]: [93m[1m
[Server #28000] Starting round 16/400.[0m
[INFO][13:18:31]: [Server #28000] Selected clients: [65, 2, 43, 39, 42, 20, 89, 30, 52, 45]
[INFO][13:18:31]: [Server #28000] Selecting client #65 for training.
[INFO][13:18:31]: [Server #28000] Sending the current model to client #65 (simulated).
[INFO][13:18:31]: [Server #28000] Sending 18.75 MB of payload data to client #65 (simulated).
[INFO][13:18:31]: [Server #28000] Selecting client #2 for training.
[INFO][13:18:31]: [Server #28000] Sending the current model to client #2 (simulated).
[INFO][13:18:31]: [Server #28000] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][13:18:31]: [Server #28000] Selecting client #43 for training.
[INFO][13:18:31]: [Server #28000] Sending the current model to client #43 (simulated).
[INFO][13:18:31]: [Server #28000] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][13:18:31]: [Server #28000] Selecting client #39 for training.
[INFO][13:18:31]: [Server #28000] Sending the current model to client #39 (simulated).
[INFO][13:18:31]: [Server #28000] Sending 18.75 MB of payload data to client #39 (simulated).
[INFO][13:18:31]: [Server #28000] Selecting client #42 for training.
[INFO][13:18:31]: [Server #28000] Sending the current model to client #42 (simulated).
[INFO][13:18:31]: [Server #28000] Sending 18.75 MB of payload data to client #42 (simulated).
[INFO][13:18:31]: [Server #28000] Selecting client #20 for training.
[INFO][13:18:31]: [Server #28000] Sending the current model to client #20 (simulated).
[INFO][13:18:31]: [Server #28000] Sending 18.75 MB of payload data to client #20 (simulated).
[INFO][13:18:31]: [Server #28000] Selecting client #89 for training.
[INFO][13:18:31]: [Server #28000] Sending the current model to client #89 (simulated).
[INFO][13:18:31]: [Server #28000] Sending 18.75 MB of payload data to client #89 (simulated).
[INFO][13:18:31]: [Server #28000] Selecting client #30 for training.
[INFO][13:18:31]: [Server #28000] Sending the current model to client #30 (simulated).
[INFO][13:18:32]: [Server #28000] Sending 18.75 MB of payload data to client #30 (simulated).
[INFO][13:18:32]: [Server #28000] Selecting client #52 for training.
[INFO][13:18:32]: [Server #28000] Sending the current model to client #52 (simulated).
[INFO][13:18:32]: [Server #28000] Sending 18.75 MB of payload data to client #52 (simulated).
[INFO][13:18:32]: [Server #28000] Selecting client #45 for training.
[INFO][13:18:32]: [Server #28000] Sending the current model to client #45 (simulated).
[INFO][13:18:33]: [Server #28000] Sending 18.75 MB of payload data to client #45 (simulated).
[INFO][13:21:15]: [Server #28000] An existing client just disconnected.
[WARNING][13:21:15]: [Server #28000] Client process #40324 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][13:21:15]: [93m[1m[Server #28000] Closing the server due to a failed client.[0m
[INFO][13:21:15]: [Server #28000] Training concluded.
[INFO][13:21:15]: [Server #28000] Model saved to ./models/refedscafl/cifar10_alpha01/resnet_9.pth.
[INFO][13:21:15]: [Server #28000] Closing the server.
[INFO][13:21:15]: Closing the connection to client #29972.
[INFO][13:21:15]: Closing the connection to client #1352.
[INFO][13:21:15]: Closing the connection to client #26544.
[INFO][13:21:15]: Closing the connection to client #36156.
[INFO][13:21:15]: Closing the connection to client #31076.
[INFO][13:21:15]: Closing the connection to client #8428.
[INFO][13:21:15]: Closing the connection to client #5772.
[INFO][13:21:15]: Closing the connection to client #19612.
[INFO][13:21:15]: Closing the connection to client #27764.
