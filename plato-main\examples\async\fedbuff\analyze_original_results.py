#!/usr/bin/env python3
"""
分析FedBuff原始版本的结果

基于已运行的原始版本结果进行分析，展示添加网络波动前的基准性能。
"""

import pandas as pd
import matplotlib.pyplot as plt
import os
from datetime import datetime

def analyze_original_fedbuff_results():
    """分析原始FedBuff的结果"""
    print("📊 分析FedBuff原始版本结果")
    print("=" * 50)
    
    # 读取原始版本结果
    result_file = "results/mnist_original_fedbuff/01/2764.csv"
    
    if not os.path.exists(result_file):
        print(f"❌ 结果文件不存在: {result_file}")
        return None
    
    try:
        df = pd.read_csv(result_file)
        print(f"✅ 成功读取结果文件: {len(df)} 轮训练数据")
        
        # 显示基本信息
        print(f"\n📋 原始版本基本信息:")
        print(f"   训练轮数: {len(df)}")
        print(f"   最终准确率: {df['accuracy'].iloc[-1]:.4f}")
        print(f"   最高准确率: {df['accuracy'].max():.4f}")
        print(f"   总训练时间: {df['elapsed_time'].iloc[-1]:.1f}秒")
        print(f"   平均每轮时间: {df['elapsed_time'].iloc[-1]/len(df):.1f}秒")
        
        # 显示全局准确率信息
        print(f"\n🌐 全局准确率信息:")
        print(f"   最终全局准确率: {df['global_accuracy'].iloc[-1]:.4f}")
        print(f"   最高全局准确率: {df['global_accuracy'].max():.4f}")
        print(f"   全局准确率标准差: {df['global_accuracy_std'].iloc[-1]:.4f}")
        
        # 显示输出字段
        print(f"\n📊 输出字段 ({len(df.columns)}):")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i}. {col}")
        
        return df
        
    except Exception as e:
        print(f"❌ 读取结果文件失败: {e}")
        return None

def compare_with_expected_enhanced_features():
    """对比预期的增强功能"""
    print(f"\n🆚 与预期增强功能对比")
    print("-" * 50)
    
    original_fields = [
        "round", "elapsed_time", "accuracy", "global_accuracy", "global_accuracy_std"
    ]
    
    enhanced_fields = [
        "round", "elapsed_time", "accuracy", "global_accuracy", "global_accuracy_std",
        "avg_staleness", "max_staleness", "min_staleness"
    ]
    
    network_fields = [
        "round", "elapsed_time", "accuracy", "global_accuracy", "global_accuracy_std",
        "avg_staleness", "max_staleness", "min_staleness", 
        "network_success_rate", "avg_communication_time"
    ]
    
    print("📋 字段对比:")
    print(f"原始版本 ({len(original_fields)} 字段):")
    for field in original_fields:
        print(f"   ✅ {field}")
    
    print(f"\n标准增强版 ({len(enhanced_fields)} 字段):")
    for field in enhanced_fields:
        if field in original_fields:
            print(f"   ✅ {field}")
        else:
            print(f"   🆕 {field}")
    
    print(f"\n网络测试版 ({len(network_fields)} 字段):")
    for field in network_fields:
        if field in original_fields:
            print(f"   ✅ {field}")
        elif field in enhanced_fields:
            print(f"   🆕 {field}")
        else:
            print(f"   🌐 {field}")

def generate_performance_visualization(df):
    """生成性能可视化图表"""
    if df is None:
        print("❌ 无数据可视化")
        return
    
    print(f"\n📊 生成性能可视化图表")
    print("-" * 50)
    
    plt.figure(figsize=(15, 10))
    
    # 1. 准确率变化
    plt.subplot(2, 3, 1)
    plt.plot(df['round'], df['accuracy'], 'b-o', label='Local Accuracy', linewidth=2)
    plt.plot(df['round'], df['global_accuracy'], 'r-s', label='Global Accuracy', linewidth=2)
    plt.xlabel('Round')
    plt.ylabel('Accuracy')
    plt.title('FedBuff Original - Accuracy Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 训练时间
    plt.subplot(2, 3, 2)
    plt.plot(df['round'], df['elapsed_time'], 'g-^', label='Elapsed Time', linewidth=2)
    plt.xlabel('Round')
    plt.ylabel('Time (seconds)')
    plt.title('FedBuff Original - Training Time')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 3. 全局准确率标准差
    plt.subplot(2, 3, 3)
    plt.plot(df['round'], df['global_accuracy_std'], 'm-d', label='Global Acc Std', linewidth=2)
    plt.xlabel('Round')
    plt.ylabel('Standard Deviation')
    plt.title('FedBuff Original - Global Accuracy Std')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 4. 准确率对比
    plt.subplot(2, 3, 4)
    plt.bar(['Local Acc', 'Global Acc'], 
            [df['accuracy'].iloc[-1], df['global_accuracy'].iloc[-1]], 
            color=['blue', 'red'], alpha=0.7)
    plt.ylabel('Final Accuracy')
    plt.title('FedBuff Original - Final Accuracy Comparison')
    plt.grid(True, alpha=0.3)
    
    # 5. 每轮时间分析
    plt.subplot(2, 3, 5)
    round_times = df['elapsed_time'].diff().fillna(df['elapsed_time'].iloc[0])
    plt.plot(df['round'], round_times, 'orange', marker='o', linewidth=2)
    plt.xlabel('Round')
    plt.ylabel('Time per Round (seconds)')
    plt.title('FedBuff Original - Time per Round')
    plt.grid(True, alpha=0.3)
    
    # 6. 收敛分析
    plt.subplot(2, 3, 6)
    plt.plot(df['round'], df['accuracy'], 'b-', label='Local', alpha=0.7, linewidth=2)
    plt.plot(df['round'], df['global_accuracy'], 'r-', label='Global', alpha=0.7, linewidth=2)
    plt.axhline(y=0.8, color='k', linestyle='--', alpha=0.5, label='80% Target')
    plt.xlabel('Round')
    plt.ylabel('Accuracy')
    plt.title('FedBuff Original - Convergence Analysis')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    plot_filename = f"fedbuff_original_analysis_{timestamp}.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"📊 分析图表已保存: {plot_filename}")
    
    plt.show()

def analyze_training_characteristics(df):
    """分析训练特征"""
    if df is None:
        return
    
    print(f"\n🔍 训练特征分析")
    print("-" * 50)
    
    # 收敛分析
    target_accuracy = 0.8
    local_convergence = df[df['accuracy'] >= target_accuracy]
    global_convergence = df[df['global_accuracy'] >= target_accuracy]
    
    print(f"📈 收敛分析 (目标准确率: {target_accuracy}):")
    if len(local_convergence) > 0:
        print(f"   本地准确率达到目标: 第{local_convergence.iloc[0]['round']}轮")
    else:
        print(f"   本地准确率未达到目标 (最高: {df['accuracy'].max():.4f})")
    
    if len(global_convergence) > 0:
        print(f"   全局准确率达到目标: 第{global_convergence.iloc[0]['round']}轮")
    else:
        print(f"   全局准确率未达到目标 (最高: {df['global_accuracy'].max():.4f})")
    
    # 稳定性分析
    print(f"\n📊 稳定性分析:")
    accuracy_std = df['accuracy'].std()
    global_accuracy_std = df['global_accuracy'].std()
    print(f"   本地准确率标准差: {accuracy_std:.4f}")
    print(f"   全局准确率标准差: {global_accuracy_std:.4f}")
    
    # 效率分析
    print(f"\n⚡ 效率分析:")
    avg_round_time = df['elapsed_time'].iloc[-1] / len(df)
    print(f"   平均每轮时间: {avg_round_time:.2f}秒")
    print(f"   准确率提升速度: {(df['accuracy'].iloc[-1] - df['accuracy'].iloc[0]) / len(df):.4f}/轮")

def generate_baseline_summary():
    """生成基准总结"""
    print(f"\n📝 FedBuff原始版本基准总结")
    print("=" * 50)
    
    summary = {
        "算法": "FedBuff (原始版本)",
        "特点": [
            "简单的缓冲聚合机制",
            "基础的准确率记录",
            "标准的CSV输出格式",
            "无网络模拟功能",
            "无陈旧度统计"
        ],
        "输出字段": [
            "round - 训练轮次",
            "elapsed_time - 累计时间",
            "accuracy - 本地准确率",
            "global_accuracy - 全局准确率",
            "global_accuracy_std - 全局准确率标准差"
        ],
        "适用场景": [
            "基础联邦学习实验",
            "算法原型验证",
            "教学演示",
            "简单性能测试"
        ]
    }
    
    for key, value in summary.items():
        print(f"\n🔹 {key}:")
        if isinstance(value, list):
            for item in value:
                print(f"   • {item}")
        else:
            print(f"   {value}")

def main():
    """主分析函数"""
    print("🚀 FedBuff原始版本结果分析")
    print("=" * 60)
    
    # 分析原始结果
    df = analyze_original_fedbuff_results()
    
    # 对比预期增强功能
    compare_with_expected_enhanced_features()
    
    # 生成可视化
    generate_performance_visualization(df)
    
    # 分析训练特征
    analyze_training_characteristics(df)
    
    # 生成基准总结
    generate_baseline_summary()
    
    print(f"\n🎯 分析结论")
    print("=" * 60)
    print("✅ FedBuff原始版本成功运行并生成基准结果")
    print("✅ 基础功能正常工作")
    print("✅ 为后续增强功能对比提供了基准")
    
    print(f"\n💡 下一步:")
    print("• 运行标准增强版FedBuff (添加陈旧度统计)")
    print("• 运行网络测试版FedBuff (添加网络模拟)")
    print("• 对比三个版本的性能差异")
    print("• 分析增强功能的价值和影响")

if __name__ == "__main__":
    main()
