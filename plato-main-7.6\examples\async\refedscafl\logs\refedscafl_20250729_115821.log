[INFO][11:58:21]: 日志系统已初始化
[INFO][11:58:21]: 日志文件路径: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\refedscafl\logs\refedscafl_20250729_115821.log
[INFO][11:58:21]: 日志级别: INFO
[WARNING][11:58:21]: 无法获取系统信息: No module named 'psutil'
[INFO][11:58:21]: 🚀 ReFedScaFL 训练开始
[INFO][11:58:21]: 配置文件: refedscafl_cifar10_resnet9.yml
[INFO][11:58:21]: 开始时间: 2025-07-29 11:58:21
[INFO][11:58:21]: [Client None] 基础初始化完成
[INFO][11:58:21]: 创建模型: resnet_9, 参数: num_classes=10, in_channels=3
[INFO][11:58:21]: 创建并缓存共享模型
[INFO][11:58:21]: [93m[1m[27352] Logging runtime results to: ./results/refedscafl/cifar10_alpha01/27352.csv.[0m
[INFO][11:58:21]: [Server #27352] Started training on 100 clients with 20 per round.
[INFO][11:58:21]: 服务器参数配置完成：
[INFO][11:58:21]: - 客户端数量: total=100, per_round=20
[INFO][11:58:21]: - 权重参数: success=0.8, distill=0.2
[INFO][11:58:21]: - SCAFL参数: V=1.0, tau_max=5
[INFO][11:58:21]: 从共享资源模型提取并缓存全局权重
[INFO][11:58:21]: [Server #27352] Configuring the server...
[INFO][11:58:21]: Training: 400 rounds or accuracy above 100.0%

[INFO][11:58:21]: [Trainer Init] 初始化 Trainer，client_id = 0
[INFO][11:58:21]: [Trainer Init] 模型已设置: <class 'plato.models.resnet.Model'>
[INFO][11:58:21]: [Trainer Init] 模型状态字典已获取，包含 74 个参数
[INFO][11:58:21]: [Trainer Init] 训练器初始化完成，参数：batch_size=50, learning_rate=0.01, epochs=5
[INFO][11:58:21]: Algorithm: fedavg
[INFO][11:58:21]: Data source: CIFAR10
[INFO][11:58:22]: Starting client #1's process.
[INFO][11:58:22]: Starting client #2's process.
[INFO][11:58:22]: Starting client #3's process.
[INFO][11:58:22]: Starting client #4's process.
[INFO][11:58:22]: Starting client #5's process.
[INFO][11:58:22]: Starting client #6's process.
[INFO][11:58:22]: Starting client #7's process.
[INFO][11:58:22]: Starting client #8's process.
[INFO][11:58:22]: Starting client #9's process.
[INFO][11:58:22]: Starting client #10's process.
[INFO][11:58:22]: Setting the random seed for selecting clients: 1
[INFO][11:58:22]: Starting a server at address 127.0.0.1 and port 8095.
[INFO][11:58:37]: [Server #27352] A new client just connected.
[INFO][11:58:37]: [Server #27352] New client with id #2 arrived.
[INFO][11:58:37]: [Server #27352] Client process #33968 registered.
[INFO][11:58:37]: 客户端2注册完成，已初始化ReFedScaFL状态
[INFO][11:58:39]: [Server #27352] A new client just connected.
[INFO][11:58:39]: [Server #27352] New client with id #9 arrived.
[INFO][11:58:39]: [Server #27352] Client process #36408 registered.
[INFO][11:58:39]: 客户端9注册完成，已初始化ReFedScaFL状态
[INFO][11:58:39]: [Server #27352] A new client just connected.
[INFO][11:58:39]: [Server #27352] New client with id #7 arrived.
[INFO][11:58:39]: [Server #27352] Client process #16884 registered.
[INFO][11:58:39]: 客户端7注册完成，已初始化ReFedScaFL状态
[INFO][11:58:39]: [Server #27352] A new client just connected.
[INFO][11:58:39]: [Server #27352] New client with id #1 arrived.
[INFO][11:58:39]: [Server #27352] Client process #28468 registered.
[INFO][11:58:39]: 客户端1注册完成，已初始化ReFedScaFL状态
[INFO][11:58:39]: [Server #27352] A new client just connected.
[INFO][11:58:39]: [Server #27352] A new client just connected.
[INFO][11:58:39]: [Server #27352] A new client just connected.
[INFO][11:58:39]: [Server #27352] New client with id #4 arrived.
[INFO][11:58:39]: [Server #27352] Client process #8936 registered.
[INFO][11:58:39]: 客户端4注册完成，已初始化ReFedScaFL状态
[INFO][11:58:39]: [Server #27352] New client with id #5 arrived.
[INFO][11:58:39]: [Server #27352] Client process #40176 registered.
[INFO][11:58:39]: 客户端5注册完成，已初始化ReFedScaFL状态
[INFO][11:58:39]: [Server #27352] New client with id #6 arrived.
[INFO][11:58:39]: [Server #27352] Client process #5884 registered.
[INFO][11:58:39]: 客户端6注册完成，已初始化ReFedScaFL状态
[INFO][11:58:39]: [Server #27352] A new client just connected.
[INFO][11:58:39]: [Server #27352] New client with id #10 arrived.
[INFO][11:58:39]: [Server #27352] Client process #24064 registered.
[INFO][11:58:39]: 客户端10注册完成，已初始化ReFedScaFL状态
[INFO][11:58:39]: [Server #27352] A new client just connected.
[INFO][11:58:39]: [Server #27352] A new client just connected.
[INFO][11:58:39]: [Server #27352] New client with id #3 arrived.
[INFO][11:58:39]: [Server #27352] Client process #17796 registered.
[INFO][11:58:39]: 客户端3注册完成，已初始化ReFedScaFL状态
[INFO][11:58:39]: [Server #27352] New client with id #8 arrived.
[INFO][11:58:39]: [Server #27352] Client process #39672 registered.
[INFO][11:58:39]: [Server #27352] Starting training.
[INFO][11:58:39]: [93m[1m
[Server #27352] Starting round 1/400.[0m
[INFO][11:58:39]: [Server #27352] Selected clients: [18, 73, 98, 9, 33, 16, 64, 58, 61, 84, 49, 27, 13, 63, 4, 50, 56, 78, 99, 1]
[INFO][11:58:39]: [Server #27352] Selecting client #18 for training.
[INFO][11:58:39]: [Server #27352] Sending the current model to client #18 (simulated).
[INFO][11:58:39]: [Server #27352] Sending 18.75 MB of payload data to client #18 (simulated).
[INFO][11:58:39]: [Server #27352] Selecting client #73 for training.
[INFO][11:58:39]: [Server #27352] Sending the current model to client #73 (simulated).
[INFO][11:58:39]: [Server #27352] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][11:58:39]: [Server #27352] Selecting client #98 for training.
[INFO][11:58:39]: [Server #27352] Sending the current model to client #98 (simulated).
[INFO][11:58:39]: [Server #27352] Sending 18.75 MB of payload data to client #98 (simulated).
[INFO][11:58:39]: [Server #27352] Selecting client #9 for training.
[INFO][11:58:39]: [Server #27352] Sending the current model to client #9 (simulated).
[INFO][11:58:39]: [Server #27352] Sending 18.75 MB of payload data to client #9 (simulated).
[INFO][11:58:39]: [Server #27352] Selecting client #33 for training.
[INFO][11:58:39]: [Server #27352] Sending the current model to client #33 (simulated).
[INFO][11:58:39]: [Server #27352] Sending 18.75 MB of payload data to client #33 (simulated).
[INFO][11:58:39]: [Server #27352] Selecting client #16 for training.
[INFO][11:58:39]: [Server #27352] Sending the current model to client #16 (simulated).
[INFO][11:58:39]: [Server #27352] Sending 18.75 MB of payload data to client #16 (simulated).
[INFO][11:58:39]: [Server #27352] Selecting client #64 for training.
[INFO][11:58:39]: [Server #27352] Sending the current model to client #64 (simulated).
[INFO][11:58:39]: [Server #27352] Sending 18.75 MB of payload data to client #64 (simulated).
[INFO][11:58:39]: [Server #27352] Selecting client #58 for training.
[INFO][11:58:39]: [Server #27352] Sending the current model to client #58 (simulated).
[INFO][11:58:39]: [Server #27352] Sending 18.75 MB of payload data to client #58 (simulated).
[INFO][11:58:39]: [Server #27352] Selecting client #61 for training.
[INFO][11:58:39]: [Server #27352] Sending the current model to client #61 (simulated).
[INFO][11:58:39]: [Server #27352] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][11:58:39]: [Server #27352] Selecting client #84 for training.
[INFO][11:58:39]: [Server #27352] Sending the current model to client #84 (simulated).
[INFO][11:58:39]: [Server #27352] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][11:58:39]: 客户端8注册完成，已初始化ReFedScaFL状态
[INFO][12:00:57]: [Server #27352] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][12:00:58]: [Server #27352] Received 18.75 MB of payload data from client #18 (simulated).
[INFO][12:01:00]: [Server #27352] Received 18.75 MB of payload data from client #98 (simulated).
[INFO][12:01:03]: [Server #27352] Received 18.75 MB of payload data from client #33 (simulated).
[INFO][12:01:03]: [Server #27352] Received 18.75 MB of payload data from client #64 (simulated).
[INFO][12:01:03]: [Server #27352] Received 18.75 MB of payload data from client #58 (simulated).
[INFO][12:01:03]: [Server #27352] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][12:01:04]: [Server #27352] Received 18.75 MB of payload data from client #16 (simulated).
[INFO][12:01:04]: [Server #27352] Received 18.75 MB of payload data from client #84 (simulated).
[INFO][12:01:04]: [Server #27352] Received 18.75 MB of payload data from client #9 (simulated).
[INFO][12:01:04]: [Server #27352] Selecting client #49 for training.
[INFO][12:01:04]: [Server #27352] Sending the current model to client #49 (simulated).
[INFO][12:01:04]: [Server #27352] Sending 18.75 MB of payload data to client #49 (simulated).
[INFO][12:01:04]: [Server #27352] Selecting client #27 for training.
[INFO][12:01:04]: [Server #27352] Sending the current model to client #27 (simulated).
[INFO][12:01:04]: [Server #27352] Sending 18.75 MB of payload data to client #27 (simulated).
[INFO][12:01:04]: [Server #27352] Selecting client #13 for training.
[INFO][12:01:04]: [Server #27352] Sending the current model to client #13 (simulated).
[INFO][12:01:04]: [Server #27352] Sending 18.75 MB of payload data to client #13 (simulated).
[INFO][12:01:04]: [Server #27352] Selecting client #63 for training.
[INFO][12:01:04]: [Server #27352] Sending the current model to client #63 (simulated).
[INFO][12:01:04]: [Server #27352] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][12:01:04]: [Server #27352] Selecting client #4 for training.
[INFO][12:01:04]: [Server #27352] Sending the current model to client #4 (simulated).
[INFO][12:01:04]: [Server #27352] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][12:01:04]: [Server #27352] Selecting client #50 for training.
[INFO][12:01:04]: [Server #27352] Sending the current model to client #50 (simulated).
[INFO][12:01:08]: [Server #27352] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][12:01:08]: [Server #27352] Selecting client #56 for training.
[INFO][12:01:08]: [Server #27352] Sending the current model to client #56 (simulated).
[INFO][12:01:08]: [Server #27352] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][12:01:08]: [Server #27352] Selecting client #78 for training.
[INFO][12:01:08]: [Server #27352] Sending the current model to client #78 (simulated).
[INFO][12:01:08]: [Server #27352] Sending 18.75 MB of payload data to client #78 (simulated).
[INFO][12:01:08]: [Server #27352] Selecting client #99 for training.
[INFO][12:01:08]: [Server #27352] Sending the current model to client #99 (simulated).
[INFO][12:01:08]: [Server #27352] Sending 18.75 MB of payload data to client #99 (simulated).
[INFO][12:01:08]: [Server #27352] Selecting client #1 for training.
[INFO][12:01:08]: [Server #27352] Sending the current model to client #1 (simulated).
[INFO][12:01:09]: [Server #27352] Sending 18.75 MB of payload data to client #1 (simulated).
[INFO][12:03:29]: [Server #27352] Received 18.75 MB of payload data from client #49 (simulated).
[INFO][12:03:31]: [Server #27352] Received 18.75 MB of payload data from client #27 (simulated).
[INFO][12:03:32]: [Server #27352] Received 18.75 MB of payload data from client #13 (simulated).
[INFO][12:03:33]: [Server #27352] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][12:03:37]: [Server #27352] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][12:03:38]: [Server #27352] Received 18.75 MB of payload data from client #78 (simulated).
[INFO][12:03:38]: [Server #27352] Received 18.75 MB of payload data from client #1 (simulated).
[INFO][12:03:38]: [Server #27352] Received 18.75 MB of payload data from client #56 (simulated).
[INFO][12:03:38]: [Server #27352] Received 18.75 MB of payload data from client #99 (simulated).
[INFO][12:03:38]: [Server #27352] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][12:03:38]: [Server #27352] Adding client #49 to the list of clients for aggregation.
[INFO][12:03:38]: [Server #27352] Adding client #27 to the list of clients for aggregation.
[INFO][12:03:38]: [Server #27352] Adding client #63 to the list of clients for aggregation.
[INFO][12:03:38]: [Server #27352] Adding client #13 to the list of clients for aggregation.
[INFO][12:03:38]: [Server #27352] Adding client #73 to the list of clients for aggregation.
[INFO][12:03:38]: [Server #27352] Adding client #18 to the list of clients for aggregation.
[INFO][12:03:38]: [Server #27352] Adding client #98 to the list of clients for aggregation.
[INFO][12:03:38]: [Server #27352] Adding client #4 to the list of clients for aggregation.
[INFO][12:03:38]: [Server #27352] Adding client #58 to the list of clients for aggregation.
[INFO][12:03:38]: [Server #27352] Adding client #99 to the list of clients for aggregation.
[INFO][12:03:38]: [Server #27352] Aggregating 10 clients in total.
[INFO][12:03:38]: [Server #27352] Updated weights have been received.
[INFO][12:03:38]: [Server #27352] Aggregating model weight deltas.
[INFO][12:03:38]: [Server #27352] Finished aggregating updated weights.
[INFO][12:03:38]: [Server #27352] Started model testing.
[INFO][12:03:52]: [Trainer.test] 测试完成 - 准确率: 15.51% (1551/10000)
[INFO][12:03:52]: [93m[1m[Server #27352] Global model accuracy: 15.51%
[0m
[INFO][12:03:52]: get_logged_items 被调用
[INFO][12:03:52]: 从updates获取参与客户端: [49, 27, 63, 13, 73, 18, 98, 4, 58, 99]
[INFO][12:03:52]: 客户端 49 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:03:52]: 客户端 27 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:03:52]: 客户端 63 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:03:52]: 客户端 13 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:03:52]: 客户端 73 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:03:52]: 客户端 18 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:03:52]: 客户端 98 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:03:52]: 客户端 4 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:03:52]: 客户端 58 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:03:52]: 客户端 99 陈旧度: 1 (当前轮次:1, 上次参与:0)
[INFO][12:03:52]: 陈旧度统计 - 参与客户端: [49, 27, 63, 13, 73, 18, 98, 4, 58, 99], 陈旧度: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
[INFO][12:03:52]: 平均陈旧度: 1.0, 最大: 1, 最小: 1
[INFO][12:03:52]: 最终logged_items: {'round': 1, 'accuracy': 0.1551, 'accuracy_std': 0, 'elapsed_time': 48.775673627853394, 'processing_time': 0.009138100000001259, 'comm_time': 0, 'round_time': 48.775673639227364, 'comm_overhead': 749.9883651733398, 'global_accuracy': 0.1551, 'avg_staleness': 1.0, 'max_staleness': 1, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:03:52]: [Server #27352] All client reports have been processed.
[INFO][12:03:52]: [Server #27352] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][12:03:52]: [Server #27352] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_1.pth.
[INFO][12:03:52]: [93m[1m
[Server #27352] Starting round 2/400.[0m
[INFO][12:03:52]: [Server #27352] Selected clients: [100, 66, 39, 34, 86, 17, 45, 5, 4, 94]
[INFO][12:03:52]: [Server #27352] Selecting client #100 for training.
[INFO][12:03:52]: [Server #27352] Sending the current model to client #100 (simulated).
[INFO][12:03:52]: [Server #27352] Sending 18.75 MB of payload data to client #100 (simulated).
[INFO][12:03:52]: [Server #27352] Selecting client #66 for training.
[INFO][12:03:52]: [Server #27352] Sending the current model to client #66 (simulated).
[INFO][12:03:52]: [Server #27352] Sending 18.75 MB of payload data to client #66 (simulated).
[INFO][12:03:52]: [Server #27352] Selecting client #39 for training.
[INFO][12:03:52]: [Server #27352] Sending the current model to client #39 (simulated).
[INFO][12:03:52]: [Server #27352] Sending 18.75 MB of payload data to client #39 (simulated).
[INFO][12:03:52]: [Server #27352] Selecting client #34 for training.
[INFO][12:03:52]: [Server #27352] Sending the current model to client #34 (simulated).
[INFO][12:03:52]: [Server #27352] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][12:03:52]: [Server #27352] Selecting client #86 for training.
[INFO][12:03:52]: [Server #27352] Sending the current model to client #86 (simulated).
[INFO][12:03:52]: [Server #27352] Sending 18.75 MB of payload data to client #86 (simulated).
[INFO][12:03:52]: [Server #27352] Selecting client #17 for training.
[INFO][12:03:52]: [Server #27352] Sending the current model to client #17 (simulated).
[INFO][12:03:52]: [Server #27352] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][12:03:52]: [Server #27352] Selecting client #45 for training.
[INFO][12:03:52]: [Server #27352] Sending the current model to client #45 (simulated).
[INFO][12:03:52]: [Server #27352] Sending 18.75 MB of payload data to client #45 (simulated).
[INFO][12:03:52]: [Server #27352] Selecting client #5 for training.
[INFO][12:03:52]: [Server #27352] Sending the current model to client #5 (simulated).
[INFO][12:03:53]: [Server #27352] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][12:03:53]: [Server #27352] Selecting client #4 for training.
[INFO][12:03:53]: [Server #27352] Sending the current model to client #4 (simulated).
[INFO][12:03:53]: [Server #27352] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][12:03:53]: [Server #27352] Selecting client #94 for training.
[INFO][12:03:53]: [Server #27352] Sending the current model to client #94 (simulated).
[INFO][12:03:54]: [Server #27352] Sending 18.75 MB of payload data to client #94 (simulated).
[INFO][12:06:28]: [Server #27352] Received 18.75 MB of payload data from client #66 (simulated).
[INFO][12:06:29]: [Server #27352] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][12:06:29]: [Server #27352] Received 18.75 MB of payload data from client #100 (simulated).
[INFO][12:06:30]: [Server #27352] Received 18.75 MB of payload data from client #86 (simulated).
[INFO][12:06:31]: [Server #27352] Received 18.75 MB of payload data from client #39 (simulated).
[INFO][12:06:31]: [Server #27352] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][12:06:31]: [Server #27352] Received 18.75 MB of payload data from client #45 (simulated).
[INFO][12:06:31]: [Server #27352] Received 18.75 MB of payload data from client #94 (simulated).
[INFO][12:06:31]: [Server #27352] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][12:06:32]: [Server #27352] Received 18.75 MB of payload data from client #5 (simulated).
[INFO][12:06:32]: [Server #27352] Adding client #64 to the list of clients for aggregation.
[INFO][12:06:32]: [Server #27352] Adding client #33 to the list of clients for aggregation.
[INFO][12:06:32]: [Server #27352] Adding client #61 to the list of clients for aggregation.
[INFO][12:06:32]: [Server #27352] Adding client #56 to the list of clients for aggregation.
[INFO][12:06:32]: [Server #27352] Adding client #78 to the list of clients for aggregation.
[INFO][12:06:32]: [Server #27352] Adding client #1 to the list of clients for aggregation.
[INFO][12:06:32]: [Server #27352] Adding client #50 to the list of clients for aggregation.
[INFO][12:06:32]: [Server #27352] Adding client #84 to the list of clients for aggregation.
[INFO][12:06:32]: [Server #27352] Adding client #9 to the list of clients for aggregation.
[INFO][12:06:32]: [Server #27352] Adding client #16 to the list of clients for aggregation.
[INFO][12:06:32]: [Server #27352] Aggregating 10 clients in total.
[INFO][12:06:32]: [Server #27352] Updated weights have been received.
[INFO][12:06:32]: [Server #27352] Aggregating model weight deltas.
[INFO][12:06:32]: [Server #27352] Finished aggregating updated weights.
[INFO][12:06:32]: [Server #27352] Started model testing.
[INFO][12:06:43]: [Trainer.test] 测试完成 - 准确率: 16.29% (1629/10000)
[INFO][12:06:43]: [93m[1m[Server #27352] Global model accuracy: 16.29%
[0m
[INFO][12:06:43]: get_logged_items 被调用
[INFO][12:06:43]: 从updates获取参与客户端: [64, 33, 61, 56, 78, 1, 50, 84, 9, 16]
[INFO][12:06:43]: 客户端 64 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:06:43]: 客户端 33 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:06:43]: 客户端 61 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:06:43]: 客户端 56 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:06:43]: 客户端 78 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:06:43]: 客户端 1 陈旧度: 2 (当前轮次:2, 上次参与:0)
[INFO][12:06:43]: 客户端 50 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:06:43]: 客户端 84 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:06:43]: 客户端 9 陈旧度: 2 (当前轮次:2, 上次参与:0)
[INFO][12:06:43]: 客户端 16 陈旧度: 1 (当前轮次:2, 上次参与:1)
[INFO][12:06:43]: 陈旧度统计 - 参与客户端: [64, 33, 61, 56, 78, 1, 50, 84, 9, 16], 陈旧度: [1, 1, 1, 1, 1, 2, 1, 1, 2, 1]
[INFO][12:06:43]: 平均陈旧度: 1.2, 最大: 2, 最小: 1
[INFO][12:06:43]: 最终logged_items: {'round': 2, 'accuracy': 0.1629, 'accuracy_std': 0, 'elapsed_time': 50.405577182769775, 'processing_time': 0.006918800000004666, 'comm_time': 0, 'round_time': 50.40557707965088, 'comm_overhead': 1124.9825477600098, 'global_accuracy': 0.1629, 'avg_staleness': 1.2, 'max_staleness': 2, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:06:43]: [Server #27352] All client reports have been processed.
[INFO][12:06:43]: [Server #27352] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][12:06:43]: [Server #27352] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_2.pth.
[INFO][12:06:43]: [93m[1m
[Server #27352] Starting round 3/400.[0m
[INFO][12:06:43]: [Server #27352] Selected clients: [77, 2, 55, 97, 31, 61, 6, 75, 32, 63]
[INFO][12:06:43]: [Server #27352] Selecting client #77 for training.
[INFO][12:06:43]: [Server #27352] Sending the current model to client #77 (simulated).
[INFO][12:06:43]: [Server #27352] Sending 18.75 MB of payload data to client #77 (simulated).
[INFO][12:06:43]: [Server #27352] Selecting client #2 for training.
[INFO][12:06:43]: [Server #27352] Sending the current model to client #2 (simulated).
[INFO][12:06:43]: [Server #27352] Sending 18.75 MB of payload data to client #2 (simulated).
[INFO][12:06:43]: [Server #27352] Selecting client #55 for training.
[INFO][12:06:43]: [Server #27352] Sending the current model to client #55 (simulated).
[INFO][12:06:43]: [Server #27352] Sending 18.75 MB of payload data to client #55 (simulated).
[INFO][12:06:43]: [Server #27352] Selecting client #97 for training.
[INFO][12:06:43]: [Server #27352] Sending the current model to client #97 (simulated).
[INFO][12:06:43]: [Server #27352] Sending 18.75 MB of payload data to client #97 (simulated).
[INFO][12:06:43]: [Server #27352] Selecting client #31 for training.
[INFO][12:06:43]: [Server #27352] Sending the current model to client #31 (simulated).
[INFO][12:06:43]: [Server #27352] Sending 18.75 MB of payload data to client #31 (simulated).
[INFO][12:06:43]: [Server #27352] Selecting client #61 for training.
[INFO][12:06:43]: [Server #27352] Sending the current model to client #61 (simulated).
[INFO][12:06:43]: [Server #27352] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][12:06:43]: [Server #27352] Selecting client #6 for training.
[INFO][12:06:43]: [Server #27352] Sending the current model to client #6 (simulated).
[INFO][12:06:44]: [Server #27352] Sending 18.75 MB of payload data to client #6 (simulated).
[INFO][12:06:44]: [Server #27352] Selecting client #75 for training.
[INFO][12:06:44]: [Server #27352] Sending the current model to client #75 (simulated).
[INFO][12:06:44]: [Server #27352] Sending 18.75 MB of payload data to client #75 (simulated).
[INFO][12:06:44]: [Server #27352] Selecting client #32 for training.
[INFO][12:06:44]: [Server #27352] Sending the current model to client #32 (simulated).
[INFO][12:06:44]: [Server #27352] Sending 18.75 MB of payload data to client #32 (simulated).
[INFO][12:06:44]: [Server #27352] Selecting client #63 for training.
[INFO][12:06:44]: [Server #27352] Sending the current model to client #63 (simulated).
[INFO][12:06:45]: [Server #27352] Sending 18.75 MB of payload data to client #63 (simulated).
[INFO][12:09:38]: [Server #27352] Received 18.75 MB of payload data from client #77 (simulated).
[INFO][12:09:39]: [Server #27352] Received 18.75 MB of payload data from client #2 (simulated).
[INFO][12:09:40]: [Server #27352] Received 18.75 MB of payload data from client #97 (simulated).
[INFO][12:09:40]: [Server #27352] Received 18.75 MB of payload data from client #55 (simulated).
[INFO][12:09:41]: [Server #27352] Received 18.75 MB of payload data from client #31 (simulated).
[INFO][12:09:42]: [Server #27352] Received 18.75 MB of payload data from client #6 (simulated).
[INFO][12:09:42]: [Server #27352] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][12:09:42]: [Server #27352] Received 18.75 MB of payload data from client #75 (simulated).
[INFO][12:09:42]: [Server #27352] Received 18.75 MB of payload data from client #32 (simulated).
[INFO][12:09:42]: [Server #27352] Received 18.75 MB of payload data from client #63 (simulated).
[INFO][12:09:42]: [Server #27352] Adding client #66 to the list of clients for aggregation.
[INFO][12:09:42]: [Server #27352] Adding client #34 to the list of clients for aggregation.
[INFO][12:09:42]: [Server #27352] Adding client #100 to the list of clients for aggregation.
[INFO][12:09:42]: [Server #27352] Adding client #86 to the list of clients for aggregation.
[INFO][12:09:42]: [Server #27352] Adding client #39 to the list of clients for aggregation.
[INFO][12:09:42]: [Server #27352] Adding client #77 to the list of clients for aggregation.
[INFO][12:09:42]: [Server #27352] Adding client #4 to the list of clients for aggregation.
[INFO][12:09:42]: [Server #27352] Adding client #17 to the list of clients for aggregation.
[INFO][12:09:42]: [Server #27352] Adding client #94 to the list of clients for aggregation.
[INFO][12:09:42]: [Server #27352] Adding client #45 to the list of clients for aggregation.
[INFO][12:09:42]: [Server #27352] Aggregating 10 clients in total.
[INFO][12:09:42]: [Server #27352] Updated weights have been received.
[INFO][12:09:42]: [Server #27352] Aggregating model weight deltas.
[INFO][12:09:42]: [Server #27352] Finished aggregating updated weights.
[INFO][12:09:42]: [Server #27352] Started model testing.
[INFO][12:09:55]: [Trainer.test] 测试完成 - 准确率: 15.11% (1511/10000)
[INFO][12:09:55]: [93m[1m[Server #27352] Global model accuracy: 15.11%
[0m
[INFO][12:09:55]: get_logged_items 被调用
[INFO][12:09:55]: 从updates获取参与客户端: [66, 34, 100, 86, 39, 77, 4, 17, 94, 45]
[INFO][12:09:55]: 客户端 66 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:09:55]: 客户端 34 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:09:55]: 客户端 100 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:09:55]: 客户端 86 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:09:55]: 客户端 39 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:09:55]: 客户端 77 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:09:55]: 客户端 4 陈旧度: 2 (当前轮次:3, 上次参与:1)
[INFO][12:09:55]: 客户端 17 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:09:55]: 客户端 94 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:09:55]: 客户端 45 陈旧度: 1 (当前轮次:3, 上次参与:2)
[INFO][12:09:55]: 陈旧度统计 - 参与客户端: [66, 34, 100, 86, 39, 77, 4, 17, 94, 45], 陈旧度: [1, 1, 1, 1, 1, 1, 2, 1, 1, 1]
[INFO][12:09:55]: 平均陈旧度: 1.1, 最大: 2, 最小: 1
[INFO][12:09:55]: 最终logged_items: {'round': 3, 'accuracy': 0.1511, 'accuracy_std': 0, 'elapsed_time': 102.53765487670898, 'processing_time': 0.015839099999993778, 'comm_time': 0, 'round_time': 53.76198127448424, 'comm_overhead': 1499.9767303466797, 'global_accuracy': 0.1511, 'avg_staleness': 1.1, 'max_staleness': 2, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:09:55]: [Server #27352] All client reports have been processed.
[INFO][12:09:55]: [Server #27352] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][12:09:55]: [Server #27352] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_3.pth.
[INFO][12:09:55]: [93m[1m
[Server #27352] Starting round 4/400.[0m
[INFO][12:09:55]: [Server #27352] Selected clients: [72, 80, 35, 50, 96, 34, 67, 43, 4, 60]
[INFO][12:09:55]: [Server #27352] Selecting client #72 for training.
[INFO][12:09:55]: [Server #27352] Sending the current model to client #72 (simulated).
[INFO][12:09:55]: [Server #27352] Sending 18.75 MB of payload data to client #72 (simulated).
[INFO][12:09:55]: [Server #27352] Selecting client #80 for training.
[INFO][12:09:55]: [Server #27352] Sending the current model to client #80 (simulated).
[INFO][12:09:55]: [Server #27352] Sending 18.75 MB of payload data to client #80 (simulated).
[INFO][12:09:55]: [Server #27352] Selecting client #35 for training.
[INFO][12:09:55]: [Server #27352] Sending the current model to client #35 (simulated).
[INFO][12:09:55]: [Server #27352] Sending 18.75 MB of payload data to client #35 (simulated).
[INFO][12:09:55]: [Server #27352] Selecting client #50 for training.
[INFO][12:09:55]: [Server #27352] Sending the current model to client #50 (simulated).
[INFO][12:09:55]: [Server #27352] Sending 18.75 MB of payload data to client #50 (simulated).
[INFO][12:09:55]: [Server #27352] Selecting client #96 for training.
[INFO][12:09:55]: [Server #27352] Sending the current model to client #96 (simulated).
[INFO][12:09:55]: [Server #27352] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][12:09:55]: [Server #27352] Selecting client #34 for training.
[INFO][12:09:55]: [Server #27352] Sending the current model to client #34 (simulated).
[INFO][12:09:56]: [Server #27352] Sending 18.75 MB of payload data to client #34 (simulated).
[INFO][12:09:56]: [Server #27352] Selecting client #67 for training.
[INFO][12:09:56]: [Server #27352] Sending the current model to client #67 (simulated).
[INFO][12:09:56]: [Server #27352] Sending 18.75 MB of payload data to client #67 (simulated).
[INFO][12:09:56]: [Server #27352] Selecting client #43 for training.
[INFO][12:09:56]: [Server #27352] Sending the current model to client #43 (simulated).
[INFO][12:09:56]: [Server #27352] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][12:09:56]: [Server #27352] Selecting client #4 for training.
[INFO][12:09:56]: [Server #27352] Sending the current model to client #4 (simulated).
[INFO][12:09:57]: [Server #27352] Sending 18.75 MB of payload data to client #4 (simulated).
[INFO][12:09:57]: [Server #27352] Selecting client #60 for training.
[INFO][12:09:57]: [Server #27352] Sending the current model to client #60 (simulated).
[INFO][12:09:58]: [Server #27352] Sending 18.75 MB of payload data to client #60 (simulated).
[INFO][12:12:50]: [Server #27352] Received 18.75 MB of payload data from client #80 (simulated).
[INFO][12:12:52]: [Server #27352] Received 18.75 MB of payload data from client #35 (simulated).
[INFO][12:12:52]: [Server #27352] Received 18.75 MB of payload data from client #72 (simulated).
[INFO][12:12:53]: [Server #27352] Received 18.75 MB of payload data from client #50 (simulated).
[INFO][12:12:53]: [Server #27352] Received 18.75 MB of payload data from client #96 (simulated).
[INFO][12:12:53]: [Server #27352] Received 18.75 MB of payload data from client #34 (simulated).
[INFO][12:12:54]: [Server #27352] Received 18.75 MB of payload data from client #60 (simulated).
[INFO][12:12:54]: [Server #27352] Received 18.75 MB of payload data from client #67 (simulated).
[INFO][12:12:54]: [Server #27352] Received 18.75 MB of payload data from client #4 (simulated).
[INFO][12:12:54]: [Server #27352] Received 18.75 MB of payload data from client #43 (simulated).
[INFO][12:12:54]: [Server #27352] Adding client #2 to the list of clients for aggregation.
[INFO][12:12:54]: [Server #27352] Adding client #5 to the list of clients for aggregation.
[INFO][12:12:54]: [Server #27352] Adding client #97 to the list of clients for aggregation.
[INFO][12:12:54]: [Server #27352] Adding client #55 to the list of clients for aggregation.
[INFO][12:12:54]: [Server #27352] Adding client #31 to the list of clients for aggregation.
[INFO][12:12:54]: [Server #27352] Adding client #6 to the list of clients for aggregation.
[INFO][12:12:54]: [Server #27352] Adding client #32 to the list of clients for aggregation.
[INFO][12:12:54]: [Server #27352] Adding client #61 to the list of clients for aggregation.
[INFO][12:12:54]: [Server #27352] Adding client #75 to the list of clients for aggregation.
[INFO][12:12:54]: [Server #27352] Adding client #63 to the list of clients for aggregation.
[INFO][12:12:54]: [Server #27352] Aggregating 10 clients in total.
[INFO][12:12:54]: [Server #27352] Updated weights have been received.
[INFO][12:12:54]: [Server #27352] Aggregating model weight deltas.
[INFO][12:12:54]: [Server #27352] Finished aggregating updated weights.
[INFO][12:12:54]: [Server #27352] Started model testing.
[INFO][12:13:08]: [Trainer.test] 测试完成 - 准确率: 21.32% (2132/10000)
[INFO][12:13:08]: [93m[1m[Server #27352] Global model accuracy: 21.32%
[0m
[INFO][12:13:08]: get_logged_items 被调用
[INFO][12:13:08]: 从updates获取参与客户端: [2, 5, 97, 55, 31, 6, 32, 61, 75, 63]
[INFO][12:13:08]: 客户端 2 陈旧度: 4 (当前轮次:4, 上次参与:0)
[INFO][12:13:08]: 客户端 5 陈旧度: 4 (当前轮次:4, 上次参与:0)
[INFO][12:13:08]: 客户端 97 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][12:13:08]: 客户端 55 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][12:13:08]: 客户端 31 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][12:13:08]: 客户端 6 陈旧度: 4 (当前轮次:4, 上次参与:0)
[INFO][12:13:08]: 客户端 32 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][12:13:08]: 客户端 61 陈旧度: 2 (当前轮次:4, 上次参与:2)
[INFO][12:13:08]: 客户端 75 陈旧度: 1 (当前轮次:4, 上次参与:3)
[INFO][12:13:08]: 客户端 63 陈旧度: 3 (当前轮次:4, 上次参与:1)
[INFO][12:13:08]: 陈旧度统计 - 参与客户端: [2, 5, 97, 55, 31, 6, 32, 61, 75, 63], 陈旧度: [4, 4, 1, 1, 1, 4, 1, 2, 1, 3]
[INFO][12:13:08]: 平均陈旧度: 2.2, 最大: 4, 最小: 1
[INFO][12:13:08]: 最终logged_items: {'round': 4, 'accuracy': 0.2132, 'accuracy_std': 0, 'elapsed_time': 110.05299496650696, 'processing_time': 0.006015199999978904, 'comm_time': 0, 'round_time': 59.64741789631648, 'comm_overhead': 1874.9709129333496, 'global_accuracy': 0.2132, 'avg_staleness': 2.2, 'max_staleness': 4, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:13:08]: [Server #27352] All client reports have been processed.
[INFO][12:13:08]: [Server #27352] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][12:13:08]: [Server #27352] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_4.pth.
[INFO][12:13:08]: [93m[1m
[Server #27352] Starting round 5/400.[0m
[INFO][12:13:08]: [Server #27352] Selected clients: [81, 92, 14, 25, 90, 41, 17, 47, 73, 61]
[INFO][12:13:08]: [Server #27352] Selecting client #81 for training.
[INFO][12:13:08]: [Server #27352] Sending the current model to client #81 (simulated).
[INFO][12:13:08]: [Server #27352] Sending 18.75 MB of payload data to client #81 (simulated).
[INFO][12:13:08]: [Server #27352] Selecting client #92 for training.
[INFO][12:13:08]: [Server #27352] Sending the current model to client #92 (simulated).
[INFO][12:13:08]: [Server #27352] Sending 18.75 MB of payload data to client #92 (simulated).
[INFO][12:13:08]: [Server #27352] Selecting client #14 for training.
[INFO][12:13:08]: [Server #27352] Sending the current model to client #14 (simulated).
[INFO][12:13:08]: [Server #27352] Sending 18.75 MB of payload data to client #14 (simulated).
[INFO][12:13:08]: [Server #27352] Selecting client #25 for training.
[INFO][12:13:08]: [Server #27352] Sending the current model to client #25 (simulated).
[INFO][12:13:08]: [Server #27352] Sending 18.75 MB of payload data to client #25 (simulated).
[INFO][12:13:08]: [Server #27352] Selecting client #90 for training.
[INFO][12:13:08]: [Server #27352] Sending the current model to client #90 (simulated).
[INFO][12:13:08]: [Server #27352] Sending 18.75 MB of payload data to client #90 (simulated).
[INFO][12:13:08]: [Server #27352] Selecting client #41 for training.
[INFO][12:13:08]: [Server #27352] Sending the current model to client #41 (simulated).
[INFO][12:13:08]: [Server #27352] Sending 18.75 MB of payload data to client #41 (simulated).
[INFO][12:13:08]: [Server #27352] Selecting client #17 for training.
[INFO][12:13:08]: [Server #27352] Sending the current model to client #17 (simulated).
[INFO][12:13:09]: [Server #27352] Sending 18.75 MB of payload data to client #17 (simulated).
[INFO][12:13:09]: [Server #27352] Selecting client #47 for training.
[INFO][12:13:09]: [Server #27352] Sending the current model to client #47 (simulated).
[INFO][12:13:09]: [Server #27352] Sending 18.75 MB of payload data to client #47 (simulated).
[INFO][12:13:09]: [Server #27352] Selecting client #73 for training.
[INFO][12:13:09]: [Server #27352] Sending the current model to client #73 (simulated).
[INFO][12:13:10]: [Server #27352] Sending 18.75 MB of payload data to client #73 (simulated).
[INFO][12:13:10]: [Server #27352] Selecting client #61 for training.
[INFO][12:13:10]: [Server #27352] Sending the current model to client #61 (simulated).
[INFO][12:13:10]: [Server #27352] Sending 18.75 MB of payload data to client #61 (simulated).
[INFO][12:17:55]: [Server #27352] Received 18.75 MB of payload data from client #14 (simulated).
[INFO][12:17:55]: [Server #27352] Received 18.75 MB of payload data from client #92 (simulated).
[INFO][12:17:59]: [Server #27352] Received 18.75 MB of payload data from client #25 (simulated).
[INFO][12:18:03]: [Server #27352] Received 18.75 MB of payload data from client #73 (simulated).
[INFO][12:18:03]: [Server #27352] Received 18.75 MB of payload data from client #47 (simulated).
[INFO][12:18:04]: [Server #27352] Received 18.75 MB of payload data from client #41 (simulated).
[INFO][12:18:04]: [Server #27352] Received 18.75 MB of payload data from client #81 (simulated).
[INFO][12:18:05]: [Server #27352] Received 18.75 MB of payload data from client #17 (simulated).
[INFO][12:18:05]: [Server #27352] Received 18.75 MB of payload data from client #61 (simulated).
[INFO][12:18:05]: [Server #27352] Received 18.75 MB of payload data from client #90 (simulated).
[INFO][12:18:05]: [Server #27352] Adding client #80 to the list of clients for aggregation.
[INFO][12:18:05]: [Server #27352] Adding client #72 to the list of clients for aggregation.
[INFO][12:18:05]: [Server #27352] Adding client #50 to the list of clients for aggregation.
[INFO][12:18:05]: [Server #27352] Adding client #35 to the list of clients for aggregation.
[INFO][12:18:05]: [Server #27352] Adding client #34 to the list of clients for aggregation.
[INFO][12:18:05]: [Server #27352] Adding client #96 to the list of clients for aggregation.
[INFO][12:18:05]: [Server #27352] Adding client #67 to the list of clients for aggregation.
[INFO][12:18:05]: [Server #27352] Adding client #60 to the list of clients for aggregation.
[INFO][12:18:05]: [Server #27352] Adding client #4 to the list of clients for aggregation.
[INFO][12:18:05]: [Server #27352] Adding client #43 to the list of clients for aggregation.
[INFO][12:18:05]: [Server #27352] Aggregating 10 clients in total.
[INFO][12:18:05]: [Server #27352] Updated weights have been received.
[INFO][12:18:05]: [Server #27352] Aggregating model weight deltas.
[INFO][12:18:06]: [Server #27352] Finished aggregating updated weights.
[INFO][12:18:06]: [Server #27352] Started model testing.
[INFO][12:18:51]: [Trainer.test] 测试完成 - 准确率: 16.37% (1637/10000)
[INFO][12:18:51]: [93m[1m[Server #27352] Global model accuracy: 16.37%
[0m
[INFO][12:18:51]: get_logged_items 被调用
[INFO][12:18:51]: 从updates获取参与客户端: [80, 72, 50, 35, 34, 96, 67, 60, 4, 43]
[INFO][12:18:51]: 客户端 80 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:18:51]: 客户端 72 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:18:51]: 客户端 50 陈旧度: 3 (当前轮次:5, 上次参与:2)
[INFO][12:18:51]: 客户端 35 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:18:51]: 客户端 34 陈旧度: 2 (当前轮次:5, 上次参与:3)
[INFO][12:18:51]: 客户端 96 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:18:51]: 客户端 67 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:18:51]: 客户端 60 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:18:51]: 客户端 4 陈旧度: 2 (当前轮次:5, 上次参与:3)
[INFO][12:18:51]: 客户端 43 陈旧度: 1 (当前轮次:5, 上次参与:4)
[INFO][12:18:51]: 陈旧度统计 - 参与客户端: [80, 72, 50, 35, 34, 96, 67, 60, 4, 43], 陈旧度: [1, 1, 3, 1, 2, 1, 1, 1, 2, 1]
[INFO][12:18:51]: 平均陈旧度: 1.4, 最大: 3, 最小: 1
[INFO][12:18:51]: 最终logged_items: {'round': 5, 'accuracy': 0.1637, 'accuracy_std': 0, 'elapsed_time': 163.16349363327026, 'processing_time': 0.008968999999979133, 'comm_time': 0, 'round_time': 60.62583866020509, 'comm_overhead': 2249.9650955200195, 'global_accuracy': 0.1637, 'avg_staleness': 1.4, 'max_staleness': 3, 'min_staleness': 1, 'global_accuracy_std': 0.0}
[INFO][12:18:51]: [Server #27352] All client reports have been processed.
[INFO][12:18:51]: [Server #27352] Saving the checkpoint to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][12:18:51]: [Server #27352] Model saved to ./models/refedscafl/cifar10_alpha01/checkpoint_resnet_9_5.pth.
[INFO][12:18:51]: [93m[1m
[Server #27352] Starting round 6/400.[0m
[INFO][12:18:51]: [Server #27352] Selected clients: [71, 96, 28, 43, 40, 84, 70, 56, 5, 68]
[INFO][12:18:51]: [Server #27352] Selecting client #71 for training.
[INFO][12:18:51]: [Server #27352] Sending the current model to client #71 (simulated).
[INFO][12:18:51]: [Server #27352] Sending 18.75 MB of payload data to client #71 (simulated).
[INFO][12:18:51]: [Server #27352] Selecting client #96 for training.
[INFO][12:18:51]: [Server #27352] Sending the current model to client #96 (simulated).
[INFO][12:18:51]: [Server #27352] Sending 18.75 MB of payload data to client #96 (simulated).
[INFO][12:18:51]: [Server #27352] Selecting client #28 for training.
[INFO][12:18:51]: [Server #27352] Sending the current model to client #28 (simulated).
[INFO][12:18:51]: [Server #27352] Sending 18.75 MB of payload data to client #28 (simulated).
[INFO][12:18:51]: [Server #27352] Selecting client #43 for training.
[INFO][12:18:51]: [Server #27352] Sending the current model to client #43 (simulated).
[INFO][12:18:51]: [Server #27352] Sending 18.75 MB of payload data to client #43 (simulated).
[INFO][12:18:51]: [Server #27352] Selecting client #40 for training.
[INFO][12:18:51]: [Server #27352] Sending the current model to client #40 (simulated).
[INFO][12:18:51]: [Server #27352] Sending 18.75 MB of payload data to client #40 (simulated).
[INFO][12:18:51]: [Server #27352] Selecting client #84 for training.
[INFO][12:18:51]: [Server #27352] Sending the current model to client #84 (simulated).
[INFO][12:18:51]: [Server #27352] Sending 18.75 MB of payload data to client #84 (simulated).
[INFO][12:18:51]: [Server #27352] Selecting client #70 for training.
[INFO][12:18:51]: [Server #27352] Sending the current model to client #70 (simulated).
[INFO][12:18:51]: [Server #27352] Sending 18.75 MB of payload data to client #70 (simulated).
[INFO][12:18:51]: [Server #27352] Selecting client #56 for training.
[INFO][12:18:51]: [Server #27352] Sending the current model to client #56 (simulated).
[INFO][12:18:52]: [Server #27352] Sending 18.75 MB of payload data to client #56 (simulated).
[INFO][12:18:52]: [Server #27352] Selecting client #5 for training.
[INFO][12:18:52]: [Server #27352] Sending the current model to client #5 (simulated).
[INFO][12:18:52]: [Server #27352] Sending 18.75 MB of payload data to client #5 (simulated).
[INFO][12:18:52]: [Server #27352] Selecting client #68 for training.
[INFO][12:18:52]: [Server #27352] Sending the current model to client #68 (simulated).
[INFO][12:18:53]: [Server #27352] Sending 18.75 MB of payload data to client #68 (simulated).
[INFO][12:21:43]: [Server #27352] An existing client just disconnected.
[WARNING][12:21:43]: [Server #27352] Client process #17796 disconnected and removed from this server, 9 client processes are remaining.
[WARNING][12:21:43]: [93m[1m[Server #27352] Closing the server due to a failed client.[0m
[INFO][12:21:43]: [Server #27352] Training concluded.
[INFO][12:21:43]: [Server #27352] Model saved to ./models/refedscafl/cifar10_alpha01/resnet_9.pth.
[INFO][12:21:43]: [Server #27352] Closing the server.
[INFO][12:21:43]: Closing the connection to client #33968.
[INFO][12:21:43]: Closing the connection to client #36408.
[INFO][12:21:43]: Closing the connection to client #16884.
[INFO][12:21:43]: Closing the connection to client #28468.
[INFO][12:21:43]: Closing the connection to client #8936.
[INFO][12:21:43]: Closing the connection to client #40176.
[INFO][12:21:43]: Closing the connection to client #5884.
[INFO][12:21:43]: Closing the connection to client #24064.
[INFO][12:21:43]: Closing the connection to client #39672.
