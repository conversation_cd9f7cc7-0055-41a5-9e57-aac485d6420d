"""
A basic federated learning client who sends weight updates to the server.
"""

import logging
import time
import copy
from types import SimpleNamespace

from plato.algorithms import registry as algorithms_registry
from plato.clients import base
from plato.config import Config
from plato.datasources import registry as datasources_registry
from plato.processors import registry as processor_registry
from plato.samplers import registry as samplers_registry
from plato.trainers import registry as trainers_registry
from plato.utils import fonts


class Client(base.Client):
    """A basic federated learning client who sends simple weight updates."""

    def __init__(
        self,
        model=None,
        datasource=None,
        algorithm=None,
        trainer=None,
        callbacks=None,
        trainer_callbacks=None,
    ):
        super().__init__(callbacks=callbacks)
        # Save the callbacks that will be passed to trainer later
        self.trainer_callbacks = trainer_callbacks

        self.custom_model = model
        self.model = None

        self.custom_datasource = datasource
        self.datasource = None

        self.custom_algorithm = algorithm
        self.algorithm = None

        self.custom_trainer = trainer
        self.trainer = None

        self.trainset = None  # Training dataset
        self.testset = None  # Testing dataset
        self.sampler = None
        self.testset_sampler = None  # Sampler for the test set

        self._report = None

    def configure(self) -> None:
        """Prepares this client for training."""
        super().configure()

        if self.model is None and self.custom_model is not None:
            self.model = self.custom_model

        # 注册trainer（用户/服务器），指定模型、数据集loader、损失函数
        # 父类为plato\trainers\base.py，但是实现大部分在plato\trainers\basic.py
        if self.trainer is None and self.custom_trainer is None:
            # 先调用plato\trainers\registry.py
            self.trainer = trainers_registry.get(
                model=self.model, callbacks=self.trainer_callbacks
            )
        elif self.trainer is None and self.custom_trainer is not None:
            self.trainer = self.custom_trainer(
                model=self.model, callbacks=self.trainer_callbacks
            )

        self.trainer.set_client_id(self.client_id)

        # 注册算法，服务器上的聚合算法
        # 父类为plato\algorithms\base.py->fedavg.py，可以继承父类算法，实现自己的聚合算法
        if self.algorithm is None and self.custom_algorithm is None:
            # 先调用plato\algorithms\registry.py
            self.algorithm = algorithms_registry.get(trainer=self.trainer)
        elif self.algorithm is None and self.custom_algorithm is not None:
            self.algorithm = self.custom_algorithm(trainer=self.trainer)

        self.algorithm.set_client_id(self.client_id)

        # Pass inbound and outbound data payloads through processors for
        # additional data processing 在接收模型之后，以及更新完发送模型之前对模型的操作
        self.outbound_processor, self.inbound_processor = processor_registry.get(
            "Client", client_id=self.client_id, trainer=self.trainer
        )

        # Setting up the data sampler
        # get the dataset name
        datasource_name = Config().data.datasource
        if datasource_name in {"Shakespeare", "FEMNIST"}:
            logging.info("[%s] The shakespeare or femnist dataset have been split.", self)

            self.sampler = None
            if (
                    hasattr(Config().clients, "do_test")
                    and Config().clients.do_test
                    and hasattr(Config().data, "testset_sampler")
            ):
                self.testset_sampler = None

        elif self.datasource:
            # 注册训练/测试数据集的划分方法（iid/non-IID）
            # sampler只能得到采样的数据的索引
            self.sampler = samplers_registry.get(self.datasource, self.client_id)

            if (
                hasattr(Config().clients, "do_test")
                and Config().clients.do_test
                and hasattr(Config().data, "testset_sampler")
            ):
                # Set the sampler for test set
                self.testset_sampler = samplers_registry.get(
                    self.datasource, self.client_id, testing=True
                )

    def _load_data(self) -> None:
        """Generates data and loads them onto this client."""
        # The only case where Config().data.reload_data is set to true is
        # when clients with different client IDs need to load from different datasets,
        # such as in the pre-partitioned Federated EMNIST dataset. We do not support
        # reloading data from a custom datasource at this time.
        # 如果数据集为空，则加载数据集
        if (
            self.datasource is None
            or hasattr(Config().data, "reload_data")
            and Config().data.reload_data
        ):
            logging.info("[%s] Loading its data source.", self)

            if self.custom_datasource is None:
                self.datasource = datasources_registry.get(client_id=self.client_id)
                # logging.info("[%s] client id: %d, custom_datasource is none", self, self.client_id)
            elif self.custom_datasource is not None:
                self.datasource = self.custom_datasource()

            # if shakespeare or femnist dataset is used, the dataset size is the number of client data
            # otherwise, the dataset size is the number of all the dataset
            logging.info(
                "[%s] Dataset size: %s", self, self.datasource.num_train_examples()
            )

    def _allocate_data(self) -> None:
        """Allocate training or testing dataset of this client."""
        # 只加载数据集
        if hasattr(Config().trainer, "use_mindspore"):
            # MindSpore requires samplers to be used while constructing
            # the dataset
            self.trainset = self.datasource.get_train_set(self.sampler)
        else:
            # PyTorch uses samplers when loading data with a data loader
            self.trainset = self.datasource.get_train_set()

        if hasattr(Config().clients, "do_test") and Config().clients.do_test:
            # Set the testset if local testing is needed
            self.testset = self.datasource.get_test_set()

    def _load_payload(self, server_payload) -> None:
        """Loads the server model onto this client."""
        self.algorithm.load_weights(server_payload)

    async def _train(self):
        """The machine learning training workload on a client.
        复写了父类中的train函数"""
        # 提取初始加载的全局模型
        init_global_model = copy.deepcopy(self.algorithm.extract_weights())

        logging.info(
            fonts.colourize(
                f"[{self}] Started training in communication round #{self.current_round}."
            )
        )

        # test global model on local dataset
        if (hasattr(Config().clients, "do_global_test") and Config().clients.do_global_test):
            global_accuracy = self.trainer.test(self.testset, self.testset_sampler)
            logging.info("[client %s] Test global accuracy: %.2f%%", self, 100 * global_accuracy)

            if global_accuracy == -1:
                # The testing process failed, disconnect from the server
                logging.info(
                    fonts.colourize(
                        f"[{self}] Global accuracy on local data is -1 when testing. Disconnecting from the server."
                    )
                )
                await self.sio.disconnect()
        else:
            global_accuracy = 0

        # Perform model training
        try:
            if hasattr(self.trainer, "current_round"):
                self.trainer.current_round = self.current_round
            training_time = self.trainer.train(self.trainset, self.sampler)

        except ValueError as exc:
            logging.info(
                fonts.colourize(f"[{self}] Error occurred during training: {exc}")
            )
            await self.sio.disconnect()

        # Extract model weights and biases
        weights = self.algorithm.extract_weights()
        deltas = self.algorithm.compute_weight_deltas(init_global_model, [weights])[0]

        # Generate a report for the server, performing model testing if applicable
        if (hasattr(Config().clients, "do_test") and Config().clients.do_test) and (
            not hasattr(Config().clients, "test_interval")
            or self.current_round % Config().clients.test_interval == 0
        ):
            accuracy = self.trainer.test(self.testset, self.testset_sampler)

            if accuracy == -1:
                # The testing process failed, disconnect from the server
                logging.info(
                    fonts.colourize(
                        f"[{self}] Accuracy is -1 when testing. Disconnecting from the server."
                    )
                )
                await self.sio.disconnect()

            if hasattr(Config().trainer, "target_perplexity"):
                logging.info("[%s] Test perplexity: %.2f", self, accuracy)
            else:
                logging.info("[%s] Test accuracy: %.2f%%", self, 100 * accuracy)
        else:
            accuracy = 0

        comm_time = time.time()

        if (
            hasattr(Config().clients, "sleep_simulation")
            and Config().clients.sleep_simulation
        ):
            sleep_seconds = Config().client_sleep_times[self.client_id - 1]
            avg_training_time = Config().clients.avg_training_time

            training_time = (
                avg_training_time + sleep_seconds
            ) * Config().trainer.epochs

        if self.sampler is None:
            num_samples = self.datasource.num_train_examples()
        else:
            num_samples = self.sampler.num_samples()

        report = SimpleNamespace(
            client_id=self.client_id,
            num_samples=num_samples,
            accuracy=accuracy,
            global_accuracy=global_accuracy,
            training_time=training_time,
            comm_time=comm_time,
            update_response=False,
            deltas=deltas,
        )

        self._report = self.customize_report(report)

        return self._report, weights

    async def _obtain_model_update(self, client_id, requested_time):
        """Retrieves a model update corresponding to a particular wall clock time."""
        model = self.trainer.obtain_model_update(client_id, requested_time)
        weights = self.algorithm.extract_weights(model)
        self._report.comm_time = time.time()
        self._report.client_id = client_id
        self._report.update_response = True

        return self._report, weights

    def customize_report(self, report: SimpleNamespace) -> SimpleNamespace:
        """Customizes the report with any additional information."""
        return report
