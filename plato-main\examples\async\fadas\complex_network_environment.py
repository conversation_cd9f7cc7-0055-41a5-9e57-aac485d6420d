"""
复杂网络环境模拟器 - 专门为FedADS设计
模拟真实的复杂网络条件，测试FedADS在恶劣环境下的表现
"""

import numpy as np
import random
import json
import time
from collections import deque, defaultdict
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
import logging

@dataclass
class ClientNetworkProfile:
    """客户端网络配置文件"""
    client_id: int
    device_type: str  # 'mobile', 'edge', 'iot', 'desktop'
    base_latency_ms: float  # 基础延迟
    bandwidth_mbps: float  # 带宽
    reliability: float  # 可靠性 (0-1)
    mobility: str  # 'static', 'low', 'medium', 'high'
    battery_dependent: bool  # 是否依赖电池
    location_zone: str  # 'urban', 'suburban', 'rural', 'remote'

class ComplexNetworkEnvironment:
    """复杂网络环境模拟器 - 专门挑战FedADS"""
    
    def __init__(self, num_clients=100, config_file=None):
        self.num_clients = num_clients
        self.current_round = 0
        self.current_time = 0.0
        
        # 网络环境状态
        self.global_network_condition = 'normal'  # 'normal', 'congested', 'unstable', 'disaster'
        self.weather_impact = 0.0  # 0-1, 天气对网络的影响
        self.peak_hour_factor = 1.0  # 高峰时段影响因子
        
        # 客户端网络配置
        self.client_profiles = self._create_client_profiles()
        
        # 网络事件历史
        self.network_events = deque(maxlen=1000)
        self.client_communication_history = defaultdict(lambda: deque(maxlen=50))
        
        # 统计信息
        self.stats = {
            'total_communications': 0,
            'successful_communications': 0,
            'failed_communications': 0,
            'timeout_communications': 0,
            'avg_latency': 0.0,
            'network_condition_changes': 0
        }
        
        logging.info(f"🌐 复杂网络环境初始化完成 - {num_clients}个客户端")
        self._log_client_distribution()
    
    def _create_client_profiles(self) -> List[ClientNetworkProfile]:
        """创建客户端网络配置文件 - 模拟真实的异构环境"""
        profiles = []
        
        # 设备类型分布 (基于真实统计)
        device_distribution = {
            'mobile': 0.60,    # 60% 移动设备
            'desktop': 0.25,   # 25% 桌面设备  
            'edge': 0.10,      # 10% 边缘设备
            'iot': 0.05        # 5% IoT设备
        }
        
        # 地理位置分布
        location_distribution = {
            'urban': 0.40,     # 40% 城市
            'suburban': 0.35,  # 35% 郊区
            'rural': 0.20,     # 20% 农村
            'remote': 0.05     # 5% 偏远地区
        }
        
        # 设备类型配置
        device_configs = {
            'mobile': {
                'base_latency_range': (50, 200),
                'bandwidth_range': (5, 50),
                'reliability_range': (0.6, 0.85),
                'mobility_options': ['low', 'medium', 'high'],
                'battery_dependent': True
            },
            'desktop': {
                'base_latency_range': (10, 50),
                'bandwidth_range': (50, 200),
                'reliability_range': (0.85, 0.95),
                'mobility_options': ['static'],
                'battery_dependent': False
            },
            'edge': {
                'base_latency_range': (5, 30),
                'bandwidth_range': (100, 500),
                'reliability_range': (0.90, 0.98),
                'mobility_options': ['static', 'low'],
                'battery_dependent': False
            },
            'iot': {
                'base_latency_range': (100, 500),
                'bandwidth_range': (1, 10),
                'reliability_range': (0.4, 0.7),
                'mobility_options': ['static', 'low'],
                'battery_dependent': True
            }
        }
        
        # 地理位置对网络的影响
        location_factors = {
            'urban': {'latency_factor': 1.0, 'bandwidth_factor': 1.0, 'reliability_factor': 1.0},
            'suburban': {'latency_factor': 1.2, 'bandwidth_factor': 0.8, 'reliability_factor': 0.9},
            'rural': {'latency_factor': 1.8, 'bandwidth_factor': 0.5, 'reliability_factor': 0.7},
            'remote': {'latency_factor': 3.0, 'bandwidth_factor': 0.2, 'reliability_factor': 0.5}
        }
        
        for client_id in range(self.num_clients):
            # 随机选择设备类型和位置
            device_type = np.random.choice(
                list(device_distribution.keys()),
                p=list(device_distribution.values())
            )
            location_zone = np.random.choice(
                list(location_distribution.keys()),
                p=list(location_distribution.values())
            )
            
            config = device_configs[device_type]
            location_factor = location_factors[location_zone]
            
            # 生成基础网络参数
            base_latency = np.random.uniform(*config['base_latency_range'])
            bandwidth = np.random.uniform(*config['bandwidth_range'])
            reliability = np.random.uniform(*config['reliability_range'])
            
            # 应用地理位置影响
            base_latency *= location_factor['latency_factor']
            bandwidth *= location_factor['bandwidth_factor']
            reliability *= location_factor['reliability_factor']
            
            # 添加一些极端情况 (5%的客户端有极差的网络条件)
            if np.random.random() < 0.05:
                base_latency *= np.random.uniform(2, 5)
                bandwidth *= np.random.uniform(0.1, 0.3)
                reliability *= np.random.uniform(0.3, 0.6)
            
            profile = ClientNetworkProfile(
                client_id=client_id,
                device_type=device_type,
                base_latency_ms=base_latency,
                bandwidth_mbps=bandwidth,
                reliability=max(0.1, min(0.99, reliability)),
                mobility=np.random.choice(config['mobility_options']),
                battery_dependent=config['battery_dependent'],
                location_zone=location_zone
            )
            
            profiles.append(profile)
        
        return profiles
    
    def _log_client_distribution(self):
        """记录客户端分布信息"""
        device_counts = defaultdict(int)
        location_counts = defaultdict(int)
        
        for profile in self.client_profiles:
            device_counts[profile.device_type] += 1
            location_counts[profile.location_zone] += 1
        
        logging.info("📱 设备类型分布:")
        for device_type, count in device_counts.items():
            logging.info(f"   {device_type}: {count} ({count/self.num_clients:.1%})")
        
        logging.info("🌍 地理位置分布:")
        for location, count in location_counts.items():
            logging.info(f"   {location}: {count} ({count/self.num_clients:.1%})")
    
    def update_global_conditions(self):
        """更新全局网络条件"""
        self.current_round += 1
        self.current_time += 1.0
        
        # 模拟网络条件变化
        self._update_network_condition()
        self._update_weather_impact()
        self._update_peak_hour_factor()
        
        # 记录网络事件
        event = {
            'round': self.current_round,
            'time': self.current_time,
            'network_condition': self.global_network_condition,
            'weather_impact': self.weather_impact,
            'peak_hour_factor': self.peak_hour_factor
        }
        self.network_events.append(event)
    
    def _update_network_condition(self):
        """更新全局网络状态"""
        # 网络状态转换概率矩阵
        transition_probs = {
            'normal': {'normal': 0.85, 'congested': 0.10, 'unstable': 0.04, 'disaster': 0.01},
            'congested': {'normal': 0.30, 'congested': 0.60, 'unstable': 0.08, 'disaster': 0.02},
            'unstable': {'normal': 0.20, 'congested': 0.25, 'unstable': 0.50, 'disaster': 0.05},
            'disaster': {'normal': 0.05, 'congested': 0.10, 'unstable': 0.25, 'disaster': 0.60}
        }
        
        current_probs = transition_probs[self.global_network_condition]
        new_condition = np.random.choice(
            list(current_probs.keys()),
            p=list(current_probs.values())
        )
        
        if new_condition != self.global_network_condition:
            self.stats['network_condition_changes'] += 1
            logging.info(f"🌐 网络状态变化: {self.global_network_condition} -> {new_condition}")
        
        self.global_network_condition = new_condition
    
    def _update_weather_impact(self):
        """更新天气影响"""
        # 天气影响随机变化
        if np.random.random() < 0.1:  # 10%概率天气变化
            self.weather_impact = np.random.uniform(0, 0.8)
    
    def _update_peak_hour_factor(self):
        """更新高峰时段影响"""
        hour = (self.current_time / 3600) % 24
        
        # 模拟一天中的网络使用模式
        if 8 <= hour <= 10 or 18 <= hour <= 22:  # 高峰时段
            self.peak_hour_factor = np.random.uniform(1.5, 2.5)
        elif 0 <= hour <= 6:  # 深夜低峰
            self.peak_hour_factor = np.random.uniform(0.5, 0.8)
        else:  # 正常时段
            self.peak_hour_factor = np.random.uniform(0.9, 1.3)
    
    def simulate_client_communication(self, client_id: int, data_size_mb: float = 1.0) -> Tuple[bool, float, Dict]:
        """模拟客户端通信过程"""
        profile = self.client_profiles[client_id]
        
        # 计算实际网络参数
        actual_latency = self._calculate_actual_latency(profile)
        actual_bandwidth = self._calculate_actual_bandwidth(profile)
        actual_reliability = self._calculate_actual_reliability(profile)
        
        # 计算传输时间
        transmission_time = (data_size_mb * 8) / actual_bandwidth  # 秒
        total_latency = actual_latency / 1000  # 转换为秒
        
        # 模拟网络拥塞和重传
        congestion_factor = self._get_congestion_factor()
        retransmission_factor = 1.0 / max(0.1, actual_reliability)
        
        total_communication_time = (transmission_time + total_latency) * congestion_factor * retransmission_factor
        
        # 判断通信是否成功
        success_probability = actual_reliability * self._get_success_probability_modifier(profile)
        is_successful = np.random.random() < success_probability
        
        # 如果失败，添加超时时间
        if not is_successful:
            timeout_penalty = np.random.uniform(10, 30)  # 10-30秒超时
            total_communication_time += timeout_penalty
            self.stats['failed_communications'] += 1
            
            # 特别长的超时 (模拟严重网络问题)
            if np.random.random() < 0.1:  # 10%概率严重超时
                total_communication_time += np.random.uniform(60, 300)  # 1-5分钟
                self.stats['timeout_communications'] += 1
        else:
            self.stats['successful_communications'] += 1
        
        self.stats['total_communications'] += 1
        
        # 更新平均延迟统计
        self.stats['avg_latency'] = (
            self.stats['avg_latency'] * (self.stats['total_communications'] - 1) + 
            total_communication_time
        ) / self.stats['total_communications']
        
        # 记录通信历史
        comm_record = {
            'round': self.current_round,
            'success': is_successful,
            'time': total_communication_time,
            'latency': actual_latency,
            'bandwidth': actual_bandwidth,
            'reliability': actual_reliability
        }
        self.client_communication_history[client_id].append(comm_record)
        
        # 详细信息
        details = {
            'client_id': client_id,
            'device_type': profile.device_type,
            'location_zone': profile.location_zone,
            'base_latency': profile.base_latency_ms,
            'actual_latency': actual_latency,
            'actual_bandwidth': actual_bandwidth,
            'actual_reliability': actual_reliability,
            'network_condition': self.global_network_condition,
            'weather_impact': self.weather_impact,
            'peak_hour_factor': self.peak_hour_factor,
            'congestion_factor': congestion_factor,
            'retransmission_factor': retransmission_factor,
            'success_probability': success_probability
        }
        
        return is_successful, total_communication_time, details
    
    def _calculate_actual_latency(self, profile: ClientNetworkProfile) -> float:
        """计算实际延迟"""
        base_latency = profile.base_latency_ms
        
        # 全局网络条件影响
        condition_factors = {
            'normal': 1.0,
            'congested': 1.8,
            'unstable': 2.5,
            'disaster': 5.0
        }
        
        # 移动性影响
        mobility_factors = {
            'static': 1.0,
            'low': 1.1,
            'medium': 1.3,
            'high': 1.8
        }
        
        # 计算最终延迟
        actual_latency = (base_latency * 
                         condition_factors[self.global_network_condition] *
                         mobility_factors[profile.mobility] *
                         (1.0 + self.weather_impact) *
                         self.peak_hour_factor)
        
        # 添加随机抖动
        jitter = np.random.uniform(0.8, 1.5)
        actual_latency *= jitter
        
        return actual_latency
    
    def _calculate_actual_bandwidth(self, profile: ClientNetworkProfile) -> float:
        """计算实际带宽"""
        base_bandwidth = profile.bandwidth_mbps
        
        # 全局网络条件影响
        condition_factors = {
            'normal': 1.0,
            'congested': 0.4,
            'unstable': 0.3,
            'disaster': 0.1
        }
        
        # 移动性影响 (移动设备带宽不稳定)
        mobility_factors = {
            'static': 1.0,
            'low': 0.9,
            'medium': 0.7,
            'high': 0.5
        }
        
        actual_bandwidth = (base_bandwidth * 
                           condition_factors[self.global_network_condition] *
                           mobility_factors[profile.mobility] *
                           (1.0 - self.weather_impact * 0.5) /
                           self.peak_hour_factor)
        
        # 确保最小带宽
        actual_bandwidth = max(0.1, actual_bandwidth)
        
        return actual_bandwidth
    
    def _calculate_actual_reliability(self, profile: ClientNetworkProfile) -> float:
        """计算实际可靠性"""
        base_reliability = profile.reliability
        
        # 全局网络条件影响
        condition_factors = {
            'normal': 1.0,
            'congested': 0.8,
            'unstable': 0.6,
            'disaster': 0.3
        }
        
        # 移动性影响
        mobility_factors = {
            'static': 1.0,
            'low': 0.95,
            'medium': 0.85,
            'high': 0.7
        }
        
        actual_reliability = (base_reliability * 
                             condition_factors[self.global_network_condition] *
                             mobility_factors[profile.mobility] *
                             (1.0 - self.weather_impact * 0.3))
        
        # 电池依赖设备的额外影响
        if profile.battery_dependent:
            battery_factor = np.random.uniform(0.8, 1.0)  # 电池状态影响
            actual_reliability *= battery_factor
        
        return max(0.05, min(0.99, actual_reliability))
    
    def _get_congestion_factor(self) -> float:
        """获取网络拥塞因子"""
        base_congestion = {
            'normal': 1.0,
            'congested': 2.0,
            'unstable': 3.0,
            'disaster': 5.0
        }[self.global_network_condition]
        
        return base_congestion * self.peak_hour_factor * (1.0 + self.weather_impact)
    
    def _get_success_probability_modifier(self, profile: ClientNetworkProfile) -> float:
        """获取成功概率修正因子"""
        modifier = 1.0
        
        # IoT设备更容易失败
        if profile.device_type == 'iot':
            modifier *= 0.8
        
        # 偏远地区更容易失败
        if profile.location_zone == 'remote':
            modifier *= 0.7
        elif profile.location_zone == 'rural':
            modifier *= 0.85
        
        # 高移动性设备更容易失败
        if profile.mobility == 'high':
            modifier *= 0.8
        elif profile.mobility == 'medium':
            modifier *= 0.9
        
        return modifier
    
    def get_network_statistics(self) -> Dict:
        """获取网络统计信息"""
        if self.stats['total_communications'] == 0:
            success_rate = 0.0
        else:
            success_rate = self.stats['successful_communications'] / self.stats['total_communications']
        
        return {
            'current_round': self.current_round,
            'global_network_condition': self.global_network_condition,
            'weather_impact': self.weather_impact,
            'peak_hour_factor': self.peak_hour_factor,
            'total_communications': self.stats['total_communications'],
            'success_rate': success_rate,
            'avg_communication_time': self.stats['avg_latency'],
            'network_condition_changes': self.stats['network_condition_changes'],
            'timeout_rate': self.stats['timeout_communications'] / max(1, self.stats['total_communications'])
        }
    
    def get_client_staleness(self, client_id: int, communication_time: float) -> int:
        """根据通信时间计算客户端的staleness (FedADS需要)"""
        # 简单的staleness计算：通信时间越长，staleness越高
        if communication_time <= 1.0:
            return 1
        elif communication_time <= 5.0:
            return 2
        elif communication_time <= 15.0:
            return 3
        elif communication_time <= 30.0:
            return 4
        elif communication_time <= 60.0:
            return 5
        else:
            return min(10, int(communication_time / 10))  # 最大staleness为10
    
    def save_statistics(self, filename: str):
        """保存统计信息到文件"""
        stats = self.get_network_statistics()
        
        # 添加客户端详细信息
        client_details = []
        for profile in self.client_profiles:
            client_details.append({
                'client_id': profile.client_id,
                'device_type': profile.device_type,
                'location_zone': profile.location_zone,
                'base_latency_ms': profile.base_latency_ms,
                'bandwidth_mbps': profile.bandwidth_mbps,
                'reliability': profile.reliability,
                'mobility': profile.mobility
            })
        
        full_stats = {
            'network_statistics': stats,
            'client_profiles': client_details,
            'network_events': list(self.network_events)
        }
        
        with open(filename, 'w') as f:
            json.dump(full_stats, f, indent=2, default=str)
        
        logging.info(f"📊 网络统计信息已保存到: {filename}")

def test_complex_network_environment():
    """测试复杂网络环境"""
    print("🧪 测试复杂网络环境模拟器")
    print("="*50)
    
    # 创建网络环境
    env = ComplexNetworkEnvironment(num_clients=20)
    
    # 模拟多轮通信
    for round_num in range(10):
        print(f"\n📡 第 {round_num+1} 轮通信测试")
        
        env.update_global_conditions()
        
        round_stats = {
            'successful': 0,
            'failed': 0,
            'total_time': 0,
            'max_time': 0,
            'min_time': float('inf')
        }
        
        # 模拟部分客户端通信 (类似FedADS的异步选择)
        selected_clients = np.random.choice(env.num_clients, size=5, replace=False)
        
        for client_id in selected_clients:
            success, comm_time, details = env.simulate_client_communication(
                client_id, data_size_mb=np.random.uniform(0.5, 2.0)
            )
            
            if success:
                round_stats['successful'] += 1
            else:
                round_stats['failed'] += 1
            
            round_stats['total_time'] += comm_time
            round_stats['max_time'] = max(round_stats['max_time'], comm_time)
            round_stats['min_time'] = min(round_stats['min_time'], comm_time)
        
        success_rate = round_stats['successful'] / len(selected_clients)
        avg_time = round_stats['total_time'] / len(selected_clients)
        
        print(f"   成功率: {success_rate:.2%}")
        print(f"   平均通信时间: {avg_time:.2f}秒")
        print(f"   通信时间范围: {round_stats['min_time']:.2f} - {round_stats['max_time']:.2f}秒")
        print(f"   网络状态: {env.global_network_condition}")
    
    # 输出最终统计
    final_stats = env.get_network_statistics()
    print(f"\n📊 最终统计:")
    print(f"   总体成功率: {final_stats['success_rate']:.2%}")
    print(f"   平均通信时间: {final_stats['avg_communication_time']:.2f}秒")
    print(f"   网络状态变化次数: {final_stats['network_condition_changes']}")
    print(f"   超时率: {final_stats['timeout_rate']:.2%}")
    
    # 保存统计信息
    env.save_statistics('fadas_network_test_results.json')
    
    return env

def create_fadas_challenging_config(base_config_path: str, output_path: str, network_env: ComplexNetworkEnvironment):
    """为FedADS创建挑战性的配置文件"""
    import yaml

    # 读取基础配置
    with open(base_config_path, 'r') as f:
        config = yaml.safe_load(f)

    # 修改配置以适应复杂网络环境
    config['clients']['total_clients'] = network_env.num_clients
    config['clients']['per_round'] = min(10, network_env.num_clients // 5)  # 减少每轮客户端数量

    # 增加延迟容忍度
    config['server']['staleness_bound'] = 10  # 增加staleness bound
    config['server']['minimum_clients_aggregated'] = max(3, config['clients']['per_round'] // 3)

    # 调整FedADS参数以应对复杂网络
    config['server']['tauc'] = 3  # 增加延迟阈值
    config['server']['global_lr'] = 0.005  # 降低学习率以提高稳定性

    # 减少训练轮数以便快速测试
    config['trainer']['rounds'] = 20
    config['trainer']['max_concurrency'] = 5  # 限制并发数

    # 保存修改后的配置
    with open(output_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)

    print(f"📝 已创建FedADS挑战性配置文件: {output_path}")
    return output_path

class FedADSNetworkIntegration:
    """FedADS与复杂网络环境的集成"""

    def __init__(self, network_env: ComplexNetworkEnvironment):
        self.network_env = network_env
        self.communication_logs = []

    def simulate_client_training_and_communication(self, client_id: int, model_size_mb: float = 5.0):
        """模拟客户端训练和通信过程"""
        # 模拟本地训练时间 (基于设备类型)
        profile = self.network_env.client_profiles[client_id]

        training_time_base = {
            'mobile': (10, 30),      # 移动设备训练慢
            'desktop': (3, 8),       # 桌面设备训练快
            'edge': (2, 5),          # 边缘设备训练很快
            'iot': (30, 120)         # IoT设备训练很慢
        }

        training_time = np.random.uniform(*training_time_base[profile.device_type])

        # 模拟通信过程
        success, comm_time, details = self.network_env.simulate_client_communication(
            client_id, model_size_mb
        )

        # 计算staleness (FedADS需要)
        staleness = self.network_env.get_client_staleness(client_id, comm_time)

        # 记录通信日志
        log_entry = {
            'client_id': client_id,
            'training_time': training_time,
            'communication_time': comm_time,
            'communication_success': success,
            'staleness': staleness,
            'device_type': profile.device_type,
            'location_zone': profile.location_zone,
            'network_condition': self.network_env.global_network_condition,
            'round': self.network_env.current_round
        }
        self.communication_logs.append(log_entry)

        return {
            'success': success,
            'training_time': training_time,
            'communication_time': comm_time,
            'staleness': staleness,
            'total_time': training_time + comm_time
        }

    def get_communication_statistics(self) -> Dict:
        """获取通信统计信息"""
        if not self.communication_logs:
            return {}

        successful_comms = [log for log in self.communication_logs if log['communication_success']]
        failed_comms = [log for log in self.communication_logs if not log['communication_success']]

        stats = {
            'total_communications': len(self.communication_logs),
            'successful_communications': len(successful_comms),
            'failed_communications': len(failed_comms),
            'success_rate': len(successful_comms) / len(self.communication_logs),
            'avg_communication_time': np.mean([log['communication_time'] for log in self.communication_logs]),
            'avg_staleness': np.mean([log['staleness'] for log in self.communication_logs]),
            'max_staleness': max([log['staleness'] for log in self.communication_logs]),
            'device_type_success_rates': {}
        }

        # 按设备类型统计成功率
        for device_type in ['mobile', 'desktop', 'edge', 'iot']:
            device_logs = [log for log in self.communication_logs if log['device_type'] == device_type]
            if device_logs:
                device_success = [log for log in device_logs if log['communication_success']]
                stats['device_type_success_rates'][device_type] = len(device_success) / len(device_logs)

        return stats

def run_fadas_stress_test():
    """运行FedADS压力测试"""
    print("🚀 FedADS复杂网络环境压力测试")
    print("="*60)

    # 创建复杂网络环境
    network_env = ComplexNetworkEnvironment(num_clients=50)

    # 创建FedADS集成
    fadas_integration = FedADSNetworkIntegration(network_env)

    # 模拟多轮FedADS训练
    results = {
        'rounds': [],
        'overall_stats': {}
    }

    for round_num in range(15):
        print(f"\n📡 第 {round_num+1} 轮 FedADS 训练")

        # 更新网络环境
        network_env.update_global_conditions()

        # 选择客户端 (模拟FedADS的异步选择)
        num_selected = np.random.randint(5, 15)  # 随机选择5-15个客户端
        selected_clients = np.random.choice(network_env.num_clients, size=num_selected, replace=False)

        round_results = {
            'round': round_num + 1,
            'selected_clients': len(selected_clients),
            'successful_clients': 0,
            'failed_clients': 0,
            'total_training_time': 0,
            'total_communication_time': 0,
            'staleness_values': [],
            'network_condition': network_env.global_network_condition
        }

        # 模拟每个选中客户端的训练和通信
        for client_id in selected_clients:
            result = fadas_integration.simulate_client_training_and_communication(
                client_id, model_size_mb=np.random.uniform(3, 8)  # 模型大小变化
            )

            if result['success']:
                round_results['successful_clients'] += 1
            else:
                round_results['failed_clients'] += 1

            round_results['total_training_time'] += result['training_time']
            round_results['total_communication_time'] += result['communication_time']
            round_results['staleness_values'].append(result['staleness'])

        # 计算轮次统计
        round_results['success_rate'] = round_results['successful_clients'] / len(selected_clients)
        round_results['avg_training_time'] = round_results['total_training_time'] / len(selected_clients)
        round_results['avg_communication_time'] = round_results['total_communication_time'] / len(selected_clients)
        round_results['avg_staleness'] = np.mean(round_results['staleness_values'])
        round_results['max_staleness'] = max(round_results['staleness_values'])

        results['rounds'].append(round_results)

        print(f"   选中客户端: {len(selected_clients)}")
        print(f"   成功率: {round_results['success_rate']:.2%}")
        print(f"   平均通信时间: {round_results['avg_communication_time']:.2f}秒")
        print(f"   平均staleness: {round_results['avg_staleness']:.1f}")
        print(f"   最大staleness: {round_results['max_staleness']}")
        print(f"   网络状态: {round_results['network_condition']}")

    # 生成总体统计
    comm_stats = fadas_integration.get_communication_statistics()
    network_stats = network_env.get_network_statistics()

    results['overall_stats'] = {
        'communication_stats': comm_stats,
        'network_stats': network_stats
    }

    print(f"\n📊 FedADS压力测试总结:")
    print(f"   总体通信成功率: {comm_stats['success_rate']:.2%}")
    print(f"   平均通信时间: {comm_stats['avg_communication_time']:.2f}秒")
    print(f"   平均staleness: {comm_stats['avg_staleness']:.1f}")
    print(f"   最大staleness: {comm_stats['max_staleness']}")
    print(f"   网络状态变化次数: {network_stats['network_condition_changes']}")

    print(f"\n📱 各设备类型成功率:")
    for device_type, success_rate in comm_stats['device_type_success_rates'].items():
        print(f"   {device_type}: {success_rate:.2%}")

    # 保存结果
    with open('fadas_stress_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\n💾 FedADS压力测试结果已保存到: fadas_stress_test_results.json")

    return results, network_env, fadas_integration

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(message)s')

    # 运行基础测试
    print("🧪 基础网络环境测试")
    env = test_complex_network_environment()

    print("\n" + "="*60)

    # 运行FedADS压力测试
    results, network_env, integration = run_fadas_stress_test()
