import copy
import logging
import os
import time

from types import SimpleNamespace
from plato.clients import simple
from plato.config import Config
from plato.utils import fonts


# pass global model to trainer to act as a regularizer
class Client(simple.Client):

    async def _start_training(self, inbound_payload):
        """Complete one round of training on this client. 所以plato/clients中的用户只需要复写_train函数即可完成不同损失函数的模型训练"""
        # 开始训练的第一步就是把全局模型加载到本地用户上
        self._load_payload(inbound_payload)
        # 传入server model
        report, outbound_payload = await self._train(inbound_payload)

        if Config().is_edge_server():
            logging.info(
                "[Server #%d] Model aggregated on edge server (%s).", os.getpid(), self
            )
        else:
            logging.info("[%s] Model trained.", self)

        return report, outbound_payload

    async def _train(self, server_model=None):
        """接收server model"""
        # 提取初始加载的全局模型
        init_global_model = copy.deepcopy(self.algorithm.extract_weights())
        logging.info(
            fonts.colourize(
                f"[{self}] Started training in communication round #{self.current_round}."
            )
        )

        # test global model on local dataset
        if (hasattr(Config().clients, "do_global_test") and Config().clients.do_global_test):
            global_accuracy = self.trainer.test(self.testset, self.testset_sampler)
            logging.info("[%s] Test global accuracy: %.2f%%", self, 100 * global_accuracy)

            if global_accuracy == -1:
                # The testing process failed, disconnect from the server
                logging.info(
                    fonts.colourize(
                        f"[{self}] Global accuracy on local data is -1 when testing. Disconnecting from the server."
                    )
                )
                await self.sio.disconnect()
        else:
            global_accuracy = 0

        # Perform model training
        try:
            if hasattr(self.trainer, "current_round"):
                self.trainer.current_round = self.current_round
            training_time = self.trainer.train(self.trainset, self.sampler, server_model)

        except ValueError as exc:
            logging.info(
                fonts.colourize(f"[{self}] Error occurred during training: {exc}")
            )
            await self.sio.disconnect()

        # Extract model weights and biases
        weights = self.algorithm.extract_weights()
        deltas = self.algorithm.compute_weight_deltas(init_global_model, [weights])[0]

        # Generate a report for the server, performing model testing if applicable
        if (hasattr(Config().clients, "do_test") and Config().clients.do_test) and (
            not hasattr(Config().clients, "test_interval")
            or self.current_round % Config().clients.test_interval == 0
        ):
            accuracy = self.trainer.test(self.testset, self.testset_sampler)

            if accuracy == -1:
                # The testing process failed, disconnect from the server
                logging.info(
                    fonts.colourize(
                        f"[{self}] Accuracy is -1 when testing. Disconnecting from the server."
                    )
                )
                await self.sio.disconnect()

            if hasattr(Config().trainer, "target_perplexity"):
                logging.info("[%s] Test perplexity: %.2f", self, accuracy)
            else:
                logging.info("[%s] Test accuracy: %.2f%%", self, 100 * accuracy)
        else:
            accuracy = 0

        comm_time = time.time()

        if (
            hasattr(Config().clients, "sleep_simulation")
            and Config().clients.sleep_simulation
        ):
            sleep_seconds = Config().client_sleep_times[self.client_id - 1]
            avg_training_time = Config().clients.avg_training_time

            training_time = (
                avg_training_time + sleep_seconds
            ) * Config().trainer.epochs

        report = SimpleNamespace(
            client_id=self.client_id,
            num_samples=self.sampler.num_samples(),
            accuracy=accuracy,
            global_accuracy=global_accuracy,
            training_time=training_time,
            comm_time=comm_time,
            update_response=False,
            deltas=deltas,
        )

        self._report = self.customize_report(report)

        return self._report, weights

