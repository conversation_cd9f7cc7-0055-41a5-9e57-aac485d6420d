clients:
    # Type
    type: simple

    # The total number of clients (减少以提高稳定性)
    total_clients: 10

    # The number of clients selected in each round
    per_round: 5

    # *Should the clients compute test accuracy locally?(test local model on local dataset)
    do_test: true

    # *Should the clients compute test accuracy with global model?(test global model on local dataset)
    do_global_test: true

    # Whether client heterogeneity should be simulated
    speed_simulation: false

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 5

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 5

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8005
    ping_timeout: 36000
    ping_interval: 36000

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server? Useful if max_concurrency is specified
    simulate_wall_time: true

    # (fedbuff)
    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 2

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 5

    # Should we send urgent notifications to stale clients beyond the staleness bound?
    request_update: true

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/mnist/01
    model_path: models/mnist/01

    random_seed: 1

    # (FedAC)
    fedac_beta1: 0.9
    fedac_beta2: 0.99
    fedac_eps: 0.00000001
    fedac_global_lr: 0.01

data:
    # The training and testing dataset
    datasource: MNIST

    # Number of samples in each partition
    partition_size: 600

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution(alpha)
    concentration: 1

    # The size of the testset on the server
    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

    # *get the local test sampler to obtain the test dataset
    testset_sampler: noniid
    
    # 是否可视化客户端数据分布
    visualize_distribution: true

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds (减少以便快速测试)
    rounds: 500

    # The maximum number of clients running concurrently
    max_concurrency: 3

    # The target accuracy
    target_accuracy: 0.98

    # 禁用困惑度停止条件，让训练运行完整轮数
    target_perplexity: 0

    # Number of epoches for local training in each communication round
    epochs: 3
    batch_size: 50
    optimizer: SGD
    lr_scheduler: LambdaLR

    # The machine learning model
    model_name: lenet5

algorithm:
    # Aggregation algorithm
    type: fedavg
    lamda: 1.0

parameters:
    model:
        num_classes: 10
        in_channels: 1  # MNIST灰度图像使用1通道
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001
    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    result_path: results/mnist/01

    # Write the following parameter(s) into a CSV (添加真实运行时间记录)
    types: round, elapsed_time, real_elapsed_time, accuracy, loss, avg_staleness
