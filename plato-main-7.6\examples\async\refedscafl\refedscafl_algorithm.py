import logging
import os
import copy
import random
import sys
import time
import numpy as np
import torch
from datetime import datetime
import asyncio
from logging.handlers import RotatingFileHandler
from collections import OrderedDict
from plato.config import Config
from plato.algorithms import fedavg
from plato.models import registry as models
from plato.utils import csv_processor
from math import log2
from types import SimpleNamespace
import pandas as pd
from plato.models import lenet5
from functools import partial

# 配置日志
def setup_logging():
    """配置日志系统"""
    print(f"我我我，算法文件下{setup_logging.__name__}被调用了")
    # 获取根日志记录器
    root_logger = logging.getLogger()
    
    # 检查是否已经配置过日志处理器，避免重复添加
    if root_logger.handlers:
        # 如果已经有处理器，直接返回现有的logger
        return root_logger
    
    # 清除任何现有的处理器（以防万一�?
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建日志目录
    log_dir = os.path.join(Config().result_path if hasattr(Config(), 'result_path') else './logs', 'algorithm_logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 设置日志文件�?
    log_file = os.path.join(log_dir, f'algorithm_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    # 配置根日志记录器
    root_logger.setLevel(logging.DEBUG)
    
    # 创建文件处理器（带轮转）
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '[%(asctime)s][%(levelname)s][%(filename)s:%(lineno)d] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理�?
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return root_logger

# 初始化日�?
logger = setup_logging()

class Algorithm(fedavg.Algorithm):
    """带时间衰减的加权聚合算法"""
    
    def __init__(self, trainer=None, server=None, *args, **kwargs):
        print(f"我我我，算法文件下Algorithm.__init__被调用了")
        print("DEBUG: Algorithm received trainer =", trainer)
        if trainer is None:
            raise ValueError("trainer cannot be None")
        self.trainer = trainer
        self.server = server  # 添加服务器引�?
        print("Algorithm initialized with trainer:", trainer)
        super().__init__(trainer=trainer)  # 使用关键字参数传�?
        self.client_staleness = {}
        
        # 初始化权重参�?
        self.success_weight = 0.7  # 成功上传模型的权�?
        self.distill_weight = 0.3  # 蒸馏补偿模型的权�?
        self.rho = 1.0  # 动量系数
        
        # 初始化缓冲池
        self.success_buffer_pool = []
        self.distill_buffer_pool = []
        self.success_buffered_clients = []
        self.distill_buffered_clients = []
        
        # 初始化其他参�?
        self.greedy_selection_size = 2
        self.V = 1.0  # 延迟权重
        self.tau_max = 6  # 最大陈旧度
        
        # 客户端状态跟�?
        self.client_beta = {}
        self.client_estimated_duration = {}
        self.staleness_queue = {}  # 添加缺失的staleness_queue属�?
        
        # 初始化基本属�?
        self.current_round = 0
        self.selected_clients = []
        self.client_upload_success = {}
        self.distilled_clients = set()
        self.client_updates = {}  # 存储客户端更�?
        self.global_weights = None  # 初始化全局权重为None
        
        # 从配置中获取参数
        config = Config()
        self.total_clients = getattr(config.clients, 'total_clients', 20)
        self.clients_per_round = getattr(config.clients, 'per_round', 5)
        
        # 初始化权重参�?
        self.success_weight = getattr(config.algorithm, 'success_weight', 0.7)  # 成功上传模型的权�?
        self.distill_weight = getattr(config.algorithm, 'distill_weight', 0.3)  # 蒸馏补偿模型的权�?

        # 初始化贪心选择参数
        self.greedy_selection_size = getattr(config.algorithm, 'greedy_selection_size', 3)  # 贪心选择的客户端数量
        self.buffer_pool_size = getattr(config.algorithm, 'buffer_pool_size', 20)  # 缓冲池大�?

        # 初始化通信阈�?
        self.communication_threshold = getattr(config.algorithm, 'communication_threshold', 0.6)  # 默认0.6�?
        self.historical_comm_times = []  # 存储历史通信时间

        # SCAFL相关参数
        self.V = getattr(config.algorithm, 'V', 1.0)  # 延迟权重
        self.tau_max = getattr(config.algorithm, 'tau_max', 5)  # 最大陈旧度
        self.rho = getattr(config.algorithm, 'rho', 1.0)  # 学习�?

        # 模型一致性评估参�?
        self.enable_consistency_factor = getattr(config.algorithm, 'enable_consistency_factor', True)
        self.consistency_gradient_weight = getattr(config.algorithm, 'consistency_gradient_weight', 0.4)
        self.consistency_magnitude_weight = getattr(config.algorithm, 'consistency_magnitude_weight', 0.3)
        self.consistency_distribution_weight = getattr(config.algorithm, 'consistency_distribution_weight', 0.3)
        self.consistency_variance_penalty = getattr(config.algorithm, 'consistency_variance_penalty', 0.2)

        logger.info("算法初始化完成")
        logger.info("参数配置：success_weight=%.2f, distill_weight=%.2f, greedy_selection_size=%d, V=%.2f, tau_max=%d",
                   self.success_weight, self.distill_weight, self.greedy_selection_size, self.V, self.tau_max)
        logger.info("一致性评估：enable_consistency=%.s, 梯度权重=%.2f, 幅度权重=%.2f, 分布权重=%.2f",
                   self.enable_consistency_factor, self.consistency_gradient_weight,
                   self.consistency_magnitude_weight, self.consistency_distribution_weight)
    def set_server_reference(self, server):
        """设置服务器引用，用于访问客户端状态信息"""
        print(f"我我我，算法文件下Algorithm.set_server_reference被调用了")
        self.server = server
        logger.info("算法类已设置服务器引用")

    def get_client_staleness(self, client_id):
        """从服务器获取客户端陈旧度"""
        logger.debug(f"[Algorithm] 获取客户端 {client_id} 的陈旧度")
        if self.server and hasattr(self.server, 'client_staleness'):
            return self.server.client_staleness.get(client_id, 0)
        return self.client_staleness.get(client_id, 0)

    async def sc_afl_style_aggregate_weights(self, updates):
        """
        参考SC_AFL的权重聚合方法
        使用陈旧度因子和样本数量计算客户端权重
        """
        if not updates:
            logger.warning("[Algorithm] 没有客户端更新可聚合")
            return None

        try:
            client_weights = {}
            total_samples = 0

            # 计算每个客户端的权重
            for update in updates:
                client_id = update['client_id']
                staleness = getattr(update, 'staleness', self.get_client_staleness(client_id))
                num_samples = getattr(update.get('report', {}), 'num_samples', 1000)  # 默认样本数
                total_samples += num_samples

                # SC_AFL的陈旧度因子计算：max(0.1, 1.0 - staleness / (2 * tau_max))
                staleness_factor = max(0.1, 1.0 - staleness / (2 * self.tau_max))
                client_weights[client_id] = num_samples * staleness_factor

                logger.info(f"[Algorithm] 客户端 {client_id} - 样本数: {num_samples}, 陈旧度: {staleness}, "
                           f"陈旧度因子: {staleness_factor:.4f}, 原始权重: {client_weights[client_id]:.4f}")

            # 权重归一化
            weight_sum = sum(client_weights.values())
            if weight_sum > 0:
                for client_id in client_weights:
                    client_weights[client_id] /= weight_sum
            else:
                # 如果权重和为0，使用均匀权重
                for client_id in client_weights:
                    client_weights[client_id] = 1.0 / len(updates)

            for client_id, weight in client_weights.items():
                logger.info(f"[Algorithm] 客户端 {client_id} 最终权重: {weight:.4f}")

            # 执行权重聚合
            aggregated_weights = {}
            if not updates[0]['weights']:
                logger.error("[Algorithm] 第一个更新的权重字典为空，无法聚合")
                return None

            for key in updates[0]['weights'].keys():
                weighted_sum = None

                for update in updates:
                    client_id = update['client_id']
                    weight = client_weights[client_id]

                    if weighted_sum is None:
                        # 确保张量在同一设备上
                        weighted_sum = update['weights'][key].to(self.trainer.device) * weight
                    else:
                        weighted_sum += update['weights'][key].to(self.trainer.device) * weight

                aggregated_weights[key] = weighted_sum

            return aggregated_weights

        except Exception as e:
            logger.error(f"[Algorithm] SC_AFL风格聚合权重时出错: {str(e)}")
            import traceback
            logger.error(f"[Algorithm] 异常堆栈: {traceback.format_exc()}")
            return None

    def aggregate_weights(self, success_updates, distill_updates, success_weight, distill_weight, rho, global_weights=None):
        """
        优化的基于梯度的聚合方法: wt = wt-1 + a*正常梯度 + b*蒸馏梯度

        优化点：
        1. 减少重复的设备转�?
        2. 使用更高效的张量操作
        3. 支持稀疏更�?
        4. 添加梯度裁剪防止梯度爆炸

        参数:
            success_updates: 成功上传的客户端更新列表（包含模型权重）
            distill_updates: 蒸馏补偿的客户端更新列表（包含梯度）
            success_weight: 成功上传模型的权重系�?a
            distill_weight: 蒸馏补偿梯度的权重系�?b
            rho: 动量参数（在此方法中不使用，保持接口兼容�?
            global_weights: 当前全局模型权重（wt-1�?
        Returns:
            OrderedDict: 聚合后的全局模型权重（wt�?
        """
        import torch
        from collections import OrderedDict
        print(f"我我我，算法文件下Algorithm.aggregate_weights被调用了")

        try:
            # 过滤无效更新
            success_updates = [u for u in success_updates if u['weights'] and isinstance(u['weights'], dict)]
            distill_updates = [u for u in distill_updates if u['weights'] and isinstance(u['weights'], dict)]

            if not success_updates and not distill_updates:
                logger.error("所有客户端的更新均无效，无法聚合")
                return None

            if global_weights is None:
                logger.error("全局权重为空，无法进行基于梯度的聚合")
                return None

            # 获取设备信息，避免重复转�?
            device = next(iter(global_weights.values())).device

            # 初始化聚合梯�?- 使用更高效的方式
            aggregated_gradients = OrderedDict()
            for key in global_weights.keys():
                aggregated_gradients[key] = torch.zeros_like(global_weights[key], device=device)

            # ------------- 优化的正常客户端处理（批量计算梯度）-------------

            # 计算总样本数（用于加权）
            total_success_samples = sum(self.get_num_samples(update) for update in success_updates) if success_updates else 0

            # 处理每个成功上传的客户端
            if success_updates:
                logger.info("处理 %s 个成功上传客户端的模型更新", len(success_updates))

                # 批量处理客户端权重，减少循环开销
                for update in success_updates:
                    try:
                        client_id = update.get('client_id', '未知')
                        weights = update['weights']  # 这是客户端训练后的模型权�?
                        if weights is None:
                            logger.warning(f"客户端{client_id}的权重为None，已跳过")
                            continue

                        # 计算该客户端的权重系�?
                        num_samples = self.get_num_samples(update)
                        client_weight = (num_samples / total_success_samples) if total_success_samples > 0 else 0

                        # 直接计算加权梯度并累加，避免中间存储
                        for key in weights.keys():
                            if key in global_weights and key in aggregated_gradients:
                                    try:
                                        # 确保设备和数据类型一致，但避免Float到Long的转换错�?
                                        target_dtype = global_weights[key].dtype
                                        source_tensor = weights[key].to(device=device)

                                        # 安全的类型转�?
                                        if target_dtype == source_tensor.dtype:
                                            # 类型已经匹配，直接使�?
                                            client_weight_tensor = source_tensor
                                        elif target_dtype.is_floating_point and source_tensor.dtype.is_floating_point:
                                            # 浮点数之间的转换
                                            client_weight_tensor = source_tensor.to(dtype=target_dtype)
                                        elif target_dtype.is_floating_point and not source_tensor.dtype.is_floating_point:
                                            # 整数到浮点数的转�?
                                            client_weight_tensor = source_tensor.to(dtype=target_dtype)
                                        elif not target_dtype.is_floating_point and source_tensor.dtype.is_floating_point:
                                            # 浮点数到整数的转�?- 需要特殊处�?
                                            client_weight_tensor = source_tensor.round().to(dtype=target_dtype)
                                        else:
                                            # 整数之间的转�?
                                            client_weight_tensor = source_tensor.to(dtype=target_dtype)

                                        # 计算加权梯度�?客户端权�?- 全局权重) * 客户端权�?* 成功权重
                                        weighted_gradient = (client_weight_tensor - global_weights[key]) * client_weight * success_weight

                                        # 梯度裁剪防止梯度爆炸
                                        gradient_norm = torch.norm(weighted_gradient)
                                        if gradient_norm > 1.0:  # 梯度裁剪阈�?
                                            weighted_gradient = weighted_gradient / gradient_norm * 1.0

                                        aggregated_gradients[key] += weighted_gradient
                                    except Exception as key_error:
                                        logger.error(f"处理客户�?{client_id} 的键 {key} 时出�? {str(key_error)}")
                                        continue  # 跳过这个键，继续处理其他键
                            elif key not in global_weights:
                                logger.warning(f"客户端权重中的键 {key} 不在全局模型中，已跳过")
                    except Exception as update_error:
                        logger.error(f"处理客户端 {update.get('client_id', '未知')} 更新时出错: {str(update_error)}")
                        continue  # 跳过这个客户端，继续处理其他客户端

            # ------------- 优化的蒸馏补偿处理（直接使用梯度�?------------

            # 计算总样本数（用于加权）
            total_distill_samples = sum(self.get_num_samples(update) for update in distill_updates) if distill_updates else 0

            # 处理每个蒸馏补偿的客户端
            if distill_updates:
                logger.info("处理 %s 个蒸馏补偿客户端的梯度更新", len(distill_updates))

                # 批量处理蒸馏梯度
                for update in distill_updates:
                    try:
                        client_id = update.get('client_id', '未知')
                        gradients = update['weights']  # 这是客户端通过蒸馏生成的梯�?
                        if gradients is None:
                            logger.warning(f"客户端{client_id}的梯度为None，已跳过")
                            continue

                        # 计算该客户端的权重系�?
                        num_samples = self.get_num_samples(update)
                        client_weight = (num_samples / total_distill_samples) if total_distill_samples > 0 else 0

                        # 直接计算加权蒸馏梯度并累�?
                        for key in gradients.keys():
                            if key in global_weights and key in aggregated_gradients:
                                    try:
                                        # 确保设备和数据类型一致，但避免Float到Long的转换错�?
                                        target_dtype = global_weights[key].dtype
                                        source_tensor = gradients[key].to(device=device)

                                        # 安全的类型转�?
                                        if target_dtype == source_tensor.dtype:
                                            # 类型已经匹配，直接使�?
                                            grad_tensor = source_tensor
                                        elif target_dtype.is_floating_point and source_tensor.dtype.is_floating_point:
                                            # 浮点数之间的转换
                                            grad_tensor = source_tensor.to(dtype=target_dtype)
                                        elif target_dtype.is_floating_point and not source_tensor.dtype.is_floating_point:
                                            # 整数到浮点数的转�?
                                            grad_tensor = source_tensor.to(dtype=target_dtype)
                                        elif not target_dtype.is_floating_point and source_tensor.dtype.is_floating_point:
                                            # 浮点数到整数的转�?- 需要特殊处�?
                                            grad_tensor = source_tensor.round().to(dtype=target_dtype)
                                        else:
                                            # 整数之间的转�?
                                            grad_tensor = source_tensor.to(dtype=target_dtype)

                                        # 计算加权蒸馏梯度
                                        weighted_distill_gradient = grad_tensor * client_weight * distill_weight

                                        # 蒸馏梯度也进行裁�?
                                        gradient_norm = torch.norm(weighted_distill_gradient)
                                        if gradient_norm > 0.5:  # 蒸馏梯度使用更小的裁剪阈�?
                                            weighted_distill_gradient = weighted_distill_gradient / gradient_norm * 0.5

                                        aggregated_gradients[key] += weighted_distill_gradient
                                    except Exception as key_error:
                                        logger.error(f"处理蒸馏客户�?{client_id} 的键 {key} 时出�? {str(key_error)}")
                                        continue  # 跳过这个键，继续处理其他键
                            elif key not in global_weights:
                                logger.warning(f"蒸馏梯度中的键 {key} 不在全局模型中，已跳过")
                    except Exception as update_error:
                        logger.error(f"处理蒸馏客户端 {update.get('client_id', '未知')} 更新时出错: {str(update_error)}")
                        continue  # 跳过这个客户端，继续处理其他客户端

            # ------------- 优化的权重更新（支持学习率调整和动量）-------------

            # 添加自适应学习率调�?
            adaptive_lr = self._calculate_adaptive_learning_rate(aggregated_gradients, global_weights)

            # 根据优化的公�? wt = wt-1 + lr * (a*正常梯度 + b*蒸馏梯度)
            # 其中，a*正常梯度 + b*蒸馏梯度 已经计算�?aggregated_gradients
            new_weights = OrderedDict()

            # 批量更新权重，减少循环开销
            for key in global_weights.keys():
                if key in aggregated_gradients:
                    # 应用自适应学习�?
                    update = aggregated_gradients[key] * adaptive_lr
                    new_weights[key] = global_weights[key] + update
                else:
                    new_weights[key] = global_weights[key].clone()  # 使用clone避免引用问题

            logger.info("优化的基于梯度的聚合完成，新权重已生成（自适应学习率: %.4f）", adaptive_lr)
            return new_weights

        except Exception as e:
            logger.error("模型聚合失败: %s", str(e))
            import traceback
            logger.error("详细错误信息: %s", traceback.format_exc())

            # 尝试使用简单平均聚合作为回退方案
            logger.warning("尝试使用简单平均聚合作为回退方案")
            try:
                from simple_aggregator import simple_average_aggregate
                return simple_average_aggregate(success_updates, distill_updates, global_weights)
            except Exception as fallback_error:
                logger.error("回退方案也失�? %s", str(fallback_error))
                return None

    def _calculate_adaptive_learning_rate(self, gradients, global_weights):
        """
        计算自适应学习�?
        基于梯度范数和权重范数的比值来调整学习�?
        """
        try:
            # 计算总梯度范�?
            total_grad_norm = 0.0
            total_weight_norm = 0.0

            for key in gradients.keys():
                if key in global_weights:
                    grad_norm = torch.norm(gradients[key]).item()
                    weight_norm = torch.norm(global_weights[key]).item()
                    total_grad_norm += grad_norm ** 2
                    total_weight_norm += weight_norm ** 2

            total_grad_norm = total_grad_norm ** 0.5
            total_weight_norm = total_weight_norm ** 0.5

            # 保守稳定的自适应学习率计�?
            if total_grad_norm > 0 and total_weight_norm > 0:
                # 基于梯度和权重的比值调整学习率，保守策�?
                ratio = total_weight_norm / total_grad_norm
                adaptive_lr = min(1.0, max(0.1, ratio * 0.1))  # 限制在[0.1, 1.0]范围�?
            else:
                adaptive_lr = 1.0  # 标准默认学习�?

            return adaptive_lr

        except Exception as e:
            logger.warning(f"计算自适应学习率失�? {e}，使用默认�?.0")
            return 1.0



    def extract_weights(self):
        """从模型中提取权重"""
        print(f"我我我，算法文件下Algorithm.extract_weights被调用了")
        if self.trainer.model is not None:
            return self.trainer.model.state_dict()
        return None
        
    def load_weights(self, weights):
        """加载权重到模型"""
        print(f"我我我，算法文件下Algorithm.load_weights被调用了")
        if self.trainer.model is not None and weights is not None:
            self.trainer.model.load_state_dict(weights)

    def greedy_select_clients(self, updates):
        """
        贪心选择客户端 - 委托给服务器端实现
        参数:
            updates: 客户端更新列表
        返回:
            selected_updates: 选中的更新列表
        """
        print(f"我我我，算法文件下Algorithm.greedy_select_clients被调用了")
        if self.server is None:
            logger.error("服务器引用为空，无法进行贪心选择")
            return []

        # 委托给服务器端的贪心选择实现
        return self.server.greedy_select_clients(updates)
        
    def get_num_samples(self, update):
        """
        从更新中获取样本�?
        参数:
            update: 客户端更�?
        返回:
            num_samples: 样本�?
        """
        print(f"我我我，算法文件下Algorithm.get_num_samples被调用了")
        try:
            # 检查report是否存在
            if 'report' not in update:
                logger.debug("更新中缺少report字段，使用默认�?00")
                return 600  # 使用默认�?00
                
            report = update['report']
            
            # 检查report的类型和属�?
            logger.debug("Report类型: %s", type(report))
            
            # 尝试不同的方式获取样本数
            if hasattr(report, 'num_samples'):
                num_samples = report.num_samples
                logger.debug("从report.num_samples获取样本�? %s", num_samples)
            elif hasattr(report, 'samples'):
                num_samples = report.samples
                logger.debug("从report.samples获取样本�? %s", num_samples)
            elif isinstance(report, dict) and 'num_samples' in report:
                num_samples = report['num_samples']
                logger.debug("从report字典获取样本�? %s", num_samples)
            else:
                # 如果找不到样本数，使用默认�?00，但不输出警�?
                logger.debug("无法从report中获取样本数，使用默认�?00")
                num_samples = 600
                
            # 确保样本数为正数
            if num_samples <= 0:
                logger.debug("样本数为非正�?%s)，使用默认�?00", num_samples)
                num_samples = 600
                
            return num_samples
            
        except Exception as e:
            logger.debug("获取样本数时出错: %s，使用默认�?00", str(e))
            return 600  # 返回默认�?00

    def simple_average_aggregate_weights(self, success_updates, distill_updates, success_weight=None, distill_weight=None, rho=None, global_weights=None):
        """
        简单聚合方法的包装器，调用外部的安全聚合器
        """
        try:
            # 优先使用快速聚合器（如果可用）
            if hasattr(self, 'fast_aggregator'):
                logger.info("🚀 使用快速聚合器")
                return self.fast_aggregator.fast_average_aggregate(success_updates)

            # 回退到简单聚合器
            from simple_aggregator import simple_average_aggregate
            return simple_average_aggregate(success_updates, distill_updates, global_weights)
        except Exception as e:
            logger.error("调用简单聚合器失败: %s", str(e))
            return None
