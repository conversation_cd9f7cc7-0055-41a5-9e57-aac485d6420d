2025-08-01 11:53:21,414 - INFO - 🚀 SC-AFL服务器启动 - 2025-08-01 11:53:21
2025-08-01 11:53:21,414 - INFO - ✅ 新日志文件已创建
2025-08-01 11:53:21,414 - INFO - 📁 日志目录: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs
2025-08-01 11:53:21,414 - INFO - 📄 日志文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs\sc_afl_server_20250801_115321_12352.log
2025-08-01 11:53:21,416 - INFO - 🔧 日志级别: DEBUG (文件), INFO (控制台)
2025-08-01 11:53:21,417 - INFO - 📊 文件模式: 新建模式 (每次启动创建新文件)
2025-08-01 11:53:21,417 - INFO - 🆔 进程ID: 12352
2025-08-01 11:53:21,417 - INFO - ✅ 日志文件创建成功，当前大小: 675 字节
2025-08-01 11:53:21,417 - INFO - 🚀 SC-AFL服务器初始化开始...
2025-08-01 11:53:21,417 - INFO - 📅 初始化时间: 2025-08-01 11:53:21
2025-08-01 11:53:21,418 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:21,435 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:21,435 - INFO - Server: 动态创建模型 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:53:21,435 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:53:21,446 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:21,447 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:53:21,447 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:53:21,447 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:53:21,448 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:21,448 - INFO - [Trainer None] 初始化完成
2025-08-01 11:53:21,448 - INFO - Server: 创建了新的Trainer实例
2025-08-01 11:53:21,448 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:53:21,448 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:53:21,448 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:21,448 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:21,448 - INFO - Server: 创建了新的Algorithm实例
2025-08-01 11:53:21,449 - INFO - [93m[1m[12352] Logging runtime results to: ./results/cifar10_with_network/12352.csv.[0m
2025-08-01 11:53:21,449 - INFO - [Server #12352] Started training on 6 clients with 3 per round.
2025-08-01 11:53:21,449 - INFO - [DEBUG] 从配置文件读取 simulate_wall_time=True
2025-08-01 11:53:21,449 - WARNING - Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。
2025-08-01 11:53:21,449 - WARNING - Server: 训练器在初始化后为None，重新创建
2025-08-01 11:53:21,449 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:53:21,449 - WARNING - [Trainer None] 模型为None，尝试创建默认模型
2025-08-01 11:53:21,449 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:21,464 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:21,464 - INFO - [Trainer None] 动态创建模型 resnet_9，输入通道: 3, 类别数: 10
2025-08-01 11:53:21,474 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:21,474 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:53:21,474 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:53:21,474 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:53:21,475 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:21,475 - INFO - [Trainer None] 初始化完成
2025-08-01 11:53:21,475 - INFO - Server: 重新创建了Trainer实例
2025-08-01 11:53:21,475 - INFO - [Algorithm] 已设置服务器引用 (客户端ID: None)
2025-08-01 11:53:21,475 - INFO - Server: 算法类已设置服务器引用
2025-08-01 11:53:21,475 - INFO - 动态加载数据集: CIFAR10
2025-08-01 11:53:21,980 - INFO - ✅ 成功加载数据集 CIFAR10: 10000 样本
2025-08-01 11:53:21,981 - INFO - ✅ 动态加载测试集成功: CIFAR10, 大小: 10000
2025-08-01 11:53:21,981 - INFO - ✅ 测试加载器已创建
2025-08-01 11:53:21,981 - INFO - 开始初始化全局模型权重
2025-08-01 11:53:21,981 - WARNING - 全局模型实例为None，正在尝试重新创建...
2025-08-01 11:53:21,982 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:21,995 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:21,995 - INFO - 成功重新创建了全局模型实例 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 11:53:22,001 - INFO - [全局权重摘要] 参数数量: 74, 均值: 0.001174, 最大: 1.000000, 最小: -0.192123
2025-08-01 11:53:22,001 - INFO - [全局模型] 输入通道数: 3
2025-08-01 11:53:22,001 - INFO - 全局模型权重初始化成功
2025-08-01 11:53:22,003 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:53:22,003 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:53:22,004 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:53:22,004 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_115322.csv
2025-08-01 11:53:22,004 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:53:22,005 - WARNING - 网络模拟器初始化失败: 'Config' object has no attribute 'get'
2025-08-01 11:53:22,005 - INFO - SC-AFL算法参数: tau_max=5, V=1.0
2025-08-01 11:53:22,005 - INFO - 服务器初始化完成
2025-08-01 11:53:22,005 - INFO - 已创建并注册 0 个客户端（将在 Server.start() 中启动任务）
2025-08-01 11:53:22,005 - INFO - 客户端ID管理器初始化完成，总客户端数: 6, ID起始值: 1
2025-08-01 11:53:22,006 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 11:53:22,006 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 11:53:22,006 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 11:53:22,006 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_115322.csv
2025-08-01 11:53:22,007 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 11:53:22,007 - INFO - 服务器实例创建成功
2025-08-01 11:53:22,007 - INFO - 正在创建和注册 6 个客户端...
2025-08-01 11:53:22,007 - INFO - 客户端ID配置: 起始ID=1, 总数=6
2025-08-01 11:53:22,007 - INFO - 开始创建客户端 1...
2025-08-01 11:53:22,007 - INFO - 初始化客户端, ID: 1
2025-08-01 11:53:22,007 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:22,024 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:22,024 - INFO - [Client 1] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:22,024 - INFO - [Trainer] 初始化训练器, client_id: 1
2025-08-01 11:53:22,035 - INFO - [Trainer 1] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:22,035 - INFO - [Trainer 1] 模型的输入通道数: 3
2025-08-01 11:53:22,035 - INFO - [Trainer 1] 强制使用CPU
2025-08-01 11:53:22,036 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:53:22,036 - INFO - [Trainer 1] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:22,036 - INFO - [Trainer 1] 初始化完成
2025-08-01 11:53:22,036 - INFO - [Client 1] 创建新训练器
2025-08-01 11:53:22,036 - INFO - [Algorithm] 从训练器获取客户端ID: 1
2025-08-01 11:53:22,036 - INFO - [Algorithm] 初始化后修正client_id: 1
2025-08-01 11:53:22,036 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:22,037 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:22,037 - INFO - [Client 1] 创建新算法
2025-08-01 11:53:22,037 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:53:22,037 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:53:22,037 - INFO - [Client None] 父类初始化完成
2025-08-01 11:53:22,037 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:22,052 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:22,053 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:22,053 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:53:22,062 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:22,063 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:53:22,063 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:53:22,063 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:53:22,064 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:22,064 - INFO - [Trainer None] 初始化完成
2025-08-01 11:53:22,064 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:53:22,064 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:53:22,064 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:53:22,064 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:22,064 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:22,065 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:53:22,065 - INFO - [Client None] 开始加载数据
2025-08-01 11:53:22,065 - INFO - 顺序分配客户端ID: 1
2025-08-01 11:53:22,065 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 1
2025-08-01 11:53:22,066 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:53:22,066 - WARNING - [Client 1] 数据源为None，已创建新数据源
2025-08-01 11:53:22,066 - WARNING - [Client 1] 数据源trainset为None，已设置为空列表
2025-08-01 11:53:22,677 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:53:22,678 - INFO - [Client 1] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:53:22,678 - INFO - [Client 1] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:53:22,682 - INFO - [Client 1] 成功划分数据集，分配到 300 个样本
2025-08-01 11:53:22,682 - INFO - [Client 1] 初始化时成功加载数据
2025-08-01 11:53:22,682 - INFO - [客户端 1] 初始化验证通过
2025-08-01 11:53:22,682 - INFO - 客户端 1 实例创建成功
2025-08-01 11:53:22,683 - INFO - 客户端1已设置服务器引用
2025-08-01 11:53:22,683 - INFO - 客户端 1 已设置服务器引用
2025-08-01 11:53:22,683 - INFO - 客户端1已注册
2025-08-01 11:53:22,683 - INFO - 客户端 1 已成功注册到服务器
2025-08-01 11:53:22,683 - INFO - 开始创建客户端 2...
2025-08-01 11:53:22,683 - INFO - 初始化客户端, ID: 2
2025-08-01 11:53:22,684 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:22,698 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:22,698 - INFO - [Client 2] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:22,699 - INFO - [Trainer] 初始化训练器, client_id: 2
2025-08-01 11:53:22,709 - INFO - [Trainer 2] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:22,709 - INFO - [Trainer 2] 模型的输入通道数: 3
2025-08-01 11:53:22,709 - INFO - [Trainer 2] 强制使用CPU
2025-08-01 11:53:22,710 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 11:53:22,710 - INFO - [Trainer 2] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:22,710 - INFO - [Trainer 2] 初始化完成
2025-08-01 11:53:22,710 - INFO - [Client 2] 创建新训练器
2025-08-01 11:53:22,710 - INFO - [Algorithm] 从训练器获取客户端ID: 2
2025-08-01 11:53:22,710 - INFO - [Algorithm] 初始化后修正client_id: 2
2025-08-01 11:53:22,710 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:22,710 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:22,710 - INFO - [Client 2] 创建新算法
2025-08-01 11:53:22,710 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 11:53:22,710 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 11:53:22,711 - INFO - [Client None] 父类初始化完成
2025-08-01 11:53:22,711 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:22,725 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:22,726 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:22,726 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:53:22,735 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:22,735 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:53:22,735 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:53:22,736 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:53:22,736 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:22,736 - INFO - [Trainer None] 初始化完成
2025-08-01 11:53:22,736 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:53:22,736 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:53:22,736 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:53:22,736 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:22,737 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:22,737 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:53:22,737 - INFO - [Client None] 开始加载数据
2025-08-01 11:53:22,737 - INFO - 顺序分配客户端ID: 2
2025-08-01 11:53:22,737 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 2
2025-08-01 11:53:22,737 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:53:22,738 - WARNING - [Client 2] 数据源为None，已创建新数据源
2025-08-01 11:53:22,738 - WARNING - [Client 2] 数据源trainset为None，已设置为空列表
2025-08-01 11:53:23,343 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:53:23,343 - INFO - [Client 2] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:53:23,343 - INFO - [Client 2] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:53:23,347 - INFO - [Client 2] 成功划分数据集，分配到 300 个样本
2025-08-01 11:53:23,347 - INFO - [Client 2] 初始化时成功加载数据
2025-08-01 11:53:23,348 - INFO - [客户端 2] 初始化验证通过
2025-08-01 11:53:23,348 - INFO - 客户端 2 实例创建成功
2025-08-01 11:53:23,348 - INFO - 客户端2已设置服务器引用
2025-08-01 11:53:23,348 - INFO - 客户端 2 已设置服务器引用
2025-08-01 11:53:23,348 - INFO - 客户端2已注册
2025-08-01 11:53:23,348 - INFO - 客户端 2 已成功注册到服务器
2025-08-01 11:53:23,349 - INFO - 开始创建客户端 3...
2025-08-01 11:53:23,349 - INFO - 初始化客户端, ID: 3
2025-08-01 11:53:23,349 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:23,363 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:23,364 - INFO - [Client 3] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:23,364 - INFO - [Trainer] 初始化训练器, client_id: 3
2025-08-01 11:53:23,373 - INFO - [Trainer 3] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:23,373 - INFO - [Trainer 3] 模型的输入通道数: 3
2025-08-01 11:53:23,373 - INFO - [Trainer 3] 强制使用CPU
2025-08-01 11:53:23,374 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 11:53:23,374 - INFO - [Trainer 3] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:23,374 - INFO - [Trainer 3] 初始化完成
2025-08-01 11:53:23,374 - INFO - [Client 3] 创建新训练器
2025-08-01 11:53:23,374 - INFO - [Algorithm] 从训练器获取客户端ID: 3
2025-08-01 11:53:23,374 - INFO - [Algorithm] 初始化后修正client_id: 3
2025-08-01 11:53:23,374 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:23,375 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:23,375 - INFO - [Client 3] 创建新算法
2025-08-01 11:53:23,375 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 11:53:23,375 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 11:53:23,375 - INFO - [Client None] 父类初始化完成
2025-08-01 11:53:23,375 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:23,388 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:23,388 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:23,388 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:53:23,398 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:23,398 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:53:23,399 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:53:23,399 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:53:23,399 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:23,399 - INFO - [Trainer None] 初始化完成
2025-08-01 11:53:23,399 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:53:23,400 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:53:23,400 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:53:23,400 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:23,400 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:23,400 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:53:23,400 - INFO - [Client None] 开始加载数据
2025-08-01 11:53:23,400 - INFO - 顺序分配客户端ID: 3
2025-08-01 11:53:23,400 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 3
2025-08-01 11:53:23,400 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:53:23,401 - WARNING - [Client 3] 数据源为None，已创建新数据源
2025-08-01 11:53:23,401 - WARNING - [Client 3] 数据源trainset为None，已设置为空列表
2025-08-01 11:53:23,983 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:53:23,983 - INFO - [Client 3] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:53:23,984 - INFO - [Client 3] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:53:23,988 - INFO - [Client 3] 成功划分数据集，分配到 300 个样本
2025-08-01 11:53:23,988 - INFO - [Client 3] 初始化时成功加载数据
2025-08-01 11:53:23,988 - INFO - [客户端 3] 初始化验证通过
2025-08-01 11:53:23,988 - INFO - 客户端 3 实例创建成功
2025-08-01 11:53:23,988 - INFO - 客户端3已设置服务器引用
2025-08-01 11:53:23,989 - INFO - 客户端 3 已设置服务器引用
2025-08-01 11:53:23,989 - INFO - 客户端3已注册
2025-08-01 11:53:23,989 - INFO - 客户端 3 已成功注册到服务器
2025-08-01 11:53:23,989 - INFO - 开始创建客户端 4...
2025-08-01 11:53:23,989 - INFO - 初始化客户端, ID: 4
2025-08-01 11:53:23,989 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:24,004 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:24,005 - INFO - [Client 4] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:24,005 - INFO - [Trainer] 初始化训练器, client_id: 4
2025-08-01 11:53:24,014 - INFO - [Trainer 4] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:24,014 - INFO - [Trainer 4] 模型的输入通道数: 3
2025-08-01 11:53:24,014 - INFO - [Trainer 4] 强制使用CPU
2025-08-01 11:53:24,015 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 11:53:24,015 - INFO - [Trainer 4] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:24,015 - INFO - [Trainer 4] 初始化完成
2025-08-01 11:53:24,015 - INFO - [Client 4] 创建新训练器
2025-08-01 11:53:24,015 - INFO - [Algorithm] 从训练器获取客户端ID: 4
2025-08-01 11:53:24,015 - INFO - [Algorithm] 初始化后修正client_id: 4
2025-08-01 11:53:24,016 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:24,016 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:24,016 - INFO - [Client 4] 创建新算法
2025-08-01 11:53:24,016 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 11:53:24,016 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 11:53:24,016 - INFO - [Client None] 父类初始化完成
2025-08-01 11:53:24,016 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:24,032 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:24,032 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:24,032 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:53:24,040 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:24,040 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:53:24,041 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:53:24,041 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:53:24,041 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:24,041 - INFO - [Trainer None] 初始化完成
2025-08-01 11:53:24,041 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:53:24,042 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:53:24,042 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:53:24,042 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:24,042 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:24,042 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:53:24,042 - INFO - [Client None] 开始加载数据
2025-08-01 11:53:24,042 - INFO - 顺序分配客户端ID: 4
2025-08-01 11:53:24,042 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 4
2025-08-01 11:53:24,043 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:53:24,043 - WARNING - [Client 4] 数据源为None，已创建新数据源
2025-08-01 11:53:24,043 - WARNING - [Client 4] 数据源trainset为None，已设置为空列表
2025-08-01 11:53:24,625 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:53:24,626 - INFO - [Client 4] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:53:24,626 - INFO - [Client 4] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:53:24,630 - INFO - [Client 4] 成功划分数据集，分配到 300 个样本
2025-08-01 11:53:24,630 - INFO - [Client 4] 初始化时成功加载数据
2025-08-01 11:53:24,631 - INFO - [客户端 4] 初始化验证通过
2025-08-01 11:53:24,631 - INFO - 客户端 4 实例创建成功
2025-08-01 11:53:24,631 - INFO - 客户端4已设置服务器引用
2025-08-01 11:53:24,631 - INFO - 客户端 4 已设置服务器引用
2025-08-01 11:53:24,631 - INFO - 客户端4已注册
2025-08-01 11:53:24,631 - INFO - 客户端 4 已成功注册到服务器
2025-08-01 11:53:24,631 - INFO - 开始创建客户端 5...
2025-08-01 11:53:24,631 - INFO - 初始化客户端, ID: 5
2025-08-01 11:53:24,632 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:24,646 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:24,646 - INFO - [Client 5] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:24,646 - INFO - [Trainer] 初始化训练器, client_id: 5
2025-08-01 11:53:24,655 - INFO - [Trainer 5] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:24,655 - INFO - [Trainer 5] 模型的输入通道数: 3
2025-08-01 11:53:24,655 - INFO - [Trainer 5] 强制使用CPU
2025-08-01 11:53:24,656 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 11:53:24,656 - INFO - [Trainer 5] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:24,656 - INFO - [Trainer 5] 初始化完成
2025-08-01 11:53:24,656 - INFO - [Client 5] 创建新训练器
2025-08-01 11:53:24,656 - INFO - [Algorithm] 从训练器获取客户端ID: 5
2025-08-01 11:53:24,656 - INFO - [Algorithm] 初始化后修正client_id: 5
2025-08-01 11:53:24,656 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:24,656 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:24,657 - INFO - [Client 5] 创建新算法
2025-08-01 11:53:24,657 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 11:53:24,657 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 11:53:24,657 - INFO - [Client None] 父类初始化完成
2025-08-01 11:53:24,657 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:24,671 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:24,671 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:24,671 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:53:24,679 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:24,679 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:53:24,680 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:53:24,680 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:53:24,680 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:24,680 - INFO - [Trainer None] 初始化完成
2025-08-01 11:53:24,680 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:53:24,680 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:53:24,681 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:53:24,681 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:24,681 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:24,681 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:53:24,681 - INFO - [Client None] 开始加载数据
2025-08-01 11:53:24,681 - INFO - 顺序分配客户端ID: 5
2025-08-01 11:53:24,681 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 5
2025-08-01 11:53:24,681 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:53:24,681 - WARNING - [Client 5] 数据源为None，已创建新数据源
2025-08-01 11:53:24,682 - WARNING - [Client 5] 数据源trainset为None，已设置为空列表
2025-08-01 11:53:25,268 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:53:25,269 - INFO - [Client 5] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:53:25,269 - INFO - [Client 5] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:53:25,273 - INFO - [Client 5] 成功划分数据集，分配到 300 个样本
2025-08-01 11:53:25,273 - INFO - [Client 5] 初始化时成功加载数据
2025-08-01 11:53:25,274 - INFO - [客户端 5] 初始化验证通过
2025-08-01 11:53:25,274 - INFO - 客户端 5 实例创建成功
2025-08-01 11:53:25,274 - INFO - 客户端5已设置服务器引用
2025-08-01 11:53:25,274 - INFO - 客户端 5 已设置服务器引用
2025-08-01 11:53:25,274 - INFO - 客户端5已注册
2025-08-01 11:53:25,274 - INFO - 客户端 5 已成功注册到服务器
2025-08-01 11:53:25,274 - INFO - 开始创建客户端 6...
2025-08-01 11:53:25,275 - INFO - 初始化客户端, ID: 6
2025-08-01 11:53:25,275 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:25,290 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:25,290 - INFO - [Client 6] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:25,290 - INFO - [Trainer] 初始化训练器, client_id: 6
2025-08-01 11:53:25,300 - INFO - [Trainer 6] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:25,300 - INFO - [Trainer 6] 模型的输入通道数: 3
2025-08-01 11:53:25,300 - INFO - [Trainer 6] 强制使用CPU
2025-08-01 11:53:25,300 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 11:53:25,301 - INFO - [Trainer 6] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:25,301 - INFO - [Trainer 6] 初始化完成
2025-08-01 11:53:25,301 - INFO - [Client 6] 创建新训练器
2025-08-01 11:53:25,301 - INFO - [Algorithm] 从训练器获取客户端ID: 6
2025-08-01 11:53:25,301 - INFO - [Algorithm] 初始化后修正client_id: 6
2025-08-01 11:53:25,301 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:25,301 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:25,301 - INFO - [Client 6] 创建新算法
2025-08-01 11:53:25,301 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 11:53:25,301 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 11:53:25,301 - INFO - [Client None] 父类初始化完成
2025-08-01 11:53:25,301 - INFO - 从config.trainer.model_name读取模型: resnet_9
2025-08-01 11:53:25,315 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 11:53:25,315 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 11:53:25,315 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 11:53:25,324 - INFO - [Trainer None] 创建了独立的ResNet-9模型实例，优化BatchNorm参数
2025-08-01 11:53:25,324 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 11:53:25,324 - INFO - [Trainer None] 强制使用CPU
2025-08-01 11:53:25,325 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 11:53:25,325 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:25,325 - INFO - [Trainer None] 初始化完成
2025-08-01 11:53:25,325 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 11:53:25,325 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 11:53:25,325 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 11:53:25,325 - INFO - [Algorithm] 初始化完成
2025-08-01 11:53:25,326 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 11:53:25,326 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 11:53:25,326 - INFO - [Client None] 开始加载数据
2025-08-01 11:53:25,326 - INFO - 顺序分配客户端ID: 6
2025-08-01 11:53:25,326 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 6
2025-08-01 11:53:25,326 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 174, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 122, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 11:53:25,326 - WARNING - [Client 6] 数据源为None，已创建新数据源
2025-08-01 11:53:25,326 - WARNING - [Client 6] 数据源trainset为None，已设置为空列表
2025-08-01 11:53:25,923 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 11:53:25,924 - INFO - [Client 6] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 11:53:25,924 - INFO - [Client 6] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 6, 浓度参数: 0.1
2025-08-01 11:53:25,928 - INFO - [Client 6] 成功划分数据集，分配到 300 个样本
2025-08-01 11:53:25,928 - INFO - [Client 6] 初始化时成功加载数据
2025-08-01 11:53:25,928 - INFO - [客户端 6] 初始化验证通过
2025-08-01 11:53:25,929 - INFO - 客户端 6 实例创建成功
2025-08-01 11:53:25,929 - INFO - 客户端6已设置服务器引用
2025-08-01 11:53:25,929 - INFO - 客户端 6 已设置服务器引用
2025-08-01 11:53:25,929 - INFO - 客户端6已注册
2025-08-01 11:53:25,929 - INFO - 客户端 6 已成功注册到服务器
2025-08-01 11:53:25,929 - INFO - 已成功创建和注册 6 个客户端
2025-08-01 11:53:25,930 - INFO - 服务器属性检查:
2025-08-01 11:53:25,930 - INFO - - 客户端数量: 6
2025-08-01 11:53:25,930 - INFO - - 全局模型: 已初始化
2025-08-01 11:53:25,930 - INFO - - 算法: 已初始化
2025-08-01 11:53:25,930 - INFO - - 训练器: 已初始化
2025-08-01 11:53:25,930 - INFO - 准备启动服务器...
2025-08-01 11:53:25,930 - INFO - [Server #12352] 启动中...
2025-08-01 11:53:25,930 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:53:25,931 - INFO - 服务器将使用事件循环: <ProactorEventLoop running=False closed=False debug=False>
2025-08-01 11:53:25,931 - INFO - ✅ 服务器已有 6 个客户端，开始启动训练
2025-08-01 11:53:25,931 - INFO - [Client 1] 模型已放置到设备: cpu
2025-08-01 11:53:25,938 - INFO - [Client 1] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:53:25,944 - INFO - [Client 1] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 11:53:25,945 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 11:53:25,945 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 11:53:25,945 - INFO - [Client 1] 已更新algorithm的client_id
2025-08-01 11:53:25,945 - INFO - [Client 1] 模型初始化完成
2025-08-01 11:53:25,945 - INFO - 客户端 1 模型初始化成功
2025-08-01 11:53:25,945 - INFO - ⚠️ 未检测到异步事件循环，客户端 1 使用线程模式
2025-08-01 11:53:30,989 - INFO - 客户端 1 开始异步训练循环
2025-08-01 11:53:30,990 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:53:30,991 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 11:53:30,992 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 11:53:30,993 - INFO - [Client 1] 开始验证训练集
2025-08-01 11:53:31,014 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:53:31,015 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 11:53:31,016 - INFO - [Trainer 1] 开始训练
2025-08-01 11:53:31,016 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 11:53:31,017 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:53:31,017 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:53:31,018 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:53:31,018 - INFO - [Trainer 1] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:53:31,018 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 11:53:31,041 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2414, y=[4, 0, 5, 5, 8]
2025-08-01 11:53:31,041 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 11:53:31,046 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 11:53:31,053 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1964
2025-08-01 11:53:31,053 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:53:31,053 - INFO - [Trainer 1] 标签样本: [5, 5, 4, 5, 8]
2025-08-01 11:53:31,235 - INFO - [Trainer 1] Batch 0, Loss: 2.0713
2025-08-01 11:53:32,278 - INFO - [Trainer 1] Batch 5, Loss: 1.9431
2025-08-01 11:53:32,900 - INFO - [Trainer 1] Epoch 1 进度: 10/10 批次
2025-08-01 11:53:32,915 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1920
2025-08-01 11:53:32,915 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:53:32,915 - INFO - [Trainer 1] 标签样本: [0, 0, 5, 8, 0]
2025-08-01 11:53:32,970 - INFO - [Trainer 1] Batch 9, Loss: 1.0915
2025-08-01 11:53:33,069 - INFO - [Trainer 1] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:53:33,069 - INFO - [Trainer 1] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.5661, Accuracy: 40.33%
2025-08-01 11:53:33,070 - INFO - [Trainer 1] 开始第 2/2 个epoch
2025-08-01 11:53:33,074 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2589, y=[4, 8, 0, 8, 4]
2025-08-01 11:53:33,075 - INFO - [Trainer 1] Epoch 2 开始处理 10 个批次
2025-08-01 11:53:33,079 - INFO - [Trainer 1] Epoch 2 进度: 1/10 批次
2025-08-01 11:53:33,087 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1322
2025-08-01 11:53:33,087 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:53:33,087 - INFO - [Trainer 1] 标签样本: [0, 8, 0, 0, 4]
2025-08-01 11:53:33,154 - INFO - [Trainer 1] Batch 0, Loss: 1.6652
2025-08-01 11:53:34,046 - INFO - [Trainer 1] Batch 5, Loss: 1.6093
2025-08-01 11:53:34,722 - INFO - [Trainer 1] Epoch 2 进度: 10/10 批次
2025-08-01 11:53:34,735 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1210
2025-08-01 11:53:34,735 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:53:34,735 - INFO - [Trainer 1] 标签样本: [8, 0, 5, 8, 8]
2025-08-01 11:53:34,784 - INFO - [Trainer 1] Batch 9, Loss: 1.3059
2025-08-01 11:53:34,881 - INFO - [Trainer 1] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:53:34,882 - INFO - [Trainer 1] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.5483, Accuracy: 43.00%
2025-08-01 11:53:34,884 - INFO - [Trainer 1] 参数 conv1.weight: 平均值=0.002509, 标准差=0.113829
2025-08-01 11:53:34,884 - INFO - [Trainer 1] 参数 bn1.weight: 平均值=1.001023, 标准差=0.013515
2025-08-01 11:53:34,884 - INFO - [Trainer 1] 参数 bn1.bias: 平均值=0.000562, 标准差=0.009880
2025-08-01 11:53:34,885 - INFO - [Trainer 1] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:53:34,885 - INFO - [Trainer.get_report] 客户端 1 训练报告 - Loss: 1.5572, Accuracy: 41.67%, 陈旧度: 0
2025-08-01 11:53:34,885 - INFO - [Trainer 1] 训练报告生成完成: Loss=1.5572, Accuracy=41.67%
2025-08-01 11:53:34,885 - INFO - [Client 1] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:53:34,885 - INFO - [Client 1] 使用训练准确率作为客户端准确率: 0.4167
2025-08-01 11:53:34,885 - INFO - [Client 1] 第 1 轮训练完成，耗时: 3.89秒, 准确率: 0.4167
2025-08-01 11:53:34,885 - INFO - [Client 1] 开始提取模型权重
2025-08-01 11:53:34,886 - INFO - [Client 1] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:53:34,886 - INFO - [Client 1] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:53:34,886 - INFO - [Client 1] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:53:34,886 - INFO - [Client 1] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:53:34,886 - INFO - [Client 1] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:53:34,886 - INFO - [服务器] 🔄 收到客户端 1 的模型更新，模型版本: unknown
2025-08-01 11:53:34,886 - INFO - [服务器] 客户端 1 训练信息 - 样本数: 300, 训练时间: 3.89秒
2025-08-01 11:53:34,886 - INFO - [服务器] 当前缓冲池大小: 0, 全局轮次: 0
2025-08-01 11:53:34,888 - INFO - [客户端权重摘要] 客户端1 | 参数数量: 74, 均值: 0.001069, 最大: 20.000000, 最小: -0.273158
2025-08-01 11:53:34,889 - INFO - 客户端 1 更新记录 - 陈旧度: 0, 提交轮次: 0
2025-08-01 11:53:34,889 - INFO - ✅ 客户端 1 的更新已加入成功缓冲池，当前池大小: 1
2025-08-01 11:53:34,889 - INFO - 📊 当前缓冲池中的客户端: [1]
2025-08-01 11:53:34,889 - INFO - 🔍 客户端 1 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:53:34,889 - INFO - 成功处理客户端 1 的更新
2025-08-01 11:53:34,889 - INFO - [Client 1] ✅ 成功上传训练结果到服务器
2025-08-01 11:53:34,889 - INFO - 客户端 1 训练完成
2025-08-01 11:54:06,132 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:54:06,133 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 11:54:06,134 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 11:54:06,134 - INFO - [Client 1] 开始验证训练集
2025-08-01 11:54:06,142 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:54:06,142 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 11:54:06,142 - INFO - [Trainer 1] 开始训练
2025-08-01 11:54:06,142 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 11:54:06,143 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:54:06,144 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:54:06,144 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:54:06,144 - INFO - [Trainer 1] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:54:06,144 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 11:54:06,151 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2669, y=[0, 5, 4, 5, 5]
2025-08-01 11:54:06,151 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 11:54:06,155 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 11:54:06,165 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0051
2025-08-01 11:54:06,165 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:54:06,166 - INFO - [Trainer 1] 标签样本: [0, 5, 5, 4, 8]
2025-08-01 11:54:06,238 - INFO - [Trainer 1] Batch 0, Loss: 1.3521
2025-08-01 11:54:07,155 - INFO - [Trainer 1] Batch 5, Loss: 1.1299
2025-08-01 11:54:07,764 - INFO - [Trainer 1] Epoch 1 进度: 10/10 批次
2025-08-01 11:54:07,775 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2723
2025-08-01 11:54:07,775 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:54:07,775 - INFO - [Trainer 1] 标签样本: [0, 5, 0, 5, 0]
2025-08-01 11:54:07,830 - INFO - [Trainer 1] Batch 9, Loss: 0.7478
2025-08-01 11:54:07,892 - INFO - [Trainer 1] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:54:07,892 - INFO - [Trainer 1] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.1891, Accuracy: 51.00%
2025-08-01 11:54:07,892 - INFO - [Trainer 1] 开始第 2/2 个epoch
2025-08-01 11:54:07,897 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1982, y=[8, 8, 0, 4, 5]
2025-08-01 11:54:07,898 - INFO - [Trainer 1] Epoch 2 开始处理 10 个批次
2025-08-01 11:54:07,902 - INFO - [Trainer 1] Epoch 2 进度: 1/10 批次
2025-08-01 11:54:07,916 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7342, x.mean: -0.3931
2025-08-01 11:54:07,917 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:54:07,917 - INFO - [Trainer 1] 标签样本: [5, 5, 5, 4, 0]
2025-08-01 11:54:07,988 - INFO - [Trainer 1] Batch 0, Loss: 1.3259
2025-08-01 11:54:08,911 - INFO - [Trainer 1] Batch 5, Loss: 0.8077
2025-08-01 11:54:09,564 - INFO - [Trainer 1] Epoch 2 进度: 10/10 批次
2025-08-01 11:54:09,570 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7147, x.mean: -0.0044
2025-08-01 11:54:09,570 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:54:09,571 - INFO - [Trainer 1] 标签样本: [0, 8, 8, 0, 0]
2025-08-01 11:54:09,622 - INFO - [Trainer 1] Batch 9, Loss: 0.8314
2025-08-01 11:54:09,687 - INFO - [Trainer 1] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:54:09,687 - INFO - [Trainer 1] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.1193, Accuracy: 50.33%
2025-08-01 11:54:09,688 - INFO - [Trainer 1] 参数 conv1.weight: 平均值=0.001617, 标准差=0.114824
2025-08-01 11:54:09,688 - INFO - [Trainer 1] 参数 bn1.weight: 平均值=1.000437, 标准差=0.013016
2025-08-01 11:54:09,688 - INFO - [Trainer 1] 参数 bn1.bias: 平均值=-0.001434, 标准差=0.009891
2025-08-01 11:54:09,689 - INFO - [Trainer 1] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:54:09,689 - INFO - [Trainer.get_report] 客户端 1 训练报告 - Loss: 1.3557, Accuracy: 46.17%, 陈旧度: 0
2025-08-01 11:54:09,689 - INFO - [Trainer 1] 训练报告生成完成: Loss=1.3557, Accuracy=46.17%
2025-08-01 11:54:09,689 - INFO - [Client 1] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:54:09,689 - INFO - [Client 1] 使用训练准确率作为客户端准确率: 0.4617
2025-08-01 11:54:09,690 - INFO - [Client 1] 第 2 轮训练完成，耗时: 3.56秒, 准确率: 0.4617
2025-08-01 11:54:09,690 - INFO - [Client 1] 开始提取模型权重
2025-08-01 11:54:09,690 - INFO - [Client 1] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:54:09,690 - INFO - [Client 1] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:54:09,691 - INFO - [Client 1] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:54:09,691 - INFO - [Client 1] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:54:09,691 - INFO - [Client 1] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:54:09,691 - INFO - [服务器] 🔄 收到客户端 1 的模型更新，模型版本: unknown
2025-08-01 11:54:09,691 - INFO - [服务器] 客户端 1 训练信息 - 样本数: 300, 训练时间: 3.56秒
2025-08-01 11:54:09,692 - INFO - [服务器] 当前缓冲池大小: 1, 全局轮次: 0
2025-08-01 11:54:09,695 - INFO - [客户端权重摘要] 客户端1 | 参数数量: 74, 均值: 0.001184, 最大: 20.000000, 最小: -0.478740
2025-08-01 11:54:09,695 - INFO - 客户端 1 已有 1 个更新在缓冲池中，删除旧更新
2025-08-01 11:54:09,696 - INFO - 移除客户端 1 的旧更新，距今 34.8 秒
2025-08-01 11:54:09,696 - INFO - 客户端 1 更新记录 - 陈旧度: 0, 提交轮次: 0
2025-08-01 11:54:09,696 - INFO - ✅ 客户端 1 的更新已加入成功缓冲池，当前池大小: 1
2025-08-01 11:54:09,696 - INFO - 📊 当前缓冲池中的客户端: [1]
2025-08-01 11:54:09,697 - INFO - 🔍 客户端 1 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:54:09,697 - INFO - 成功处理客户端 1 的更新
2025-08-01 11:54:09,697 - INFO - [Client 1] ✅ 成功上传训练结果到服务器
2025-08-01 11:54:09,697 - INFO - 客户端 1 训练完成
2025-08-01 11:54:30,017 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:54:30,018 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 11:54:30,018 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 11:54:30,019 - INFO - [Client 1] 开始验证训练集
2025-08-01 11:54:30,020 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:54:30,020 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 11:54:30,020 - INFO - [Trainer 1] 开始训练
2025-08-01 11:54:30,020 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 11:54:30,021 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:54:30,021 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:54:30,022 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:54:30,023 - INFO - [Trainer 1] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:54:30,023 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 11:54:30,030 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2683, y=[5, 0, 8, 8, 0]
2025-08-01 11:54:30,030 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 11:54:30,035 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 11:54:30,048 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1132
2025-08-01 11:54:30,051 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:54:30,052 - INFO - [Trainer 1] 标签样本: [8, 0, 8, 8, 5]
2025-08-01 11:54:30,129 - INFO - [Trainer 1] Batch 0, Loss: 1.0372
2025-08-01 11:54:31,180 - INFO - [Trainer 1] Batch 5, Loss: 0.8425
2025-08-01 11:54:31,863 - INFO - [Trainer 1] Epoch 1 进度: 10/10 批次
2025-08-01 11:54:31,879 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0955
2025-08-01 11:54:31,880 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:54:31,880 - INFO - [Trainer 1] 标签样本: [4, 8, 5, 5, 0]
2025-08-01 11:54:31,938 - INFO - [Trainer 1] Batch 9, Loss: 1.1797
2025-08-01 11:54:31,994 - INFO - [Trainer 1] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:54:31,994 - INFO - [Trainer 1] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 1.1190, Accuracy: 50.67%
2025-08-01 11:54:31,994 - INFO - [Trainer 1] 开始第 2/2 个epoch
2025-08-01 11:54:32,000 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0152, y=[0, 5, 0, 5, 0]
2025-08-01 11:54:32,000 - INFO - [Trainer 1] Epoch 2 开始处理 10 个批次
2025-08-01 11:54:32,004 - INFO - [Trainer 1] Epoch 2 进度: 1/10 批次
2025-08-01 11:54:32,021 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1896
2025-08-01 11:54:32,021 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:54:32,022 - INFO - [Trainer 1] 标签样本: [4, 4, 8, 0, 5]
2025-08-01 11:54:32,100 - INFO - [Trainer 1] Batch 0, Loss: 0.8623
2025-08-01 11:54:33,057 - INFO - [Trainer 1] Batch 5, Loss: 1.0192
2025-08-01 11:54:33,706 - INFO - [Trainer 1] Epoch 2 进度: 10/10 批次
2025-08-01 11:54:33,721 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0442
2025-08-01 11:54:33,721 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:54:33,721 - INFO - [Trainer 1] 标签样本: [5, 0, 0, 0, 4]
2025-08-01 11:54:33,802 - INFO - [Trainer 1] Batch 9, Loss: 2.0324
2025-08-01 11:54:33,873 - INFO - [Trainer 1] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:54:33,873 - INFO - [Trainer 1] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 1.1230, Accuracy: 49.00%
2025-08-01 11:54:33,873 - INFO - [Trainer 1] 参数 conv1.weight: 平均值=0.001829, 标准差=0.115841
2025-08-01 11:54:33,874 - INFO - [Trainer 1] 参数 bn1.weight: 平均值=0.999781, 标准差=0.016266
2025-08-01 11:54:33,874 - INFO - [Trainer 1] 参数 bn1.bias: 平均值=-0.004609, 标准差=0.011560
2025-08-01 11:54:33,874 - INFO - [Trainer 1] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:54:33,874 - INFO - [Trainer.get_report] 客户端 1 训练报告 - Loss: 1.2775, Accuracy: 47.39%, 陈旧度: 0
2025-08-01 11:54:33,875 - INFO - [Trainer 1] 训练报告生成完成: Loss=1.2775, Accuracy=47.39%
2025-08-01 11:54:33,875 - INFO - [Client 1] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:54:33,875 - INFO - [Client 1] 使用训练准确率作为客户端准确率: 0.4739
2025-08-01 11:54:33,875 - INFO - [Client 1] 第 3 轮训练完成，耗时: 3.86秒, 准确率: 0.4739
2025-08-01 11:54:33,875 - INFO - [Client 1] 开始提取模型权重
2025-08-01 11:54:33,876 - INFO - [Client 1] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:54:33,876 - INFO - [Client 1] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:54:33,877 - INFO - [Client 1] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:54:33,877 - INFO - [Client 1] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:54:33,877 - INFO - [Client 1] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:54:33,877 - INFO - [服务器] 🔄 收到客户端 1 的模型更新，模型版本: unknown
2025-08-01 11:54:33,877 - INFO - [服务器] 客户端 1 训练信息 - 样本数: 300, 训练时间: 3.86秒
2025-08-01 11:54:33,877 - INFO - [服务器] 当前缓冲池大小: 1, 全局轮次: 0
2025-08-01 11:54:33,882 - INFO - [客户端权重摘要] 客户端1 | 参数数量: 74, 均值: 0.001245, 最大: 20.000000, 最小: -0.464959
2025-08-01 11:54:33,882 - INFO - 客户端 1 已有 1 个更新在缓冲池中，删除旧更新
2025-08-01 11:54:33,882 - INFO - 移除客户端 1 的旧更新，距今 24.2 秒
2025-08-01 11:54:33,883 - INFO - 客户端 1 更新记录 - 陈旧度: 0, 提交轮次: 0
2025-08-01 11:54:33,883 - INFO - ✅ 客户端 1 的更新已加入成功缓冲池，当前池大小: 1
2025-08-01 11:54:33,883 - INFO - 📊 当前缓冲池中的客户端: [1]
2025-08-01 11:54:33,883 - INFO - 🔍 客户端 1 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:54:33,883 - INFO - 成功处理客户端 1 的更新
2025-08-01 11:54:33,884 - INFO - [Client 1] ✅ 成功上传训练结果到服务器
2025-08-01 11:54:33,884 - INFO - 客户端 1 训练完成
2025-08-01 11:55:11,612 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:55:11,613 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 11:55:11,613 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 11:55:11,613 - INFO - [Client 1] 开始验证训练集
2025-08-01 11:55:11,621 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:55:11,622 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 11:55:11,622 - INFO - [Trainer 1] 开始训练
2025-08-01 11:55:11,622 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 11:55:11,623 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:55:11,623 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:55:11,624 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:55:11,624 - INFO - [Trainer 1] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:55:11,624 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 11:55:11,629 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3552, y=[8, 0, 8, 0, 5]
2025-08-01 11:55:11,629 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 11:55:11,634 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 11:55:11,641 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1714
2025-08-01 11:55:11,641 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:55:11,641 - INFO - [Trainer 1] 标签样本: [5, 5, 5, 5, 8]
2025-08-01 11:55:11,715 - INFO - [Trainer 1] Batch 0, Loss: 0.7395
2025-08-01 11:55:12,648 - INFO - [Trainer 1] Batch 5, Loss: 1.1006
2025-08-01 11:55:13,330 - INFO - [Trainer 1] Epoch 1 进度: 10/10 批次
2025-08-01 11:55:13,339 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1154
2025-08-01 11:55:13,340 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:55:13,340 - INFO - [Trainer 1] 标签样本: [8, 5, 0, 8, 5]
2025-08-01 11:55:13,395 - INFO - [Trainer 1] Batch 9, Loss: 0.6886
2025-08-01 11:55:13,484 - INFO - [Trainer 1] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:55:13,485 - INFO - [Trainer 1] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.9518, Accuracy: 57.33%
2025-08-01 11:55:13,485 - INFO - [Trainer 1] 开始第 2/2 个epoch
2025-08-01 11:55:13,492 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1373, y=[0, 8, 8, 5, 8]
2025-08-01 11:55:13,492 - INFO - [Trainer 1] Epoch 2 开始处理 10 个批次
2025-08-01 11:55:13,497 - INFO - [Trainer 1] Epoch 2 进度: 1/10 批次
2025-08-01 11:55:13,510 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0437
2025-08-01 11:55:13,510 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:55:13,510 - INFO - [Trainer 1] 标签样本: [0, 8, 8, 0, 5]
2025-08-01 11:55:13,587 - INFO - [Trainer 1] Batch 0, Loss: 0.8060
2025-08-01 11:55:14,535 - INFO - [Trainer 1] Batch 5, Loss: 1.0122
2025-08-01 11:55:15,262 - INFO - [Trainer 1] Epoch 2 进度: 10/10 批次
2025-08-01 11:55:15,274 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0280
2025-08-01 11:55:15,274 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:55:15,274 - INFO - [Trainer 1] 标签样本: [5, 8, 4, 0, 0]
2025-08-01 11:55:15,331 - INFO - [Trainer 1] Batch 9, Loss: 0.7863
2025-08-01 11:55:15,402 - INFO - [Trainer 1] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:55:15,403 - INFO - [Trainer 1] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.9208, Accuracy: 57.33%
2025-08-01 11:55:15,403 - INFO - [Trainer 1] 参数 conv1.weight: 平均值=0.000618, 标准差=0.117161
2025-08-01 11:55:15,403 - INFO - [Trainer 1] 参数 bn1.weight: 平均值=1.001093, 标准差=0.018837
2025-08-01 11:55:15,403 - INFO - [Trainer 1] 参数 bn1.bias: 平均值=-0.007979, 标准差=0.013668
2025-08-01 11:55:15,403 - INFO - [Trainer 1] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:55:15,403 - INFO - [Trainer.get_report] 客户端 1 训练报告 - Loss: 1.1922, Accuracy: 49.88%, 陈旧度: 0
2025-08-01 11:55:15,404 - INFO - [Trainer 1] 训练报告生成完成: Loss=1.1922, Accuracy=49.88%
2025-08-01 11:55:15,404 - INFO - [Client 1] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:55:15,404 - INFO - [Client 1] 使用训练准确率作为客户端准确率: 0.4988
2025-08-01 11:55:15,404 - INFO - [Client 1] 第 4 轮训练完成，耗时: 3.79秒, 准确率: 0.4988
2025-08-01 11:55:15,404 - INFO - [Client 1] 开始提取模型权重
2025-08-01 11:55:15,404 - INFO - [Client 1] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:55:15,404 - INFO - [Client 1] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:55:15,404 - INFO - [Client 1] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:55:15,404 - INFO - [Client 1] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:55:15,404 - INFO - [Client 1] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:55:15,405 - INFO - [服务器] 🔄 收到客户端 1 的模型更新，模型版本: unknown
2025-08-01 11:55:15,405 - INFO - [服务器] 客户端 1 训练信息 - 样本数: 300, 训练时间: 3.79秒
2025-08-01 11:55:15,405 - INFO - [服务器] 当前缓冲池大小: 1, 全局轮次: 0
2025-08-01 11:55:15,407 - INFO - [客户端权重摘要] 客户端1 | 参数数量: 74, 均值: 0.001228, 最大: 20.000000, 最小: -0.470685
2025-08-01 11:55:15,407 - INFO - 客户端 1 已有 1 个更新在缓冲池中，删除旧更新
2025-08-01 11:55:15,408 - INFO - 移除客户端 1 的旧更新，距今 41.5 秒
2025-08-01 11:55:15,408 - INFO - 客户端 1 更新记录 - 陈旧度: 0, 提交轮次: 0
2025-08-01 11:55:15,408 - INFO - ✅ 客户端 1 的更新已加入成功缓冲池，当前池大小: 1
2025-08-01 11:55:15,408 - INFO - 📊 当前缓冲池中的客户端: [1]
2025-08-01 11:55:15,408 - INFO - 🔍 客户端 1 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:55:15,408 - INFO - 成功处理客户端 1 的更新
2025-08-01 11:55:15,408 - INFO - [Client 1] ✅ 成功上传训练结果到服务器
2025-08-01 11:55:15,408 - INFO - 客户端 1 训练完成
2025-08-01 11:55:49,184 - DEBUG - Using proactor: IocpProactor
2025-08-01 11:55:49,184 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 11:55:49,185 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 11:55:49,185 - INFO - [Client 1] 开始验证训练集
2025-08-01 11:55:49,194 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 11:55:49,194 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 11:55:49,194 - INFO - [Trainer 1] 开始训练
2025-08-01 11:55:49,194 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 11:55:49,195 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 11:55:49,195 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 11:55:49,195 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 11:55:49,196 - INFO - [Trainer 1] 开始训练 2 个epoch，已优化BatchNorm设置
2025-08-01 11:55:49,196 - INFO - [Trainer 1] 开始第 1/2 个epoch
2025-08-01 11:55:49,200 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1420, y=[5, 8, 5, 0, 4]
2025-08-01 11:55:49,201 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 11:55:49,206 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 11:55:49,210 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1922
2025-08-01 11:55:49,210 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:55:49,210 - INFO - [Trainer 1] 标签样本: [8, 8, 8, 4, 8]
2025-08-01 11:55:49,299 - INFO - [Trainer 1] Batch 0, Loss: 0.9348
2025-08-01 11:55:50,291 - INFO - [Trainer 1] Batch 5, Loss: 0.9696
2025-08-01 11:55:50,955 - INFO - [Trainer 1] Epoch 1 进度: 10/10 批次
2025-08-01 11:55:50,957 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2882
2025-08-01 11:55:50,958 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:55:50,958 - INFO - [Trainer 1] 标签样本: [4, 5, 0, 4, 5]
2025-08-01 11:55:51,005 - INFO - [Trainer 1] Batch 9, Loss: 0.8751
2025-08-01 11:55:51,083 - INFO - [Trainer 1] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 11:55:51,083 - INFO - [Trainer 1] Epoch 1/2 完成 - 处理了 10 个批次, Loss: 0.9037, Accuracy: 60.67%
2025-08-01 11:55:51,083 - INFO - [Trainer 1] 开始第 2/2 个epoch
2025-08-01 11:55:51,092 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1530, y=[0, 0, 0, 0, 0]
2025-08-01 11:55:51,093 - INFO - [Trainer 1] Epoch 2 开始处理 10 个批次
2025-08-01 11:55:51,099 - INFO - [Trainer 1] Epoch 2 进度: 1/10 批次
2025-08-01 11:55:51,113 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0048
2025-08-01 11:55:51,114 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:55:51,114 - INFO - [Trainer 1] 标签样本: [5, 8, 8, 5, 5]
2025-08-01 11:55:51,193 - INFO - [Trainer 1] Batch 0, Loss: 0.7014
2025-08-01 11:55:52,107 - INFO - [Trainer 1] Batch 5, Loss: 0.9802
2025-08-01 11:55:52,808 - INFO - [Trainer 1] Epoch 2 进度: 10/10 批次
2025-08-01 11:55:52,811 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4216
2025-08-01 11:55:52,811 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 11:55:52,811 - INFO - [Trainer 1] 标签样本: [5, 5, 5, 0, 5]
2025-08-01 11:55:52,855 - INFO - [Trainer 1] Batch 9, Loss: 1.2722
2025-08-01 11:55:52,936 - INFO - [Trainer 1] Epoch 2 批次循环完成，处理了 10 个批次
2025-08-01 11:55:52,936 - INFO - [Trainer 1] Epoch 2/2 完成 - 处理了 10 个批次, Loss: 0.9759, Accuracy: 55.00%
2025-08-01 11:55:52,936 - INFO - [Trainer 1] 参数 conv1.weight: 平均值=0.002098, 标准差=0.117607
2025-08-01 11:55:52,936 - INFO - [Trainer 1] 参数 bn1.weight: 平均值=1.001517, 标准差=0.020190
2025-08-01 11:55:52,936 - INFO - [Trainer 1] 参数 bn1.bias: 平均值=-0.008357, 标准差=0.013434
2025-08-01 11:55:52,937 - INFO - [Trainer 1] 🎉 训练完成！总共 2 个epoch，38 个参数
2025-08-01 11:55:52,937 - INFO - [Trainer.get_report] 客户端 1 训练报告 - Loss: 1.1417, Accuracy: 51.47%, 陈旧度: 0
2025-08-01 11:55:52,937 - INFO - [Trainer 1] 训练报告生成完成: Loss=1.1417, Accuracy=51.47%
2025-08-01 11:55:52,937 - INFO - [Client 1] ✅ 训练器训练完成，获得报告: True
2025-08-01 11:55:52,937 - INFO - [Client 1] 使用训练准确率作为客户端准确率: 0.5147
2025-08-01 11:55:52,937 - INFO - [Client 1] 第 5 轮训练完成，耗时: 3.75秒, 准确率: 0.5147
2025-08-01 11:55:52,937 - INFO - [Client 1] 开始提取模型权重
2025-08-01 11:55:52,937 - INFO - [Client 1] ✅ 成功提取模型权重，权重数量: 74
2025-08-01 11:55:52,938 - INFO - [Client 1] 权重样本: ['conv1.weight: torch.Size([64, 3, 3, 3])', 'bn1.weight: torch.Size([64])', 'bn1.bias: torch.Size([64])']
2025-08-01 11:55:52,938 - INFO - [Client 1] 配置检查 - synchronous_mode: False, 有服务器引用: True
2025-08-01 11:55:52,938 - INFO - [Client 1] 🚀 异步模式，训练完成后立即上传结果
2025-08-01 11:55:52,938 - INFO - [Client 1] 正在调用服务器的 receive_update_from_client 方法...
2025-08-01 11:55:52,938 - INFO - [服务器] 🔄 收到客户端 1 的模型更新，模型版本: unknown
2025-08-01 11:55:52,938 - INFO - [服务器] 客户端 1 训练信息 - 样本数: 300, 训练时间: 3.75秒
2025-08-01 11:55:52,938 - INFO - [服务器] 当前缓冲池大小: 1, 全局轮次: 0
2025-08-01 11:55:52,941 - INFO - [客户端权重摘要] 客户端1 | 参数数量: 74, 均值: 0.001226, 最大: 20.000000, 最小: -0.483173
2025-08-01 11:55:52,941 - INFO - 客户端 1 已有 1 个更新在缓冲池中，删除旧更新
2025-08-01 11:55:52,941 - INFO - 移除客户端 1 的旧更新，距今 37.5 秒
2025-08-01 11:55:52,941 - INFO - 客户端 1 更新记录 - 陈旧度: 0, 提交轮次: 0
2025-08-01 11:55:52,942 - INFO - ✅ 客户端 1 的更新已加入成功缓冲池，当前池大小: 1
2025-08-01 11:55:52,942 - INFO - 📊 当前缓冲池中的客户端: [1]
2025-08-01 11:55:52,942 - INFO - 🔍 客户端 1 更新处理完成，等待主循环检查聚合条件...
2025-08-01 11:55:52,942 - INFO - 成功处理客户端 1 的更新
2025-08-01 11:55:52,942 - INFO - [Client 1] ✅ 成功上传训练结果到服务器
2025-08-01 11:55:52,942 - INFO - 客户端 1 训练完成
