2025-08-01 00:10:26,565 - INFO - 🚀 SC-AFL服务器启动 - 2025-08-01 00:10:26
2025-08-01 00:10:26,565 - INFO - ✅ 新日志文件已创建
2025-08-01 00:10:26,565 - INFO - 📁 日志目录: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs
2025-08-01 00:10:26,565 - INFO - 📄 日志文件: D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\logs\sc_afl_server_20250801_001026_10168.log
2025-08-01 00:10:26,565 - INFO - 🔧 日志级别: DEBUG (文件), INFO (控制台)
2025-08-01 00:10:26,565 - INFO - 📊 文件模式: 新建模式 (每次启动创建新文件)
2025-08-01 00:10:26,565 - INFO - 🆔 进程ID: 10168
2025-08-01 00:10:26,566 - INFO - ✅ 日志文件创建成功，当前大小: 675 字节
2025-08-01 00:10:26,566 - INFO - 🚀 SC-AFL服务器初始化开始...
2025-08-01 00:10:26,566 - INFO - 📅 初始化时间: 2025-08-01 00:10:26
2025-08-01 00:10:26,586 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:26,586 - INFO - Server: 动态创建模型 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 00:10:26,586 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:26,598 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:26,599 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:26,599 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:26,599 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:26,600 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:26,600 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:26,600 - INFO - Server: 创建了新的Trainer实例
2025-08-01 00:10:26,600 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:26,600 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:26,600 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:26,601 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:26,601 - INFO - Server: 创建了新的Algorithm实例
2025-08-01 00:10:26,601 - INFO - [93m[1m[10168] Logging runtime results to: ./results/cifar10_with_network/10168.csv.[0m
2025-08-01 00:10:26,602 - INFO - [Server #10168] Started training on 50 clients with 20 per round.
2025-08-01 00:10:26,602 - INFO - [DEBUG] 从配置文件读取 simulate_wall_time=True
2025-08-01 00:10:26,602 - WARNING - Server: super().__init__后发现self.algorithm引用被改变或为None，正在恢复/重新设置。
2025-08-01 00:10:26,602 - WARNING - Server: 训练器在初始化后为None，重新创建
2025-08-01 00:10:26,602 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:26,602 - WARNING - [Trainer None] 模型为None，尝试创建默认模型
2025-08-01 00:10:26,617 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:26,617 - INFO - [Trainer None] 动态创建模型 resnet_9，输入通道: 3, 类别数: 10
2025-08-01 00:10:26,624 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:26,624 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:26,625 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:26,625 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:26,625 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:26,626 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:26,626 - INFO - Server: 重新创建了Trainer实例
2025-08-01 00:10:26,626 - INFO - [Algorithm] 已设置服务器引用 (客户端ID: None)
2025-08-01 00:10:26,626 - INFO - Server: 算法类已设置服务器引用
2025-08-01 00:10:26,626 - INFO - 动态加载数据集: CIFAR10
2025-08-01 00:10:27,127 - INFO - ✅ 成功加载数据集 CIFAR10: 10000 样本
2025-08-01 00:10:27,127 - INFO - ✅ 动态加载测试集成功: CIFAR10, 大小: 10000
2025-08-01 00:10:27,127 - INFO - ✅ 测试加载器已创建
2025-08-01 00:10:27,127 - INFO - 开始初始化全局模型权重
2025-08-01 00:10:27,127 - WARNING - 全局模型实例为None，正在尝试重新创建...
2025-08-01 00:10:27,140 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:27,141 - INFO - 成功重新创建了全局模型实例 resnet_9，数据集: CIFAR10, 输入通道数: 3, 类别数: 10
2025-08-01 00:10:27,149 - INFO - [全局权重摘要] 参数数量: 74, 均值: 0.001175, 最大: 1.000000, 最小: -0.192282
2025-08-01 00:10:27,149 - INFO - [全局模型] 输入通道数: 3
2025-08-01 00:10:27,149 - INFO - 全局模型权重初始化成功
2025-08-01 00:10:27,150 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:10:27,150 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 00:10:27,150 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 00:10:27,150 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 00:10:27,152 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_001027.csv
2025-08-01 00:10:27,152 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 00:10:27,152 - WARNING - 网络模拟器初始化失败: 'Config' object has no attribute 'get'
2025-08-01 00:10:27,152 - INFO - SC-AFL算法参数: tau_max=5, V=1.0
2025-08-01 00:10:27,153 - INFO - 服务器初始化完成
2025-08-01 00:10:27,153 - INFO - 已创建并注册 0 个客户端（将在 Server.start() 中启动任务）
2025-08-01 00:10:27,153 - INFO - 客户端ID管理器初始化完成，总客户端数: 50, ID起始值: 1
2025-08-01 00:10:27,153 - INFO - 使用结果路径: ./results/cifar10_with_network
2025-08-01 00:10:27,153 - INFO - 结果类型: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness, max_staleness, min_staleness, virtual_time, aggregated_clients_count
2025-08-01 00:10:27,153 - INFO - 从命令行获取配置文件名: sc_afl_cifar10_resnet9_with_network
2025-08-01 00:10:27,154 - INFO - 初始化结果文件: ./results/cifar10_with_network/sc_afl_cifar10_resnet9_with_network_20250801_001027.csv
2025-08-01 00:10:27,154 - INFO - 记录字段: ['round', 'elapsed_time', 'accuracy', 'global_accuracy', 'global_accuracy_std', 'avg_staleness', 'max_staleness', 'min_staleness', 'virtual_time', 'aggregated_clients_count']
2025-08-01 00:10:27,154 - INFO - 服务器实例创建成功
2025-08-01 00:10:27,154 - INFO - 正在创建和注册 50 个客户端...
2025-08-01 00:10:27,154 - INFO - 客户端ID配置: 起始ID=1, 总数=50
2025-08-01 00:10:27,154 - INFO - 开始创建客户端 1...
2025-08-01 00:10:27,154 - INFO - 初始化客户端, ID: 1
2025-08-01 00:10:27,172 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:27,172 - INFO - [Client 1] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:27,172 - INFO - [Trainer] 初始化训练器, client_id: 1
2025-08-01 00:10:27,183 - INFO - [Trainer 1] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:27,184 - INFO - [Trainer 1] 模型的输入通道数: 3
2025-08-01 00:10:27,184 - INFO - [Trainer 1] 强制使用CPU
2025-08-01 00:10:27,184 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 00:10:27,185 - INFO - [Trainer 1] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:27,185 - INFO - [Trainer 1] 初始化完成
2025-08-01 00:10:27,185 - INFO - [Client 1] 创建新训练器
2025-08-01 00:10:27,185 - INFO - [Algorithm] 从训练器获取客户端ID: 1
2025-08-01 00:10:27,186 - INFO - [Algorithm] 初始化后修正client_id: 1
2025-08-01 00:10:27,186 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:27,186 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:27,186 - INFO - [Client 1] 创建新算法
2025-08-01 00:10:27,186 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 00:10:27,186 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 00:10:27,187 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:27,202 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:27,202 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:27,203 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:27,211 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:27,211 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:27,212 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:27,212 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:27,212 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:27,213 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:27,213 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:27,213 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:27,213 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:27,213 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:27,213 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:27,213 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:27,214 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:27,214 - INFO - 顺序分配客户端ID: 1
2025-08-01 00:10:27,214 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 1
2025-08-01 00:10:27,214 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:27,215 - WARNING - [Client 1] 数据源为None，已创建新数据源
2025-08-01 00:10:27,215 - WARNING - [Client 1] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:27,816 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:27,817 - INFO - [Client 1] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:27,817 - INFO - [Client 1] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:27,835 - INFO - [Client 1] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:27,835 - INFO - [Client 1] 初始化时成功加载数据
2025-08-01 00:10:27,835 - INFO - [客户端 1] 初始化验证通过
2025-08-01 00:10:27,835 - INFO - 客户端 1 实例创建成功
2025-08-01 00:10:27,835 - INFO - 客户端1已设置服务器引用
2025-08-01 00:10:27,835 - INFO - 客户端 1 已设置服务器引用
2025-08-01 00:10:27,835 - INFO - 客户端1已注册
2025-08-01 00:10:27,835 - INFO - 客户端 1 已成功注册到服务器
2025-08-01 00:10:27,835 - INFO - 开始创建客户端 2...
2025-08-01 00:10:27,836 - INFO - 初始化客户端, ID: 2
2025-08-01 00:10:27,854 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:27,854 - INFO - [Client 2] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:27,855 - INFO - [Trainer] 初始化训练器, client_id: 2
2025-08-01 00:10:27,861 - INFO - [Trainer 2] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:27,861 - INFO - [Trainer 2] 模型的输入通道数: 3
2025-08-01 00:10:27,861 - INFO - [Trainer 2] 强制使用CPU
2025-08-01 00:10:27,862 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 00:10:27,862 - INFO - [Trainer 2] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:27,862 - INFO - [Trainer 2] 初始化完成
2025-08-01 00:10:27,862 - INFO - [Client 2] 创建新训练器
2025-08-01 00:10:27,862 - INFO - [Algorithm] 从训练器获取客户端ID: 2
2025-08-01 00:10:27,862 - INFO - [Algorithm] 初始化后修正client_id: 2
2025-08-01 00:10:27,862 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:27,862 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:27,862 - INFO - [Client 2] 创建新算法
2025-08-01 00:10:27,863 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 00:10:27,863 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 00:10:27,863 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:27,879 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:27,879 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:27,879 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:27,885 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:27,885 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:27,885 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:27,886 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:27,886 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:27,886 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:27,886 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:27,886 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:27,886 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:27,886 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:27,886 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:27,887 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:27,887 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:27,887 - INFO - 顺序分配客户端ID: 2
2025-08-01 00:10:27,887 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 2
2025-08-01 00:10:27,887 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:27,887 - WARNING - [Client 2] 数据源为None，已创建新数据源
2025-08-01 00:10:27,887 - WARNING - [Client 2] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:28,481 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:28,481 - INFO - [Client 2] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:28,481 - INFO - [Client 2] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:28,485 - INFO - [Client 2] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:28,485 - INFO - [Client 2] 初始化时成功加载数据
2025-08-01 00:10:28,485 - INFO - [客户端 2] 初始化验证通过
2025-08-01 00:10:28,485 - INFO - 客户端 2 实例创建成功
2025-08-01 00:10:28,485 - INFO - 客户端2已设置服务器引用
2025-08-01 00:10:28,486 - INFO - 客户端 2 已设置服务器引用
2025-08-01 00:10:28,486 - INFO - 客户端2已注册
2025-08-01 00:10:28,486 - INFO - 客户端 2 已成功注册到服务器
2025-08-01 00:10:28,486 - INFO - 开始创建客户端 3...
2025-08-01 00:10:28,486 - INFO - 初始化客户端, ID: 3
2025-08-01 00:10:28,502 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:28,503 - INFO - [Client 3] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:28,503 - INFO - [Trainer] 初始化训练器, client_id: 3
2025-08-01 00:10:28,509 - INFO - [Trainer 3] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:28,509 - INFO - [Trainer 3] 模型的输入通道数: 3
2025-08-01 00:10:28,510 - INFO - [Trainer 3] 强制使用CPU
2025-08-01 00:10:28,510 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 00:10:28,510 - INFO - [Trainer 3] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:28,510 - INFO - [Trainer 3] 初始化完成
2025-08-01 00:10:28,510 - INFO - [Client 3] 创建新训练器
2025-08-01 00:10:28,510 - INFO - [Algorithm] 从训练器获取客户端ID: 3
2025-08-01 00:10:28,511 - INFO - [Algorithm] 初始化后修正client_id: 3
2025-08-01 00:10:28,511 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:28,511 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:28,511 - INFO - [Client 3] 创建新算法
2025-08-01 00:10:28,511 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 00:10:28,511 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 00:10:28,511 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:28,526 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:28,526 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:28,526 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:28,532 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:28,532 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:28,532 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:28,532 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:28,533 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:28,533 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:28,533 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:28,533 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:28,533 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:28,533 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:28,533 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:28,533 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:28,533 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:28,533 - INFO - 顺序分配客户端ID: 3
2025-08-01 00:10:28,533 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 3
2025-08-01 00:10:28,534 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:28,534 - WARNING - [Client 3] 数据源为None，已创建新数据源
2025-08-01 00:10:28,534 - WARNING - [Client 3] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:29,122 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:29,122 - INFO - [Client 3] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:29,122 - INFO - [Client 3] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:29,126 - INFO - [Client 3] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:29,126 - INFO - [Client 3] 初始化时成功加载数据
2025-08-01 00:10:29,126 - INFO - [客户端 3] 初始化验证通过
2025-08-01 00:10:29,126 - INFO - 客户端 3 实例创建成功
2025-08-01 00:10:29,126 - INFO - 客户端3已设置服务器引用
2025-08-01 00:10:29,126 - INFO - 客户端 3 已设置服务器引用
2025-08-01 00:10:29,126 - INFO - 客户端3已注册
2025-08-01 00:10:29,126 - INFO - 客户端 3 已成功注册到服务器
2025-08-01 00:10:29,126 - INFO - 开始创建客户端 4...
2025-08-01 00:10:29,126 - INFO - 初始化客户端, ID: 4
2025-08-01 00:10:29,144 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:29,144 - INFO - [Client 4] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:29,145 - INFO - [Trainer] 初始化训练器, client_id: 4
2025-08-01 00:10:29,151 - INFO - [Trainer 4] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:29,151 - INFO - [Trainer 4] 模型的输入通道数: 3
2025-08-01 00:10:29,151 - INFO - [Trainer 4] 强制使用CPU
2025-08-01 00:10:29,151 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 00:10:29,151 - INFO - [Trainer 4] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:29,152 - INFO - [Trainer 4] 初始化完成
2025-08-01 00:10:29,152 - INFO - [Client 4] 创建新训练器
2025-08-01 00:10:29,152 - INFO - [Algorithm] 从训练器获取客户端ID: 4
2025-08-01 00:10:29,152 - INFO - [Algorithm] 初始化后修正client_id: 4
2025-08-01 00:10:29,152 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:29,152 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:29,152 - INFO - [Client 4] 创建新算法
2025-08-01 00:10:29,152 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 00:10:29,152 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 00:10:29,152 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:29,167 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:29,167 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:29,168 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:29,173 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:29,173 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:29,174 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:29,174 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:29,174 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:29,174 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:29,174 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:29,174 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:29,174 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:29,174 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:29,175 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:29,175 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:29,175 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:29,175 - INFO - 顺序分配客户端ID: 4
2025-08-01 00:10:29,175 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 4
2025-08-01 00:10:29,175 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:29,175 - WARNING - [Client 4] 数据源为None，已创建新数据源
2025-08-01 00:10:29,175 - WARNING - [Client 4] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:29,774 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:29,775 - INFO - [Client 4] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:29,775 - INFO - [Client 4] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:29,779 - INFO - [Client 4] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:29,779 - INFO - [Client 4] 初始化时成功加载数据
2025-08-01 00:10:29,779 - INFO - [客户端 4] 初始化验证通过
2025-08-01 00:10:29,779 - INFO - 客户端 4 实例创建成功
2025-08-01 00:10:29,779 - INFO - 客户端4已设置服务器引用
2025-08-01 00:10:29,779 - INFO - 客户端 4 已设置服务器引用
2025-08-01 00:10:29,780 - INFO - 客户端4已注册
2025-08-01 00:10:29,780 - INFO - 客户端 4 已成功注册到服务器
2025-08-01 00:10:29,780 - INFO - 开始创建客户端 5...
2025-08-01 00:10:29,780 - INFO - 初始化客户端, ID: 5
2025-08-01 00:10:29,797 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:29,797 - INFO - [Client 5] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:29,797 - INFO - [Trainer] 初始化训练器, client_id: 5
2025-08-01 00:10:29,805 - INFO - [Trainer 5] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:29,805 - INFO - [Trainer 5] 模型的输入通道数: 3
2025-08-01 00:10:29,806 - INFO - [Trainer 5] 强制使用CPU
2025-08-01 00:10:29,806 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 00:10:29,807 - INFO - [Trainer 5] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:29,807 - INFO - [Trainer 5] 初始化完成
2025-08-01 00:10:29,807 - INFO - [Client 5] 创建新训练器
2025-08-01 00:10:29,807 - INFO - [Algorithm] 从训练器获取客户端ID: 5
2025-08-01 00:10:29,807 - INFO - [Algorithm] 初始化后修正client_id: 5
2025-08-01 00:10:29,807 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:29,807 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:29,808 - INFO - [Client 5] 创建新算法
2025-08-01 00:10:29,808 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 00:10:29,808 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 00:10:29,808 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:29,823 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:29,824 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:29,824 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:29,830 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:29,830 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:29,830 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:29,830 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:29,831 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:29,831 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:29,831 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:29,831 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:29,831 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:29,831 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:29,831 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:29,832 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:29,832 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:29,832 - INFO - 顺序分配客户端ID: 5
2025-08-01 00:10:29,832 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 5
2025-08-01 00:10:29,832 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:29,832 - WARNING - [Client 5] 数据源为None，已创建新数据源
2025-08-01 00:10:29,832 - WARNING - [Client 5] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:30,405 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:30,406 - INFO - [Client 5] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:30,406 - INFO - [Client 5] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:30,410 - INFO - [Client 5] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:30,410 - INFO - [Client 5] 初始化时成功加载数据
2025-08-01 00:10:30,410 - INFO - [客户端 5] 初始化验证通过
2025-08-01 00:10:30,410 - INFO - 客户端 5 实例创建成功
2025-08-01 00:10:30,411 - INFO - 客户端5已设置服务器引用
2025-08-01 00:10:30,411 - INFO - 客户端 5 已设置服务器引用
2025-08-01 00:10:30,411 - INFO - 客户端5已注册
2025-08-01 00:10:30,411 - INFO - 客户端 5 已成功注册到服务器
2025-08-01 00:10:30,411 - INFO - 开始创建客户端 6...
2025-08-01 00:10:30,411 - INFO - 初始化客户端, ID: 6
2025-08-01 00:10:30,429 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:30,429 - INFO - [Client 6] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:30,429 - INFO - [Trainer] 初始化训练器, client_id: 6
2025-08-01 00:10:30,437 - INFO - [Trainer 6] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:30,437 - INFO - [Trainer 6] 模型的输入通道数: 3
2025-08-01 00:10:30,438 - INFO - [Trainer 6] 强制使用CPU
2025-08-01 00:10:30,438 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 00:10:30,438 - INFO - [Trainer 6] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:30,438 - INFO - [Trainer 6] 初始化完成
2025-08-01 00:10:30,438 - INFO - [Client 6] 创建新训练器
2025-08-01 00:10:30,438 - INFO - [Algorithm] 从训练器获取客户端ID: 6
2025-08-01 00:10:30,438 - INFO - [Algorithm] 初始化后修正client_id: 6
2025-08-01 00:10:30,438 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:30,439 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:30,439 - INFO - [Client 6] 创建新算法
2025-08-01 00:10:30,439 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 00:10:30,439 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 00:10:30,439 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:30,456 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:30,456 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:30,457 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:30,462 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:30,463 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:30,463 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:30,463 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:30,463 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:30,463 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:30,463 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:30,464 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:30,464 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:30,464 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:30,464 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:30,464 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:30,464 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:30,464 - INFO - 顺序分配客户端ID: 6
2025-08-01 00:10:30,464 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 6
2025-08-01 00:10:30,465 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:30,465 - WARNING - [Client 6] 数据源为None，已创建新数据源
2025-08-01 00:10:30,465 - WARNING - [Client 6] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:31,037 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:31,037 - INFO - [Client 6] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:31,037 - INFO - [Client 6] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:31,042 - INFO - [Client 6] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:31,042 - INFO - [Client 6] 初始化时成功加载数据
2025-08-01 00:10:31,042 - INFO - [客户端 6] 初始化验证通过
2025-08-01 00:10:31,043 - INFO - 客户端 6 实例创建成功
2025-08-01 00:10:31,043 - INFO - 客户端6已设置服务器引用
2025-08-01 00:10:31,043 - INFO - 客户端 6 已设置服务器引用
2025-08-01 00:10:31,043 - INFO - 客户端6已注册
2025-08-01 00:10:31,043 - INFO - 客户端 6 已成功注册到服务器
2025-08-01 00:10:31,043 - INFO - 开始创建客户端 7...
2025-08-01 00:10:31,043 - INFO - 初始化客户端, ID: 7
2025-08-01 00:10:31,061 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:31,061 - INFO - [Client 7] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:31,061 - INFO - [Trainer] 初始化训练器, client_id: 7
2025-08-01 00:10:31,068 - INFO - [Trainer 7] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:31,069 - INFO - [Trainer 7] 模型的输入通道数: 3
2025-08-01 00:10:31,069 - INFO - [Trainer 7] 强制使用CPU
2025-08-01 00:10:31,069 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 00:10:31,069 - INFO - [Trainer 7] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:31,070 - INFO - [Trainer 7] 初始化完成
2025-08-01 00:10:31,070 - INFO - [Client 7] 创建新训练器
2025-08-01 00:10:31,070 - INFO - [Algorithm] 从训练器获取客户端ID: 7
2025-08-01 00:10:31,070 - INFO - [Algorithm] 初始化后修正client_id: 7
2025-08-01 00:10:31,070 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:31,070 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:31,070 - INFO - [Client 7] 创建新算法
2025-08-01 00:10:31,070 - INFO - [Algorithm] 设置客户端ID: 7
2025-08-01 00:10:31,070 - INFO - [Algorithm] 同步更新trainer的client_id: 7
2025-08-01 00:10:31,070 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:31,086 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:31,086 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:31,086 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:31,092 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:31,092 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:31,092 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:31,092 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:31,092 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:31,092 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:31,092 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:31,092 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:31,093 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:31,093 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:31,093 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:31,093 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:31,093 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:31,093 - INFO - 顺序分配客户端ID: 7
2025-08-01 00:10:31,093 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 7
2025-08-01 00:10:31,093 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:31,093 - WARNING - [Client 7] 数据源为None，已创建新数据源
2025-08-01 00:10:31,093 - WARNING - [Client 7] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:31,679 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:31,679 - INFO - [Client 7] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:31,679 - INFO - [Client 7] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:31,683 - INFO - [Client 7] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:31,684 - INFO - [Client 7] 初始化时成功加载数据
2025-08-01 00:10:31,684 - INFO - [客户端 7] 初始化验证通过
2025-08-01 00:10:31,684 - INFO - 客户端 7 实例创建成功
2025-08-01 00:10:31,684 - INFO - 客户端7已设置服务器引用
2025-08-01 00:10:31,684 - INFO - 客户端 7 已设置服务器引用
2025-08-01 00:10:31,684 - INFO - 客户端7已注册
2025-08-01 00:10:31,685 - INFO - 客户端 7 已成功注册到服务器
2025-08-01 00:10:31,685 - INFO - 开始创建客户端 8...
2025-08-01 00:10:31,685 - INFO - 初始化客户端, ID: 8
2025-08-01 00:10:31,819 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:31,819 - INFO - [Client 8] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:31,820 - INFO - [Trainer] 初始化训练器, client_id: 8
2025-08-01 00:10:31,825 - INFO - [Trainer 8] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:31,826 - INFO - [Trainer 8] 模型的输入通道数: 3
2025-08-01 00:10:31,826 - INFO - [Trainer 8] 强制使用CPU
2025-08-01 00:10:31,826 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 00:10:31,826 - INFO - [Trainer 8] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:31,826 - INFO - [Trainer 8] 初始化完成
2025-08-01 00:10:31,826 - INFO - [Client 8] 创建新训练器
2025-08-01 00:10:31,826 - INFO - [Algorithm] 从训练器获取客户端ID: 8
2025-08-01 00:10:31,826 - INFO - [Algorithm] 初始化后修正client_id: 8
2025-08-01 00:10:31,826 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:31,827 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:31,827 - INFO - [Client 8] 创建新算法
2025-08-01 00:10:31,827 - INFO - [Algorithm] 设置客户端ID: 8
2025-08-01 00:10:31,827 - INFO - [Algorithm] 同步更新trainer的client_id: 8
2025-08-01 00:10:31,827 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:31,842 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:31,842 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:31,843 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:31,849 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:31,849 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:31,849 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:31,850 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:31,850 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:31,850 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:31,850 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:31,850 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:31,850 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:31,850 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:31,850 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:31,850 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:31,850 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:31,850 - INFO - 顺序分配客户端ID: 8
2025-08-01 00:10:31,850 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 8
2025-08-01 00:10:31,851 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:31,851 - WARNING - [Client 8] 数据源为None，已创建新数据源
2025-08-01 00:10:31,851 - WARNING - [Client 8] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:32,461 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:32,461 - INFO - [Client 8] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:32,461 - INFO - [Client 8] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:32,465 - INFO - [Client 8] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:32,465 - INFO - [Client 8] 初始化时成功加载数据
2025-08-01 00:10:32,465 - INFO - [客户端 8] 初始化验证通过
2025-08-01 00:10:32,465 - INFO - 客户端 8 实例创建成功
2025-08-01 00:10:32,465 - INFO - 客户端8已设置服务器引用
2025-08-01 00:10:32,465 - INFO - 客户端 8 已设置服务器引用
2025-08-01 00:10:32,466 - INFO - 客户端8已注册
2025-08-01 00:10:32,466 - INFO - 客户端 8 已成功注册到服务器
2025-08-01 00:10:32,466 - INFO - 开始创建客户端 9...
2025-08-01 00:10:32,466 - INFO - 初始化客户端, ID: 9
2025-08-01 00:10:32,482 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:32,482 - INFO - [Client 9] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:32,482 - INFO - [Trainer] 初始化训练器, client_id: 9
2025-08-01 00:10:32,488 - INFO - [Trainer 9] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:32,489 - INFO - [Trainer 9] 模型的输入通道数: 3
2025-08-01 00:10:32,489 - INFO - [Trainer 9] 强制使用CPU
2025-08-01 00:10:32,489 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 00:10:32,489 - INFO - [Trainer 9] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:32,490 - INFO - [Trainer 9] 初始化完成
2025-08-01 00:10:32,490 - INFO - [Client 9] 创建新训练器
2025-08-01 00:10:32,490 - INFO - [Algorithm] 从训练器获取客户端ID: 9
2025-08-01 00:10:32,490 - INFO - [Algorithm] 初始化后修正client_id: 9
2025-08-01 00:10:32,490 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:32,490 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:32,490 - INFO - [Client 9] 创建新算法
2025-08-01 00:10:32,490 - INFO - [Algorithm] 设置客户端ID: 9
2025-08-01 00:10:32,490 - INFO - [Algorithm] 同步更新trainer的client_id: 9
2025-08-01 00:10:32,490 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:32,505 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:32,506 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:32,506 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:32,513 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:32,513 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:32,513 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:32,513 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:32,514 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:32,514 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:32,514 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:32,514 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:32,514 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:32,514 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:32,514 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:32,514 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:32,514 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:32,514 - INFO - 顺序分配客户端ID: 9
2025-08-01 00:10:32,514 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 9
2025-08-01 00:10:32,515 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:32,515 - WARNING - [Client 9] 数据源为None，已创建新数据源
2025-08-01 00:10:32,515 - WARNING - [Client 9] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:33,093 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:33,093 - INFO - [Client 9] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:33,093 - INFO - [Client 9] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:33,096 - INFO - [Client 9] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:33,097 - INFO - [Client 9] 初始化时成功加载数据
2025-08-01 00:10:33,097 - INFO - [客户端 9] 初始化验证通过
2025-08-01 00:10:33,097 - INFO - 客户端 9 实例创建成功
2025-08-01 00:10:33,097 - INFO - 客户端9已设置服务器引用
2025-08-01 00:10:33,097 - INFO - 客户端 9 已设置服务器引用
2025-08-01 00:10:33,097 - INFO - 客户端9已注册
2025-08-01 00:10:33,097 - INFO - 客户端 9 已成功注册到服务器
2025-08-01 00:10:33,097 - INFO - 开始创建客户端 10...
2025-08-01 00:10:33,098 - INFO - 初始化客户端, ID: 10
2025-08-01 00:10:33,114 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:33,115 - INFO - [Client 10] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:33,115 - INFO - [Trainer] 初始化训练器, client_id: 10
2025-08-01 00:10:33,121 - INFO - [Trainer 10] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:33,121 - INFO - [Trainer 10] 模型的输入通道数: 3
2025-08-01 00:10:33,121 - INFO - [Trainer 10] 强制使用CPU
2025-08-01 00:10:33,121 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 00:10:33,122 - INFO - [Trainer 10] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:33,122 - INFO - [Trainer 10] 初始化完成
2025-08-01 00:10:33,122 - INFO - [Client 10] 创建新训练器
2025-08-01 00:10:33,122 - INFO - [Algorithm] 从训练器获取客户端ID: 10
2025-08-01 00:10:33,122 - INFO - [Algorithm] 初始化后修正client_id: 10
2025-08-01 00:10:33,122 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:33,122 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:33,122 - INFO - [Client 10] 创建新算法
2025-08-01 00:10:33,122 - INFO - [Algorithm] 设置客户端ID: 10
2025-08-01 00:10:33,122 - INFO - [Algorithm] 同步更新trainer的client_id: 10
2025-08-01 00:10:33,122 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:33,138 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:33,138 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:33,138 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:33,145 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:33,145 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:33,146 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:33,146 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:33,146 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:33,147 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:33,147 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:33,147 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:33,147 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:33,147 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:33,147 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:33,147 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:33,147 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:33,147 - INFO - 顺序分配客户端ID: 10
2025-08-01 00:10:33,148 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 10
2025-08-01 00:10:33,148 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:33,148 - WARNING - [Client 10] 数据源为None，已创建新数据源
2025-08-01 00:10:33,148 - WARNING - [Client 10] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:33,734 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:33,734 - INFO - [Client 10] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:33,734 - INFO - [Client 10] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:33,738 - INFO - [Client 10] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:33,738 - INFO - [Client 10] 初始化时成功加载数据
2025-08-01 00:10:33,738 - INFO - [客户端 10] 初始化验证通过
2025-08-01 00:10:33,738 - INFO - 客户端 10 实例创建成功
2025-08-01 00:10:33,738 - INFO - 客户端10已设置服务器引用
2025-08-01 00:10:33,738 - INFO - 客户端 10 已设置服务器引用
2025-08-01 00:10:33,738 - INFO - 客户端10已注册
2025-08-01 00:10:33,738 - INFO - 客户端 10 已成功注册到服务器
2025-08-01 00:10:33,738 - INFO - 开始创建客户端 11...
2025-08-01 00:10:33,738 - INFO - 初始化客户端, ID: 11
2025-08-01 00:10:33,756 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:33,756 - INFO - [Client 11] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:33,757 - INFO - [Trainer] 初始化训练器, client_id: 11
2025-08-01 00:10:33,762 - INFO - [Trainer 11] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:33,763 - INFO - [Trainer 11] 模型的输入通道数: 3
2025-08-01 00:10:33,763 - INFO - [Trainer 11] 强制使用CPU
2025-08-01 00:10:33,763 - INFO - [Trainer 11] 模型已移至设备: cpu
2025-08-01 00:10:33,763 - INFO - [Trainer 11] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:33,763 - INFO - [Trainer 11] 初始化完成
2025-08-01 00:10:33,763 - INFO - [Client 11] 创建新训练器
2025-08-01 00:10:33,763 - INFO - [Algorithm] 从训练器获取客户端ID: 11
2025-08-01 00:10:33,763 - INFO - [Algorithm] 初始化后修正client_id: 11
2025-08-01 00:10:33,763 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:33,763 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:33,764 - INFO - [Client 11] 创建新算法
2025-08-01 00:10:33,764 - INFO - [Algorithm] 设置客户端ID: 11
2025-08-01 00:10:33,764 - INFO - [Algorithm] 同步更新trainer的client_id: 11
2025-08-01 00:10:33,764 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:33,778 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:33,778 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:33,778 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:33,784 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:33,784 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:33,785 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:33,785 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:33,785 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:33,785 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:33,785 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:33,785 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:33,786 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:33,786 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:33,786 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:33,786 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:33,786 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:33,786 - INFO - 顺序分配客户端ID: 11
2025-08-01 00:10:33,786 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 11
2025-08-01 00:10:33,786 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:33,786 - WARNING - [Client 11] 数据源为None，已创建新数据源
2025-08-01 00:10:33,786 - WARNING - [Client 11] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:34,366 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:34,366 - INFO - [Client 11] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:34,367 - INFO - [Client 11] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:34,371 - INFO - [Client 11] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:34,371 - INFO - [Client 11] 初始化时成功加载数据
2025-08-01 00:10:34,371 - INFO - [客户端 11] 初始化验证通过
2025-08-01 00:10:34,371 - INFO - 客户端 11 实例创建成功
2025-08-01 00:10:34,372 - INFO - 客户端11已设置服务器引用
2025-08-01 00:10:34,372 - INFO - 客户端 11 已设置服务器引用
2025-08-01 00:10:34,372 - INFO - 客户端11已注册
2025-08-01 00:10:34,372 - INFO - 客户端 11 已成功注册到服务器
2025-08-01 00:10:34,372 - INFO - 开始创建客户端 12...
2025-08-01 00:10:34,372 - INFO - 初始化客户端, ID: 12
2025-08-01 00:10:34,388 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:34,388 - INFO - [Client 12] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:34,389 - INFO - [Trainer] 初始化训练器, client_id: 12
2025-08-01 00:10:34,394 - INFO - [Trainer 12] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:34,396 - INFO - [Trainer 12] 模型的输入通道数: 3
2025-08-01 00:10:34,396 - INFO - [Trainer 12] 强制使用CPU
2025-08-01 00:10:34,396 - INFO - [Trainer 12] 模型已移至设备: cpu
2025-08-01 00:10:34,396 - INFO - [Trainer 12] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:34,397 - INFO - [Trainer 12] 初始化完成
2025-08-01 00:10:34,397 - INFO - [Client 12] 创建新训练器
2025-08-01 00:10:34,397 - INFO - [Algorithm] 从训练器获取客户端ID: 12
2025-08-01 00:10:34,397 - INFO - [Algorithm] 初始化后修正client_id: 12
2025-08-01 00:10:34,397 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:34,397 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:34,397 - INFO - [Client 12] 创建新算法
2025-08-01 00:10:34,397 - INFO - [Algorithm] 设置客户端ID: 12
2025-08-01 00:10:34,397 - INFO - [Algorithm] 同步更新trainer的client_id: 12
2025-08-01 00:10:34,397 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:34,412 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:34,413 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:34,413 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:34,419 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:34,419 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:34,419 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:34,420 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:34,420 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:34,420 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:34,420 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:34,420 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:34,420 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:34,420 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:34,420 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:34,420 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:34,420 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:34,420 - INFO - 顺序分配客户端ID: 12
2025-08-01 00:10:34,421 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 12
2025-08-01 00:10:34,421 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:34,421 - WARNING - [Client 12] 数据源为None，已创建新数据源
2025-08-01 00:10:34,421 - WARNING - [Client 12] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:35,006 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:35,006 - INFO - [Client 12] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:35,007 - INFO - [Client 12] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:35,010 - INFO - [Client 12] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:35,010 - INFO - [Client 12] 初始化时成功加载数据
2025-08-01 00:10:35,010 - INFO - [客户端 12] 初始化验证通过
2025-08-01 00:10:35,010 - INFO - 客户端 12 实例创建成功
2025-08-01 00:10:35,010 - INFO - 客户端12已设置服务器引用
2025-08-01 00:10:35,010 - INFO - 客户端 12 已设置服务器引用
2025-08-01 00:10:35,010 - INFO - 客户端12已注册
2025-08-01 00:10:35,010 - INFO - 客户端 12 已成功注册到服务器
2025-08-01 00:10:35,011 - INFO - 开始创建客户端 13...
2025-08-01 00:10:35,011 - INFO - 初始化客户端, ID: 13
2025-08-01 00:10:35,026 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:35,026 - INFO - [Client 13] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:35,026 - INFO - [Trainer] 初始化训练器, client_id: 13
2025-08-01 00:10:35,032 - INFO - [Trainer 13] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:35,032 - INFO - [Trainer 13] 模型的输入通道数: 3
2025-08-01 00:10:35,035 - INFO - [Trainer 13] 强制使用CPU
2025-08-01 00:10:35,036 - INFO - [Trainer 13] 模型已移至设备: cpu
2025-08-01 00:10:35,036 - INFO - [Trainer 13] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:35,036 - INFO - [Trainer 13] 初始化完成
2025-08-01 00:10:35,036 - INFO - [Client 13] 创建新训练器
2025-08-01 00:10:35,036 - INFO - [Algorithm] 从训练器获取客户端ID: 13
2025-08-01 00:10:35,036 - INFO - [Algorithm] 初始化后修正client_id: 13
2025-08-01 00:10:35,036 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:35,036 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:35,036 - INFO - [Client 13] 创建新算法
2025-08-01 00:10:35,037 - INFO - [Algorithm] 设置客户端ID: 13
2025-08-01 00:10:35,037 - INFO - [Algorithm] 同步更新trainer的client_id: 13
2025-08-01 00:10:35,037 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:35,052 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:35,053 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:35,053 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:35,058 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:35,058 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:35,058 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:35,058 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:35,059 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:35,059 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:35,059 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:35,059 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:35,059 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:35,059 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:35,059 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:35,059 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:35,059 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:35,059 - INFO - 顺序分配客户端ID: 13
2025-08-01 00:10:35,059 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 13
2025-08-01 00:10:35,059 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:35,060 - WARNING - [Client 13] 数据源为None，已创建新数据源
2025-08-01 00:10:35,060 - WARNING - [Client 13] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:35,661 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:35,661 - INFO - [Client 13] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:35,662 - INFO - [Client 13] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:35,665 - INFO - [Client 13] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:35,665 - INFO - [Client 13] 初始化时成功加载数据
2025-08-01 00:10:35,665 - INFO - [客户端 13] 初始化验证通过
2025-08-01 00:10:35,665 - INFO - 客户端 13 实例创建成功
2025-08-01 00:10:35,665 - INFO - 客户端13已设置服务器引用
2025-08-01 00:10:35,665 - INFO - 客户端 13 已设置服务器引用
2025-08-01 00:10:35,665 - INFO - 客户端13已注册
2025-08-01 00:10:35,665 - INFO - 客户端 13 已成功注册到服务器
2025-08-01 00:10:35,665 - INFO - 开始创建客户端 14...
2025-08-01 00:10:35,665 - INFO - 初始化客户端, ID: 14
2025-08-01 00:10:35,680 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:35,681 - INFO - [Client 14] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:35,681 - INFO - [Trainer] 初始化训练器, client_id: 14
2025-08-01 00:10:35,687 - INFO - [Trainer 14] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:35,687 - INFO - [Trainer 14] 模型的输入通道数: 3
2025-08-01 00:10:35,687 - INFO - [Trainer 14] 强制使用CPU
2025-08-01 00:10:35,687 - INFO - [Trainer 14] 模型已移至设备: cpu
2025-08-01 00:10:35,687 - INFO - [Trainer 14] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:35,687 - INFO - [Trainer 14] 初始化完成
2025-08-01 00:10:35,689 - INFO - [Client 14] 创建新训练器
2025-08-01 00:10:35,689 - INFO - [Algorithm] 从训练器获取客户端ID: 14
2025-08-01 00:10:35,689 - INFO - [Algorithm] 初始化后修正client_id: 14
2025-08-01 00:10:35,689 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:35,689 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:35,689 - INFO - [Client 14] 创建新算法
2025-08-01 00:10:35,689 - INFO - [Algorithm] 设置客户端ID: 14
2025-08-01 00:10:35,689 - INFO - [Algorithm] 同步更新trainer的client_id: 14
2025-08-01 00:10:35,689 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:35,705 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:35,705 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:35,705 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:35,710 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:35,711 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:35,711 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:35,711 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:35,711 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:35,711 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:35,711 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:35,711 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:35,711 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:35,711 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:35,711 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:35,712 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:35,712 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:35,712 - INFO - 顺序分配客户端ID: 14
2025-08-01 00:10:35,712 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 14
2025-08-01 00:10:35,712 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:35,712 - WARNING - [Client 14] 数据源为None，已创建新数据源
2025-08-01 00:10:35,712 - WARNING - [Client 14] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:36,289 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:36,290 - INFO - [Client 14] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:36,290 - INFO - [Client 14] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:36,293 - INFO - [Client 14] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:36,294 - INFO - [Client 14] 初始化时成功加载数据
2025-08-01 00:10:36,294 - INFO - [客户端 14] 初始化验证通过
2025-08-01 00:10:36,294 - INFO - 客户端 14 实例创建成功
2025-08-01 00:10:36,294 - INFO - 客户端14已设置服务器引用
2025-08-01 00:10:36,294 - INFO - 客户端 14 已设置服务器引用
2025-08-01 00:10:36,294 - INFO - 客户端14已注册
2025-08-01 00:10:36,294 - INFO - 客户端 14 已成功注册到服务器
2025-08-01 00:10:36,294 - INFO - 开始创建客户端 15...
2025-08-01 00:10:36,294 - INFO - 初始化客户端, ID: 15
2025-08-01 00:10:36,311 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:36,311 - INFO - [Client 15] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:36,312 - INFO - [Trainer] 初始化训练器, client_id: 15
2025-08-01 00:10:36,317 - INFO - [Trainer 15] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:36,318 - INFO - [Trainer 15] 模型的输入通道数: 3
2025-08-01 00:10:36,318 - INFO - [Trainer 15] 强制使用CPU
2025-08-01 00:10:36,318 - INFO - [Trainer 15] 模型已移至设备: cpu
2025-08-01 00:10:36,318 - INFO - [Trainer 15] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:36,318 - INFO - [Trainer 15] 初始化完成
2025-08-01 00:10:36,318 - INFO - [Client 15] 创建新训练器
2025-08-01 00:10:36,318 - INFO - [Algorithm] 从训练器获取客户端ID: 15
2025-08-01 00:10:36,319 - INFO - [Algorithm] 初始化后修正client_id: 15
2025-08-01 00:10:36,319 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:36,319 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:36,319 - INFO - [Client 15] 创建新算法
2025-08-01 00:10:36,319 - INFO - [Algorithm] 设置客户端ID: 15
2025-08-01 00:10:36,319 - INFO - [Algorithm] 同步更新trainer的client_id: 15
2025-08-01 00:10:36,319 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:36,334 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:36,334 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:36,334 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:36,340 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:36,340 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:36,340 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:36,341 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:36,341 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:36,341 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:36,341 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:36,341 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:36,341 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:36,341 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:36,341 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:36,341 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:36,341 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:36,341 - INFO - 顺序分配客户端ID: 15
2025-08-01 00:10:36,341 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 15
2025-08-01 00:10:36,342 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:36,342 - WARNING - [Client 15] 数据源为None，已创建新数据源
2025-08-01 00:10:36,342 - WARNING - [Client 15] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:36,918 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:36,918 - INFO - [Client 15] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:36,918 - INFO - [Client 15] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:36,921 - INFO - [Client 15] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:36,921 - INFO - [Client 15] 初始化时成功加载数据
2025-08-01 00:10:36,922 - INFO - [客户端 15] 初始化验证通过
2025-08-01 00:10:36,922 - INFO - 客户端 15 实例创建成功
2025-08-01 00:10:36,922 - INFO - 客户端15已设置服务器引用
2025-08-01 00:10:36,922 - INFO - 客户端 15 已设置服务器引用
2025-08-01 00:10:36,922 - INFO - 客户端15已注册
2025-08-01 00:10:36,922 - INFO - 客户端 15 已成功注册到服务器
2025-08-01 00:10:36,922 - INFO - 开始创建客户端 16...
2025-08-01 00:10:36,922 - INFO - 初始化客户端, ID: 16
2025-08-01 00:10:36,938 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:36,938 - INFO - [Client 16] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:36,938 - INFO - [Trainer] 初始化训练器, client_id: 16
2025-08-01 00:10:36,944 - INFO - [Trainer 16] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:36,945 - INFO - [Trainer 16] 模型的输入通道数: 3
2025-08-01 00:10:36,945 - INFO - [Trainer 16] 强制使用CPU
2025-08-01 00:10:36,945 - INFO - [Trainer 16] 模型已移至设备: cpu
2025-08-01 00:10:36,945 - INFO - [Trainer 16] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:36,945 - INFO - [Trainer 16] 初始化完成
2025-08-01 00:10:36,946 - INFO - [Client 16] 创建新训练器
2025-08-01 00:10:36,946 - INFO - [Algorithm] 从训练器获取客户端ID: 16
2025-08-01 00:10:36,946 - INFO - [Algorithm] 初始化后修正client_id: 16
2025-08-01 00:10:36,946 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:36,946 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:36,946 - INFO - [Client 16] 创建新算法
2025-08-01 00:10:36,946 - INFO - [Algorithm] 设置客户端ID: 16
2025-08-01 00:10:36,946 - INFO - [Algorithm] 同步更新trainer的client_id: 16
2025-08-01 00:10:36,946 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:36,962 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:36,962 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:36,962 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:36,968 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:36,968 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:36,968 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:36,968 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:36,969 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:36,969 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:36,969 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:36,969 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:36,969 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:36,969 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:36,969 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:36,969 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:36,969 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:36,969 - INFO - 顺序分配客户端ID: 16
2025-08-01 00:10:36,969 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 16
2025-08-01 00:10:36,970 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:36,970 - WARNING - [Client 16] 数据源为None，已创建新数据源
2025-08-01 00:10:36,970 - WARNING - [Client 16] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:37,545 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:37,546 - INFO - [Client 16] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:37,546 - INFO - [Client 16] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:37,550 - INFO - [Client 16] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:37,550 - INFO - [Client 16] 初始化时成功加载数据
2025-08-01 00:10:37,550 - INFO - [客户端 16] 初始化验证通过
2025-08-01 00:10:37,550 - INFO - 客户端 16 实例创建成功
2025-08-01 00:10:37,550 - INFO - 客户端16已设置服务器引用
2025-08-01 00:10:37,552 - INFO - 客户端 16 已设置服务器引用
2025-08-01 00:10:37,552 - INFO - 客户端16已注册
2025-08-01 00:10:37,552 - INFO - 客户端 16 已成功注册到服务器
2025-08-01 00:10:37,552 - INFO - 开始创建客户端 17...
2025-08-01 00:10:37,552 - INFO - 初始化客户端, ID: 17
2025-08-01 00:10:37,568 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:37,568 - INFO - [Client 17] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:37,568 - INFO - [Trainer] 初始化训练器, client_id: 17
2025-08-01 00:10:37,574 - INFO - [Trainer 17] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:37,574 - INFO - [Trainer 17] 模型的输入通道数: 3
2025-08-01 00:10:37,574 - INFO - [Trainer 17] 强制使用CPU
2025-08-01 00:10:37,575 - INFO - [Trainer 17] 模型已移至设备: cpu
2025-08-01 00:10:37,575 - INFO - [Trainer 17] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:37,576 - INFO - [Trainer 17] 初始化完成
2025-08-01 00:10:37,576 - INFO - [Client 17] 创建新训练器
2025-08-01 00:10:37,576 - INFO - [Algorithm] 从训练器获取客户端ID: 17
2025-08-01 00:10:37,576 - INFO - [Algorithm] 初始化后修正client_id: 17
2025-08-01 00:10:37,576 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:37,576 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:37,577 - INFO - [Client 17] 创建新算法
2025-08-01 00:10:37,577 - INFO - [Algorithm] 设置客户端ID: 17
2025-08-01 00:10:37,577 - INFO - [Algorithm] 同步更新trainer的client_id: 17
2025-08-01 00:10:37,577 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:37,592 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:37,592 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:37,592 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:37,599 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:37,599 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:37,599 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:37,599 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:37,600 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:37,600 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:37,600 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:37,600 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:37,600 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:37,600 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:37,600 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:37,600 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:37,600 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:37,600 - INFO - 顺序分配客户端ID: 17
2025-08-01 00:10:37,600 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 17
2025-08-01 00:10:37,601 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:37,601 - WARNING - [Client 17] 数据源为None，已创建新数据源
2025-08-01 00:10:37,601 - WARNING - [Client 17] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:38,186 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:38,187 - INFO - [Client 17] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:38,187 - INFO - [Client 17] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:38,191 - INFO - [Client 17] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:38,191 - INFO - [Client 17] 初始化时成功加载数据
2025-08-01 00:10:38,191 - INFO - [客户端 17] 初始化验证通过
2025-08-01 00:10:38,191 - INFO - 客户端 17 实例创建成功
2025-08-01 00:10:38,191 - INFO - 客户端17已设置服务器引用
2025-08-01 00:10:38,191 - INFO - 客户端 17 已设置服务器引用
2025-08-01 00:10:38,191 - INFO - 客户端17已注册
2025-08-01 00:10:38,191 - INFO - 客户端 17 已成功注册到服务器
2025-08-01 00:10:38,191 - INFO - 开始创建客户端 18...
2025-08-01 00:10:38,192 - INFO - 初始化客户端, ID: 18
2025-08-01 00:10:38,207 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:38,207 - INFO - [Client 18] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:38,207 - INFO - [Trainer] 初始化训练器, client_id: 18
2025-08-01 00:10:38,213 - INFO - [Trainer 18] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:38,213 - INFO - [Trainer 18] 模型的输入通道数: 3
2025-08-01 00:10:38,214 - INFO - [Trainer 18] 强制使用CPU
2025-08-01 00:10:38,214 - INFO - [Trainer 18] 模型已移至设备: cpu
2025-08-01 00:10:38,214 - INFO - [Trainer 18] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:38,214 - INFO - [Trainer 18] 初始化完成
2025-08-01 00:10:38,214 - INFO - [Client 18] 创建新训练器
2025-08-01 00:10:38,214 - INFO - [Algorithm] 从训练器获取客户端ID: 18
2025-08-01 00:10:38,214 - INFO - [Algorithm] 初始化后修正client_id: 18
2025-08-01 00:10:38,214 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:38,214 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:38,214 - INFO - [Client 18] 创建新算法
2025-08-01 00:10:38,214 - INFO - [Algorithm] 设置客户端ID: 18
2025-08-01 00:10:38,214 - INFO - [Algorithm] 同步更新trainer的client_id: 18
2025-08-01 00:10:38,214 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:38,230 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:38,230 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:38,230 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:38,235 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:38,235 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:38,235 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:38,237 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:38,237 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:38,237 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:38,237 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:38,237 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:38,237 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:38,237 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:38,237 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:38,237 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:38,237 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:38,237 - INFO - 顺序分配客户端ID: 18
2025-08-01 00:10:38,237 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 18
2025-08-01 00:10:38,238 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:38,238 - WARNING - [Client 18] 数据源为None，已创建新数据源
2025-08-01 00:10:38,238 - WARNING - [Client 18] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:38,819 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:38,819 - INFO - [Client 18] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:38,819 - INFO - [Client 18] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:38,823 - INFO - [Client 18] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:38,823 - INFO - [Client 18] 初始化时成功加载数据
2025-08-01 00:10:38,824 - INFO - [客户端 18] 初始化验证通过
2025-08-01 00:10:38,824 - INFO - 客户端 18 实例创建成功
2025-08-01 00:10:38,824 - INFO - 客户端18已设置服务器引用
2025-08-01 00:10:38,824 - INFO - 客户端 18 已设置服务器引用
2025-08-01 00:10:38,824 - INFO - 客户端18已注册
2025-08-01 00:10:38,824 - INFO - 客户端 18 已成功注册到服务器
2025-08-01 00:10:38,824 - INFO - 开始创建客户端 19...
2025-08-01 00:10:38,824 - INFO - 初始化客户端, ID: 19
2025-08-01 00:10:38,841 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:38,841 - INFO - [Client 19] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:38,841 - INFO - [Trainer] 初始化训练器, client_id: 19
2025-08-01 00:10:38,848 - INFO - [Trainer 19] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:38,848 - INFO - [Trainer 19] 模型的输入通道数: 3
2025-08-01 00:10:38,848 - INFO - [Trainer 19] 强制使用CPU
2025-08-01 00:10:38,848 - INFO - [Trainer 19] 模型已移至设备: cpu
2025-08-01 00:10:38,849 - INFO - [Trainer 19] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:38,849 - INFO - [Trainer 19] 初始化完成
2025-08-01 00:10:38,849 - INFO - [Client 19] 创建新训练器
2025-08-01 00:10:38,849 - INFO - [Algorithm] 从训练器获取客户端ID: 19
2025-08-01 00:10:38,849 - INFO - [Algorithm] 初始化后修正client_id: 19
2025-08-01 00:10:38,849 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:38,849 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:38,849 - INFO - [Client 19] 创建新算法
2025-08-01 00:10:38,850 - INFO - [Algorithm] 设置客户端ID: 19
2025-08-01 00:10:38,850 - INFO - [Algorithm] 同步更新trainer的client_id: 19
2025-08-01 00:10:38,850 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:38,865 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:38,865 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:38,865 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:38,873 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:38,873 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:38,873 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:38,873 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:38,873 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:38,874 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:38,874 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:38,874 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:38,874 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:38,874 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:38,874 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:38,874 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:38,874 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:38,874 - INFO - 顺序分配客户端ID: 19
2025-08-01 00:10:38,874 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 19
2025-08-01 00:10:38,874 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:38,875 - WARNING - [Client 19] 数据源为None，已创建新数据源
2025-08-01 00:10:38,875 - WARNING - [Client 19] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:39,447 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:39,447 - INFO - [Client 19] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:39,447 - INFO - [Client 19] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:39,450 - INFO - [Client 19] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:39,450 - INFO - [Client 19] 初始化时成功加载数据
2025-08-01 00:10:39,450 - INFO - [客户端 19] 初始化验证通过
2025-08-01 00:10:39,450 - INFO - 客户端 19 实例创建成功
2025-08-01 00:10:39,450 - INFO - 客户端19已设置服务器引用
2025-08-01 00:10:39,450 - INFO - 客户端 19 已设置服务器引用
2025-08-01 00:10:39,451 - INFO - 客户端19已注册
2025-08-01 00:10:39,451 - INFO - 客户端 19 已成功注册到服务器
2025-08-01 00:10:39,451 - INFO - 开始创建客户端 20...
2025-08-01 00:10:39,451 - INFO - 初始化客户端, ID: 20
2025-08-01 00:10:39,467 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:39,467 - INFO - [Client 20] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:39,467 - INFO - [Trainer] 初始化训练器, client_id: 20
2025-08-01 00:10:39,472 - INFO - [Trainer 20] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:39,472 - INFO - [Trainer 20] 模型的输入通道数: 3
2025-08-01 00:10:39,472 - INFO - [Trainer 20] 强制使用CPU
2025-08-01 00:10:39,473 - INFO - [Trainer 20] 模型已移至设备: cpu
2025-08-01 00:10:39,473 - INFO - [Trainer 20] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:39,473 - INFO - [Trainer 20] 初始化完成
2025-08-01 00:10:39,473 - INFO - [Client 20] 创建新训练器
2025-08-01 00:10:39,473 - INFO - [Algorithm] 从训练器获取客户端ID: 20
2025-08-01 00:10:39,473 - INFO - [Algorithm] 初始化后修正client_id: 20
2025-08-01 00:10:39,473 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:39,473 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:39,473 - INFO - [Client 20] 创建新算法
2025-08-01 00:10:39,473 - INFO - [Algorithm] 设置客户端ID: 20
2025-08-01 00:10:39,474 - INFO - [Algorithm] 同步更新trainer的client_id: 20
2025-08-01 00:10:39,474 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:39,489 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:39,489 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:39,489 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:39,495 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:39,495 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:39,495 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:39,495 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:39,495 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:39,496 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:39,496 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:39,496 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:39,496 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:39,496 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:39,496 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:39,496 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:39,496 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:39,496 - INFO - 顺序分配客户端ID: 20
2025-08-01 00:10:39,496 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 20
2025-08-01 00:10:39,496 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:39,497 - WARNING - [Client 20] 数据源为None，已创建新数据源
2025-08-01 00:10:39,497 - WARNING - [Client 20] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:40,067 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:40,067 - INFO - [Client 20] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:40,067 - INFO - [Client 20] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:40,071 - INFO - [Client 20] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:40,071 - INFO - [Client 20] 初始化时成功加载数据
2025-08-01 00:10:40,071 - INFO - [客户端 20] 初始化验证通过
2025-08-01 00:10:40,071 - INFO - 客户端 20 实例创建成功
2025-08-01 00:10:40,071 - INFO - 客户端20已设置服务器引用
2025-08-01 00:10:40,071 - INFO - 客户端 20 已设置服务器引用
2025-08-01 00:10:40,072 - INFO - 客户端20已注册
2025-08-01 00:10:40,072 - INFO - 客户端 20 已成功注册到服务器
2025-08-01 00:10:40,072 - INFO - 开始创建客户端 21...
2025-08-01 00:10:40,072 - INFO - 初始化客户端, ID: 21
2025-08-01 00:10:40,088 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:40,088 - INFO - [Client 21] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:40,088 - INFO - [Trainer] 初始化训练器, client_id: 21
2025-08-01 00:10:40,094 - INFO - [Trainer 21] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:40,094 - INFO - [Trainer 21] 模型的输入通道数: 3
2025-08-01 00:10:40,095 - INFO - [Trainer 21] 强制使用CPU
2025-08-01 00:10:40,095 - INFO - [Trainer 21] 模型已移至设备: cpu
2025-08-01 00:10:40,095 - INFO - [Trainer 21] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:40,095 - INFO - [Trainer 21] 初始化完成
2025-08-01 00:10:40,095 - INFO - [Client 21] 创建新训练器
2025-08-01 00:10:40,095 - INFO - [Algorithm] 从训练器获取客户端ID: 21
2025-08-01 00:10:40,095 - INFO - [Algorithm] 初始化后修正client_id: 21
2025-08-01 00:10:40,095 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:40,096 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:40,096 - INFO - [Client 21] 创建新算法
2025-08-01 00:10:40,096 - INFO - [Algorithm] 设置客户端ID: 21
2025-08-01 00:10:40,096 - INFO - [Algorithm] 同步更新trainer的client_id: 21
2025-08-01 00:10:40,096 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:40,111 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:40,111 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:40,111 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:40,117 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:40,117 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:40,117 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:40,117 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:40,118 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:40,118 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:40,118 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:40,118 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:40,118 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:40,118 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:40,118 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:40,118 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:40,118 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:40,119 - INFO - 顺序分配客户端ID: 21
2025-08-01 00:10:40,119 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 21
2025-08-01 00:10:40,119 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:40,119 - WARNING - [Client 21] 数据源为None，已创建新数据源
2025-08-01 00:10:40,119 - WARNING - [Client 21] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:40,703 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:40,703 - INFO - [Client 21] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:40,703 - INFO - [Client 21] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:40,707 - INFO - [Client 21] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:40,707 - INFO - [Client 21] 初始化时成功加载数据
2025-08-01 00:10:40,707 - INFO - [客户端 21] 初始化验证通过
2025-08-01 00:10:40,707 - INFO - 客户端 21 实例创建成功
2025-08-01 00:10:40,707 - INFO - 客户端21已设置服务器引用
2025-08-01 00:10:40,707 - INFO - 客户端 21 已设置服务器引用
2025-08-01 00:10:40,707 - INFO - 客户端21已注册
2025-08-01 00:10:40,707 - INFO - 客户端 21 已成功注册到服务器
2025-08-01 00:10:40,707 - INFO - 开始创建客户端 22...
2025-08-01 00:10:40,707 - INFO - 初始化客户端, ID: 22
2025-08-01 00:10:40,723 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:40,724 - INFO - [Client 22] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:40,724 - INFO - [Trainer] 初始化训练器, client_id: 22
2025-08-01 00:10:40,730 - INFO - [Trainer 22] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:40,730 - INFO - [Trainer 22] 模型的输入通道数: 3
2025-08-01 00:10:40,730 - INFO - [Trainer 22] 强制使用CPU
2025-08-01 00:10:40,730 - INFO - [Trainer 22] 模型已移至设备: cpu
2025-08-01 00:10:40,730 - INFO - [Trainer 22] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:40,731 - INFO - [Trainer 22] 初始化完成
2025-08-01 00:10:40,731 - INFO - [Client 22] 创建新训练器
2025-08-01 00:10:40,731 - INFO - [Algorithm] 从训练器获取客户端ID: 22
2025-08-01 00:10:40,731 - INFO - [Algorithm] 初始化后修正client_id: 22
2025-08-01 00:10:40,731 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:40,731 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:40,731 - INFO - [Client 22] 创建新算法
2025-08-01 00:10:40,731 - INFO - [Algorithm] 设置客户端ID: 22
2025-08-01 00:10:40,731 - INFO - [Algorithm] 同步更新trainer的client_id: 22
2025-08-01 00:10:40,731 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:40,747 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:40,748 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:40,748 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:40,754 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:40,754 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:40,754 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:40,754 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:40,754 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:40,755 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:40,755 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:40,755 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:40,756 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:40,756 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:40,756 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:40,756 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:40,756 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:40,756 - INFO - 顺序分配客户端ID: 22
2025-08-01 00:10:40,756 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 22
2025-08-01 00:10:40,757 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:40,757 - WARNING - [Client 22] 数据源为None，已创建新数据源
2025-08-01 00:10:40,757 - WARNING - [Client 22] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:41,334 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:41,334 - INFO - [Client 22] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:41,334 - INFO - [Client 22] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:41,338 - INFO - [Client 22] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:41,339 - INFO - [Client 22] 初始化时成功加载数据
2025-08-01 00:10:41,339 - INFO - [客户端 22] 初始化验证通过
2025-08-01 00:10:41,339 - INFO - 客户端 22 实例创建成功
2025-08-01 00:10:41,339 - INFO - 客户端22已设置服务器引用
2025-08-01 00:10:41,339 - INFO - 客户端 22 已设置服务器引用
2025-08-01 00:10:41,339 - INFO - 客户端22已注册
2025-08-01 00:10:41,339 - INFO - 客户端 22 已成功注册到服务器
2025-08-01 00:10:41,339 - INFO - 开始创建客户端 23...
2025-08-01 00:10:41,339 - INFO - 初始化客户端, ID: 23
2025-08-01 00:10:41,356 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:41,356 - INFO - [Client 23] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:41,356 - INFO - [Trainer] 初始化训练器, client_id: 23
2025-08-01 00:10:41,361 - INFO - [Trainer 23] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:41,361 - INFO - [Trainer 23] 模型的输入通道数: 3
2025-08-01 00:10:41,362 - INFO - [Trainer 23] 强制使用CPU
2025-08-01 00:10:41,362 - INFO - [Trainer 23] 模型已移至设备: cpu
2025-08-01 00:10:41,362 - INFO - [Trainer 23] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:41,362 - INFO - [Trainer 23] 初始化完成
2025-08-01 00:10:41,362 - INFO - [Client 23] 创建新训练器
2025-08-01 00:10:41,362 - INFO - [Algorithm] 从训练器获取客户端ID: 23
2025-08-01 00:10:41,362 - INFO - [Algorithm] 初始化后修正client_id: 23
2025-08-01 00:10:41,362 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:41,362 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:41,362 - INFO - [Client 23] 创建新算法
2025-08-01 00:10:41,364 - INFO - [Algorithm] 设置客户端ID: 23
2025-08-01 00:10:41,364 - INFO - [Algorithm] 同步更新trainer的client_id: 23
2025-08-01 00:10:41,364 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:41,378 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:41,379 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:41,379 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:41,385 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:41,385 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:41,385 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:41,385 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:41,385 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:41,385 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:41,385 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:41,385 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:41,385 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:41,385 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:41,385 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:41,387 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:41,387 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:41,387 - INFO - 顺序分配客户端ID: 23
2025-08-01 00:10:41,387 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 23
2025-08-01 00:10:41,387 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:41,387 - WARNING - [Client 23] 数据源为None，已创建新数据源
2025-08-01 00:10:41,387 - WARNING - [Client 23] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:41,965 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:41,966 - INFO - [Client 23] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:41,966 - INFO - [Client 23] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:41,969 - INFO - [Client 23] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:41,970 - INFO - [Client 23] 初始化时成功加载数据
2025-08-01 00:10:41,970 - INFO - [客户端 23] 初始化验证通过
2025-08-01 00:10:41,970 - INFO - 客户端 23 实例创建成功
2025-08-01 00:10:41,970 - INFO - 客户端23已设置服务器引用
2025-08-01 00:10:41,970 - INFO - 客户端 23 已设置服务器引用
2025-08-01 00:10:41,970 - INFO - 客户端23已注册
2025-08-01 00:10:41,970 - INFO - 客户端 23 已成功注册到服务器
2025-08-01 00:10:41,971 - INFO - 开始创建客户端 24...
2025-08-01 00:10:41,971 - INFO - 初始化客户端, ID: 24
2025-08-01 00:10:41,986 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:41,986 - INFO - [Client 24] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:41,986 - INFO - [Trainer] 初始化训练器, client_id: 24
2025-08-01 00:10:41,993 - INFO - [Trainer 24] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:41,993 - INFO - [Trainer 24] 模型的输入通道数: 3
2025-08-01 00:10:41,993 - INFO - [Trainer 24] 强制使用CPU
2025-08-01 00:10:41,993 - INFO - [Trainer 24] 模型已移至设备: cpu
2025-08-01 00:10:41,993 - INFO - [Trainer 24] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:41,993 - INFO - [Trainer 24] 初始化完成
2025-08-01 00:10:41,993 - INFO - [Client 24] 创建新训练器
2025-08-01 00:10:41,994 - INFO - [Algorithm] 从训练器获取客户端ID: 24
2025-08-01 00:10:41,994 - INFO - [Algorithm] 初始化后修正client_id: 24
2025-08-01 00:10:41,994 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:41,994 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:41,994 - INFO - [Client 24] 创建新算法
2025-08-01 00:10:41,994 - INFO - [Algorithm] 设置客户端ID: 24
2025-08-01 00:10:41,994 - INFO - [Algorithm] 同步更新trainer的client_id: 24
2025-08-01 00:10:41,994 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:42,010 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:42,010 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:42,010 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:42,017 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:42,017 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:42,017 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:42,017 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:42,017 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:42,019 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:42,019 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:42,019 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:42,019 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:42,019 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:42,019 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:42,019 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:42,019 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:42,019 - INFO - 顺序分配客户端ID: 24
2025-08-01 00:10:42,019 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 24
2025-08-01 00:10:42,019 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:42,020 - WARNING - [Client 24] 数据源为None，已创建新数据源
2025-08-01 00:10:42,020 - WARNING - [Client 24] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:42,626 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:42,626 - INFO - [Client 24] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:42,626 - INFO - [Client 24] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:42,630 - INFO - [Client 24] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:42,631 - INFO - [Client 24] 初始化时成功加载数据
2025-08-01 00:10:42,631 - INFO - [客户端 24] 初始化验证通过
2025-08-01 00:10:42,631 - INFO - 客户端 24 实例创建成功
2025-08-01 00:10:42,631 - INFO - 客户端24已设置服务器引用
2025-08-01 00:10:42,631 - INFO - 客户端 24 已设置服务器引用
2025-08-01 00:10:42,631 - INFO - 客户端24已注册
2025-08-01 00:10:42,631 - INFO - 客户端 24 已成功注册到服务器
2025-08-01 00:10:42,631 - INFO - 开始创建客户端 25...
2025-08-01 00:10:42,631 - INFO - 初始化客户端, ID: 25
2025-08-01 00:10:42,647 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:42,647 - INFO - [Client 25] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:42,647 - INFO - [Trainer] 初始化训练器, client_id: 25
2025-08-01 00:10:42,655 - INFO - [Trainer 25] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:42,655 - INFO - [Trainer 25] 模型的输入通道数: 3
2025-08-01 00:10:42,655 - INFO - [Trainer 25] 强制使用CPU
2025-08-01 00:10:42,655 - INFO - [Trainer 25] 模型已移至设备: cpu
2025-08-01 00:10:42,656 - INFO - [Trainer 25] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:42,656 - INFO - [Trainer 25] 初始化完成
2025-08-01 00:10:42,656 - INFO - [Client 25] 创建新训练器
2025-08-01 00:10:42,656 - INFO - [Algorithm] 从训练器获取客户端ID: 25
2025-08-01 00:10:42,656 - INFO - [Algorithm] 初始化后修正client_id: 25
2025-08-01 00:10:42,656 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:42,656 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:42,656 - INFO - [Client 25] 创建新算法
2025-08-01 00:10:42,656 - INFO - [Algorithm] 设置客户端ID: 25
2025-08-01 00:10:42,656 - INFO - [Algorithm] 同步更新trainer的client_id: 25
2025-08-01 00:10:42,656 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:42,671 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:42,672 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:42,672 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:42,679 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:42,679 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:42,679 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:42,679 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:42,680 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:42,680 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:42,680 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:42,680 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:42,680 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:42,680 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:42,681 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:42,681 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:42,681 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:42,681 - INFO - 顺序分配客户端ID: 25
2025-08-01 00:10:42,681 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 25
2025-08-01 00:10:42,681 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:42,682 - WARNING - [Client 25] 数据源为None，已创建新数据源
2025-08-01 00:10:42,682 - WARNING - [Client 25] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:43,283 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:43,284 - INFO - [Client 25] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:43,284 - INFO - [Client 25] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:43,288 - INFO - [Client 25] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:43,288 - INFO - [Client 25] 初始化时成功加载数据
2025-08-01 00:10:43,288 - INFO - [客户端 25] 初始化验证通过
2025-08-01 00:10:43,288 - INFO - 客户端 25 实例创建成功
2025-08-01 00:10:43,288 - INFO - 客户端25已设置服务器引用
2025-08-01 00:10:43,288 - INFO - 客户端 25 已设置服务器引用
2025-08-01 00:10:43,288 - INFO - 客户端25已注册
2025-08-01 00:10:43,288 - INFO - 客户端 25 已成功注册到服务器
2025-08-01 00:10:43,289 - INFO - 开始创建客户端 26...
2025-08-01 00:10:43,289 - INFO - 初始化客户端, ID: 26
2025-08-01 00:10:43,305 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:43,305 - INFO - [Client 26] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:43,305 - INFO - [Trainer] 初始化训练器, client_id: 26
2025-08-01 00:10:43,311 - INFO - [Trainer 26] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:43,312 - INFO - [Trainer 26] 模型的输入通道数: 3
2025-08-01 00:10:43,312 - INFO - [Trainer 26] 强制使用CPU
2025-08-01 00:10:43,312 - INFO - [Trainer 26] 模型已移至设备: cpu
2025-08-01 00:10:43,312 - INFO - [Trainer 26] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:43,313 - INFO - [Trainer 26] 初始化完成
2025-08-01 00:10:43,313 - INFO - [Client 26] 创建新训练器
2025-08-01 00:10:43,313 - INFO - [Algorithm] 从训练器获取客户端ID: 26
2025-08-01 00:10:43,313 - INFO - [Algorithm] 初始化后修正client_id: 26
2025-08-01 00:10:43,313 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:43,313 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:43,313 - INFO - [Client 26] 创建新算法
2025-08-01 00:10:43,313 - INFO - [Algorithm] 设置客户端ID: 26
2025-08-01 00:10:43,313 - INFO - [Algorithm] 同步更新trainer的client_id: 26
2025-08-01 00:10:43,314 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:43,329 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:43,329 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:43,329 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:43,335 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:43,337 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:43,337 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:43,337 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:43,337 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:43,337 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:43,337 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:43,338 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:43,338 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:43,338 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:43,338 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:43,338 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:43,338 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:43,338 - INFO - 顺序分配客户端ID: 26
2025-08-01 00:10:43,338 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 26
2025-08-01 00:10:43,341 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:43,341 - WARNING - [Client 26] 数据源为None，已创建新数据源
2025-08-01 00:10:43,341 - WARNING - [Client 26] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:43,917 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:43,917 - INFO - [Client 26] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:43,917 - INFO - [Client 26] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:43,921 - INFO - [Client 26] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:43,921 - INFO - [Client 26] 初始化时成功加载数据
2025-08-01 00:10:43,921 - INFO - [客户端 26] 初始化验证通过
2025-08-01 00:10:43,921 - INFO - 客户端 26 实例创建成功
2025-08-01 00:10:43,922 - INFO - 客户端26已设置服务器引用
2025-08-01 00:10:43,922 - INFO - 客户端 26 已设置服务器引用
2025-08-01 00:10:43,922 - INFO - 客户端26已注册
2025-08-01 00:10:43,922 - INFO - 客户端 26 已成功注册到服务器
2025-08-01 00:10:43,922 - INFO - 开始创建客户端 27...
2025-08-01 00:10:43,922 - INFO - 初始化客户端, ID: 27
2025-08-01 00:10:43,937 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:43,938 - INFO - [Client 27] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:43,938 - INFO - [Trainer] 初始化训练器, client_id: 27
2025-08-01 00:10:43,943 - INFO - [Trainer 27] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:43,943 - INFO - [Trainer 27] 模型的输入通道数: 3
2025-08-01 00:10:43,943 - INFO - [Trainer 27] 强制使用CPU
2025-08-01 00:10:43,944 - INFO - [Trainer 27] 模型已移至设备: cpu
2025-08-01 00:10:43,944 - INFO - [Trainer 27] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:43,944 - INFO - [Trainer 27] 初始化完成
2025-08-01 00:10:43,944 - INFO - [Client 27] 创建新训练器
2025-08-01 00:10:43,944 - INFO - [Algorithm] 从训练器获取客户端ID: 27
2025-08-01 00:10:43,944 - INFO - [Algorithm] 初始化后修正client_id: 27
2025-08-01 00:10:43,944 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:43,944 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:43,944 - INFO - [Client 27] 创建新算法
2025-08-01 00:10:43,944 - INFO - [Algorithm] 设置客户端ID: 27
2025-08-01 00:10:43,944 - INFO - [Algorithm] 同步更新trainer的client_id: 27
2025-08-01 00:10:43,944 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:43,959 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:43,959 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:43,959 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:43,965 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:43,965 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:43,965 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:43,966 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:43,966 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:43,966 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:43,966 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:43,966 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:43,966 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:43,966 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:43,966 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:43,966 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:43,966 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:43,967 - INFO - 顺序分配客户端ID: 27
2025-08-01 00:10:43,967 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 27
2025-08-01 00:10:43,967 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:43,967 - WARNING - [Client 27] 数据源为None，已创建新数据源
2025-08-01 00:10:43,967 - WARNING - [Client 27] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:44,605 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:44,605 - INFO - [Client 27] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:44,606 - INFO - [Client 27] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:44,609 - INFO - [Client 27] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:44,609 - INFO - [Client 27] 初始化时成功加载数据
2025-08-01 00:10:44,609 - INFO - [客户端 27] 初始化验证通过
2025-08-01 00:10:44,609 - INFO - 客户端 27 实例创建成功
2025-08-01 00:10:44,609 - INFO - 客户端27已设置服务器引用
2025-08-01 00:10:44,609 - INFO - 客户端 27 已设置服务器引用
2025-08-01 00:10:44,610 - INFO - 客户端27已注册
2025-08-01 00:10:44,610 - INFO - 客户端 27 已成功注册到服务器
2025-08-01 00:10:44,610 - INFO - 开始创建客户端 28...
2025-08-01 00:10:44,610 - INFO - 初始化客户端, ID: 28
2025-08-01 00:10:44,625 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:44,625 - INFO - [Client 28] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:44,626 - INFO - [Trainer] 初始化训练器, client_id: 28
2025-08-01 00:10:44,631 - INFO - [Trainer 28] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:44,633 - INFO - [Trainer 28] 模型的输入通道数: 3
2025-08-01 00:10:44,633 - INFO - [Trainer 28] 强制使用CPU
2025-08-01 00:10:44,633 - INFO - [Trainer 28] 模型已移至设备: cpu
2025-08-01 00:10:44,633 - INFO - [Trainer 28] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:44,634 - INFO - [Trainer 28] 初始化完成
2025-08-01 00:10:44,634 - INFO - [Client 28] 创建新训练器
2025-08-01 00:10:44,634 - INFO - [Algorithm] 从训练器获取客户端ID: 28
2025-08-01 00:10:44,634 - INFO - [Algorithm] 初始化后修正client_id: 28
2025-08-01 00:10:44,634 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:44,634 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:44,634 - INFO - [Client 28] 创建新算法
2025-08-01 00:10:44,634 - INFO - [Algorithm] 设置客户端ID: 28
2025-08-01 00:10:44,634 - INFO - [Algorithm] 同步更新trainer的client_id: 28
2025-08-01 00:10:44,635 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:44,649 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:44,649 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:44,649 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:44,657 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:44,657 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:44,657 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:44,658 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:44,658 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:44,658 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:44,658 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:44,658 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:44,658 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:44,658 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:44,658 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:44,658 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:44,658 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:44,658 - INFO - 顺序分配客户端ID: 28
2025-08-01 00:10:44,658 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 28
2025-08-01 00:10:44,660 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:44,660 - WARNING - [Client 28] 数据源为None，已创建新数据源
2025-08-01 00:10:44,660 - WARNING - [Client 28] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:45,261 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:45,261 - INFO - [Client 28] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:45,261 - INFO - [Client 28] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:45,264 - INFO - [Client 28] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:45,265 - INFO - [Client 28] 初始化时成功加载数据
2025-08-01 00:10:45,265 - INFO - [客户端 28] 初始化验证通过
2025-08-01 00:10:45,265 - INFO - 客户端 28 实例创建成功
2025-08-01 00:10:45,265 - INFO - 客户端28已设置服务器引用
2025-08-01 00:10:45,265 - INFO - 客户端 28 已设置服务器引用
2025-08-01 00:10:45,265 - INFO - 客户端28已注册
2025-08-01 00:10:45,265 - INFO - 客户端 28 已成功注册到服务器
2025-08-01 00:10:45,265 - INFO - 开始创建客户端 29...
2025-08-01 00:10:45,265 - INFO - 初始化客户端, ID: 29
2025-08-01 00:10:45,282 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:45,282 - INFO - [Client 29] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:45,283 - INFO - [Trainer] 初始化训练器, client_id: 29
2025-08-01 00:10:45,288 - INFO - [Trainer 29] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:45,288 - INFO - [Trainer 29] 模型的输入通道数: 3
2025-08-01 00:10:45,288 - INFO - [Trainer 29] 强制使用CPU
2025-08-01 00:10:45,288 - INFO - [Trainer 29] 模型已移至设备: cpu
2025-08-01 00:10:45,290 - INFO - [Trainer 29] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:45,290 - INFO - [Trainer 29] 初始化完成
2025-08-01 00:10:45,290 - INFO - [Client 29] 创建新训练器
2025-08-01 00:10:45,290 - INFO - [Algorithm] 从训练器获取客户端ID: 29
2025-08-01 00:10:45,290 - INFO - [Algorithm] 初始化后修正client_id: 29
2025-08-01 00:10:45,290 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:45,290 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:45,290 - INFO - [Client 29] 创建新算法
2025-08-01 00:10:45,290 - INFO - [Algorithm] 设置客户端ID: 29
2025-08-01 00:10:45,290 - INFO - [Algorithm] 同步更新trainer的client_id: 29
2025-08-01 00:10:45,290 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:45,306 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:45,306 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:45,306 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:45,311 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:45,313 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:45,313 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:45,313 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:45,313 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:45,313 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:45,313 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:45,313 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:45,314 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:45,314 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:45,314 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:45,314 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:45,314 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:45,314 - INFO - 顺序分配客户端ID: 29
2025-08-01 00:10:45,314 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 29
2025-08-01 00:10:45,314 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:45,314 - WARNING - [Client 29] 数据源为None，已创建新数据源
2025-08-01 00:10:45,314 - WARNING - [Client 29] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:45,909 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:45,909 - INFO - [Client 29] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:45,909 - INFO - [Client 29] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:45,913 - INFO - [Client 29] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:45,913 - INFO - [Client 29] 初始化时成功加载数据
2025-08-01 00:10:45,913 - INFO - [客户端 29] 初始化验证通过
2025-08-01 00:10:45,913 - INFO - 客户端 29 实例创建成功
2025-08-01 00:10:45,913 - INFO - 客户端29已设置服务器引用
2025-08-01 00:10:45,913 - INFO - 客户端 29 已设置服务器引用
2025-08-01 00:10:45,913 - INFO - 客户端29已注册
2025-08-01 00:10:45,913 - INFO - 客户端 29 已成功注册到服务器
2025-08-01 00:10:45,913 - INFO - 开始创建客户端 30...
2025-08-01 00:10:45,913 - INFO - 初始化客户端, ID: 30
2025-08-01 00:10:45,931 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:45,931 - INFO - [Client 30] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:45,931 - INFO - [Trainer] 初始化训练器, client_id: 30
2025-08-01 00:10:45,938 - INFO - [Trainer 30] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:45,940 - INFO - [Trainer 30] 模型的输入通道数: 3
2025-08-01 00:10:45,940 - INFO - [Trainer 30] 强制使用CPU
2025-08-01 00:10:45,940 - INFO - [Trainer 30] 模型已移至设备: cpu
2025-08-01 00:10:45,940 - INFO - [Trainer 30] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:45,940 - INFO - [Trainer 30] 初始化完成
2025-08-01 00:10:45,940 - INFO - [Client 30] 创建新训练器
2025-08-01 00:10:45,941 - INFO - [Algorithm] 从训练器获取客户端ID: 30
2025-08-01 00:10:45,941 - INFO - [Algorithm] 初始化后修正client_id: 30
2025-08-01 00:10:45,941 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:45,941 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:45,941 - INFO - [Client 30] 创建新算法
2025-08-01 00:10:45,941 - INFO - [Algorithm] 设置客户端ID: 30
2025-08-01 00:10:45,941 - INFO - [Algorithm] 同步更新trainer的client_id: 30
2025-08-01 00:10:45,941 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:45,957 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:45,957 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:45,957 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:45,964 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:45,965 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:45,965 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:45,965 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:45,965 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:45,966 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:45,966 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:45,966 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:45,966 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:45,966 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:45,966 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:45,966 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:45,966 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:45,967 - INFO - 顺序分配客户端ID: 30
2025-08-01 00:10:45,967 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 30
2025-08-01 00:10:45,967 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:45,967 - WARNING - [Client 30] 数据源为None，已创建新数据源
2025-08-01 00:10:45,967 - WARNING - [Client 30] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:46,601 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:46,602 - INFO - [Client 30] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:46,602 - INFO - [Client 30] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:46,605 - INFO - [Client 30] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:46,606 - INFO - [Client 30] 初始化时成功加载数据
2025-08-01 00:10:46,606 - INFO - [客户端 30] 初始化验证通过
2025-08-01 00:10:46,606 - INFO - 客户端 30 实例创建成功
2025-08-01 00:10:46,606 - INFO - 客户端30已设置服务器引用
2025-08-01 00:10:46,606 - INFO - 客户端 30 已设置服务器引用
2025-08-01 00:10:46,606 - INFO - 客户端30已注册
2025-08-01 00:10:46,606 - INFO - 客户端 30 已成功注册到服务器
2025-08-01 00:10:46,606 - INFO - 开始创建客户端 31...
2025-08-01 00:10:46,606 - INFO - 初始化客户端, ID: 31
2025-08-01 00:10:46,623 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:46,623 - INFO - [Client 31] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:46,623 - INFO - [Trainer] 初始化训练器, client_id: 31
2025-08-01 00:10:46,630 - INFO - [Trainer 31] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:46,630 - INFO - [Trainer 31] 模型的输入通道数: 3
2025-08-01 00:10:46,631 - INFO - [Trainer 31] 强制使用CPU
2025-08-01 00:10:46,631 - INFO - [Trainer 31] 模型已移至设备: cpu
2025-08-01 00:10:46,631 - INFO - [Trainer 31] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:46,631 - INFO - [Trainer 31] 初始化完成
2025-08-01 00:10:46,631 - INFO - [Client 31] 创建新训练器
2025-08-01 00:10:46,632 - INFO - [Algorithm] 从训练器获取客户端ID: 31
2025-08-01 00:10:46,632 - INFO - [Algorithm] 初始化后修正client_id: 31
2025-08-01 00:10:46,632 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:46,632 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:46,632 - INFO - [Client 31] 创建新算法
2025-08-01 00:10:46,632 - INFO - [Algorithm] 设置客户端ID: 31
2025-08-01 00:10:46,632 - INFO - [Algorithm] 同步更新trainer的client_id: 31
2025-08-01 00:10:46,632 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:46,648 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:46,648 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:46,649 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:46,654 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:46,655 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:46,655 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:46,655 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:46,655 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:46,655 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:46,655 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:46,656 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:46,656 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:46,656 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:46,656 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:46,656 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:46,656 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:46,656 - INFO - 顺序分配客户端ID: 31
2025-08-01 00:10:46,656 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 31
2025-08-01 00:10:46,657 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:46,657 - WARNING - [Client 31] 数据源为None，已创建新数据源
2025-08-01 00:10:46,657 - WARNING - [Client 31] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:47,261 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:47,261 - INFO - [Client 31] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:47,261 - INFO - [Client 31] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:47,266 - INFO - [Client 31] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:47,266 - INFO - [Client 31] 初始化时成功加载数据
2025-08-01 00:10:47,266 - INFO - [客户端 31] 初始化验证通过
2025-08-01 00:10:47,266 - INFO - 客户端 31 实例创建成功
2025-08-01 00:10:47,266 - INFO - 客户端31已设置服务器引用
2025-08-01 00:10:47,266 - INFO - 客户端 31 已设置服务器引用
2025-08-01 00:10:47,266 - INFO - 客户端31已注册
2025-08-01 00:10:47,266 - INFO - 客户端 31 已成功注册到服务器
2025-08-01 00:10:47,266 - INFO - 开始创建客户端 32...
2025-08-01 00:10:47,267 - INFO - 初始化客户端, ID: 32
2025-08-01 00:10:47,284 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:47,284 - INFO - [Client 32] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:47,284 - INFO - [Trainer] 初始化训练器, client_id: 32
2025-08-01 00:10:47,289 - INFO - [Trainer 32] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:47,291 - INFO - [Trainer 32] 模型的输入通道数: 3
2025-08-01 00:10:47,291 - INFO - [Trainer 32] 强制使用CPU
2025-08-01 00:10:47,291 - INFO - [Trainer 32] 模型已移至设备: cpu
2025-08-01 00:10:47,291 - INFO - [Trainer 32] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:47,291 - INFO - [Trainer 32] 初始化完成
2025-08-01 00:10:47,291 - INFO - [Client 32] 创建新训练器
2025-08-01 00:10:47,291 - INFO - [Algorithm] 从训练器获取客户端ID: 32
2025-08-01 00:10:47,292 - INFO - [Algorithm] 初始化后修正client_id: 32
2025-08-01 00:10:47,292 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:47,292 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:47,292 - INFO - [Client 32] 创建新算法
2025-08-01 00:10:47,292 - INFO - [Algorithm] 设置客户端ID: 32
2025-08-01 00:10:47,292 - INFO - [Algorithm] 同步更新trainer的client_id: 32
2025-08-01 00:10:47,292 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:47,307 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:47,307 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:47,307 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:47,316 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:47,316 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:47,316 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:47,316 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:47,316 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:47,316 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:47,316 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:47,317 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:47,317 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:47,317 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:47,317 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:47,317 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:47,317 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:47,317 - INFO - 顺序分配客户端ID: 32
2025-08-01 00:10:47,317 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 32
2025-08-01 00:10:47,317 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:47,317 - WARNING - [Client 32] 数据源为None，已创建新数据源
2025-08-01 00:10:47,317 - WARNING - [Client 32] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:47,904 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:47,905 - INFO - [Client 32] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:47,905 - INFO - [Client 32] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:47,911 - INFO - [Client 32] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:47,911 - INFO - [Client 32] 初始化时成功加载数据
2025-08-01 00:10:47,911 - INFO - [客户端 32] 初始化验证通过
2025-08-01 00:10:47,911 - INFO - 客户端 32 实例创建成功
2025-08-01 00:10:47,911 - INFO - 客户端32已设置服务器引用
2025-08-01 00:10:47,911 - INFO - 客户端 32 已设置服务器引用
2025-08-01 00:10:47,911 - INFO - 客户端32已注册
2025-08-01 00:10:47,911 - INFO - 客户端 32 已成功注册到服务器
2025-08-01 00:10:47,911 - INFO - 开始创建客户端 33...
2025-08-01 00:10:47,912 - INFO - 初始化客户端, ID: 33
2025-08-01 00:10:47,928 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:47,928 - INFO - [Client 33] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:47,928 - INFO - [Trainer] 初始化训练器, client_id: 33
2025-08-01 00:10:47,934 - INFO - [Trainer 33] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:47,934 - INFO - [Trainer 33] 模型的输入通道数: 3
2025-08-01 00:10:47,935 - INFO - [Trainer 33] 强制使用CPU
2025-08-01 00:10:47,935 - INFO - [Trainer 33] 模型已移至设备: cpu
2025-08-01 00:10:47,935 - INFO - [Trainer 33] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:47,935 - INFO - [Trainer 33] 初始化完成
2025-08-01 00:10:47,935 - INFO - [Client 33] 创建新训练器
2025-08-01 00:10:47,935 - INFO - [Algorithm] 从训练器获取客户端ID: 33
2025-08-01 00:10:47,935 - INFO - [Algorithm] 初始化后修正client_id: 33
2025-08-01 00:10:47,935 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:47,935 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:47,935 - INFO - [Client 33] 创建新算法
2025-08-01 00:10:47,935 - INFO - [Algorithm] 设置客户端ID: 33
2025-08-01 00:10:47,936 - INFO - [Algorithm] 同步更新trainer的client_id: 33
2025-08-01 00:10:47,936 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:47,952 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:47,952 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:47,952 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:47,958 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:47,959 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:47,959 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:47,959 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:47,959 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:47,960 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:47,960 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:47,960 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:47,960 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:47,960 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:47,960 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:47,960 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:47,960 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:47,961 - INFO - 顺序分配客户端ID: 33
2025-08-01 00:10:47,961 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 33
2025-08-01 00:10:47,961 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:47,961 - WARNING - [Client 33] 数据源为None，已创建新数据源
2025-08-01 00:10:47,961 - WARNING - [Client 33] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:48,552 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:48,553 - INFO - [Client 33] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:48,553 - INFO - [Client 33] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:48,557 - INFO - [Client 33] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:48,557 - INFO - [Client 33] 初始化时成功加载数据
2025-08-01 00:10:48,557 - INFO - [客户端 33] 初始化验证通过
2025-08-01 00:10:48,557 - INFO - 客户端 33 实例创建成功
2025-08-01 00:10:48,557 - INFO - 客户端33已设置服务器引用
2025-08-01 00:10:48,557 - INFO - 客户端 33 已设置服务器引用
2025-08-01 00:10:48,557 - INFO - 客户端33已注册
2025-08-01 00:10:48,557 - INFO - 客户端 33 已成功注册到服务器
2025-08-01 00:10:48,558 - INFO - 开始创建客户端 34...
2025-08-01 00:10:48,558 - INFO - 初始化客户端, ID: 34
2025-08-01 00:10:48,574 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:48,574 - INFO - [Client 34] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:48,574 - INFO - [Trainer] 初始化训练器, client_id: 34
2025-08-01 00:10:48,581 - INFO - [Trainer 34] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:48,582 - INFO - [Trainer 34] 模型的输入通道数: 3
2025-08-01 00:10:48,582 - INFO - [Trainer 34] 强制使用CPU
2025-08-01 00:10:48,582 - INFO - [Trainer 34] 模型已移至设备: cpu
2025-08-01 00:10:48,582 - INFO - [Trainer 34] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:48,583 - INFO - [Trainer 34] 初始化完成
2025-08-01 00:10:48,583 - INFO - [Client 34] 创建新训练器
2025-08-01 00:10:48,583 - INFO - [Algorithm] 从训练器获取客户端ID: 34
2025-08-01 00:10:48,583 - INFO - [Algorithm] 初始化后修正client_id: 34
2025-08-01 00:10:48,583 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:48,583 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:48,584 - INFO - [Client 34] 创建新算法
2025-08-01 00:10:48,584 - INFO - [Algorithm] 设置客户端ID: 34
2025-08-01 00:10:48,584 - INFO - [Algorithm] 同步更新trainer的client_id: 34
2025-08-01 00:10:48,584 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:48,600 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:48,600 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:48,601 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:48,608 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:48,608 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:48,608 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:48,609 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:48,609 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:48,609 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:48,609 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:48,609 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:48,610 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:48,610 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:48,610 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:48,610 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:48,610 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:48,610 - INFO - 顺序分配客户端ID: 34
2025-08-01 00:10:48,610 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 34
2025-08-01 00:10:48,610 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:48,611 - WARNING - [Client 34] 数据源为None，已创建新数据源
2025-08-01 00:10:48,611 - WARNING - [Client 34] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:49,400 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:49,400 - INFO - [Client 34] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:49,400 - INFO - [Client 34] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:49,404 - INFO - [Client 34] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:49,404 - INFO - [Client 34] 初始化时成功加载数据
2025-08-01 00:10:49,404 - INFO - [客户端 34] 初始化验证通过
2025-08-01 00:10:49,404 - INFO - 客户端 34 实例创建成功
2025-08-01 00:10:49,404 - INFO - 客户端34已设置服务器引用
2025-08-01 00:10:49,404 - INFO - 客户端 34 已设置服务器引用
2025-08-01 00:10:49,404 - INFO - 客户端34已注册
2025-08-01 00:10:49,404 - INFO - 客户端 34 已成功注册到服务器
2025-08-01 00:10:49,404 - INFO - 开始创建客户端 35...
2025-08-01 00:10:49,404 - INFO - 初始化客户端, ID: 35
2025-08-01 00:10:49,420 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:49,420 - INFO - [Client 35] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:49,421 - INFO - [Trainer] 初始化训练器, client_id: 35
2025-08-01 00:10:49,426 - INFO - [Trainer 35] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:49,427 - INFO - [Trainer 35] 模型的输入通道数: 3
2025-08-01 00:10:49,427 - INFO - [Trainer 35] 强制使用CPU
2025-08-01 00:10:49,427 - INFO - [Trainer 35] 模型已移至设备: cpu
2025-08-01 00:10:49,427 - INFO - [Trainer 35] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:49,427 - INFO - [Trainer 35] 初始化完成
2025-08-01 00:10:49,427 - INFO - [Client 35] 创建新训练器
2025-08-01 00:10:49,427 - INFO - [Algorithm] 从训练器获取客户端ID: 35
2025-08-01 00:10:49,428 - INFO - [Algorithm] 初始化后修正client_id: 35
2025-08-01 00:10:49,428 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:49,428 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:49,428 - INFO - [Client 35] 创建新算法
2025-08-01 00:10:49,428 - INFO - [Algorithm] 设置客户端ID: 35
2025-08-01 00:10:49,428 - INFO - [Algorithm] 同步更新trainer的client_id: 35
2025-08-01 00:10:49,428 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:49,444 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:49,444 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:49,445 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:49,451 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:49,451 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:49,452 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:49,452 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:49,452 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:49,452 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:49,452 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:49,452 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:49,453 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:49,453 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:49,453 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:49,453 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:49,453 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:49,453 - INFO - 顺序分配客户端ID: 35
2025-08-01 00:10:49,453 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 35
2025-08-01 00:10:49,453 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:49,453 - WARNING - [Client 35] 数据源为None，已创建新数据源
2025-08-01 00:10:49,454 - WARNING - [Client 35] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:50,085 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:50,085 - INFO - [Client 35] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:50,085 - INFO - [Client 35] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:50,089 - INFO - [Client 35] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:50,089 - INFO - [Client 35] 初始化时成功加载数据
2025-08-01 00:10:50,089 - INFO - [客户端 35] 初始化验证通过
2025-08-01 00:10:50,089 - INFO - 客户端 35 实例创建成功
2025-08-01 00:10:50,089 - INFO - 客户端35已设置服务器引用
2025-08-01 00:10:50,089 - INFO - 客户端 35 已设置服务器引用
2025-08-01 00:10:50,089 - INFO - 客户端35已注册
2025-08-01 00:10:50,089 - INFO - 客户端 35 已成功注册到服务器
2025-08-01 00:10:50,089 - INFO - 开始创建客户端 36...
2025-08-01 00:10:50,089 - INFO - 初始化客户端, ID: 36
2025-08-01 00:10:50,106 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:50,106 - INFO - [Client 36] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:50,106 - INFO - [Trainer] 初始化训练器, client_id: 36
2025-08-01 00:10:50,113 - INFO - [Trainer 36] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:50,113 - INFO - [Trainer 36] 模型的输入通道数: 3
2025-08-01 00:10:50,113 - INFO - [Trainer 36] 强制使用CPU
2025-08-01 00:10:50,113 - INFO - [Trainer 36] 模型已移至设备: cpu
2025-08-01 00:10:50,114 - INFO - [Trainer 36] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:50,114 - INFO - [Trainer 36] 初始化完成
2025-08-01 00:10:50,114 - INFO - [Client 36] 创建新训练器
2025-08-01 00:10:50,114 - INFO - [Algorithm] 从训练器获取客户端ID: 36
2025-08-01 00:10:50,114 - INFO - [Algorithm] 初始化后修正client_id: 36
2025-08-01 00:10:50,114 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:50,114 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:50,114 - INFO - [Client 36] 创建新算法
2025-08-01 00:10:50,114 - INFO - [Algorithm] 设置客户端ID: 36
2025-08-01 00:10:50,114 - INFO - [Algorithm] 同步更新trainer的client_id: 36
2025-08-01 00:10:50,114 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:50,131 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:50,132 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:50,132 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:50,138 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:50,138 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:50,138 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:50,139 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:50,139 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:50,139 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:50,139 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:50,139 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:50,139 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:50,139 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:50,139 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:50,139 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:50,139 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:50,140 - INFO - 顺序分配客户端ID: 36
2025-08-01 00:10:50,140 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 36
2025-08-01 00:10:50,140 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:50,140 - WARNING - [Client 36] 数据源为None，已创建新数据源
2025-08-01 00:10:50,140 - WARNING - [Client 36] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:50,845 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:50,845 - INFO - [Client 36] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:50,845 - INFO - [Client 36] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:50,849 - INFO - [Client 36] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:50,849 - INFO - [Client 36] 初始化时成功加载数据
2025-08-01 00:10:50,849 - INFO - [客户端 36] 初始化验证通过
2025-08-01 00:10:50,849 - INFO - 客户端 36 实例创建成功
2025-08-01 00:10:50,849 - INFO - 客户端36已设置服务器引用
2025-08-01 00:10:50,849 - INFO - 客户端 36 已设置服务器引用
2025-08-01 00:10:50,849 - INFO - 客户端36已注册
2025-08-01 00:10:50,849 - INFO - 客户端 36 已成功注册到服务器
2025-08-01 00:10:50,850 - INFO - 开始创建客户端 37...
2025-08-01 00:10:50,850 - INFO - 初始化客户端, ID: 37
2025-08-01 00:10:50,868 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:50,868 - INFO - [Client 37] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:50,868 - INFO - [Trainer] 初始化训练器, client_id: 37
2025-08-01 00:10:50,881 - INFO - [Trainer 37] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:50,881 - INFO - [Trainer 37] 模型的输入通道数: 3
2025-08-01 00:10:50,881 - INFO - [Trainer 37] 强制使用CPU
2025-08-01 00:10:50,882 - INFO - [Trainer 37] 模型已移至设备: cpu
2025-08-01 00:10:50,882 - INFO - [Trainer 37] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:50,882 - INFO - [Trainer 37] 初始化完成
2025-08-01 00:10:50,882 - INFO - [Client 37] 创建新训练器
2025-08-01 00:10:50,882 - INFO - [Algorithm] 从训练器获取客户端ID: 37
2025-08-01 00:10:50,882 - INFO - [Algorithm] 初始化后修正client_id: 37
2025-08-01 00:10:50,882 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:50,883 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:50,883 - INFO - [Client 37] 创建新算法
2025-08-01 00:10:50,883 - INFO - [Algorithm] 设置客户端ID: 37
2025-08-01 00:10:50,883 - INFO - [Algorithm] 同步更新trainer的client_id: 37
2025-08-01 00:10:50,883 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:50,907 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:50,907 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:50,908 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:50,927 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:50,928 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:50,928 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:50,928 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:50,929 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:50,929 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:50,929 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:50,929 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:50,929 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:50,929 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:50,930 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:50,930 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:50,930 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:50,930 - INFO - 顺序分配客户端ID: 37
2025-08-01 00:10:50,930 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 37
2025-08-01 00:10:50,931 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:50,931 - WARNING - [Client 37] 数据源为None，已创建新数据源
2025-08-01 00:10:50,931 - WARNING - [Client 37] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:51,647 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:51,647 - INFO - [Client 37] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:51,647 - INFO - [Client 37] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:51,652 - INFO - [Client 37] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:51,652 - INFO - [Client 37] 初始化时成功加载数据
2025-08-01 00:10:51,652 - INFO - [客户端 37] 初始化验证通过
2025-08-01 00:10:51,652 - INFO - 客户端 37 实例创建成功
2025-08-01 00:10:51,652 - INFO - 客户端37已设置服务器引用
2025-08-01 00:10:51,652 - INFO - 客户端 37 已设置服务器引用
2025-08-01 00:10:51,652 - INFO - 客户端37已注册
2025-08-01 00:10:51,652 - INFO - 客户端 37 已成功注册到服务器
2025-08-01 00:10:51,652 - INFO - 开始创建客户端 38...
2025-08-01 00:10:51,653 - INFO - 初始化客户端, ID: 38
2025-08-01 00:10:51,670 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:51,670 - INFO - [Client 38] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:51,670 - INFO - [Trainer] 初始化训练器, client_id: 38
2025-08-01 00:10:51,680 - INFO - [Trainer 38] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:51,680 - INFO - [Trainer 38] 模型的输入通道数: 3
2025-08-01 00:10:51,680 - INFO - [Trainer 38] 强制使用CPU
2025-08-01 00:10:51,680 - INFO - [Trainer 38] 模型已移至设备: cpu
2025-08-01 00:10:51,681 - INFO - [Trainer 38] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:51,681 - INFO - [Trainer 38] 初始化完成
2025-08-01 00:10:51,681 - INFO - [Client 38] 创建新训练器
2025-08-01 00:10:51,681 - INFO - [Algorithm] 从训练器获取客户端ID: 38
2025-08-01 00:10:51,681 - INFO - [Algorithm] 初始化后修正client_id: 38
2025-08-01 00:10:51,681 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:51,681 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:51,681 - INFO - [Client 38] 创建新算法
2025-08-01 00:10:51,681 - INFO - [Algorithm] 设置客户端ID: 38
2025-08-01 00:10:51,681 - INFO - [Algorithm] 同步更新trainer的client_id: 38
2025-08-01 00:10:51,681 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:51,699 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:51,699 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:51,699 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:51,707 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:51,707 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:51,707 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:51,707 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:51,707 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:51,707 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:51,707 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:51,707 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:51,708 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:51,708 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:51,708 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:51,708 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:51,708 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:51,709 - INFO - 顺序分配客户端ID: 38
2025-08-01 00:10:51,709 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 38
2025-08-01 00:10:51,710 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:51,710 - WARNING - [Client 38] 数据源为None，已创建新数据源
2025-08-01 00:10:51,710 - WARNING - [Client 38] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:52,334 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:52,334 - INFO - [Client 38] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:52,334 - INFO - [Client 38] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:52,337 - INFO - [Client 38] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:52,338 - INFO - [Client 38] 初始化时成功加载数据
2025-08-01 00:10:52,338 - INFO - [客户端 38] 初始化验证通过
2025-08-01 00:10:52,338 - INFO - 客户端 38 实例创建成功
2025-08-01 00:10:52,338 - INFO - 客户端38已设置服务器引用
2025-08-01 00:10:52,338 - INFO - 客户端 38 已设置服务器引用
2025-08-01 00:10:52,338 - INFO - 客户端38已注册
2025-08-01 00:10:52,338 - INFO - 客户端 38 已成功注册到服务器
2025-08-01 00:10:52,338 - INFO - 开始创建客户端 39...
2025-08-01 00:10:52,339 - INFO - 初始化客户端, ID: 39
2025-08-01 00:10:52,355 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:52,356 - INFO - [Client 39] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:52,356 - INFO - [Trainer] 初始化训练器, client_id: 39
2025-08-01 00:10:52,365 - INFO - [Trainer 39] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:52,365 - INFO - [Trainer 39] 模型的输入通道数: 3
2025-08-01 00:10:52,365 - INFO - [Trainer 39] 强制使用CPU
2025-08-01 00:10:52,366 - INFO - [Trainer 39] 模型已移至设备: cpu
2025-08-01 00:10:52,366 - INFO - [Trainer 39] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:52,366 - INFO - [Trainer 39] 初始化完成
2025-08-01 00:10:52,366 - INFO - [Client 39] 创建新训练器
2025-08-01 00:10:52,367 - INFO - [Algorithm] 从训练器获取客户端ID: 39
2025-08-01 00:10:52,367 - INFO - [Algorithm] 初始化后修正client_id: 39
2025-08-01 00:10:52,367 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:52,367 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:52,367 - INFO - [Client 39] 创建新算法
2025-08-01 00:10:52,367 - INFO - [Algorithm] 设置客户端ID: 39
2025-08-01 00:10:52,367 - INFO - [Algorithm] 同步更新trainer的client_id: 39
2025-08-01 00:10:52,368 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:52,384 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:52,385 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:52,385 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:52,393 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:52,393 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:52,393 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:52,393 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:52,394 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:52,394 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:52,394 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:52,394 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:52,394 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:52,394 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:52,394 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:52,394 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:52,394 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:52,394 - INFO - 顺序分配客户端ID: 39
2025-08-01 00:10:52,394 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 39
2025-08-01 00:10:52,395 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:52,395 - WARNING - [Client 39] 数据源为None，已创建新数据源
2025-08-01 00:10:52,395 - WARNING - [Client 39] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:53,000 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:53,000 - INFO - [Client 39] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:53,000 - INFO - [Client 39] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:53,004 - INFO - [Client 39] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:53,004 - INFO - [Client 39] 初始化时成功加载数据
2025-08-01 00:10:53,004 - INFO - [客户端 39] 初始化验证通过
2025-08-01 00:10:53,005 - INFO - 客户端 39 实例创建成功
2025-08-01 00:10:53,005 - INFO - 客户端39已设置服务器引用
2025-08-01 00:10:53,005 - INFO - 客户端 39 已设置服务器引用
2025-08-01 00:10:53,005 - INFO - 客户端39已注册
2025-08-01 00:10:53,005 - INFO - 客户端 39 已成功注册到服务器
2025-08-01 00:10:53,006 - INFO - 开始创建客户端 40...
2025-08-01 00:10:53,006 - INFO - 初始化客户端, ID: 40
2025-08-01 00:10:53,022 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:53,022 - INFO - [Client 40] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:53,022 - INFO - [Trainer] 初始化训练器, client_id: 40
2025-08-01 00:10:53,032 - INFO - [Trainer 40] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:53,032 - INFO - [Trainer 40] 模型的输入通道数: 3
2025-08-01 00:10:53,032 - INFO - [Trainer 40] 强制使用CPU
2025-08-01 00:10:53,032 - INFO - [Trainer 40] 模型已移至设备: cpu
2025-08-01 00:10:53,035 - INFO - [Trainer 40] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:53,036 - INFO - [Trainer 40] 初始化完成
2025-08-01 00:10:53,036 - INFO - [Client 40] 创建新训练器
2025-08-01 00:10:53,036 - INFO - [Algorithm] 从训练器获取客户端ID: 40
2025-08-01 00:10:53,036 - INFO - [Algorithm] 初始化后修正client_id: 40
2025-08-01 00:10:53,036 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:53,036 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:53,036 - INFO - [Client 40] 创建新算法
2025-08-01 00:10:53,036 - INFO - [Algorithm] 设置客户端ID: 40
2025-08-01 00:10:53,037 - INFO - [Algorithm] 同步更新trainer的client_id: 40
2025-08-01 00:10:53,037 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:53,052 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:53,052 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:53,053 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:53,060 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:53,060 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:53,060 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:53,060 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:53,061 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:53,061 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:53,061 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:53,061 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:53,061 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:53,061 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:53,061 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:53,061 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:53,061 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:53,061 - INFO - 顺序分配客户端ID: 40
2025-08-01 00:10:53,061 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 40
2025-08-01 00:10:53,062 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:53,062 - WARNING - [Client 40] 数据源为None，已创建新数据源
2025-08-01 00:10:53,062 - WARNING - [Client 40] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:53,671 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:53,671 - INFO - [Client 40] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:53,671 - INFO - [Client 40] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:53,675 - INFO - [Client 40] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:53,675 - INFO - [Client 40] 初始化时成功加载数据
2025-08-01 00:10:53,675 - INFO - [客户端 40] 初始化验证通过
2025-08-01 00:10:53,676 - INFO - 客户端 40 实例创建成功
2025-08-01 00:10:53,676 - INFO - 客户端40已设置服务器引用
2025-08-01 00:10:53,676 - INFO - 客户端 40 已设置服务器引用
2025-08-01 00:10:53,676 - INFO - 客户端40已注册
2025-08-01 00:10:53,676 - INFO - 客户端 40 已成功注册到服务器
2025-08-01 00:10:53,676 - INFO - 开始创建客户端 41...
2025-08-01 00:10:53,677 - INFO - 初始化客户端, ID: 41
2025-08-01 00:10:53,693 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:53,694 - INFO - [Client 41] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:53,694 - INFO - [Trainer] 初始化训练器, client_id: 41
2025-08-01 00:10:53,700 - INFO - [Trainer 41] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:53,701 - INFO - [Trainer 41] 模型的输入通道数: 3
2025-08-01 00:10:53,701 - INFO - [Trainer 41] 强制使用CPU
2025-08-01 00:10:53,701 - INFO - [Trainer 41] 模型已移至设备: cpu
2025-08-01 00:10:53,701 - INFO - [Trainer 41] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:53,702 - INFO - [Trainer 41] 初始化完成
2025-08-01 00:10:53,703 - INFO - [Client 41] 创建新训练器
2025-08-01 00:10:53,703 - INFO - [Algorithm] 从训练器获取客户端ID: 41
2025-08-01 00:10:53,703 - INFO - [Algorithm] 初始化后修正client_id: 41
2025-08-01 00:10:53,703 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:53,703 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:53,703 - INFO - [Client 41] 创建新算法
2025-08-01 00:10:53,703 - INFO - [Algorithm] 设置客户端ID: 41
2025-08-01 00:10:53,703 - INFO - [Algorithm] 同步更新trainer的client_id: 41
2025-08-01 00:10:53,703 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:53,718 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:53,718 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:53,718 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:53,726 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:53,726 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:53,726 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:53,726 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:53,727 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:53,727 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:53,727 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:53,727 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:53,727 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:53,727 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:53,728 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:53,728 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:53,728 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:53,728 - INFO - 顺序分配客户端ID: 41
2025-08-01 00:10:53,728 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 41
2025-08-01 00:10:53,728 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:53,728 - WARNING - [Client 41] 数据源为None，已创建新数据源
2025-08-01 00:10:53,728 - WARNING - [Client 41] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:54,310 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:54,310 - INFO - [Client 41] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:54,310 - INFO - [Client 41] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:54,315 - INFO - [Client 41] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:54,315 - INFO - [Client 41] 初始化时成功加载数据
2025-08-01 00:10:54,315 - INFO - [客户端 41] 初始化验证通过
2025-08-01 00:10:54,315 - INFO - 客户端 41 实例创建成功
2025-08-01 00:10:54,315 - INFO - 客户端41已设置服务器引用
2025-08-01 00:10:54,315 - INFO - 客户端 41 已设置服务器引用
2025-08-01 00:10:54,316 - INFO - 客户端41已注册
2025-08-01 00:10:54,316 - INFO - 客户端 41 已成功注册到服务器
2025-08-01 00:10:54,316 - INFO - 开始创建客户端 42...
2025-08-01 00:10:54,316 - INFO - 初始化客户端, ID: 42
2025-08-01 00:10:54,330 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:54,331 - INFO - [Client 42] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:54,331 - INFO - [Trainer] 初始化训练器, client_id: 42
2025-08-01 00:10:54,338 - INFO - [Trainer 42] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:54,338 - INFO - [Trainer 42] 模型的输入通道数: 3
2025-08-01 00:10:54,338 - INFO - [Trainer 42] 强制使用CPU
2025-08-01 00:10:54,338 - INFO - [Trainer 42] 模型已移至设备: cpu
2025-08-01 00:10:54,338 - INFO - [Trainer 42] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:54,339 - INFO - [Trainer 42] 初始化完成
2025-08-01 00:10:54,339 - INFO - [Client 42] 创建新训练器
2025-08-01 00:10:54,339 - INFO - [Algorithm] 从训练器获取客户端ID: 42
2025-08-01 00:10:54,339 - INFO - [Algorithm] 初始化后修正client_id: 42
2025-08-01 00:10:54,339 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:54,339 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:54,339 - INFO - [Client 42] 创建新算法
2025-08-01 00:10:54,339 - INFO - [Algorithm] 设置客户端ID: 42
2025-08-01 00:10:54,339 - INFO - [Algorithm] 同步更新trainer的client_id: 42
2025-08-01 00:10:54,339 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:54,354 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:54,354 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:54,354 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:54,359 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:54,359 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:54,359 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:54,360 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:54,360 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:54,360 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:54,360 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:54,360 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:54,360 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:54,360 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:54,360 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:54,360 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:54,360 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:54,360 - INFO - 顺序分配客户端ID: 42
2025-08-01 00:10:54,360 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 42
2025-08-01 00:10:54,360 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:54,362 - WARNING - [Client 42] 数据源为None，已创建新数据源
2025-08-01 00:10:54,362 - WARNING - [Client 42] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:54,935 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:54,935 - INFO - [Client 42] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:54,935 - INFO - [Client 42] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:54,939 - INFO - [Client 42] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:54,939 - INFO - [Client 42] 初始化时成功加载数据
2025-08-01 00:10:54,940 - INFO - [客户端 42] 初始化验证通过
2025-08-01 00:10:54,940 - INFO - 客户端 42 实例创建成功
2025-08-01 00:10:54,940 - INFO - 客户端42已设置服务器引用
2025-08-01 00:10:54,940 - INFO - 客户端 42 已设置服务器引用
2025-08-01 00:10:54,940 - INFO - 客户端42已注册
2025-08-01 00:10:54,940 - INFO - 客户端 42 已成功注册到服务器
2025-08-01 00:10:54,940 - INFO - 开始创建客户端 43...
2025-08-01 00:10:54,940 - INFO - 初始化客户端, ID: 43
2025-08-01 00:10:54,957 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:54,957 - INFO - [Client 43] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:54,958 - INFO - [Trainer] 初始化训练器, client_id: 43
2025-08-01 00:10:54,964 - INFO - [Trainer 43] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:54,964 - INFO - [Trainer 43] 模型的输入通道数: 3
2025-08-01 00:10:54,964 - INFO - [Trainer 43] 强制使用CPU
2025-08-01 00:10:54,964 - INFO - [Trainer 43] 模型已移至设备: cpu
2025-08-01 00:10:54,965 - INFO - [Trainer 43] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:54,965 - INFO - [Trainer 43] 初始化完成
2025-08-01 00:10:54,965 - INFO - [Client 43] 创建新训练器
2025-08-01 00:10:54,965 - INFO - [Algorithm] 从训练器获取客户端ID: 43
2025-08-01 00:10:54,965 - INFO - [Algorithm] 初始化后修正client_id: 43
2025-08-01 00:10:54,965 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:54,965 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:54,965 - INFO - [Client 43] 创建新算法
2025-08-01 00:10:54,965 - INFO - [Algorithm] 设置客户端ID: 43
2025-08-01 00:10:54,965 - INFO - [Algorithm] 同步更新trainer的client_id: 43
2025-08-01 00:10:54,966 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:54,981 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:54,981 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:54,982 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:54,990 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:54,990 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:54,990 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:54,991 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:54,991 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:54,991 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:54,991 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:54,991 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:54,991 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:54,991 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:54,991 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:54,991 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:54,992 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:54,992 - INFO - 顺序分配客户端ID: 43
2025-08-01 00:10:54,992 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 43
2025-08-01 00:10:54,992 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:54,992 - WARNING - [Client 43] 数据源为None，已创建新数据源
2025-08-01 00:10:54,992 - WARNING - [Client 43] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:55,647 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:55,648 - INFO - [Client 43] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:55,648 - INFO - [Client 43] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:55,651 - INFO - [Client 43] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:55,651 - INFO - [Client 43] 初始化时成功加载数据
2025-08-01 00:10:55,652 - INFO - [客户端 43] 初始化验证通过
2025-08-01 00:10:55,652 - INFO - 客户端 43 实例创建成功
2025-08-01 00:10:55,652 - INFO - 客户端43已设置服务器引用
2025-08-01 00:10:55,652 - INFO - 客户端 43 已设置服务器引用
2025-08-01 00:10:55,652 - INFO - 客户端43已注册
2025-08-01 00:10:55,652 - INFO - 客户端 43 已成功注册到服务器
2025-08-01 00:10:55,652 - INFO - 开始创建客户端 44...
2025-08-01 00:10:55,652 - INFO - 初始化客户端, ID: 44
2025-08-01 00:10:55,675 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:55,676 - INFO - [Client 44] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:55,676 - INFO - [Trainer] 初始化训练器, client_id: 44
2025-08-01 00:10:55,688 - INFO - [Trainer 44] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:55,689 - INFO - [Trainer 44] 模型的输入通道数: 3
2025-08-01 00:10:55,689 - INFO - [Trainer 44] 强制使用CPU
2025-08-01 00:10:55,689 - INFO - [Trainer 44] 模型已移至设备: cpu
2025-08-01 00:10:55,689 - INFO - [Trainer 44] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:55,689 - INFO - [Trainer 44] 初始化完成
2025-08-01 00:10:55,689 - INFO - [Client 44] 创建新训练器
2025-08-01 00:10:55,689 - INFO - [Algorithm] 从训练器获取客户端ID: 44
2025-08-01 00:10:55,689 - INFO - [Algorithm] 初始化后修正client_id: 44
2025-08-01 00:10:55,689 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:55,690 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:55,690 - INFO - [Client 44] 创建新算法
2025-08-01 00:10:55,690 - INFO - [Algorithm] 设置客户端ID: 44
2025-08-01 00:10:55,690 - INFO - [Algorithm] 同步更新trainer的client_id: 44
2025-08-01 00:10:55,690 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:55,714 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:55,714 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:55,715 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:55,735 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:55,736 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:55,736 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:55,738 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:55,738 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:55,739 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:55,739 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:55,739 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:55,739 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:55,739 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:55,739 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:55,740 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:55,740 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:55,740 - INFO - 顺序分配客户端ID: 44
2025-08-01 00:10:55,740 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 44
2025-08-01 00:10:55,740 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:55,741 - WARNING - [Client 44] 数据源为None，已创建新数据源
2025-08-01 00:10:55,741 - WARNING - [Client 44] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:56,389 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:56,389 - INFO - [Client 44] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:56,389 - INFO - [Client 44] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:56,394 - INFO - [Client 44] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:56,394 - INFO - [Client 44] 初始化时成功加载数据
2025-08-01 00:10:56,394 - INFO - [客户端 44] 初始化验证通过
2025-08-01 00:10:56,394 - INFO - 客户端 44 实例创建成功
2025-08-01 00:10:56,394 - INFO - 客户端44已设置服务器引用
2025-08-01 00:10:56,395 - INFO - 客户端 44 已设置服务器引用
2025-08-01 00:10:56,395 - INFO - 客户端44已注册
2025-08-01 00:10:56,395 - INFO - 客户端 44 已成功注册到服务器
2025-08-01 00:10:56,395 - INFO - 开始创建客户端 45...
2025-08-01 00:10:56,395 - INFO - 初始化客户端, ID: 45
2025-08-01 00:10:56,413 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:56,413 - INFO - [Client 45] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:56,413 - INFO - [Trainer] 初始化训练器, client_id: 45
2025-08-01 00:10:56,420 - INFO - [Trainer 45] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:56,420 - INFO - [Trainer 45] 模型的输入通道数: 3
2025-08-01 00:10:56,421 - INFO - [Trainer 45] 强制使用CPU
2025-08-01 00:10:56,421 - INFO - [Trainer 45] 模型已移至设备: cpu
2025-08-01 00:10:56,421 - INFO - [Trainer 45] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:56,421 - INFO - [Trainer 45] 初始化完成
2025-08-01 00:10:56,421 - INFO - [Client 45] 创建新训练器
2025-08-01 00:10:56,421 - INFO - [Algorithm] 从训练器获取客户端ID: 45
2025-08-01 00:10:56,421 - INFO - [Algorithm] 初始化后修正client_id: 45
2025-08-01 00:10:56,422 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:56,422 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:56,422 - INFO - [Client 45] 创建新算法
2025-08-01 00:10:56,422 - INFO - [Algorithm] 设置客户端ID: 45
2025-08-01 00:10:56,422 - INFO - [Algorithm] 同步更新trainer的client_id: 45
2025-08-01 00:10:56,422 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:56,438 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:56,438 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:56,438 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:56,445 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:56,446 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:56,446 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:56,446 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:56,447 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:56,447 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:56,447 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:56,447 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:56,447 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:56,447 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:56,447 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:56,447 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:56,447 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:56,448 - INFO - 顺序分配客户端ID: 45
2025-08-01 00:10:56,448 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 45
2025-08-01 00:10:56,448 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:56,448 - WARNING - [Client 45] 数据源为None，已创建新数据源
2025-08-01 00:10:56,448 - WARNING - [Client 45] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:57,138 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:57,138 - INFO - [Client 45] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:57,138 - INFO - [Client 45] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:57,142 - INFO - [Client 45] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:57,142 - INFO - [Client 45] 初始化时成功加载数据
2025-08-01 00:10:57,143 - INFO - [客户端 45] 初始化验证通过
2025-08-01 00:10:57,143 - INFO - 客户端 45 实例创建成功
2025-08-01 00:10:57,143 - INFO - 客户端45已设置服务器引用
2025-08-01 00:10:57,143 - INFO - 客户端 45 已设置服务器引用
2025-08-01 00:10:57,143 - INFO - 客户端45已注册
2025-08-01 00:10:57,143 - INFO - 客户端 45 已成功注册到服务器
2025-08-01 00:10:57,143 - INFO - 开始创建客户端 46...
2025-08-01 00:10:57,143 - INFO - 初始化客户端, ID: 46
2025-08-01 00:10:57,161 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:57,161 - INFO - [Client 46] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:57,161 - INFO - [Trainer] 初始化训练器, client_id: 46
2025-08-01 00:10:57,170 - INFO - [Trainer 46] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:57,170 - INFO - [Trainer 46] 模型的输入通道数: 3
2025-08-01 00:10:57,170 - INFO - [Trainer 46] 强制使用CPU
2025-08-01 00:10:57,171 - INFO - [Trainer 46] 模型已移至设备: cpu
2025-08-01 00:10:57,171 - INFO - [Trainer 46] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:57,171 - INFO - [Trainer 46] 初始化完成
2025-08-01 00:10:57,172 - INFO - [Client 46] 创建新训练器
2025-08-01 00:10:57,172 - INFO - [Algorithm] 从训练器获取客户端ID: 46
2025-08-01 00:10:57,172 - INFO - [Algorithm] 初始化后修正client_id: 46
2025-08-01 00:10:57,172 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:57,172 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:57,172 - INFO - [Client 46] 创建新算法
2025-08-01 00:10:57,172 - INFO - [Algorithm] 设置客户端ID: 46
2025-08-01 00:10:57,172 - INFO - [Algorithm] 同步更新trainer的client_id: 46
2025-08-01 00:10:57,172 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:57,194 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:57,194 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:57,194 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:57,204 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:57,204 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:57,204 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:57,205 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:57,205 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:57,205 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:57,206 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:57,206 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:57,206 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:57,206 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:57,206 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:57,206 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:57,207 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:57,207 - INFO - 顺序分配客户端ID: 46
2025-08-01 00:10:57,207 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 46
2025-08-01 00:10:57,207 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:57,207 - WARNING - [Client 46] 数据源为None，已创建新数据源
2025-08-01 00:10:57,208 - WARNING - [Client 46] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:57,852 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:57,852 - INFO - [Client 46] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:57,852 - INFO - [Client 46] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:57,856 - INFO - [Client 46] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:57,856 - INFO - [Client 46] 初始化时成功加载数据
2025-08-01 00:10:57,857 - INFO - [客户端 46] 初始化验证通过
2025-08-01 00:10:57,857 - INFO - 客户端 46 实例创建成功
2025-08-01 00:10:57,857 - INFO - 客户端46已设置服务器引用
2025-08-01 00:10:57,857 - INFO - 客户端 46 已设置服务器引用
2025-08-01 00:10:57,857 - INFO - 客户端46已注册
2025-08-01 00:10:57,857 - INFO - 客户端 46 已成功注册到服务器
2025-08-01 00:10:57,857 - INFO - 开始创建客户端 47...
2025-08-01 00:10:57,857 - INFO - 初始化客户端, ID: 47
2025-08-01 00:10:57,874 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:57,875 - INFO - [Client 47] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:57,875 - INFO - [Trainer] 初始化训练器, client_id: 47
2025-08-01 00:10:57,882 - INFO - [Trainer 47] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:57,882 - INFO - [Trainer 47] 模型的输入通道数: 3
2025-08-01 00:10:57,882 - INFO - [Trainer 47] 强制使用CPU
2025-08-01 00:10:57,883 - INFO - [Trainer 47] 模型已移至设备: cpu
2025-08-01 00:10:57,883 - INFO - [Trainer 47] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:57,883 - INFO - [Trainer 47] 初始化完成
2025-08-01 00:10:57,883 - INFO - [Client 47] 创建新训练器
2025-08-01 00:10:57,883 - INFO - [Algorithm] 从训练器获取客户端ID: 47
2025-08-01 00:10:57,883 - INFO - [Algorithm] 初始化后修正client_id: 47
2025-08-01 00:10:57,883 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:57,883 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:57,883 - INFO - [Client 47] 创建新算法
2025-08-01 00:10:57,883 - INFO - [Algorithm] 设置客户端ID: 47
2025-08-01 00:10:57,883 - INFO - [Algorithm] 同步更新trainer的client_id: 47
2025-08-01 00:10:57,883 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:57,898 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:57,899 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:57,899 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:57,906 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:57,907 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:57,907 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:57,907 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:57,908 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:57,908 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:57,908 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:57,908 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:57,908 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:57,908 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:57,908 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:57,908 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:57,908 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:57,908 - INFO - 顺序分配客户端ID: 47
2025-08-01 00:10:57,908 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 47
2025-08-01 00:10:57,909 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:57,909 - WARNING - [Client 47] 数据源为None，已创建新数据源
2025-08-01 00:10:57,909 - WARNING - [Client 47] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:58,551 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:58,551 - INFO - [Client 47] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:58,551 - INFO - [Client 47] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:58,554 - INFO - [Client 47] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:58,555 - INFO - [Client 47] 初始化时成功加载数据
2025-08-01 00:10:58,555 - INFO - [客户端 47] 初始化验证通过
2025-08-01 00:10:58,555 - INFO - 客户端 47 实例创建成功
2025-08-01 00:10:58,555 - INFO - 客户端47已设置服务器引用
2025-08-01 00:10:58,555 - INFO - 客户端 47 已设置服务器引用
2025-08-01 00:10:58,555 - INFO - 客户端47已注册
2025-08-01 00:10:58,555 - INFO - 客户端 47 已成功注册到服务器
2025-08-01 00:10:58,555 - INFO - 开始创建客户端 48...
2025-08-01 00:10:58,555 - INFO - 初始化客户端, ID: 48
2025-08-01 00:10:58,572 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:58,572 - INFO - [Client 48] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:58,572 - INFO - [Trainer] 初始化训练器, client_id: 48
2025-08-01 00:10:58,579 - INFO - [Trainer 48] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:58,579 - INFO - [Trainer 48] 模型的输入通道数: 3
2025-08-01 00:10:58,579 - INFO - [Trainer 48] 强制使用CPU
2025-08-01 00:10:58,579 - INFO - [Trainer 48] 模型已移至设备: cpu
2025-08-01 00:10:58,580 - INFO - [Trainer 48] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:58,580 - INFO - [Trainer 48] 初始化完成
2025-08-01 00:10:58,580 - INFO - [Client 48] 创建新训练器
2025-08-01 00:10:58,580 - INFO - [Algorithm] 从训练器获取客户端ID: 48
2025-08-01 00:10:58,580 - INFO - [Algorithm] 初始化后修正client_id: 48
2025-08-01 00:10:58,580 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:58,580 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:58,580 - INFO - [Client 48] 创建新算法
2025-08-01 00:10:58,580 - INFO - [Algorithm] 设置客户端ID: 48
2025-08-01 00:10:58,580 - INFO - [Algorithm] 同步更新trainer的client_id: 48
2025-08-01 00:10:58,580 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:58,597 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:58,597 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:58,597 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:58,603 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:58,603 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:58,603 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:58,604 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:58,604 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:58,604 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:58,604 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:58,604 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:58,604 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:58,604 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:58,604 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:58,605 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:58,605 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:58,605 - INFO - 顺序分配客户端ID: 48
2025-08-01 00:10:58,605 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 48
2025-08-01 00:10:58,605 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:58,605 - WARNING - [Client 48] 数据源为None，已创建新数据源
2025-08-01 00:10:58,605 - WARNING - [Client 48] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:59,202 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:59,202 - INFO - [Client 48] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:59,202 - INFO - [Client 48] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:59,206 - INFO - [Client 48] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:59,206 - INFO - [Client 48] 初始化时成功加载数据
2025-08-01 00:10:59,206 - INFO - [客户端 48] 初始化验证通过
2025-08-01 00:10:59,206 - INFO - 客户端 48 实例创建成功
2025-08-01 00:10:59,206 - INFO - 客户端48已设置服务器引用
2025-08-01 00:10:59,206 - INFO - 客户端 48 已设置服务器引用
2025-08-01 00:10:59,207 - INFO - 客户端48已注册
2025-08-01 00:10:59,207 - INFO - 客户端 48 已成功注册到服务器
2025-08-01 00:10:59,208 - INFO - 开始创建客户端 49...
2025-08-01 00:10:59,208 - INFO - 初始化客户端, ID: 49
2025-08-01 00:10:59,222 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:59,223 - INFO - [Client 49] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:59,223 - INFO - [Trainer] 初始化训练器, client_id: 49
2025-08-01 00:10:59,228 - INFO - [Trainer 49] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:59,229 - INFO - [Trainer 49] 模型的输入通道数: 3
2025-08-01 00:10:59,229 - INFO - [Trainer 49] 强制使用CPU
2025-08-01 00:10:59,229 - INFO - [Trainer 49] 模型已移至设备: cpu
2025-08-01 00:10:59,230 - INFO - [Trainer 49] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:59,230 - INFO - [Trainer 49] 初始化完成
2025-08-01 00:10:59,230 - INFO - [Client 49] 创建新训练器
2025-08-01 00:10:59,230 - INFO - [Algorithm] 从训练器获取客户端ID: 49
2025-08-01 00:10:59,230 - INFO - [Algorithm] 初始化后修正client_id: 49
2025-08-01 00:10:59,230 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:59,230 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:59,230 - INFO - [Client 49] 创建新算法
2025-08-01 00:10:59,231 - INFO - [Algorithm] 设置客户端ID: 49
2025-08-01 00:10:59,231 - INFO - [Algorithm] 同步更新trainer的client_id: 49
2025-08-01 00:10:59,231 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:59,246 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:59,246 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:59,246 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:59,252 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:59,252 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:59,252 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:59,252 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:59,253 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:59,253 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:59,253 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:59,253 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:59,253 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:59,253 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:59,253 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:59,253 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:59,253 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:59,253 - INFO - 顺序分配客户端ID: 49
2025-08-01 00:10:59,253 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 49
2025-08-01 00:10:59,253 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:59,254 - WARNING - [Client 49] 数据源为None，已创建新数据源
2025-08-01 00:10:59,254 - WARNING - [Client 49] 数据源trainset为None，已设置为空列表
2025-08-01 00:10:59,844 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:10:59,844 - INFO - [Client 49] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:10:59,844 - INFO - [Client 49] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:10:59,848 - INFO - [Client 49] 成功划分数据集，分配到 300 个样本
2025-08-01 00:10:59,848 - INFO - [Client 49] 初始化时成功加载数据
2025-08-01 00:10:59,849 - INFO - [客户端 49] 初始化验证通过
2025-08-01 00:10:59,849 - INFO - 客户端 49 实例创建成功
2025-08-01 00:10:59,849 - INFO - 客户端49已设置服务器引用
2025-08-01 00:10:59,849 - INFO - 客户端 49 已设置服务器引用
2025-08-01 00:10:59,849 - INFO - 客户端49已注册
2025-08-01 00:10:59,849 - INFO - 客户端 49 已成功注册到服务器
2025-08-01 00:10:59,849 - INFO - 开始创建客户端 50...
2025-08-01 00:10:59,849 - INFO - 初始化客户端, ID: 50
2025-08-01 00:10:59,867 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:59,867 - INFO - [Client 50] 初始化阶段 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:59,867 - INFO - [Trainer] 初始化训练器, client_id: 50
2025-08-01 00:10:59,873 - INFO - [Trainer 50] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:59,873 - INFO - [Trainer 50] 模型的输入通道数: 3
2025-08-01 00:10:59,873 - INFO - [Trainer 50] 强制使用CPU
2025-08-01 00:10:59,874 - INFO - [Trainer 50] 模型已移至设备: cpu
2025-08-01 00:10:59,874 - INFO - [Trainer 50] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:59,874 - INFO - [Trainer 50] 初始化完成
2025-08-01 00:10:59,874 - INFO - [Client 50] 创建新训练器
2025-08-01 00:10:59,874 - INFO - [Algorithm] 从训练器获取客户端ID: 50
2025-08-01 00:10:59,874 - INFO - [Algorithm] 初始化后修正client_id: 50
2025-08-01 00:10:59,874 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:59,874 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:59,875 - INFO - [Client 50] 创建新算法
2025-08-01 00:10:59,875 - INFO - [Algorithm] 设置客户端ID: 50
2025-08-01 00:10:59,875 - INFO - [Algorithm] 同步更新trainer的client_id: 50
2025-08-01 00:10:59,875 - INFO - [Client None] 父类初始化完成
2025-08-01 00:10:59,891 - INFO - ✅ 成功创建模型 resnet_9: Model
2025-08-01 00:10:59,892 - INFO - [Client None] 父类初始化后 动态创建模型 resnet_9，输入通道数: 3, 类别数: 10
2025-08-01 00:10:59,892 - INFO - [Trainer] 初始化训练器, client_id: None
2025-08-01 00:10:59,898 - INFO - [Trainer None] 创建了模型的深拷贝并重置BatchNorm统计信息，避免实例共享
2025-08-01 00:10:59,898 - INFO - [Trainer None] 模型的输入通道数: 3
2025-08-01 00:10:59,898 - INFO - [Trainer None] 强制使用CPU
2025-08-01 00:10:59,899 - INFO - [Trainer None] 模型已移至设备: cpu
2025-08-01 00:10:59,899 - INFO - [Trainer None] 优化器已创建: SGD, lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:10:59,899 - INFO - [Trainer None] 初始化完成
2025-08-01 00:10:59,899 - INFO - [Client None] 父类初始化后创建新训练器
2025-08-01 00:10:59,899 - INFO - [Algorithm] 从训练器获取客户端ID: None
2025-08-01 00:10:59,900 - INFO - [Algorithm] 初始化后修正client_id: None
2025-08-01 00:10:59,900 - INFO - [Algorithm] 初始化完成
2025-08-01 00:10:59,900 - INFO - [Algorithm] 参数配置：tau_max=5, lambda=0.5
2025-08-01 00:10:59,900 - INFO - [Client None] 父类初始化后创建新算法
2025-08-01 00:10:59,900 - INFO - [Client None] 开始加载数据
2025-08-01 00:10:59,900 - INFO - 顺序分配客户端ID: 50
2025-08-01 00:10:59,900 - WARNING - [Client Load Data] 客户端ID为None，已分配新ID: 50
2025-08-01 00:10:59,900 - WARNING - [Client Load Data] 调用堆栈: ['  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 163, in <module>\n    main()\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl.py", line 111, in main\n    client = Client(client_id=client_id)\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 613, in __init__\n    if self.load_data():\n', '  File "D:\\Experiment\\orgin-plato-main\\plato-main-7.6\\examples\\async\\SC_AFL\\sc_afl_client.py", line 671, in load_data\n    stack = traceback.format_stack()\n']
2025-08-01 00:10:59,901 - WARNING - [Client 50] 数据源为None，已创建新数据源
2025-08-01 00:10:59,901 - WARNING - [Client 50] 数据源trainset为None，已设置为空列表
2025-08-01 00:11:00,486 - INFO - ✅ 成功加载数据集 CIFAR10: 50000 样本
2025-08-01 00:11:00,487 - INFO - [Client 50] 成功动态加载CIFAR10数据集，大小: 50000
2025-08-01 00:11:00,487 - INFO - [Client 50] 开始使用标准Dirichlet sampler划分数据集，总客户端数: 50, 浓度参数: 0.1
2025-08-01 00:11:00,491 - INFO - [Client 50] 成功划分数据集，分配到 300 个样本
2025-08-01 00:11:00,491 - INFO - [Client 50] 初始化时成功加载数据
2025-08-01 00:11:00,491 - INFO - [客户端 50] 初始化验证通过
2025-08-01 00:11:00,491 - INFO - 客户端 50 实例创建成功
2025-08-01 00:11:00,491 - INFO - 客户端50已设置服务器引用
2025-08-01 00:11:00,491 - INFO - 客户端 50 已设置服务器引用
2025-08-01 00:11:00,491 - INFO - 客户端50已注册
2025-08-01 00:11:00,491 - INFO - 客户端 50 已成功注册到服务器
2025-08-01 00:11:00,491 - INFO - 已成功创建和注册 50 个客户端
2025-08-01 00:11:00,491 - INFO - 服务器属性检查:
2025-08-01 00:11:00,491 - INFO - - 客户端数量: 50
2025-08-01 00:11:00,492 - INFO - - 全局模型: 已初始化
2025-08-01 00:11:00,492 - INFO - - 算法: 已初始化
2025-08-01 00:11:00,492 - INFO - - 训练器: 已初始化
2025-08-01 00:11:00,492 - INFO - 准备启动服务器...
2025-08-01 00:11:00,493 - INFO - [Server #10168] 启动中...
2025-08-01 00:11:00,494 - INFO - 服务器将使用事件循环: <ProactorEventLoop running=False closed=False debug=False>
2025-08-01 00:11:00,495 - INFO - [Client 1] 模型已放置到设备: cpu
2025-08-01 00:11:00,518 - INFO - [Client 1] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,524 - INFO - [Client 1] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,525 - INFO - [Algorithm] 设置客户端ID: 1
2025-08-01 00:11:00,525 - INFO - [Algorithm] 同步更新trainer的client_id: 1
2025-08-01 00:11:00,525 - INFO - [Client 1] 已更新algorithm的client_id
2025-08-01 00:11:00,525 - INFO - [Client 1] 模型初始化完成
2025-08-01 00:11:00,525 - INFO - 客户端 1 模型初始化成功
2025-08-01 00:11:00,528 - INFO - 客户端 1 异步训练线程已启动
2025-08-01 00:11:00,528 - INFO - [Client 2] 模型已放置到设备: cpu
2025-08-01 00:11:00,548 - INFO - [Client 2] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,554 - INFO - [Client 2] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,554 - INFO - [Algorithm] 设置客户端ID: 2
2025-08-01 00:11:00,554 - INFO - [Algorithm] 同步更新trainer的client_id: 2
2025-08-01 00:11:00,555 - INFO - [Client 2] 已更新algorithm的client_id
2025-08-01 00:11:00,556 - INFO - [Client 2] 模型初始化完成
2025-08-01 00:11:00,556 - INFO - 客户端 2 模型初始化成功
2025-08-01 00:11:00,557 - INFO - 客户端 2 异步训练线程已启动
2025-08-01 00:11:00,557 - INFO - [Client 3] 模型已放置到设备: cpu
2025-08-01 00:11:00,582 - INFO - [Client 3] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,589 - INFO - [Client 3] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,589 - INFO - [Algorithm] 设置客户端ID: 3
2025-08-01 00:11:00,590 - INFO - [Algorithm] 同步更新trainer的client_id: 3
2025-08-01 00:11:00,590 - INFO - [Client 3] 已更新algorithm的client_id
2025-08-01 00:11:00,590 - INFO - [Client 3] 模型初始化完成
2025-08-01 00:11:00,590 - INFO - 客户端 3 模型初始化成功
2025-08-01 00:11:00,590 - INFO - 客户端 3 异步训练线程已启动
2025-08-01 00:11:00,592 - INFO - [Client 4] 模型已放置到设备: cpu
2025-08-01 00:11:00,619 - INFO - [Client 4] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,626 - INFO - [Client 4] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,626 - INFO - [Algorithm] 设置客户端ID: 4
2025-08-01 00:11:00,626 - INFO - [Algorithm] 同步更新trainer的client_id: 4
2025-08-01 00:11:00,626 - INFO - [Client 4] 已更新algorithm的client_id
2025-08-01 00:11:00,627 - INFO - [Client 4] 模型初始化完成
2025-08-01 00:11:00,627 - INFO - 客户端 4 模型初始化成功
2025-08-01 00:11:00,628 - INFO - 客户端 4 异步训练线程已启动
2025-08-01 00:11:00,629 - INFO - [Client 5] 模型已放置到设备: cpu
2025-08-01 00:11:00,657 - INFO - [Client 5] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,664 - INFO - [Client 5] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,664 - INFO - [Algorithm] 设置客户端ID: 5
2025-08-01 00:11:00,664 - INFO - [Algorithm] 同步更新trainer的client_id: 5
2025-08-01 00:11:00,664 - INFO - [Client 5] 已更新algorithm的client_id
2025-08-01 00:11:00,664 - INFO - [Client 5] 模型初始化完成
2025-08-01 00:11:00,664 - INFO - 客户端 5 模型初始化成功
2025-08-01 00:11:00,667 - INFO - 客户端 5 异步训练线程已启动
2025-08-01 00:11:00,668 - INFO - [Client 6] 模型已放置到设备: cpu
2025-08-01 00:11:00,693 - INFO - [Client 6] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,708 - INFO - [Client 6] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,708 - INFO - [Algorithm] 设置客户端ID: 6
2025-08-01 00:11:00,709 - INFO - [Algorithm] 同步更新trainer的client_id: 6
2025-08-01 00:11:00,709 - INFO - [Client 6] 已更新algorithm的client_id
2025-08-01 00:11:00,709 - INFO - [Client 6] 模型初始化完成
2025-08-01 00:11:00,709 - INFO - 客户端 6 模型初始化成功
2025-08-01 00:11:00,710 - INFO - 客户端 6 异步训练线程已启动
2025-08-01 00:11:00,711 - INFO - [Client 7] 模型已放置到设备: cpu
2025-08-01 00:11:00,751 - INFO - [Client 7] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,766 - INFO - [Client 7] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,766 - INFO - [Algorithm] 设置客户端ID: 7
2025-08-01 00:11:00,766 - INFO - [Algorithm] 同步更新trainer的client_id: 7
2025-08-01 00:11:00,767 - INFO - [Client 7] 已更新algorithm的client_id
2025-08-01 00:11:00,767 - INFO - [Client 7] 模型初始化完成
2025-08-01 00:11:00,767 - INFO - 客户端 7 模型初始化成功
2025-08-01 00:11:00,769 - INFO - 客户端 7 异步训练线程已启动
2025-08-01 00:11:00,775 - INFO - [Client 8] 模型已放置到设备: cpu
2025-08-01 00:11:00,820 - INFO - [Client 8] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,833 - INFO - [Client 8] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,833 - INFO - [Algorithm] 设置客户端ID: 8
2025-08-01 00:11:00,835 - INFO - [Algorithm] 同步更新trainer的client_id: 8
2025-08-01 00:11:00,835 - INFO - [Client 8] 已更新algorithm的client_id
2025-08-01 00:11:00,835 - INFO - [Client 8] 模型初始化完成
2025-08-01 00:11:00,835 - INFO - 客户端 8 模型初始化成功
2025-08-01 00:11:00,837 - INFO - 客户端 8 异步训练线程已启动
2025-08-01 00:11:00,843 - INFO - [Client 9] 模型已放置到设备: cpu
2025-08-01 00:11:00,901 - INFO - [Client 9] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,915 - INFO - [Client 9] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:00,915 - INFO - [Algorithm] 设置客户端ID: 9
2025-08-01 00:11:00,915 - INFO - [Algorithm] 同步更新trainer的client_id: 9
2025-08-01 00:11:00,915 - INFO - [Client 9] 已更新algorithm的client_id
2025-08-01 00:11:00,915 - INFO - [Client 9] 模型初始化完成
2025-08-01 00:11:00,916 - INFO - 客户端 9 模型初始化成功
2025-08-01 00:11:00,916 - INFO - 客户端 9 异步训练线程已启动
2025-08-01 00:11:00,923 - INFO - [Client 10] 模型已放置到设备: cpu
2025-08-01 00:11:00,931 - INFO - 客户端 3 开始异步训练循环
2025-08-01 00:11:00,940 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:00,963 - INFO - [客户端类型: Client, ID: 3] 准备开始训练
2025-08-01 00:11:00,963 - INFO - [客户端 3] 使用的训练器 client_id: 3
2025-08-01 00:11:00,964 - INFO - [Client 3] 开始验证训练集
2025-08-01 00:11:01,026 - INFO - [Client 10] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,057 - INFO - [Client 10] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,057 - INFO - [Algorithm] 设置客户端ID: 10
2025-08-01 00:11:01,058 - INFO - [Algorithm] 同步更新trainer的client_id: 10
2025-08-01 00:11:01,058 - INFO - [Client 10] 已更新algorithm的client_id
2025-08-01 00:11:01,058 - INFO - [Client 10] 模型初始化完成
2025-08-01 00:11:01,058 - INFO - 客户端 10 模型初始化成功
2025-08-01 00:11:01,058 - INFO - 客户端 10 异步训练线程已启动
2025-08-01 00:11:01,062 - INFO - [Client 11] 模型已放置到设备: cpu
2025-08-01 00:11:01,064 - INFO - [Client 3] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:01,064 - INFO - [Client 3] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:01,065 - INFO - [Trainer 3] 开始训练
2025-08-01 00:11:01,065 - INFO - [Trainer 3] 训练集大小: 300
2025-08-01 00:11:01,066 - INFO - [Trainer 3] 模型已移至设备: cpu
2025-08-01 00:11:01,066 - INFO - [Trainer 3] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:01,075 - INFO - [Trainer 3] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:01,076 - INFO - [Trainer 3] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:01,076 - INFO - [Trainer 3] 开始第 1/5 个epoch
2025-08-01 00:11:01,153 - INFO - [Client 11] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,183 - INFO - [Client 11] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,183 - INFO - [Algorithm] 设置客户端ID: 11
2025-08-01 00:11:01,184 - INFO - [Algorithm] 同步更新trainer的client_id: 11
2025-08-01 00:11:01,184 - INFO - [Client 11] 已更新algorithm的client_id
2025-08-01 00:11:01,185 - INFO - [Client 11] 模型初始化完成
2025-08-01 00:11:01,185 - INFO - 客户端 11 模型初始化成功
2025-08-01 00:11:01,190 - INFO - 客户端 11 异步训练线程已启动
2025-08-01 00:11:01,195 - INFO - [Client 12] 模型已放置到设备: cpu
2025-08-01 00:11:01,224 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4972, y=[2, 6, 2, 2, 2]
2025-08-01 00:11:01,224 - INFO - [Trainer 3] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:01,266 - INFO - [Client 12] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,286 - INFO - [Trainer 3] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:01,288 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2282
2025-08-01 00:11:01,289 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:01,289 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 00:11:01,289 - INFO - [Client 12] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,298 - INFO - [Algorithm] 设置客户端ID: 12
2025-08-01 00:11:01,298 - INFO - [Algorithm] 同步更新trainer的client_id: 12
2025-08-01 00:11:01,298 - INFO - [Client 12] 已更新algorithm的client_id
2025-08-01 00:11:01,298 - INFO - [Client 12] 模型初始化完成
2025-08-01 00:11:01,299 - INFO - 客户端 12 模型初始化成功
2025-08-01 00:11:01,299 - INFO - 客户端 12 异步训练线程已启动
2025-08-01 00:11:01,301 - INFO - [Client 13] 模型已放置到设备: cpu
2025-08-01 00:11:01,346 - INFO - [Client 13] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,360 - INFO - [Client 13] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,361 - INFO - [Algorithm] 设置客户端ID: 13
2025-08-01 00:11:01,361 - INFO - [Algorithm] 同步更新trainer的client_id: 13
2025-08-01 00:11:01,361 - INFO - [Client 13] 已更新algorithm的client_id
2025-08-01 00:11:01,362 - INFO - [Client 13] 模型初始化完成
2025-08-01 00:11:01,362 - INFO - 客户端 13 模型初始化成功
2025-08-01 00:11:01,362 - INFO - 客户端 13 异步训练线程已启动
2025-08-01 00:11:01,366 - INFO - [Client 14] 模型已放置到设备: cpu
2025-08-01 00:11:01,410 - INFO - [Client 14] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,420 - INFO - [Client 14] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,420 - INFO - [Algorithm] 设置客户端ID: 14
2025-08-01 00:11:01,420 - INFO - [Algorithm] 同步更新trainer的client_id: 14
2025-08-01 00:11:01,420 - INFO - [Client 14] 已更新algorithm的client_id
2025-08-01 00:11:01,420 - INFO - [Client 14] 模型初始化完成
2025-08-01 00:11:01,420 - INFO - 客户端 14 模型初始化成功
2025-08-01 00:11:01,422 - INFO - 客户端 14 异步训练线程已启动
2025-08-01 00:11:01,422 - INFO - [Client 15] 模型已放置到设备: cpu
2025-08-01 00:11:01,490 - INFO - [Client 15] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,517 - INFO - [Client 15] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,518 - INFO - [Algorithm] 设置客户端ID: 15
2025-08-01 00:11:01,518 - INFO - [Algorithm] 同步更新trainer的client_id: 15
2025-08-01 00:11:01,518 - INFO - [Client 15] 已更新algorithm的client_id
2025-08-01 00:11:01,518 - INFO - [Client 15] 模型初始化完成
2025-08-01 00:11:01,519 - INFO - 客户端 15 模型初始化成功
2025-08-01 00:11:01,520 - INFO - 客户端 15 异步训练线程已启动
2025-08-01 00:11:01,521 - INFO - [Client 16] 模型已放置到设备: cpu
2025-08-01 00:11:01,554 - INFO - [Client 16] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,569 - INFO - [Client 16] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,569 - INFO - [Algorithm] 设置客户端ID: 16
2025-08-01 00:11:01,569 - INFO - [Algorithm] 同步更新trainer的client_id: 16
2025-08-01 00:11:01,569 - INFO - [Client 16] 已更新algorithm的client_id
2025-08-01 00:11:01,569 - INFO - [Client 16] 模型初始化完成
2025-08-01 00:11:01,570 - INFO - 客户端 16 模型初始化成功
2025-08-01 00:11:01,572 - INFO - 客户端 16 异步训练线程已启动
2025-08-01 00:11:01,573 - INFO - [Client 17] 模型已放置到设备: cpu
2025-08-01 00:11:01,600 - INFO - [Client 17] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,619 - INFO - [Client 17] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,619 - INFO - [Algorithm] 设置客户端ID: 17
2025-08-01 00:11:01,619 - INFO - [Algorithm] 同步更新trainer的client_id: 17
2025-08-01 00:11:01,619 - INFO - [Client 17] 已更新algorithm的client_id
2025-08-01 00:11:01,620 - INFO - [Client 17] 模型初始化完成
2025-08-01 00:11:01,620 - INFO - 客户端 17 模型初始化成功
2025-08-01 00:11:01,620 - INFO - 客户端 17 异步训练线程已启动
2025-08-01 00:11:01,623 - INFO - [Client 18] 模型已放置到设备: cpu
2025-08-01 00:11:01,631 - INFO - [Trainer 3] Batch 0, Loss: 2.1754
2025-08-01 00:11:01,638 - INFO - [Client 18] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,649 - INFO - 客户端 1 开始异步训练循环
2025-08-01 00:11:01,649 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:01,650 - INFO - [客户端类型: Client, ID: 1] 准备开始训练
2025-08-01 00:11:01,650 - INFO - [客户端 1] 使用的训练器 client_id: 1
2025-08-01 00:11:01,650 - INFO - [Client 1] 开始验证训练集
2025-08-01 00:11:01,652 - INFO - [Client 1] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:01,653 - INFO - [Client 1] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:01,653 - INFO - [Trainer 1] 开始训练
2025-08-01 00:11:01,653 - INFO - [Trainer 1] 训练集大小: 300
2025-08-01 00:11:01,654 - INFO - [Trainer 1] 模型已移至设备: cpu
2025-08-01 00:11:01,654 - INFO - [Trainer 1] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:01,656 - INFO - [Trainer 1] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:01,657 - INFO - [Trainer 1] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:01,657 - INFO - [Trainer 1] 开始第 1/5 个epoch
2025-08-01 00:11:01,658 - INFO - [Client 18] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,658 - INFO - [Algorithm] 设置客户端ID: 18
2025-08-01 00:11:01,659 - INFO - [Algorithm] 同步更新trainer的client_id: 18
2025-08-01 00:11:01,659 - INFO - [Client 18] 已更新algorithm的client_id
2025-08-01 00:11:01,659 - INFO - [Client 18] 模型初始化完成
2025-08-01 00:11:01,659 - INFO - 客户端 18 模型初始化成功
2025-08-01 00:11:01,662 - INFO - 客户端 18 异步训练线程已启动
2025-08-01 00:11:01,664 - INFO - [Client 19] 模型已放置到设备: cpu
2025-08-01 00:11:01,701 - INFO - [Client 19] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,723 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2453, y=[8, 5, 5, 4, 8]
2025-08-01 00:11:01,723 - INFO - [Trainer 1] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:01,730 - INFO - [Client 19] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,732 - INFO - [Algorithm] 设置客户端ID: 19
2025-08-01 00:11:01,732 - INFO - [Algorithm] 同步更新trainer的client_id: 19
2025-08-01 00:11:01,732 - INFO - [Client 19] 已更新algorithm的client_id
2025-08-01 00:11:01,732 - INFO - [Client 19] 模型初始化完成
2025-08-01 00:11:01,732 - INFO - 客户端 19 模型初始化成功
2025-08-01 00:11:01,734 - INFO - 客户端 19 异步训练线程已启动
2025-08-01 00:11:01,742 - INFO - [Client 20] 模型已放置到设备: cpu
2025-08-01 00:11:01,782 - INFO - [Trainer 1] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:01,787 - INFO - [Client 20] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,790 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1408
2025-08-01 00:11:01,790 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:01,790 - INFO - [Trainer 1] 标签样本: [0, 8, 5, 5, 5]
2025-08-01 00:11:01,875 - INFO - [Client 20] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,876 - INFO - [Algorithm] 设置客户端ID: 20
2025-08-01 00:11:01,876 - INFO - [Algorithm] 同步更新trainer的client_id: 20
2025-08-01 00:11:01,876 - INFO - [Client 20] 已更新algorithm的client_id
2025-08-01 00:11:01,876 - INFO - [Client 20] 模型初始化完成
2025-08-01 00:11:01,876 - INFO - 客户端 20 模型初始化成功
2025-08-01 00:11:01,878 - INFO - 客户端 20 异步训练线程已启动
2025-08-01 00:11:01,893 - INFO - [Client 21] 模型已放置到设备: cpu
2025-08-01 00:11:01,957 - INFO - [Client 21] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,975 - INFO - 客户端 5 开始异步训练循环
2025-08-01 00:11:01,975 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:01,977 - INFO - [客户端类型: Client, ID: 5] 准备开始训练
2025-08-01 00:11:01,977 - INFO - [客户端 5] 使用的训练器 client_id: 5
2025-08-01 00:11:01,977 - INFO - [Client 5] 开始验证训练集
2025-08-01 00:11:01,978 - INFO - [Client 5] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:01,979 - INFO - [Client 5] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:01,979 - INFO - [Trainer 5] 开始训练
2025-08-01 00:11:01,979 - INFO - [Trainer 5] 训练集大小: 300
2025-08-01 00:11:01,979 - INFO - [Trainer 5] 模型已移至设备: cpu
2025-08-01 00:11:01,981 - INFO - [Trainer 5] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:01,984 - INFO - [Trainer 5] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:01,985 - INFO - [Trainer 5] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:01,986 - INFO - [Trainer 5] 开始第 1/5 个epoch
2025-08-01 00:11:01,994 - INFO - [Client 21] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:01,995 - INFO - [Algorithm] 设置客户端ID: 21
2025-08-01 00:11:01,995 - INFO - [Algorithm] 同步更新trainer的client_id: 21
2025-08-01 00:11:01,996 - INFO - [Client 21] 已更新algorithm的client_id
2025-08-01 00:11:01,996 - INFO - [Client 21] 模型初始化完成
2025-08-01 00:11:01,997 - INFO - 客户端 21 模型初始化成功
2025-08-01 00:11:01,999 - INFO - 客户端 21 异步训练线程已启动
2025-08-01 00:11:02,007 - INFO - [Client 22] 模型已放置到设备: cpu
2025-08-01 00:11:02,037 - INFO - 客户端 6 开始异步训练循环
2025-08-01 00:11:02,038 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:02,038 - INFO - [Trainer 1] Batch 0, Loss: 2.3532
2025-08-01 00:11:02,042 - INFO - [客户端类型: Client, ID: 6] 准备开始训练
2025-08-01 00:11:02,042 - INFO - [客户端 6] 使用的训练器 client_id: 6
2025-08-01 00:11:02,042 - INFO - [Client 6] 开始验证训练集
2025-08-01 00:11:02,048 - INFO - [Client 6] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:02,049 - INFO - [Client 6] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:02,049 - INFO - [Trainer 6] 开始训练
2025-08-01 00:11:02,051 - INFO - [Trainer 6] 训练集大小: 300
2025-08-01 00:11:02,058 - INFO - [Trainer 6] 模型已移至设备: cpu
2025-08-01 00:11:02,059 - INFO - [Trainer 6] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:02,061 - INFO - [Trainer 6] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:02,063 - INFO - [Client 22] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:02,066 - INFO - [Trainer 6] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:02,066 - INFO - [Trainer 6] 开始第 1/5 个epoch
2025-08-01 00:11:02,114 - INFO - [Client 22] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:02,114 - INFO - [Algorithm] 设置客户端ID: 22
2025-08-01 00:11:02,115 - INFO - [Algorithm] 同步更新trainer的client_id: 22
2025-08-01 00:11:02,115 - INFO - [Client 22] 已更新algorithm的client_id
2025-08-01 00:11:02,116 - INFO - [Client 22] 模型初始化完成
2025-08-01 00:11:02,117 - INFO - 客户端 22 模型初始化成功
2025-08-01 00:11:02,119 - INFO - 客户端 22 异步训练线程已启动
2025-08-01 00:11:02,122 - INFO - [Client 23] 模型已放置到设备: cpu
2025-08-01 00:11:02,133 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5726, y=[7, 7, 7, 7, 9]
2025-08-01 00:11:02,133 - INFO - [Trainer 5] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:02,168 - INFO - [Client 23] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:02,201 - INFO - [Client 23] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:02,202 - INFO - [Algorithm] 设置客户端ID: 23
2025-08-01 00:11:02,202 - INFO - [Algorithm] 同步更新trainer的client_id: 23
2025-08-01 00:11:02,203 - INFO - [Client 23] 已更新algorithm的client_id
2025-08-01 00:11:02,204 - INFO - [Client 23] 模型初始化完成
2025-08-01 00:11:02,204 - INFO - 客户端 23 模型初始化成功
2025-08-01 00:11:02,207 - INFO - 客户端 23 异步训练线程已启动
2025-08-01 00:11:02,217 - INFO - [Client 24] 模型已放置到设备: cpu
2025-08-01 00:11:02,222 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1347, y=[8, 7, 7, 8, 8]
2025-08-01 00:11:02,222 - INFO - [Trainer 6] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:02,253 - INFO - [Client 24] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:02,267 - INFO - [Trainer 5] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:02,280 - INFO - [Client 24] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:02,281 - INFO - [Algorithm] 设置客户端ID: 24
2025-08-01 00:11:02,281 - INFO - [Algorithm] 同步更新trainer的client_id: 24
2025-08-01 00:11:02,282 - INFO - [Client 24] 已更新algorithm的client_id
2025-08-01 00:11:02,282 - INFO - [Client 24] 模型初始化完成
2025-08-01 00:11:02,283 - INFO - 客户端 24 模型初始化成功
2025-08-01 00:11:02,284 - INFO - 客户端 24 异步训练线程已启动
2025-08-01 00:11:02,289 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3458
2025-08-01 00:11:02,289 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:02,289 - INFO - [Trainer 5] 标签样本: [7, 9, 7, 7, 7]
2025-08-01 00:11:02,290 - INFO - [Client 25] 模型已放置到设备: cpu
2025-08-01 00:11:02,345 - INFO - [Trainer 6] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:02,349 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2269
2025-08-01 00:11:02,350 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:02,350 - INFO - [Trainer 6] 标签样本: [8, 8, 7, 8, 0]
2025-08-01 00:11:02,403 - INFO - [Client 25] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:02,513 - INFO - [Client 25] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:02,514 - INFO - [Algorithm] 设置客户端ID: 25
2025-08-01 00:11:02,515 - INFO - [Algorithm] 同步更新trainer的client_id: 25
2025-08-01 00:11:02,518 - INFO - [Client 25] 已更新algorithm的client_id
2025-08-01 00:11:02,518 - INFO - [Client 25] 模型初始化完成
2025-08-01 00:11:02,519 - INFO - 客户端 25 模型初始化成功
2025-08-01 00:11:02,523 - INFO - 客户端 25 异步训练线程已启动
2025-08-01 00:11:02,525 - INFO - [Client 26] 模型已放置到设备: cpu
2025-08-01 00:11:02,677 - INFO - [Client 26] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:02,736 - INFO - [Trainer 5] Batch 0, Loss: 2.2931
2025-08-01 00:11:02,860 - INFO - [Trainer 6] Batch 0, Loss: 2.2943
2025-08-01 00:11:02,978 - INFO - 客户端 7 开始异步训练循环
2025-08-01 00:11:02,980 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:02,982 - INFO - [客户端类型: Client, ID: 7] 准备开始训练
2025-08-01 00:11:02,983 - INFO - [客户端 7] 使用的训练器 client_id: 7
2025-08-01 00:11:02,989 - INFO - [Client 7] 开始验证训练集
2025-08-01 00:11:02,992 - INFO - [Client 7] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:02,993 - INFO - [Client 7] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:02,996 - INFO - [Trainer 7] 开始训练
2025-08-01 00:11:02,997 - INFO - [Trainer 7] 训练集大小: 300
2025-08-01 00:11:02,999 - INFO - [Trainer 7] 模型已移至设备: cpu
2025-08-01 00:11:03,002 - INFO - [Trainer 7] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:03,004 - INFO - [Trainer 7] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:03,006 - INFO - [Trainer 7] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:03,007 - INFO - [Trainer 7] 开始第 1/5 个epoch
2025-08-01 00:11:03,087 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3691, y=[2, 2, 2, 2, 2]
2025-08-01 00:11:03,088 - INFO - [Trainer 7] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:03,188 - INFO - [Trainer 7] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:03,199 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2974
2025-08-01 00:11:03,200 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:03,200 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 00:11:03,243 - INFO - 客户端 18 开始异步训练循环
2025-08-01 00:11:03,244 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:03,245 - INFO - [客户端类型: Client, ID: 18] 准备开始训练
2025-08-01 00:11:03,245 - INFO - [客户端 18] 使用的训练器 client_id: 18
2025-08-01 00:11:03,247 - INFO - [Client 18] 开始验证训练集
2025-08-01 00:11:03,249 - INFO - [Client 18] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:03,249 - INFO - [Client 18] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:03,250 - INFO - [Trainer 18] 开始训练
2025-08-01 00:11:03,250 - INFO - [Trainer 18] 训练集大小: 300
2025-08-01 00:11:03,252 - INFO - [Trainer 18] 模型已移至设备: cpu
2025-08-01 00:11:03,252 - INFO - [Trainer 18] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:03,253 - INFO - [Trainer 18] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:03,255 - INFO - [Trainer 18] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:03,256 - INFO - [Trainer 18] 开始第 1/5 个epoch
2025-08-01 00:11:03,320 - INFO - [Trainer 18] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3532, y=[1, 8, 1, 2, 1]
2025-08-01 00:11:03,320 - INFO - [Trainer 18] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:03,329 - INFO - [Trainer 7] Batch 0, Loss: 1.8415
2025-08-01 00:11:03,358 - INFO - [Trainer 18] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:03,377 - INFO - [Trainer 18] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2209
2025-08-01 00:11:03,380 - INFO - [Trainer 18] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:03,381 - INFO - [Trainer 18] 标签样本: [8, 9, 4, 1, 1]
2025-08-01 00:11:03,479 - INFO - 客户端 2 开始异步训练循环
2025-08-01 00:11:03,480 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:03,481 - INFO - [客户端类型: Client, ID: 2] 准备开始训练
2025-08-01 00:11:03,491 - INFO - [客户端 2] 使用的训练器 client_id: 2
2025-08-01 00:11:03,491 - INFO - [Client 2] 开始验证训练集
2025-08-01 00:11:03,501 - INFO - [Client 2] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:03,501 - INFO - [Client 2] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:03,502 - INFO - [Trainer 2] 开始训练
2025-08-01 00:11:03,502 - INFO - [Trainer 2] 训练集大小: 300
2025-08-01 00:11:03,503 - INFO - [Trainer 2] 模型已移至设备: cpu
2025-08-01 00:11:03,504 - INFO - [Trainer 2] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:03,523 - INFO - [Trainer 2] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:03,526 - INFO - [Trainer 2] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:03,533 - INFO - [Trainer 2] 开始第 1/5 个epoch
2025-08-01 00:11:03,697 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0638, y=[5, 8, 8, 8, 8]
2025-08-01 00:11:03,698 - INFO - [Trainer 2] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:03,749 - INFO - [Client 26] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:03,753 - INFO - [Algorithm] 设置客户端ID: 26
2025-08-01 00:11:03,755 - INFO - [Algorithm] 同步更新trainer的client_id: 26
2025-08-01 00:11:03,756 - INFO - [Client 26] 已更新algorithm的client_id
2025-08-01 00:11:03,766 - INFO - [Client 26] 模型初始化完成
2025-08-01 00:11:03,768 - INFO - 客户端 26 模型初始化成功
2025-08-01 00:11:03,782 - INFO - 客户端 26 异步训练线程已启动
2025-08-01 00:11:03,791 - INFO - [Client 27] 模型已放置到设备: cpu
2025-08-01 00:11:03,849 - INFO - [Trainer 18] Batch 0, Loss: 2.4899
2025-08-01 00:11:03,898 - INFO - [Trainer 2] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:03,918 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0807
2025-08-01 00:11:03,920 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:03,921 - INFO - [Client 27] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:03,922 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 00:11:03,970 - INFO - [Client 27] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:03,973 - INFO - [Algorithm] 设置客户端ID: 27
2025-08-01 00:11:03,975 - INFO - [Algorithm] 同步更新trainer的client_id: 27
2025-08-01 00:11:03,975 - INFO - [Client 27] 已更新algorithm的client_id
2025-08-01 00:11:03,976 - INFO - [Client 27] 模型初始化完成
2025-08-01 00:11:03,976 - INFO - 客户端 27 模型初始化成功
2025-08-01 00:11:03,986 - INFO - 客户端 27 异步训练线程已启动
2025-08-01 00:11:03,994 - INFO - [Client 28] 模型已放置到设备: cpu
2025-08-01 00:11:04,068 - INFO - 客户端 21 开始异步训练循环
2025-08-01 00:11:04,070 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:04,077 - INFO - [客户端类型: Client, ID: 21] 准备开始训练
2025-08-01 00:11:04,078 - INFO - [客户端 21] 使用的训练器 client_id: 21
2025-08-01 00:11:04,081 - INFO - [Client 21] 开始验证训练集
2025-08-01 00:11:04,083 - INFO - [Client 28] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:04,090 - INFO - [Trainer 2] Batch 0, Loss: 2.5641
2025-08-01 00:11:04,091 - INFO - [Client 21] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:04,092 - INFO - [Client 21] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:04,092 - INFO - [Trainer 21] 开始训练
2025-08-01 00:11:04,093 - INFO - [Trainer 21] 训练集大小: 300
2025-08-01 00:11:04,122 - INFO - [Trainer 21] 模型已移至设备: cpu
2025-08-01 00:11:04,125 - INFO - [Trainer 21] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:04,129 - INFO - [Trainer 21] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:04,141 - INFO - [Trainer 21] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:04,147 - INFO - [Trainer 21] 开始第 1/5 个epoch
2025-08-01 00:11:04,210 - INFO - [Client 28] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:04,213 - INFO - [Algorithm] 设置客户端ID: 28
2025-08-01 00:11:04,215 - INFO - [Algorithm] 同步更新trainer的client_id: 28
2025-08-01 00:11:04,215 - INFO - [Client 28] 已更新algorithm的client_id
2025-08-01 00:11:04,218 - INFO - [Client 28] 模型初始化完成
2025-08-01 00:11:04,218 - INFO - 客户端 28 模型初始化成功
2025-08-01 00:11:04,220 - INFO - 客户端 28 异步训练线程已启动
2025-08-01 00:11:04,240 - INFO - [Client 29] 模型已放置到设备: cpu
2025-08-01 00:11:04,317 - INFO - [Client 29] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:04,380 - INFO - [Client 29] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:04,382 - INFO - [Algorithm] 设置客户端ID: 29
2025-08-01 00:11:04,384 - INFO - [Algorithm] 同步更新trainer的client_id: 29
2025-08-01 00:11:04,384 - INFO - [Client 29] 已更新algorithm的client_id
2025-08-01 00:11:04,385 - INFO - [Client 29] 模型初始化完成
2025-08-01 00:11:04,385 - INFO - 客户端 29 模型初始化成功
2025-08-01 00:11:04,388 - INFO - 客户端 29 异步训练线程已启动
2025-08-01 00:11:04,400 - INFO - [Client 30] 模型已放置到设备: cpu
2025-08-01 00:11:04,403 - INFO - [Trainer 21] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0806, y=[7, 8, 8, 7, 7]
2025-08-01 00:11:04,407 - INFO - [Trainer 21] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:04,504 - INFO - [Client 30] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:04,612 - INFO - [Client 30] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:04,620 - INFO - [Algorithm] 设置客户端ID: 30
2025-08-01 00:11:04,621 - INFO - [Algorithm] 同步更新trainer的client_id: 30
2025-08-01 00:11:04,624 - INFO - [Client 30] 已更新algorithm的client_id
2025-08-01 00:11:04,625 - INFO - [Client 30] 模型初始化完成
2025-08-01 00:11:04,631 - INFO - 客户端 30 模型初始化成功
2025-08-01 00:11:04,642 - INFO - 客户端 30 异步训练线程已启动
2025-08-01 00:11:04,664 - INFO - [Client 31] 模型已放置到设备: cpu
2025-08-01 00:11:04,739 - INFO - 客户端 28 开始异步训练循环
2025-08-01 00:11:04,745 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:04,749 - INFO - [Trainer 21] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:04,760 - INFO - [客户端类型: Client, ID: 28] 准备开始训练
2025-08-01 00:11:04,768 - INFO - [客户端 28] 使用的训练器 client_id: 28
2025-08-01 00:11:04,769 - INFO - [Client 28] 开始验证训练集
2025-08-01 00:11:04,774 - INFO - [Trainer 21] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2819
2025-08-01 00:11:04,777 - INFO - [Trainer 21] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:04,780 - INFO - [Client 28] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:04,785 - INFO - [Client 31] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:04,785 - INFO - [Trainer 21] 标签样本: [8, 7, 8, 8, 7]
2025-08-01 00:11:04,789 - INFO - [Client 28] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:04,794 - INFO - [Trainer 28] 开始训练
2025-08-01 00:11:04,800 - INFO - [Trainer 28] 训练集大小: 300
2025-08-01 00:11:04,810 - INFO - [Trainer 28] 模型已移至设备: cpu
2025-08-01 00:11:04,815 - INFO - [Trainer 28] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:04,816 - INFO - [Trainer 28] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:04,819 - INFO - [Trainer 28] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:04,821 - INFO - [Trainer 28] 开始第 1/5 个epoch
2025-08-01 00:11:04,865 - INFO - [Client 31] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:04,867 - INFO - [Algorithm] 设置客户端ID: 31
2025-08-01 00:11:04,868 - INFO - [Algorithm] 同步更新trainer的client_id: 31
2025-08-01 00:11:04,869 - INFO - [Client 31] 已更新algorithm的client_id
2025-08-01 00:11:04,871 - INFO - [Client 31] 模型初始化完成
2025-08-01 00:11:04,873 - INFO - 客户端 31 模型初始化成功
2025-08-01 00:11:04,882 - INFO - 客户端 31 异步训练线程已启动
2025-08-01 00:11:04,892 - INFO - [Client 32] 模型已放置到设备: cpu
2025-08-01 00:11:04,941 - INFO - 客户端 31 开始异步训练循环
2025-08-01 00:11:04,955 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:04,955 - INFO - [Trainer 28] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3526, y=[0, 7, 7, 4, 7]
2025-08-01 00:11:04,959 - INFO - [客户端类型: Client, ID: 31] 准备开始训练
2025-08-01 00:11:04,959 - INFO - [Trainer 28] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:04,960 - INFO - [客户端 31] 使用的训练器 client_id: 31
2025-08-01 00:11:04,961 - INFO - [Client 31] 开始验证训练集
2025-08-01 00:11:04,965 - INFO - [Client 31] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:04,965 - INFO - [Client 31] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:04,966 - INFO - [Trainer 31] 开始训练
2025-08-01 00:11:04,966 - INFO - [Trainer 31] 训练集大小: 300
2025-08-01 00:11:04,972 - INFO - [Trainer 31] 模型已移至设备: cpu
2025-08-01 00:11:04,973 - INFO - [Trainer 31] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:04,974 - INFO - [Trainer 31] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:04,978 - INFO - [Client 32] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:04,985 - INFO - [Trainer 31] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:04,987 - INFO - [Trainer 31] 开始第 1/5 个epoch
2025-08-01 00:11:05,052 - INFO - [Trainer 31] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4252, y=[1, 1, 1, 1, 5]
2025-08-01 00:11:05,053 - INFO - [Trainer 31] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:05,066 - INFO - [Trainer 21] Batch 0, Loss: 2.6771
2025-08-01 00:11:05,082 - INFO - [Client 32] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:05,083 - INFO - [Algorithm] 设置客户端ID: 32
2025-08-01 00:11:05,086 - INFO - [Algorithm] 同步更新trainer的client_id: 32
2025-08-01 00:11:05,087 - INFO - [Client 32] 已更新algorithm的client_id
2025-08-01 00:11:05,088 - INFO - [Client 32] 模型初始化完成
2025-08-01 00:11:05,091 - INFO - 客户端 32 模型初始化成功
2025-08-01 00:11:05,113 - INFO - 客户端 32 异步训练线程已启动
2025-08-01 00:11:05,132 - INFO - [Client 33] 模型已放置到设备: cpu
2025-08-01 00:11:05,164 - INFO - [Trainer 28] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:05,187 - INFO - [Trainer 28] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2735
2025-08-01 00:11:05,193 - INFO - [Trainer 28] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:05,194 - INFO - [Trainer 28] 标签样本: [4, 7, 4, 7, 7]
2025-08-01 00:11:05,271 - INFO - [Trainer 31] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:05,283 - INFO - [Client 33] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:05,288 - INFO - [Trainer 31] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4322
2025-08-01 00:11:05,291 - INFO - [Trainer 31] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:05,297 - INFO - [Trainer 31] 标签样本: [1, 1, 1, 1, 1]
2025-08-01 00:11:05,403 - INFO - [Client 33] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:05,406 - INFO - [Algorithm] 设置客户端ID: 33
2025-08-01 00:11:05,406 - INFO - [Algorithm] 同步更新trainer的client_id: 33
2025-08-01 00:11:05,407 - INFO - [Client 33] 已更新algorithm的client_id
2025-08-01 00:11:05,408 - INFO - [Client 33] 模型初始化完成
2025-08-01 00:11:05,409 - INFO - 客户端 33 模型初始化成功
2025-08-01 00:11:05,412 - INFO - [Trainer 31] Batch 0, Loss: 2.2356
2025-08-01 00:11:05,412 - INFO - [Trainer 28] Batch 0, Loss: 2.2888
2025-08-01 00:11:05,412 - INFO - 客户端 33 异步训练线程已启动
2025-08-01 00:11:05,432 - INFO - [Client 34] 模型已放置到设备: cpu
2025-08-01 00:11:05,453 - INFO - 客户端 8 开始异步训练循环
2025-08-01 00:11:05,455 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:05,471 - INFO - [客户端类型: Client, ID: 8] 准备开始训练
2025-08-01 00:11:05,472 - INFO - [客户端 8] 使用的训练器 client_id: 8
2025-08-01 00:11:05,475 - INFO - [Client 8] 开始验证训练集
2025-08-01 00:11:05,511 - INFO - [Client 8] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:05,511 - INFO - [Client 8] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:05,511 - INFO - [Trainer 8] 开始训练
2025-08-01 00:11:05,513 - INFO - [Trainer 8] 训练集大小: 300
2025-08-01 00:11:06,722 - INFO - 客户端 23 开始异步训练循环
2025-08-01 00:11:06,722 - INFO - 客户端 22 开始异步训练循环
2025-08-01 00:11:06,722 - INFO - 客户端 17 开始异步训练循环
2025-08-01 00:11:06,723 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:06,723 - INFO - 客户端 10 开始异步训练循环
2025-08-01 00:11:06,723 - INFO - [Trainer 8] 模型已移至设备: cpu
2025-08-01 00:11:06,731 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:06,732 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:06,735 - INFO - [客户端类型: Client, ID: 17] 准备开始训练
2025-08-01 00:11:06,737 - INFO - [客户端类型: Client, ID: 23] 准备开始训练
2025-08-01 00:11:06,738 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:06,741 - INFO - [Trainer 8] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:06,745 - INFO - [客户端类型: Client, ID: 22] 准备开始训练
2025-08-01 00:11:06,746 - INFO - [客户端类型: Client, ID: 10] 准备开始训练
2025-08-01 00:11:06,748 - INFO - [客户端 17] 使用的训练器 client_id: 17
2025-08-01 00:11:06,750 - INFO - [客户端 23] 使用的训练器 client_id: 23
2025-08-01 00:11:06,754 - INFO - [Trainer 8] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:06,761 - INFO - [客户端 22] 使用的训练器 client_id: 22
2025-08-01 00:11:06,766 - INFO - [客户端 10] 使用的训练器 client_id: 10
2025-08-01 00:11:06,767 - INFO - [Client 17] 开始验证训练集
2025-08-01 00:11:06,770 - INFO - [Client 23] 开始验证训练集
2025-08-01 00:11:06,771 - INFO - 客户端 29 开始异步训练循环
2025-08-01 00:11:06,779 - INFO - [Client 22] 开始验证训练集
2025-08-01 00:11:06,782 - INFO - [Client 10] 开始验证训练集
2025-08-01 00:11:06,788 - INFO - 客户端 33 开始异步训练循环
2025-08-01 00:11:06,790 - INFO - [Trainer 8] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:06,803 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:06,808 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:06,811 - INFO - [Trainer 8] 开始第 1/5 个epoch
2025-08-01 00:11:06,812 - INFO - [Client 22] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:06,816 - INFO - [Client 10] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:06,819 - INFO - [客户端类型: Client, ID: 33] 准备开始训练
2025-08-01 00:11:06,817 - INFO - [客户端类型: Client, ID: 29] 准备开始训练
2025-08-01 00:11:06,820 - INFO - [Client 23] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:06,824 - INFO - [Client 17] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:06,827 - INFO - [Client 22] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:06,827 - INFO - [Client 10] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:06,834 - INFO - [客户端 33] 使用的训练器 client_id: 33
2025-08-01 00:11:06,836 - INFO - [客户端 29] 使用的训练器 client_id: 29
2025-08-01 00:11:06,841 - INFO - [Client 23] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:06,846 - INFO - [Client 17] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:06,850 - INFO - [Client 34] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:06,850 - INFO - [Trainer 22] 开始训练
2025-08-01 00:11:06,853 - INFO - [Trainer 10] 开始训练
2025-08-01 00:11:06,855 - INFO - [Client 33] 开始验证训练集
2025-08-01 00:11:06,855 - INFO - [Client 29] 开始验证训练集
2025-08-01 00:11:06,856 - INFO - [Trainer 23] 开始训练
2025-08-01 00:11:06,857 - INFO - [Trainer 17] 开始训练
2025-08-01 00:11:06,858 - INFO - [Trainer 22] 训练集大小: 300
2025-08-01 00:11:06,858 - INFO - [Trainer 10] 训练集大小: 300
2025-08-01 00:11:06,861 - INFO - [Trainer 23] 训练集大小: 300
2025-08-01 00:11:06,863 - INFO - [Trainer 17] 训练集大小: 300
2025-08-01 00:11:06,877 - INFO - [Client 33] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:06,880 - INFO - [Client 33] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:06,881 - INFO - [Trainer 33] 开始训练
2025-08-01 00:11:06,881 - INFO - [Client 29] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:06,881 - INFO - [Trainer 33] 训练集大小: 300
2025-08-01 00:11:06,882 - INFO - [Client 29] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:06,884 - INFO - [Trainer 29] 开始训练
2025-08-01 00:11:06,884 - INFO - [Trainer 29] 训练集大小: 300
2025-08-01 00:11:06,907 - INFO - [Trainer 10] 模型已移至设备: cpu
2025-08-01 00:11:06,910 - INFO - [Trainer 10] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:06,910 - INFO - [Trainer 17] 模型已移至设备: cpu
2025-08-01 00:11:06,910 - INFO - [Trainer 23] 模型已移至设备: cpu
2025-08-01 00:11:06,911 - INFO - [Trainer 10] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:06,913 - INFO - [Trainer 17] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:06,914 - INFO - [Trainer 23] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:06,916 - INFO - [Trainer 17] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:06,916 - INFO - [Trainer 23] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:06,916 - INFO - [Trainer 22] 模型已移至设备: cpu
2025-08-01 00:11:06,922 - INFO - [Trainer 29] 模型已移至设备: cpu
2025-08-01 00:11:06,922 - INFO - [Trainer 33] 模型已移至设备: cpu
2025-08-01 00:11:06,925 - INFO - [Trainer 22] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:06,926 - INFO - [Trainer 29] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:06,929 - INFO - [Trainer 33] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:06,932 - INFO - [Trainer 22] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:06,934 - INFO - [Trainer 10] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:06,935 - INFO - [Trainer 29] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:06,936 - INFO - [Trainer 33] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:06,938 - INFO - [Trainer 22] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:06,940 - INFO - [Trainer 10] 开始第 1/5 个epoch
2025-08-01 00:11:06,942 - INFO - [Trainer 17] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:06,943 - INFO - [Trainer 22] 开始第 1/5 个epoch
2025-08-01 00:11:06,945 - INFO - [Trainer 17] 开始第 1/5 个epoch
2025-08-01 00:11:06,949 - INFO - [Trainer 23] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:06,952 - INFO - [Trainer 23] 开始第 1/5 个epoch
2025-08-01 00:11:06,954 - INFO - [Trainer 33] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:06,959 - INFO - [Trainer 29] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:06,960 - INFO - [Trainer 33] 开始第 1/5 个epoch
2025-08-01 00:11:06,962 - INFO - [Trainer 29] 开始第 1/5 个epoch
2025-08-01 00:11:07,029 - INFO - [Client 34] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:07,029 - INFO - [Algorithm] 设置客户端ID: 34
2025-08-01 00:11:07,030 - INFO - [Algorithm] 同步更新trainer的client_id: 34
2025-08-01 00:11:07,031 - INFO - [Client 34] 已更新algorithm的client_id
2025-08-01 00:11:07,032 - INFO - [Client 34] 模型初始化完成
2025-08-01 00:11:07,032 - INFO - 客户端 34 模型初始化成功
2025-08-01 00:11:07,038 - INFO - 客户端 34 异步训练线程已启动
2025-08-01 00:11:07,072 - INFO - [Client 35] 模型已放置到设备: cpu
2025-08-01 00:11:07,203 - INFO - [Client 35] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:07,338 - INFO - [Client 35] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:07,339 - INFO - [Algorithm] 设置客户端ID: 35
2025-08-01 00:11:07,340 - INFO - [Algorithm] 同步更新trainer的client_id: 35
2025-08-01 00:11:07,340 - INFO - [Client 35] 已更新algorithm的client_id
2025-08-01 00:11:07,340 - INFO - [Client 35] 模型初始化完成
2025-08-01 00:11:07,342 - INFO - 客户端 35 模型初始化成功
2025-08-01 00:11:07,344 - INFO - 客户端 35 异步训练线程已启动
2025-08-01 00:11:07,367 - INFO - [Client 36] 模型已放置到设备: cpu
2025-08-01 00:11:07,381 - INFO - [Trainer 8] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3672, y=[6, 1, 6, 0, 0]
2025-08-01 00:11:07,381 - INFO - [Trainer 8] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:07,395 - INFO - 客户端 26 开始异步训练循环
2025-08-01 00:11:07,396 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:07,407 - INFO - [客户端类型: Client, ID: 26] 准备开始训练
2025-08-01 00:11:07,408 - INFO - [客户端 26] 使用的训练器 client_id: 26
2025-08-01 00:11:07,414 - INFO - [Client 26] 开始验证训练集
2025-08-01 00:11:07,430 - INFO - [Client 26] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:07,432 - INFO - [Client 26] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:07,433 - INFO - [Trainer 26] 开始训练
2025-08-01 00:11:07,435 - INFO - [Trainer 26] 训练集大小: 300
2025-08-01 00:11:07,448 - INFO - [Trainer 29] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0869, y=[9, 0, 4, 4, 0]
2025-08-01 00:11:07,450 - INFO - [Trainer 29] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:07,462 - INFO - [Trainer 10] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2423, y=[4, 8, 8, 8, 7]
2025-08-01 00:11:07,464 - INFO - [Trainer 10] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:07,487 - INFO - [Trainer 26] 模型已移至设备: cpu
2025-08-01 00:11:07,492 - INFO - [Trainer 26] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:07,494 - INFO - [Trainer 26] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:07,504 - INFO - [Trainer 26] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:07,505 - INFO - [Trainer 26] 开始第 1/5 个epoch
2025-08-01 00:11:07,510 - INFO - [Trainer 22] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4690, y=[6, 6, 6, 8, 8]
2025-08-01 00:11:07,512 - INFO - [Trainer 22] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:07,524 - INFO - [Client 36] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:07,528 - INFO - [Trainer 23] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4365, y=[1, 9, 1, 6, 9]
2025-08-01 00:11:07,531 - INFO - [Trainer 23] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:07,585 - INFO - [Trainer 33] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4929, y=[4, 4, 4, 4, 4]
2025-08-01 00:11:07,586 - INFO - [Trainer 33] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:07,589 - INFO - [Trainer 17] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.6150, y=[5, 6, 5, 5, 2]
2025-08-01 00:11:07,590 - INFO - [Trainer 17] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:07,660 - INFO - 客户端 24 开始异步训练循环
2025-08-01 00:11:07,662 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:07,668 - INFO - [客户端类型: Client, ID: 24] 准备开始训练
2025-08-01 00:11:07,672 - INFO - [客户端 24] 使用的训练器 client_id: 24
2025-08-01 00:11:07,674 - INFO - [Client 24] 开始验证训练集
2025-08-01 00:11:07,709 - INFO - [Client 24] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:07,711 - INFO - [Client 24] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:07,712 - INFO - [Trainer 24] 开始训练
2025-08-01 00:11:07,714 - INFO - [Client 36] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:07,714 - INFO - [Trainer 24] 训练集大小: 300
2025-08-01 00:11:07,715 - INFO - [Algorithm] 设置客户端ID: 36
2025-08-01 00:11:07,721 - INFO - [Algorithm] 同步更新trainer的client_id: 36
2025-08-01 00:11:07,723 - INFO - [Client 36] 已更新algorithm的client_id
2025-08-01 00:11:07,724 - INFO - [Client 36] 模型初始化完成
2025-08-01 00:11:07,728 - INFO - 客户端 36 模型初始化成功
2025-08-01 00:11:07,740 - INFO - 客户端 36 异步训练线程已启动
2025-08-01 00:11:07,746 - INFO - [Trainer 24] 模型已移至设备: cpu
2025-08-01 00:11:07,747 - INFO - [Trainer 24] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:07,750 - INFO - [Trainer 24] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:07,770 - INFO - [Trainer 24] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:07,778 - INFO - [Trainer 24] 开始第 1/5 个epoch
2025-08-01 00:11:07,809 - INFO - 客户端 19 开始异步训练循环
2025-08-01 00:11:07,813 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:07,819 - INFO - [Client 37] 模型已放置到设备: cpu
2025-08-01 00:11:07,823 - INFO - [客户端类型: Client, ID: 19] 准备开始训练
2025-08-01 00:11:07,830 - INFO - [客户端 19] 使用的训练器 client_id: 19
2025-08-01 00:11:07,832 - INFO - [Client 19] 开始验证训练集
2025-08-01 00:11:07,878 - INFO - [Client 19] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:07,884 - INFO - [Client 19] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:07,889 - INFO - [Trainer 19] 开始训练
2025-08-01 00:11:07,890 - INFO - [Trainer 19] 训练集大小: 300
2025-08-01 00:11:07,936 - INFO - [Trainer 19] 模型已移至设备: cpu
2025-08-01 00:11:07,944 - INFO - [Trainer 19] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:07,950 - INFO - [Trainer 19] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:07,989 - INFO - [Trainer 19] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:07,990 - INFO - [Trainer 19] 开始第 1/5 个epoch
2025-08-01 00:11:08,042 - INFO - [Trainer 29] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:08,056 - INFO - [Trainer 29] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1597
2025-08-01 00:11:08,060 - INFO - [Trainer 29] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:08,065 - INFO - [Trainer 29] 标签样本: [4, 9, 0, 9, 0]
2025-08-01 00:11:08,076 - INFO - [Client 37] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:08,121 - INFO - [Trainer 26] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4114, y=[7, 7, 7, 7, 7]
2025-08-01 00:11:08,126 - INFO - [Trainer 23] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:08,126 - INFO - [Trainer 26] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:08,132 - INFO - [Trainer 10] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:08,147 - INFO - [Trainer 23] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4431
2025-08-01 00:11:08,149 - INFO - [Trainer 23] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:08,151 - INFO - [Trainer 23] 标签样本: [6, 6, 6, 9, 6]
2025-08-01 00:11:08,154 - INFO - [Trainer 10] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2883
2025-08-01 00:11:08,157 - INFO - [Trainer 10] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:08,159 - INFO - [Trainer 10] 标签样本: [6, 8, 8, 8, 8]
2025-08-01 00:11:08,218 - INFO - [Trainer 29] Batch 0, Loss: 2.1396
2025-08-01 00:11:08,277 - INFO - [Trainer 23] Batch 0, Loss: 2.4289
2025-08-01 00:11:08,279 - INFO - [Trainer 8] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:08,285 - INFO - [Trainer 10] Batch 0, Loss: 2.3526
2025-08-01 00:11:08,309 - INFO - [Trainer 8] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1031
2025-08-01 00:11:08,312 - INFO - [Trainer 8] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:08,313 - INFO - [Trainer 8] 标签样本: [6, 1, 6, 0, 0]
2025-08-01 00:11:08,350 - INFO - [Trainer 22] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:08,358 - INFO - [Client 37] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:08,359 - INFO - [Algorithm] 设置客户端ID: 37
2025-08-01 00:11:08,361 - INFO - [Algorithm] 同步更新trainer的client_id: 37
2025-08-01 00:11:08,369 - INFO - [Client 37] 已更新algorithm的client_id
2025-08-01 00:11:08,379 - INFO - [Client 37] 模型初始化完成
2025-08-01 00:11:08,398 - INFO - 客户端 37 模型初始化成功
2025-08-01 00:11:08,399 - INFO - [Trainer 22] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3775
2025-08-01 00:11:08,404 - INFO - [Trainer 22] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:08,410 - INFO - 客户端 37 异步训练线程已启动
2025-08-01 00:11:08,413 - INFO - [Trainer 22] 标签样本: [6, 6, 6, 6, 6]
2025-08-01 00:11:08,424 - INFO - [Trainer 33] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:08,445 - INFO - 客户端 27 开始异步训练循环
2025-08-01 00:11:08,447 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:08,457 - INFO - [Client 38] 模型已放置到设备: cpu
2025-08-01 00:11:08,474 - INFO - [Trainer 24] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0810, y=[0, 0, 0, 8, 9]
2025-08-01 00:11:08,478 - INFO - [Trainer 33] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4551
2025-08-01 00:11:08,479 - INFO - [Trainer 24] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:08,480 - INFO - [客户端类型: Client, ID: 27] 准备开始训练
2025-08-01 00:11:08,484 - INFO - [Trainer 33] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:08,490 - INFO - [客户端 27] 使用的训练器 client_id: 27
2025-08-01 00:11:08,501 - INFO - [Trainer 33] 标签样本: [7, 4, 4, 7, 4]
2025-08-01 00:11:08,503 - INFO - [Client 27] 开始验证训练集
2025-08-01 00:11:08,549 - INFO - [Client 27] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:08,553 - INFO - [Client 27] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:08,560 - INFO - [Trainer 27] 开始训练
2025-08-01 00:11:08,561 - INFO - [Trainer 27] 训练集大小: 300
2025-08-01 00:11:08,563 - INFO - [Trainer 8] Batch 0, Loss: 2.5252
2025-08-01 00:11:08,573 - INFO - [Trainer 17] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:08,594 - INFO - [Trainer 17] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3699
2025-08-01 00:11:08,597 - INFO - [Trainer 17] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:08,598 - INFO - [Trainer 17] 标签样本: [5, 6, 6, 5, 6]
2025-08-01 00:11:08,611 - INFO - [Trainer 22] Batch 0, Loss: 2.5765
2025-08-01 00:11:08,616 - INFO - [Trainer 27] 模型已移至设备: cpu
2025-08-01 00:11:08,623 - INFO - [Trainer 27] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:08,629 - INFO - [Trainer 27] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:08,660 - INFO - [Client 38] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:08,667 - INFO - 客户端 37 开始异步训练循环
2025-08-01 00:11:08,671 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:08,672 - INFO - [客户端类型: Client, ID: 37] 准备开始训练
2025-08-01 00:11:08,682 - INFO - [客户端 37] 使用的训练器 client_id: 37
2025-08-01 00:11:08,688 - INFO - [Trainer 27] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:08,690 - INFO - [Client 37] 开始验证训练集
2025-08-01 00:11:08,697 - INFO - [Trainer 27] 开始第 1/5 个epoch
2025-08-01 00:11:08,712 - INFO - [Trainer 33] Batch 0, Loss: 2.3202
2025-08-01 00:11:08,728 - INFO - 客户端 9 开始异步训练循环
2025-08-01 00:11:08,749 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:08,754 - INFO - [Client 37] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:08,760 - INFO - [Client 37] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:08,765 - INFO - [客户端类型: Client, ID: 9] 准备开始训练
2025-08-01 00:11:08,769 - INFO - [Trainer 37] 开始训练
2025-08-01 00:11:08,784 - INFO - [客户端 9] 使用的训练器 client_id: 9
2025-08-01 00:11:08,790 - INFO - [Trainer 37] 训练集大小: 300
2025-08-01 00:11:08,803 - INFO - [Client 9] 开始验证训练集
2025-08-01 00:11:08,855 - INFO - [Client 9] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:08,867 - INFO - [Client 9] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:08,877 - INFO - [Trainer 9] 开始训练
2025-08-01 00:11:08,883 - INFO - [Trainer 9] 训练集大小: 300
2025-08-01 00:11:08,894 - INFO - [Trainer 26] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:08,914 - INFO - [Trainer 37] 模型已移至设备: cpu
2025-08-01 00:11:08,919 - INFO - [Trainer 37] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:08,923 - INFO - [Trainer 37] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:08,926 - INFO - [Trainer 26] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3205
2025-08-01 00:11:08,930 - INFO - [Trainer 9] 模型已移至设备: cpu
2025-08-01 00:11:09,013 - INFO - [Client 38] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:09,014 - INFO - [Trainer 26] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:09,016 - INFO - [Trainer 9] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:09,017 - INFO - [Algorithm] 设置客户端ID: 38
2025-08-01 00:11:09,021 - INFO - [Trainer 26] 标签样本: [3, 7, 7, 7, 7]
2025-08-01 00:11:09,025 - INFO - [Trainer 9] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:09,025 - INFO - [Trainer 37] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:09,046 - INFO - [Algorithm] 同步更新trainer的client_id: 38
2025-08-01 00:11:09,067 - INFO - [Trainer 37] 开始第 1/5 个epoch
2025-08-01 00:11:09,076 - INFO - [Client 38] 已更新algorithm的client_id
2025-08-01 00:11:09,096 - INFO - [Trainer 9] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:09,109 - INFO - [Client 38] 模型初始化完成
2025-08-01 00:11:09,110 - INFO - [Trainer 9] 开始第 1/5 个epoch
2025-08-01 00:11:09,123 - INFO - 客户端 38 模型初始化成功
2025-08-01 00:11:09,144 - INFO - 客户端 38 异步训练线程已启动
2025-08-01 00:11:09,165 - INFO - [Trainer 24] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:09,177 - INFO - [Client 39] 模型已放置到设备: cpu
2025-08-01 00:11:09,184 - INFO - [Trainer 24] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1091
2025-08-01 00:11:09,186 - INFO - [Trainer 24] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:09,188 - INFO - [Trainer 24] 标签样本: [0, 0, 0, 0, 0]
2025-08-01 00:11:09,361 - INFO - [Trainer 3] Batch 5, Loss: 2.6771
2025-08-01 00:11:09,396 - INFO - 客户端 35 开始异步训练循环
2025-08-01 00:11:09,397 - INFO - [Trainer 17] Batch 0, Loss: 2.4789
2025-08-01 00:11:09,399 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:09,403 - INFO - [客户端类型: Client, ID: 35] 准备开始训练
2025-08-01 00:11:09,412 - INFO - [客户端 35] 使用的训练器 client_id: 35
2025-08-01 00:11:09,413 - INFO - [Client 35] 开始验证训练集
2025-08-01 00:11:09,420 - INFO - [Client 35] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:09,421 - INFO - [Client 35] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:09,423 - INFO - [Trainer 35] 开始训练
2025-08-01 00:11:09,425 - INFO - [Trainer 35] 训练集大小: 300
2025-08-01 00:11:09,449 - INFO - [Trainer 35] 模型已移至设备: cpu
2025-08-01 00:11:09,454 - INFO - [Trainer 35] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:09,458 - INFO - [Trainer 35] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:09,468 - INFO - [Trainer 19] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5251, y=[4, 4, 4, 4, 4]
2025-08-01 00:11:09,476 - INFO - [Trainer 35] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:09,482 - INFO - [Trainer 19] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:09,512 - INFO - [Trainer 35] 开始第 1/5 个epoch
2025-08-01 00:11:09,642 - INFO - [Trainer 26] Batch 0, Loss: 2.0690
2025-08-01 00:11:09,670 - INFO - [Client 39] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:09,710 - INFO - [Trainer 27] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5173, y=[5, 4, 5, 4, 1]
2025-08-01 00:11:09,717 - INFO - [Trainer 27] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:09,769 - INFO - [Trainer 24] Batch 0, Loss: 2.3711
2025-08-01 00:11:09,870 - INFO - 客户端 20 开始异步训练循环
2025-08-01 00:11:09,872 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:09,893 - INFO - [客户端类型: Client, ID: 20] 准备开始训练
2025-08-01 00:11:09,898 - INFO - [客户端 20] 使用的训练器 client_id: 20
2025-08-01 00:11:09,903 - INFO - [Client 20] 开始验证训练集
2025-08-01 00:11:09,931 - INFO - [Client 39] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:09,936 - INFO - [Algorithm] 设置客户端ID: 39
2025-08-01 00:11:09,937 - INFO - [Algorithm] 同步更新trainer的client_id: 39
2025-08-01 00:11:09,939 - INFO - [Client 39] 已更新algorithm的client_id
2025-08-01 00:11:09,945 - INFO - [Client 39] 模型初始化完成
2025-08-01 00:11:09,946 - INFO - 客户端 39 模型初始化成功
2025-08-01 00:11:09,951 - INFO - [Client 20] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:09,953 - INFO - 客户端 39 异步训练线程已启动
2025-08-01 00:11:09,956 - INFO - [Client 20] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:09,968 - INFO - [Trainer 20] 开始训练
2025-08-01 00:11:09,970 - INFO - [Trainer 20] 训练集大小: 300
2025-08-01 00:11:09,989 - INFO - 客户端 12 开始异步训练循环
2025-08-01 00:11:09,994 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:10,010 - INFO - [客户端类型: Client, ID: 12] 准备开始训练
2025-08-01 00:11:10,012 - INFO - [客户端 12] 使用的训练器 client_id: 12
2025-08-01 00:11:10,014 - INFO - [Client 12] 开始验证训练集
2025-08-01 00:11:10,042 - INFO - [Client 12] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:10,044 - INFO - [Client 12] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:10,049 - INFO - [Trainer 12] 开始训练
2025-08-01 00:11:10,050 - INFO - [Trainer 12] 训练集大小: 300
2025-08-01 00:11:10,058 - INFO - [Trainer 20] 模型已移至设备: cpu
2025-08-01 00:11:10,059 - INFO - [Trainer 20] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:10,060 - INFO - [Trainer 20] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:10,060 - INFO - [Client 40] 模型已放置到设备: cpu
2025-08-01 00:11:10,068 - INFO - 客户端 4 开始异步训练循环
2025-08-01 00:11:10,072 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:10,084 - INFO - 客户端 13 开始异步训练循环
2025-08-01 00:11:10,085 - INFO - [Trainer 20] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:10,087 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:10,090 - INFO - [Trainer 20] 开始第 1/5 个epoch
2025-08-01 00:11:10,091 - INFO - [客户端类型: Client, ID: 4] 准备开始训练
2025-08-01 00:11:10,099 - INFO - [客户端 4] 使用的训练器 client_id: 4
2025-08-01 00:11:10,101 - INFO - [Client 4] 开始验证训练集
2025-08-01 00:11:10,112 - INFO - [客户端类型: Client, ID: 13] 准备开始训练
2025-08-01 00:11:10,113 - INFO - [客户端 13] 使用的训练器 client_id: 13
2025-08-01 00:11:10,115 - INFO - 客户端 36 开始异步训练循环
2025-08-01 00:11:10,115 - INFO - [Client 13] 开始验证训练集
2025-08-01 00:11:10,118 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:10,131 - INFO - [Trainer 12] 模型已移至设备: cpu
2025-08-01 00:11:10,132 - INFO - [Trainer 12] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:10,133 - INFO - [Client 4] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:10,133 - INFO - [Trainer 12] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:10,134 - INFO - [Client 4] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:10,135 - INFO - [客户端类型: Client, ID: 36] 准备开始训练
2025-08-01 00:11:10,137 - INFO - [Trainer 4] 开始训练
2025-08-01 00:11:10,139 - INFO - [客户端 36] 使用的训练器 client_id: 36
2025-08-01 00:11:10,140 - INFO - [Trainer 4] 训练集大小: 300
2025-08-01 00:11:10,142 - INFO - [Client 36] 开始验证训练集
2025-08-01 00:11:10,148 - INFO - [Client 13] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:10,151 - INFO - [Client 13] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:10,154 - INFO - [Trainer 13] 开始训练
2025-08-01 00:11:10,156 - INFO - [Trainer 12] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:10,158 - INFO - [Trainer 13] 训练集大小: 300
2025-08-01 00:11:10,160 - INFO - [Trainer 12] 开始第 1/5 个epoch
2025-08-01 00:11:10,166 - INFO - [Client 36] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:10,168 - INFO - [Client 36] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:10,170 - INFO - [Trainer 36] 开始训练
2025-08-01 00:11:10,171 - INFO - [Trainer 36] 训练集大小: 300
2025-08-01 00:11:10,195 - INFO - [Trainer 4] 模型已移至设备: cpu
2025-08-01 00:11:10,197 - INFO - [Trainer 4] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:10,203 - INFO - [Trainer 4] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:10,203 - INFO - [Trainer 37] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=0.0542, y=[0, 0, 8, 0, 5]
2025-08-01 00:11:10,213 - INFO - [Trainer 37] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:10,214 - INFO - [Trainer 36] 模型已移至设备: cpu
2025-08-01 00:11:10,216 - INFO - [Trainer 36] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:10,219 - INFO - [Trainer 36] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:10,229 - INFO - [Trainer 4] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:10,232 - INFO - 客户端 25 开始异步训练循环
2025-08-01 00:11:10,232 - INFO - [Trainer 4] 开始第 1/5 个epoch
2025-08-01 00:11:10,232 - INFO - [Trainer 13] 模型已移至设备: cpu
2025-08-01 00:11:10,236 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:10,238 - INFO - [Trainer 36] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:10,245 - INFO - [Trainer 13] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:10,250 - INFO - [Trainer 36] 开始第 1/5 个epoch
2025-08-01 00:11:10,258 - INFO - [Trainer 13] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:10,273 - INFO - [客户端类型: Client, ID: 25] 准备开始训练
2025-08-01 00:11:10,286 - INFO - [客户端 25] 使用的训练器 client_id: 25
2025-08-01 00:11:10,290 - INFO - [Client 25] 开始验证训练集
2025-08-01 00:11:10,295 - INFO - [Trainer 13] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:10,302 - INFO - [Trainer 13] 开始第 1/5 个epoch
2025-08-01 00:11:10,307 - INFO - [Trainer 9] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4014, y=[8, 7, 6, 6, 8]
2025-08-01 00:11:10,309 - INFO - [Trainer 9] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:10,309 - INFO - [Client 40] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:10,334 - INFO - [Client 25] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:10,348 - INFO - [Client 25] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:10,354 - INFO - [Trainer 25] 开始训练
2025-08-01 00:11:10,356 - INFO - [Trainer 25] 训练集大小: 300
2025-08-01 00:11:10,397 - INFO - [Trainer 25] 模型已移至设备: cpu
2025-08-01 00:11:10,402 - INFO - [Trainer 25] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:10,404 - INFO - [Trainer 25] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:10,428 - INFO - [Trainer 25] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:10,431 - INFO - [Trainer 25] 开始第 1/5 个epoch
2025-08-01 00:11:10,541 - INFO - 客户端 15 开始异步训练循环
2025-08-01 00:11:10,545 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:10,600 - INFO - [客户端类型: Client, ID: 15] 准备开始训练
2025-08-01 00:11:10,614 - INFO - [客户端 15] 使用的训练器 client_id: 15
2025-08-01 00:11:10,621 - INFO - [Client 15] 开始验证训练集
2025-08-01 00:11:10,658 - INFO - [Trainer 35] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.6242, y=[6, 6, 6, 6, 6]
2025-08-01 00:11:10,659 - INFO - [Trainer 35] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:10,679 - INFO - [Trainer 27] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:10,687 - INFO - [Trainer 19] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:10,690 - INFO - [Client 40] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:10,691 - INFO - [Algorithm] 设置客户端ID: 40
2025-08-01 00:11:10,694 - INFO - [Algorithm] 同步更新trainer的client_id: 40
2025-08-01 00:11:10,694 - INFO - [Client 15] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:10,695 - INFO - [Client 40] 已更新algorithm的client_id
2025-08-01 00:11:10,698 - INFO - 客户端 32 开始异步训练循环
2025-08-01 00:11:10,699 - INFO - [Client 15] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:10,703 - INFO - [Client 40] 模型初始化完成
2025-08-01 00:11:10,713 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:10,714 - INFO - [Trainer 19] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.6952, x.mean: -0.5971
2025-08-01 00:11:10,714 - INFO - [Trainer 15] 开始训练
2025-08-01 00:11:10,717 - INFO - 客户端 40 模型初始化成功
2025-08-01 00:11:10,718 - INFO - [Trainer 19] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:10,720 - INFO - [Trainer 15] 训练集大小: 300
2025-08-01 00:11:10,722 - INFO - [Trainer 27] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4456
2025-08-01 00:11:10,725 - INFO - 客户端 40 异步训练线程已启动
2025-08-01 00:11:10,725 - INFO - [Trainer 19] 标签样本: [4, 4, 4, 4, 4]
2025-08-01 00:11:10,731 - INFO - [Trainer 27] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:10,731 - INFO - [客户端类型: Client, ID: 32] 准备开始训练
2025-08-01 00:11:10,746 - INFO - [Trainer 27] 标签样本: [1, 4, 5, 4, 1]
2025-08-01 00:11:10,750 - INFO - [客户端 32] 使用的训练器 client_id: 32
2025-08-01 00:11:10,759 - INFO - [Client 32] 开始验证训练集
2025-08-01 00:11:10,806 - INFO - [Client 32] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:10,814 - INFO - [Client 41] 模型已放置到设备: cpu
2025-08-01 00:11:10,823 - INFO - [Client 32] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:10,846 - INFO - [Trainer 32] 开始训练
2025-08-01 00:11:10,852 - INFO - [Trainer 32] 训练集大小: 300
2025-08-01 00:11:10,856 - INFO - [Trainer 15] 模型已移至设备: cpu
2025-08-01 00:11:10,863 - INFO - [Trainer 15] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:10,878 - INFO - [Trainer 15] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:10,885 - INFO - 客户端 11 开始异步训练循环
2025-08-01 00:11:10,905 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:10,910 - INFO - [Trainer 15] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:10,915 - INFO - [Trainer 15] 开始第 1/5 个epoch
2025-08-01 00:11:10,933 - INFO - [客户端类型: Client, ID: 11] 准备开始训练
2025-08-01 00:11:10,938 - INFO - [客户端 11] 使用的训练器 client_id: 11
2025-08-01 00:11:10,941 - INFO - [Client 11] 开始验证训练集
2025-08-01 00:11:10,952 - INFO - [Trainer 32] 模型已移至设备: cpu
2025-08-01 00:11:10,954 - INFO - [Trainer 32] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:10,960 - INFO - [Trainer 32] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:10,977 - INFO - [Trainer 19] Batch 0, Loss: 2.3091
2025-08-01 00:11:10,983 - INFO - [Client 11] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:10,986 - INFO - [Client 11] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:10,988 - INFO - [Trainer 11] 开始训练
2025-08-01 00:11:10,988 - INFO - [Trainer 27] Batch 0, Loss: 1.9829
2025-08-01 00:11:10,989 - INFO - [Trainer 11] 训练集大小: 300
2025-08-01 00:11:11,000 - INFO - [Trainer 32] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:11,007 - INFO - [Trainer 32] 开始第 1/5 个epoch
2025-08-01 00:11:11,042 - INFO - [Trainer 11] 模型已移至设备: cpu
2025-08-01 00:11:11,045 - INFO - [Trainer 11] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:11,053 - INFO - [Trainer 11] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:11,062 - INFO - 客户端 16 开始异步训练循环
2025-08-01 00:11:11,063 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:11,093 - INFO - [Trainer 11] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:11,095 - INFO - [Trainer 11] 开始第 1/5 个epoch
2025-08-01 00:11:11,106 - INFO - [客户端类型: Client, ID: 16] 准备开始训练
2025-08-01 00:11:11,108 - INFO - [客户端 16] 使用的训练器 client_id: 16
2025-08-01 00:11:11,117 - INFO - [Client 16] 开始验证训练集
2025-08-01 00:11:11,188 - INFO - [Client 16] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:11,189 - INFO - [Client 16] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:11,191 - INFO - [Trainer 16] 开始训练
2025-08-01 00:11:11,194 - INFO - [Trainer 16] 训练集大小: 300
2025-08-01 00:11:11,265 - INFO - [Client 41] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:11,282 - INFO - [Trainer 16] 模型已移至设备: cpu
2025-08-01 00:11:11,283 - INFO - [Trainer 16] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:11,284 - INFO - [Trainer 16] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:11,307 - INFO - [Trainer 16] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:11,309 - INFO - [Trainer 16] 开始第 1/5 个epoch
2025-08-01 00:11:11,353 - INFO - [Trainer 9] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:11,376 - INFO - [Trainer 9] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2500
2025-08-01 00:11:11,377 - INFO - [Trainer 9] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:11,381 - INFO - [Trainer 9] 标签样本: [6, 6, 6, 7, 6]
2025-08-01 00:11:11,387 - INFO - 客户端 14 开始异步训练循环
2025-08-01 00:11:11,390 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:11,406 - INFO - [客户端类型: Client, ID: 14] 准备开始训练
2025-08-01 00:11:11,407 - INFO - [客户端 14] 使用的训练器 client_id: 14
2025-08-01 00:11:11,412 - INFO - [Client 14] 开始验证训练集
2025-08-01 00:11:11,444 - INFO - [Trainer 12] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4651, y=[3, 3, 5, 3, 3]
2025-08-01 00:11:11,447 - INFO - [Trainer 12] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:11,454 - INFO - [Trainer 20] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4759, y=[1, 9, 1, 9, 1]
2025-08-01 00:11:11,458 - INFO - [Trainer 20] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:11,469 - INFO - [Client 14] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:11,471 - INFO - [Client 14] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:11,475 - INFO - [Trainer 14] 开始训练
2025-08-01 00:11:11,477 - INFO - [Trainer 14] 训练集大小: 300
2025-08-01 00:11:11,497 - INFO - [Trainer 9] Batch 0, Loss: 2.5090
2025-08-01 00:11:11,502 - INFO - [Trainer 37] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:11,512 - INFO - 客户端 39 开始异步训练循环
2025-08-01 00:11:11,513 - INFO - [Trainer 37] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0445
2025-08-01 00:11:11,516 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:11,516 - INFO - [Trainer 25] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1565, y=[0, 0, 0, 0, 0]
2025-08-01 00:11:11,518 - INFO - [Trainer 36] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5513, y=[1, 1, 1, 5, 5]
2025-08-01 00:11:11,524 - INFO - [Trainer 37] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:11,528 - INFO - [Trainer 25] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:11,531 - INFO - [Trainer 36] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:11,532 - INFO - [Client 41] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:11,533 - INFO - [Trainer 37] 标签样本: [0, 0, 0, 0, 0]
2025-08-01 00:11:11,535 - INFO - [客户端类型: Client, ID: 39] 准备开始训练
2025-08-01 00:11:11,541 - INFO - [Algorithm] 设置客户端ID: 41
2025-08-01 00:11:11,550 - INFO - [客户端 39] 使用的训练器 client_id: 39
2025-08-01 00:11:11,550 - INFO - [Trainer 4] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2604, y=[5, 5, 0, 0, 0]
2025-08-01 00:11:11,553 - INFO - [Algorithm] 同步更新trainer的client_id: 41
2025-08-01 00:11:11,558 - INFO - [Client 39] 开始验证训练集
2025-08-01 00:11:11,567 - INFO - [Trainer 4] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:11,569 - INFO - [Client 41] 已更新algorithm的client_id
2025-08-01 00:11:11,577 - INFO - [Trainer 14] 模型已移至设备: cpu
2025-08-01 00:11:11,591 - INFO - [Client 41] 模型初始化完成
2025-08-01 00:11:11,596 - INFO - [Trainer 13] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2769, y=[4, 2, 2, 1, 2]
2025-08-01 00:11:11,599 - INFO - [Trainer 14] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:11,607 - INFO - [Client 39] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:11,610 - INFO - [Trainer 13] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:11,613 - INFO - 客户端 41 模型初始化成功
2025-08-01 00:11:11,621 - INFO - [Trainer 14] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:11,627 - INFO - [Client 39] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:11,651 - INFO - [Trainer 39] 开始训练
2025-08-01 00:11:11,652 - INFO - [Trainer 35] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:11,653 - INFO - 客户端 41 异步训练线程已启动
2025-08-01 00:11:11,655 - INFO - [Trainer 39] 训练集大小: 300
2025-08-01 00:11:11,688 - INFO - [Trainer 14] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:11,690 - INFO - [Trainer 14] 开始第 1/5 个epoch
2025-08-01 00:11:11,720 - INFO - [Trainer 35] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5089
2025-08-01 00:11:11,730 - INFO - [Trainer 35] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:11,732 - INFO - [Trainer 35] 标签样本: [2, 3, 6, 0, 3]
2025-08-01 00:11:11,759 - INFO - [Client 42] 模型已放置到设备: cpu
2025-08-01 00:11:11,789 - INFO - [Trainer 39] 模型已移至设备: cpu
2025-08-01 00:11:11,790 - INFO - [Trainer 37] Batch 0, Loss: 2.6716
2025-08-01 00:11:11,800 - INFO - [Trainer 39] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:11,808 - INFO - [Trainer 39] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:11,822 - INFO - [Trainer 39] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:11,843 - INFO - [Trainer 39] 开始第 1/5 个epoch
2025-08-01 00:11:12,044 - INFO - [Trainer 35] Batch 0, Loss: 2.5392
2025-08-01 00:11:12,211 - INFO - [Client 42] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:12,342 - INFO - [Trainer 11] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5052, y=[4, 4, 4, 4, 6]
2025-08-01 00:11:12,347 - INFO - [Trainer 11] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:12,443 - INFO - 客户端 30 开始异步训练循环
2025-08-01 00:11:12,448 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:12,459 - INFO - [Trainer 32] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=0.0496, y=[2, 2, 0, 2, 0]
2025-08-01 00:11:12,465 - INFO - [Trainer 32] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:12,485 - INFO - [客户端类型: Client, ID: 30] 准备开始训练
2025-08-01 00:11:12,488 - INFO - [客户端 30] 使用的训练器 client_id: 30
2025-08-01 00:11:12,498 - INFO - [Client 30] 开始验证训练集
2025-08-01 00:11:12,549 - INFO - [Client 30] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:12,550 - INFO - [Client 30] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:12,552 - INFO - [Trainer 30] 开始训练
2025-08-01 00:11:12,559 - INFO - [Trainer 30] 训练集大小: 300
2025-08-01 00:11:12,604 - INFO - [Trainer 30] 模型已移至设备: cpu
2025-08-01 00:11:12,607 - INFO - [Trainer 30] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:12,617 - INFO - [Trainer 30] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:12,642 - INFO - [Trainer 15] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2524, y=[0, 7, 7, 5, 0]
2025-08-01 00:11:12,650 - INFO - [Trainer 15] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:12,664 - INFO - [Trainer 30] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:12,676 - INFO - [Trainer 30] 开始第 1/5 个epoch
2025-08-01 00:11:12,711 - INFO - [Client 42] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:12,723 - INFO - [Algorithm] 设置客户端ID: 42
2025-08-01 00:11:12,725 - INFO - [Algorithm] 同步更新trainer的client_id: 42
2025-08-01 00:11:12,737 - INFO - [Client 42] 已更新algorithm的client_id
2025-08-01 00:11:12,739 - INFO - [Client 42] 模型初始化完成
2025-08-01 00:11:12,749 - INFO - 客户端 42 模型初始化成功
2025-08-01 00:11:12,777 - INFO - 客户端 42 异步训练线程已启动
2025-08-01 00:11:12,860 - INFO - [Client 43] 模型已放置到设备: cpu
2025-08-01 00:11:13,001 - INFO - [Trainer 5] Batch 5, Loss: 1.1311
2025-08-01 00:11:13,045 - INFO - [Trainer 16] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3387, y=[6, 8, 5, 3, 6]
2025-08-01 00:11:13,045 - INFO - [Trainer 16] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:13,098 - INFO - [Trainer 39] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4010, y=[5, 6, 5, 6, 5]
2025-08-01 00:11:13,103 - INFO - [Trainer 39] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:13,120 - INFO - [Trainer 6] Batch 5, Loss: 1.5100
2025-08-01 00:11:13,171 - INFO - [Trainer 36] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:13,195 - INFO - [Trainer 36] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5730
2025-08-01 00:11:13,196 - INFO - [Trainer 36] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:13,197 - INFO - [Trainer 36] 标签样本: [1, 1, 1, 1, 5]
2025-08-01 00:11:13,215 - INFO - [Client 43] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:13,230 - INFO - [Trainer 25] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:13,276 - INFO - [Trainer 25] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0549
2025-08-01 00:11:13,279 - INFO - [Trainer 25] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:13,284 - INFO - [Trainer 25] 标签样本: [0, 0, 0, 0, 0]
2025-08-01 00:11:13,322 - INFO - [Trainer 12] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:13,356 - INFO - [Trainer 12] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0785
2025-08-01 00:11:13,357 - INFO - [Trainer 12] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:13,366 - INFO - [Trainer 12] 标签样本: [3, 3, 3, 3, 3]
2025-08-01 00:11:13,391 - INFO - [Trainer 36] Batch 0, Loss: 2.2610
2025-08-01 00:11:13,472 - INFO - [Trainer 13] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:13,482 - INFO - [Trainer 25] Batch 0, Loss: 2.8316
2025-08-01 00:11:13,537 - INFO - [Trainer 13] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4640
2025-08-01 00:11:13,548 - INFO - [Trainer 13] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:13,566 - INFO - [Trainer 20] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:13,568 - INFO - [Trainer 13] 标签样本: [2, 1, 1, 2, 2]
2025-08-01 00:11:13,620 - INFO - [Trainer 4] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:13,637 - INFO - [Trainer 20] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3457
2025-08-01 00:11:13,643 - INFO - [Trainer 20] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:13,666 - INFO - [Trainer 20] 标签样本: [1, 6, 1, 6, 6]
2025-08-01 00:11:13,670 - INFO - [Trainer 4] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2657
2025-08-01 00:11:13,711 - INFO - [Trainer 4] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:13,727 - INFO - [Trainer 4] 标签样本: [0, 1, 0, 1, 1]
2025-08-01 00:11:13,757 - INFO - [Trainer 12] Batch 0, Loss: 2.0402
2025-08-01 00:11:13,889 - INFO - [Client 43] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:13,891 - INFO - [Algorithm] 设置客户端ID: 43
2025-08-01 00:11:13,911 - INFO - [Algorithm] 同步更新trainer的client_id: 43
2025-08-01 00:11:13,919 - INFO - [Client 43] 已更新algorithm的client_id
2025-08-01 00:11:13,927 - INFO - [Client 43] 模型初始化完成
2025-08-01 00:11:13,969 - INFO - 客户端 43 模型初始化成功
2025-08-01 00:11:14,017 - INFO - 客户端 43 异步训练线程已启动
2025-08-01 00:11:14,095 - INFO - [Trainer 14] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4374, y=[6, 4, 8, 8, 4]
2025-08-01 00:11:14,098 - INFO - [Trainer 14] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:14,113 - INFO - [Trainer 11] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:14,152 - INFO - [Trainer 11] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4972
2025-08-01 00:11:14,159 - INFO - [Trainer 11] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:14,163 - INFO - [Client 44] 模型已放置到设备: cpu
2025-08-01 00:11:14,164 - INFO - [Trainer 11] 标签样本: [4, 4, 4, 4, 4]
2025-08-01 00:11:14,251 - INFO - 客户端 38 开始异步训练循环
2025-08-01 00:11:14,254 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:14,276 - INFO - [客户端类型: Client, ID: 38] 准备开始训练
2025-08-01 00:11:14,329 - INFO - [客户端 38] 使用的训练器 client_id: 38
2025-08-01 00:11:14,335 - INFO - [Client 38] 开始验证训练集
2025-08-01 00:11:14,369 - INFO - [Trainer 30] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4989, y=[2, 2, 2, 2, 2]
2025-08-01 00:11:14,377 - INFO - [Trainer 30] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:14,377 - INFO - [Client 38] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:14,378 - INFO - [Trainer 13] Batch 0, Loss: 2.1517
2025-08-01 00:11:14,389 - INFO - [Client 38] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:14,426 - INFO - [Trainer 1] Batch 5, Loss: 3.7383
2025-08-01 00:11:14,439 - INFO - [Trainer 38] 开始训练
2025-08-01 00:11:14,452 - INFO - [Trainer 20] Batch 0, Loss: 2.4540
2025-08-01 00:11:14,458 - INFO - [Trainer 4] Batch 0, Loss: 2.3044
2025-08-01 00:11:14,475 - INFO - [Trainer 38] 训练集大小: 300
2025-08-01 00:11:14,527 - INFO - 客户端 34 开始异步训练循环
2025-08-01 00:11:14,549 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:14,575 - INFO - [Trainer 38] 模型已移至设备: cpu
2025-08-01 00:11:14,578 - INFO - [Trainer 38] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:14,584 - INFO - [客户端类型: Client, ID: 34] 准备开始训练
2025-08-01 00:11:14,624 - INFO - [客户端 34] 使用的训练器 client_id: 34
2025-08-01 00:11:14,625 - INFO - [Client 34] 开始验证训练集
2025-08-01 00:11:14,633 - INFO - [Trainer 38] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:14,671 - INFO - [Client 34] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:14,672 - INFO - [Trainer 32] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:14,710 - INFO - [Trainer 38] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:14,718 - INFO - [Client 34] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:14,762 - INFO - [Trainer 39] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:14,777 - INFO - [Trainer 38] 开始第 1/5 个epoch
2025-08-01 00:11:14,863 - INFO - [Trainer 32] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0173
2025-08-01 00:11:14,864 - INFO - [Trainer 34] 开始训练
2025-08-01 00:11:14,938 - INFO - [Trainer 34] 训练集大小: 300
2025-08-01 00:11:14,939 - INFO - [Trainer 32] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:14,962 - INFO - [Trainer 39] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5115
2025-08-01 00:11:14,963 - INFO - [Trainer 34] 模型已移至设备: cpu
2025-08-01 00:11:14,980 - INFO - [Trainer 32] 标签样本: [0, 0, 0, 2, 0]
2025-08-01 00:11:15,006 - INFO - [Client 44] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:15,059 - INFO - [Trainer 34] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:15,082 - INFO - [Trainer 39] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:15,094 - INFO - [Trainer 34] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:15,126 - INFO - [Trainer 39] 标签样本: [4, 5, 4, 6, 4]
2025-08-01 00:11:15,263 - INFO - [Trainer 15] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:15,289 - INFO - [Trainer 34] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:15,313 - INFO - [Trainer 34] 开始第 1/5 个epoch
2025-08-01 00:11:15,390 - INFO - [Trainer 15] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3249
2025-08-01 00:11:15,394 - INFO - [Trainer 15] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:15,398 - INFO - [Trainer 15] 标签样本: [5, 5, 5, 5, 5]
2025-08-01 00:11:15,445 - INFO - [Trainer 30] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:15,497 - INFO - [Trainer 16] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:15,558 - INFO - [Trainer 14] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:15,646 - INFO - [Trainer 30] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3286
2025-08-01 00:11:15,647 - INFO - [Trainer 30] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:15,651 - INFO - [Trainer 30] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 00:11:15,702 - INFO - [Trainer 11] Batch 0, Loss: 2.2584
2025-08-01 00:11:15,708 - INFO - [Trainer 38] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5061, y=[1, 1, 1, 1, 1]
2025-08-01 00:11:15,712 - INFO - [Trainer 16] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4130
2025-08-01 00:11:15,716 - INFO - [Trainer 14] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3886
2025-08-01 00:11:15,724 - INFO - [Trainer 38] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:15,731 - INFO - [Trainer 16] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:15,731 - INFO - [Trainer 14] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:15,733 - INFO - [Trainer 16] 标签样本: [6, 6, 6, 3, 6]
2025-08-01 00:11:15,737 - INFO - [Trainer 14] 标签样本: [7, 4, 7, 7, 4]
2025-08-01 00:11:15,824 - INFO - [Client 44] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:15,827 - INFO - [Algorithm] 设置客户端ID: 44
2025-08-01 00:11:15,827 - INFO - [Algorithm] 同步更新trainer的client_id: 44
2025-08-01 00:11:15,828 - INFO - [Client 44] 已更新algorithm的client_id
2025-08-01 00:11:15,832 - INFO - [Client 44] 模型初始化完成
2025-08-01 00:11:15,833 - INFO - 客户端 44 模型初始化成功
2025-08-01 00:11:15,845 - INFO - 客户端 44 异步训练线程已启动
2025-08-01 00:11:15,878 - INFO - [Client 45] 模型已放置到设备: cpu
2025-08-01 00:11:15,918 - INFO - [Trainer 34] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4530, y=[6, 7, 6, 7, 6]
2025-08-01 00:11:15,919 - INFO - [Trainer 34] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:16,268 - INFO - 客户端 40 开始异步训练循环
2025-08-01 00:11:16,287 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:16,311 - INFO - [客户端类型: Client, ID: 40] 准备开始训练
2025-08-01 00:11:16,313 - INFO - [客户端 40] 使用的训练器 client_id: 40
2025-08-01 00:11:16,316 - INFO - [Client 40] 开始验证训练集
2025-08-01 00:11:16,335 - INFO - [Client 40] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:16,339 - INFO - [Client 40] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:16,340 - INFO - [Trainer 40] 开始训练
2025-08-01 00:11:16,342 - INFO - [Trainer 40] 训练集大小: 300
2025-08-01 00:11:16,391 - INFO - [Trainer 40] 模型已移至设备: cpu
2025-08-01 00:11:16,459 - INFO - [Trainer 40] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:16,464 - INFO - 客户端 43 开始异步训练循环
2025-08-01 00:11:16,465 - INFO - [Trainer 40] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:16,466 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:16,534 - INFO - [客户端类型: Client, ID: 43] 准备开始训练
2025-08-01 00:11:16,550 - INFO - [客户端 43] 使用的训练器 client_id: 43
2025-08-01 00:11:16,553 - INFO - [Client 43] 开始验证训练集
2025-08-01 00:11:16,556 - INFO - [Trainer 38] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:16,556 - INFO - [Trainer 40] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:16,576 - INFO - [Trainer 40] 开始第 1/5 个epoch
2025-08-01 00:11:16,587 - INFO - [Client 45] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:16,642 - INFO - [Client 43] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:16,649 - INFO - [Client 43] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:16,651 - INFO - [Trainer 43] 开始训练
2025-08-01 00:11:16,651 - INFO - [Trainer 38] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4387
2025-08-01 00:11:16,652 - INFO - [Trainer 43] 训练集大小: 300
2025-08-01 00:11:16,652 - INFO - [Trainer 38] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:16,655 - INFO - [Trainer 38] 标签样本: [1, 1, 1, 1, 1]
2025-08-01 00:11:16,761 - INFO - [Trainer 43] 模型已移至设备: cpu
2025-08-01 00:11:16,770 - INFO - [Trainer 43] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:16,777 - INFO - [Trainer 43] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:16,824 - INFO - [Trainer 43] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:16,826 - INFO - [Trainer 43] 开始第 1/5 个epoch
2025-08-01 00:11:16,834 - INFO - [Trainer 18] Batch 5, Loss: 2.3736
2025-08-01 00:11:17,300 - INFO - [Trainer 32] Batch 0, Loss: 2.2718
2025-08-01 00:11:17,373 - INFO - [Trainer 39] Batch 0, Loss: 2.3966
2025-08-01 00:11:17,379 - INFO - [Trainer 30] Batch 0, Loss: 2.8730
2025-08-01 00:11:17,501 - INFO - [Trainer 15] Batch 0, Loss: 2.2354
2025-08-01 00:11:17,507 - INFO - [Trainer 7] Batch 5, Loss: 0.0000
2025-08-01 00:11:17,642 - INFO - [Client 45] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:17,645 - INFO - [Algorithm] 设置客户端ID: 45
2025-08-01 00:11:17,646 - INFO - [Algorithm] 同步更新trainer的client_id: 45
2025-08-01 00:11:17,647 - INFO - [Client 45] 已更新algorithm的client_id
2025-08-01 00:11:17,647 - INFO - [Client 45] 模型初始化完成
2025-08-01 00:11:17,653 - INFO - 客户端 45 模型初始化成功
2025-08-01 00:11:17,665 - INFO - 客户端 45 异步训练线程已启动
2025-08-01 00:11:17,701 - INFO - [Trainer 14] Batch 0, Loss: 2.1871
2025-08-01 00:11:17,730 - INFO - [Client 46] 模型已放置到设备: cpu
2025-08-01 00:11:17,737 - INFO - [Trainer 16] Batch 0, Loss: 2.3985
2025-08-01 00:11:17,758 - INFO - [Trainer 34] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:17,889 - INFO - [Trainer 34] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.6772
2025-08-01 00:11:17,894 - INFO - [Trainer 34] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:17,895 - INFO - [Trainer 34] 标签样本: [6, 7, 7, 7, 7]
2025-08-01 00:11:18,080 - INFO - [Trainer 38] Batch 0, Loss: 2.8330
2025-08-01 00:11:18,134 - INFO - [Client 46] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:18,211 - INFO - [Trainer 40] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2410, y=[5, 5, 5, 5, 5]
2025-08-01 00:11:18,213 - INFO - [Trainer 40] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:19,594 - INFO - 客户端 44 开始异步训练循环
2025-08-01 00:11:19,596 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:19,605 - INFO - [客户端类型: Client, ID: 44] 准备开始训练
2025-08-01 00:11:19,607 - INFO - [客户端 44] 使用的训练器 client_id: 44
2025-08-01 00:11:19,607 - INFO - [Client 44] 开始验证训练集
2025-08-01 00:11:19,612 - INFO - [Client 44] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:19,612 - INFO - [Client 44] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:19,615 - INFO - [Trainer 44] 开始训练
2025-08-01 00:11:19,615 - INFO - [Trainer 44] 训练集大小: 300
2025-08-01 00:11:19,618 - INFO - [Trainer 44] 模型已移至设备: cpu
2025-08-01 00:11:19,619 - INFO - [Trainer 44] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:19,622 - INFO - [Trainer 44] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:19,624 - INFO - [Trainer 44] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:19,626 - INFO - [Trainer 44] 开始第 1/5 个epoch
2025-08-01 00:11:19,667 - INFO - [Trainer 40] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:19,749 - INFO - 客户端 41 开始异步训练循环
2025-08-01 00:11:19,749 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:19,751 - INFO - [客户端类型: Client, ID: 41] 准备开始训练
2025-08-01 00:11:19,751 - INFO - [客户端 41] 使用的训练器 client_id: 41
2025-08-01 00:11:19,752 - INFO - [Client 41] 开始验证训练集
2025-08-01 00:11:19,755 - INFO - [Client 41] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:19,755 - INFO - [Client 41] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:19,755 - INFO - [Trainer 41] 开始训练
2025-08-01 00:11:19,756 - INFO - [Trainer 41] 训练集大小: 300
2025-08-01 00:11:19,757 - INFO - [Trainer 41] 模型已移至设备: cpu
2025-08-01 00:11:19,759 - INFO - [Trainer 41] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:19,759 - INFO - [Trainer 41] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:19,760 - INFO - [Trainer 41] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:19,760 - INFO - [Trainer 41] 开始第 1/5 个epoch
2025-08-01 00:11:19,964 - INFO - [Trainer 43] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3271, y=[7, 7, 7, 7, 7]
2025-08-01 00:11:19,966 - INFO - [Trainer 43] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:19,986 - INFO - [Trainer 40] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5174
2025-08-01 00:11:19,988 - INFO - [Trainer 40] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:19,989 - INFO - [Trainer 40] 标签样本: [5, 4, 6, 5, 5]
2025-08-01 00:11:20,054 - INFO - [Client 46] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:20,056 - INFO - [Algorithm] 设置客户端ID: 46
2025-08-01 00:11:20,056 - INFO - [Algorithm] 同步更新trainer的client_id: 46
2025-08-01 00:11:20,059 - INFO - [Client 46] 已更新algorithm的client_id
2025-08-01 00:11:20,059 - INFO - [Client 46] 模型初始化完成
2025-08-01 00:11:20,060 - INFO - 客户端 46 模型初始化成功
2025-08-01 00:11:20,082 - INFO - 客户端 46 异步训练线程已启动
2025-08-01 00:11:20,111 - INFO - [Client 47] 模型已放置到设备: cpu
2025-08-01 00:11:20,203 - INFO - [Trainer 44] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4116, y=[7, 4, 0, 7, 4]
2025-08-01 00:11:20,203 - INFO - [Trainer 43] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:20,203 - INFO - [Trainer 44] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:20,253 - INFO - [Trainer 44] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:20,270 - INFO - [Trainer 43] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3552
2025-08-01 00:11:20,277 - INFO - [Trainer 43] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:20,278 - INFO - [Trainer 43] 标签样本: [7, 7, 7, 7, 7]
2025-08-01 00:11:20,279 - INFO - [Trainer 41] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4206, y=[4, 4, 4, 4, 4]
2025-08-01 00:11:20,280 - INFO - [Trainer 41] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:20,322 - INFO - [Trainer 44] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3457
2025-08-01 00:11:20,324 - INFO - [Trainer 44] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:20,325 - INFO - [Trainer 44] 标签样本: [4, 4, 7, 7, 4]
2025-08-01 00:11:20,357 - INFO - [Trainer 41] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:20,371 - INFO - [Client 47] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:20,430 - INFO - [Trainer 41] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4791
2025-08-01 00:11:20,431 - INFO - [Trainer 41] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:20,432 - INFO - [Trainer 41] 标签样本: [4, 4, 4, 4, 9]
2025-08-01 00:11:20,592 - INFO - [Client 47] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:20,593 - INFO - [Algorithm] 设置客户端ID: 47
2025-08-01 00:11:20,593 - INFO - [Algorithm] 同步更新trainer的client_id: 47
2025-08-01 00:11:20,593 - INFO - [Client 47] 已更新algorithm的client_id
2025-08-01 00:11:20,594 - INFO - [Client 47] 模型初始化完成
2025-08-01 00:11:20,594 - INFO - 客户端 47 模型初始化成功
2025-08-01 00:11:20,597 - INFO - 客户端 47 异步训练线程已启动
2025-08-01 00:11:20,601 - INFO - [Client 48] 模型已放置到设备: cpu
2025-08-01 00:11:20,846 - INFO - [Client 48] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:21,076 - INFO - 客户端 42 开始异步训练循环
2025-08-01 00:11:21,077 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:21,088 - INFO - [客户端类型: Client, ID: 42] 准备开始训练
2025-08-01 00:11:21,097 - INFO - [客户端 42] 使用的训练器 client_id: 42
2025-08-01 00:11:21,099 - INFO - [Client 42] 开始验证训练集
2025-08-01 00:11:21,113 - INFO - [Client 42] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:21,114 - INFO - [Client 42] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:21,114 - INFO - [Trainer 42] 开始训练
2025-08-01 00:11:21,114 - INFO - [Trainer 42] 训练集大小: 300
2025-08-01 00:11:21,131 - INFO - [Trainer 42] 模型已移至设备: cpu
2025-08-01 00:11:21,133 - INFO - [Trainer 42] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:21,137 - INFO - [Trainer 42] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:21,141 - INFO - [Trainer 42] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:21,144 - INFO - [Trainer 42] 开始第 1/5 个epoch
2025-08-01 00:11:21,245 - INFO - [Client 48] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:21,246 - INFO - [Algorithm] 设置客户端ID: 48
2025-08-01 00:11:21,246 - INFO - [Algorithm] 同步更新trainer的client_id: 48
2025-08-01 00:11:21,248 - INFO - [Client 48] 已更新algorithm的client_id
2025-08-01 00:11:21,249 - INFO - [Client 48] 模型初始化完成
2025-08-01 00:11:21,250 - INFO - 客户端 48 模型初始化成功
2025-08-01 00:11:21,262 - INFO - 客户端 48 异步训练线程已启动
2025-08-01 00:11:21,299 - INFO - [Trainer 34] Batch 0, Loss: 2.5101
2025-08-01 00:11:21,318 - INFO - [Client 49] 模型已放置到设备: cpu
2025-08-01 00:11:21,553 - INFO - [Client 49] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:21,760 - INFO - [Trainer 40] Batch 0, Loss: 2.3233
2025-08-01 00:11:21,900 - INFO - [Trainer 43] Batch 0, Loss: 2.2282
2025-08-01 00:11:21,913 - INFO - [Client 49] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:21,914 - INFO - [Trainer 42] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5803, y=[6, 1, 6, 6, 6]
2025-08-01 00:11:21,915 - INFO - [Algorithm] 设置客户端ID: 49
2025-08-01 00:11:21,916 - INFO - [Trainer 42] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:21,918 - INFO - [Algorithm] 同步更新trainer的client_id: 49
2025-08-01 00:11:21,929 - INFO - [Client 49] 已更新algorithm的client_id
2025-08-01 00:11:21,930 - INFO - [Client 49] 模型初始化完成
2025-08-01 00:11:21,933 - INFO - 客户端 49 模型初始化成功
2025-08-01 00:11:21,939 - INFO - [Trainer 44] Batch 0, Loss: 2.2245
2025-08-01 00:11:21,942 - INFO - 客户端 49 异步训练线程已启动
2025-08-01 00:11:21,993 - INFO - [Client 50] 模型已放置到设备: cpu
2025-08-01 00:11:22,189 - INFO - [Trainer 42] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:22,267 - INFO - [Trainer 41] Batch 0, Loss: 2.3543
2025-08-01 00:11:22,271 - INFO - [Trainer 42] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5353
2025-08-01 00:11:22,300 - INFO - [Trainer 42] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:22,300 - INFO - [Trainer 42] 标签样本: [4, 1, 6, 6, 6]
2025-08-01 00:11:23,172 - INFO - 客户端 45 开始异步训练循环
2025-08-01 00:11:23,173 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:23,175 - INFO - [客户端类型: Client, ID: 45] 准备开始训练
2025-08-01 00:11:23,175 - INFO - [客户端 45] 使用的训练器 client_id: 45
2025-08-01 00:11:23,176 - INFO - [Client 45] 开始验证训练集
2025-08-01 00:11:23,194 - INFO - [Client 45] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:23,284 - INFO - [Client 50] 已为trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:23,311 - INFO - [Client 45] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:23,313 - INFO - [Trainer 45] 开始训练
2025-08-01 00:11:23,330 - INFO - [Trainer 45] 训练集大小: 300
2025-08-01 00:11:23,331 - INFO - [Trainer 45] 模型已移至设备: cpu
2025-08-01 00:11:23,332 - INFO - [Trainer 45] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:23,335 - INFO - [Trainer 45] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:23,342 - INFO - [Trainer 45] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:23,345 - INFO - [Trainer 45] 开始第 1/5 个epoch
2025-08-01 00:11:23,526 - INFO - [Trainer 45] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.6330, y=[6, 6, 6, 6, 6]
2025-08-01 00:11:23,527 - INFO - [Trainer 45] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:23,598 - INFO - [Trainer 45] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:23,599 - INFO - [Client 50] 已为algorithm的trainer创建模型的深拷贝，避免实例共享
2025-08-01 00:11:23,601 - INFO - [Algorithm] 设置客户端ID: 50
2025-08-01 00:11:23,602 - INFO - [Algorithm] 同步更新trainer的client_id: 50
2025-08-01 00:11:23,603 - INFO - [Client 50] 已更新algorithm的client_id
2025-08-01 00:11:23,603 - INFO - [Client 50] 模型初始化完成
2025-08-01 00:11:23,604 - INFO - 客户端 50 模型初始化成功
2025-08-01 00:11:23,607 - INFO - 客户端 50 异步训练线程已启动
2025-08-01 00:11:23,611 - INFO - 服务器主循环任务已启动: <Task pending name='Task-46' coro=<Server.run() running at D:\Experiment\orgin-plato-main\plato-main-7.6\examples\async\SC_AFL\sc_afl_server.py:1301>>
2025-08-01 00:11:23,614 - INFO - Starting a server at address 127.0.0.1 and port 8000.
2025-08-01 00:11:23,766 - INFO - [Trainer 45] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.5511
2025-08-01 00:11:23,768 - INFO - [Trainer 45] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:23,788 - INFO - [Server #10168] 开始训练，共有 50 个客户端，每轮最多聚合 5 个客户端
2025-08-01 00:11:23,790 - INFO - [Trainer 45] 标签样本: [6, 6, 6, 6, 6]
2025-08-01 00:11:23,803 - INFO - 总训练轮次: 500
2025-08-01 00:11:23,827 - INFO - 🚀 开始第 1 轮训练（目标：500 轮）
2025-08-01 00:11:23,828 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:23,829 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:24,663 - INFO - 客户端 46 开始异步训练循环
2025-08-01 00:11:24,664 - INFO - 客户端 49 开始异步训练循环
2025-08-01 00:11:24,683 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:24,706 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:24,708 - INFO - [客户端类型: Client, ID: 46] 准备开始训练
2025-08-01 00:11:24,709 - INFO - [客户端类型: Client, ID: 49] 准备开始训练
2025-08-01 00:11:24,719 - INFO - [客户端 46] 使用的训练器 client_id: 46
2025-08-01 00:11:24,722 - INFO - [客户端 49] 使用的训练器 client_id: 49
2025-08-01 00:11:24,883 - INFO - [Client 46] 开始验证训练集
2025-08-01 00:11:24,890 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:24,898 - INFO - [Client 49] 开始验证训练集
2025-08-01 00:11:24,963 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:25,086 - INFO - [Client 46] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:25,093 - INFO - [Client 46] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:25,100 - INFO - [Trainer 46] 开始训练
2025-08-01 00:11:25,100 - INFO - [Trainer 46] 训练集大小: 300
2025-08-01 00:11:25,101 - INFO - [Trainer 46] 模型已移至设备: cpu
2025-08-01 00:11:25,101 - INFO - [Trainer 46] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:25,102 - INFO - [Trainer 46] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:25,102 - INFO - [Trainer 46] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:25,102 - INFO - [Trainer 46] 开始第 1/5 个epoch
2025-08-01 00:11:25,122 - INFO - [Client 49] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:25,125 - INFO - [Client 49] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:25,125 - INFO - [Trainer 49] 开始训练
2025-08-01 00:11:25,126 - INFO - [Trainer 49] 训练集大小: 300
2025-08-01 00:11:25,142 - INFO - [Trainer 49] 模型已移至设备: cpu
2025-08-01 00:11:25,144 - INFO - [Trainer 49] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:25,146 - INFO - [Trainer 49] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:25,151 - INFO - [Trainer 49] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:25,152 - INFO - [Trainer 49] 开始第 1/5 个epoch
2025-08-01 00:11:25,387 - INFO - [Trainer 46] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4011, y=[4, 4, 7, 7, 7]
2025-08-01 00:11:25,389 - INFO - [Trainer 46] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:25,661 - INFO - 客户端 48 开始异步训练循环
2025-08-01 00:11:25,677 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:25,691 - INFO - [客户端类型: Client, ID: 48] 准备开始训练
2025-08-01 00:11:25,692 - INFO - [客户端 48] 使用的训练器 client_id: 48
2025-08-01 00:11:25,692 - INFO - [Client 48] 开始验证训练集
2025-08-01 00:11:25,699 - INFO - [Client 48] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:25,709 - INFO - [Client 48] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:25,726 - INFO - [Trainer 48] 开始训练
2025-08-01 00:11:25,731 - INFO - [Trainer 48] 训练集大小: 300
2025-08-01 00:11:25,737 - INFO - [Trainer 48] 模型已移至设备: cpu
2025-08-01 00:11:25,740 - INFO - [Trainer 48] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:25,741 - INFO - [Trainer 48] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:25,755 - INFO - [Trainer 48] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:25,757 - INFO - [Trainer 48] 开始第 1/5 个epoch
2025-08-01 00:11:25,782 - INFO - [Trainer 46] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:25,849 - INFO - [Trainer 49] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2358, y=[1, 7, 8, 8, 8]
2025-08-01 00:11:25,849 - INFO - [Trainer 49] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:26,025 - INFO - [Trainer 46] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3491
2025-08-01 00:11:26,033 - INFO - [Trainer 46] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:26,044 - INFO - [Trainer 46] 标签样本: [4, 4, 7, 0, 7]
2025-08-01 00:11:26,072 - INFO - [Trainer 49] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:26,103 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:26,107 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:26,146 - INFO - [Trainer 42] Batch 0, Loss: 2.3508
2025-08-01 00:11:26,153 - INFO - [Trainer 49] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1190
2025-08-01 00:11:26,153 - INFO - [Trainer 49] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:26,155 - INFO - [Trainer 49] 标签样本: [8, 8, 8, 7, 8]
2025-08-01 00:11:26,166 - INFO - [Trainer 48] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.6562, x.mean=-0.4474, y=[2, 3, 3, 3, 3]
2025-08-01 00:11:26,170 - INFO - [Trainer 48] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:26,248 - INFO - [Trainer 2] Batch 5, Loss: 0.0000
2025-08-01 00:11:26,298 - INFO - [Trainer 48] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:26,358 - INFO - [Trainer 48] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.6299
2025-08-01 00:11:26,363 - INFO - [Trainer 48] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:26,364 - INFO - [Trainer 48] 标签样本: [2, 3, 3, 3, 3]
2025-08-01 00:11:27,119 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:27,119 - INFO - [Trainer 45] Batch 0, Loss: 2.4056
2025-08-01 00:11:27,139 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:27,196 - INFO - 客户端 47 开始异步训练循环
2025-08-01 00:11:27,371 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:27,608 - INFO - [客户端类型: Client, ID: 47] 准备开始训练
2025-08-01 00:11:27,965 - INFO - [客户端 47] 使用的训练器 client_id: 47
2025-08-01 00:11:27,966 - INFO - [Client 47] 开始验证训练集
2025-08-01 00:11:27,974 - INFO - [Client 47] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:27,980 - INFO - [Client 47] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:27,986 - INFO - [Trainer 47] 开始训练
2025-08-01 00:11:27,986 - INFO - [Trainer 47] 训练集大小: 300
2025-08-01 00:11:27,996 - INFO - [Trainer 47] 模型已移至设备: cpu
2025-08-01 00:11:27,999 - INFO - [Trainer 47] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:28,003 - INFO - [Trainer 47] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:28,006 - INFO - [Trainer 47] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:28,006 - INFO - [Trainer 47] 开始第 1/5 个epoch
2025-08-01 00:11:28,147 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:28,148 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:28,434 - INFO - [Trainer 47] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4578, y=[2, 2, 4, 1, 1]
2025-08-01 00:11:28,436 - INFO - [Trainer 47] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:28,726 - INFO - [Trainer 47] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:28,776 - INFO - [Trainer 31] Batch 5, Loss: 0.9991
2025-08-01 00:11:28,850 - INFO - [Trainer 47] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3915
2025-08-01 00:11:28,855 - INFO - [Trainer 47] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:28,857 - INFO - [Trainer 47] 标签样本: [2, 1, 4, 4, 7]
2025-08-01 00:11:29,221 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:29,229 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:30,524 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:31,257 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:32,716 - INFO - 客户端 50 开始异步训练循环
2025-08-01 00:11:32,847 - DEBUG - Using proactor: IocpProactor
2025-08-01 00:11:32,857 - INFO - [客户端类型: Client, ID: 50] 准备开始训练
2025-08-01 00:11:32,865 - INFO - [客户端 50] 使用的训练器 client_id: 50
2025-08-01 00:11:32,867 - INFO - [Client 50] 开始验证训练集
2025-08-01 00:11:32,872 - INFO - [Client 50] 成功访问第一个训练样本: <class 'tuple'>
2025-08-01 00:11:32,875 - INFO - [Client 50] 🚀 开始训练，数据集大小: 300
2025-08-01 00:11:32,927 - INFO - [Trainer 50] 开始训练
2025-08-01 00:11:32,927 - INFO - [Trainer 50] 训练集大小: 300
2025-08-01 00:11:32,936 - INFO - [Trainer 50] 模型已移至设备: cpu
2025-08-01 00:11:32,939 - INFO - [Trainer 50] 创建优化器，lr=0.05, momentum=0.9, weight_decay=0.0001
2025-08-01 00:11:32,944 - INFO - [Trainer 50] 创建数据加载器，批次大小: 32, 批次数: 10
2025-08-01 00:11:32,952 - INFO - [Trainer 50] 开始训练 5 个epoch，已重置BatchNorm统计信息
2025-08-01 00:11:32,953 - INFO - [Trainer 50] 开始第 1/5 个epoch
2025-08-01 00:11:33,034 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:33,036 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:33,319 - INFO - [Trainer 50] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4033, y=[7, 7, 7, 7, 9]
2025-08-01 00:11:33,322 - INFO - [Trainer 50] Epoch 1 开始处理 10 个批次
2025-08-01 00:11:33,623 - INFO - [Trainer 48] Batch 0, Loss: 2.4956
2025-08-01 00:11:33,764 - INFO - [Trainer 46] Batch 0, Loss: 2.3504
2025-08-01 00:11:33,787 - INFO - [Trainer 50] Epoch 1 进度: 1/10 批次
2025-08-01 00:11:33,844 - INFO - [Trainer 49] Batch 0, Loss: 2.1721
2025-08-01 00:11:33,883 - INFO - [Trainer 50] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3198
2025-08-01 00:11:33,884 - INFO - [Trainer 50] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:33,884 - INFO - [Trainer 50] 标签样本: [7, 9, 7, 7, 7]
2025-08-01 00:11:34,038 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:34,041 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:34,812 - INFO - [Trainer 28] Batch 5, Loss: 1.6183
2025-08-01 00:11:35,087 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:35,094 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:35,411 - INFO - [Trainer 47] Batch 0, Loss: 2.4667
2025-08-01 00:11:36,116 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:36,126 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:36,671 - INFO - [Trainer 50] Batch 0, Loss: 3.0054
2025-08-01 00:11:37,138 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:37,145 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:38,157 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:38,163 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:39,177 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:39,179 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:39,779 - INFO - [Trainer 21] Batch 5, Loss: 1.1590
2025-08-01 00:11:40,186 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:40,187 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:41,199 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:41,208 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:41,368 - INFO - [Trainer 3] Epoch 1 进度: 10/10 批次
2025-08-01 00:11:41,445 - INFO - [Trainer 3] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.6172, x.mean: -0.4460
2025-08-01 00:11:41,448 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:41,449 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 00:11:42,236 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:42,240 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:43,249 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:43,250 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:43,537 - INFO - [Trainer 10] Batch 5, Loss: 0.4653
2025-08-01 00:11:43,824 - INFO - [Trainer 3] Batch 9, Loss: 0.0208
2025-08-01 00:11:44,278 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:44,279 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:44,491 - INFO - [Trainer 29] Batch 5, Loss: 0.9699
2025-08-01 00:11:45,288 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:45,288 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:45,719 - INFO - [Trainer 23] Batch 5, Loss: 1.6734
2025-08-01 00:11:46,291 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:46,298 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:46,651 - INFO - [Trainer 24] Batch 5, Loss: 2.9184
2025-08-01 00:11:47,135 - INFO - [Trainer 26] Batch 5, Loss: 0.6184
2025-08-01 00:11:47,315 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:47,317 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:48,331 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:48,331 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:48,901 - INFO - [Trainer 3] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 00:11:48,905 - INFO - [Trainer 3] Epoch 1/5 完成 - 处理了 10 个批次, Loss: 1.2147, Accuracy: 81.33%
2025-08-01 00:11:48,908 - INFO - [Trainer 3] 开始第 2/5 个epoch
2025-08-01 00:11:49,064 - INFO - [Trainer 3] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.5221, y=[2, 2, 2, 2, 2]
2025-08-01 00:11:49,064 - INFO - [Trainer 3] Epoch 2 开始处理 10 个批次
2025-08-01 00:11:49,087 - INFO - [Trainer 3] Epoch 2 进度: 1/10 批次
2025-08-01 00:11:49,213 - INFO - [Trainer 3] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4175
2025-08-01 00:11:49,222 - INFO - [Trainer 3] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:49,223 - INFO - [Trainer 3] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 00:11:49,255 - INFO - [Trainer 18] Epoch 1 进度: 10/10 批次
2025-08-01 00:11:49,270 - INFO - [Trainer 22] Batch 5, Loss: 0.4284
2025-08-01 00:11:49,337 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:49,372 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:49,375 - INFO - [Trainer 18] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3291
2025-08-01 00:11:49,376 - INFO - [Trainer 18] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:49,376 - INFO - [Trainer 18] 标签样本: [1, 0, 9, 2, 1]
2025-08-01 00:11:49,466 - INFO - [Trainer 6] Epoch 1 进度: 10/10 批次
2025-08-01 00:11:49,533 - INFO - [Trainer 6] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.1161
2025-08-01 00:11:49,537 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:49,539 - INFO - [Trainer 6] 标签样本: [8, 8, 0, 7, 0]
2025-08-01 00:11:49,595 - INFO - [Trainer 8] Batch 5, Loss: 2.0472
2025-08-01 00:11:49,659 - INFO - [Trainer 5] Epoch 1 进度: 10/10 批次
2025-08-01 00:11:49,724 - INFO - [Trainer 5] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2170
2025-08-01 00:11:49,725 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:49,725 - INFO - [Trainer 5] 标签样本: [7, 7, 7, 3, 7]
2025-08-01 00:11:50,389 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:50,393 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:50,817 - INFO - [Trainer 33] Batch 5, Loss: 0.6426
2025-08-01 00:11:50,955 - INFO - [Trainer 17] Batch 5, Loss: 1.2826
2025-08-01 00:11:51,407 - INFO - [Trainer 18] Batch 9, Loss: 2.5930
2025-08-01 00:11:51,409 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:51,411 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:51,483 - INFO - [Trainer 3] Batch 0, Loss: 0.3164
2025-08-01 00:11:51,628 - INFO - [Trainer 1] Epoch 1 进度: 10/10 批次
2025-08-01 00:11:51,691 - INFO - [Trainer 1] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4872
2025-08-01 00:11:51,693 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:51,699 - INFO - [Trainer 1] 标签样本: [5, 0, 8, 5, 8]
2025-08-01 00:11:51,702 - INFO - [Trainer 6] Batch 9, Loss: 1.2565
2025-08-01 00:11:51,959 - INFO - [Trainer 5] Batch 9, Loss: 0.3344
2025-08-01 00:11:52,422 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:52,454 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:53,306 - INFO - [Trainer 9] Batch 5, Loss: 0.2663
2025-08-01 00:11:53,461 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:53,462 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:53,714 - INFO - [Trainer 27] Batch 5, Loss: 3.5275
2025-08-01 00:11:53,880 - INFO - [Trainer 1] Batch 9, Loss: 3.1506
2025-08-01 00:11:54,037 - INFO - [Trainer 35] Batch 5, Loss: 0.6522
2025-08-01 00:11:54,480 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:54,483 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:54,706 - INFO - [Trainer 7] Epoch 1 进度: 10/10 批次
2025-08-01 00:11:54,738 - INFO - [Trainer 37] Batch 5, Loss: 0.7688
2025-08-01 00:11:54,743 - INFO - [Trainer 19] Batch 5, Loss: 0.0000
2025-08-01 00:11:54,759 - INFO - [Trainer 7] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0026
2025-08-01 00:11:54,763 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:54,764 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 00:11:55,495 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:55,498 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:56,022 - INFO - [Trainer 18] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 00:11:56,023 - INFO - [Trainer 18] Epoch 1/5 完成 - 处理了 10 个批次, Loss: 2.0217, Accuracy: 44.33%
2025-08-01 00:11:56,023 - INFO - [Trainer 18] 开始第 2/5 个epoch
2025-08-01 00:11:56,106 - INFO - [Trainer 18] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.3334, y=[8, 1, 9, 1, 0]
2025-08-01 00:11:56,110 - INFO - [Trainer 18] Epoch 2 开始处理 10 个批次
2025-08-01 00:11:56,174 - INFO - [Trainer 18] Epoch 2 进度: 1/10 批次
2025-08-01 00:11:56,262 - INFO - [Trainer 18] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4069
2025-08-01 00:11:56,265 - INFO - [Trainer 18] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:56,266 - INFO - [Trainer 18] 标签样本: [1, 1, 1, 6, 9]
2025-08-01 00:11:56,503 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:56,514 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:56,756 - INFO - [Trainer 6] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 00:11:56,758 - INFO - [Trainer 6] Epoch 1/5 完成 - 处理了 10 个批次, Loss: 1.4306, Accuracy: 45.00%
2025-08-01 00:11:56,759 - INFO - [Trainer 6] 开始第 2/5 个epoch
2025-08-01 00:11:56,948 - INFO - [Trainer 6] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.1760, y=[7, 8, 7, 0, 0]
2025-08-01 00:11:56,951 - INFO - [Trainer 6] Epoch 2 开始处理 10 个批次
2025-08-01 00:11:56,980 - INFO - [Trainer 5] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 00:11:56,981 - INFO - [Trainer 5] Epoch 1/5 完成 - 处理了 10 个批次, Loss: 1.4812, Accuracy: 62.00%
2025-08-01 00:11:56,982 - INFO - [Trainer 5] 开始第 2/5 个epoch
2025-08-01 00:11:57,061 - INFO - [Trainer 7] Batch 9, Loss: 0.0000
2025-08-01 00:11:57,114 - INFO - [Trainer 6] Epoch 2 进度: 1/10 批次
2025-08-01 00:11:57,185 - INFO - [Trainer 36] Batch 5, Loss: 1.0341
2025-08-01 00:11:57,187 - INFO - [Trainer 5] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4786, y=[7, 7, 3, 7, 7]
2025-08-01 00:11:57,187 - INFO - [Trainer 6] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0766
2025-08-01 00:11:57,189 - INFO - [Trainer 5] Epoch 2 开始处理 10 个批次
2025-08-01 00:11:57,190 - INFO - [Trainer 6] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:57,190 - INFO - [Trainer 6] 标签样本: [8, 8, 7, 0, 7]
2025-08-01 00:11:57,229 - INFO - [Trainer 5] Epoch 2 进度: 1/10 批次
2025-08-01 00:11:57,282 - INFO - [Trainer 5] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3597
2025-08-01 00:11:57,283 - INFO - [Trainer 5] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:57,287 - INFO - [Trainer 5] 标签样本: [3, 7, 7, 7, 7]
2025-08-01 00:11:57,519 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:57,519 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:58,010 - INFO - [Trainer 25] Batch 5, Loss: 0.3860
2025-08-01 00:11:58,288 - INFO - [Trainer 18] Batch 0, Loss: 1.5313
2025-08-01 00:11:58,506 - INFO - [Trainer 1] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 00:11:58,509 - INFO - [Trainer 1] Epoch 1/5 完成 - 处理了 10 个批次, Loss: 2.1428, Accuracy: 32.00%
2025-08-01 00:11:58,512 - INFO - [Trainer 1] 开始第 2/5 个epoch
2025-08-01 00:11:58,537 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:58,541 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:58,649 - INFO - [Trainer 1] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.0764, y=[0, 5, 0, 5, 8]
2025-08-01 00:11:58,650 - INFO - [Trainer 1] Epoch 2 开始处理 10 个批次
2025-08-01 00:11:58,799 - INFO - [Trainer 1] Epoch 2 进度: 1/10 批次
2025-08-01 00:11:58,923 - INFO - [Trainer 1] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.0853
2025-08-01 00:11:58,925 - INFO - [Trainer 1] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:11:58,926 - INFO - [Trainer 1] 标签样本: [0, 5, 8, 5, 5]
2025-08-01 00:11:58,945 - INFO - [Trainer 11] Batch 5, Loss: 3.5387
2025-08-01 00:11:59,049 - INFO - [Trainer 12] Batch 5, Loss: 1.5033
2025-08-01 00:11:59,370 - INFO - [Trainer 13] Batch 5, Loss: 0.4588
2025-08-01 00:11:59,429 - INFO - [Trainer 6] Batch 0, Loss: 1.2904
2025-08-01 00:11:59,553 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:59,556 - INFO - [Trainer 5] Batch 0, Loss: 0.3536
2025-08-01 00:11:59,558 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:11:59,941 - INFO - [Trainer 31] Epoch 1 进度: 10/10 批次
2025-08-01 00:12:00,084 - INFO - [Trainer 31] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.2336
2025-08-01 00:12:00,088 - INFO - [Trainer 31] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:12:00,090 - INFO - [Trainer 31] 标签样本: [1, 1, 1, 1, 1]
2025-08-01 00:12:00,133 - INFO - [Trainer 2] Epoch 1 进度: 10/10 批次
2025-08-01 00:12:00,235 - INFO - [Trainer 2] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: 0.0663
2025-08-01 00:12:00,236 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:12:00,237 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 00:12:00,596 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:00,600 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:00,935 - INFO - [Trainer 4] Batch 5, Loss: 1.1051
2025-08-01 00:12:00,956 - INFO - [Trainer 20] Batch 5, Loss: 1.3370
2025-08-01 00:12:01,507 - INFO - [Trainer 1] Batch 0, Loss: 1.5375
2025-08-01 00:12:01,610 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:01,611 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:01,889 - INFO - [Trainer 30] Batch 5, Loss: 2.1388
2025-08-01 00:12:02,081 - INFO - [Trainer 39] Batch 5, Loss: 0.9412
2025-08-01 00:12:02,171 - INFO - [Trainer 31] Batch 9, Loss: 0.3661
2025-08-01 00:12:02,213 - INFO - [Trainer 38] Batch 5, Loss: 0.0000
2025-08-01 00:12:02,228 - INFO - [Trainer 7] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 00:12:02,231 - INFO - [Trainer 7] Epoch 1/5 完成 - 处理了 10 个批次, Loss: 0.5134, Accuracy: 94.67%
2025-08-01 00:12:02,234 - INFO - [Trainer 7] 开始第 2/5 个epoch
2025-08-01 00:12:02,321 - INFO - [Trainer 2] Batch 9, Loss: 0.0031
2025-08-01 00:12:02,331 - INFO - [Trainer 7] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4188, y=[2, 2, 2, 2, 2]
2025-08-01 00:12:02,332 - INFO - [Trainer 7] Epoch 2 开始处理 10 个批次
2025-08-01 00:12:02,470 - INFO - [Trainer 7] Epoch 2 进度: 1/10 批次
2025-08-01 00:12:02,588 - INFO - [Trainer 7] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1242
2025-08-01 00:12:02,591 - INFO - [Trainer 7] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:12:02,591 - INFO - [Trainer 7] 标签样本: [2, 2, 2, 2, 2]
2025-08-01 00:12:02,622 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:02,626 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:02,811 - INFO - [Trainer 28] Epoch 1 进度: 10/10 批次
2025-08-01 00:12:02,878 - INFO - [Trainer 28] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.3555
2025-08-01 00:12:02,911 - INFO - [Trainer 28] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:12:02,914 - INFO - [Trainer 28] 标签样本: [0, 7, 4, 7, 7]
2025-08-01 00:12:03,639 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:03,642 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:04,044 - INFO - [Trainer 32] Batch 5, Loss: 1.4483
2025-08-01 00:12:04,069 - INFO - [Trainer 16] Batch 5, Loss: 1.3724
2025-08-01 00:12:04,653 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:04,654 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:04,859 - INFO - [Trainer 7] Batch 0, Loss: 0.0000
2025-08-01 00:12:05,026 - INFO - [Trainer 14] Batch 5, Loss: 0.5068
2025-08-01 00:12:05,147 - INFO - [Trainer 28] Batch 9, Loss: 0.8965
2025-08-01 00:12:05,235 - INFO - [Trainer 44] Batch 5, Loss: 3.2513
2025-08-01 00:12:05,288 - INFO - [Trainer 15] Batch 5, Loss: 1.5023
2025-08-01 00:12:05,347 - INFO - [Trainer 43] Batch 5, Loss: 0.4298
2025-08-01 00:12:05,584 - INFO - [Trainer 34] Batch 5, Loss: 0.7851
2025-08-01 00:12:05,687 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:05,690 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:05,699 - INFO - [Trainer 40] Batch 5, Loss: 1.8456
2025-08-01 00:12:06,116 - INFO - [Trainer 41] Batch 5, Loss: 0.7377
2025-08-01 00:12:06,701 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:06,701 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:07,208 - INFO - [Trainer 31] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 00:12:07,209 - INFO - [Trainer 31] Epoch 1/5 完成 - 处理了 10 个批次, Loss: 0.8019, Accuracy: 72.00%
2025-08-01 00:12:07,210 - INFO - [Trainer 31] 开始第 2/5 个epoch
2025-08-01 00:12:07,390 - INFO - [Trainer 31] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.4719, y=[1, 1, 1, 5, 1]
2025-08-01 00:12:07,390 - INFO - [Trainer 31] Epoch 2 开始处理 10 个批次
2025-08-01 00:12:07,483 - INFO - [Trainer 31] Epoch 2 进度: 1/10 批次
2025-08-01 00:12:07,537 - INFO - [Trainer 21] Epoch 1 进度: 10/10 批次
2025-08-01 00:12:07,585 - INFO - [Trainer 31] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.4242
2025-08-01 00:12:07,586 - INFO - [Trainer 31] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:12:07,586 - INFO - [Trainer 31] 标签样本: [5, 1, 1, 1, 5]
2025-08-01 00:12:07,639 - INFO - [Trainer 21] Batch 9, x.shape: torch.Size([12, 3, 32, 32]), y.shape: torch.Size([12]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1035
2025-08-01 00:12:07,642 - INFO - [Trainer 21] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:12:07,645 - INFO - [Trainer 21] 标签样本: [7, 7, 8, 8, 7]
2025-08-01 00:12:07,712 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:07,756 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:07,796 - INFO - [Trainer 2] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 00:12:07,798 - INFO - [Trainer 2] Epoch 1/5 完成 - 处理了 10 个批次, Loss: 0.9492, Accuracy: 85.00%
2025-08-01 00:12:07,800 - INFO - [Trainer 2] 开始第 2/5 个epoch
2025-08-01 00:12:07,915 - INFO - [Trainer 2] 第一个批次数据: x.shape=torch.Size([32, 3, 32, 32]), y.shape=torch.Size([32]), x.min=-2.4291, x.max=2.7537, x.mean=-0.2248, y=[8, 8, 8, 8, 8]
2025-08-01 00:12:07,917 - INFO - [Trainer 2] Epoch 2 开始处理 10 个批次
2025-08-01 00:12:07,988 - INFO - [Trainer 2] Epoch 2 进度: 1/10 批次
2025-08-01 00:12:08,072 - INFO - [Trainer 2] Batch 0, x.shape: torch.Size([32, 3, 32, 32]), y.shape: torch.Size([32]), x.min: -2.4291, x.max: 2.7537, x.mean: -0.1436
2025-08-01 00:12:08,073 - INFO - [Trainer 2] 模型设备: cpu, 输入设备: cpu
2025-08-01 00:12:08,076 - INFO - [Trainer 2] 标签样本: [8, 8, 8, 8, 8]
2025-08-01 00:12:08,775 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:08,776 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:08,992 - INFO - [Trainer 45] Batch 5, Loss: 0.1093
2025-08-01 00:12:09,374 - INFO - [Trainer 42] Batch 5, Loss: 1.3825
2025-08-01 00:12:09,737 - INFO - [Trainer 31] Batch 0, Loss: 0.5426
2025-08-01 00:12:09,780 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:09,781 - INFO - [Trainer 21] Batch 9, Loss: 0.7142
2025-08-01 00:12:09,782 - INFO - 缓冲池为空，不触发聚合（缓冲池大小: 0）
2025-08-01 00:12:10,051 - INFO - [Trainer 48] Batch 5, Loss: 1.6119
2025-08-01 00:12:10,086 - INFO - [Trainer 2] Batch 0, Loss: 0.4022
2025-08-01 00:12:10,348 - INFO - 服务器启动完成
2025-08-01 00:12:10,618 - INFO - [Trainer 28] Epoch 1 批次循环完成，处理了 10 个批次
2025-08-01 00:12:10,637 - INFO - [Trainer 28] Epoch 1/5 完成 - 处理了 10 个批次, Loss: 1.5266, Accuracy: 45.67%
2025-08-01 00:12:10,662 - INFO - [Trainer 28] 开始第 2/5 个epoch
