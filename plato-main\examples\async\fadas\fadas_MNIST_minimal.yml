clients:
    # Type
    type: simple

    # 最小化客户端配置
    total_clients: 5
    per_round: 2

    # 简化测试配置
    do_test: false
    do_global_test: false

    # 关闭所有模拟
    speed_simulation: false
    sleep_simulation: false
    network_simulation: false

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8007
    ping_timeout: 36000
    ping_interval: 36000

    # 同步模式更稳定
    synchronous: true
    simulate_wall_time: false

    # 最小聚合要求
    minimum_clients_aggregated: 1
    staleness_bound: 5
    request_update: false

    # 简化路径
    checkpoint_path: models/mnist/minimal
    model_path: models/mnist/minimal

    random_seed: 1

    # FADAS参数
    beta1: 0.9
    beta2: 0.99
    tauc: 1
    eps: 0.00000001
    global_lr: 0.01

data:
    # 简化数据配置
    datasource: MNIST
    partition_size: 100  # 减少数据量
    sampler: iid  # 使用IID更稳定
    random_seed: 1
    testset_size: 50

trainer:
    # 最小化训练配置
    type: basic
    rounds: 3  # 只训练3轮
    max_concurrency: 1  # 单线程
    target_accuracy: 0.5  # 降低目标精度
    
    # 减少训练负载
    epochs: 1
    batch_size: 64
    optimizer: SGD
    # 使用简单的学习率调度器
    lr_scheduler: StepLR
    
    # 模型配置
    model_name: lenet5

algorithm:
    # 简单聚合
    type: fedavg

parameters:
    model:
        num_classes: 10
        # 移除 in_channels 参数

    optimizer:
        lr: 0.1  # 提高学习率加快收敛
        momentum: 0.9
        weight_decay: 0.0

    learning_rate:
        step_size: 2
        gamma: 0.5

results:
    result_path: results/mnist/minimal
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std, avg_staleness
