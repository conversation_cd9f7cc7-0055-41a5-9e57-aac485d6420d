clients:
    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 10

    # Should the clients compute test accuracy locally?
    do_test: false

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The simulation distribution
    simulation_distribution:
        # staleness is simulated from uniform distribution as mentioned in Section 5.2
        distribution: uniform
        low: 0
        high: 20

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: true

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 20

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8000

    # Should we operate in sychronous mode?
    synchronous: false

    # Should we simulate the wall-clock time on the server? Useful if max_concurrency is specified
    simulate_wall_time: true

    # What is the minimum number of clients that need to report before aggregation begins?
    minimum_clients_aggregated: 6

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 10

    random_seed: 1

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition
    partition_size: 5000

    # Whether clients have different partition size or not?
    variable_partition: true

    # IID or non-IID?
    sampler: noniid

    # The concentration parameter for the Dirichlet distribution
    concentration: 5

    # The random seed for sampling data
    random_seed: 1

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 200

    # The maximum number of clients running concurrently
    max_concurrency: 4

    # The target accuracy
    target_accuracy: 1.0

    # Number of epoches for local training in each communication round
    epochs: 1
    batch_size: 128
    optimizer: SGD
    lr_scheduler: LambdaLR

    # The machine learning model
    model_name: resnet_18

algorithm:
    # Aggregation algorithm
    type: fedavg

    # RL agent
    discrete_action_space: false
    n_features: 4
    max_action: 1
    min_action: -1
    max_episode: 2000
    alpha: 5 # controls the decreasing rate of the mapping function
    beta: 20 # coefficient used in reward function
    theta: 0.005 # threshold for stdev(pre_acc)
    base: 1000 # for exponential function in state normalization
    log_interval: 10
    mode: train # train/test
    pretrained: false
    pretrained_iter: 0
    test_step: 200

    # RL policy
    model_name: td3
    # reward discounted factor
    gamma: 0.99
    tau: 0.005
    learning_rate: 0.0003
    # Noise added to target policy during critic update
    policy_noise: 0.25
    # Range to clip target policy noise
    noise_clip: 0.5
    # Frequency of delayed policy updates
    policy_freq: 2
    # mini batch size
    batch_size: 64
    hidden_size: 256
    # steps sampling random actions
    start_steps: 8
    # replay memory
    replay_size: 10000
    replay_seed: 1234
    # whether use LSTM or FC nets
    recurrent_actor: true

parameters:
    optimizer:
        lr: 0.1
        momentum: 0.9
        weight_decay: 0.0001

    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    # Write the following parameter(s) into a CSV
    types: round, accuracy, elapsed_time, comm_time, round_time
