# include basic settings for the models and the learning process
general: !include base_path.yml

# clients settings
clients:
  type: simple # type
  total_clients: 50 # total number of clients
  per_round: 30 # number of clients selected in each round

  do_test: False # Should clients compute test accuracy locally?

# server settings
server:
  address: 127.0.0.1
  port: 8000

# dataset settings for the original dataset and the corresponding multimodal data processor
data:
  dataname: COCO2017
  datasource: COCO

  download_train_url: http://images.cocodataset.org/zips/train2017.zip
  download_test_url: http://images.cocodataset.org/zips/test2017.zip
  download_val_url: http://images.cocodataset.org/zips/val2017.zip
  download_annotation_url: http://images.cocodataset.org/annotations/annotations_trainval2017.zip

  downloader:
    num_workers: 4

  sampler: sample_quantity_noniid
  testset_sampler: sample_quantity_noniid
  modality_sampler: modality_iid

  per_client_classes_size: 6

  min_partition_size: 200

  # The random seed for sampling data
  random_seed: 1

# Train settings

# optimizer
optimizer: &optimizer
  type: SGD
  lr: 0.000125 # this lr is used for 8 gpus
  momentum: 0.9
  weight_decay: 0.0001

optimizer_config: &optimizer_config
  grad_clip:
    max_norm: 40
    norm_type: 2

# learning policy
lr_configs: &lr_config
  policy: step
  step:
    - 32
    - 48

  warmup: linear
  warmup_ratio: 0.1
  warmup_by_epoch: True
  warmup_iters: 16

total_epochs: &total_epochs 1000

log_config: &log_config
  interval: 20
  hooks:
    - type: TextLoggerHook
    - type: TensorboardLoggerHook

checkpoint_config: &checkpoint_config
  interval: 2

trainer:
  type: basic
  batch_size: 2
  optimizer: *optimizer
  optimizer_config: *optimizer_config
  learning_rate_config: *lr_config
  rounds: *total_epochs
  max_accuracy: 3
  target_accuracy: 0.67

  log_config: *log_config
  checkpoint_config: *checkpoint_config

evaluation:
  interval: 5
  metrics:
    - top_k_accuracy
    - mean_class_accuracy

algorithm:
  type: fedavg

# Aggregation algorithm
# The number of local aggregation rounds on edge servers before sending
# aggreagted weights to the central server

# runtime setting
runner_setting:
  dist_params:
    backend: nccl
    log_level: INFO
    work_dir: ./work_dirs/mmf # noqa: E501
    load_from: null
    resume_from: null
    workflow:
      - train
      - 1
    find_unused_parameters: True
