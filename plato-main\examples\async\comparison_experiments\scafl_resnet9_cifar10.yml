# SCAFL算法 - ResNet9 CIFAR10 对比试验配置
# 基于陈旧度感知的客户端自适应联邦学习算法

clients:
    # Type
    type: simple

    # The total number of clients
    total_clients: 100

    # The number of clients selected in each round
    per_round: 20

    # Maximum number of clients for aggregation
    max_aggregation_clients: 20

    # Should the clients compute test accuracy locally?
    do_test: true

    # Should the clients compute test accuracy with global model?
    do_test_with_global_model: false

    # Whether client heterogeneity should be simulated
    speed_simulation: true

    # The distribution of client speeds
    simulation_distribution:
        distribution: pareto
        alpha: 1

    # The maximum amount of time for clients to sleep after each epoch
    max_sleep_time: 5

    # Should clients really go to sleep, or should we just simulate the sleep times?
    sleep_simulation: false

    # If we are simulating client training times, what is the average training time?
    avg_training_time: 5

    random_seed: 1

server:
    address: 127.0.0.1
    port: 8005
    ping_timeout: 36000
    ping_interval: 36000

    # Should we simulate the wall-clock time on the server?
    simulate_wall_time: true

    # Should we operate in sychronous mode?
    synchronous: false

    # What is the staleness bound, beyond which the server should wait for stale clients?
    staleness_bound: 5

    # SCAFL specific parameters
    tau_max: 5  # Maximum staleness threshold
    V: 1.0      # Delay weight parameter

    # Aggregation strategy configuration
    aggregation_strategy: "scafl"
    
    # Buffer pool configuration
    buffer_size: 10
    min_clients_for_aggregation: 3

    # The paths for storing temporary checkpoints and models
    checkpoint_path: models/comparison/scafl
    model_path: models/comparison/scafl

    random_seed: 1

data:
    # The training and testing dataset
    datasource: CIFAR10

    # Number of samples in each partition
    partition_size: 300

    # IID or non-IID?
    sampler: noniid
    concentration: 0.5

    # The size of the testset on the server
    testset_size: 100

    # The random seed for sampling data
    random_seed: 1

    # Get the local test sampler to obtain the test dataset
    testset_sampler: noniid

trainer:
    # The type of the trainer
    type: basic

    # The maximum number of training rounds
    rounds: 400

    # The maximum number of clients running concurrently
    max_concurrency: 10

    # The target accuracy
    target_accuracy: 1

    # The machine learning model
    model_name: resnet_9

    # Number of epoches for local training in each communication round
    epochs: 5
    batch_size: 50
    optimizer: SGD
    lr_scheduler: LambdaLR

algorithm:
    # Aggregation algorithm
    type: fedavg

    # SCAFL specific parameters
    tau_max: 5  # Maximum staleness threshold
    V: 1.0      # Delay weight parameter

parameters:
    model:
        num_classes: 10
    optimizer:
        lr: 0.01
        momentum: 0.9
        weight_decay: 0.0001
    learning_rate:
        gamma: 0.1
        milestone_steps: 80ep,120ep

results:
    result_path: results/comparison/scafl

    # Write the following parameter(s) into a CSV
    types: round, elapsed_time, accuracy, global_accuracy, global_accuracy_std
