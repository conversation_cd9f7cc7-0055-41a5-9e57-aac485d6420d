#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SC_AFL（Staleness-aware Client-Adaptive Federated Learning）客户端实现。
"""

import asyncio
import logging
import os
import sys
import time
import traceback
import numpy as np
from types import SimpleNamespace

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../../"))
sys.path.insert(0, project_root)

import torch
from torch.utils.data import Dataset, Subset
import torchvision.transforms as transforms
from torchvision import datasets
import matplotlib
import matplotlib.pyplot as plt

from plato.config import Config
from plato.clients import simple
from plato.datasources import base
from plato.models import lenet5

# 统一的错误处理函数
def log_exception(context: str, exception: Exception):
    """统一的异常日志记录函数"""
    logging.error(f"{context}: {str(exception)}")
    logging.error(f"异常堆栈: {traceback.format_exc()}")

# 统一的动态模型创建函数
def create_dynamic_model_for_client(context: str = "", client_id: str = ""):
    """统一的客户端动态模型创建函数"""
    try:
        from dynamic_loader import DynamicModelLoader, ConfigurationManager
        config = Config()
        dataset_name, num_classes, in_channels = ConfigurationManager.get_dataset_info_from_config(config)
        model_name = ConfigurationManager.get_model_info_from_config(config)
        model = DynamicModelLoader.create_model(model_name, num_classes, in_channels)
        logging.info(f"[Client {client_id}] {context} 动态创建模型 {model_name}，输入通道数: {in_channels}, 类别数: {num_classes}")
        return model
    except Exception as e:
        logging.warning(f"[Client {client_id}] {context} 动态模型创建失败: {e}，回退到LeNet5")
        try:
            config = Config()
            in_channels = getattr(config.parameters.model, 'in_channels', 1)
            num_classes = getattr(config.parameters.model, 'num_classes', 10)
            model = lenet5.Model(num_classes=num_classes, in_channels=in_channels)
            logging.info(f"[Client {client_id}] {context} 回退创建LeNet5模型，输入通道数: {in_channels}, 类别数: {num_classes}")
            return model
        except Exception as fallback_e:
            log_exception(f"[Client {client_id}] {context} 回退模型创建也失败", fallback_e)
            raise

class ClientIDManager:
    """客户端ID管理器，负责分配和验证客户端ID"""
    
    _instance = None
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(ClientIDManager, cls).__new__(cls)
            cls._instance.initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化客户端ID管理器"""
        if self.initialized:
            return
            
        config = Config()
        self.total_clients = getattr(config.clients, 'total_clients', 10)
        self.id_start = getattr(config.clients, 'id_start', 1)
        self.id_assignment = getattr(config.clients, 'id_assignment', 'sequential')
        
        # 已分配的客户端ID集合
        self.assigned_ids = set()
        
        # 随机数生成器，用于随机分配ID
        self.prng = np.random.RandomState(seed=getattr(config.data, 'random_seed', 1))
        
        self.initialized = True
        logging.info(f"客户端ID管理器初始化完成，总客户端数: {self.total_clients}, ID起始值: {self.id_start}")
    
    def get_valid_id(self, client_id=None):
        """获取有效的客户端ID
        
        如果提供了client_id，则验证其有效性；
        如果未提供或无效，则根据配置的分配方式生成一个新的ID
        
        Args:
            client_id: 要验证的客户端ID
            
        Returns:
            int: 有效的客户端ID
        """
        # 如果提供了有效的客户端ID，直接返回
        if client_id is not None and isinstance(client_id, int) and self.is_valid_id(client_id):
            return client_id
            
        # 根据配置的分配方式生成新的ID
        if self.id_assignment == 'sequential':
            # 顺序分配：从起始ID开始，找到第一个未分配的ID
            for new_id in range(self.id_start, self.id_start + self.total_clients):
                if new_id not in self.assigned_ids:
                    self.assigned_ids.add(new_id)
                    logging.info(f"顺序分配客户端ID: {new_id}")
                    return new_id
        else:
            # 随机分配：在有效范围内随机选择一个未分配的ID
            available_ids = set(range(self.id_start, self.id_start + self.total_clients)) - self.assigned_ids
            if available_ids:
                new_id = self.prng.choice(list(available_ids))
                self.assigned_ids.add(new_id)
                logging.info(f"随机分配客户端ID: {new_id}")
                return new_id
        
        # 如果所有ID都已分配，则映射到有效范围内
        if client_id is not None:
            mapped_id = self.id_start + (client_id - self.id_start) % self.total_clients
            logging.warning(f"所有客户端ID已分配，将ID {client_id} 映射到 {mapped_id}")
            return mapped_id
        else:
            # 如果没有提供ID且所有ID都已分配，则使用起始ID
            logging.warning(f"所有客户端ID已分配，使用起始ID: {self.id_start}")
            return self.id_start
    
    def is_valid_id(self, client_id):
        """检查客户端ID是否有效
        
        Args:
            client_id: 要检查的客户端ID
            
        Returns:
            bool: 如果ID有效则返回True，否则返回False
        """
        if not isinstance(client_id, int):
            return False
            
        # 检查ID是否在有效范围内
        return self.id_start <= client_id < self.id_start + self.total_clients
    
    def get_mapped_id(self, client_id):
        """将客户端ID映射到有效范围内
        
        Args:
            client_id: 要映射的客户端ID
            
        Returns:
            int: 映射后的客户端ID
        """
        if not isinstance(client_id, int):
            try:
                client_id = int(client_id)
            except (ValueError, TypeError):
                return self.get_valid_id(None)
                
        mapped_id = self.id_start + ((client_id - self.id_start) % self.total_clients)
        if mapped_id != client_id:
            logging.warning(f"客户端ID {client_id} 超出范围，映射到 {mapped_id}")
        return mapped_id
    
    def release_id(self, client_id):
        """释放一个客户端ID，使其可以被重新分配
        
        Args:
            client_id: 要释放的客户端ID
        """
        if client_id in self.assigned_ids:
            self.assigned_ids.remove(client_id)
            logging.info(f"释放客户端ID: {client_id}")

"""
SCAFL客户端类，扩展自基础客户端
实现异步训练和基于过时度的加权聚合功能
"""

import torch
import logging
from plato.clients import simple
from sc_afl_algorithm import Algorithm
from sc_afl_trainer import Trainer
from plato.datasources import base
from plato.datasources import mnist
from plato.models import registry as models
import asyncio
from plato.config import Config
from plato.models import lenet5
import numpy as np
from torch.utils.data import Subset
import time
from types import SimpleNamespace
import copy
from torchvision import datasets, transforms
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，避免需要图形界面
import matplotlib.pyplot as plt
import os
import sys

def custom_partition(dataset, num_clients, client_id, config=None):
    """
    自定义数据划分函数，与FADAS保持一致的Dirichlet分布采样方式

    Args:
        dataset: 完整的数据集
        num_clients: 客户端总数
        client_id: 当前客户端ID (已经由ClientIDManager映射到有效范围)
        config: 配置对象

    Returns:
        Subset: 当前客户端的数据子集
    """
    try:
        # 基本验证
        if dataset is None:
            logging.error(f"客户端{client_id}：数据集为None")
            return None

        if not hasattr(dataset, '__len__'):
            logging.error(f"客户端{client_id}：数据集没有__len__方法")
            return None

        if len(dataset) == 0:
            logging.error(f"客户端{client_id}：数据集为空")
            return None

        if client_id is None:
            logging.error("客户端ID为None")
            return None

        if num_clients <= 0:
            logging.error(f"客户端{client_id}：客户端总数无效: {num_clients}")
            return None

        # 从配置中获取参数，如果没有则使用默认值
        if config is None:
            config = Config()

        # 获取随机种子和浓度参数（与FADAS保持一致）
        random_seed = getattr(config.data, 'random_seed', 1)
        concentration = getattr(config.data, 'concentration', 0.1)
        partition_size = getattr(config.data, 'partition_size', 300)

        # 设置客户端特定的随机种子（与FADAS的Dirichlet sampler保持一致）
        np.random.seed(random_seed * int(client_id))

        logging.info(f"客户端{client_id}：开始划分数据集，参数：partition_size={partition_size}, "
                    f"concentration={concentration}, random_seed={random_seed}")
        
        # 提取数据集标签
        try:
            labels = []
            for i in range(len(dataset)):
                try:
                    _, label = dataset[i]
                    labels.append(label)
                except Exception as e:
                    logging.error(f"客户端{client_id}：提取第{i}个样本标签时出错: {str(e)}")
                    continue

            labels = np.array(labels)

            if len(labels) == 0:
                logging.error(f"客户端{client_id}：未能提取任何标签")
                indices = np.random.choice(range(len(dataset)), min(100, len(dataset)), replace=False)
                return Subset(dataset, indices)

            logging.info(f"客户端{client_id}：成功提取{len(labels)}个样本标签")
        except Exception as e:
            logging.error(f"客户端{client_id}：提取数据集标签时出错: {str(e)}")
            import traceback
            logging.error(f"客户端{client_id}：异常堆栈: {traceback.format_exc()}")
            indices = np.random.choice(range(len(dataset)), min(100, len(dataset)), replace=False)
            return Subset(dataset, indices)

        # 获取类别信息
        unique_labels = np.unique(labels)
        num_classes = len(unique_labels)
        logging.info(f"客户端{client_id}：数据集包含{num_classes}个类别")

        # 使用与FADAS相同的Dirichlet分布生成类别比例
        target_proportions = np.random.dirichlet(np.repeat(concentration, num_classes))

        # 如果生成的比例为空，随机选择一个类别
        if np.isnan(np.sum(target_proportions)):
            target_proportions = np.repeat(0, num_classes)
            target_proportions[np.random.randint(0, num_classes)] = 1

        logging.info(f"客户端{client_id}：生成的类别比例: {target_proportions}")

        # 创建样本权重数组（与FADAS的WeightedRandomSampler逻辑一致）
        sample_weights = np.zeros(len(labels))
        for i, label in enumerate(labels):
            # 找到标签在unique_labels中的索引
            label_idx = np.where(unique_labels == label)[0][0]
            sample_weights[i] = target_proportions[label_idx]

        # 使用权重采样（模拟WeightedRandomSampler的行为）
        # 确保分区大小不超过数据集大小
        actual_partition_size = min(partition_size, len(dataset))

        # 归一化权重
        if np.sum(sample_weights) > 0:
            sample_weights = sample_weights / np.sum(sample_weights)
        else:
            # 如果权重全为0，使用均匀分布
            sample_weights = np.ones(len(labels)) / len(labels)

        # 根据权重进行采样（不重复采样）
        try:
            client_indices = np.random.choice(
                range(len(dataset)),
                size=actual_partition_size,
                replace=False,
                p=sample_weights
            )
        except ValueError:
            # 如果权重采样失败，使用均匀随机采样
            logging.warning(f"客户端{client_id}：权重采样失败，使用均匀随机采样")
            client_indices = np.random.choice(
                range(len(dataset)),
                size=actual_partition_size,
                replace=False
            )

        # 输出调试信息
        selected_labels = labels[client_indices]
        label_counts = {label: np.sum(selected_labels == label) for label in unique_labels}
        logging.info(f"客户端{client_id}：分配到的样本数: {len(client_indices)}")
        logging.info(f"客户端{client_id}：每个类别的样本数: {label_counts}")

        return Subset(dataset, client_indices)
        
    except Exception as e:
        logging.error(f"客户端{client_id}：数据划分时出错: {str(e)}")
        import traceback
        logging.error(f"客户端{client_id}：异常堆栈: {traceback.format_exc()}")
        # 返回一个小的随机子集，避免程序崩溃
        try:
            indices = np.random.choice(range(len(dataset)), min(100, len(dataset)), replace=False)
            return Subset(dataset, indices)
        except Exception as e2:
            logging.error(f"客户端{client_id}：创建随机子集时出错: {str(e2)}")
            # 如果连随机子集都无法创建，返回None
            return None

def visualize_data_distribution(client_id, dataset, save_dir='data_distribution'):
    """
    可视化客户端数据分布
    
    参数:
        client_id: 客户端ID
        dataset: 客户端数据集
        save_dir: 保存目录
    """
    try:
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 提取标签
        if hasattr(dataset, 'targets'):
            labels = dataset.targets
        else:
            # 如果是Subset类型，需要从原始数据集提取标签
            if isinstance(dataset, Subset):
                if hasattr(dataset.dataset, 'targets'):
                    all_labels = dataset.dataset.targets
                    indices = dataset.indices
                    labels = [all_labels[i] for i in indices]
                else:
                    # 尝试从数据集中提取标签
                    labels = []
                    for i in range(len(dataset)):
                        _, label = dataset[i]
                        labels.append(label)
            else:
                # 尝试从数据集中提取标签
                labels = []
                for i in range(len(dataset)):
                    _, label = dataset[i]
                    labels.append(label)
        
        # 转换为numpy数组
        if not isinstance(labels, np.ndarray):
            labels = np.array(labels)
        
        # 计算每个类别的样本数
        unique_labels = np.unique(labels)
        class_counts = [np.sum(labels == label) for label in unique_labels]
        
        # 绘制类别分布柱状图
        plt.figure(figsize=(10, 6))
        plt.bar(unique_labels, class_counts)
        
        # 使用英文标签避免中文字体问题
        plt.xlabel('Class')
        plt.ylabel('Number of Samples')
        plt.title(f'Class Distribution for Client {client_id} (Total: {len(dataset)} samples)')
        plt.xticks(unique_labels)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        
        # 保存图像
        save_path = os.path.join(save_dir, f'client_{client_id}_distribution.png')
        plt.savefig(save_path)
        plt.close()
        
        logging.info(f"Client {client_id} data distribution saved to {save_path}")
        
        # 保存数据分布的文本描述
        text_path = os.path.join(save_dir, f'client_{client_id}_distribution.txt')
        with open(text_path, 'w') as f:
            f.write(f"Client {client_id} Data Distribution\n")
            f.write(f"Total samples: {len(dataset)}\n")
            f.write("Class distribution:\n")
            for label, count in zip(unique_labels, class_counts):
                f.write(f"  Class {label}: {count} samples ({count/len(dataset)*100:.2f}%)\n")
        
        return True
    except Exception as e:
        logging.error(f"Error visualizing data distribution for client {client_id}: {str(e)}")
        return False

def setup_matplotlib_chinese():
    """设置matplotlib支持中文显示"""
    try:
        # 尝试使用不同的中文字体，按优先级排序
        font_names = [
            'SimHei',        # Windows中文黑体
            'Microsoft YaHei', # Windows微软雅黑
            'WenQuanYi Micro Hei', # Linux文泉驿微米黑
            'Hiragino Sans GB',  # macOS冬青黑体
            'Noto Sans CJK SC', # Google Noto Sans中文
            'Noto Sans SC',    # Google Noto Sans简体中文
            'Source Han Sans CN', # Adobe思源黑体
            'PingFang SC',     # macOS苹方
            'STHeiti'          # macOS华文黑体
        ]
        
        # 检查系统中可用的字体
        from matplotlib.font_manager import FontProperties, findfont, FontManager
        font_manager = FontManager()
        font_files = font_manager.ttflist
        available_fonts = [f.name for f in font_files]
        
        # 找到第一个可用的中文字体
        chinese_font = None
        for font in font_names:
            if font in available_fonts:
                chinese_font = font
                break
        
        if chinese_font:
            # 设置matplotlib参数
            plt.rcParams['font.family'] = ['sans-serif']
            plt.rcParams['font.sans-serif'] = [chinese_font]
            plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
            logging.info(f"成功设置matplotlib中文字体: {chinese_font}")
            return True
        else:
            # 如果没有找到中文字体，使用默认字体并记录警告
            logging.warning("未找到可用的中文字体，将使用默认字体，中文可能无法正确显示")
            return False
    except Exception as e:
        logging.error(f"设置matplotlib中文字体时出错: {str(e)}")
        return False

# 在导入模块时尝试设置中文字体
setup_matplotlib_chinese()

class Client(simple.Client):
    def __init__(self, client_id=None, model=None, algorithm=None, trainer=None):
        """初始化客户端
        
        Args:
            client_id: 客户端ID
            model: 模型实例
            algorithm: 算法实例
            trainer: 训练器实例
        """
        # 使用ClientIDManager获取有效的客户端ID
        id_manager = ClientIDManager()
        self.client_id = id_manager.get_valid_id(client_id)
        logging.info(f"初始化客户端, ID: {self.client_id}")
        
        # 初始化服务器引用为None
        self.server = None
        self.model_received = False
        self.model_version = 0
        self.last_round = 0  # 添加last_round属性初始化
        self.upload_trial_count = 0
        self.data_loaded = False
        
        # 初始化训练历史记录
        self.training_history = []
        self.accuracy_history = []
        self.loss_history = []
        
        # 导入必要的类
        from sc_afl_trainer import Trainer as SCTrainer
        from sc_afl_algorithm import Algorithm as SCAlgorithm
        
        # 确保模型已初始化
        if model is None:
            try:
                # 使用统一的动态模型创建函数
                model = create_dynamic_model_for_client("初始化阶段", self.client_id)
            except Exception as e:
                logging.error(f"[Client {self.client_id}] 创建模型时出错: {str(e)}")
                import traceback
                logging.error(f"[Client {self.client_id}] 异常堆栈: {traceback.format_exc()}")
                model = None  # 确保model不为None，即使创建失败
        
        # 创建数据源
        datasource = base.DataSource()
        datasource.trainset = []
        
        # 确保训练器已初始化
        if trainer is None:
            try:
                trainer = SCTrainer(model=model, client_id=self.client_id)
                logging.info(f"[Client {self.client_id}] 创建新训练器")
            except Exception as e:
                logging.error(f"[Client {self.client_id}] 创建训练器时出错: {str(e)}")
                import traceback
                logging.error(f"[Client {self.client_id}] 异常堆栈: {traceback.format_exc()}")
                # 如果创建失败，尝试使用最小化参数创建
                try:
                    trainer = SCTrainer(client_id=self.client_id)
                    logging.info(f"[Client {self.client_id}] 使用最小化参数创建训练器")
                except:
                    trainer = None  # 确保trainer不为None，即使创建失败
        
        # 确保算法已初始化
        if algorithm is None:
            try:
                algorithm = SCAlgorithm(trainer=trainer)
                logging.info(f"[Client {self.client_id}] 创建新算法")
            except Exception as e:
                logging.error(f"[Client {self.client_id}] 创建算法时出错: {str(e)}")
                import traceback
                logging.error(f"[Client {self.client_id}] 异常堆栈: {traceback.format_exc()}")
                # 如果创建失败，尝试使用最小化参数创建
                try:
                    algorithm = SCAlgorithm()
                    logging.info(f"[Client {self.client_id}] 使用最小化参数创建算法")
                except:
                    algorithm = None
        
        # 确保训练器中有client_id
        if trainer is not None and not hasattr(trainer, 'client_id'):
            trainer.client_id = self.client_id
            
        # 确保算法中有client_id
        if algorithm is not None and hasattr(algorithm, 'set_client_id'):
            algorithm.set_client_id(self.client_id)
        
        # 调用父类初始化
        try:
            super().__init__(model=model, datasource=datasource, algorithm=algorithm, trainer=trainer)
            logging.info(f"[Client {self.client_id}] 父类初始化完成")
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 父类初始化失败: {str(e)}")
            import traceback
            logging.error(f"[Client {self.client_id}] 异常堆栈: {traceback.format_exc()}")
            # 如果父类初始化失败，手动设置关键属性
            self.model = model
            self.datasource = datasource
            self.algorithm = algorithm
            self.trainer = trainer
            
        # 再次确保关键组件不为None
        if self.model is None:
            try:
                self.model = create_dynamic_model_for_client("父类初始化后", self.client_id)
            except Exception as e:
                log_exception(f"[Client {self.client_id}] 父类初始化后创建模型失败", e)
            
        if self.trainer is None:
            try:
                from sc_afl_trainer import Trainer
                self.trainer = Trainer(model=self.model, client_id=self.client_id)
                logging.info(f"[Client {self.client_id}] 父类初始化后创建新训练器")
            except Exception as e:
                logging.error(f"[Client {self.client_id}] 父类初始化后创建训练器失败: {str(e)}")
            
        if self.algorithm is None:
            try:
                from sc_afl_algorithm import Algorithm
                self.algorithm = Algorithm(trainer=self.trainer)
                logging.info(f"[Client {self.client_id}] 父类初始化后创建新算法")
            except Exception as e:
                logging.error(f"[Client {self.client_id}] 父类初始化后创建算法失败: {str(e)}")
        
        # 确保训练器中有正确的client_id
        if hasattr(self.trainer, 'client_id') and self.trainer.client_id != self.client_id:
            logging.error(f"客户端ID不匹配: 预期 {self.client_id}，实际 {self.trainer.client_id}")
            self.trainer.client_id = self.client_id  # 修正client_id
        
        # 立即加载数据
        self.data_loaded = False
        try:
            if self.load_data():
                logging.info(f"[Client {self.client_id}] 初始化时成功加载数据")
            else:
                logging.error(f"[Client {self.client_id}] 初始化时数据加载失败")
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 初始化时数据加载异常: {str(e)}")
            import traceback
            logging.error(f"[Client {self.client_id}] 数据加载异常堆栈: {traceback.format_exc()}")

        # 简单验证初始化结果（不进行修复）
        self.simple_validate_initialization()

    def simple_validate_initialization(self):
        """简单验证客户端初始化状态（只记录，不修复）"""
        try:
            issues = []
            if self.client_id is None:
                issues.append("客户端ID为None")
            if not hasattr(self, 'model') or self.model is None:
                issues.append("模型为None")
            if not hasattr(self, 'trainer') or self.trainer is None:
                issues.append("训练器为None")
            if not hasattr(self, 'algorithm') or self.algorithm is None:
                issues.append("算法为None")
            if not hasattr(self, 'datasource') or self.datasource is None:
                issues.append("数据源为None")

            if issues:
                logging.warning(f"[客户端 {self.client_id}] 初始化问题: {', '.join(issues)}")
            else:
                logging.info(f"[客户端 {self.client_id}] 初始化验证通过")

        except Exception as e:
            logging.error(f"[客户端 {self.client_id}] 简单验证过程中出错: {str(e)}")

    def load_data(self):
        """加载客户端数据
        
        此方法负责：
        1. 初始化/验证数据源
        2. 加载MNIST数据集
        3. 按照客户端ID进行数据划分
        4. 可选地可视化数据分布
        """
        try:
            if self.data_loaded:
                logging.info(f"[Client {self.client_id}] 数据已经加载，跳过")
                return True
                
            logging.info(f"[Client {self.client_id}] 开始加载数据")
            
            # 确保client_id有效
            if self.client_id is None:
                # 使用ID管理器获取有效的客户端ID
                id_manager = ClientIDManager()
                self.client_id = id_manager.get_valid_id(None)
                logging.warning(f"[Client Load Data] 客户端ID为None，已分配新ID: {self.client_id}")
                import traceback
                stack = traceback.format_stack()
                logging.warning(f"[Client Load Data] 调用堆栈: {stack}")
            
            # 确保datasource存在
            if self.datasource is None:
                self.datasource = base.DataSource()
                logging.warning(f"[Client {self.client_id}] 数据源为None，已创建新数据源")
            
            # 初始化trainset为空列表（而非None）
            if not hasattr(self.datasource, 'trainset'):
                self.datasource.trainset = []
                logging.warning(f"[Client {self.client_id}] 数据源没有trainset属性，已添加")
            elif self.datasource.trainset is None:
                self.datasource.trainset = []
                logging.warning(f"[Client {self.client_id}] 数据源trainset为None，已设置为空列表")
                
            # 动态加载数据集
            try:
                config = Config()
                data_path = getattr(config.parameters, 'data_path', './data')
                datasource_name = getattr(config.data, 'datasource', 'MNIST')

                # 使用动态加载器
                try:
                    from dynamic_loader import DynamicDataLoader
                    full_trainset = DynamicDataLoader.load_dataset(
                        datasource_name, data_path, is_train=True, download=True
                    )
                    logging.info(f"[Client {self.client_id}] 成功动态加载{datasource_name}数据集，大小: {len(full_trainset)}")
                except Exception as dynamic_error:
                    logging.warning(f"[Client {self.client_id}] 动态加载失败: {dynamic_error}，回退到MNIST")
                    # 回退到MNIST
                    train_transform = transforms.Compose([
                        transforms.ToTensor(),
                        transforms.Normalize((0.1307,), (0.3081,))
                    ])
                    full_trainset = datasets.MNIST(
                        root=data_path, train=True, download=True, transform=train_transform
                    )
                    logging.info(f"[Client {self.client_id}] 回退加载MNIST数据集，大小: {len(full_trainset)}")
            
                num_clients = getattr(config.clients, 'total_clients', 10)
                concentration = getattr(config.data, 'concentration', 0.1)
                
                # 使用标准的Plato Dirichlet sampler（与FADAS保持一致）
                logging.info(f"[Client {self.client_id}] 开始使用标准Dirichlet sampler划分数据集，总客户端数: {num_clients}, 浓度参数: {concentration}")

                # 使用ID管理器获取映射后的客户端ID用于数据划分
                id_manager = ClientIDManager()
                mapped_client_id = id_manager.get_mapped_id(self.client_id)

                # 创建标准的Dirichlet sampler（与FADAS相同的逻辑）
                from plato.samplers.dirichlet import Sampler as DirichletSampler

                # 创建一个临时的数据源对象来使用Dirichlet sampler
                class TempDataSource:
                    def __init__(self, dataset):
                        self.dataset = dataset
                        self._targets = None
                        self._classes = None

                    def targets(self):
                        """返回所有样本的标签"""
                        if self._targets is None:
                            targets = []
                            # 对于不同数据集，处理targets属性
                            if hasattr(self.dataset, 'targets'):
                                # 检查targets是否已经是列表
                                if isinstance(self.dataset.targets, list):
                                    targets = self.dataset.targets
                                else:
                                    # 如果是tensor，转换为列表
                                    targets = self.dataset.targets.tolist()
                            else:
                                # 其他数据集，逐个提取标签
                                for i in range(len(self.dataset)):
                                    try:
                                        # 临时禁用transform来提高性能
                                        original_transform = getattr(self.dataset, 'transform', None)
                                        self.dataset.transform = None
                                        _, label = self.dataset[i]
                                        targets.append(label)
                                        # 恢复transform
                                        self.dataset.transform = original_transform
                                    except Exception as e:
                                        logging.error(f"提取第{i}个样本标签时出错: {e}")
                                        continue
                            self._targets = targets
                        return self._targets

                    def classes(self):
                        """返回所有类别"""
                        if self._classes is None:
                            targets = self.targets()
                            self._classes = list(set(targets))
                        return self._classes

                temp_datasource = TempDataSource(full_trainset)

                # 创建Dirichlet sampler
                sampler = DirichletSampler(temp_datasource, mapped_client_id, testing=False)

                # 获取采样器
                subset_sampler = sampler.get()

                # 创建训练集子集
                from torch.utils.data import Subset
                trainset = Subset(full_trainset, list(subset_sampler.indices))
                
                if trainset is not None and hasattr(trainset, '__len__') and len(trainset) > 0:
                    # 设置数据源的训练集
                    self.datasource.trainset = trainset
                    logging.info(f"[Client {self.client_id}] 成功划分数据集，分配到 {len(trainset)} 个样本")
                    self.data_loaded = True
                    
                    # 可视化数据分布（如果配置中启用）
                    if getattr(config.data, 'visualize_distribution', False):
                        try:
                            visualize_data_distribution(self.client_id, self.datasource.trainset)
                        except Exception as e:
                            logging.error(f"[Client {self.client_id}] 可视化数据分布失败: {str(e)}")
                            
                    return True
                else:
                    logging.error(f"[Client {self.client_id}] 划分数据集失败，结果为None或空")
                    return False
                    
            except Exception as e:
                logging.error(f"[Client {self.client_id}] 加载或划分数据集时出错: {str(e)}")
                import traceback
                logging.error(f"[Client {self.client_id}] 数据加载异常堆栈: {traceback.format_exc()}")
                return False
                
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 加载数据时出错: {str(e)}")
            import traceback
            logging.error(f"[Client {self.client_id}] 异常堆栈: {traceback.format_exc()}")
            return False
            
    # validate_initialization函数已删除，避免代码冗余
    # 该函数未被使用且包含复杂的递归逻辑

    def configure(self):
        """配置客户端"""
        try:
            # 确保模型已经初始化
            if self.model is None:
                config = Config()
                try:
                    self.model = create_dynamic_model_for_client("配置阶段", self.client_id)
                except Exception as e:
                    log_exception(f"[Client {self.client_id}] 配置阶段模型创建失败", e)
                
            # 确保训练器已经初始化
            if self.trainer is None:
                from sc_afl_trainer import Trainer
                self.trainer = Trainer(model=self.model, client_id=self.client_id)
                logging.info(f"[Client {self.client_id}] 配置阶段创建新训练器")
                
            # 确保算法已经初始化
            if self.algorithm is None:
                from sc_afl_algorithm import Algorithm
                self.algorithm = Algorithm(trainer=self.trainer)
                logging.info(f"[Client {self.client_id}] 配置阶段创建新算法")
                # 确保训练器中的client_id正确
                if hasattr(self.trainer, 'client_id') and self.trainer.client_id != self.client_id:
                    logging.error(f"客户端ID不匹配: 预期 {self.client_id}，实际 {self.trainer.client_id}")
                    self.trainer.client_id = self.client_id  # 修正client_id
                
            # 确保数据源已经初始化
            if self.datasource is None:
                logging.error(f"[Client {self.client_id}] 数据源未初始化")
                raise ValueError("数据源未初始化")
                
            # 验证数据集
            self.validate_data(self.datasource.trainset)
            
            # 验证模型
            self.validate_model()
            
            logging.info(f"[Client {self.client_id}] 配置完成")
            
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 配置失败: {str(e)}")
            raise

    def validate_data(self, trainset):
        """验证数据集有效性
        
        Args:
            trainset: 训练数据集
            
        Returns:
            bool: 数据是否有效
        """
        if trainset is None:
            logging.error(f"[Client {self.client_id}] 训练集为None")
            return False
            
        if not hasattr(trainset, '__len__'):
            logging.error(f"[Client {self.client_id}] 训练集没有__len__方法")
            return False
            
        if len(trainset) == 0:
            logging.warning(f"[Client {self.client_id}] 训练集为空")
            return False
        
        # 检查数据格式
        try:
            sample = trainset[0]
            logging.debug(f"[Client {self.client_id}] 训练样本类型: {type(sample)}, 值: {sample}")
            
            if not isinstance(sample, (tuple, list)):
                logging.error(f"[Client {self.client_id}] 训练样本不是元组或列表: {type(sample)}")
                return False
                
            if len(sample) < 2:
                logging.error(f"[Client {self.client_id}] 训练样本元素数量不足: {len(sample)}")
                return False
                
            # 检查数据和标签
            data, label = sample[0], sample[1]
            logging.debug(f"[Client {self.client_id}] 数据类型: {type(data)}, 标签类型: {type(label)}")
            
            logging.info(f"[Client {self.client_id}] 训练集验证成功，大小: {len(trainset)}")
            return True
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 访问训练样本时出错: {str(e)}")
            return False

    def validate_model(self):
        """验证模型有效性
        
        Returns:
            bool: 模型是否有效
        """
        if self.model is None:
            logging.error(f"[Client {self.client_id}] 模型未初始化")
            return False
        
        # 检查模型是否可用
        try:
            self.model.eval()
            for param in self.model.parameters():
                if torch.isnan(param).any() or torch.isinf(param).any():
                    logging.error(f"[Client {self.client_id}] 模型参数包含NaN或Inf值")
                    return False
            logging.info(f"[Client {self.client_id}] 模型验证成功")
            return True
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 模型验证失败: {str(e)}")
            return False

    async def receive_model(self, model_weights=None):
        """接收模型
        
        Args:
            model_weights: 可选，直接传入的模型权重
            
        Returns:
            bool: 接收模型是否成功
        """
        try:
            # 记录接收开始时间
            self.training_start_time = time.time()
            
            # 如果直接传入了模型权重，使用传入的权重
            if model_weights is not None:
                logging.info(f"[Client {self.client_id}] 收到直接传入的模型权重")
                return await self._process_received_weights(model_weights)
            
            # 否则从服务器请求权重
            # 如果服务器引用未设置，则无法接收模型
            if self.server is None:
                logging.error(f"[Client {self.client_id}] 服务器引用未设置，无法接收模型")
                return False
                
            # 使用安全的配置获取方式
            config = Config()
            timeout = getattr(config, 'client_timeout', 60)  # 默认60秒超时
            
            # 使用 wait_for 替代 timeout 装饰器
            await asyncio.wait_for(
                self._receive_model_impl(),
                timeout=timeout
            )
            return True
            
        except asyncio.TimeoutError:
            logging.error(f"[Client {self.client_id}] 接收模型超时")
            return False
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 接收模型错误: {str(e)}")
            return False
            
    async def _process_received_weights(self, model_weights):
        """处理接收到的模型权重
        
        Args:
            model_weights: 模型权重
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 检查算法对象是否已初始化
            if self.algorithm is None:
                logging.error(f"[Client {self.client_id}] 算法对象未初始化")
                # 尝试初始化算法对象
                if self.model is None:
                    config = Config()
                    try:
                        self.model = create_dynamic_model_for_client("自动创建", self.client_id)
                    except Exception as e:
                        log_exception(f"[Client {self.client_id}] 自动模型创建失败", e)
                
                # 确保训练器已初始化
                if self.trainer is None:
                    from sc_afl_trainer import Trainer
                    self.trainer = Trainer(model=self.model, client_id=self.client_id)
                    logging.info(f"[Client {self.client_id}] 自动创建训练器")
                    
                # 创建算法对象
                from sc_afl_algorithm import Algorithm
                self.algorithm = Algorithm(trainer=self.trainer)
                logging.info(f"[Client {self.client_id}] 自动创建算法对象")
                # 确保训练器中的client_id正确
                if hasattr(self.trainer, 'client_id') and self.trainer.client_id != self.client_id:
                    logging.error(f"客户端ID不匹配: 预期 {self.client_id}，实际 {self.trainer.client_id}")
                    self.trainer.client_id = self.client_id  # 修正client_id
                    
            # 处理通道数不匹配问题
            model_in_channels = self._get_model_in_channels()
            weights_in_channels = self._get_weights_in_channels(model_weights)
            
            if model_in_channels != weights_in_channels:
                # 处理通道数不匹配问题
                model_weights = self._handle_channel_mismatch(model_weights, model_in_channels, weights_in_channels)
            
            # 确保权重在与模型相同的设备上
            if hasattr(self.trainer, 'device'):
                device = self.trainer.device
                weight_device = None
                
                # 获取权重设备
                for key, value in model_weights.items():
                    if isinstance(value, torch.Tensor):
                        weight_device = value.device
                        break
                
                if weight_device is not None and device != weight_device:
                    logging.info(f"[Client {self.client_id}] 将权重从设备 {weight_device} 移至 {device}")
                    for key in model_weights:
                        if isinstance(model_weights[key], torch.Tensor):
                            model_weights[key] = model_weights[key].to(device)
            
            # 加载权重到模型
            success = self.algorithm.load_weights(model_weights)
            
            if success:
                logging.info(f"[Client {self.client_id}] 成功接收并加载模型")
                return True
            else:
                logging.error(f"[Client {self.client_id}] 加载模型权重失败")
                return False
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 处理模型权重时出错: {str(e)}")
            import traceback
            logging.error(f"[Client {self.client_id}] 异常堆栈: {traceback.format_exc()}")
            return False
            
    async def _receive_model_impl(self):
        """实际的模型接收逻辑"""
        if self.server is None:
            raise RuntimeError("服务器引用未设置")
            
        # 从服务器接收模型
        try:
            model_response = await self.server.send_model_to_client(self.client_id)
            
            if model_response is None:
                logging.error(f"[Client {self.client_id}] 从服务器接收到的响应为None")
                return False
            # 处理接收到的权重
            return await self._process_received_weights(model_response)
                
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 接收模型过程中出错: {str(e)}")
            import traceback
            logging.error(f"[Client {self.client_id}] 异常堆栈: {traceback.format_exc()}")
            return False

    async def train(self):
        """训练模型
        
        Returns:
            tuple: (训练报告, 模型权重)
        """
        try:
            # 检查客户端ID是否有效
            if self.client_id is None:
                # 使用ID管理器获取有效的客户端ID
                id_manager = ClientIDManager()
                self.client_id = id_manager.get_valid_id(None)
                logging.warning(f"[Client Train] 客户端ID为None，已分配新ID: {self.client_id}")
                import traceback
                stack = traceback.format_stack()
                logging.warning(f"[Client Train] 调用堆栈: {stack}")
                
            # 确保client_id是整数
            try:
                self.client_id = int(self.client_id)
            except (ValueError, TypeError):
                logging.error(f"[Client Train] 客户端ID类型无效: {type(self.client_id)}，值: {self.client_id}")
                # 使用ID管理器获取有效的客户端ID
                id_manager = ClientIDManager()
                self.client_id = id_manager.get_valid_id(None)
                logging.warning(f"[Client Train] 客户端ID类型无效，已分配新ID: {self.client_id}")
                
            # 输出更明确的客户端身份信息
            logging.info(f"[客户端类型: {self.__class__.__name__}, ID: {self.client_id}] 准备开始训练")
            
            # 确保训练器ID与客户端ID一致
            if hasattr(self, 'trainer') and self.trainer is not None:
                trainer_id = getattr(self.trainer, 'client_id', None)
                logging.info(f"[客户端 {self.client_id}] 使用的训练器 client_id: {trainer_id}")
                # 确保训练器ID与客户端ID一致
                if trainer_id != self.client_id:
                    logging.warning(f"[客户端 {self.client_id}] 训练器ID不匹配: {trainer_id}，正在修正")
                    self.trainer.client_id = self.client_id
                
            # 记录训练开始时间
            start_time = time.time()
            
            # 设置训练状态
            self.is_training = True
            
            # 确保数据集已初始化
            if not hasattr(self.datasource, 'trainset'):
                logging.error(f"[Client {self.client_id}] 数据源没有trainset属性")
                self.is_training = False
                return None, None
            
            # 详细验证数据集，如果为空则尝试加载
            logging.info(f"[Client {self.client_id}] 开始验证训练集")
            if (self.datasource.trainset is None or
                not hasattr(self.datasource.trainset, '__len__') or
                len(self.datasource.trainset) == 0):

                logging.warning(f"[Client {self.client_id}] 训练集为空或无效，尝试加载数据")

                # 尝试加载数据
                if not self.load_data():
                    logging.error(f"[Client {self.client_id}] 数据加载失败")
                    self.is_training = False
                    return None, None

                # 再次验证数据集
                if (self.datasource.trainset is None or
                    not hasattr(self.datasource.trainset, '__len__') or
                    len(self.datasource.trainset) == 0):
                    logging.error(f"[Client {self.client_id}] 数据加载后训练集仍为空")
                    self.is_training = False
                    return None, None
            
            # 尝试访问第一个样本，验证数据格式
            try:
                sample = self.datasource.trainset[0]
                logging.info(f"[Client {self.client_id}] 成功访问第一个训练样本: {type(sample)}")
            except Exception as e:
                logging.error(f"[Client {self.client_id}] 访问训练样本时出错: {str(e)}")
                self.is_training = False
                return None, None
            
            # 更新当前轮次
            current_round = self.last_round + 1
            self.last_round = current_round
            
            # 确保模型和训练器已初始化
            if self.model is None:
                config = Config()
                try:
                    self.model = create_dynamic_model_for_client("训练时", self.client_id)
                except Exception as e:
                    log_exception(f"[Client {self.client_id}] 训练时模型创建失败", e)
                
            if self.trainer is None:
                from sc_afl_trainer import Trainer
                self.trainer = Trainer(model=self.model, client_id=self.client_id)
                logging.info(f"[Client {self.client_id}] 创建新训练器用于训练")
            
            # 再次确保训练器的client_id与客户端ID一致
            if self.trainer.client_id != self.client_id:
                logging.warning(f"[Client {self.client_id}] 训练前再次检查到训练器ID不匹配: {self.trainer.client_id}，修正为: {self.client_id}")
                self.trainer.client_id = self.client_id
            
            # 直接使用trainer的train方法，并显式传递client_id
            logging.info(f"[Client {self.client_id}] 🚀 开始训练，数据集大小: {len(self.datasource.trainset)}")

            try:
                trainer_report = await self.trainer.train(
                    trainset=self.datasource.trainset,
                    sampler=None,  # 不使用sampler，避免num_samples相关问题
                    client_id=self.client_id  # 显式传递client_id
                )
                logging.info(f"[Client {self.client_id}] ✅ 训练器训练完成，获得报告: {trainer_report is not None}")
            except Exception as e:
                logging.error(f"[Client {self.client_id}] 训练器训练过程中出错: {str(e)}")
                import traceback
                logging.error(f"[Client {self.client_id}] 训练器异常堆栈: {traceback.format_exc()}")
                self.is_training = False
                return None, None

            if trainer_report is None:
                logging.error(f"[Client {self.client_id}] 训练失败: 训练器返回None")
                self.is_training = False
                return None, None
            
            # 记录训练结束时间和持续时间
            self.training_end_time = time.time()
            self.training_time = self.training_end_time - start_time
            
            # 检查是否需要进行本地测试
            from plato.config import Config
            config = Config()
            do_test = getattr(config.clients, 'do_test', True)

            # 获取训练准确率作为客户端准确率（因为客户端通常没有独立的测试集）
            train_accuracy = trainer_report.get('train_accuracy', 0.0)

            # 如果客户端有独立的测试集，则进行本地测试
            test_accuracy = 0.0
            if do_test and hasattr(self.datasource, 'testset') and self.datasource.testset is not None:
                try:
                    logging.info(f"[Client {self.client_id}] 开始本地测试，测试集大小: {len(self.datasource.testset)}")
                    test_accuracy = self.trainer.test(self.datasource.testset)
                    logging.info(f"[Client {self.client_id}] 本地测试完成，测试准确率: {test_accuracy:.4f}")
                except Exception as e:
                    logging.error(f"[Client {self.client_id}] 本地测试失败: {str(e)}")
                    test_accuracy = 0.0
            else:
                logging.info(f"[Client {self.client_id}] 使用训练准确率作为客户端准确率: {train_accuracy:.4f}")

            # 创建标准格式的报告
            report = SimpleNamespace()
            report.accuracy = test_accuracy if test_accuracy > 0 else train_accuracy  # 优先使用测试准确率，否则使用训练准确率
            report.train_accuracy = train_accuracy  # 保留训练准确率
            report.loss = trainer_report.get('train_loss', 0.0)
            report.num_samples = len(self.datasource.trainset)
            report.training_time = self.training_time
            report.client_id = self.client_id
            
            # 更新训练历史
            self.training_history.append({
                'round': self.last_round,
                'time': self.training_time,
                'samples': report.num_samples,
                'accuracy': report.accuracy,
                'loss': report.loss
            })
            
            # 更新准确率和损失历史
            self.accuracy_history.append(report.accuracy)
            self.loss_history.append(report.loss)
            
            # 设置训练状态
            self.is_training = False
            
            # 记录训练信息
            logging.info(f"[Client {self.client_id}] 第 {current_round} 轮训练完成，耗时: {self.training_time:.2f}秒, 准确率: {report.accuracy:.4f}")
            
            # 提取权重
            logging.info(f"[Client {self.client_id}] 开始提取模型权重")
            try:
                weights = self.algorithm.extract_weights()
                if weights is None:
                    logging.error(f"[Client {self.client_id}] 无法提取模型权重: algorithm.extract_weights() 返回None")
                    return report, None

                logging.info(f"[Client {self.client_id}] ✅ 成功提取模型权重，权重数量: {len(weights) if weights else 0}")

                # 检查权重格式
                if isinstance(weights, dict):
                    weight_info = []
                    for key, value in list(weights.items())[:3]:  # 只检查前3个权重
                        if hasattr(value, 'shape'):
                            weight_info.append(f"{key}: {value.shape}")
                    logging.info(f"[Client {self.client_id}] 权重样本: {weight_info}")
                else:
                    logging.warning(f"[Client {self.client_id}] 权重不是字典格式: {type(weights)}")

            except Exception as e:
                logging.error(f"[Client {self.client_id}] 提取权重时出错: {str(e)}")
                import traceback
                logging.error(f"[Client {self.client_id}] 权重提取异常堆栈: {traceback.format_exc()}")
                return report, None

            # 异步模式下，训练完成后立即上传结果
            from plato.config import Config
            config = Config()
            synchronous_mode = getattr(config.server, 'synchronous_mode', False)

            logging.info(f"[Client {self.client_id}] 配置检查 - synchronous_mode: {synchronous_mode}, 有服务器引用: {hasattr(self, 'server') and self.server is not None}")

            if not synchronous_mode and hasattr(self, 'server') and self.server is not None:
                logging.info(f"[Client {self.client_id}] 🚀 异步模式，训练完成后立即上传结果")

                # 创建上传载荷
                payload = SimpleNamespace()
                payload.client_id = self.client_id
                payload.report = report
                payload.weights = weights

                try:
                    logging.info(f"[Client {self.client_id}] 正在调用服务器的 receive_update_from_client 方法...")
                    # 调用服务器方法上传
                    upload_success = await self.server.receive_update_from_client(payload)
                    if upload_success:
                        logging.info(f"[Client {self.client_id}] ✅ 成功上传训练结果到服务器")
                    else:
                        logging.error(f"[Client {self.client_id}] ❌ 上传训练结果失败")
                except Exception as e:
                    logging.error(f"[Client {self.client_id}] ❌ 上传训练结果时出错: {str(e)}")
                    import traceback
                    logging.error(f"[Client {self.client_id}] 上传异常堆栈: {traceback.format_exc()}")
            else:
                if synchronous_mode:
                    logging.info(f"[Client {self.client_id}] 同步模式，不立即上传结果")
                else:
                    logging.warning(f"[Client {self.client_id}] 没有服务器引用，无法上传结果")

            return report, weights
            
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 训练过程中发生异常: {str(e)}")
            import traceback
            logging.error(f"[Client {self.client_id}] 异常堆栈: {traceback.format_exc()}")
            self.is_training = False
            return None, None

    def set_server_reference(self, server):
        """设置服务器引用"""
        # 检查客户端ID是否有效
        if self.client_id is None:
            # 使用ID管理器获取有效的客户端ID
            id_manager = ClientIDManager()
            self.client_id = id_manager.get_valid_id(None)
            logging.warning(f"[Client Set Server] 客户端ID为None，已分配新ID: {self.client_id}")
            import traceback
            stack = traceback.format_stack()
            logging.warning(f"[Client Set Server] 调用堆栈: {stack}")
            
        # 确保client_id是整数
        try:
            self.client_id = int(self.client_id)
        except (ValueError, TypeError):
            logging.error(f"[Client Set Server] 客户端ID类型无效: {type(self.client_id)}，值: {self.client_id}")
            # 使用ID管理器获取有效的客户端ID
            id_manager = ClientIDManager()
            self.client_id = id_manager.get_valid_id(None)
            logging.warning(f"[Client Set Server] 客户端ID类型无效，已分配新ID: {self.client_id}")
            
        self.server = server
        logging.info(f"客户端{self.client_id}已设置服务器引用")

    def update_client_state(self):
        """
        更新客户端状态
        
        简化版：客户端本地只需维护model_version
        服务器端负责计算和更新tau_k、D_k、Q_k等状态
        """
        # 确保model_version属性存在
        if not hasattr(self, 'model_version'):
            self.model_version = 0
            
        # 记录日志
        logging.debug(f"[Client {self.client_id}] 客户端状态已更新，当前模型版本: {self.model_version}")

    async def process_server_response(self, server_response):
        """处理来自服务器的响应

        Args:
            server_response: 服务器响应数据

        Returns:
            bool: 处理是否成功
        """
        try:
            if server_response is None:
                logging.error(f"[Client {self.client_id}] 收到空的服务器响应")
                return False
            
            # 检查是否是聚合完成通知
            if isinstance(server_response, dict) and server_response.get('status') == 'aggregation_complete':
                logging.info(f"[Client {self.client_id}] 收到聚合完成通知，可以开始新一轮训练")
                self.aggregation_complete = True
                return True
                
            # 处理模型更新通知
            if isinstance(server_response, dict) and server_response.get('status') == 'model_updated':
                logging.info(f"[Client {self.client_id}] 收到模型更新通知，将在下次训练中使用最新模型")
                # 这里可以标记模型需要更新，下次训练前获取最新模型
                self.model_needs_update = True
                return True
                
            # 处理其他类型的响应
            if isinstance(server_response, dict):
                for key, value in server_response.items():
                    logging.info(f"[Client {self.client_id}] 接收到服务器响应: {key} = {value}")
            
            return True
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 处理服务器响应时出错: {str(e)}")
            import traceback
            logging.error(f"[Client {self.client_id}] 异常堆栈: {traceback.format_exc()}")
            return False

    async def run(self):
        """运行客户端训练流程
        
        Returns:
            bool: 运行是否成功
        """
        if self.server is None:
            logging.error(f"[Client {self.client_id}] 服务器引用未设置，无法运行")
            return False
            
        try:
            # 强制加载数据
            logging.info(f"[Client {self.client_id}] 确保训练数据已加载")
            if not self.data_loaded:
                success = self.load_data()
                if success:
                    logging.info(f"[Client {self.client_id}] 成功加载训练数据，数据集大小: {len(self.datasource.trainset)}")
                else:
                    logging.error(f"[Client {self.client_id}] 数据加载失败，尝试手动创建数据集")
            
            # 验证数据集状态
            if not hasattr(self.datasource, 'trainset') or self.datasource.trainset is None or len(self.datasource.trainset) == 0:
                logging.warning(f"[Client {self.client_id}] 训练集未加载或为空，再次尝试加载")
                self.load_data()
                
                # 最终检查
                if not hasattr(self.datasource, 'trainset') or self.datasource.trainset is None or len(self.datasource.trainset) == 0:
                    logging.error(f"[Client {self.client_id}] 无法加载训练数据，训练将可能失败")
            else:
                logging.info(f"[Client {self.client_id}] 训练数据已就绪，数据集大小: {len(self.datasource.trainset)}")
            
            # 获取配置项
            config = Config()
            synchronous_mode = getattr(config.server, 'synchronous_mode', False)  # 默认为异步模式
            max_rounds = getattr(config.trainer, 'rounds', 100)  # 默认最多训练100轮
            max_retry = getattr(config.clients, 'max_retry', 3)  # 默认最多重试3次
            retry_interval = getattr(config.clients, 'retry_interval', 5)  # 默认重试间隔5秒

            logging.info(f"[Client {self.client_id}] 运行模式: {'同步' if synchronous_mode else '异步'}")

            # 初始化同步模式所需的属性
            if synchronous_mode and not hasattr(self, 'aggregation_complete'):
                self.aggregation_complete = True  # 初始状态设为True，以便启动第一轮训练
            
            # 记录开始时间
            start_time = time.time()
            
            # 主训练循环
            while self.last_round < max_rounds:
                logging.info(f"[Client {self.client_id}] 准备第 {self.last_round + 1} 轮训练")
                
                # 同步模式下，检查是否收到聚合完成通知
                if synchronous_mode and not self.aggregation_complete:
                    logging.info(f"[Client {self.client_id}] 等待服务器完成聚合...")
                    
                    # 等待聚合完成
                    wait_start = time.time()
                    max_wait_time = getattr(config.clients, 'max_aggregation_wait', 60)  # 默认最多等待60秒
                    
                    while not self.aggregation_complete:
                        # 检查是否超时
                        if time.time() - wait_start > max_wait_time:
                            logging.warning(f"[Client {self.client_id}] 等待聚合完成超时，强制继续")
                            break
                            
                        # 短暂等待
                        await asyncio.sleep(1)
                        
                        # 每10秒输出一次等待状态
                        elapsed = time.time() - wait_start
                        if int(elapsed) % 10 == 0:
                            logging.info(f"[Client {self.client_id}] 已等待聚合 {elapsed:.1f} 秒...")
                
                # 异步模式下跳过接收模型步骤，同步模式下接收服务器模型
                if synchronous_mode:
                    retry_count = 0
                    receive_success = False

                    while retry_count < max_retry and not receive_success:
                        try:
                            receive_success = await self.receive_model()
                            if receive_success:
                                break
                        except Exception as e:
                            logging.error(f"[Client {self.client_id}] 接收模型失败 (尝试 {retry_count+1}/{max_retry}): {str(e)}")

                        retry_count += 1
                        if retry_count < max_retry:
                            logging.info(f"[Client {self.client_id}] {retry_interval} 秒后重试接收模型...")
                            await asyncio.sleep(retry_interval)

                    if not receive_success:
                        logging.error(f"[Client {self.client_id}] 达到最大重试次数，放弃本轮训练")
                        continue
                else:
                    # 异步模式下，检查是否需要拉取新模型
                    logging.info(f"[Client {self.client_id}] 异步模式，检查是否需要拉取新模型")

                    # 尝试拉取最新模型（如果有更新的话）
                    try:
                        receive_success = await self.receive_model()
                        if receive_success:
                            logging.info(f"[Client {self.client_id}] 成功拉取最新模型")
                        else:
                            logging.info(f"[Client {self.client_id}] 使用当前模型进行训练")
                    except Exception as e:
                        logging.warning(f"[Client {self.client_id}] 拉取模型失败，使用当前模型: {str(e)}")
                    
                # 进行训练
                report, weights = await self.train()
                if report is None or weights is None:
                    logging.error(f"[Client {self.client_id}] 训练失败，跳过上传")
                    continue

                logging.info(f"[Client {self.client_id}] 训练完成，准备上传结果")
                    
                # 上传训练结果
                retry_count = 0
                upload_success = False
                
                while retry_count < max_retry and not upload_success:
                    try:
                        # 创建上传载荷
                        payload = SimpleNamespace()
                        payload.client_id = self.client_id
                        payload.report = report
                        payload.weights = weights
                        
                        # 调用服务器方法上传
                        upload_success = await self.server.receive_update_from_client(payload)
                        if upload_success:
                            logging.info(f"[Client {self.client_id}] 成功上传训练结果")
                            break
                    except Exception as e:
                        logging.error(f"[Client {self.client_id}] 上传训练结果失败 (尝试 {retry_count+1}/{max_retry}): {str(e)}")
                        
                    retry_count += 1
                    if retry_count < max_retry:
                        logging.info(f"[Client {self.client_id}] {retry_interval} 秒后重试上传...")
                        await asyncio.sleep(retry_interval)
                        
                if not upload_success:
                    logging.error(f"[Client {self.client_id}] 达到最大重试次数，放弃上传")
                
                # 同步模式下，设置等待聚合完成标志
                if synchronous_mode:
                    self.aggregation_complete = False
                    
                # 更新客户端状态
                self.update_client_state()
                
                # 记录训练轮次和时间
                elapsed_time = time.time() - start_time
                logging.info(f"[Client {self.client_id}] 完成第 {self.last_round} 轮训练，累计耗时 {elapsed_time:.2f} 秒")
                
            logging.info(f"[Client {self.client_id}] 完成所有训练轮次")
            return True
            
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 运行过程中出错: {str(e)}")
            import traceback
            logging.error(f"[Client {self.client_id}] 异常堆栈: {traceback.format_exc()}")
            return False

    def __str__(self):
        """返回客户端的字符串表示"""
        return f"Client #{self.client_id}"

    def __repr__(self):
        """返回客户端的字符串表示（用于调试）"""
        return f"Client(client_id={self.client_id})"

    def initialize_model(self):
        """初始化客户端模型
        
        确保模型被正确创建并加载到设备上
        """
        try:
            config = Config()
            # 检查Config中是否有in_channels配置，默认为MNIST的1通道
            in_channels = getattr(config.parameters.model, 'in_channels', 1)
            num_classes = getattr(config.parameters.model, 'num_classes', 10)
            
            if self.model is None:
                # 使用统一的动态模型创建函数
                try:
                    self.model = create_dynamic_model_for_client("模型检查", self.client_id)
                except Exception as e:
                    log_exception(f"[Client {self.client_id}] 模型检查时创建失败", e)
            else:
                # 检查现有模型的输入通道
                if hasattr(self.model, 'conv1') and hasattr(self.model.conv1, 'weight'):
                    model_in_channels = self.model.conv1.weight.shape[1]
                    if model_in_channels != in_channels:
                        logging.warning(f"[Client {self.client_id}] 模型通道数({model_in_channels})与配置通道数({in_channels})不匹配")
                        # 如果通道不匹配，重新创建模型
                        try:
                            self.model = create_dynamic_model_for_client("重新创建", self.client_id)
                        except Exception as e:
                            log_exception(f"[Client {self.client_id}] 模型重建失败", e)
                
            # 确保模型在正确的设备上
            if hasattr(self.trainer, 'device'):
                device = self.trainer.device
                self.model = self.model.to(device)
                logging.info(f"[Client {self.client_id}] 模型已放置到设备: {device}")
            else:
                # 如果trainer没有device属性，我们创建一个
                device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                logging.info(f"[Client {self.client_id}] 自动选择设备: {device}")
                self.model = self.model.to(device)
                
                # 确保trainer有device属性
                if hasattr(self, 'trainer') and self.trainer is not None:
                    self.trainer.device = device
                    logging.info(f"[Client {self.client_id}] 已为trainer设置device: {device}")
                    
                    # 确保trainer的模型也在同一设备上
                    if hasattr(self.trainer, 'model'):
                        self.trainer.model = self.trainer.model.to(device)
                        logging.info(f"[Client {self.client_id}] 已将trainer的模型移至设备: {device}")
                
            # 确保trainer也有正确的模型引用 - 使用深拷贝避免模型实例共享
            if self.trainer is not None:
                import copy
                import torch
                self.trainer.model = copy.deepcopy(self.model)

                # 重置BatchNorm层的统计信息，避免共享running_mean和running_var
                for module in self.trainer.model.modules():
                    if isinstance(module, torch.nn.BatchNorm2d):
                        module.reset_running_stats()
                        module.momentum = 0.1
                        module.track_running_stats = True

                # 确保拷贝的模型在正确的设备上
                if hasattr(self.trainer, 'device'):
                    self.trainer.model = self.trainer.model.to(self.trainer.device)
                logging.info(f"[Client {self.client_id}] 已为trainer创建模型的深拷贝，避免实例共享")
                
                # 确保训练器初始化了必要的组件
                if not hasattr(self.trainer, 'optimizer') or self.trainer.optimizer is None:
                    try:
                        self.trainer.optimizer = torch.optim.SGD(
                            self.model.parameters(),
                            lr=getattr(config.trainer, 'learning_rate', 0.01),
                            momentum=getattr(config.trainer, 'momentum', 0.9),
                            weight_decay=getattr(config.trainer, 'weight_decay', 0.0001)
                        )
                        logging.info(f"[Client {self.client_id}] 已为trainer创建optimizer")
                    except Exception as e:
                        logging.error(f"[Client {self.client_id}] 创建optimizer失败: {str(e)}")
                
                if not hasattr(self.trainer, 'loss_criterion') or self.trainer.loss_criterion is None:
                    self.trainer.loss_criterion = torch.nn.CrossEntropyLoss()
                    logging.info(f"[Client {self.client_id}] 已为trainer创建loss_criterion")
            
            # 确保algorithm也有正确的模型引用（通过trainer） - 使用深拷贝避免模型实例共享
            if self.algorithm is not None and hasattr(self.algorithm, 'trainer'):
                import copy
                self.algorithm.trainer.model = copy.deepcopy(self.model)
                # 确保拷贝的模型在正确的设备上
                if hasattr(self.algorithm.trainer, 'device'):
                    self.algorithm.trainer.model = self.algorithm.trainer.model.to(self.algorithm.trainer.device)
                logging.info(f"[Client {self.client_id}] 已为algorithm的trainer创建模型的深拷贝，避免实例共享")
                
            # 确保algorithm中有client_id
            if self.algorithm is not None and hasattr(self.algorithm, 'set_client_id'):
                self.algorithm.set_client_id(self.client_id)
                logging.info(f"[Client {self.client_id}] 已更新algorithm的client_id")
                
            logging.info(f"[Client {self.client_id}] 模型初始化完成")
            return True
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 初始化模型时出错: {str(e)}")
            import traceback
            logging.error(f"[Client {self.client_id}] 初始化模型异常堆栈: {traceback.format_exc()}")
            return False

    def _get_model_in_channels(self):
        """获取模型的输入通道数"""
        try:
            if self.model is None:
                return None
                
            if hasattr(self.model, 'conv1') and hasattr(self.model.conv1, 'weight'):
                return self.model.conv1.weight.shape[1]
            elif hasattr(self.model, 'in_channels'):
                return self.model.in_channels
            else:
                return None
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 获取模型通道数时出错: {str(e)}")
            return None
    
    def _get_weights_in_channels(self, weights):
        """获取权重的输入通道数"""
        try:
            if weights is None or 'conv1.weight' not in weights:
                return None
                
            return weights['conv1.weight'].shape[1]
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 获取权重通道数时出错: {str(e)}")
            return None
    
    def _handle_channel_mismatch(self, weights, model_channels, weights_channels):
        """处理通道数不匹配问题
        
        Args:
            weights: 模型权重
            model_channels: 模型通道数
            weights_channels: 权重通道数
            
        Returns:
            dict: 处理后的权重
        """
        try:
            logging.warning(f"[Client {self.client_id}] 检测到通道数不匹配: 模型 {model_channels} 通道, 权重 {weights_channels} 通道")
            
            # 如果模型使用1通道，但权重是3通道
            if model_channels == 1 and weights_channels == 3:
                # 使用动态加载器创建新的模型以匹配权重
                try:
                    from dynamic_loader import DynamicModelLoader, ConfigurationManager
                    config = Config()
                    dataset_name, num_classes, _ = ConfigurationManager.get_dataset_info_from_config(config)
                    model_name = ConfigurationManager.get_model_info_from_config(config)
                    new_model = DynamicModelLoader.create_model(model_name, num_classes, in_channels=3)
                    logging.info(f"[Client {self.client_id}] 动态创建新的3通道模型 {model_name} 以匹配权重")
                except Exception as e:
                    logging.warning(f"[Client {self.client_id}] 动态创建3通道模型失败: {e}，回退到LeNet5")
                    # 获取现有模型的类别数
                    num_classes = 10
                    if hasattr(self.model, 'fc5') and hasattr(self.model.fc5, 'out_features'):
                        num_classes = self.model.fc5.out_features
                    elif hasattr(self.model, 'linear') and hasattr(self.model.linear, 'out_features'):
                        num_classes = self.model.linear.out_features

                    from plato.models import lenet5
                    new_model = lenet5.Model(in_channels=3, num_classes=num_classes)
                    logging.info(f"[Client {self.client_id}] 回退创建新的3通道LeNet5模型以匹配权重")
                
                # 使用当前设备
                if hasattr(self.trainer, 'device'):
                    new_model = new_model.to(self.trainer.device)
                
                # 更新模型引用
                self.model = new_model
                if self.trainer is not None:
                    # 使用深拷贝避免模型实例共享
                    import copy
                    import torch
                    self.trainer.model = copy.deepcopy(new_model)

                    # 重置BatchNorm层的统计信息，避免共享running_mean和running_var
                    for module in self.trainer.model.modules():
                        if isinstance(module, torch.nn.BatchNorm2d):
                            module.reset_running_stats()
                            module.momentum = 0.1
                            module.track_running_stats = True

                    if hasattr(self.trainer, 'device'):
                        self.trainer.model = self.trainer.model.to(self.trainer.device)
                
                logging.info(f"[Client {self.client_id}] 已创建3通道模型以匹配权重")
                return weights
            
            # 如果模型使用3通道，但权重是1通道
            elif model_channels == 3 and weights_channels == 1:
                # 使用动态加载器创建新的模型以匹配权重
                try:
                    from dynamic_loader import DynamicModelLoader, ConfigurationManager
                    config = Config()
                    dataset_name, num_classes, _ = ConfigurationManager.get_dataset_info_from_config(config)
                    model_name = ConfigurationManager.get_model_info_from_config(config)
                    new_model = DynamicModelLoader.create_model(model_name, num_classes, in_channels=1)
                    logging.info(f"[Client {self.client_id}] 动态创建新的1通道模型 {model_name} 以匹配权重")
                except Exception as e:
                    logging.warning(f"[Client {self.client_id}] 动态创建1通道模型失败: {e}，回退到LeNet5")
                    # 获取现有模型的类别数
                    num_classes = 10
                    if hasattr(self.model, 'fc5') and hasattr(self.model.fc5, 'out_features'):
                        num_classes = self.model.fc5.out_features
                    elif hasattr(self.model, 'linear') and hasattr(self.model.linear, 'out_features'):
                        num_classes = self.model.linear.out_features

                    from plato.models import lenet5
                    new_model = lenet5.Model(in_channels=1, num_classes=num_classes)
                    logging.info(f"[Client {self.client_id}] 回退创建新的1通道LeNet5模型以匹配权重")
                
                # 使用当前设备
                if hasattr(self.trainer, 'device'):
                    new_model = new_model.to(self.trainer.device)
                
                # 更新模型引用
                self.model = new_model
                if self.trainer is not None:
                    # 使用深拷贝避免模型实例共享
                    import copy
                    import torch
                    self.trainer.model = copy.deepcopy(new_model)

                    # 重置BatchNorm层的统计信息，避免共享running_mean和running_var
                    for module in self.trainer.model.modules():
                        if isinstance(module, torch.nn.BatchNorm2d):
                            module.reset_running_stats()
                            module.momentum = 0.1
                            module.track_running_stats = True

                    if hasattr(self.trainer, 'device'):
                        self.trainer.model = self.trainer.model.to(self.trainer.device)
                
                logging.info(f"[Client {self.client_id}] 已创建1通道模型以匹配权重")
                return weights
            
            # 无法处理的情况
            else:
                logging.error(f"[Client {self.client_id}] 无法处理通道数不匹配: 模型 {model_channels} 通道, 权重 {weights_channels} 通道")
                return weights
        except Exception as e:
            logging.error(f"[Client {self.client_id}] 处理通道数不匹配时出错: {str(e)}")
            return weights

# 确保客户端能被Plato框架识别
def client() -> Client:
    return Client()