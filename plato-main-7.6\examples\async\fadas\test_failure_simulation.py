#!/usr/bin/env python3
"""
简单的失败模拟测试程序
用于验证通信失败模拟模块是否正常工作
"""

import sys
import os
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from communication_failure_simulator import CommunicationFailureSimulator


def test_failure_simulation():
    """测试失败模拟功能"""
    print("🧪 开始测试通信失败模拟模块...")
    
    # 创建模拟器
    config = {
        'base_communication_time': 0.5,
        'communication_noise_std': 0.5,
        'initial_threshold': 1.0,
        'enable_logging': True
    }
    
    simulator = CommunicationFailureSimulator(config)
    
    print(f"✅ 模拟器初始化成功")
    print(f"   - 基础通信时间: {config['base_communication_time']}s")
    print(f"   - 初始阈值: {config['initial_threshold']}s")
    
    # 模拟多个客户端的上传尝试
    print("\n📊 模拟客户端上传测试:")
    print("-" * 50)
    
    for round_num in range(1, 4):
        print(f"\n🔄 第{round_num}轮:")
        
        for client_id in range(1, 7):  # 6个客户端
            success, comm_time, info = simulator.should_upload_succeed(client_id)
            status = "✅ 成功" if success else "❌ 失败"
            print(f"   客户端{client_id}: {status} (通信时间: {comm_time:.3f}s)")
    
    # 打印统计信息
    print("\n📈 统计信息:")
    print("-" * 50)
    stats = simulator.get_statistics()
    overall = stats['overall']
    
    print(f"总尝试次数: {overall['total_attempts']}")
    print(f"总失败次数: {overall['total_failures']}")
    print(f"总体失败率: {overall['failure_rate']:.2%}")
    print(f"当前阈值: {overall['current_threshold']:.3f}s")
    print(f"平均通信时间: {overall['avg_comm_time']:.3f}s")
    
    # 客户端详细统计
    print(f"\n👥 客户端详细统计:")
    for client_id, client_stats in stats['clients'].items():
        print(f"   客户端{client_id}: "
              f"尝试{client_stats['attempts']}次, "
              f"失败{client_stats['failures']}次, "
              f"失败率{client_stats['failure_rate']:.2%}")
    
    print(f"\n🎉 测试完成！模拟器工作正常。")
    return True


def test_integration_example():
    """测试集成示例"""
    print(f"\n🔧 测试集成示例...")
    
    # 模拟FedAS服务器的简化版本
    class MockFedASServer:
        def __init__(self):
            self.failure_simulator = CommunicationFailureSimulator({
                'base_communication_time': 0.6,
                'enable_logging': False  # 减少日志输出
            })
            self.successful_updates = []
            self.failed_updates = []
        
        def process_client_update(self, client_id, mock_weights):
            """处理客户端更新"""
            success, comm_time, info = self.failure_simulator.should_upload_succeed(client_id)
            
            if success:
                self.successful_updates.append({
                    'client_id': client_id,
                    'weights': mock_weights,
                    'comm_time': comm_time
                })
                return True, f"成功处理客户端{client_id}"
            else:
                self.failed_updates.append({
                    'client_id': client_id,
                    'comm_time': comm_time,
                    'reason': 'communication_timeout'
                })
                return False, f"客户端{client_id}上传失败"
    
    # 创建模拟服务器
    server = MockFedASServer()
    
    # 模拟训练过程
    print("模拟FedAS训练过程:")
    for round_num in range(1, 4):
        print(f"\n第{round_num}轮:")
        round_successful = 0
        round_failed = 0
        
        for client_id in range(1, 5):  # 4个客户端
            mock_weights = {'layer1': [1, 2, 3], 'layer2': [4, 5, 6]}
            success, message = server.process_client_update(client_id, mock_weights)
            
            if success:
                round_successful += 1
                print(f"   ✅ {message}")
            else:
                round_failed += 1
                print(f"   ❌ {message}")
        
        print(f"   本轮结果: {round_successful}成功, {round_failed}失败")
    
    # 最终统计
    total_successful = len(server.successful_updates)
    total_failed = len(server.failed_updates)
    total_attempts = total_successful + total_failed
    
    print(f"\n📊 最终统计:")
    print(f"   总更新尝试: {total_attempts}")
    print(f"   成功处理: {total_successful}")
    print(f"   失败丢弃: {total_failed}")
    print(f"   成功率: {total_successful/total_attempts:.2%}")
    
    print(f"✅ 集成测试完成！")
    return True


def main():
    """主函数"""
    print("🚀 FedAS通信失败模拟验证程序")
    print("=" * 60)
    
    try:
        # 测试基础功能
        test_failure_simulation()
        
        # 测试集成功能
        test_integration_example()
        
        print(f"\n🎯 验证结论:")
        print(f"✅ 通信失败模拟模块工作正常")
        print(f"✅ 可以成功集成到FedAS中")
        print(f"✅ 失败的客户端更新会被正确丢弃")
        print(f"✅ 成功的客户端更新会被正常处理")
        print(f"✅ 统计信息记录准确")
        
        print(f"\n💡 下一步:")
        print(f"1. 将此模块集成到您的FedAS实现中")
        print(f"2. 在配置文件中添加failure_simulation配置")
        print(f"3. 运行完整的FedAS训练并观察效果")
        print(f"4. 与其他算法进行公平对比")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    # 设置简单的日志
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # 运行测试
    success = main()
    
    if success:
        print(f"\n🎉 所有测试通过！您可以开始使用失败模拟功能了。")
    else:
        print(f"\n💥 测试失败，请检查错误信息。")
        sys.exit(1)
