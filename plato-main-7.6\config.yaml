clients:
    type: simple
    total_clients: 100
    per_round: 5
    do_test: false
    speed_simulation: true
    simulation_distribution:
        distribution: pareto
        alpha: 1
    max_sleep_time: 30
    sleep_simulation: true
    avg_training_time: 10
    random_seed: 1

server:
    type: fedavg
    address: 127.0.0.1
    port: 8000
    synchronous: false
    simulate_wall_time: true

    # SC-AFL 专属参数
    tau_max: 6
    V: 0.5

    # 是否保存模型/日志
    checkpoint_path: models/scafl/mnist
    model_path: models/scafl/mnist

data:
    datasource: MNIST
    partition_size: 600
    sampler: noniid
    concentration: 0.1  # 非IID程度强（越小越极端）
    random_seed: 1

trainer:
    type: basic
    rounds: 5
    max_concurrency: 2
    target_accuracy: 0.98  # 目标准确率
    model_name: lenet5
    epochs: 1
    batch_size: 128
    optimizer: SGD

algorithm:
    type: fedavg
    communication_threshold: 5.0  # 通信时间阈值（秒）
    communication_failure_prob: 0.1  # 通信失败概率（0-1）
    K_max: 10  # 最大缓冲池大小
    score_threshold: 100.0  # 分数阈值
    tau_max: 6  # 最大允许陈旧度
    V: 0.5  # 通信开销权重
    rho: 1.0  # 上传客户端权重系数
    eta: 1.0  # 蒸馏客户端权重系数

parameters:
    model:
        num_classes: 10
        in_channels: 3  # 指定输入通道数为3，适应彩色图像

    optimizer:
        lr: 0.005
        momentum: 0.9
        weight_decay: 0.0004

results:
    result_path: results/scafl/mnist
    types: round, elapsed_time, accuracy
