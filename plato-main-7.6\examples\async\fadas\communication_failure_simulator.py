"""
可移植的客户端上传失败模拟模块
基于ReFedScaFL的实现，适用于所有联邦学习算法
"""

import logging
import time
import numpy as np
from typing import Dict, List, Optional, Tuple
from collections import deque


class CommunicationFailureSimulator:
    """客户端上传失败模拟器"""
    
    def __init__(self, config: Optional[Dict] = None):
        # 设置默认配置
        default_config = {
            'base_communication_time': 0.5,
            'communication_noise_std': 0.5,
            'initial_threshold': 1.0,
            'threshold_range': [0.2, 3.0],
            'smoothing_alpha': 0.3,
            'window_size': 20,
            'enable_dynamic_threshold': True,
            'enable_logging': True
        }
        
        # 合并用户配置
        self.config = {**default_config, **(config or {})}
        
        # 初始化状态变量
        self.upload_threshold = self.config['initial_threshold']
        self.comm_time_window = deque(maxlen=self.config['window_size'])
        self.client_upload_success: Dict[int, bool] = {}
        self.client_comm_times: Dict[int, float] = {}
        
        # 统计信息
        self.total_attempts = 0
        self.total_failures = 0
        self.client_failure_counts: Dict[int, int] = {}
        self.client_attempt_counts: Dict[int, int] = {}
        
        # 日志设置
        self.logger = logging.getLogger(f"{__name__}.CommunicationFailureSimulator")
        if self.config['enable_logging']:
            self.logger.setLevel(logging.INFO)
    
    def generate_communication_time(self, client_id: int) -> float:
        """为指定客户端生成通信时间"""
        base_time = self.config['base_communication_time']
        noise_std = base_time * self.config['communication_noise_std']
        noise = np.random.normal(0, noise_std)
        comm_time = max(base_time + noise, 0.1)
        self.client_comm_times[client_id] = comm_time
        return comm_time
    
    def update_threshold(self, comm_time: float) -> None:
        """更新动态通信阈值"""
        if not self.config['enable_dynamic_threshold']:
            return
            
        self.comm_time_window.append(comm_time)
        
        if len(self.comm_time_window) < 5:
            return
        
        # 排序并去除异常值
        sorted_times = sorted(list(self.comm_time_window))
        if len(sorted_times) > 4:
            valid_times = sorted_times[1:-1]
        else:
            valid_times = sorted_times
        
        # 计算统计量
        mean_time = np.mean(valid_times)
        std_time = np.std(valid_times)
        
        # 根据波动性动态调整系数
        cv = std_time / mean_time if mean_time > 0 else 0
        if cv > 0.5:  # 高波动性
            coef = 0.3
        elif cv > 0.2:  # 中波动性
            coef = 0.15
        else:  # 低波动性
            coef = 0.05
        
        # 计算新阈值
        new_threshold = mean_time + coef * std_time
        
        # 平滑更新
        alpha = self.config['smoothing_alpha']
        self.upload_threshold = alpha * new_threshold + (1 - alpha) * self.upload_threshold
        
        # 限制阈值范围
        min_threshold, max_threshold = self.config['threshold_range']
        self.upload_threshold = np.clip(self.upload_threshold, min_threshold, max_threshold)
        
        if self.config['enable_logging']:
            self.logger.info(
                f"[动态阈值更新] 新阈值: {self.upload_threshold:.3f}s "
                f"(均值: {mean_time:.3f}s, 标准差: {std_time:.3f}s)"
            )
    
    def should_upload_succeed(self, client_id: int) -> Tuple[bool, float, str]:
        """判断客户端上传是否成功"""
        # 生成通信时间
        comm_time = self.generate_communication_time(client_id)
        
        # 更新动态阈值
        self.update_threshold(comm_time)
        
        # 判断是否成功
        success = comm_time <= self.upload_threshold
        
        # 更新统计信息
        self.total_attempts += 1
        if client_id not in self.client_attempt_counts:
            self.client_attempt_counts[client_id] = 0
            self.client_failure_counts[client_id] = 0
        
        self.client_attempt_counts[client_id] += 1
        
        if not success:
            self.total_failures += 1
            self.client_failure_counts[client_id] += 1
        
        # 记录结果
        self.client_upload_success[client_id] = success
        
        # 生成详细信息
        info = (
            f"客户端{client_id}: 通信时间{comm_time:.3f}s "
            f"{'<=' if success else '>'} 阈值{self.upload_threshold:.3f}s "
            f"-> {'成功' if success else '失败'}"
        )
        
        # 记录日志
        if self.config['enable_logging']:
            if success:
                self.logger.info(f"[上传成功] {info}")
            else:
                self.logger.warning(f"[上传失败] {info}")
        
        return success, comm_time, info
    
    def get_statistics(self) -> Dict:
        """获取详细统计信息"""
        overall_failure_rate = self.total_failures / self.total_attempts if self.total_attempts > 0 else 0
        
        client_stats = {}
        for client_id in self.client_attempt_counts:
            attempts = self.client_attempt_counts[client_id]
            failures = self.client_failure_counts[client_id]
            failure_rate = failures / attempts if attempts > 0 else 0
            
            client_stats[client_id] = {
                'attempts': attempts,
                'failures': failures,
                'failure_rate': failure_rate,
                'last_comm_time': self.client_comm_times.get(client_id, 0),
                'last_success': self.client_upload_success.get(client_id, False)
            }
        
        return {
            'overall': {
                'total_attempts': self.total_attempts,
                'total_failures': self.total_failures,
                'failure_rate': overall_failure_rate,
                'current_threshold': self.upload_threshold,
                'window_size': len(self.comm_time_window),
                'avg_comm_time': np.mean(list(self.comm_time_window)) if self.comm_time_window else 0
            },
            'clients': client_stats,
            'config': self.config
        }
    
    def reset_statistics(self) -> None:
        """重置所有统计信息"""
        self.total_attempts = 0
        self.total_failures = 0
        self.client_failure_counts.clear()
        self.client_attempt_counts.clear()
        self.client_upload_success.clear()
        self.client_comm_times.clear()
        
        if self.config['enable_logging']:
            self.logger.info("统计信息已重置")
    
    def set_threshold(self, threshold: float) -> None:
        """手动设置上传阈值"""
        min_threshold, max_threshold = self.config['threshold_range']
        self.upload_threshold = np.clip(threshold, min_threshold, max_threshold)
        
        if self.config['enable_logging']:
            self.logger.info(f"手动设置阈值为: {self.upload_threshold:.3f}s")
