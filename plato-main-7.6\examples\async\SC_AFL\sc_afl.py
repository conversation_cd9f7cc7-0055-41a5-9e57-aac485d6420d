#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SCAFL（Staleness-aware Client-Adaptive Federated Learning）主入口文件。
"""

import asyncio
import logging
import os
import sys
import argparse

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../../"))
sys.path.insert(0, project_root)

# 解析命令行参数
parser = argparse.ArgumentParser(description="运行SCAFL算法")
parser.add_argument("-c", "--config", type=str, default="sc_afl_config.yml", help="配置文件名（不含扩展名）或完整路径")
args = parser.parse_args()

# 处理配置文件路径
config_file = args.config
if not config_file.endswith('.yml'):
    config_file = config_file + '.yml'

# 如果不是绝对路径，则在当前目录中查找
if not os.path.isabs(config_file):
    config_file = os.path.join(current_dir, config_file)

# 检查配置文件路径是否有效
if not os.path.exists(config_file):
    print(f"错误：配置文件不存在: {config_file}")
    print(f"当前目录: {current_dir}")
    print(f"查找的文件: {config_file}")
    sys.exit(1)

# 规范化配置文件路径，避免重复
config_path = os.path.abspath(config_file)

# 设置环境变量，使Config类能够找到配置文件
os.environ["config_file"] = config_path
print(f"使用配置文件: {config_path}")

# 现在可以安全地导入其他模块
import torch
from plato.config import Config
from plato.utils import csv_processor
from sc_afl_server import Server
from sc_afl_client import Client
from sc_afl_algorithm import Algorithm
from plato.models import lenet5
from plato.trainers.basic import Trainer

def main():
    """主函数"""
    # 配置详细的日志输出
    log_dir = './logs'
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, 'sc_afl_debug.log')
    
    logging.basicConfig(
        level=logging.DEBUG,
        format='[%(asctime)s][%(levelname)s][%(filename)s:%(lineno)d] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logging.info("======== SC-AFL 启动 ========")
    logging.info(f"Python版本: {sys.version}")
    logging.info(f"PyTorch版本: {torch.__version__}")
    logging.info(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        logging.info(f"CUDA设备数: {torch.cuda.device_count()}")
        logging.info(f"当前CUDA设备: {torch.cuda.current_device()}")
        logging.info(f"设备名称: {torch.cuda.get_device_name(0)}")
    
    try:
        # 打印配置信息
        config = Config()
        logging.info(f"配置加载完成:")
        logging.info(f"客户端总数: {config.clients.total_clients}")
        logging.info(f"每轮选择的客户端数量: {config.clients.per_round}")
        logging.info(f"最大聚合客户端数: {getattr(config.clients, 'max_aggregation_clients', config.clients.per_round)}")
        logging.info(f"训练轮次: {config.trainer.rounds}")
        
        # 打印数据配置
        logging.info(f"数据源: {getattr(config.data, 'datasource', 'N/A')}")
        logging.info(f"数据分区方式: {getattr(config.data, 'sampler', 'N/A')}")
        
        # 安全地打印可能不存在的配置属性
        if hasattr(config.data, 'sampler'):
            logging.info(f"采样方式: {config.data.sampler}")
        if hasattr(config.data, 'noniid_mode'):
            logging.info(f"Non-IID模式: {config.data.noniid_mode}")
        
        try:
            # 创建服务器实例
            logging.info("开始创建服务器实例...")
            server = Server()
            logging.info("服务器实例创建成功")
            
            # 创建客户端实例并注册到服务器
            logging.info(f"正在创建和注册 {config.clients.total_clients} 个客户端...")

            # 获取客户端ID配置参数
            id_start = getattr(config.clients, 'id_start', 1)  # 默认从1开始
            total_clients = config.clients.total_clients

            logging.info(f"客户端ID配置: 起始ID={id_start}, 总数={total_clients}")

            for i in range(total_clients):
                client_id = id_start + i  # 使用配置的起始ID
                try:
                    # 创建客户端实例
                    logging.info(f"开始创建客户端 {client_id}...")
                    client = Client(client_id=client_id)
                    logging.info(f"客户端 {client_id} 实例创建成功")

                    # 为客户端设置服务器引用
                    client.set_server_reference(server)
                    logging.info(f"客户端 {client_id} 已设置服务器引用")

                    # 注册客户端到服务器
                    server.register_client(client)
                    logging.info(f"客户端 {client_id} 已成功注册到服务器")

                except Exception as client_err:
                    logging.error(f"创建客户端 {client_id} 时出错: {str(client_err)}")
                    import traceback
                    logging.error(f"客户端错误堆栈: {traceback.format_exc()}")
            
            logging.info(f"已成功创建和注册 {len(server.clients)} 个客户端")
            
            # 检查服务器属性
            logging.info(f"服务器属性检查:")
            logging.info(f"- 客户端数量: {len(server.clients)}")
            logging.info(f"- 全局模型: {'已初始化' if server.model is not None else '未初始化'}")
            logging.info(f"- 算法: {'已初始化' if server.algorithm is not None else '未初始化'}")
            logging.info(f"- 训练器: {'已初始化' if server.trainer is not None else '未初始化'}")
            
            # 运行服务器（会自动启动客户端）
            logging.info("准备启动服务器...")
            server.start()
            logging.info("服务器启动完成")
            
        except Exception as inner_err:
            logging.error(f"运行过程中出错: {str(inner_err)}")
            import traceback
            logging.error(f"内部错误堆栈: {traceback.format_exc()}")
        
    except Exception as e:
        logging.error(f"主程序运行出错: {str(e)}")
        import traceback
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise

async def run(server_task, client_tasks):
    """运行联邦学习训练"""
    try:
        # 等待所有任务完成
        await asyncio.gather(server_task, *client_tasks)
        
    except Exception as e:
        logging.error(f"运行联邦学习训练时出错: {str(e)}")
        raise

if __name__ == "__main__":
    main()