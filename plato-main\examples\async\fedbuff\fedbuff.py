"""
A federated learning training session using FedBuff.

Reference:

<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al., "Federated Learning with Buffered Asynchronous Aggregation,
" in Proc. International Conference on Artificial Intelligence and Statistics (AISTATS 2022).

https://proceedings.mlr.press/v151/nguyen22b/nguyen22b.pdf
"""

import fedbuff_server
import logging

def main():
    """A Plato federated learning training session using FedBuff."""
    print("🚀 [DEBUG] 步骤1: 开始FedBuff训练会话")
    print("🚀 [DEBUG] 步骤2: 创建FedBuff服务器实例")
    server = fedbuff_server.Server()
    print("🚀 [DEBUG] 步骤3: 调用server.run()开始训练")
    server.run()


if __name__ == "__main__":
    main()
