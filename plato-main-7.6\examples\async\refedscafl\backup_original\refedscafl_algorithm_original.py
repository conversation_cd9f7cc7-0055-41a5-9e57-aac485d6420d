import logging
import os
import copy
import random
import sys
import time
import numpy as np
import torch
from datetime import datetime
import asyncio
from logging.handlers import RotatingFileHandler
from collections import OrderedDict
from plato.config import Config
from plato.algorithms import fedavg
from plato.models import registry as models
from plato.utils import csv_processor
from math import log2
from types import SimpleNamespace
import pandas as pd
from plato.models import lenet5
from functools import partial

# 配置日志
def setup_logging():
    """配置日志系统"""
    print(f"我我我，算法文件下{setup_logging.__name__}被调用了")
    # 获取根日志记录器
    root_logger = logging.getLogger()
    
    # 检查是否已经配置过日志处理器，避免重复添加
    if root_logger.handlers:
        # 如果已经有处理器，直接返回现有的logger
        return root_logger
    
    # 清除任何现有的处理器（以防万一）
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建日志目录
    log_dir = os.path.join(Config().result_path if hasattr(Config(), 'result_path') else './logs', 'algorithm_logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 设置日志文件名
    log_file = os.path.join(log_dir, f'algorithm_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    # 配置根日志记录器
    root_logger.setLevel(logging.DEBUG)
    
    # 创建文件处理器（带轮转）
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '[%(asctime)s][%(levelname)s][%(filename)s:%(lineno)d] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return root_logger

# 初始化日志
logger = setup_logging()

class Algorithm(fedavg.Algorithm):
    """带时间衰减的加权聚合算法"""
    
    def __init__(self, trainer=None, server=None, *args, **kwargs):
        print(f"我我我，算法文件下Algorithm.__init__被调用了")
        print("DEBUG: Algorithm received trainer =", trainer)
        if trainer is None:
            raise ValueError("trainer cannot be None")
        self.trainer = trainer
        self.server = server  # 添加服务器引用
        print("Algorithm initialized with trainer:", trainer)
        super().__init__(trainer=trainer)  # 使用关键字参数传递
        self.client_staleness = {}
        
        # 初始化权重参数
        self.success_weight = 0.7  # 成功上传模型的权重
        self.distill_weight = 0.3  # 蒸馏补偿模型的权重
        self.rho = 1.0  # 动量系数
        
        # 初始化缓冲池
        self.success_buffer_pool = []
        self.distill_buffer_pool = []
        self.success_buffered_clients = []
        self.distill_buffered_clients = []
        
        # 初始化其他参数
        self.greedy_selection_size = 2
        self.V = 1.0  # 延迟权重
        self.tau_max = 6  # 最大陈旧度
        
        # 客户端状态跟踪
        self.client_beta = {}
        self.client_estimated_duration = {}
        self.staleness_queue = {}  # 添加缺失的staleness_queue属性
        
        # 初始化基本属性
        self.current_round = 0
        self.selected_clients = []
        self.client_upload_success = {}
        self.distilled_clients = set()
        self.client_updates = {}  # 存储客户端更新
        self.global_weights = None  # 初始化全局权重为None
        
        # 从配置中获取参数
        config = Config()
        self.total_clients = getattr(config.clients, 'total_clients', 20)
        self.clients_per_round = getattr(config.clients, 'per_round', 5)
        
        # 初始化权重参数
        self.success_weight = getattr(config.algorithm, 'success_weight', 0.7)  # 成功上传模型的权重
        self.distill_weight = getattr(config.algorithm, 'distill_weight', 0.3)  # 蒸馏补偿模型的权重

        # 初始化贪心选择参数
        self.greedy_selection_size = getattr(config.algorithm, 'greedy_selection_size', 3)  # 贪心选择的客户端数量
        self.buffer_pool_size = getattr(config.algorithm, 'buffer_pool_size', 20)  # 缓冲池大小

        # 初始化通信阈值
        self.communication_threshold = getattr(config.algorithm, 'communication_threshold', 0.6)  # 默认0.6秒
        self.historical_comm_times = []  # 存储历史通信时间

        # SCAFL相关参数
        self.V = getattr(config.algorithm, 'V', 1.0)  # 延迟权重
        self.tau_max = getattr(config.algorithm, 'tau_max', 5)  # 最大陈旧度
        self.rho = getattr(config.algorithm, 'rho', 1.0)  # 学习率
        
        logger.info("算法初始化完成")
        logger.info("参数配置：success_weight=%.2f, distill_weight=%.2f, greedy_selection_size=%s, V=%.2f, tau_max=%s",
                   self.success_weight, self.distill_weight, self.greedy_selection_size, self.V, self.tau_max)
    def set_server_reference(self, server):
        """设置服务器引用，用于访问客户端状态信息"""
        print(f"我我我，算法文件下Algorithm.set_server_reference被调用了")
        self.server = server
        logger.info("算法类已设置服务器引用")

    def get_client_staleness(self, client_id):
        """从服务器获取客户端陈旧度"""
        print(f"我我我，算法文件下Algorithm.get_client_staleness被调用了")
        if self.server and hasattr(self.server, 'client_staleness'):
            return self.server.client_staleness.get(client_id, 0)
        return self.client_staleness.get(client_id, 0)

    def aggregate_weights(self, success_updates, distill_updates, success_weight, distill_weight, rho, global_weights=None):
        """
        优化的基于梯度的聚合方法: wt = wt-1 + a*正常梯度 + b*蒸馏梯度

        优化点：
        1. 减少重复的设备转换
        2. 使用更高效的张量操作
        3. 支持稀疏更新
        4. 添加梯度裁剪防止梯度爆炸

        参数:
            success_updates: 成功上传的客户端更新列表（包含模型权重）
            distill_updates: 蒸馏补偿的客户端更新列表（包含梯度）
            success_weight: 成功上传模型的权重系数 a
            distill_weight: 蒸馏补偿梯度的权重系数 b
            rho: 动量参数（在此方法中不使用，保持接口兼容）
            global_weights: 当前全局模型权重（wt-1）
        Returns:
            OrderedDict: 聚合后的全局模型权重（wt）
        """
        import torch
        from collections import OrderedDict
        print(f"我我我，算法文件下Algorithm.aggregate_weights被调用了")

        # 过滤无效更新
        success_updates = [u for u in success_updates if u['weights'] and isinstance(u['weights'], dict)]
        distill_updates = [u for u in distill_updates if u['weights'] and isinstance(u['weights'], dict)]

        if not success_updates and not distill_updates:
            logger.error("所有客户端的更新均无效，无法聚合")
            return None

        if global_weights is None:
            logger.error("全局权重为空，无法进行基于梯度的聚合")
            return None

        # 获取设备信息，避免重复转换
        device = next(iter(global_weights.values())).device

        # 初始化聚合梯度 - 使用更高效的方式
        aggregated_gradients = OrderedDict()
        for key in global_weights.keys():
            aggregated_gradients[key] = torch.zeros_like(global_weights[key], device=device)
        
        # ------------- 优化的正常客户端处理（批量计算梯度）-------------

        # 计算总样本数（用于加权）
        total_success_samples = sum(self.get_num_samples(update) for update in success_updates) if success_updates else 0

        # 处理每个成功上传的客户端
        if success_updates:
            logger.info("处理 %s 个成功上传客户端的模型更新", len(success_updates))

            # 批量处理客户端权重，减少循环开销
            for update in success_updates:
                weights = update['weights']  # 这是客户端训练后的模型权重
                if weights is None:
                    logger.warning(f"客户端{update.get('client_id', '未知')}的权重为None，已跳过")
                    continue

                # 计算该客户端的权重系数
                num_samples = self.get_num_samples(update)
                client_weight = (num_samples / total_success_samples) if total_success_samples > 0 else 0

                # 直接计算加权梯度并累加，避免中间存储
                for key in weights.keys():
                    if key in global_weights and key in aggregated_gradients:
                        # 确保设备一致（一次性转换）
                        client_weight_tensor = weights[key].to(device)

                        # 计算加权梯度：(客户端权重 - 全局权重) * 客户端权重 * 成功权重
                        weighted_gradient = (client_weight_tensor - global_weights[key]) * client_weight * success_weight

                        # 梯度裁剪防止梯度爆炸
                        gradient_norm = torch.norm(weighted_gradient)
                        if gradient_norm > 1.0:  # 梯度裁剪阈值
                            weighted_gradient = weighted_gradient / gradient_norm * 1.0

                        aggregated_gradients[key] += weighted_gradient
                    elif key not in global_weights:
                        logger.warning(f"客户端权重中的键 {key} 不在全局模型中，已跳过")
        
        # ------------- 优化的蒸馏补偿处理（直接使用梯度）-------------

        # 计算总样本数（用于加权）
        total_distill_samples = sum(self.get_num_samples(update) for update in distill_updates) if distill_updates else 0

        # 处理每个蒸馏补偿的客户端
        if distill_updates:
            logger.info("处理 %s 个蒸馏补偿客户端的梯度更新", len(distill_updates))

            # 批量处理蒸馏梯度
            for update in distill_updates:
                gradients = update['weights']  # 这是客户端通过蒸馏生成的梯度
                if gradients is None:
                    logger.warning(f"客户端{update.get('client_id', '未知')}的梯度为None，已跳过")
                    continue

                # 计算该客户端的权重系数
                num_samples = self.get_num_samples(update)
                client_weight = (num_samples / total_distill_samples) if total_distill_samples > 0 else 0

                # 直接计算加权蒸馏梯度并累加
                for key in gradients.keys():
                    if key in global_weights and key in aggregated_gradients:
                        # 确保设备一致（一次性转换）
                        grad_tensor = gradients[key].to(device)

                        # 计算加权蒸馏梯度
                        weighted_distill_gradient = grad_tensor * client_weight * distill_weight

                        # 蒸馏梯度也进行裁剪
                        gradient_norm = torch.norm(weighted_distill_gradient)
                        if gradient_norm > 0.5:  # 蒸馏梯度使用更小的裁剪阈值
                            weighted_distill_gradient = weighted_distill_gradient / gradient_norm * 0.5

                        aggregated_gradients[key] += weighted_distill_gradient
                    elif key not in global_weights:
                        logger.warning(f"蒸馏梯度中的键 {key} 不在全局模型中，已跳过")
        
        # ------------- 优化的权重更新（支持学习率调整和动量）-------------

        # 添加自适应学习率调整
        adaptive_lr = self._calculate_adaptive_learning_rate(aggregated_gradients, global_weights)

        # 根据优化的公式: wt = wt-1 + lr * (a*正常梯度 + b*蒸馏梯度)
        # 其中，a*正常梯度 + b*蒸馏梯度 已经计算为 aggregated_gradients
        new_weights = OrderedDict()

        # 批量更新权重，减少循环开销
        for key in global_weights.keys():
            if key in aggregated_gradients:
                # 应用自适应学习率
                update = aggregated_gradients[key] * adaptive_lr
                new_weights[key] = global_weights[key] + update
            else:
                new_weights[key] = global_weights[key].clone()  # 使用clone避免引用问题

        logger.info("优化的基于梯度的聚合完成，新权重已生成（自适应学习率: %.4f）", adaptive_lr)
        return new_weights

    def _calculate_adaptive_learning_rate(self, gradients, global_weights):
        """
        计算自适应学习率
        基于梯度范数和权重范数的比值来调整学习率
        """
        try:
            # 计算总梯度范数
            total_grad_norm = 0.0
            total_weight_norm = 0.0

            for key in gradients.keys():
                if key in global_weights:
                    grad_norm = torch.norm(gradients[key]).item()
                    weight_norm = torch.norm(global_weights[key]).item()
                    total_grad_norm += grad_norm ** 2
                    total_weight_norm += weight_norm ** 2

            total_grad_norm = total_grad_norm ** 0.5
            total_weight_norm = total_weight_norm ** 0.5

            # 保守稳定的自适应学习率计算
            if total_grad_norm > 0 and total_weight_norm > 0:
                # 基于梯度和权重的比值调整学习率，保守策略
                ratio = total_weight_norm / total_grad_norm
                adaptive_lr = min(1.0, max(0.1, ratio * 0.1))  # 限制在[0.1, 1.0]范围内
            else:
                adaptive_lr = 1.0  # 标准默认学习率

            return adaptive_lr

        except Exception as e:
            logger.warning(f"计算自适应学习率失败: {e}，使用默认值1.0")
            return 1.0



    def extract_weights(self):
        """从模型中提取权重"""
        print(f"我我我，算法文件下Algorithm.extract_weights被调用了")
        if self.trainer.model is not None:
            return self.trainer.model.state_dict()
        return None
        
    def load_weights(self, weights):
        """加载权重到模型"""
        print(f"我我我，算法文件下Algorithm.load_weights被调用了")
        if self.trainer.model is not None and weights is not None:
            self.trainer.model.load_state_dict(weights)

    def greedy_select_clients(self, updates):
        """
        贪心选择客户端 - 委托给服务器端实现
        参数:
            updates: 客户端更新列表
        返回:
            selected_updates: 选中的更新列表
        """
        print(f"我我我，算法文件下Algorithm.greedy_select_clients被调用了")
        if self.server is None:
            logger.error("服务器引用为空，无法进行贪心选择")
            return []

        # 委托给服务器端的贪心选择实现
        return self.server.greedy_select_clients(updates)
        
    def get_num_samples(self, update):
        """
        从更新中获取样本数
        参数:
            update: 客户端更新
        返回:
            num_samples: 样本数
        """
        print(f"我我我，算法文件下Algorithm.get_num_samples被调用了")
        try:
            # 检查report是否存在
            if 'report' not in update:
                logger.debug("更新中缺少report字段，使用默认值600")
                return 600  # 使用默认值600
                
            report = update['report']
            
            # 检查report的类型和属性
            logger.debug("Report类型: %s", type(report))
            
            # 尝试不同的方式获取样本数
            if hasattr(report, 'num_samples'):
                num_samples = report.num_samples
                logger.debug("从report.num_samples获取样本数: %s", num_samples)
            elif hasattr(report, 'samples'):
                num_samples = report.samples
                logger.debug("从report.samples获取样本数: %s", num_samples)
            elif isinstance(report, dict) and 'num_samples' in report:
                num_samples = report['num_samples']
                logger.debug("从report字典获取样本数: %s", num_samples)
            else:
                # 如果找不到样本数，使用默认值600，但不输出警告
                logger.debug("无法从report中获取样本数，使用默认值600")
                num_samples = 600
                
            # 确保样本数为正数
            if num_samples <= 0:
                logger.debug("样本数为非正数(%s)，使用默认值600", num_samples)
                num_samples = 600
                
            return num_samples
            
        except Exception as e:
            logger.debug("获取样本数时出错: %s，使用默认值600", str(e))
            return 600  # 返回默认值600

        