"""
FedAC专用的LeNet5模型，支持可配置的输入通道数
基于Plato框架的LeNet5模型，修复了MNIST数据集的通道数不匹配问题
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

try:
    from plato.config import Config
except ImportError:
    Config = None


class Model(nn.Module):
    """
    FedAC专用的LeNet5模型
    支持可配置的输入通道数，默认为1通道（适用于MNIST）
    """

    def __init__(self, num_classes=10, in_channels=None, **kwargs):
        super().__init__()
        
        # 从配置中获取输入通道数，如果没有提供则从配置文件读取
        if in_channels is None:
            try:
                config = Config()
                in_channels = getattr(config.parameters.model, 'in_channels', 1)
            except:
                in_channels = 1  # 默认值：1通道（MNIST）

        # We pad the image to get an input size of 32x32 as for the
        # original network in the LeCun paper
        self.conv1 = nn.Conv2d(
            in_channels=in_channels, out_channels=6, kernel_size=5, stride=1, padding=2, bias=True
        )
        self.conv2 = nn.Conv2d(
            in_channels=6, out_channels=16, kernel_size=5, stride=1, padding=0, bias=True
        )
        self.conv3 = nn.Conv2d(
            in_channels=16, out_channels=120, kernel_size=5, stride=1, padding=0, bias=True
        )

        self.fc1 = nn.Linear(in_features=120, out_features=84, bias=True)
        self.fc2 = nn.Linear(in_features=84, out_features=num_classes, bias=True)

        print(f"[FedAC LeNet5] 创建模型: num_classes={num_classes}, in_channels={in_channels}")

    def forward(self, x):
        """前向传播"""
        # 第一层卷积 + 激活 + 池化
        x = F.relu(self.conv1(x))
        x = F.avg_pool2d(x, kernel_size=2, stride=2)

        # 第二层卷积 + 激活 + 池化
        x = F.relu(self.conv2(x))
        x = F.avg_pool2d(x, kernel_size=2, stride=2)

        # 第三层卷积 + 激活
        x = F.relu(self.conv3(x))

        # 展平
        x = torch.flatten(x, 1)

        # 全连接层
        x = F.relu(self.fc1(x))
        x = self.fc2(x)

        return x


def get_model(num_classes=10, in_channels=1, **kwargs):
    """
    获取FedAC LeNet5模型的工厂函数
    
    Args:
        num_classes: 分类数量，默认10（MNIST）
        in_channels: 输入通道数，默认1（MNIST灰度图像）
        **kwargs: 其他参数
    
    Returns:
        Model: LeNet5模型实例
    """
    return Model(num_classes=num_classes, in_channels=in_channels, **kwargs)


# 为了兼容性，也提供一个直接的类别名
LeNet5 = Model
